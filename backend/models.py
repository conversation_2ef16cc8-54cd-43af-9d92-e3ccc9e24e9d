#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库模型定义
版本: 1.0
创建时间: 2025-01-13

定义所有数据库表结构和模型关系
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Numeric
import json

# 数据库实例
db = SQLAlchemy()

class User(db.Model):
    """
    用户模型
    包含用户基本信息、认证信息和权限管理
    """
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True, comment='用户ID')
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    email = db.Column(db.String(100), unique=True, nullable=False, comment='邮箱')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    # 角色系统升级：支持更细粒度的用户角色定义
    user_type = db.Column(db.Enum('airline_buyer', 'platform_staff', 'maintenance_engineer', 
                                 'logistics_specialist', 'admin', 'supplier', 'distributor',
                                 # 向后兼容的旧角色类型
                                 'airline', 'maintenance', 
                                 name='user_types'), 
                         nullable=False, comment='用户类型')
    
    # 新增：公司类型，区分内部员工和外部客户
    company_type = db.Column(db.Enum('internal', 'external', name='company_types'), 
                            default='external', comment='公司类型：internal-平台内部员工，external-外部客户')
    
    # 新增：权限等级，支持权限层级管理
    permission_level = db.Column(db.Integer, default=1, comment='权限等级：1-基础用户，2-高级用户，3-管理员，4-超级管理员')
    
    # 新的角色系统字段
    role_id = db.Column(db.Integer, db.ForeignKey('system_roles.id'), comment='角色ID，关联system_roles表')
    
    company_name = db.Column(db.String(100), comment='公司名称')
    real_name = db.Column(db.String(50), comment='真实姓名')
    phone = db.Column(db.String(20), comment='联系电话')
    status = db.Column(db.Enum('active', 'inactive', 'suspended', name='user_status'), 
                      default='active', comment='用户状态')
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    orders_as_buyer = db.relationship('Order', foreign_keys='Order.buyer_id', backref='buyer', lazy='dynamic')
    orders_as_supplier = db.relationship('Order', foreign_keys='Order.supplier_id', backref='supplier', lazy='dynamic')
    demands = db.relationship('Demand', foreign_keys='Demand.requester_id', lazy='dynamic', overlaps="requester")
    notifications = db.relationship('Notification', backref='user', lazy='dynamic')
    supplied_inventory = db.relationship('InventoryItem', 
                                       foreign_keys='InventoryItem.supplier_id',
                                       backref='inventory_supplier', 
                                       lazy='dynamic',
                                       overlaps="supplier,managed_inventory")
    
    def set_password(self, password):
        """设置密码哈希"""
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'user_type': self.user_type,
            'company_type': self.company_type,
            'permission_level': self.permission_level,
            'company_name': self.company_name,
            'real_name': self.real_name,
            'phone': self.phone,
            'status': self.status,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat()
        }
    
    def is_internal_staff(self):
        """判断是否为平台内部员工"""
        return self.company_type == 'internal'
    
    def is_admin(self):
        """判断是否为管理员"""
        return self.user_type == 'admin' or self.permission_level >= 3
    
    def can_cross_company_access(self):
        """判断是否具有跨公司数据访问权限"""
        return self.is_internal_staff() or self.user_type in ['platform_staff', 'admin']
    
    def get_role_permissions(self):
        """获取角色对应的权限列表"""
        # 角色权限映射表
        ROLE_PERMISSIONS = {
            'airline_buyer': [
                'publish_demand', 'manage_inventory', 'publish_shared', 
                'manage_orders', 'view_own_data'
            ],
            'platform_staff': [
                'process_all_orders', 'cross_company_access', 'audit_shared_materials',
                'publish_demand', 'manage_inventory', 'publish_shared', 'manage_orders',
                'view_all_data', 'customer_service'
            ],
            'maintenance_engineer': [
                'update_maintenance_status', 'technical_support', 
                'view_maintenance_data', 'manage_work_orders'
            ],
            'logistics_specialist': [
                'track_shipment', 'update_logistics', 
                'manage_delivery', 'view_logistics_data'
            ],
            'admin': [
                'system_config', 'data_analysis', 'user_management',
                'all_permissions'  # 管理员具有所有权限
            ],
            # 新定义的核心业务角色
            'supplier': [
                # 供应商：只销售，不采购
                'manage_inventory', 'publish_shared', 'manage_sales_orders', 
                'view_own_data', 'respond_to_inquiry'
            ],
            'distributor': [
                # 分销商：既买也卖，功能最全面
                'publish_demand', 'manage_inventory', 'publish_shared', 
                'manage_orders', 'manage_sales_orders', 'manage_purchase_orders',
                'view_own_data', 'respond_to_inquiry', 'trade_analysis'
            ],
            # 向后兼容的旧角色
            'airline': ['publish_demand', 'manage_inventory', 'manage_orders', 'view_own_data'],
            'maintenance': ['update_maintenance_status', 'technical_support', 'view_maintenance_data']
        }
        
        base_permissions = ROLE_PERMISSIONS.get(self.user_type, [])
        
        # 根据权限等级添加额外权限
        if self.permission_level >= 2:
            base_permissions.extend(['advanced_features', 'priority_support'])
        if self.permission_level >= 3:
            base_permissions.extend(['admin_features', 'user_management'])
        if self.permission_level >= 4:
            base_permissions.extend(['super_admin', 'system_config'])
            
        return list(set(base_permissions))  # 去重
    
    def is_supplier(self):
        """判断是否为供应商"""
        return self.user_type == 'supplier'
    
    def is_distributor(self):
        """判断是否为分销商"""
        return self.user_type == 'distributor'
        
    def is_airline(self):
        """判断是否为航空公司"""
        return self.user_type in ['airline', 'airline_buyer']
    
    def can_publish_demand(self):
        """判断是否可以发布采购需求"""
        # 只有航空公司和分销商可以发布采购需求，供应商不行
        return self.user_type in ['airline', 'airline_buyer', 'distributor'] or self.has_permission('publish_demand')
    
    def can_publish_products(self):
        """判断是否可以发布销售产品"""
        # 供应商和分销商都可以发布销售产品
        return self.user_type in ['supplier', 'distributor'] or self.has_permission('publish_shared')
    
    def can_manage_sales(self):
        """判断是否可以管理销售业务"""
        return self.user_type in ['supplier', 'distributor'] or self.has_permission('manage_sales_orders')
    
    def can_manage_purchases(self):
        """判断是否可以管理采购业务"""
        return self.user_type in ['airline', 'airline_buyer', 'distributor'] or self.has_permission('manage_purchase_orders')
    
    def get_business_direction(self):
        """获取用户的业务方向"""
        if self.user_type == 'supplier':
            return 'sell_only'  # 只销售
        elif self.user_type == 'distributor':
            return 'buy_and_sell'  # 既买也卖
        elif self.user_type in ['airline', 'airline_buyer']:
            return 'buy_only'  # 只采购
        else:
            return 'other'
    
    def has_permission(self, permission):
        """检查用户是否具有特定权限"""
        user_permissions = self.get_role_permissions()
        return permission in user_permissions or 'all_permissions' in user_permissions

class Material(db.Model):
    """
    航材模型
    存储航材基本信息和规格参数
    """
    __tablename__ = 'materials'
    
    id = db.Column(db.Integer, primary_key=True, comment='航材ID')
    part_number = db.Column(db.String(50), unique=True, nullable=False, comment='零件号')
    part_name = db.Column(db.String(200), nullable=False, comment='零件名称')
    name = db.Column(db.String(200), nullable=False, comment='航材名称')
    category = db.Column(db.String(50), nullable=False, comment='类别')
    manufacturer = db.Column(db.String(100), comment='制造商')
    aircraft_type = db.Column(db.String(50), comment='适用机型')
    model = db.Column(db.String(100), comment='型号')
    description = db.Column(db.Text, comment='描述')
    specifications = db.Column(db.Text, comment='规格参数(JSON)')
    unit = db.Column(db.String(20), default='PCS', comment='计量单位')
    weight = db.Column(db.Float, comment='重量(kg)')
    shelf_life_months = db.Column(db.Integer, comment='保质期(月)')
    image_url = db.Column(db.String(500), comment='图片URL')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    inventory_items = db.relationship('InventoryItem', backref='material', lazy='dynamic')
    order_items = db.relationship('OrderItem', backref='material', lazy='dynamic')
    certificates = db.relationship('Certificate', backref='material', lazy='dynamic')
    shared_materials = db.relationship('SharedMaterial', backref='material', lazy='dynamic')
    
    def get_specifications(self):
        """获取规格参数"""
        return json.loads(self.specifications) if self.specifications else {}
    
    def set_specifications(self, specs_dict):
        """设置规格参数"""
        self.specifications = json.dumps(specs_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'part_number': self.part_number,
            'part_name': self.part_name,
            'category': self.category,
            'manufacturer': self.manufacturer,
            'aircraft_type': self.aircraft_type,
            'description': self.description,
            'specifications': self.get_specifications(),
            'unit': self.unit,
            'weight': self.weight,
            'shelf_life_months': self.shelf_life_months,
            'created_at': self.created_at.isoformat()
        }

class InventoryItem(db.Model):
    """
    库存项目模型
    管理航材库存数量和状态
    """
    __tablename__ = 'inventory_items'
    
    id = db.Column(db.Integer, primary_key=True, comment='库存ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    location = db.Column(db.String(100), nullable=False, comment='存储位置')
    current_stock = db.Column(db.Integer, default=0, comment='当前库存')
    safety_stock = db.Column(db.Integer, default=0, comment='安全库存')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    condition_code = db.Column(db.Enum('NE', 'NS', 'OH', 'SV', 'AR', name='inventory_condition_codes'), default='NE', comment='状况代码')
    is_shareable = db.Column(db.Boolean, default=False, comment='是否可共享')
    status = db.Column(db.Enum('normal', 'warning', 'shortage', 'expired', name='inventory_status'), 
                      default='normal', comment='库存状态')
    batch_number = db.Column(db.String(50), comment='批次号')
    expiry_date = db.Column(db.Date, comment='过期日期')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='供应商ID')
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='最后更新时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    supplier = db.relationship('User', 
                              foreign_keys=[supplier_id],
                              backref=db.backref('managed_inventory', lazy='dynamic',
                                                overlaps="supplied_inventory,inventory_supplier"),
                              overlaps="supplied_inventory,inventory_supplier")
    
    def update_status(self):
        """更新库存状态"""
        if self.current_stock <= 0:
            self.status = 'shortage'
        elif self.current_stock <= self.safety_stock:
            self.status = 'warning'
        elif self.expiry_date and self.expiry_date < datetime.now().date():
            self.status = 'expired'
        else:
            self.status = 'normal'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'location': self.location,
            'current_stock': self.current_stock,
            'safety_stock': self.safety_stock,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'condition_code': self.condition_code,
            'is_shareable': self.is_shareable,
            'status': self.status,
            'batch_number': self.batch_number,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'supplier_id': self.supplier_id,
            'supplier': self.supplier.to_dict() if self.supplier else None,
            'last_updated': self.last_updated.isoformat(),
            'created_at': self.created_at.isoformat()
        }

class Order(db.Model):
    """
    订单模型
    管理采购订单和交易信息
    """
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True, comment='订单ID')
    order_number = db.Column(db.String(50), unique=True, nullable=False, comment='订单号')
    buyer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='买方ID')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='供应商ID')
    status = db.Column(db.Enum('pending', 'confirmed', 'processing', 'shipping', 'completed', 'cancelled', 
                               name='order_status'), default='pending', comment='订单状态')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='order_priority'), 
                        default='normal', comment='优先级')
    total_amount = db.Column(Numeric(12, 2), comment='订单总金额')
    currency = db.Column(db.String(3), default='CNY', comment='货币类型')
    delivery_address = db.Column(db.Text, comment='交货地址')
    delivery_date = db.Column(db.DateTime, comment='交货日期')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    order_items = db.relationship('OrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    logistics = db.relationship('LogisticsInfo', backref='order', uselist=False)
    
    def calculate_total(self):
        """计算订单总金额"""
        total = sum(item.subtotal for item in self.order_items)
        self.total_amount = total
        return total
    
    def update_status(self, new_status, operator_id, reason=None, notes=None):
        """更新订单状态并记录历史"""
        from models import OrderStatusHistory  # 避免循环导入

        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()

        # 记录状态变更历史
        history = OrderStatusHistory(
            order_id=self.id,
            from_status=old_status,
            to_status=new_status,
            operator_id=operator_id,
            reason=reason,
            notes=notes
        )
        db.session.add(history)

        return history

    def can_transition_to(self, new_status):
        """检查是否可以转换到新状态"""
        valid_transitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['processing', 'cancelled'],
            'processing': ['shipping', 'cancelled'],
            'shipping': ['completed'],
            'completed': [],
            'cancelled': []
        }

        return new_status in valid_transitions.get(self.status, [])

    def requires_approval(self):
        """检查订单是否需要审批"""
        # AOG订单或高金额订单需要审批
        if self.priority == 'aog':
            return True
        if self.total_amount and self.total_amount > 100000:  # 10万以上需要审批
            return True
        return False

    def get_approval_status(self):
        """获取审批状态"""
        if not self.requires_approval():
            return 'not_required'

        approvals = self.approvals
        if not approvals:
            return 'pending'

        # 检查是否有拒绝的审批
        for approval in approvals:
            if approval.status == 'rejected':
                return 'rejected'

        # 检查是否所有审批都通过
        required_levels = self._get_required_approval_levels()
        approved_levels = [a.approval_level for a in approvals if a.status == 'approved']

        if all(level in approved_levels for level in required_levels):
            return 'approved'

        return 'pending'

    def _get_required_approval_levels(self):
        """获取需要的审批级别"""
        levels = []

        if self.priority == 'aog':
            levels.append(1)  # 部门主管

        if self.total_amount:
            if self.total_amount > 100000:
                levels.append(1)  # 部门主管
            if self.total_amount > 500000:
                levels.append(2)  # 财务主管
            if self.total_amount > 1000000:
                levels.append(3)  # 总经理

        return levels or [1]  # 默认需要一级审批

    def to_dict(self, include_items=True, include_history=False, include_approvals=False):
        """转换为字典格式"""
        result = {
            'id': self.id,
            'order_number': self.order_number,
            'buyer_id': self.buyer_id,
            'supplier_id': self.supplier_id,
            'status': self.status,
            'priority': self.priority,
            'total_amount': float(self.total_amount) if self.total_amount else None,
            'currency': self.currency,
            'delivery_address': self.delivery_address,
            'delivery_date': self.delivery_date.isoformat() if self.delivery_date else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'requires_approval': self.requires_approval(),
            'approval_status': self.get_approval_status()
        }

        if include_items:
            result['items'] = [item.to_dict() for item in self.order_items]

        if include_history:
            result['status_history'] = [h.to_dict() for h in self.status_history]

        if include_approvals:
            result['approvals'] = [a.to_dict() for a in self.approvals]

        return result

class OrderItem(db.Model):
    """
    订单项目模型
    订单中的具体航材项目
    """
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True, comment='订单项ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    quantity = db.Column(db.Integer, nullable=False, comment='数量')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    subtotal = db.Column(Numeric(12, 2), comment='小计')
    condition_code = db.Column(db.String(10), comment='状况代码')
    delivery_requirement = db.Column(db.Text, comment='交货要求')
    
    def calculate_subtotal(self):
        """计算小计"""
        if self.quantity and self.unit_price:
            self.subtotal = self.quantity * self.unit_price
        return self.subtotal
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'subtotal': float(self.subtotal) if self.subtotal else None,
            'condition_code': self.condition_code,
            'delivery_requirement': self.delivery_requirement
        }

class OrderStatusHistory(db.Model):
    """
    订单状态历史模型
    记录订单状态变更的历史记录
    """
    __tablename__ = 'order_status_history'

    id = db.Column(db.Integer, primary_key=True, comment='历史记录ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    from_status = db.Column(db.String(20), comment='原状态')
    to_status = db.Column(db.String(20), nullable=False, comment='新状态')
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='操作人ID')
    reason = db.Column(db.Text, comment='状态变更原因')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    order = db.relationship('Order', backref='status_history')
    operator = db.relationship('User', backref='operated_order_status')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'from_status': self.from_status,
            'to_status': self.to_status,
            'operator_id': self.operator_id,
            'operator': self.operator.to_dict() if self.operator else None,
            'reason': self.reason,
            'notes': self.notes,
            'created_at': self.created_at.isoformat()
        }

class OrderApproval(db.Model):
    """
    订单审批模型
    管理订单的审批流程
    """
    __tablename__ = 'order_approvals'

    id = db.Column(db.Integer, primary_key=True, comment='审批记录ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='审批人ID')
    approval_level = db.Column(db.Integer, nullable=False, comment='审批级别')
    status = db.Column(db.Enum('pending', 'approved', 'rejected', name='approval_status'),
                      default='pending', comment='审批状态')
    comments = db.Column(db.Text, comment='审批意见')
    approved_at = db.Column(db.DateTime, comment='审批时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    order = db.relationship('Order', backref='approvals')
    approver = db.relationship('User', backref='order_approvals')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'approver_id': self.approver_id,
            'approver': self.approver.to_dict() if self.approver else None,
            'approval_level': self.approval_level,
            'status': self.status,
            'comments': self.comments,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'created_at': self.created_at.isoformat()
        }

class OrderAttachment(db.Model):
    """
    订单附件模型
    管理订单相关的文件附件
    """
    __tablename__ = 'order_attachments'

    id = db.Column(db.Integer, primary_key=True, comment='附件ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    original_filename = db.Column(db.String(255), nullable=False, comment='原始文件名')
    file_path = db.Column(db.String(500), nullable=False, comment='文件路径')
    file_size = db.Column(db.Integer, comment='文件大小(字节)')
    file_type = db.Column(db.String(50), comment='文件类型')
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='上传人ID')
    description = db.Column(db.Text, comment='文件描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='上传时间')

    # 关系
    order = db.relationship('Order', backref='attachments')
    uploader = db.relationship('User', backref='uploaded_order_attachments')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'uploaded_by': self.uploaded_by,
            'uploader': self.uploader.to_dict() if self.uploader else None,
            'description': self.description,
            'created_at': self.created_at.isoformat()
        }

class Certificate(db.Model):
    """
    适航证书模型
    管理航材的适航证书和认证信息
    """
    __tablename__ = 'certificates'
    
    id = db.Column(db.Integer, primary_key=True, comment='证书ID')
    certificate_number = db.Column(db.String(100), unique=True, nullable=False, comment='证书编号')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    certificate_type = db.Column(db.Enum('8130-3', 'caac', 'faa', 'easa', name='certificate_types'), 
                                nullable=False, comment='证书类型')
    issuing_authority = db.Column(db.String(100), nullable=False, comment='颁发机构')
    issue_date = db.Column(db.Date, nullable=False, comment='颁发日期')
    expiry_date = db.Column(db.Date, comment='到期日期')
    status = db.Column(db.Enum('valid', 'expiring', 'expired', 'suspended', name='certificate_status'), 
                      default='valid', comment='证书状态')
    document_path = db.Column(db.String(255), comment='证书文件路径')
    verification_status = db.Column(db.Boolean, default=False, comment='验证状态')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def update_status(self):
        """更新证书状态"""
        if self.expiry_date:
            days_to_expiry = (self.expiry_date - datetime.now().date()).days
            if days_to_expiry < 0:
                self.status = 'expired'
            elif days_to_expiry <= 30:
                self.status = 'expiring'
            else:
                self.status = 'valid'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'certificate_number': self.certificate_number,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'certificate_type': self.certificate_type,
            'issuing_authority': self.issuing_authority,
            'issue_date': self.issue_date.isoformat(),
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'status': self.status,
            'document_path': self.document_path,
            'verification_status': self.verification_status,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class WorkOrder(db.Model):
    """
    维修工单模型
    管理维修工作订单和进度
    """
    __tablename__ = 'work_orders'
    
    id = db.Column(db.Integer, primary_key=True, comment='工单ID')
    work_order_number = db.Column(db.String(50), unique=True, nullable=False, comment='工单号')
    aircraft_tail = db.Column(db.String(20), nullable=False, comment='飞机尾号')
    aircraft_type = db.Column(db.String(50), nullable=False, comment='机型')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='work_order_priority'), 
                        default='normal', comment='优先级')
    status = db.Column(db.Enum('new', 'in_progress', 'waiting_parts', 'completed', 'cancelled', 
                               name='work_order_status'), default='new', comment='工单状态')
    fault_title = db.Column(db.String(200), nullable=False, comment='故障标题')
    fault_description = db.Column(db.Text, comment='故障描述')
    station = db.Column(db.String(100), comment='维修站')
    assigned_technician_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='分配技师ID')
    estimated_hours = db.Column(db.Float, comment='预计工时')
    actual_hours = db.Column(db.Float, comment='实际工时')
    progress = db.Column(db.Integer, default=0, comment='进度百分比')
    start_time = db.Column(db.DateTime, comment='开始时间')
    estimated_completion = db.Column(db.DateTime, comment='预计完成时间')
    completion_time = db.Column(db.DateTime, comment='实际完成时间')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    assigned_technician = db.relationship('User', backref='assigned_work_orders')
    labor_records = db.relationship('LaborRecord', backref='work_order', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'work_order_number': self.work_order_number,
            'aircraft_tail': self.aircraft_tail,
            'aircraft_type': self.aircraft_type,
            'priority': self.priority,
            'status': self.status,
            'fault_title': self.fault_title,
            'fault_description': self.fault_description,
            'station': self.station,
            'assigned_technician_id': self.assigned_technician_id,
            'assigned_technician': self.assigned_technician.to_dict() if self.assigned_technician else None,
            'estimated_hours': self.estimated_hours,
            'actual_hours': self.actual_hours,
            'progress': self.progress,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'estimated_completion': self.estimated_completion.isoformat() if self.estimated_completion else None,
            'completion_time': self.completion_time.isoformat() if self.completion_time else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class LaborRecord(db.Model):
    """
    工时记录模型
    记录维修工作的工时信息
    """
    __tablename__ = 'labor_records'
    
    id = db.Column(db.Integer, primary_key=True, comment='工时记录ID')
    work_order_id = db.Column(db.Integer, db.ForeignKey('work_orders.id'), nullable=False, comment='工单ID')
    technician_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='技师ID')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    hours_worked = db.Column(db.Float, comment='工作小时数')
    work_description = db.Column(db.Text, comment='工作描述')
    labor_rate = db.Column(Numeric(10, 2), comment='工时费率')
    labor_cost = db.Column(Numeric(10, 2), comment='工时成本')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    technician = db.relationship('User', backref='labor_records')
    
    def calculate_hours(self):
        """计算工作小时数"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            self.hours_worked = delta.total_seconds() / 3600
        return self.hours_worked
    
    def calculate_cost(self):
        """计算工时成本"""
        if self.hours_worked and self.labor_rate:
            self.labor_cost = self.hours_worked * self.labor_rate
        return self.labor_cost
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'work_order_id': self.work_order_id,
            'technician_id': self.technician_id,
            'technician': self.technician.to_dict() if self.technician else None,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'hours_worked': self.hours_worked,
            'work_description': self.work_description,
            'labor_rate': float(self.labor_rate) if self.labor_rate else None,
            'labor_cost': float(self.labor_cost) if self.labor_cost else None,
            'created_at': self.created_at.isoformat()
        }

class AOGCase(db.Model):
    """
    AOG紧急案例模型
    管理Aircraft on Ground紧急响应案例
    """
    __tablename__ = 'aog_cases'
    
    id = db.Column(db.Integer, primary_key=True, comment='AOG案例ID')
    case_number = db.Column(db.String(50), unique=True, nullable=False, comment='案例编号')
    aircraft_tail = db.Column(db.String(20), nullable=False, comment='飞机尾号')
    aircraft_type = db.Column(db.String(50), nullable=False, comment='机型')
    location = db.Column(db.String(100), nullable=False, comment='位置')
    priority = db.Column(db.Enum('critical', 'high', 'medium', name='aog_priority'), 
                        default='critical', comment='优先级')
    status = db.Column(db.Enum('new', 'responding', 'in_progress', 'waiting_parts', 'resolved', 
                               name='aog_status'), default='new', comment='状态')
    fault_title = db.Column(db.String(200), nullable=False, comment='故障标题')
    fault_description = db.Column(db.Text, comment='故障描述')
    customer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='客户ID')
    assigned_team_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='分配团队ID')
    contact_name = db.Column(db.String(100), comment='联系人姓名')
    contact_phone = db.Column(db.String(20), comment='联系电话')
    contact_email = db.Column(db.String(100), comment='联系邮箱')
    response_time = db.Column(db.DateTime, comment='响应时间')
    estimated_resolution = db.Column(db.DateTime, comment='预计解决时间')
    resolution_time = db.Column(db.DateTime, comment='解决时间')
    resolution_notes = db.Column(db.Text, comment='解决方案备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    customer = db.relationship('User', foreign_keys=[customer_id], backref='aog_cases_as_customer')
    assigned_team = db.relationship('User', foreign_keys=[assigned_team_id], backref='aog_cases_as_team')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'case_number': self.case_number,
            'aircraft': {
                'tail_number': self.aircraft_tail,
                'aircraft_type': self.aircraft_type,
                'location': self.location
            },
            'priority': self.priority,
            'status': self.status,
            'fault': {
                'title': self.fault_title,
                'description': self.fault_description
            },
            'customer_id': self.customer_id,
            'assigned_team_id': self.assigned_team_id,
            'assigned_team': self.assigned_team.to_dict() if self.assigned_team else None,
            'contact': {
                'name': self.contact_name,
                'phone': self.contact_phone,
                'email': self.contact_email
            },
            'response_time': self.response_time.isoformat() if self.response_time else None,
            'estimated_resolution': self.estimated_resolution.isoformat() if self.estimated_resolution else None,
            'resolution_time': self.resolution_time.isoformat() if self.resolution_time else None,
            'resolution_notes': self.resolution_notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Demand(db.Model):
    """
    需求模型
    管理用户发布的航材需求
    """
    __tablename__ = 'demands'
    
    id = db.Column(db.Integer, primary_key=True, comment='需求ID')
    demand_number = db.Column(db.String(50), unique=True, nullable=False, comment='需求编号')
    title = db.Column(db.String(300), comment='需求标题')
    requester_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='需求方ID')
    type = db.Column(db.Enum('turnaround', 'consumable', 'maintenance', 'aog', name='demand_types'), 
                     nullable=False, comment='需求类型')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='demand_priority'), 
                        default='normal', comment='优先级')
    status = db.Column(db.Enum('published', 'matched', 'negotiating', 'confirmed', 'cancelled', 
                               name='demand_status'), default='published', comment='状态')
    material_name = db.Column(db.String(200), nullable=False, comment='航材名称')
    part_number = db.Column(db.String(50), comment='零件号')
    aircraft_type = db.Column(db.String(50), comment='机型')
    quantity = db.Column(db.Integer, nullable=False, comment='需求数量')
    unit = db.Column(db.String(20), default='PCS', comment='单位')
    description = db.Column(db.Text, comment='需求描述')
    delivery_location = db.Column(db.String(200), comment='交货地点')
    delivery_time = db.Column(db.DateTime, comment='交货时间')
    budget_range = db.Column(db.String(100), comment='预算范围')
    quality_requirements = db.Column(db.Text, comment='质量要求')
    contact_info = db.Column(db.Text, comment='联系信息(JSON)')
    expires_at = db.Column(db.DateTime, comment='需求过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    requester = db.relationship('User', lazy='select')
    responses = db.relationship('DemandResponse', backref='demand', lazy='dynamic')
    
    def get_contact_info(self):
        """获取联系信息"""
        return json.loads(self.contact_info) if self.contact_info else {}
    
    def set_contact_info(self, contact_dict):
        """设置联系信息"""
        self.contact_info = json.dumps(contact_dict, ensure_ascii=False)
    
    def generate_auto_title(self):
        """自动生成需求标题"""
        # 需求类型映射
        type_map = {
            'turnaround': '周转件需求',
            'consumable': '消耗件需求', 
            'maintenance': '维修需求',
            'aog': 'AOG紧急'
        }
        
        # 优先级映射  
        priority_map = {
            'aog': '紧急',
            'high': '高优先级',
            'normal': '',
            'low': '低优先级'
        }
        
        # 构建标题部分
        parts = []
        
        # 添加需求类型
        if self.type in type_map:
            parts.append(type_map[self.type])
        
        # 添加航材名称
        if self.material_name:
            parts.append(self.material_name)
        
        # 添加机型信息
        if self.aircraft_type and self.aircraft_type != 'other':
            parts.append(self.aircraft_type)
        
        # 添加优先级（仅当非普通优先级时）
        if self.priority in priority_map and priority_map[self.priority]:
            parts.append(priority_map[self.priority])
        
        # 组合标题
        title = '-'.join(parts) if parts else '航材需求'
        
        # 限制标题长度
        if len(title) > 100:
            title = title[:97] + '...'
            
        return title
    
    def ensure_title(self):
        """确保有标题，如果没有则自动生成"""
        if not self.title:
            self.title = self.generate_auto_title()
        return self.title
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_number': self.demand_number,
            'title': self.title,
            'requester_id': self.requester_id,
            'requester': self.requester.to_dict() if self.requester else None,
            'type': self.type,
            'priority': self.priority,
            'status': self.status,
            'material_name': self.material_name,
            'part_number': self.part_number,
            'aircraft_type': self.aircraft_type,
            'quantity': self.quantity,
            'unit': self.unit,
            'description': self.description,
            'delivery_location': self.delivery_location,
            'delivery_time': self.delivery_time.isoformat() if self.delivery_time else None,
            'budget_range': self.budget_range,
            'quality_requirements': self.quality_requirements,
            'contact_info': self.get_contact_info(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class DemandResponse(db.Model):
    """
    需求响应模型
    供应商对需求的响应
    """
    __tablename__ = 'demand_responses'
    
    id = db.Column(db.Integer, primary_key=True, comment='响应ID')
    demand_id = db.Column(db.Integer, db.ForeignKey('demands.id'), nullable=False, comment='需求ID')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='供应商ID')
    status = db.Column(db.Enum('pending', 'accepted', 'rejected', 'expired', name='response_status'), 
                      default='pending', comment='响应状态')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    total_price = db.Column(Numeric(12, 2), comment='总价')
    delivery_time = db.Column(db.DateTime, comment='交货时间')
    availability = db.Column(db.Integer, comment='可供应数量')
    condition_code = db.Column(db.String(10), comment='状况代码')
    certificate_info = db.Column(db.Text, comment='证书信息')
    message = db.Column(db.Text, comment='响应消息')
    valid_until = db.Column(db.DateTime, comment='报价有效期')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    supplier = db.relationship('User', backref='demand_responses')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_id': self.demand_id,
            'supplier_id': self.supplier_id,
            'supplier': self.supplier.to_dict() if self.supplier else None,
            'status': self.status,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_price': float(self.total_price) if self.total_price else None,
            'delivery_time': self.delivery_time.isoformat() if self.delivery_time else None,
            'availability': self.availability,
            'condition_code': self.condition_code,
            'certificate_info': self.certificate_info,
            'message': self.message,
            'valid_until': self.valid_until.isoformat() if self.valid_until else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class LogisticsInfo(db.Model):
    """
    物流信息模型
    跟踪订单的物流配送信息
    """
    __tablename__ = 'logistics_info'
    
    id = db.Column(db.Integer, primary_key=True, comment='物流ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    tracking_number = db.Column(db.String(100), comment='运单号')
    carrier = db.Column(db.String(100), comment='承运商')
    status = db.Column(db.Enum('preparing', 'shipped', 'in_transit', 'arrived', 'delivered', 'exception', 
                               name='logistics_status'), default='preparing', comment='物流状态')
    shipped_at = db.Column(db.DateTime, comment='发货时间')
    estimated_arrival = db.Column(db.DateTime, comment='预计到达时间')
    actual_arrival = db.Column(db.DateTime, comment='实际到达时间')
    current_location = db.Column(db.String(200), comment='当前位置')
    delivery_notes = db.Column(db.Text, comment='配送备注')
    recipient_name = db.Column(db.String(100), comment='收货人姓名')
    recipient_phone = db.Column(db.String(20), comment='收货人电话')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'tracking_number': self.tracking_number,
            'carrier': self.carrier,
            'status': self.status,
            'shipped_at': self.shipped_at.isoformat() if self.shipped_at else None,
            'estimated_arrival': self.estimated_arrival.isoformat() if self.estimated_arrival else None,
            'actual_arrival': self.actual_arrival.isoformat() if self.actual_arrival else None,
            'current_location': self.current_location,
            'delivery_notes': self.delivery_notes,
            'recipient_name': self.recipient_name,
            'recipient_phone': self.recipient_phone,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Notification(db.Model):
    """
    通知模型
    系统通知和消息管理
    """
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True, comment='通知ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    type = db.Column(db.Enum('order', 'aog', 'logistics', 'quality', 'supplier', 'system', 
                             name='notification_types'), nullable=False, comment='通知类型')
    title = db.Column(db.String(200), nullable=False, comment='通知标题')
    content = db.Column(db.Text, comment='通知内容')
    priority = db.Column(db.Enum('low', 'normal', 'high', 'urgent', name='notification_priority'), 
                        default='normal', comment='优先级')
    is_read = db.Column(db.Boolean, default=False, comment='是否已读')
    read_at = db.Column(db.DateTime, comment='阅读时间')
    related_id = db.Column(db.Integer, comment='关联对象ID')
    related_type = db.Column(db.String(50), comment='关联对象类型')
    expires_at = db.Column(db.DateTime, comment='过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def mark_as_read(self):
        """标记为已读"""
        self.is_read = True
        self.read_at = datetime.utcnow()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'type': self.type,
            'title': self.title,
            'content': self.content,
            'priority': self.priority,
            'is_read': self.is_read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'related_id': self.related_id,
            'related_type': self.related_type,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat()
        }

class WorkflowInstance(db.Model):
    """
    工作流实例模型 - SpiffWorkflow集成版本
    管理业务流程的审批实例，支持BPMN工作流引擎
    """
    __tablename__ = 'workflow_instances'
    
    id = db.Column(db.Integer, primary_key=True, comment='工作流实例ID')
    workflow_key = db.Column(db.String(100), nullable=False, comment='工作流定义键值')
    business_key = db.Column(db.String(100), nullable=False, comment='业务键值')
    business_type = db.Column(db.String(50), comment='业务类型')
    business_id = db.Column(db.Integer, comment='业务对象ID')
    initiator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='发起人ID')
    status = db.Column(db.Enum('RUNNING', 'COMPLETED', 'CANCELLED', 'ERROR', name='workflow_status'), 
                      default='RUNNING', comment='工作流状态')
    variables = db.Column(db.Text, comment='工作流变量(JSON)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    completed_at = db.Column(db.DateTime, comment='完成时间')
    
    # 关系
    initiator = db.relationship('User', backref='initiated_workflows')
    tasks = db.relationship('WorkflowTask', backref='workflow_instance', lazy='dynamic',
                           overlaps="instance,workflow_tasks")
    
    def get_variables(self):
        """获取工作流变量"""
        return json.loads(self.variables) if self.variables else {}
    
    def set_variables(self, variables_dict):
        """设置工作流变量"""
        self.variables = json.dumps(variables_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'workflow_key': self.workflow_key,
            'business_key': self.business_key,
            'business_type': self.business_type,
            'business_id': self.business_id,
            'initiator_id': self.initiator_id,
            'initiator': self.initiator.to_dict() if self.initiator else None,
            'status': self.status,
            'variables': self.get_variables(),
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

class WorkflowTask(db.Model):
    """
    工作流任务模型 - SpiffWorkflow集成版本
    管理工作流中的具体审批任务，支持BPMN任务处理
    """
    __tablename__ = 'workflow_tasks'
    
    id = db.Column(db.Integer, primary_key=True, comment='任务记录ID')
    instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'), 
                           nullable=False, comment='工作流实例ID')
    task_id = db.Column(db.String(100), nullable=False, comment='SpiffWorkflow任务ID')
    task_name = db.Column(db.String(100), nullable=False, comment='任务名称')
    assignee = db.Column(db.String(100), comment='指派人')
    status = db.Column(db.Enum('READY', 'COMPLETED', 'CANCELLED', name='task_status'), 
                      default='READY', comment='任务状态')
    comment = db.Column(db.Text, comment='处理意见')
    task_data = db.Column(db.Text, comment='任务数据(JSON)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    completed_at = db.Column(db.DateTime, comment='完成时间')
    
    # 关系
    instance = db.relationship('WorkflowInstance', 
                               backref=db.backref('workflow_tasks', lazy='dynamic',
                                                 overlaps="tasks,workflow_instance"),
                               overlaps="tasks,workflow_instance")
    
    def get_task_data(self):
        """获取任务数据"""
        return json.loads(self.task_data) if self.task_data else {}
    
    def set_task_data(self, data_dict):
        """设置任务数据"""
        self.task_data = json.dumps(data_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'instance_id': self.instance_id,
            'task_id': self.task_id,
            'task_name': self.task_name,
            'assignee': self.assignee,
            'status': self.status,
            'comment': self.comment,
            'task_data': self.get_task_data(),
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

class SharedMaterial(db.Model):
    """
    共享件模型
    管理用户发布的可共享航材信息和状态
    """
    __tablename__ = 'shared_materials'
    
    id = db.Column(db.Integer, primary_key=True, comment='共享件ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='发布人ID')
    share_type = db.Column(db.Enum('sale', 'lease', 'exchange', 'loan', name='share_types'), 
                          default='sale', comment='共享类型')
    share_quantity = db.Column(db.Integer, nullable=False, comment='共享数量')
    available_quantity = db.Column(db.Integer, nullable=False, comment='可用数量')
    reserved_quantity = db.Column(db.Integer, default=0, comment='预留数量')
    price = db.Column(Numeric(12, 2), nullable=False, comment='单价')
    min_order_quantity = db.Column(db.Integer, default=1, comment='最小订购数量')
    description = db.Column(db.Text, comment='描述信息')
    sharing_policy = db.Column(db.Enum('immediate', 'approval', 'inquiry', name='sharing_policies'), 
                              default='immediate', comment='共享策略')
    condition_code = db.Column(db.Enum('NE', 'NS', 'OH', 'SV', 'AR', name='shared_condition_codes'), default='NE', comment='状况代码')
    location = db.Column(db.String(100), comment='存储位置')
    expiry_date = db.Column(db.DateTime, comment='过期日期')
    
    # 审核相关字段
    status = db.Column(db.Enum('pending_review', 'approved', 'rejected', 'offline', name='shared_material_status'), 
                      default='pending_review', comment='审核状态')
    workflow_instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'), comment='工作流实例ID')
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='审核人ID')
    approved_at = db.Column(db.DateTime, comment='审核时间')
    rejected_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='拒绝人ID')
    rejected_at = db.Column(db.DateTime, comment='拒绝时间')
    review_comment = db.Column(db.Text, comment='审核意见')
    
    # 统计字段
    view_count = db.Column(db.Integer, default=0, comment='查看次数')
    inquiry_count = db.Column(db.Integer, default=0, comment='询价次数')
    transaction_count = db.Column(db.Integer, default=0, comment='交易次数')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    published_at = db.Column(db.DateTime, comment='发布时间')
    
    # 关系
    owner = db.relationship('User', foreign_keys=[owner_id], backref='shared_materials')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_shared_materials')
    rejecter = db.relationship('User', foreign_keys=[rejected_by], backref='rejected_shared_materials')
    workflow_instance = db.relationship('WorkflowInstance', backref='shared_materials')
    
    def update_available_quantity(self, reserved_qty=0):
        """更新可用数量"""
        self.available_quantity = max(0, self.share_quantity - self.reserved_quantity - reserved_qty)
        return self.available_quantity
    
    def can_reserve(self, quantity):
        """检查是否可以预留指定数量"""
        return quantity <= self.available_quantity
    
    def reserve_quantity(self, quantity):
        """预留数量"""
        if not self.can_reserve(quantity):
            raise ValueError("预留数量超过可用数量")
        
        self.reserved_quantity += quantity
        self.available_quantity -= quantity
        return True
    
    def release_reservation(self, quantity):
        """释放预留数量"""
        release_qty = min(quantity, self.reserved_quantity)
        self.reserved_quantity -= release_qty
        self.available_quantity += release_qty
        return release_qty
    
    def increment_view_count(self):
        """增加查看次数"""
        self.view_count += 1
    
    def increment_inquiry_count(self):
        """增加询价次数"""
        self.inquiry_count += 1
    
    def increment_transaction_count(self):
        """增加交易次数"""
        self.transaction_count += 1
    
    def is_available(self):
        """检查是否可用"""
        if self.status != 'approved':
            return False
        if self.available_quantity <= 0:
            return False
        if self.expiry_date and self.expiry_date <= datetime.utcnow():
            return False
        return True
    
    def get_availability_status(self):
        """获取可用状态描述"""
        if not self.is_available():
            return '不可用'
        elif self.available_quantity > 10:
            return '现货充足'
        elif self.available_quantity > 0:
            return '现货'
        else:
            return '缺货'
    
    def to_dict(self, include_owner=False):
        """转换为字典格式"""
        result = {
            'id': self.id,
            'material_id': self.material_id,
            'material': self.material.to_dict() if hasattr(self, 'material') and self.material else None,
            'owner_id': self.owner_id,
            'share_type': self.share_type,
            'share_quantity': self.share_quantity,
            'available_quantity': self.available_quantity,
            'reserved_quantity': self.reserved_quantity,
            'price': float(self.price),
            'min_order_quantity': self.min_order_quantity,
            'description': self.description,
            'sharing_policy': self.sharing_policy,
            'condition_code': self.condition_code,
            'location': self.location,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'status': self.status,
            'workflow_instance_id': self.workflow_instance_id,
            'review_comment': self.review_comment,
            'view_count': self.view_count,
            'inquiry_count': self.inquiry_count,
            'transaction_count': self.transaction_count,
            'availability_status': self.get_availability_status(),
            'is_available': self.is_available(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'published_at': self.published_at.isoformat() if self.published_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejected_at': self.rejected_at.isoformat() if self.rejected_at else None
        }
        
        if include_owner and self.owner:
            result['owner'] = {
                'id': self.owner.id,
                'name': self.owner.real_name or self.owner.username,
                'company': self.owner.company_name,
                'user_type': self.owner.user_type,
                'phone': self.owner.phone
            }
        
        return result


# =====================================================
# 权限系统模型 - 第一阶段开发
# =====================================================

class Permission(db.Model):
    """
    权限模型
    定义系统中所有可用的权限
    """
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True, comment='权限ID')
    code = db.Column(db.String(50), unique=True, nullable=False, comment='权限代码')
    name = db.Column(db.String(100), nullable=False, comment='权限名称')
    description = db.Column(db.String(255), comment='权限描述')
    category = db.Column(db.String(50), comment='权限分类')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class UserPermission(db.Model):
    """
    用户权限关联模型
    记录用户具有的特定权限
    """
    __tablename__ = 'user_permissions'
    
    id = db.Column(db.Integer, primary_key=True, comment='记录ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), nullable=False, comment='权限ID')
    granted_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='授权人ID')
    granted_at = db.Column(db.DateTime, default=datetime.utcnow, comment='授权时间')
    expires_at = db.Column(db.DateTime, comment='过期时间')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    
    # 关系
    user = db.relationship('User', foreign_keys=[user_id], backref='user_permissions')
    permission = db.relationship('Permission', backref='user_permissions')
    grantor = db.relationship('User', foreign_keys=[granted_by])
    
    # 唯一约束
    __table_args__ = (db.UniqueConstraint('user_id', 'permission_id', name='uk_user_permission'),)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'permission_id': self.permission_id,
            'permission_code': self.permission.code if self.permission else None,
            'permission_name': self.permission.name if self.permission else None,
            'granted_by': self.granted_by,
            'granted_at': self.granted_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active
        }


class DataAccessLog(db.Model):
    """
    数据访问日志模型
    记录用户的数据访问行为，用于审计和安全监控
    """
    __tablename__ = 'data_access_logs'
    
    id = db.Column(db.Integer, primary_key=True, comment='日志ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    resource_type = db.Column(db.String(50), nullable=False, comment='资源类型')
    resource_id = db.Column(db.String(50), comment='资源ID')
    action = db.Column(db.String(20), nullable=False, comment='操作类型')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.String(255), comment='用户代理')
    success = db.Column(db.Boolean, default=True, comment='是否成功')
    error_message = db.Column(db.String(255), comment='错误信息')
    additional_data = db.Column(db.Text, comment='附加数据(JSON)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    user = db.relationship('User', backref='access_logs')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'action': self.action,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'success': self.success,
            'error_message': self.error_message,
            'additional_data': self.additional_data,
            'created_at': self.created_at.isoformat()
        }


class CompanyDataScope(db.Model):
    """
    公司数据范围模型
    定义用户可以访问哪些公司的数据
    """
    __tablename__ = 'company_data_scopes'
    
    id = db.Column(db.Integer, primary_key=True, comment='记录ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    company_name = db.Column(db.String(100), nullable=False, comment='公司名称')
    access_type = db.Column(db.Enum('read', 'write', 'admin', name='access_types'), 
                           default='read', comment='访问类型')
    granted_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='授权人ID')
    granted_at = db.Column(db.DateTime, default=datetime.utcnow, comment='授权时间')
    expires_at = db.Column(db.DateTime, comment='过期时间')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    
    # 关系
    user = db.relationship('User', foreign_keys=[user_id], backref='data_scopes')
    grantor = db.relationship('User', foreign_keys=[granted_by])
    
    # 唯一约束
    __table_args__ = (db.UniqueConstraint('user_id', 'company_name', name='uk_user_company_scope'),)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'company_name': self.company_name,
            'access_type': self.access_type,
            'granted_by': self.granted_by,
            'granted_at': self.granted_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active
        }


# =====================================================
# 需求管理扩展模型 - 功能补全
# =====================================================

class DemandMatch(db.Model):
    """
    需求匹配模型
    记录需求与供应商/库存的匹配结果
    """
    __tablename__ = 'demand_matches'
    
    id = db.Column(db.Integer, primary_key=True, comment='匹配ID')
    demand_id = db.Column(db.Integer, db.ForeignKey('demands.id'), nullable=False, comment='需求ID')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='供应商ID')
    inventory_item_id = db.Column(db.Integer, db.ForeignKey('inventory_items.id'), comment='库存项ID')
    shared_material_id = db.Column(db.Integer, db.ForeignKey('shared_materials.id'), comment='共享件ID')
    
    # 匹配评分相关
    match_score = db.Column(Numeric(5, 2), nullable=False, comment='匹配分数(0-100)')
    part_number_match = db.Column(db.Boolean, default=False, comment='零件号是否完全匹配')
    substitute_match = db.Column(db.Boolean, default=False, comment='是否为替代件匹配')
    location_distance = db.Column(Numeric(8, 2), comment='地理距离(km)')
    price_estimate = db.Column(Numeric(12, 2), comment='预估价格')
    
    # 库存信息
    available_quantity = db.Column(db.Integer, comment='可用数量')
    condition_code = db.Column(db.String(10), comment='状况代码')
    estimated_delivery_days = db.Column(db.Integer, comment='预计交货天数')
    
    # 供应商信息
    supplier_rating = db.Column(Numeric(3, 2), comment='供应商评级')
    supplier_certification = db.Column(db.String(100), comment='供应商认证')
    
    # 匹配状态
    status = db.Column(db.Enum('active', 'responded', 'selected', 'expired', name='match_status'), 
                      default='active', comment='匹配状态')
    
    # 时间戳
    matched_at = db.Column(db.DateTime, default=datetime.utcnow, comment='匹配时间')
    expires_at = db.Column(db.DateTime, comment='匹配过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    demand = db.relationship('Demand', backref='matches')
    supplier = db.relationship('User', backref='demand_matches')
    inventory_item = db.relationship('InventoryItem', backref='demand_matches')
    shared_material = db.relationship('SharedMaterial', backref='demand_matches')
    
    def get_match_details(self):
        """获取匹配详细信息"""
        details = {
            'match_type': 'exact' if self.part_number_match else 'substitute' if self.substitute_match else 'category',
            'availability': 'in_stock' if self.available_quantity and self.available_quantity > 0 else 'need_procurement',
            'urgency_compatible': self.estimated_delivery_days <= 7 if self.demand.priority == 'aog' else True,
            'price_competitive': True  # 可以基于市场价格进行计算
        }
        return details
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_id': self.demand_id,
            'supplier_id': self.supplier_id,
            'supplier': {
                'id': self.supplier.id,
                'name': self.supplier.real_name or self.supplier.username,
                'company': self.supplier.company_name,
                'rating': float(self.supplier_rating) if self.supplier_rating else None,
                'certification': self.supplier_certification
            } if self.supplier else None,
            'inventory_item_id': self.inventory_item_id,
            'shared_material_id': self.shared_material_id,
            'match_score': float(self.match_score),
            'part_number_match': self.part_number_match,
            'substitute_match': self.substitute_match,
            'location_distance': float(self.location_distance) if self.location_distance else None,
            'price_estimate': float(self.price_estimate) if self.price_estimate else None,
            'available_quantity': self.available_quantity,
            'condition_code': self.condition_code,
            'estimated_delivery_days': self.estimated_delivery_days,
            'supplier_rating': float(self.supplier_rating) if self.supplier_rating else None,
            'supplier_certification': self.supplier_certification,
            'status': self.status,
            'match_details': self.get_match_details(),
            'matched_at': self.matched_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class DemandHistory(db.Model):
    """
    需求历史记录模型
    记录需求的所有操作历史和状态变更
    """
    __tablename__ = 'demand_history'
    
    id = db.Column(db.Integer, primary_key=True, comment='历史记录ID')
    demand_id = db.Column(db.Integer, db.ForeignKey('demands.id'), nullable=False, comment='需求ID')
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='操作人ID')
    
    # 操作信息
    action = db.Column(db.Enum('created', 'updated', 'matched', 'responded', 'selected', 'cancelled', 
                              'completed', 'expired', name='demand_actions'), 
                      nullable=False, comment='操作类型')
    description = db.Column(db.String(255), nullable=False, comment='操作描述')
    details = db.Column(db.Text, comment='详细信息(JSON)')
    
    # 状态变更
    old_status = db.Column(db.String(50), comment='变更前状态')
    new_status = db.Column(db.String(50), comment='变更后状态')
    
    # 相关对象
    related_object_type = db.Column(db.String(50), comment='关联对象类型')
    related_object_id = db.Column(db.Integer, comment='关联对象ID')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    demand = db.relationship('Demand', backref='history')
    operator = db.relationship('User', backref='operated_demand_history')
    
    def get_details(self):
        """获取详细信息"""
        return json.loads(self.details) if self.details else {}
    
    def set_details(self, details_dict):
        """设置详细信息"""
        self.details = json.dumps(details_dict, ensure_ascii=False)
    
    @classmethod
    def log_action(cls, demand_id, operator_id, action, description, old_status=None, new_status=None, 
                   related_object_type=None, related_object_id=None, details=None):
        """记录操作历史"""
        history = cls(
            demand_id=demand_id,
            operator_id=operator_id,
            action=action,
            description=description,
            old_status=old_status,
            new_status=new_status,
            related_object_type=related_object_type,
            related_object_id=related_object_id
        )
        
        if details:
            history.set_details(details)
        
        db.session.add(history)
        return history
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_id': self.demand_id,
            'operator_id': self.operator_id,
            'operator': {
                'id': self.operator.id,
                'name': self.operator.real_name or self.operator.username,
                'company': self.operator.company_name
            } if self.operator else None,
            'action': self.action,
            'description': self.description,
            'details': self.get_details(),
            'old_status': self.old_status,
            'new_status': self.new_status,
            'related_object_type': self.related_object_type,
            'related_object_id': self.related_object_id,
            'created_at': self.created_at.isoformat()
        }

# ===== 新的角色权限系统模型 =====

class SystemRole(db.Model):
    """
    系统角色模型
    定义系统中所有可用的角色
    """
    __tablename__ = 'system_roles'
    
    id = db.Column(db.Integer, primary_key=True, comment='角色ID')
    role_code = db.Column(db.String(50), unique=True, nullable=False, comment='角色代码')
    role_name = db.Column(db.String(100), nullable=False, comment='角色名称')
    display_name = db.Column(db.String(100), nullable=False, comment='显示名称')
    description = db.Column(db.Text, comment='角色描述')
    category = db.Column(db.Enum('internal', 'external', name='role_categories'), 
                        nullable=False, comment='角色分类')
    business_type = db.Column(db.Enum('buy_only', 'sell_only', 'buy_and_sell', 'service', name='business_types'),
                             nullable=False, comment='业务类型')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    sort_order = db.Column(db.Integer, default=0, comment='排序')
    icon_name = db.Column(db.String(50), comment='图标名称')
    theme_color = db.Column(db.String(20), comment='主题色')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    users = db.relationship('User', backref='system_role', lazy='dynamic')
    permissions = db.relationship('SystemPermission', secondary='role_permissions', 
                                backref=db.backref('roles', lazy='dynamic'))
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'role_code': self.role_code,
            'role_name': self.role_name,
            'display_name': self.display_name,
            'description': self.description,
            'category': self.category,
            'business_type': self.business_type,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'icon_name': self.icon_name,
            'theme_color': self.theme_color,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class SystemPermission(db.Model):
    """
    系统权限模型
    定义系统中所有可用的权限
    """
    __tablename__ = 'system_permissions'
    
    id = db.Column(db.Integer, primary_key=True, comment='权限ID')
    permission_code = db.Column(db.String(100), unique=True, nullable=False, comment='权限代码')
    permission_name = db.Column(db.String(100), nullable=False, comment='权限名称') 
    description = db.Column(db.Text, comment='权限描述')
    category = db.Column(db.String(50), comment='权限分类')
    module = db.Column(db.String(50), comment='所属模块')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'permission_code': self.permission_code,
            'permission_name': self.permission_name,
            'description': self.description,
            'category': self.category,
            'module': self.module,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }

class RolePermission(db.Model):
    """
    角色权限关联模型
    """
    __tablename__ = 'role_permissions'
    
    id = db.Column(db.Integer, primary_key=True, comment='关联ID')
    role_id = db.Column(db.Integer, db.ForeignKey('system_roles.id', ondelete='CASCADE'), 
                       nullable=False, comment='角色ID')
    permission_id = db.Column(db.Integer, db.ForeignKey('system_permissions.id', ondelete='CASCADE'),
                             nullable=False, comment='权限ID')
    granted_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='授权人ID')
    granted_at = db.Column(db.DateTime, default=datetime.utcnow, comment='授权时间')
    
    # 唯一约束
    __table_args__ = (db.UniqueConstraint('role_id', 'permission_id'),)
    
    # 关系
    role = db.relationship('SystemRole', backref='role_permissions')
    permission = db.relationship('SystemPermission', backref='role_permissions')
    granter = db.relationship('User', backref='granted_permissions')

class InvitationCode(db.Model):
    """
    邀请码模型
    """
    __tablename__ = 'invitation_codes'
    
    id = db.Column(db.Integer, primary_key=True, comment='邀请码ID')
    code = db.Column(db.String(20), unique=True, nullable=False, comment='邀请码')
    code_type = db.Column(db.Enum('staff', 'admin', name='invitation_code_types'),
                         default='staff', comment='邀请码类型')
    allowed_roles = db.Column(db.Text, comment='允许的角色列表(JSON格式)')
    max_uses = db.Column(db.Integer, default=1, comment='最大使用次数')
    used_count = db.Column(db.Integer, default=0, comment='已使用次数')
    expires_at = db.Column(db.DateTime, comment='过期时间')
    ip_whitelist = db.Column(db.Text, comment='IP白名单(JSON格式)')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    creator = db.relationship('User', backref='created_invitation_codes')
    uses = db.relationship('InvitationCodeUse', backref='invitation_code', lazy='dynamic')
    
    def get_allowed_roles(self):
        """获取允许的角色列表"""
        if self.allowed_roles:
            try:
                return json.loads(self.allowed_roles)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_allowed_roles(self, roles):
        """设置允许的角色列表"""
        self.allowed_roles = json.dumps(roles) if roles else None
    
    def get_ip_whitelist(self):
        """获取IP白名单"""
        if self.ip_whitelist:
            try:
                return json.loads(self.ip_whitelist)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_ip_whitelist(self, ips):
        """设置IP白名单"""
        self.ip_whitelist = json.dumps(ips) if ips else None
    
    def is_expired(self):
        """检查是否过期"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_exhausted(self):
        """检查是否用尽"""
        return self.used_count >= self.max_uses
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'code': self.code,
            'code_type': self.code_type,
            'allowed_roles': self.get_allowed_roles(),
            'max_uses': self.max_uses,
            'used_count': self.used_count,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'ip_whitelist': self.get_ip_whitelist(),
            'is_active': self.is_active,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat()
        }

class InvitationCodeUse(db.Model):
    """
    邀请码使用记录模型
    """
    __tablename__ = 'invitation_code_uses'
    
    id = db.Column(db.Integer, primary_key=True, comment='使用记录ID')
    invitation_id = db.Column(db.Integer, db.ForeignKey('invitation_codes.id', ondelete='CASCADE'),
                             nullable=False, comment='邀请码ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='使用用户ID')
    ip_address = db.Column(db.String(45), comment='使用IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    used_at = db.Column(db.DateTime, default=datetime.utcnow, comment='使用时间')
    
    # 关系
    user = db.relationship('User', backref='invitation_code_uses')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'invitation_id': self.invitation_id,
            'user_id': self.user_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'used_at': self.used_at.isoformat(),
            'user': {
                'id': self.user.id,
                'username': self.user.username,
                'real_name': self.user.real_name
            } if self.user else None
        }

class AuditLog(db.Model):
    """
    审计日志模型
    """
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True, comment='日志ID')
    log_id = db.Column(db.String(36), unique=True, nullable=False, comment='日志UUID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='用户ID')
    username = db.Column(db.String(100), comment='用户名')
    user_role = db.Column(db.String(50), comment='用户角色')
    operation_type = db.Column(db.String(50), nullable=False, comment='操作类型')
    operation_desc = db.Column(db.String(200), comment='操作描述')
    resource_type = db.Column(db.String(50), comment='资源类型')
    resource_id = db.Column(db.String(100), comment='资源ID')
    request_method = db.Column(db.String(10), comment='请求方法')
    request_path = db.Column(db.String(500), comment='请求路径')
    request_params = db.Column(db.Text, comment='请求参数')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    session_id = db.Column(db.String(100), comment='会话ID')
    before_data = db.Column(db.Text, comment='操作前数据')
    after_data = db.Column(db.Text, comment='操作后数据') 
    risk_level = db.Column(db.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='risk_levels'),
                          default='LOW', comment='风险级别')
    is_success = db.Column(db.Boolean, default=True, comment='是否成功')
    error_message = db.Column(db.Text, comment='错误消息')
    execution_time_ms = db.Column(db.Integer, comment='执行时间(毫秒)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    user = db.relationship('User', backref='audit_logs')
    
    def get_before_data(self):
        """获取操作前数据"""
        if self.before_data:
            try:
                return json.loads(self.before_data)
            except json.JSONDecodeError:
                return self.before_data
        return None
    
    def get_after_data(self):
        """获取操作后数据"""
        if self.after_data:
            try:
                return json.loads(self.after_data)
            except json.JSONDecodeError:
                return self.after_data
        return None
    
    def get_request_params(self):
        """获取请求参数"""
        if self.request_params:
            try:
                return json.loads(self.request_params)
            except json.JSONDecodeError:
                return self.request_params
        return None
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'log_id': self.log_id,
            'user_id': self.user_id,
            'username': self.username,
            'user_role': self.user_role,
            'operation_type': self.operation_type,
            'operation_desc': self.operation_desc,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'request_method': self.request_method,
            'request_path': self.request_path,
            'request_params': self.get_request_params(),
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'session_id': self.session_id,
            'before_data': self.get_before_data(),
            'after_data': self.get_after_data(),
            'risk_level': self.risk_level,
            'is_success': self.is_success,
            'error_message': self.error_message,
            'execution_time_ms': self.execution_time_ms,
            'created_at': self.created_at.isoformat()
        }