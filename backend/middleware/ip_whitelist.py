#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - IP白名单验证中间件
版本: 1.0
创建时间: 2025-07-24

提供基于IP地址的访问控制中间件
"""

import ipaddress
from functools import wraps
from flask import request, current_app
from utils.response import error_response
from typing import List, Optional, Union
import logging

logger = logging.getLogger(__name__)

class IPWhitelistMiddleware:
    """IP白名单中间件类"""
    
    def __init__(self, app=None, default_whitelist: List[str] = None):
        """
        初始化IP白名单中间件
        
        Args:
            app: Flask应用实例
            default_whitelist: 默认白名单列表
        """
        self.default_whitelist = default_whitelist or [
            '127.0.0.1',      # 本地回环
            '::1',            # IPv6本地回环
            '10.0.0.0/8',     # 私有网络A类
            '**********/12',  # 私有网络B类
            '***********/16', # 私有网络C类
            '0.0.0.0/0'       # 开发模式允许所有IP (生产环境应移除)
        ]
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """
        初始化Flask应用
        
        Args:
            app: Flask应用实例
        """
        app.config.setdefault('IP_WHITELIST_ENABLED', True)
        app.config.setdefault('IP_WHITELIST_DEFAULT', self.default_whitelist)
        app.config.setdefault('IP_WHITELIST_STRICT_MODE', False)
        
        # 存储中间件实例到应用扩展中
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions['ip_whitelist'] = self
        
        logger.info("IP白名单中间件初始化完成")
    
    def get_client_ip(self, request) -> str:
        """
        获取客户端真实IP地址
        
        Args:
            request: Flask请求对象
            
        Returns:
            客户端IP地址
        """
        # 优先从代理头获取真实IP
        forwarded_ips = request.headers.get('X-Forwarded-For')
        if forwarded_ips:
            # X-Forwarded-For可能包含多个IP，取第一个
            client_ip = forwarded_ips.split(',')[0].strip()
            logger.debug(f"从X-Forwarded-For获取IP: {client_ip}")
            return client_ip
        
        # 从其他代理头获取
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            logger.debug(f"从X-Real-IP获取IP: {real_ip}")
            return real_ip.strip()
        
        # 从Cloudflare获取
        cf_ip = request.headers.get('CF-Connecting-IP')
        if cf_ip:
            logger.debug(f"从CF-Connecting-IP获取IP: {cf_ip}")
            return cf_ip.strip()
        
        # 从WSGI环境获取
        remote_addr = request.environ.get('REMOTE_ADDR', '127.0.0.1')
        logger.debug(f"从REMOTE_ADDR获取IP: {remote_addr}")
        return remote_addr
    
    def is_ip_allowed(self, client_ip: str, whitelist: List[str] = None) -> bool:
        """
        检查IP是否在白名单中
        
        Args:
            client_ip: 客户端IP地址
            whitelist: 白名单列表，如果为None则使用默认白名单
            
        Returns:
            是否允许访问
        """
        if not whitelist:
            whitelist = current_app.config.get('IP_WHITELIST_DEFAULT', self.default_whitelist)
        
        try:
            client_ip_obj = ipaddress.ip_address(client_ip)
            
            for allowed_ip in whitelist:
                try:
                    # 支持单个IP和CIDR网段
                    if ipaddress.ip_address(client_ip_obj) in ipaddress.ip_network(allowed_ip, strict=False):
                        logger.debug(f"IP {client_ip} 匹配白名单规则: {allowed_ip}")
                        return True
                except ValueError as e:
                    logger.warning(f"无效的白名单IP格式: {allowed_ip}, 错误: {e}")
                    continue
            
            logger.warning(f"IP {client_ip} 不在白名单中")
            return False
            
        except ValueError as e:
            logger.error(f"无效的客户端IP格式: {client_ip}, 错误: {e}")
            return False
    
    def get_whitelist_from_config(self, config_key: str = 'IP_WHITELIST_CUSTOM') -> List[str]:
        """
        从配置获取自定义白名单
        
        Args:
            config_key: 配置键名
            
        Returns:
            白名单列表
        """
        custom_whitelist = current_app.config.get(config_key, [])
        default_whitelist = current_app.config.get('IP_WHITELIST_DEFAULT', self.default_whitelist)
        
        # 合并默认白名单和自定义白名单
        combined_whitelist = list(set(default_whitelist + custom_whitelist))
        
        logger.debug(f"获取到白名单: {combined_whitelist}")
        return combined_whitelist

def require_ip_whitelist(whitelist: Optional[List[str]] = None, strict_mode: bool = None):
    """
    IP白名单验证装饰器
    
    Args:
        whitelist: 自定义白名单列表
        strict_mode: 严格模式，为True时拒绝所有不在白名单的请求
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查是否启用IP白名单
            if not current_app.config.get('IP_WHITELIST_ENABLED', True):
                logger.debug("IP白名单验证已禁用，跳过检查")
                return f(*args, **kwargs)
            
            # 获取中间件实例
            ip_middleware = current_app.extensions.get('ip_whitelist')
            if not ip_middleware:
                logger.error("IP白名单中间件未初始化")
                return error_response(500, '服务器配置错误')
            
            # 获取客户端IP
            client_ip = ip_middleware.get_client_ip(request)
            
            # 确定使用的白名单
            effective_whitelist = whitelist
            if not effective_whitelist:
                effective_whitelist = ip_middleware.get_whitelist_from_config()
            
            # 确定严格模式
            effective_strict_mode = strict_mode
            if effective_strict_mode is None:
                effective_strict_mode = current_app.config.get('IP_WHITELIST_STRICT_MODE', False)
            
            # 检查IP是否被允许
            if not ip_middleware.is_ip_allowed(client_ip, effective_whitelist):
                # 记录安全事件
                logger.warning(f"IP白名单验证失败: IP={client_ip}, 路径={request.path}, 用户代理={request.headers.get('User-Agent', '')}")
                
                if effective_strict_mode:
                    return error_response(403, '访问被拒绝：IP地址不在允许范围内')
                else:
                    # 非严格模式下记录警告但允许继续
                    logger.info(f"非严格模式：允许IP {client_ip} 继续访问")
            else:
                logger.debug(f"IP白名单验证通过: {client_ip}")
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def require_internal_ip():
    """
    要求内部网络IP访问的装饰器
    
    Returns:
        装饰器函数
    """
    internal_networks = [
        '127.0.0.1',          # 本地回环
        '::1',                # IPv6本地回环
        '10.0.0.0/8',         # 私有网络A类
        '**********/12',      # 私有网络B类
        '***********/16',     # 私有网络C类
    ]
    
    return require_ip_whitelist(whitelist=internal_networks, strict_mode=True)

def require_admin_ip():
    """
    要求管理员网络IP访问的装饰器（更严格的白名单，如VPN网段）
    
    Returns:
        装饰器函数
    """
    admin_networks = [
        '127.0.0.1',          # 本地回环
        '*********/16',       # 管理员VPN网段（示例）
        '*************/24',   # 管理员办公网段（示例）
    ]
    
    return require_ip_whitelist(whitelist=admin_networks, strict_mode=True)

class IPWhitelistConfig:
    """IP白名单配置管理类"""
    
    @staticmethod
    def load_from_database():
        """
        从数据库加载IP白名单配置
        
        Returns:
            白名单配置字典
        """
        try:
            from sqlalchemy import text
            from models import db
            
            # 查询IP白名单配置
            sql = """
            SELECT config_key, config_value, is_active 
            FROM system_config 
            WHERE config_key LIKE 'ip_whitelist_%' AND is_active = true
            """
            
            result = db.session.execute(text(sql)).fetchall()
            
            config = {}
            for row in result:
                config_key = row[0]
                config_value = row[1]
                
                # 解析配置值
                if config_key == 'ip_whitelist_enabled':
                    config['enabled'] = config_value.lower() == 'true'
                elif config_key == 'ip_whitelist_strict_mode':
                    config['strict_mode'] = config_value.lower() == 'true'
                elif config_key == 'ip_whitelist_custom':
                    # 自定义白名单，以逗号分隔
                    config['custom_whitelist'] = [ip.strip() for ip in config_value.split(',') if ip.strip()]
            
            logger.info(f"从数据库加载IP白名单配置: {config}")
            return config
            
        except Exception as e:
            logger.error(f"从数据库加载IP白名单配置失败: {e}")
            return {}
    
    @staticmethod
    def save_to_database(config: dict):
        """
        保存IP白名单配置到数据库
        
        Args:
            config: 配置字典
        """
        try:
            from sqlalchemy import text
            from models import db
            
            # 更新或插入配置
            for key, value in config.items():
                config_key = f"ip_whitelist_{key}"
                
                if isinstance(value, bool):
                    config_value = 'true' if value else 'false'
                elif isinstance(value, list):
                    config_value = ','.join(value)
                else:
                    config_value = str(value)
                
                # 使用UPSERT操作
                upsert_sql = """
                INSERT INTO system_config (config_key, config_value, is_active, updated_at) 
                VALUES (:config_key, :config_value, true, CURRENT_TIMESTAMP)
                ON CONFLICT (config_key) 
                DO UPDATE SET 
                    config_value = EXCLUDED.config_value,
                    updated_at = EXCLUDED.updated_at
                """
                
                db.session.execute(text(upsert_sql), {
                    'config_key': config_key,
                    'config_value': config_value
                })
            
            db.session.commit()
            logger.info(f"IP白名单配置保存成功: {config}")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存IP白名单配置失败: {e}")
            raise

# 创建全局中间件实例
ip_whitelist_middleware = IPWhitelistMiddleware()