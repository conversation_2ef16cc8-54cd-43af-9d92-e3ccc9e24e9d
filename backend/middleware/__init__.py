#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 中间件初始化模块
版本: 1.0
创建时间: 2025-07-24

统一管理和初始化各种中间件
"""

from .ip_whitelist import ip_whitelist_middleware, IPWhitelistMiddleware
from .rate_limit import rate_limit_middleware, RateLimitMiddleware

# 导出中间件实例和类
__all__ = [
    'ip_whitelist_middleware',
    'IPWhitelistMiddleware', 
    'rate_limit_middleware',
    'RateLimitMiddleware',
    'init_middlewares'
]

def init_middlewares(app):
    """
    初始化所有中间件
    
    Args:
        app: Flask应用实例
    """
    # 初始化IP白名单中间件
    ip_whitelist_middleware.init_app(app)
    
    # 初始化频率限制中间件
    rate_limit_middleware.init_app(app)
    
    # 初始化审计日志
    from utils.audit_logger import audit_logger
    audit_logger.init_app(app)
    
    # 配置中间件默认设置
    _configure_middleware_defaults(app)
    
    print("🔧 所有中间件初始化完成")

def _configure_middleware_defaults(app):
    """
    配置中间件默认设置
    
    Args:
        app: Flask应用实例
    """
    # IP白名单默认配置
    app.config.setdefault('IP_WHITELIST_ENABLED', True)
    app.config.setdefault('IP_WHITELIST_STRICT_MODE', False)
    
    # 频率限制默认配置
    app.config.setdefault('RATE_LIMIT_ENABLED', True)
    app.config.setdefault('RATE_LIMIT_STORAGE', 'memory')  # 开发环境使用内存存储
    
    # 审计日志默认配置
    app.config.setdefault('AUDIT_LOG_ENABLED', True)
    app.config.setdefault('AUDIT_LOG_RETENTION_DAYS', 365)