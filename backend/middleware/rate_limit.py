#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 频率限制中间件
版本: 1.0
创建时间: 2025-07-24

提供基于滑动窗口算法的API频率限制中间件
支持多种限制策略：IP、用户、API端点
"""

import time
import json
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
from functools import wraps
from flask import request, current_app, g
from flask_jwt_extended import get_current_user, verify_jwt_in_request
from utils.response import error_response
from typing import Dict, List, Optional, Tuple, Union
import logging
import hashlib

logger = logging.getLogger(__name__)

class RateLimitMiddleware:
    """频率限制中间件类"""
    
    def __init__(self, app=None, redis_client=None):
        """
        初始化频率限制中间件
        
        Args:
            app: Flask应用实例
            redis_client: Redis客户端实例
        """
        self.redis_client = redis_client
        self.default_limits = {
            'default': {'requests': 100, 'window': 60},        # 默认：每分钟100次
            'auth': {'requests': 10, 'window': 60},            # 认证接口：每分钟10次
            'upload': {'requests': 5, 'window': 60},           # 上传接口：每分钟5次
            'search': {'requests': 50, 'window': 60},          # 搜索接口：每分钟50次
            'admin': {'requests': 200, 'window': 60},          # 管理接口：每分钟200次
        }
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """
        初始化Flask应用
        
        Args:
            app: Flask应用实例
        """
        app.config.setdefault('RATE_LIMIT_ENABLED', True)
        app.config.setdefault('RATE_LIMIT_STORAGE', 'redis')
        app.config.setdefault('RATE_LIMIT_REDIS_URL', 'redis://localhost:6379/0')
        app.config.setdefault('RATE_LIMIT_KEY_PREFIX', 'rate_limit:')
        app.config.setdefault('RATE_LIMIT_DEFAULT_LIMITS', self.default_limits)
        app.config.setdefault('RATE_LIMIT_SKIP_FAILED_REQUEST', True)
        app.config.setdefault('RATE_LIMIT_HEADERS_ENABLED', True)
        
        # 初始化Redis连接
        if not self.redis_client and REDIS_AVAILABLE:
            try:
                redis_url = app.config.get('RATE_LIMIT_REDIS_URL')
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # 测试连接
                self.redis_client.ping()
                logger.info(f"频率限制中间件Redis连接成功: {redis_url}")
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存存储: {e}")
                # 使用内存存储作为fallback
                self.redis_client = None
                app.config['RATE_LIMIT_STORAGE'] = 'memory'
        elif not REDIS_AVAILABLE:
            logger.warning("Redis模块未安装，使用内存存储")
            self.redis_client = None
            app.config['RATE_LIMIT_STORAGE'] = 'memory'
        
        # 内存存储（当Redis不可用时）
        if app.config['RATE_LIMIT_STORAGE'] == 'memory':
            if not hasattr(app, 'rate_limit_storage'):
                app.rate_limit_storage = {}
        
        # 存储中间件实例到应用扩展中
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions['rate_limit'] = self
        
        logger.info("频率限制中间件初始化完成")
    
    def get_client_id(self, request, use_user_id: bool = True) -> str:
        """
        获取客户端标识符
        
        Args:
            request: Flask请求对象
            use_user_id: 是否优先使用用户ID
            
        Returns:
            客户端标识符
        """
        client_parts = []
        
        # 优先使用用户ID（如果已认证）
        if use_user_id:
            try:
                verify_jwt_in_request(optional=True)
                current_user = get_current_user()
                if current_user:
                    client_parts.append(f"user:{current_user.get('id', 'unknown')}")
            except Exception:
                pass
        
        # 使用IP地址
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', 
                                       request.environ.get('REMOTE_ADDR', '127.0.0.1'))
        if ',' in ip_address:
            ip_address = ip_address.split(',')[0].strip()
        client_parts.append(f"ip:{ip_address}")
        
        # 使用User-Agent作为辅助标识
        user_agent = request.headers.get('User-Agent', '')
        if user_agent:
            # 对User-Agent进行hash以节省存储空间
            ua_hash = hashlib.md5(user_agent.encode()).hexdigest()[:8]
            client_parts.append(f"ua:{ua_hash}")
        
        return ':'.join(client_parts)
    
    def get_rate_limit_key(self, client_id: str, endpoint: str, method: str) -> str:
        """
        生成频率限制存储键
        
        Args:
            client_id: 客户端标识符
            endpoint: API端点
            method: HTTP方法
            
        Returns:
            存储键
        """
        prefix = current_app.config.get('RATE_LIMIT_KEY_PREFIX', 'rate_limit:')
        return f"{prefix}{client_id}:{method}:{endpoint}"
    
    def get_limit_config(self, endpoint: str, method: str) -> Dict[str, int]:
        """
        获取端点的限制配置
        
        Args:
            endpoint: API端点
            method: HTTP方法
            
        Returns:
            限制配置字典 {'requests': int, 'window': int}
        """
        default_limits = current_app.config.get('RATE_LIMIT_DEFAULT_LIMITS', self.default_limits)
        
        # 按端点匹配限制策略
        if '/auth/' in endpoint:
            return default_limits.get('auth', default_limits['default'])
        elif '/upload' in endpoint or 'upload' in endpoint:
            return default_limits.get('upload', default_limits['default'])
        elif '/search' in endpoint or 'search' in endpoint:
            return default_limits.get('search', default_limits['default'])
        elif '/admin/' in endpoint or '/roles/' in endpoint or '/permissions/' in endpoint:
            return default_limits.get('admin', default_limits['default'])
        else:
            return default_limits.get('default')
    
    def is_rate_limited(self, client_id: str, endpoint: str, method: str) -> Tuple[bool, Dict[str, int]]:
        """
        检查是否超过频率限制
        
        Args:
            client_id: 客户端标识符
            endpoint: API端点
            method: HTTP方法
            
        Returns:
            (是否限制, 限制信息字典)
        """
        limit_config = self.get_limit_config(endpoint, method)
        max_requests = limit_config['requests']
        window_seconds = limit_config['window']
        
        # 滑动窗口算法实现
        current_time = int(time.time())
        window_start = current_time - window_seconds
        
        key = self.get_rate_limit_key(client_id, endpoint, method)
        
        try:
            if self.redis_client:
                # 使用Redis实现滑动窗口
                pipe = self.redis_client.pipeline()
                
                # 移除窗口外的记录
                pipe.zremrangebyscore(key, 0, window_start)
                
                # 获取当前窗口内的请求数量
                pipe.zcard(key)
                
                # 添加当前请求
                pipe.zadd(key, {str(current_time): current_time})
                
                # 设置过期时间
                pipe.expire(key, window_seconds + 10)
                
                results = pipe.execute()
                current_requests = results[1]
                
            else:
                # 使用内存存储
                storage = current_app.rate_limit_storage
                
                if key not in storage:
                    storage[key] = []
                
                # 清理窗口外的记录
                storage[key] = [timestamp for timestamp in storage[key] if timestamp > window_start]
                
                current_requests = len(storage[key])
                
                # 添加当前请求
                storage[key].append(current_time)
            
            # 计算剩余请求数和重置时间
            remaining_requests = max(0, max_requests - current_requests - 1)
            reset_time = current_time + window_seconds
            
            limit_info = {
                'limit': max_requests,
                'remaining': remaining_requests,
                'reset': reset_time,
                'reset_after': window_seconds,
                'current_requests': current_requests + 1
            }
            
            is_limited = current_requests >= max_requests
            
            if is_limited:
                logger.warning(f"频率限制触发: client={client_id}, endpoint={endpoint}, "
                             f"requests={current_requests + 1}/{max_requests}")
            
            return is_limited, limit_info
            
        except Exception as e:
            logger.error(f"频率限制检查失败: {e}")
            # 发生错误时不限制，但记录日志
            return False, {
                'limit': max_requests,
                'remaining': max_requests,
                'reset': current_time + window_seconds,
                'reset_after': window_seconds,
                'current_requests': 0,
                'error': str(e)
            }
    
    def add_rate_limit_headers(self, response, limit_info: Dict[str, int]):
        """
        添加频率限制响应头
        
        Args:
            response: Flask响应对象
            limit_info: 限制信息字典
        """
        if not current_app.config.get('RATE_LIMIT_HEADERS_ENABLED', True):
            return
        
        response.headers['X-RateLimit-Limit'] = str(limit_info['limit'])
        response.headers['X-RateLimit-Remaining'] = str(limit_info['remaining'])
        response.headers['X-RateLimit-Reset'] = str(limit_info['reset'])
        response.headers['X-RateLimit-Reset-After'] = str(limit_info['reset_after'])
        
        if 'error' in limit_info:
            response.headers['X-RateLimit-Error'] = limit_info['error']

def rate_limit(per_minute: Optional[int] = None, per_hour: Optional[int] = None, 
              per_day: Optional[int] = None, key_func=None, skip_if=None,
              error_message: str = None):
    """
    频率限制装饰器
    
    Args:
        per_minute: 每分钟允许的请求次数
        per_hour: 每小时允许的请求次数
        per_day: 每天允许的请求次数
        key_func: 自定义键生成函数
        skip_if: 跳过限制的条件函数
        error_message: 自定义错误消息
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查是否启用频率限制
            if not current_app.config.get('RATE_LIMIT_ENABLED', True):
                return f(*args, **kwargs)
            
            # 检查跳过条件
            if skip_if and skip_if():
                return f(*args, **kwargs)
            
            # 获取中间件实例
            rate_limiter = current_app.extensions.get('rate_limit')
            if not rate_limiter:
                logger.error("频率限制中间件未初始化")
                return f(*args, **kwargs)
            
            # 生成客户端标识符
            if key_func:
                client_id = key_func()
            else:
                client_id = rate_limiter.get_client_id(request)
            
            endpoint = request.endpoint or 'unknown'
            method = request.method
            
            # 检查频率限制
            is_limited, limit_info = rate_limiter.is_rate_limited(client_id, endpoint, method)
            
            if is_limited:
                # 记录限制事件
                logger.warning(f"API频率限制: client={client_id}, endpoint={endpoint}, "
                             f"limit={limit_info.get('limit')}")
                
                # 创建错误响应
                message = error_message or f"请求频率过高，请在 {limit_info.get('reset_after', 60)} 秒后重试"
                response = error_response(429, message, {
                    'rate_limit': limit_info
                })
                
                # 添加频率限制头
                rate_limiter.add_rate_limit_headers(response, limit_info)
                return response
            
            # 执行原函数
            result = f(*args, **kwargs)
            
            # 为成功的响应添加频率限制头
            if hasattr(result, 'headers'):
                rate_limiter.add_rate_limit_headers(result, limit_info)
            
            return result
        
        return decorated_function
    return decorator

def rate_limit_per_minute(requests: int, error_message: str = None):
    """每分钟频率限制装饰器"""
    return rate_limit(per_minute=requests, error_message=error_message)

def rate_limit_per_hour(requests: int, error_message: str = None):
    """每小时频率限制装饰器"""
    return rate_limit(per_hour=requests, error_message=error_message)

def rate_limit_auth():
    """认证接口专用频率限制装饰器"""
    return rate_limit_per_minute(10, "登录尝试过于频繁，请稍后再试")

def rate_limit_upload():
    """上传接口专用频率限制装饰器"""
    return rate_limit_per_minute(5, "上传请求过于频繁，请稍后再试")

def exempt_from_rate_limit():
    """
    豁免频率限制的装饰器
    通常用于系统内部接口或健康检查接口
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 设置标记表示此请求豁免频率限制
            g.rate_limit_exempt = True
            return f(*args, **kwargs)
        return decorated_function
    return decorator

class RateLimitConfig:
    """频率限制配置管理类"""
    
    @staticmethod
    def load_from_database():
        """
        从数据库加载频率限制配置
        
        Returns:
            限制配置字典
        """
        try:
            from sqlalchemy import text
            from models import db
            
            # 查询频率限制配置
            sql = """
            SELECT config_key, config_value, is_active 
            FROM system_config 
            WHERE config_key LIKE 'rate_limit_%' AND is_active = true
            """
            
            result = db.session.execute(text(sql)).fetchall()
            
            config = {}
            for row in result:
                config_key = row[0]
                config_value = row[1]
                
                # 解析配置值
                if config_key == 'rate_limit_enabled':
                    config['enabled'] = config_value.lower() == 'true'
                elif config_key.startswith('rate_limit_') and config_key.endswith('_requests'):
                    # 解析各类接口的请求限制
                    endpoint_type = config_key.replace('rate_limit_', '').replace('_requests', '')
                    if endpoint_type not in config:
                        config[endpoint_type] = {}
                    config[endpoint_type]['requests'] = int(config_value)
                elif config_key.startswith('rate_limit_') and config_key.endswith('_window'):
                    # 解析各类接口的时间窗口
                    endpoint_type = config_key.replace('rate_limit_', '').replace('_window', '')
                    if endpoint_type not in config:
                        config[endpoint_type] = {}
                    config[endpoint_type]['window'] = int(config_value)
            
            logger.info(f"从数据库加载频率限制配置: {config}")
            return config
            
        except Exception as e:
            logger.error(f"从数据库加载频率限制配置失败: {e}")
            return {}
    
    @staticmethod
    def save_to_database(config: dict):
        """
        保存频率限制配置到数据库
        
        Args:
            config: 配置字典
        """
        try:
            from sqlalchemy import text
            from models import db
            
            # 保存配置到数据库
            config_items = []
            
            if 'enabled' in config:
                config_items.append(('rate_limit_enabled', 'true' if config['enabled'] else 'false'))
            
            # 保存各端点的限制配置
            for endpoint_type, limits in config.items():
                if isinstance(limits, dict) and 'requests' in limits:
                    config_items.append((f'rate_limit_{endpoint_type}_requests', str(limits['requests'])))
                if isinstance(limits, dict) and 'window' in limits:
                    config_items.append((f'rate_limit_{endpoint_type}_window', str(limits['window'])))
            
            # 批量更新配置
            for config_key, config_value in config_items:
                upsert_sql = """
                INSERT INTO system_config (config_key, config_value, is_active, updated_at) 
                VALUES (:config_key, :config_value, true, CURRENT_TIMESTAMP)
                ON CONFLICT (config_key) 
                DO UPDATE SET 
                    config_value = EXCLUDED.config_value,
                    updated_at = EXCLUDED.updated_at
                """
                
                db.session.execute(text(upsert_sql), {
                    'config_key': config_key,
                    'config_value': config_value
                })
            
            db.session.commit()
            logger.info(f"频率限制配置保存成功: {len(config_items)} 项配置")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存频率限制配置失败: {e}")
            raise

# 创建全局中间件实例
rate_limit_middleware = RateLimitMiddleware()