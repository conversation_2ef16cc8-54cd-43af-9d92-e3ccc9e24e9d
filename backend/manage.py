#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据管理命令行工具
版本: 1.0
创建时间: 2025-01-15

提供数据初始化、清理、备份等管理功能
"""

import click
import os
import shutil
from datetime import datetime
from flask import Flask
from models import db
from utils.init_data import DataManager
from config import Config

def create_app():
    """创建Flask应用实例"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

@click.group()
def cli():
    """航材共享保障平台数据管理工具"""
    pass

@cli.command('init-data')
@click.option('--mode', default='development',
              type=click.Choice(['development', 'demo', 'production']),
              help='初始化模式: development(开发), demo(演示), production(生产)')
def init_data(mode):
    """
    初始化数据
    
    模式说明:
    - development: 基础配置 + 演示数据 + 测试数据 (完整开发环境)
    - demo: 基础配置 + 演示数据 (演示环境)
    - production: 仅基础配置 (生产环境)
    """
    app = create_app()
    with app.app_context():
        click.echo(f"正在以 {mode} 模式初始化数据...")
        
        # 创建数据库表
        db.create_all()
        
        if mode == 'development':
            DataManager.init_base_data()
            DataManager.init_demo_data() 
            DataManager.init_test_data()
            click.echo("✅ 开发环境数据初始化完成")
            
        elif mode == 'demo':
            DataManager.init_base_data()
            DataManager.init_demo_data()
            click.echo("✅ 演示环境数据初始化完成")
            
        elif mode == 'production':
            DataManager.init_base_data()
            click.echo("✅ 生产环境数据初始化完成")

@cli.command('clean-data')
@click.option('--type', default='test',
              type=click.Choice(['test', 'all']),
              help='清理数据类型: test(测试数据), all(所有数据)')
@click.confirmation_option(prompt='确定要清理数据吗？此操作不可恢复')
def clean_data(type):
    """
    清理数据
    
    类型说明:
    - test: 仅清理测试数据，保留基础配置和演示数据
    - all: 清理所有数据，重置数据库
    """
    app = create_app()
    with app.app_context():
        if type == 'test':
            DataManager.clean_test_data()
            click.echo("✅ 测试数据清理完成")
            
        elif type == 'all':
            # 删除所有表
            db.drop_all()
            click.echo("✅ 所有数据清理完成")

@cli.command()
@click.option('--output', default=None, help='备份文件路径')
def backup(output):
    """备份数据库"""
    app = create_app()
    with app.app_context():
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if output is None:
            output = f"backup_{timestamp}.db"
        
        # 获取当前数据库文件路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        
        if os.path.exists(db_path):
            shutil.copy2(db_path, output)
            click.echo(f"✅ 数据库备份完成: {output}")
        else:
            click.echo("❌ 数据库文件不存在")

@cli.command()
@click.option('--input', required=True, help='备份文件路径')
@click.confirmation_option(prompt='确定要恢复数据库吗？当前数据将被覆盖')
def restore(input):
    """从备份文件恢复数据库"""
    app = create_app()
    with app.app_context():
        # 获取当前数据库文件路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        
        if os.path.exists(input):
            shutil.copy2(input, db_path)
            click.echo(f"✅ 数据库恢复完成: {input}")
        else:
            click.echo("❌ 备份文件不存在")

@cli.command()
def status():
    """查看数据库状态"""
    app = create_app()
    with app.app_context():
        from models import User, Material, Order, InventoryItem
        
        click.echo("📊 数据库状态:")
        click.echo(f"  用户数量: {User.query.count()}")
        click.echo(f"  航材数量: {Material.query.count()}")
        click.echo(f"  订单数量: {Order.query.count()}")
        click.echo(f"  库存项目: {InventoryItem.query.count()}")
        
        # 显示用户类型分布
        click.echo("\n👥 用户类型分布:")
        for user_type in ['airline', 'supplier', 'maintenance', 'admin']:
            count = User.query.filter_by(user_type=user_type).count()
            if count > 0:
                click.echo(f"  {user_type}: {count}")

@cli.command('create-admin')
def create_admin():
    """创建管理员用户"""
    app = create_app()
    with app.app_context():
        from models import User
        
        username = click.prompt('管理员用户名')
        email = click.prompt('管理员邮箱')
        password = click.prompt('管理员密码', hide_input=True)
        real_name = click.prompt('真实姓名', default='系统管理员')
        
        # 检查用户是否已存在
        existing_user = User.query.filter(
            (User.username == username) | (User.email == email)
        ).first()
        
        if existing_user:
            click.echo("❌ 用户名或邮箱已存在")
            return
        
        # 创建管理员用户
        admin_user = User(
            username=username,
            email=email,
            user_type='admin',
            company_name='系统管理',
            real_name=real_name,
            phone='',
            status='active'
        )
        admin_user.set_password(password)
        
        db.session.add(admin_user)
        db.session.commit()
        
        click.echo("✅ 管理员用户创建成功")

@cli.command('test-login')
def test_login():
    """测试登录功能"""
    app = create_app()
    with app.app_context():
        from models import User
        
        # 显示可用的演示账号
        demo_users = User.query.filter(User.username.like('%demo%')).all()
        
        if demo_users:
            click.echo("🔑 可用的演示账号:")
            for user in demo_users:
                click.echo(f"  用户名: {user.username}")
                click.echo(f"  邮箱: {user.email}")
                click.echo(f"  类型: {user.user_type}")
                click.echo(f"  公司: {user.company_name}")
                click.echo("  密码: demo123")
                click.echo("  ---")
        else:
            click.echo("❌ 没有找到演示账号，请先运行: python manage.py init_data --mode=demo")

if __name__ == '__main__':
    cli()
