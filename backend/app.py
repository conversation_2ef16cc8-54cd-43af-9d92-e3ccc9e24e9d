#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - Flask后端应用主入口
版本: 1.0
创建时间: 2025-01-13
作者: <PERSON> AI Assistant

这是航材共享保障平台的Flask后端应用，提供完整的RESTful API服务。
支持用户认证、航材管理、订单处理、库存管理、质量控制等核心业务功能。
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, Blueprint
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from flask_jwt_extended import JWTManager, jwt_required
from werkzeug.exceptions import HTTPException

# 导入配置
from config import Config

# 导入数据库实例
from models import db

# 导入蓝图路由
from routes.auth import auth_bp
from routes.aog import aog_bp
from routes.analytics import analytics_bp
from routes.quality import quality_bp
from routes.maintenance import maintenance_bp
from routes.materials import materials_bp
from routes.orders import orders_bp
from routes.inventory import inventory_bp
from routes.demands import demands_bp
from routes.notifications import notifications_bp
from routes.workflow import workflow_bp
from routes.portal import portal_bp
from routes.shared_materials import shared_materials_bp

# 导入新的API蓝图 (第三阶段角色系统)
from routes.api.v1.roles import roles_bp
from routes.api.v1.permissions import permissions_bp
from routes.api.v1.invitation_codes import invitation_codes_bp
from routes.api.v1.auth import auth_bp as auth_v1_bp
from routes.api.v1.users import users_bp
# 创建其他蓝图
# 物流管理蓝图
logistics_bp = Blueprint('logistics', __name__)

@logistics_bp.route('/tracking/<tracking_number>', methods=['GET'])
@jwt_required()
def track_logistics(tracking_number):
    """物流跟踪"""
    try:
        # 模拟物流跟踪数据
        tracking_info = {
            'tracking_number': tracking_number,
            'status': 'in_transit',
            'current_location': '北京分拣中心',
            'estimated_delivery': (datetime.now() + timedelta(days=1)).isoformat(),
            'tracking_history': [
                {
                    'time': (datetime.now() - timedelta(hours=2)).isoformat(),
                    'location': '北京分拣中心',
                    'status': '已发出'
                }
            ]
        }
        
        return jsonify({
            'error': 0,
            'message': '物流信息获取成功',
            'body': tracking_info,
            'success': True
        })
    except Exception as e:
        return jsonify({
            'error': 500,
            'message': f'获取物流信息失败: {str(e)}',
            'body': {},
            'success': False
        }), 500

# 注意：工作流蓝图已在routes/workflow.py中定义，这里不需要重复定义

def create_app(config_class=Config):
    """
    应用工厂函数 - 创建并配置Flask应用实例
    
    Args:
        config_class: 配置类，默认使用Config
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    
    # 数据库迁移
    migrate = Migrate(app, db)
    
    # 初始化中间件
    from middleware import init_middlewares
    init_middlewares(app)
    
    # 跨域请求支持
    CORS(app, resources={
        r"/api/*": {
            "origins": [
                "http://localhost:3000", 
                "http://localhost:3001",
                "http://localhost:5173",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:3001",
                "http://127.0.0.1:5173"
            ],
            "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # JWT认证管理
    jwt = JWTManager(app)
    
    # 配置日志
    if not app.debug and not app.testing:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler('logs/aviation_platform.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('航材共享保障平台启动')
    
    # 注册蓝图路由
    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
    app.register_blueprint(aog_bp, url_prefix='/api/v1/aog')
    app.register_blueprint(analytics_bp, url_prefix='/api/v1/analytics')
    app.register_blueprint(quality_bp, url_prefix='/api/v1/quality')
    app.register_blueprint(maintenance_bp, url_prefix='/api/v1/maintenance')
    app.register_blueprint(materials_bp, url_prefix='/api/v1/materials')
    app.register_blueprint(orders_bp, url_prefix='/api/v1/orders')
    app.register_blueprint(inventory_bp, url_prefix='/api/v1/inventory')
    app.register_blueprint(logistics_bp, url_prefix='/api/v1/logistics')
    app.register_blueprint(notifications_bp, url_prefix='/api/v1/notifications')
    app.register_blueprint(demands_bp, url_prefix='/api/v1/demands')
    app.register_blueprint(workflow_bp, url_prefix='/api/v1/workflow')
    app.register_blueprint(portal_bp, url_prefix='/api/v1')
    app.register_blueprint(shared_materials_bp, url_prefix='/api/v1')
    
    # 注册新的API蓝图 (第三阶段角色系统)
    app.register_blueprint(roles_bp, url_prefix='/api/v1/roles')
    app.register_blueprint(permissions_bp, url_prefix='/api/v1/permissions')
    app.register_blueprint(invitation_codes_bp, url_prefix='/api/v1/invitation-codes')
    app.register_blueprint(auth_v1_bp, url_prefix='/api/v1/auth')
    app.register_blueprint(users_bp, url_prefix='/api/v1/users')
    
    # 根路径健康检查
    @app.route('/')
    def health_check():
        """
        健康检查接口
        
        Returns:
            dict: 包含服务状态和时间戳的响应
        """
        return jsonify({
            'error': 0,
            'message': '航材共享保障平台API服务正常运行',
            'body': {
                'service': 'Aviation Parts Sharing Platform',
                'version': '1.0',
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            },
            'success': True
        })
    
    # API版本信息
    @app.route('/api/v1')
    def api_info():
        """
        API版本信息接口
        
        Returns:
            dict: API版本和功能模块信息
        """
        return jsonify({
            'error': 0,
            'message': 'API信息获取成功',
            'body': {
                'version': 'v1.0',
                'description': '航材共享保障平台RESTful API',
                'modules': [
                    'auth - 用户认证',
                    'aog - AOG紧急响应', 
                    'analytics - 数据分析',
                    'quality - 质量管理',
                    'maintenance - 维修管理',
                    'materials - 航材管理',
                    'orders - 订单管理',
                    'inventory - 库存管理',
                    'logistics - 物流管理',
                    'notifications - 通知管理',
                    'demands - 需求管理',
                    'workflow - 工作流管理',
                    'roles - 角色管理 (v2.0)',
                    'permissions - 权限管理 (v2.0)',
                    'invitation-codes - 邀请码管理 (v2.0)'
                ]
            },
            'success': True
        })
    
    # 全局错误处理
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """
        HTTP异常处理器
        
        Args:
            error: HTTP异常对象
            
        Returns:
            tuple: JSON响应和状态码
        """
        return jsonify({
            'error': error.code,
            'message': error.description,
            'body': {},
            'success': False
        }), error.code
    
    @app.errorhandler(Exception)
    def handle_general_exception(error):
        """
        通用异常处理器
        
        Args:
            error: 异常对象
            
        Returns:
            tuple: JSON响应和状态码
        """
        app.logger.error(f'服务器内部错误: {str(error)}')
        return jsonify({
            'error': 500,
            'message': '服务器内部错误，请稍后重试',
            'body': {},
            'success': False
        }), 500
    
    # JWT错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        """
        JWT令牌过期处理

        Returns:
            tuple: JSON响应和状态码
        """
        # 添加调试日志
        print(f"🔒 JWT令牌过期 - 用户ID: {jwt_payload.get('sub')}, 过期时间: {jwt_payload.get('exp')}")

        return jsonify({
            'error': 401,
            'message': '访问令牌已过期，请重新登录',
            'body': {
                'expired_at': jwt_payload.get('exp'),
                'user_id': jwt_payload.get('sub')
            },
            'success': False
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        """
        JWT令牌无效处理

        Returns:
            tuple: JSON响应和状态码
        """
        # 添加调试日志
        print(f"🔒 JWT令牌无效 - 错误: {str(error)}")

        return jsonify({
            'error': 401,
            'message': '无效的访问令牌',
            'body': {
                'error_detail': str(error)
            },
            'success': False
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        """
        JWT令牌缺失处理

        Returns:
            tuple: JSON响应和状态码
        """
        # 添加调试日志
        print(f"🔒 JWT令牌缺失 - 错误: {str(error)}")

        return jsonify({
            'error': 401,
            'message': '访问此资源需要提供访问令牌',
            'body': {
                'error_detail': str(error)
            },
            'success': False
        }), 401
    
    return app

# 创建应用实例
app = create_app()

# 添加Flask-Migrate CLI命令支持
from flask.cli import with_appcontext
import click

@click.group()
def db_cli():
    """数据库迁移命令"""
    pass

@db_cli.command()
@with_appcontext  
def init():
    """初始化数据库迁移环境"""
    from flask_migrate import init as migrate_init
    migrate_init()
    click.echo('✅ 数据库迁移环境初始化完成')

@db_cli.command()
@click.option('-m', '--message', required=True, help='迁移消息')
@with_appcontext
def migrate(message):
    """生成迁移文件"""
    from flask_migrate import migrate as db_migrate
    db_migrate(message=message)
    click.echo(f'✅ 迁移文件生成完成: {message}')

@db_cli.command()
@with_appcontext
def upgrade():
    """应用迁移到数据库"""
    from flask_migrate import upgrade as db_upgrade
    db_upgrade()
    click.echo('✅ 数据库迁移应用完成')

@db_cli.command()
@with_appcontext
def downgrade():
    """回滚数据库迁移"""
    from flask_migrate import downgrade as db_downgrade
    db_downgrade()
    click.echo('✅ 数据库迁移回滚完成')

# 注册CLI命令
app.cli.add_command(db_cli, 'db')

if __name__ == '__main__':
    """
    应用启动入口
    开发模式下直接运行此文件启动服务器
    """
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 初始化基础数据
        from utils.init_data import init_database
        init_database()
    
    # 启动开发服务器
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=True,
        threaded=True
    )