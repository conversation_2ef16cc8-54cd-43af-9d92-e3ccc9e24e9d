#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库同步命令行工具
版本: 1.0
创建时间: 2025-07-24

提供命令行接口来执行数据库同步操作
"""

import sys
import os
import argparse
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.db_sync import create_sync_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def cmd_status(args):
    """显示数据库状态"""
    sync_manager = create_sync_manager()
    status = sync_manager.get_database_status()
    
    print("📊 数据库状态报告")
    print("=" * 50)
    print(f"状态: {status.get('status', 'unknown')}")
    print(f"连接: {status.get('connection', 'unknown')}")
    print(f"当前版本: {status.get('current_revision', 'unknown')}")
    print(f"最新版本: {status.get('head_revisions', [])}")
    print(f"迁移状态: {status.get('migration_status', 'unknown')}")
    print(f"检查时间: {status.get('timestamp', 'unknown')}")
    
    if 'table_statistics' in status:
        print("\n📋 表数据统计:")
        for table, count in status['table_statistics'].items():
            print(f"  {table}: {count} 条记录")
    
    if 'error' in status:
        print(f"\n❌ 错误: {status['error']}")

def cmd_sync_schema(args):
    """同步数据库结构"""
    sync_manager = create_sync_manager()
    
    print("🔄 同步数据库结构...")
    if sync_manager.sync_database_schema():
        print("✅ 数据库结构同步成功")
    else:
        print("❌ 数据库结构同步失败")
        return 1
    return 0

def cmd_export_seed(args):
    """导出种子数据"""
    sync_manager = create_sync_manager()
    
    output_file = args.output if args.output else None
    
    print(f"📤 导出种子数据...")
    if sync_manager.export_seed_data(output_file):
        print("✅ 种子数据导出成功")
    else:
        print("❌ 种子数据导出失败")
        return 1
    return 0

def cmd_import_seed(args):
    """导入种子数据"""
    sync_manager = create_sync_manager()
    
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return 1
    
    print(f"📥 导入种子数据: {args.input}")
    if sync_manager.import_seed_data(args.input, args.overwrite):
        print("✅ 种子数据导入成功")
    else:
        print("❌ 种子数据导入失败")
        return 1
    return 0

def cmd_backup(args):
    """备份数据库"""
    sync_manager = create_sync_manager()
    
    output_file = args.output if args.output else None
    
    print("💾 备份数据库...")
    if sync_manager.backup_database(output_file):
        print("✅ 数据库备份成功")
    else:
        print("❌ 数据库备份失败")
        return 1
    return 0

def cmd_restore(args):
    """恢复数据库"""
    sync_manager = create_sync_manager()
    
    if not os.path.exists(args.input):
        print(f"❌ 备份文件不存在: {args.input}")
        return 1
    
    print(f"🔄 恢复数据库: {args.input}")
    print("⚠️  警告: 此操作将覆盖当前数据库!")
    
    if not args.force:
        confirm = input("确认继续? (yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return 1
    
    if sync_manager.restore_database(args.input):
        print("✅ 数据库恢复成功")
    else:
        print("❌ 数据库恢复失败")
        return 1
    return 0

def cmd_full_sync(args):
    """完整同步（结构+种子数据）"""
    sync_manager = create_sync_manager()
    
    print("🚀 执行完整数据库同步...")
    
    # 1. 同步结构
    print("\n步骤 1/2: 同步数据库结构")
    if not sync_manager.sync_database_schema():
        print("❌ 数据库结构同步失败")
        return 1
    
    # 2. 导入种子数据
    if args.seed_file:
        print(f"\n步骤 2/2: 导入种子数据: {args.seed_file}")
        if not sync_manager.import_seed_data(args.seed_file, args.overwrite):
            print("❌ 种子数据导入失败")
            return 1
    else:
        print("\n步骤 2/2: 跳过种子数据导入（未指定文件）")
    
    print("\n✅ 完整同步成功!")
    return 0

def cmd_export_full(args):
    """导出完整业务数据"""
    sync_manager = create_sync_manager()
    
    output_file = args.output if args.output else None
    include_tables = args.tables.split(',') if args.tables else None
    exclude_sensitive = not args.include_sensitive
    
    print("📤 导出完整业务数据...")
    if sync_manager.export_full_data(output_file, include_tables, exclude_sensitive):
        print("✅ 完整数据导出成功")
    else:
        print("❌ 完整数据导出失败")
        return 1
    return 0

def cmd_import_full(args):
    """导入完整业务数据"""
    sync_manager = create_sync_manager()
    
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return 1
    
    print(f"📥 导入完整业务数据: {args.input}")
    print(f"导入模式: {args.mode}")
    
    if not args.force and args.mode == 'replace':
        confirm = input("⚠️  替换模式将删除现有数据，确认继续? (yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return 1
    
    if sync_manager.import_full_data(args.input, args.mode, args.validate_schema):
        print("✅ 完整数据导入成功")
    else:
        print("❌ 完整数据导入失败")
        return 1
    return 0

def cmd_export_incremental(args):
    """导出增量数据"""
    sync_manager = create_sync_manager()
    
    output_file = args.output if args.output else None
    
    print(f"📤 导出增量数据（自 {args.since_date}）...")
    if sync_manager.export_incremental_data(args.since_date, output_file):
        print("✅ 增量数据导出成功")
    else:
        print("❌ 增量数据导出失败")
        return 1
    return 0

def cmd_validate_integrity(args):
    """验证数据完整性"""
    sync_manager = create_sync_manager()
    
    print("🔍 验证数据完整性...")
    result = sync_manager.validate_data_integrity()
    
    print("📊 数据完整性检查结果")
    print("=" * 50)
    print(f"状态: {result.get('status', 'unknown')}")
    print(f"检查时间: {result.get('timestamp', 'unknown')}")
    
    if 'checks' in result:
        print("\n📋 完整性检查:")
        for check_name, check_result in result['checks'].items():
            status_emoji = "✅" if check_result['status'] == 'pass' else "❌"
            print(f"  {status_emoji} {check_result['description']}")
            if check_result['status'] == 'fail':
                print(f"    违规数量: {check_result['violations']}")
            elif check_result['status'] == 'error':
                print(f"    错误: {check_result['error']}")
    
    if 'errors' in result and result['errors']:
        print(f"\n❌ 发现问题:")
        for error in result['errors']:
            print(f"  - {error}")
        return 1
    
    print("\n✅ 数据完整性检查通过")
    return 0

def cmd_init_new_machine(args):
    """初始化新机器"""
    print("🏗️  初始化新开发机器...")
    
    # 执行完整同步
    if args.seed_file:
        args.overwrite = True  # 新机器初始化时强制覆盖
        return cmd_full_sync(args)
    else:
        print("❌ 初始化新机器需要指定种子数据文件 (--seed-file)")
        return 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='航材共享保障平台 - 数据库同步工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 查看数据库状态
  python db_sync_cli.py status
  
  # 同步数据库结构
  python db_sync_cli.py sync-schema
  
  # 导出种子数据
  python db_sync_cli.py export-seed --output seed_data.json
  
  # 导入种子数据
  python db_sync_cli.py import-seed --input seed_data.json --overwrite
  
  # 导出完整业务数据
  python db_sync_cli.py export-full --output full_data.json
  
  # 导出指定表的数据
  python db_sync_cli.py export-full --tables users,materials --output partial_data.json
  
  # 导入完整业务数据（合并模式）
  python db_sync_cli.py import-full --input full_data.json --mode merge
  
  # 导入完整业务数据（替换模式）
  python db_sync_cli.py import-full --input full_data.json --mode replace --force
  
  # 导出增量数据（自指定日期以来的变更）
  python db_sync_cli.py export-incremental --since-date 2025-01-01
  
  # 验证数据完整性
  python db_sync_cli.py validate-integrity
  
  # 完整同步（新机器初始化）
  python db_sync_cli.py init-new-machine --seed-file seed_data.json
  
  # 备份数据库
  python db_sync_cli.py backup --output backup.sql
  
  # 恢复数据库
  python db_sync_cli.py restore --input backup.sql --force
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # status 命令
    parser_status = subparsers.add_parser('status', help='显示数据库状态')
    parser_status.set_defaults(func=cmd_status)
    
    # sync-schema 命令
    parser_sync = subparsers.add_parser('sync-schema', help='同步数据库结构')
    parser_sync.set_defaults(func=cmd_sync_schema)
    
    # export-seed 命令
    parser_export = subparsers.add_parser('export-seed', help='导出种子数据')
    parser_export.add_argument('--output', '-o', help='输出文件路径')
    parser_export.set_defaults(func=cmd_export_seed)
    
    # import-seed 命令
    parser_import = subparsers.add_parser('import-seed', help='导入种子数据')
    parser_import.add_argument('--input', '-i', required=True, help='输入文件路径')
    parser_import.add_argument('--overwrite', action='store_true', help='覆盖现有数据')
    parser_import.set_defaults(func=cmd_import_seed)
    
    # backup 命令
    parser_backup = subparsers.add_parser('backup', help='备份数据库')
    parser_backup.add_argument('--output', '-o', help='输出文件路径')
    parser_backup.set_defaults(func=cmd_backup)
    
    # restore 命令
    parser_restore = subparsers.add_parser('restore', help='恢复数据库')
    parser_restore.add_argument('--input', '-i', required=True, help='备份文件路径')
    parser_restore.add_argument('--force', action='store_true', help='强制执行，不询问确认')
    parser_restore.set_defaults(func=cmd_restore)
    
    # full-sync 命令
    parser_full = subparsers.add_parser('full-sync', help='完整同步（结构+种子数据）')
    parser_full.add_argument('--seed-file', help='种子数据文件路径')
    parser_full.add_argument('--overwrite', action='store_true', help='覆盖现有种子数据')
    parser_full.set_defaults(func=cmd_full_sync)
    
    # export-full 命令
    parser_export_full = subparsers.add_parser('export-full', help='导出完整业务数据')
    parser_export_full.add_argument('--output', '-o', help='输出文件路径')
    parser_export_full.add_argument('--tables', help='指定导出的表（逗号分隔），为空则导出所有业务表')
    parser_export_full.add_argument('--include-sensitive', action='store_true', help='包含敏感数据（如密码）')
    parser_export_full.set_defaults(func=cmd_export_full)
    
    # import-full 命令
    parser_import_full = subparsers.add_parser('import-full', help='导入完整业务数据')
    parser_import_full.add_argument('--input', '-i', required=True, help='输入文件路径')
    parser_import_full.add_argument('--mode', choices=['merge', 'replace', 'skip_existing'], 
                                    default='merge', help='导入模式: merge=合并更新, replace=替换删除, skip_existing=跳过现有')
    parser_import_full.add_argument('--no-validate-schema', dest='validate_schema', action='store_false', 
                                    help='跳过模式版本验证')
    parser_import_full.add_argument('--force', action='store_true', help='强制执行，不询问确认')
    parser_import_full.set_defaults(func=cmd_import_full)
    
    # export-incremental 命令
    parser_export_inc = subparsers.add_parser('export-incremental', help='导出增量数据')
    parser_export_inc.add_argument('--since-date', required=True, help='起始日期 (YYYY-MM-DD格式)')
    parser_export_inc.add_argument('--output', '-o', help='输出文件路径')
    parser_export_inc.set_defaults(func=cmd_export_incremental)
    
    # validate-integrity 命令
    parser_validate = subparsers.add_parser('validate-integrity', help='验证数据完整性')
    parser_validate.set_defaults(func=cmd_validate_integrity)
    
    # init-new-machine 命令
    parser_init = subparsers.add_parser('init-new-machine', help='初始化新开发机器')
    parser_init.add_argument('--seed-file', required=True, help='种子数据文件路径')
    parser_init.set_defaults(func=cmd_init_new_machine)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        logger.exception("详细错误信息:")
        return 1

if __name__ == '__main__':
    sys.exit(main())