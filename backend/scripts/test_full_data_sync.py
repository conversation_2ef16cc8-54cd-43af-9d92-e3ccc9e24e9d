#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 全量数据同步测试脚本
版本: 1.0
创建时间: 2025-07-24

测试新增的全量数据导出/导入功能
"""

import sys
import os
import subprocess
import tempfile
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def run_cli_command(command_args):
    """运行CLI命令并返回结果"""
    cli_script = project_root / 'scripts' / 'db_sync_cli.py'
    cmd = ['python3', str(cli_script)] + command_args
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            cwd=str(project_root)
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, '', str(e)

def test_database_status():
    """测试数据库状态查询"""
    print("🔍 测试数据库状态查询...")
    
    returncode, stdout, stderr = run_cli_command(['status'])
    
    if returncode == 0:
        print("✅ 数据库状态查询成功")
        print(f"输出:\n{stdout}")
        return True
    else:
        print(f"❌ 数据库状态查询失败: {stderr}")
        return False

def test_full_data_export():
    """测试完整数据导出"""
    print("\n📤 测试完整数据导出...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        returncode, stdout, stderr = run_cli_command([
            'export-full', 
            '--output', temp_path,
            '--tables', 'users,materials'
        ])
        
        if returncode == 0:
            print("✅ 完整数据导出成功")
            
            # 验证导出文件
            if os.path.exists(temp_path):
                with open(temp_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"导出文件大小: {os.path.getsize(temp_path)} 字节")
                print(f"包含表: {list(data.get('data', {}).keys())}")
                
                if 'metadata' in data:
                    print(f"导出时间: {data['metadata'].get('export_time')}")
                    print(f"包含的表: {data['metadata'].get('tables_included')}")
                
                return True, temp_path
            else:
                print("❌ 导出文件未生成")
                return False, None
        else:
            print(f"❌ 完整数据导出失败: {stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False, None

def test_data_integrity_validation():
    """测试数据完整性验证"""
    print("\n🔍 测试数据完整性验证...")
    
    returncode, stdout, stderr = run_cli_command(['validate-integrity'])
    
    if returncode == 0:
        print("✅ 数据完整性验证成功")
        print(f"结果:\n{stdout}")
        return True
    else:
        print(f"⚠️  数据完整性验证发现问题或出错: {stderr}")
        print(f"输出:\n{stdout}")
        return False

def test_incremental_export():
    """测试增量数据导出"""
    print("\n📤 测试增量数据导出...")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        returncode, stdout, stderr = run_cli_command([
            'export-incremental',
            '--since-date', '2025-01-01',
            '--output', temp_path
        ])
        
        if returncode == 0:
            print("✅ 增量数据导出成功")
            
            if os.path.exists(temp_path):
                with open(temp_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"导出文件大小: {os.path.getsize(temp_path)} 字节")
                print(f"导出类型: {data.get('metadata', {}).get('export_type')}")
                print(f"起始日期: {data.get('metadata', {}).get('since_date')}")
                
                # 清理临时文件
                os.unlink(temp_path)
                return True
            else:
                print("❌ 增量导出文件未生成")
                return False
        else:
            print(f"❌ 增量数据导出失败: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_help_command():
    """测试帮助命令"""
    print("\n📖 测试CLI帮助...")
    
    returncode, stdout, stderr = run_cli_command(['--help'])
    
    if returncode == 0:
        print("✅ CLI帮助显示成功")
        # 检查是否包含新命令
        if 'export-full' in stdout and 'import-full' in stdout:
            print("✅ 新命令已正确集成到CLI中")
            return True
        else:
            print("❌ 新命令未在帮助中显示")
            return False
    else:
        print(f"❌ CLI帮助显示失败: {stderr}")
        return False

def main():
    """主测试函数"""
    print("🧪 航材共享保障平台 - 全量数据同步功能测试")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1: 数据库状态
    total_tests += 1
    if test_database_status():
        tests_passed += 1
    
    # 测试2: CLI帮助
    total_tests += 1
    if test_help_command():
        tests_passed += 1
    
    # 测试3: 完整数据导出
    total_tests += 1
    export_success, export_file = test_full_data_export()
    if export_success:
        tests_passed += 1
    
    # 测试4: 数据完整性验证
    total_tests += 1
    if test_data_integrity_validation():
        tests_passed += 1
    
    # 测试5: 增量数据导出
    total_tests += 1
    if test_incremental_export():
        tests_passed += 1
    
    # 清理临时文件
    if export_file and os.path.exists(export_file):
        os.unlink(export_file)
    
    # 测试结果总结
    print("\n" + "=" * 60)
    print(f"🎯 测试完成: {tests_passed}/{total_tests} 个测试通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！全量数据同步功能工作正常")
        return 0
    else:
        print(f"⚠️  {total_tests - tests_passed} 个测试失败，请检查相关功能")
        return 1

if __name__ == '__main__':
    sys.exit(main())