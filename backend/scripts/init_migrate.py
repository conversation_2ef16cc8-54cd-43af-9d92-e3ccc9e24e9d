#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - Flask-Migrate初始化脚本
版本: 1.0
创建时间: 2025-07-24

用于初始化Flask-Migrate环境并生成初始迁移文件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from flask_migrate import init, migrate, upgrade
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_flask_migrate():
    """初始化Flask-Migrate环境"""
    with app.app_context():
        try:
            logger.info("🚀 开始初始化Flask-Migrate环境...")
            
            # 检查migrations目录是否存在
            if os.path.exists('migrations'):
                logger.info("⚠️  发现现有migrations目录，自动删除并重新初始化")
                import shutil
                shutil.rmtree('migrations')
                logger.info("🗑️  删除现有migrations目录")
            
            # 初始化migrations目录
            init()
            logger.info("✅ Flask-Migrate环境初始化完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            return False

def create_initial_migration():
    """基于当前数据库状态创建初始迁移"""
    with app.app_context():
        try:
            logger.info("🔄 开始生成初始迁移文件...")
            
            # 生成初始迁移
            migrate(message='Initial migration - existing database schema')
            logger.info("✅ 初始迁移文件生成完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 生成初始迁移失败: {e}")
            return False

def stamp_current():
    """将当前数据库状态标记为最新迁移版本"""
    with app.app_context():
        try:
            logger.info("🏷️  标记当前数据库版本...")
            
            # 导入stamp函数
            from flask_migrate import stamp
            stamp()
            logger.info("✅ 数据库版本标记完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 版本标记失败: {e}")
            return False

def main():
    """主执行函数"""
    logger.info("🏗️  Flask-Migrate初始化工具")
    logger.info("=" * 50)
    
    # 步骤1: 初始化Flask-Migrate环境
    if not init_flask_migrate():
        return
    
    # 步骤2: 生成初始迁移文件
    if not create_initial_migration():
        return
    
    # 步骤3: 标记当前数据库版本
    if not stamp_current():
        return
    
    logger.info("=" * 50)
    logger.info("🎉 Flask-Migrate初始化完成！")
    logger.info("")
    logger.info("📝 后续使用命令:")
    logger.info("  python3 -m flask --app=app db migrate -m '描述'  # 生成新迁移")
    logger.info("  python3 -m flask --app=app db upgrade           # 应用迁移")
    logger.info("  python3 -m flask --app=app db downgrade         # 回滚迁移")

if __name__ == '__main__':
    main()