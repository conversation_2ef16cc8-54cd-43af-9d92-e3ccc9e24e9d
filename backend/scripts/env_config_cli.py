#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 环境配置管理命令行工具
版本: 1.0
创建时间: 2025-07-24

提供命令行接口来执行环境配置管理操作
"""

import sys
import os
import argparse
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.env_config import create_env_manager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def cmd_status(args):
    """显示环境管理状态"""
    env_manager = create_env_manager()
    status = env_manager.get_environment_status()
    
    print("📊 环境管理状态报告")
    print("=" * 50)
    print(f"当前环境: {status.get('current_environment', '未设置')}")
    print(f"可用环境: {status.get('available_environments', [])}")
    print(f"环境总数: {status.get('total_environments', 0)}")
    print(f"配置目录: {status.get('config_directory', 'unknown')}")
    print(f".env文件存在: {'是' if status.get('env_file_exists') else '否'}")
    if status.get('env_file_modified'):
        print(f".env文件修改时间: {status.get('env_file_modified')}")
    print(f"可用模板数: {status.get('templates_available', 0)}")
    print(f"状态: {status.get('status', 'unknown')}")
    print(f"检查时间: {status.get('timestamp', 'unknown')}")
    
    if 'error' in status:
        print(f"\n❌ 错误: {status['error']}")
        return 1
    
    return 0

def cmd_list_environments(args):
    """列出所有可用环境"""
    env_manager = create_env_manager()
    environments = env_manager.get_available_environments()
    current_env = env_manager.get_current_environment()
    
    print("📋 可用环境列表")
    print("=" * 30)
    
    if not environments:
        print("❌ 没有找到任何环境配置")
        print("提示: 使用 'create-templates' 命令创建默认模板")
        return 1
    
    for env in environments:
        marker = " (当前)" if env == current_env else ""
        print(f"  📁 {env}{marker}")
    
    print(f"\n总计: {len(environments)} 个环境")
    return 0

def cmd_create_templates(args):
    """创建环境配置模板"""
    env_manager = create_env_manager()
    
    print("🏗️  创建环境配置模板...")
    if env_manager.create_environment_template():
        print("✅ 环境配置模板创建成功")
        print("📋 已创建以下模板:")
        print("  - development (开发环境)")
        print("  - staging (测试环境)")
        print("  - production (生产环境)")
    else:
        print("❌ 环境配置模板创建失败")
        return 1
    
    return 0

def cmd_create_environment(args):
    """从模板创建新环境"""
    env_manager = create_env_manager()
    
    overrides = {}
    if args.database_url:
        overrides['database_url'] = args.database_url
    if args.secret_key:
        overrides['secret_key'] = args.secret_key
    if args.port:
        overrides['port'] = args.port
    if args.debug is not None:
        overrides['debug'] = args.debug
    
    print(f"🏗️  从模板 {args.template} 创建环境 {args.name}...")
    if env_manager.create_environment_from_template(args.template, args.name, overrides):
        print("✅ 环境创建成功")
        if args.switch:
            print("🔄 正在切换到新环境...")
            if env_manager.switch_environment(args.name):
                print("✅ 已切换到新环境")
            else:
                print("⚠️  环境创建成功但切换失败")
                return 1
    else:
        print("❌ 环境创建失败")
        return 1
    
    return 0

def cmd_switch_environment(args):
    """切换环境"""
    env_manager = create_env_manager()
    
    # 检查环境是否存在
    available_envs = env_manager.get_available_environments()
    if args.name not in available_envs:
        print(f"❌ 环境 '{args.name}' 不存在")
        print(f"可用环境: {available_envs}")
        return 1
    
    print(f"🔄 切换到环境: {args.name}")
    if env_manager.switch_environment(args.name):
        print("✅ 环境切换成功")
        print(f"当前环境: {args.name}")
        print("📝 已更新 .env 文件")
    else:
        print("❌ 环境切换失败")
        return 1
    
    return 0

def cmd_validate_environment(args):
    """验证环境配置"""
    env_manager = create_env_manager()
    
    if args.all:
        environments = env_manager.get_available_environments()
        if not environments:
            print("❌ 没有找到任何环境配置")
            return 1
    else:
        environments = [args.name] if args.name else [env_manager.get_current_environment()]
        if not environments[0]:
            print("❌ 请指定要验证的环境名称或设置当前环境")
            return 1
    
    all_valid = True
    
    for env_name in environments:
        print(f"\n🔍 验证环境: {env_name}")
        print("-" * 30)
        
        result = env_manager.validate_environment(env_name)
        
        if result['valid']:
            print("✅ 验证通过")
        else:
            print("❌ 验证失败")
            all_valid = False
        
        if result.get('errors'):
            print("❌ 错误:")
            for error in result['errors']:
                print(f"  - {error}")
        
        if result.get('warnings'):
            print("⚠️  警告:")
            for warning in result['warnings']:
                print(f"  - {warning}")
        
        print(f"配置文件: {result.get('config_file', 'unknown')}")
        print(f"验证时间: {result.get('validated_at', 'unknown')}")
    
    if args.all:
        print(f"\n📊 总结: {'所有环境验证通过' if all_valid else '部分环境验证失败'}")
    
    return 0 if all_valid else 1

def cmd_export_environment(args):
    """导出环境配置"""
    env_manager = create_env_manager()
    
    env_name = args.name or env_manager.get_current_environment()
    if not env_name:
        print("❌ 请指定要导出的环境名称或设置当前环境")
        return 1
    
    output_file = args.output
    include_secrets = args.include_secrets
    
    print(f"📤 导出环境配置: {env_name}")
    if include_secrets:
        print("⚠️  警告: 导出将包含敏感信息")
    
    if env_manager.export_environment_config(env_name, output_file, include_secrets):
        print("✅ 环境配置导出成功")
    else:
        print("❌ 环境配置导出失败")
        return 1
    
    return 0

def cmd_current_environment(args):
    """显示当前环境"""
    env_manager = create_env_manager()
    current_env = env_manager.get_current_environment()
    
    if current_env:
        print(f"当前环境: {current_env}")
        
        # 显示验证结果
        if args.validate:
            print("\n🔍 验证当前环境...")
            result = env_manager.validate_environment(current_env)
            if result['valid']:
                print("✅ 当前环境配置有效")
            else:
                print("❌ 当前环境配置存在问题")
                for error in result.get('errors', []):
                    print(f"  - {error}")
                return 1
    else:
        print("❌ 未设置当前环境")
        print("使用 'switch <环境名>' 命令切换到指定环境")
        return 1
    
    return 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='航材共享保障平台 - 环境配置管理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 查看环境管理状态
  python env_config_cli.py status
  
  # 列出所有可用环境
  python env_config_cli.py list
  
  # 创建环境配置模板
  python env_config_cli.py create-templates
  
  # 从模板创建新环境
  python env_config_cli.py create my-dev --template development --database-url postgresql://user:pass@localhost/mydb
  
  # 切换到指定环境
  python env_config_cli.py switch development
  
  # 验证环境配置
  python env_config_cli.py validate development
  
  # 验证所有环境
  python env_config_cli.py validate --all
  
  # 显示当前环境
  python env_config_cli.py current
  
  # 导出环境配置
  python env_config_cli.py export --name development --output dev_config.yaml
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # status 命令
    parser_status = subparsers.add_parser('status', help='显示环境管理状态')
    parser_status.set_defaults(func=cmd_status)
    
    # list 命令
    parser_list = subparsers.add_parser('list', help='列出所有可用环境')
    parser_list.set_defaults(func=cmd_list_environments)
    
    # create-templates 命令
    parser_templates = subparsers.add_parser('create-templates', help='创建环境配置模板')
    parser_templates.set_defaults(func=cmd_create_templates)
    
    # create 命令
    parser_create = subparsers.add_parser('create', help='从模板创建新环境')
    parser_create.add_argument('name', help='新环境名称')
    parser_create.add_argument('--template', required=True, choices=['development', 'staging', 'production'], 
                              help='使用的模板')
    parser_create.add_argument('--database-url', help='覆盖数据库URL')
    parser_create.add_argument('--secret-key', help='覆盖密钥')
    parser_create.add_argument('--port', type=int, help='覆盖端口号')
    parser_create.add_argument('--debug', type=bool, help='覆盖调试模式')
    parser_create.add_argument('--switch', action='store_true', help='创建后立即切换到新环境')
    parser_create.set_defaults(func=cmd_create_environment)
    
    # switch 命令
    parser_switch = subparsers.add_parser('switch', help='切换到指定环境')
    parser_switch.add_argument('name', help='环境名称')
    parser_switch.set_defaults(func=cmd_switch_environment)
    
    # validate 命令
    parser_validate = subparsers.add_parser('validate', help='验证环境配置')
    parser_validate.add_argument('name', nargs='?', help='环境名称（可选，默认为当前环境）')
    parser_validate.add_argument('--all', action='store_true', help='验证所有环境')
    parser_validate.set_defaults(func=cmd_validate_environment)
    
    # export 命令
    parser_export = subparsers.add_parser('export', help='导出环境配置')
    parser_export.add_argument('--name', help='环境名称（可选，默认为当前环境）')
    parser_export.add_argument('--output', '-o', help='输出文件路径')
    parser_export.add_argument('--include-secrets', action='store_true', help='包含敏感信息')
    parser_export.set_defaults(func=cmd_export_environment)
    
    # current 命令
    parser_current = subparsers.add_parser('current', help='显示当前环境')
    parser_current.add_argument('--validate', action='store_true', help='验证当前环境配置')
    parser_current.set_defaults(func=cmd_current_environment)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        logger.exception("详细错误信息:")
        return 1

if __name__ == '__main__':
    sys.exit(main())