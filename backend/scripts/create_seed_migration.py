#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 创建种子数据迁移脚本
版本: 1.0
创建时间: 2025-07-24

基于手动迁移脚本，创建Flask-Migrate格式的种子数据迁移
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from flask_migrate import migrate
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_seed_data_migration():
    """创建种子数据迁移文件"""
    with app.app_context():
        try:
            logger.info("🌱 创建种子数据迁移文件...")
            
            # 生成新的迁移文件
            migrate(message='Add seed data - roles, permissions, initial user data')
            logger.info("✅ 种子数据迁移文件创建完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建种子数据迁移失败: {e}")
            return False

def main():
    """主执行函数"""
    logger.info("🏗️  创建种子数据迁移工具")
    logger.info("=" * 50)
    
    if create_seed_data_migration():
        logger.info("=" * 50)
        logger.info("🎉 种子数据迁移文件创建完成！")
        logger.info("")
        logger.info("📝 下一步:")
        logger.info("1. 编辑新生成的迁移文件，添加种子数据初始化逻辑")
        logger.info("2. 运行 python3 -m flask --app=app db upgrade 应用迁移")
    else:
        logger.error("❌ 创建失败，请检查错误信息")

if __name__ == '__main__':
    main()