#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - Flask-Migrate完全重置脚本
版本: 1.0
创建时间: 2025-07-24

完全重置Flask-Migrate环境，清理版本历史
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from flask_migrate import init, migrate, stamp
from sqlalchemy import text
from models import db
import logging
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_alembic_version():
    """清理alembic版本表"""
    with app.app_context():
        try:
            logger.info("🧹 清理alembic版本信息...")
            
            # 删除alembic_version表
            db.session.execute(text("DROP TABLE IF EXISTS alembic_version"))
            db.session.commit()
            logger.info("✅ alembic版本表清理完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理alembic版本表失败: {e}")
            db.session.rollback()
            return False

def reset_migrations():
    """完全重置迁移环境"""
    with app.app_context():
        try:
            logger.info("🔄 完全重置Flask-Migrate环境...")
            
            # 1. 删除migrations目录
            if os.path.exists('migrations'):
                shutil.rmtree('migrations')
                logger.info("🗑️  删除migrations目录")
            
            # 2. 清理alembic版本表
            clean_alembic_version()
            
            # 3. 重新初始化
            init()
            logger.info("✅ Flask-Migrate重新初始化完成")
            
            # 4. 生成初始迁移
            migrate(message='Initial migration - complete database schema')
            logger.info("✅ 初始迁移文件生成完成")
            
            # 5. 标记为当前版本
            stamp()
            logger.info("✅ 数据库版本标记完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 重置失败: {e}")
            return False

def main():
    """主执行函数"""
    logger.info("🏗️  Flask-Migrate完全重置工具")
    logger.info("=" * 50)
    
    if reset_migrations():
        logger.info("=" * 50)
        logger.info("🎉 Flask-Migrate重置完成！")
        logger.info("")
        logger.info("📝 后续使用命令:")
        logger.info("  python3 -m flask --app=app db migrate -m '描述'  # 生成新迁移")
        logger.info("  python3 -m flask --app=app db upgrade           # 应用迁移")
        logger.info("  python3 -m flask --app=app db downgrade         # 回滚迁移")
    else:
        logger.error("❌ 重置失败，请检查错误信息")

if __name__ == '__main__':
    main()