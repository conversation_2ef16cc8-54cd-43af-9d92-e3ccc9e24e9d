#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 装饰器函数
版本: 1.0
创建时间: 2025-01-13

包含各种装饰器函数，用于请求验证、权限检查等
"""

import functools
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt

from models import User

def validate_json(required_fields=None, optional_fields=None):
    """
    验证JSON请求体装饰器
    
    Args:
        required_fields: 必需字段列表
        optional_fields: 可选字段列表
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查Content-Type
            if not request.is_json:
                return jsonify({
                    'error': 400,
                    'message': '请求必须是JSON格式',
                    'body': {},
                    'success': False
                }), 400
            
            # 获取JSON数据
            try:
                data = request.get_json()
                if data is None:
                    return jsonify({
                        'error': 400,
                        'message': '无效的JSON数据',
                        'body': {},
                        'success': False
                    }), 400
            except Exception:
                return jsonify({
                    'error': 400,
                    'message': 'JSON解析失败',
                    'body': {},
                    'success': False
                }), 400
            
            # 验证必需字段
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data or data[field] is None or str(data[field]).strip() == '':
                        missing_fields.append(field)
                
                if missing_fields:
                    return jsonify({
                        'error': 400,
                        'message': f'缺少必需字段: {", ".join(missing_fields)}',
                        'body': {'missing_fields': missing_fields},
                        'success': False
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def handle_exceptions(f):
    """
    统一异常处理装饰器
    自动捕获和处理函数中的异常，返回标准的错误响应格式
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"API异常: {request.method} {request.path} - 错误: {str(e)}")
            
            return jsonify({
                'error': 500,
                'message': f'服务器内部错误: {str(e)}',
                'body': {},
                'success': False
            }), 500
    
    return decorated_function

def require_roles(allowed_roles):
    """
    角色权限验证装饰器
    
    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            if user.user_type not in allowed_roles:
                return jsonify({
                    'error': 403,
                    'message': '权限不足',
                    'body': {'required_roles': allowed_roles, 'user_role': user.user_type},
                    'success': False
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin(f):
    """
    要求管理员权限的装饰器
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or user.user_type != 'admin':
            return jsonify({
                'error': 403,
                'message': '需要管理员权限',
                'body': {},
                'success': False
            }), 403
        
        return f(*args, **kwargs)
    return decorated_function

def require_active_user(f):
    """
    要求活跃用户状态的装饰器
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'error': 404,
                'message': '用户不存在',
                'body': {},
                'success': False
            }), 404
        
        if user.status != 'active':
            return jsonify({
                'error': 403,
                'message': '账户已被禁用',
                'body': {'user_status': user.status},
                'success': False
            }), 403
        
        return f(*args, **kwargs)
    return decorated_function

def log_api_call(f):
    """
    API调用日志装饰器
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        from datetime import datetime
        import logging
        
        # 记录请求信息
        logger = logging.getLogger(__name__)
        logger.info(f"API调用: {request.method} {request.path} - 客户端IP: {request.remote_addr}")
        
        start_time = datetime.now()
        
        try:
            # 执行原函数
            result = f(*args, **kwargs)
            
            # 记录成功信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"API成功: {request.method} {request.path} - 耗时: {duration:.3f}s")
            
            return result
            
        except Exception as e:
            # 记录错误信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"API错误: {request.method} {request.path} - 耗时: {duration:.3f}s - 错误: {str(e)}")
            raise
    
    return decorated_function

def rate_limit(max_requests=100, window_seconds=3600):
    """
    简单的速率限制装饰器
    注意: 这是一个简单实现，生产环境建议使用Redis等外部存储
    
    Args:
        max_requests: 最大请求数
        window_seconds: 时间窗口（秒）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现基于IP或用户的速率限制
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def cache_response(timeout=300):
    """
    响应缓存装饰器
    注意: 这是一个简单实现，生产环境建议使用Redis等缓存系统
    
    Args:
        timeout: 缓存超时时间（秒）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现响应缓存逻辑
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_ownership(resource_param='id', user_field='user_id'):
    """
    资源所有权验证装饰器
    验证当前用户是否拥有指定资源
    
    Args:
        resource_param: 资源ID参数名
        user_field: 资源中的用户字段名
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            current_user = User.query.get(current_user_id)
            
            if not current_user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 管理员可以访问所有资源
            if current_user.user_type == 'admin':
                return f(*args, **kwargs)
            
            # 这里需要根据具体的资源类型实现所有权检查
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_file_upload(allowed_extensions=None, max_size=None):
    """
    文件上传验证装饰器
    
    Args:
        allowed_extensions: 允许的文件扩展名集合
        max_size: 最大文件大小（字节）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if 'file' not in request.files:
                return jsonify({
                    'error': 400,
                    'message': '没有文件被上传',
                    'body': {},
                    'success': False
                }), 400
            
            file = request.files['file']
            
            if file.filename == '':
                return jsonify({
                    'error': 400,
                    'message': '没有选择文件',
                    'body': {},
                    'success': False
                }), 400
            
            # 检查文件扩展名
            if allowed_extensions:
                ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
                if ext not in allowed_extensions:
                    return jsonify({
                        'error': 400,
                        'message': f'不支持的文件类型，允许的类型: {", ".join(allowed_extensions)}',
                        'body': {'allowed_extensions': list(allowed_extensions)},
                        'success': False
                    }), 400
            
            # 检查文件大小
            if max_size:
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(0)  # 重置到文件开头
                
                if file_size > max_size:
                    return jsonify({
                        'error': 400,
                        'message': f'文件太大，最大允许 {max_size} 字节',
                        'body': {'max_size': max_size, 'file_size': file_size},
                        'success': False
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


# =====================================================
# 第一阶段新增权限装饰器
# =====================================================

def require_permission(permission_code):
    """
    权限验证装饰器 - 基于权限代码的访问控制
    检查用户是否具有特定权限
    
    Args:
        permission_code: 权限代码字符串
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 检查用户状态
            if user.status != 'active':
                return jsonify({
                    'error': 403,
                    'message': '账户已被禁用',
                    'body': {'user_status': user.status},
                    'success': False
                }), 403
            
            # 检查权限
            if not user.has_permission(permission_code):
                # 记录权限不足的访问尝试
                _log_access_attempt(user.id, 'permission_denied', permission_code, False, 
                                  f'权限不足: {permission_code}')
                
                return jsonify({
                    'error': 403,
                    'message': f'权限不足，需要权限: {permission_code}',
                    'body': {
                        'required_permission': permission_code,
                        'user_permissions': user.get_role_permissions()
                    },
                    'success': False
                }), 403
            
            # 记录成功的权限访问
            _log_access_attempt(user.id, 'permission_granted', permission_code, True)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_ownership_enhanced(model, ownership_field='supplier_id', allow_admin=True, allow_cross_company=False):
    """
    增强版资源所有权验证装饰器
    验证用户是否有权访问特定资源
    
    Args:
        model: 资源模型类
        ownership_field: 所有权字段名
        allow_admin: 是否允许管理员访问
        allow_cross_company: 是否允许跨公司访问（仅限内部员工）
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            from flask import request
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 获取资源ID（从URL参数或请求体中）
            resource_id = kwargs.get('id') or kwargs.get('resource_id') or request.view_args.get('id')
            if not resource_id:
                return jsonify({
                    'error': 400,
                    'message': '缺少资源ID',
                    'body': {},
                    'success': False
                }), 400
            
            # 查找资源
            resource = model.query.get(resource_id)
            if not resource:
                return jsonify({
                    'error': 404,
                    'message': '资源不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 管理员权限检查
            if allow_admin and user.is_admin():
                _log_access_attempt(user.id, 'admin_access', f'{model.__name__}:{resource_id}', True)
                return f(*args, **kwargs)
            
            # 跨公司访问权限检查
            if allow_cross_company and user.can_cross_company_access():
                _log_access_attempt(user.id, 'cross_company_access', f'{model.__name__}:{resource_id}', True)
                return f(*args, **kwargs)
            
            # 所有权检查
            resource_owner_id = getattr(resource, ownership_field, None)
            if resource_owner_id != current_user_id:
                # 检查是否为同公司用户（如果资源有公司信息）
                if hasattr(resource, 'company_name') and resource.company_name == user.company_name:
                    _log_access_attempt(user.id, 'company_access', f'{model.__name__}:{resource_id}', True)
                    return f(*args, **kwargs)
                
                _log_access_attempt(user.id, 'ownership_denied', f'{model.__name__}:{resource_id}', False,
                                  f'资源所有者ID: {resource_owner_id}, 当前用户ID: {current_user_id}')
                
                return jsonify({
                    'error': 403,
                    'message': '权限不足，无法访问该资源',
                    'body': {'resource_type': model.__name__, 'resource_id': resource_id},
                    'success': False
                }), 403
            
            _log_access_attempt(user.id, 'ownership_granted', f'{model.__name__}:{resource_id}', True)
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def data_isolation(model, filter_field='supplier_id'):
    """
    数据隔离装饰器
    自动根据用户权限过滤查询结果
    
    Args:
        model: 数据模型类
        filter_field: 过滤字段名
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 将用户信息传递给视图函数，用于数据过滤
            kwargs['current_user'] = user
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def cross_company_access(f):
    """
    跨公司访问权限装饰器
    仅允许平台内部员工或管理员访问
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'error': 404,
                'message': '用户不存在',
                'body': {},
                'success': False
            }), 404
        
        if not user.can_cross_company_access():
            _log_access_attempt(user.id, 'cross_company_denied', 'cross_company_access', False,
                              f'用户类型: {user.user_type}, 公司类型: {user.company_type}')
            
            return jsonify({
                'error': 403,
                'message': '权限不足，需要跨公司访问权限',
                'body': {
                    'user_type': user.user_type,
                    'company_type': user.company_type
                },
                'success': False
            }), 403
        
        _log_access_attempt(user.id, 'cross_company_granted', 'cross_company_access', True)
        return f(*args, **kwargs)
    return decorated_function


def require_internal_staff(f):
    """
    要求平台内部员工权限的装饰器
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'error': 404,
                'message': '用户不存在',
                'body': {},
                'success': False
            }), 404
        
        if not user.is_internal_staff():
            _log_access_attempt(user.id, 'internal_staff_denied', 'internal_staff_access', False,
                              f'公司类型: {user.company_type}')
            
            return jsonify({
                'error': 403,
                'message': '权限不足，需要内部员工权限',
                'body': {'company_type': user.company_type},
                'success': False
            }), 403
        
        _log_access_attempt(user.id, 'internal_staff_granted', 'internal_staff_access', True)
        return f(*args, **kwargs)
    return decorated_function


def audit_action(action_type, resource_type=None):
    """
    操作审计装饰器
    自动记录用户的重要操作
    
    Args:
        action_type: 操作类型
        resource_type: 资源类型
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            from flask import request
            import json
            
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 执行原函数
            try:
                result = f(*args, **kwargs)
                
                # 记录成功的操作
                resource_id = kwargs.get('id') or request.view_args.get('id') if request.view_args else None
                additional_data = {
                    'method': request.method,
                    'path': request.path,
                    'params': dict(request.args),
                    'user_agent': request.headers.get('User-Agent', ''),
                }
                
                if request.is_json and request.method in ['POST', 'PUT', 'PATCH']:
                    try:
                        additional_data['request_data'] = request.get_json()
                    except:
                        pass
                
                _log_access_attempt(
                    user_id=user.id,
                    action=action_type,
                    resource_info=f'{resource_type}:{resource_id}' if resource_type and resource_id else action_type,
                    success=True,
                    additional_data=json.dumps(additional_data, ensure_ascii=False)
                )
                
                return result
                
            except Exception as e:
                # 记录失败的操作
                _log_access_attempt(
                    user_id=user.id,
                    action=action_type,
                    resource_info=f'{resource_type}' if resource_type else action_type,
                    success=False,
                    error_message=str(e)
                )
                raise
                
        return decorated_function
    return decorator


def _log_access_attempt(user_id, action, resource_info, success, error_message=None, additional_data=None):
    """
    记录访问尝试的辅助函数
    
    Args:
        user_id: 用户ID
        action: 操作类型
        resource_info: 资源信息
        success: 是否成功
        error_message: 错误信息
        additional_data: 附加数据
    """
    try:
        from models import DataAccessLog, db
        from flask import request
        
        log_entry = DataAccessLog(
            user_id=user_id,
            resource_type=action,
            resource_id=resource_info,
            action=action,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent', '') if request else '',
            success=success,
            error_message=error_message,
            additional_data=additional_data
        )
        
        db.session.add(log_entry)
        db.session.commit()
        
    except Exception as e:
        # 记录日志失败不应该影响主要业务逻辑
        import logging
        logging.getLogger(__name__).error(f"记录访问日志失败: {str(e)}")
        pass