#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 环境配置管理工具
版本: 1.0
创建时间: 2025-07-24

提供多环境配置管理功能：
1. 环境配置模板管理
2. 配置文件生成和切换
3. 配置验证和检查
4. 敏感信息管理
"""

import os
import sys
import json
import yaml
import shutil
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import configparser

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EnvironmentConfig:
    """环境配置数据类"""
    name: str
    description: str
    database_url: str
    debug: bool
    secret_key: str
    flask_env: str
    database_type: str = 'postgresql'
    host: str = 'localhost'
    port: int = 5000
    redis_url: Optional[str] = None
    log_level: str = 'INFO'
    backup_retention_days: int = 30
    custom_settings: Dict = None

    def __post_init__(self):
        if self.custom_settings is None:
            self.custom_settings = {}

class EnvironmentManager:
    """环境配置管理器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_dir = self.project_root / 'config'
        self.templates_dir = self.config_dir / 'templates'
        self.environments_dir = self.config_dir / 'environments'
        self.current_env_file = self.config_dir / '.current_env'
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        self.environments_dir.mkdir(exist_ok=True)
    
    def get_available_environments(self) -> List[str]:
        """
        获取可用的环境列表
        
        Returns:
            环境名称列表
        """
        try:
            env_files = list(self.environments_dir.glob('*.yaml'))
            return [f.stem for f in env_files]
        except Exception as e:
            logger.error(f"获取环境列表失败: {e}")
            return []
    
    def get_current_environment(self) -> Optional[str]:
        """
        获取当前激活的环境
        
        Returns:
            当前环境名称，无则返回None
        """
        try:
            if self.current_env_file.exists():
                return self.current_env_file.read_text().strip()
            return None
        except Exception as e:
            logger.error(f"获取当前环境失败: {e}")
            return None
    
    def create_environment_template(self) -> bool:
        """
        创建环境配置模板文件
        
        Returns:
            是否成功
        """
        try:
            logger.info("🏗️  创建环境配置模板...")
            
            # 开发环境模板
            dev_config = EnvironmentConfig(
                name='development',
                description='开发环境配置',
                database_url='*************************************************',
                debug=True,
                secret_key='dev-secret-key-change-in-production',
                flask_env='development',
                database_type='postgresql',
                host='localhost',
                port=5000,
                redis_url='redis://localhost:6379/0',
                log_level='DEBUG',
                backup_retention_days=7,
                custom_settings={
                    'TESTING': False,
                    'WTF_CSRF_ENABLED': False,
                    'MAIL_DEBUG': True
                }
            )
            
            # 测试环境模板
            staging_config = EnvironmentConfig(
                name='staging',
                description='测试环境配置',
                database_url='******************************************************',
                debug=False,
                secret_key='staging-secret-key-replace-with-secure-key',
                flask_env='production',
                database_type='postgresql',
                host='0.0.0.0',
                port=5000,
                redis_url='redis://staging-redis:6379/0',
                log_level='INFO',
                backup_retention_days=15,
                custom_settings={
                    'TESTING': False,
                    'WTF_CSRF_ENABLED': True,
                    'MAIL_DEBUG': False
                }
            )
            
            # 生产环境模板
            prod_config = EnvironmentConfig(
                name='production',
                description='生产环境配置',
                database_url='postgresql://prod_user:${PROD_DB_PASSWORD}@prod-db/cassdemo_prod',
                debug=False,
                secret_key='${PROD_SECRET_KEY}',
                flask_env='production',
                database_type='postgresql',
                host='0.0.0.0',
                port=80,
                redis_url='redis://prod-redis:6379/0',
                log_level='WARNING',
                backup_retention_days=90,
                custom_settings={
                    'TESTING': False,
                    'WTF_CSRF_ENABLED': True,
                    'MAIL_DEBUG': False,
                    'SSL_REQUIRED': True
                }
            )
            
            # 保存模板文件
            templates = {
                'development': dev_config,
                'staging': staging_config,
                'production': prod_config
            }
            
            for env_name, config in templates.items():
                template_file = self.templates_dir / f'{env_name}.yaml'
                self._save_config_to_yaml(template_file, config)
                logger.info(f"  ✅ 创建模板: {template_file}")
            
            logger.info("✅ 环境配置模板创建完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建环境配置模板失败: {e}")
            return False
    
    def create_environment_from_template(self, template_name: str, 
                                       new_env_name: str,
                                       overrides: Optional[Dict] = None) -> bool:
        """
        从模板创建新的环境配置
        
        Args:
            template_name: 模板名称
            new_env_name: 新环境名称
            overrides: 覆盖的配置项
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"🏗️  从模板 {template_name} 创建环境 {new_env_name}...")
            
            template_file = self.templates_dir / f'{template_name}.yaml'
            if not template_file.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_file}")
            
            # 加载模板配置
            config = self._load_config_from_yaml(template_file)
            
            # 应用覆盖配置
            if overrides:
                for key, value in overrides.items():
                    setattr(config, key, value)
            
            # 更新名称
            config.name = new_env_name
            
            # 保存新环境配置
            env_file = self.environments_dir / f'{new_env_name}.yaml'
            self._save_config_to_yaml(env_file, config)
            
            logger.info(f"✅ 环境 {new_env_name} 创建完成: {env_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建环境失败: {e}")
            return False
    
    def switch_environment(self, env_name: str) -> bool:
        """
        切换到指定环境
        
        Args:
            env_name: 环境名称
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"🔄 切换到环境: {env_name}")
            
            env_file = self.environments_dir / f'{env_name}.yaml'
            if not env_file.exists():
                raise FileNotFoundError(f"环境配置文件不存在: {env_file}")
            
            # 加载环境配置
            config = self._load_config_from_yaml(env_file)
            
            # 生成.env文件
            self._generate_env_file(config)
            
            # 更新当前环境标记
            self.current_env_file.write_text(env_name)
            
            logger.info(f"✅ 已切换到环境: {env_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 切换环境失败: {e}")
            return False
    
    def validate_environment(self, env_name: str) -> Dict[str, any]:
        """
        验证环境配置
        
        Args:
            env_name: 环境名称
            
        Returns:
            验证结果字典
        """
        try:
            logger.info(f"🔍 验证环境配置: {env_name}")
            
            env_file = self.environments_dir / f'{env_name}.yaml'
            if not env_file.exists():
                return {
                    'valid': False,
                    'errors': [f'环境配置文件不存在: {env_file}'],
                    'warnings': []
                }
            
            config = self._load_config_from_yaml(env_file)
            errors = []
            warnings = []
            
            # 验证必需配置项
            required_fields = ['database_url', 'secret_key', 'flask_env']
            for field in required_fields:
                value = getattr(config, field, None)
                if not value:
                    errors.append(f'缺少必需配置项: {field}')
                elif isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                    warnings.append(f'配置项使用环境变量: {field} = {value}')
            
            # 验证数据库URL格式
            if config.database_url and not config.database_url.startswith(('postgresql://', 'sqlite:///')):
                errors.append('database_url格式不正确，应为postgresql://或sqlite:///')
            
            # 验证端口号
            if not (1 <= config.port <= 65535):
                errors.append(f'端口号无效: {config.port}')
            
            # 验证日志级别
            valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if config.log_level not in valid_log_levels:
                errors.append(f'日志级别无效: {config.log_level}')
            
            # 生产环境特殊检查
            if env_name == 'production':
                if config.debug:
                    errors.append('生产环境不应启用调试模式')
                if 'dev' in config.secret_key.lower() or len(config.secret_key) < 32:
                    errors.append('生产环境需要更安全的secret_key')
            
            result = {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'config_file': str(env_file),
                'validated_at': datetime.now().isoformat()
            }
            
            if result['valid']:
                logger.info(f"✅ 环境配置验证通过: {env_name}")
            else:
                logger.warning(f"⚠️  环境配置验证失败: {env_name}")
                for error in errors:
                    logger.warning(f"  - {error}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 验证环境配置失败: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': []
            }
    
    def export_environment_config(self, env_name: str, 
                                 output_file: Optional[str] = None,
                                 include_secrets: bool = False) -> bool:
        """
        导出环境配置
        
        Args:
            env_name: 环境名称
            output_file: 输出文件路径
            include_secrets: 是否包含敏感信息
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"📤 导出环境配置: {env_name}")
            
            env_file = self.environments_dir / f'{env_name}.yaml'
            if not env_file.exists():
                raise FileNotFoundError(f"环境配置文件不存在: {env_file}")
            
            config = self._load_config_from_yaml(env_file)
            
            # 处理敏感信息
            if not include_secrets:
                # 脱敏处理
                if config.secret_key and not config.secret_key.startswith('${'):
                    config.secret_key = '***REDACTED***'
                
                if config.database_url and '://' in config.database_url:
                    # 隐藏数据库密码
                    parts = config.database_url.split('://')
                    if len(parts) == 2 and '@' in parts[1]:
                        scheme = parts[0]
                        rest = parts[1]
                        if ':' in rest.split('@')[0]:
                            user_part = rest.split('@')[0]
                            user = user_part.split(':')[0]
                            host_part = rest.split('@')[1]
                            config.database_url = f"{scheme}://{user}:***@{host_part}"
            
            if not output_file:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = self.config_dir / f'export_{env_name}_{timestamp}.yaml'
            
            self._save_config_to_yaml(output_file, config)
            
            logger.info(f"✅ 环境配置导出完成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出环境配置失败: {e}")
            return False
    
    def _load_config_from_yaml(self, file_path: Path) -> EnvironmentConfig:
        """从YAML文件加载配置"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return EnvironmentConfig(**data)
    
    def _save_config_to_yaml(self, file_path: Path, config: EnvironmentConfig):
        """保存配置到YAML文件"""
        # 转换为字典
        config_dict = {
            'name': config.name,
            'description': config.description,
            'database_url': config.database_url,
            'debug': config.debug,
            'secret_key': config.secret_key,
            'flask_env': config.flask_env,
            'database_type': config.database_type,
            'host': config.host,
            'port': config.port,
            'redis_url': config.redis_url,
            'log_level': config.log_level,
            'backup_retention_days': config.backup_retention_days,
            'custom_settings': config.custom_settings
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def _generate_env_file(self, config: EnvironmentConfig):
        """生成.env文件"""
        env_file = self.project_root / '.env'
        
        env_content = f"""# 航材共享保障平台环境配置
# 环境: {config.name}
# 生成时间: {datetime.now().isoformat()}

# Flask配置
FLASK_ENV={config.flask_env}
FLASK_DEBUG={'1' if config.debug else '0'}
SECRET_KEY={config.secret_key}

# 数据库配置
SQLALCHEMY_DATABASE_URI={config.database_url}
DATABASE_TYPE={config.database_type}

# 服务器配置
HOST={config.host}
PORT={config.port}

# Redis配置
REDIS_URL={config.redis_url or ''}

# 日志配置
LOG_LEVEL={config.log_level}

# 备份配置
BACKUP_RETENTION_DAYS={config.backup_retention_days}

# 自定义配置
"""
        
        if config.custom_settings:
            for key, value in config.custom_settings.items():
                env_content += f"{key}={value}\n"
        
        env_file.write_text(env_content)
        logger.info(f"✅ 生成.env文件: {env_file}")
    
    def get_environment_status(self) -> Dict[str, any]:
        """
        获取环境管理状态
        
        Returns:
            状态信息字典
        """
        try:
            available_envs = self.get_available_environments()
            current_env = self.get_current_environment()
            
            # 检查.env文件状态
            env_file = self.project_root / '.env'
            env_file_exists = env_file.exists()
            env_file_mtime = None
            if env_file_exists:
                env_file_mtime = datetime.fromtimestamp(env_file.stat().st_mtime).isoformat()
            
            return {
                'available_environments': available_envs,
                'current_environment': current_env,
                'total_environments': len(available_envs),
                'config_directory': str(self.config_dir),
                'env_file_exists': env_file_exists,
                'env_file_modified': env_file_mtime,
                'templates_available': len(list(self.templates_dir.glob('*.yaml'))),
                'status': 'healthy' if available_envs else 'no_environments',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取环境状态失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

def create_env_manager():
    """创建环境管理器实例"""
    return EnvironmentManager()