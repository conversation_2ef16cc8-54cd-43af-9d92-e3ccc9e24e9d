#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 权限查询缓存优化
版本: 1.0
创建时间: 2025-07-24

提供权限查询的缓存机制，提升系统性能
"""

import time
from typing import List, Dict, Optional
from threading import Lock
import logging

logger = logging.getLogger(__name__)

class PermissionCache:
    """权限缓存管理类"""
    
    def __init__(self, cache_ttl: int = 300):  # 默认5分钟过期
        """
        初始化权限缓存
        
        Args:
            cache_ttl: 缓存生存时间（秒）
        """
        self._cache = {}
        self._cache_ttl = cache_ttl
        self._lock = Lock()
        
    def _is_expired(self, timestamp: float) -> bool:
        """检查缓存是否过期"""
        return time.time() - timestamp > self._cache_ttl
    
    def get_user_permissions(self, user_id: int) -> Optional[List[str]]:
        """
        从缓存获取用户权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            权限列表或None（如果缓存不存在或已过期）
        """
        with self._lock:
            cache_key = f"user_permissions_{user_id}"
            
            if cache_key in self._cache:
                permissions, timestamp = self._cache[cache_key]
                
                if not self._is_expired(timestamp):
                    logger.debug(f"命中权限缓存: user_id={user_id}, permissions={len(permissions)}")
                    return permissions
                else:
                    # 清除过期缓存
                    del self._cache[cache_key]
                    logger.debug(f"清除过期权限缓存: user_id={user_id}")
            
            logger.debug(f"权限缓存未命中: user_id={user_id}")
            return None
    
    def set_user_permissions(self, user_id: int, permissions: List[str]) -> None:
        """
        设置用户权限缓存
        
        Args:
            user_id: 用户ID
            permissions: 权限列表
        """
        with self._lock:
            cache_key = f"user_permissions_{user_id}"
            self._cache[cache_key] = (permissions, time.time())
            logger.debug(f"设置权限缓存: user_id={user_id}, permissions={len(permissions)}")
    
    def invalidate_user_permissions(self, user_id: int) -> None:
        """
        使用户权限缓存失效
        
        Args:
            user_id: 用户ID
        """
        with self._lock:
            cache_key = f"user_permissions_{user_id}"
            if cache_key in self._cache:
                del self._cache[cache_key]
                logger.debug(f"使权限缓存失效: user_id={user_id}")
    
    def invalidate_role_permissions(self, role_id: int) -> None:
        """
        使角色相关的所有用户权限缓存失效
        
        Args:
            role_id: 角色ID
        """
        # 由于需要查询数据库获取该角色的所有用户，这里简单地清空所有缓存
        # 在生产环境中可以考虑更精确的缓存失效策略
        with self._lock:
            # 记录清空前的缓存数量
            cache_count = len(self._cache)
            self._cache.clear()
            logger.info(f"角色权限变更，清空所有权限缓存: role_id={role_id}, cleared={cache_count}")
    
    def get_role_info(self, role_code: str) -> Optional[Dict]:
        """
        从缓存获取角色信息
        
        Args:
            role_code: 角色编码
            
        Returns:
            角色信息或None
        """
        with self._lock:
            cache_key = f"role_info_{role_code}"
            
            if cache_key in self._cache:
                role_info, timestamp = self._cache[cache_key]
                
                if not self._is_expired(timestamp):
                    logger.debug(f"命中角色缓存: role_code={role_code}")
                    return role_info
                else:
                    del self._cache[cache_key]
                    logger.debug(f"清除过期角色缓存: role_code={role_code}")
            
            logger.debug(f"角色缓存未命中: role_code={role_code}")
            return None
    
    def set_role_info(self, role_code: str, role_info: Dict) -> None:
        """
        设置角色信息缓存
        
        Args:
            role_code: 角色编码
            role_info: 角色信息
        """
        with self._lock:
            cache_key = f"role_info_{role_code}"
            self._cache[cache_key] = (role_info, time.time())
            logger.debug(f"设置角色缓存: role_code={role_code}")
    
    def invalidate_role_info(self, role_code: str = None) -> None:
        """
        使角色信息缓存失效
        
        Args:
            role_code: 角色编码，如果为None则清空所有角色缓存
        """
        with self._lock:
            if role_code:
                cache_key = f"role_info_{role_code}"
                if cache_key in self._cache:
                    del self._cache[cache_key]
                    logger.debug(f"使角色缓存失效: role_code={role_code}")
            else:
                # 清空所有角色缓存
                role_keys = [key for key in self._cache.keys() if key.startswith('role_info_')]
                for key in role_keys:
                    del self._cache[key]
                logger.info(f"清空所有角色缓存: cleared={len(role_keys)}")
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        with self._lock:
            total_entries = len(self._cache)
            user_permission_entries = len([k for k in self._cache.keys() if k.startswith('user_permissions_')])
            role_info_entries = len([k for k in self._cache.keys() if k.startswith('role_info_')])
            
            # 计算过期条目数
            current_time = time.time()
            expired_entries = 0
            for key, (data, timestamp) in self._cache.items():
                if current_time - timestamp > self._cache_ttl:
                    expired_entries += 1
            
            return {
                'total_entries': total_entries,
                'user_permission_entries': user_permission_entries,
                'role_info_entries': role_info_entries,
                'expired_entries': expired_entries,
                'cache_ttl': self._cache_ttl
            }
    
    def cleanup_expired(self) -> int:
        """
        清理过期的缓存条目
        
        Returns:
            清理的条目数
        """
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, (data, timestamp) in self._cache.items():
                if current_time - timestamp > self._cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
            
            logger.info(f"清理过期缓存: cleaned={len(expired_keys)}")
            return len(expired_keys)

# 全局权限缓存实例
permission_cache = PermissionCache()

def get_permission_cache() -> PermissionCache:
    """获取全局权限缓存实例"""
    return permission_cache