#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 操作审计日志功能
版本: 1.0
创建时间: 2025-07-24

提供全面的用户操作审计日志记录功能
包括登录、数据变更、权限操作、敏感操作等审计跟踪
"""

import json
import logging
from datetime import datetime
from functools import wraps
from flask import request, current_app, g
from flask_jwt_extended import get_current_user, verify_jwt_in_request
from sqlalchemy import text
from models import db
from typing import Dict, Any, Optional, List
import traceback
import uuid

logger = logging.getLogger(__name__)

class AuditLogger:
    """审计日志记录器"""
    
    # 操作类型常量
    OPERATION_TYPES = {
        # 认证相关
        'LOGIN': '用户登录',
        'LOGOUT': '用户登出',
        'LOGIN_FAILED': '登录失败',
        'PASSWORD_CHANGE': '密码修改',
        'PASSWORD_RESET': '密码重置',
        
        # 数据操作
        'CREATE': '创建数据',
        'UPDATE': '更新数据',
        'DELETE': '删除数据',
        'VIEW': '查看数据',
        'EXPORT': '导出数据',
        'IMPORT': '导入数据',
        
        # 权限操作
        'ROLE_ASSIGN': '分配角色',
        'ROLE_REVOKE': '撤销角色',
        'PERMISSION_GRANT': '授予权限',
        'PERMISSION_REVOKE': '撤销权限',
        
        # 系统管理
        'CONFIG_CHANGE': '配置修改',
        'SYSTEM_BACKUP': '系统备份',
        'SYSTEM_RESTORE': '系统恢复',
        'USER_ACTIVATE': '用户激活',
        'USER_DEACTIVATE': '用户禁用',
        
        # 业务操作
        'ORDER_CREATE': '创建订单',
        'ORDER_APPROVE': '订单审批',
        'ORDER_CANCEL': '取消订单',
        'INVENTORY_ADJUST': '库存调整',
        'MATERIAL_SHARE': '航材共享',
        'AOG_HANDLE': 'AOG处理',
        
        # 安全相关
        'SECURITY_VIOLATION': '安全违规',
        'ACCESS_DENIED': '访问拒绝',
        'SUSPICIOUS_ACTIVITY': '可疑活动',
        'DATA_BREACH_ATTEMPT': '数据泄露尝试'
    }
    
    # 风险级别
    RISK_LEVELS = {
        'LOW': '低风险',
        'MEDIUM': '中风险', 
        'HIGH': '高风险',
        'CRITICAL': '严重风险'
    }
    
    def __init__(self, app=None):
        """
        初始化审计日志记录器
        
        Args:
            app: Flask应用实例
        """
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """
        初始化Flask应用
        
        Args:
            app: Flask应用实例
        """
        app.config.setdefault('AUDIT_LOG_ENABLED', True)
        app.config.setdefault('AUDIT_LOG_RETENTION_DAYS', 365)
        app.config.setdefault('AUDIT_LOG_SENSITIVE_FIELDS', [
            'password', 'token', 'secret', 'key', 'credential'
        ])
        app.config.setdefault('AUDIT_LOG_EXCLUDE_PATHS', [
            '/health', '/ping', '/api/v1/health'
        ])
        
        # 存储实例到应用扩展中
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions['audit_logger'] = self
        
        # 确保审计日志表存在（在应用上下文中运行）
        with app.app_context():
            self._ensure_audit_table()
        
        logger.info("审计日志功能初始化完成")
    
    def _ensure_audit_table(self):
        """确保审计日志表存在"""
        try:
            # 创建审计日志表（如果不存在）
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS audit_logs (
                id SERIAL PRIMARY KEY,
                log_id VARCHAR(36) UNIQUE NOT NULL,
                user_id INTEGER,
                username VARCHAR(100),
                user_role VARCHAR(50),
                operation_type VARCHAR(50) NOT NULL,
                operation_desc VARCHAR(200),
                resource_type VARCHAR(50),
                resource_id VARCHAR(100),
                request_method VARCHAR(10),
                request_path VARCHAR(500),
                request_params TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                session_id VARCHAR(100),
                before_data TEXT,
                after_data TEXT,
                risk_level VARCHAR(20) DEFAULT 'LOW',
                is_success BOOLEAN DEFAULT true,
                error_message TEXT,
                execution_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # 创建索引（分别执行）
            index_sqls = [
                "CREATE INDEX IF NOT EXISTS idx_audit_user_id ON audit_logs (user_id);",
                "CREATE INDEX IF NOT EXISTS idx_audit_operation ON audit_logs (operation_type);",
                "CREATE INDEX IF NOT EXISTS idx_audit_created_at ON audit_logs (created_at);",
                "CREATE INDEX IF NOT EXISTS idx_audit_risk_level ON audit_logs (risk_level);",
                "CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_logs (resource_type, resource_id);"
            ]
            
            db.session.execute(text(create_table_sql))
            
            # 创建索引
            for index_sql in index_sqls:
                db.session.execute(text(index_sql))
            
            db.session.commit()
            logger.info("审计日志表和索引创建完成")
            
        except Exception as e:
            logger.error(f"创建审计日志表失败: {e}")
            db.session.rollback()
    
    def get_current_user_info(self) -> Dict[str, Any]:
        """
        获取当前用户信息
        
        Returns:
            用户信息字典
        """
        user_info = {
            'user_id': None,
            'username': 'anonymous',
            'user_role': 'guest'
        }
        
        try:
            verify_jwt_in_request(optional=True)
            current_user = get_current_user()
            if current_user:
                user_info.update({
                    'user_id': current_user.get('id'),
                    'username': current_user.get('username', 'unknown'),
                    'user_role': current_user.get('role', 'user')
                })
        except Exception:
            pass
        
        return user_info
    
    def get_client_info(self, request) -> Dict[str, str]:
        """
        获取客户端信息
        
        Args:
            request: Flask请求对象
            
        Returns:
            客户端信息字典
        """
        # 获取真实IP地址
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR')
        if ip_address:
            ip_address = ip_address.split(',')[0].strip()
        else:
            ip_address = request.environ.get('REMOTE_ADDR', '127.0.0.1')
        
        return {
            'ip_address': ip_address,
            'user_agent': request.headers.get('User-Agent', ''),
            'session_id': request.headers.get('X-Session-ID', str(uuid.uuid4()))
        }
    
    def sanitize_data(self, data: Any) -> Any:
        """
        清理敏感数据
        
        Args:
            data: 原始数据
            
        Returns:
            清理后的数据
        """
        if not data:
            return data
        
        sensitive_fields = current_app.config.get('AUDIT_LOG_SENSITIVE_FIELDS', [])
        
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_fields):
                    sanitized[key] = '***REDACTED***'
                else:
                    sanitized[key] = self.sanitize_data(value)
            return sanitized
        elif isinstance(data, list):
            return [self.sanitize_data(item) for item in data]
        else:
            return data
    
    def log_operation(self, operation_type: str, operation_desc: str = None,
                     resource_type: str = None, resource_id: str = None,
                     before_data: Any = None, after_data: Any = None,
                     risk_level: str = 'LOW', is_success: bool = True,
                     error_message: str = None, execution_time_ms: int = None,
                     additional_data: Dict[str, Any] = None):
        """
        记录操作审计日志
        
        Args:
            operation_type: 操作类型
            operation_desc: 操作描述
            resource_type: 资源类型
            resource_id: 资源ID
            before_data: 操作前数据
            after_data: 操作后数据
            risk_level: 风险级别
            is_success: 是否成功
            error_message: 错误消息
            execution_time_ms: 执行时间（毫秒）
            additional_data: 附加数据
        """
        try:
            if not current_app.config.get('AUDIT_LOG_ENABLED', True):
                return
            
            # 检查排除路径
            exclude_paths = current_app.config.get('AUDIT_LOG_EXCLUDE_PATHS', [])
            if request and any(path in request.path for path in exclude_paths):
                return
            
            # 获取用户和客户端信息
            user_info = self.get_current_user_info()
            client_info = self.get_client_info(request) if request else {}
            
            # 构建日志记录
            log_data = {
                'log_id': str(uuid.uuid4()),
                'user_id': user_info['user_id'],
                'username': user_info['username'],
                'user_role': user_info['user_role'],
                'operation_type': operation_type,
                'operation_desc': operation_desc or self.OPERATION_TYPES.get(operation_type, operation_type),
                'resource_type': resource_type,
                'resource_id': str(resource_id) if resource_id else None,
                'request_method': request.method if request else None,
                'request_path': request.path if request else None,
                'request_params': json.dumps(self.sanitize_data(dict(request.args))) if request and request.args else None,
                'ip_address': client_info.get('ip_address'),
                'user_agent': client_info.get('user_agent'),
                'session_id': client_info.get('session_id'),
                'before_data': json.dumps(self.sanitize_data(before_data)) if before_data else None,
                'after_data': json.dumps(self.sanitize_data(after_data)) if after_data else None,
                'risk_level': risk_level,
                'is_success': is_success,
                'error_message': error_message,
                'execution_time_ms': execution_time_ms
            }
            
            # 添加额外数据
            if additional_data:
                for key, value in additional_data.items():
                    if key not in log_data:
                        log_data[key] = value
            
            # 插入数据库
            insert_sql = """
            INSERT INTO audit_logs (
                log_id, user_id, username, user_role, operation_type, operation_desc,
                resource_type, resource_id, request_method, request_path, request_params,
                ip_address, user_agent, session_id, before_data, after_data,
                risk_level, is_success, error_message, execution_time_ms
            ) VALUES (
                :log_id, :user_id, :username, :user_role, :operation_type, :operation_desc,
                :resource_type, :resource_id, :request_method, :request_path, :request_params,
                :ip_address, :user_agent, :session_id, :before_data, :after_data,
                :risk_level, :is_success, :error_message, :execution_time_ms
            )
            """
            
            db.session.execute(text(insert_sql), log_data)
            db.session.commit()
            
            # 高风险操作记录到系统日志
            if risk_level in ['HIGH', 'CRITICAL']:
                logger.warning(f"高风险操作审计: {operation_type} - {operation_desc} "
                             f"用户: {user_info['username']} IP: {client_info.get('ip_address')}")
            
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
            db.session.rollback()
    
    def query_audit_logs(self, filters: Dict[str, Any] = None, 
                        page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        查询审计日志
        
        Args:
            filters: 过滤条件
            page: 页码
            per_page: 每页数量
            
        Returns:
            查询结果字典
        """
        try:
            # 构建查询条件
            where_conditions = []
            params = {}
            
            if filters:
                if filters.get('user_id'):
                    where_conditions.append("user_id = :user_id")
                    params['user_id'] = filters['user_id']
                
                if filters.get('username'):
                    where_conditions.append("username LIKE :username")
                    params['username'] = f"%{filters['username']}%"
                
                if filters.get('operation_type'):
                    where_conditions.append("operation_type = :operation_type")
                    params['operation_type'] = filters['operation_type']
                
                if filters.get('risk_level'):
                    where_conditions.append("risk_level = :risk_level")
                    params['risk_level'] = filters['risk_level']
                
                if filters.get('date_from'):
                    where_conditions.append("created_at >= :date_from")
                    params['date_from'] = filters['date_from']
                
                if filters.get('date_to'):
                    where_conditions.append("created_at <= :date_to")
                    params['date_to'] = filters['date_to']
                
                if filters.get('resource_type'):
                    where_conditions.append("resource_type = :resource_type")
                    params['resource_type'] = filters['resource_type']
                
                if filters.get('is_success') is not None:
                    where_conditions.append("is_success = :is_success")
                    params['is_success'] = filters['is_success']
            
            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            # 计算总数
            count_sql = f"SELECT COUNT(*) FROM audit_logs {where_clause}"
            total_count = db.session.execute(text(count_sql), params).scalar()
            
            # 查询数据
            offset = (page - 1) * per_page
            query_sql = f"""
            SELECT * FROM audit_logs {where_clause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
            """
            
            params.update({'limit': per_page, 'offset': offset})
            result = db.session.execute(text(query_sql), params).fetchall()
            
            # 转换为字典列表
            logs = []
            for row in result:
                log = dict(row._mapping)
                # 转换JSON字段
                for json_field in ['request_params', 'before_data', 'after_data']:
                    if log.get(json_field):
                        try:
                            log[json_field] = json.loads(log[json_field])
                        except json.JSONDecodeError:
                            pass
                logs.append(log)
            
            return {
                'logs': logs,
                'total': total_count,
                'page': page,
                'per_page': per_page,
                'pages': (total_count + per_page - 1) // per_page
            }
            
        except Exception as e:
            logger.error(f"查询审计日志失败: {e}")
            return {'logs': [], 'total': 0, 'page': page, 'per_page': per_page, 'pages': 0}
    
    def cleanup_old_logs(self, retention_days: int = None):
        """
        清理过期的审计日志
        
        Args:
            retention_days: 保留天数
        """
        try:
            if retention_days is None:
                retention_days = current_app.config.get('AUDIT_LOG_RETENTION_DAYS', 365)
            
            cleanup_sql = """
            DELETE FROM audit_logs 
            WHERE created_at < CURRENT_DATE - INTERVAL %s DAY
            """
            
            result = db.session.execute(text(cleanup_sql), (retention_days,))
            deleted_count = result.rowcount
            db.session.commit()
            
            logger.info(f"清理过期审计日志完成，删除 {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理审计日志失败: {e}")
            db.session.rollback()
            return 0

def audit_log(operation_type: str, operation_desc: str = None,
             resource_type: str = None, risk_level: str = 'LOW',
             log_request_data: bool = False, log_response_data: bool = False):
    """
    审计日志装饰器
    
    Args:
        operation_type: 操作类型
        operation_desc: 操作描述
        resource_type: 资源类型
        risk_level: 风险级别
        log_request_data: 是否记录请求数据
        log_response_data: 是否记录响应数据
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.now()
            audit_logger = current_app.extensions.get('audit_logger')
            
            if not audit_logger:
                return f(*args, **kwargs)
            
            before_data = None
            after_data = None
            resource_id = None
            is_success = True
            error_message = None
            
            try:
                # 记录请求数据
                if log_request_data and request:
                    before_data = {
                        'method': request.method,
                        'args': dict(request.args),
                        'json': request.get_json(silent=True),
                        'form': dict(request.form) if request.form else None
                    }
                
                # 从URL路径中提取资源ID
                if 'id' in kwargs:
                    resource_id = kwargs['id']
                elif request and '/api/' in request.path:
                    path_parts = request.path.split('/')
                    if len(path_parts) > 3 and path_parts[-1].isdigit():
                        resource_id = path_parts[-1]
                
                # 执行原函数
                result = f(*args, **kwargs)
                
                # 记录响应数据
                if log_response_data:
                    after_data = {
                        'status': 'success',
                        'result': result if isinstance(result, (dict, list, str, int, float, bool)) else str(result)
                    }
                
                return result
                
            except Exception as e:
                is_success = False
                error_message = str(e)
                logger.error(f"操作执行失败: {e}")
                raise
                
            finally:
                # 计算执行时间
                execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
                
                # 记录审计日志
                audit_logger.log_operation(
                    operation_type=operation_type,
                    operation_desc=operation_desc,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    before_data=before_data,
                    after_data=after_data,
                    risk_level=risk_level,
                    is_success=is_success,
                    error_message=error_message,
                    execution_time_ms=execution_time_ms
                )
        
        return decorated_function
    return decorator

def audit_login(success: bool = True, error_message: str = None):
    """记录登录审计日志的便捷函数"""
    audit_logger = current_app.extensions.get('audit_logger')
    if audit_logger:
        operation_type = 'LOGIN' if success else 'LOGIN_FAILED'
        risk_level = 'LOW' if success else 'MEDIUM'
        
        audit_logger.log_operation(
            operation_type=operation_type,
            resource_type='user_session',
            risk_level=risk_level,
            is_success=success,
            error_message=error_message
        )

def audit_data_change(operation_type: str, resource_type: str, resource_id: str,
                     before_data: Any = None, after_data: Any = None):
    """记录数据变更审计日志的便捷函数"""
    audit_logger = current_app.extensions.get('audit_logger')
    if audit_logger:
        risk_level = 'MEDIUM' if operation_type == 'DELETE' else 'LOW'
        
        audit_logger.log_operation(
            operation_type=operation_type,
            resource_type=resource_type,
            resource_id=resource_id,
            before_data=before_data,
            after_data=after_data,
            risk_level=risk_level
        )

def audit_permission_change(operation_type: str, target_user: str, permission_details: Dict[str, Any]):
    """记录权限变更审计日志的便捷函数"""
    audit_logger = current_app.extensions.get('audit_logger')
    if audit_logger:
        audit_logger.log_operation(
            operation_type=operation_type,
            resource_type='user_permission',
            resource_id=target_user,
            after_data=permission_details,
            risk_level='HIGH'
        )

# 创建全局审计日志实例
audit_logger = AuditLogger()