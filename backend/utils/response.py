#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 统一响应处理
版本: 1.0
创建时间: 2025-01-13

提供统一的API响应格式处理函数
"""

from flask import jsonify

def success_response(data=None, message='操作成功', status_code=200):
    """
    成功响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        status_code: HTTP状态码
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    response = {
        'error': 0,
        'message': message,
        'body': data if data is not None else {},
        'success': True
    }
    return jsonify(response), status_code

def error_response(error_code=500, message='操作失败', data=None):
    """
    错误响应格式
    
    Args:
        error_code: 错误码（通常与HTTP状态码相同）
        message: 错误消息
        data: 额外的错误数据
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    response = {
        'error': error_code,
        'message': message,
        'body': data if data is not None else {},
        'success': False
    }
    return jsonify(response), error_code

def paginated_response(items, pagination_info, message='获取数据成功'):
    """
    分页响应格式
    
    Args:
        items: 数据项列表
        pagination_info: 分页信息
        message: 响应消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    data = {
        'items': items,
        'pagination': pagination_info
    }
    return success_response(data, message)

def created_response(data, message='创建成功'):
    """
    创建成功响应格式
    
    Args:
        data: 创建的数据
        message: 响应消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return success_response(data, message, 201)

def updated_response(data, message='更新成功'):
    """
    更新成功响应格式
    
    Args:
        data: 更新后的数据
        message: 响应消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return success_response(data, message)

def deleted_response(message='删除成功'):
    """
    删除成功响应格式
    
    Args:
        message: 响应消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return success_response({}, message, 204)

def validation_error_response(errors):
    """
    验证错误响应格式
    
    Args:
        errors: 验证错误信息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(400, '数据验证失败', {'validation_errors': errors})

def not_found_response(resource='资源'):
    """
    资源不存在响应格式
    
    Args:
        resource: 资源名称
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(404, f'{resource}不存在')

def unauthorized_response(message='未授权访问'):
    """
    未授权响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(401, message)

def forbidden_response(message='权限不足'):
    """
    禁止访问响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(403, message)

def conflict_response(message='资源冲突'):
    """
    资源冲突响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(409, message)

def server_error_response(message='服务器内部错误'):
    """
    服务器错误响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (JSON响应, 状态码)
    """
    return error_response(500, message)