#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 环境配置加载器
版本: 1.0
创建时间: 2025-07-24

为Flask应用提供环境配置加载功能
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Optional, Any
from dotenv import load_dotenv

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)

class EnvironmentLoader:
    """环境配置加载器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_dir = self.project_root / 'config'
        self.current_env_file = self.config_dir / '.current_env'
        self.env_file = self.project_root / '.env'
        
    def load_environment(self, env_name: Optional[str] = None) -> Dict[str, Any]:
        """
        加载环境配置
        
        Args:
            env_name: 环境名称，如果为None则使用当前环境
            
        Returns:
            配置字典
        """
        try:
            # 首先加载.env文件
            if self.env_file.exists():
                load_dotenv(self.env_file)
                logger.info(f"✅ 已加载.env文件: {self.env_file}")
            else:
                logger.warning("⚠️  .env文件不存在，使用默认配置")
            
            # 获取当前环境
            if not env_name:
                env_name = self._get_current_environment()
            
            logger.info(f"🔧 当前环境: {env_name or 'default'}")
            
            # 构建配置字典
            config = self._build_config_dict(env_name)
            
            return config
            
        except Exception as e:
            logger.error(f"❌ 加载环境配置失败: {e}")
            return self._get_default_config()
    
    def _get_current_environment(self) -> Optional[str]:
        """获取当前环境名称"""
        try:
            if self.current_env_file.exists():
                return self.current_env_file.read_text().strip()
            return None
        except Exception as e:
            logger.error(f"获取当前环境失败: {e}")
            return None
    
    def _build_config_dict(self, env_name: Optional[str]) -> Dict[str, Any]:
        """构建Flask配置字典"""
        config = {}
        
        # 基础Flask配置
        config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
        config['DEBUG'] = os.getenv('FLASK_DEBUG', '0') == '1'
        config['TESTING'] = os.getenv('TESTING', 'False').lower() == 'true'
        
        # 数据库配置
        config['SQLALCHEMY_DATABASE_URI'] = os.getenv(
            'SQLALCHEMY_DATABASE_URI', 
            'sqlite:///cassdemo.db'
        )
        config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        config['SQLALCHEMY_ENGINE_OPTIONS'] = {
            'pool_recycle': 300,
            'pool_pre_ping': True
        }
        
        # 服务器配置
        config['HOST'] = os.getenv('HOST', 'localhost')
        config['PORT'] = int(os.getenv('PORT', '5000'))
        
        # Redis配置
        redis_url = os.getenv('REDIS_URL')
        if redis_url:
            config['REDIS_URL'] = redis_url
        
        # 日志配置
        config['LOG_LEVEL'] = os.getenv('LOG_LEVEL', 'INFO')
        
        # 自定义配置项
        custom_configs = [
            'WTF_CSRF_ENABLED', 'MAIL_DEBUG', 'SSL_REQUIRED',
            'BACKUP_RETENTION_DAYS', 'DATABASE_TYPE'
        ]
        
        for key in custom_configs:
            value = os.getenv(key)
            if value is not None:
                # 尝试转换布尔值
                if value.lower() in ('true', 'false'):
                    config[key] = value.lower() == 'true'
                # 尝试转换数字
                elif value.isdigit():
                    config[key] = int(value)
                else:
                    config[key] = value
        
        # 环境特定配置
        if env_name:
            config['ENVIRONMENT'] = env_name
            
            # 开发环境特定配置
            if env_name == 'development':
                config['TEMPLATES_AUTO_RELOAD'] = True
                config['EXPLAIN_TEMPLATE_LOADING'] = False
                
            # 生产环境特定配置
            elif env_name == 'production':
                config['TEMPLATES_AUTO_RELOAD'] = False
                config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 year
        
        return config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'SECRET_KEY': 'dev-secret-key-change-in-production',
            'DEBUG': True,
            'TESTING': False,
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///cassdemo.db',
            'SQLALCHEMY_TRACK_MODIFICATIONS': False,
            'HOST': 'localhost',
            'PORT': 5000,
            'LOG_LEVEL': 'INFO',
            'ENVIRONMENT': 'default'
        }
    
    def validate_required_config(self, config: Dict[str, Any]) -> bool:
        """
        验证必需的配置项
        
        Args:
            config: 配置字典
            
        Returns:
            是否有效
        """
        required_keys = [
            'SECRET_KEY', 'SQLALCHEMY_DATABASE_URI'
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in config or not config[key]:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"❌ 缺少必需配置项: {missing_keys}")
            return False
        
        # 验证数据库URL格式
        db_uri = config['SQLALCHEMY_DATABASE_URI']
        if not (db_uri.startswith('postgresql://') or db_uri.startswith('sqlite:///')):
            logger.error("❌ 数据库URI格式无效")
            return False
        
        logger.info("✅ 配置验证通过")
        return True
    
    def get_environment_info(self) -> Dict[str, Any]:
        """
        获取环境信息
        
        Returns:
            环境信息字典
        """
        current_env = self._get_current_environment()
        
        return {
            'current_environment': current_env,
            'env_file_exists': self.env_file.exists(),
            'config_dir_exists': self.config_dir.exists(),
            'has_current_env_marker': self.current_env_file.exists(),
            'flask_env': os.getenv('FLASK_ENV', 'development'),
            'flask_debug': os.getenv('FLASK_DEBUG', '0') == '1',
            'database_type': os.getenv('DATABASE_TYPE', 'unknown'),
            'log_level': os.getenv('LOG_LEVEL', 'INFO')
        }

def load_flask_config(app, env_name: Optional[str] = None) -> bool:
    """
    为Flask应用加载环境配置
    
    Args:
        app: Flask应用实例
        env_name: 环境名称
        
    Returns:
        是否成功
    """
    try:
        loader = EnvironmentLoader()
        config = loader.load_environment(env_name)
        
        # 验证配置
        if not loader.validate_required_config(config):
            logger.error("❌ 配置验证失败，使用默认配置")
            return False
        
        # 应用配置到Flask应用
        for key, value in config.items():
            app.config[key] = value
        
        logger.info(f"✅ Flask配置加载完成，环境: {config.get('ENVIRONMENT', 'unknown')}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Flask配置加载失败: {e}")
        return False

def create_env_loader():
    """创建环境加载器实例"""
    return EnvironmentLoader()