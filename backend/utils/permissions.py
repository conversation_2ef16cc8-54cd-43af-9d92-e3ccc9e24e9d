#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 动态权限验证装饰器
版本: 1.0
创建时间: 2025-07-24

提供基于数据库的动态权限验证功能
"""

from functools import wraps
from flask import current_app
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from services.role_service import RoleService
from utils.response import error_response
import logging

logger = logging.getLogger(__name__)

def require_permission(permission_code: str):
    """
    动态权限验证装饰器
    
    Args:
        permission_code: 需要的权限代码
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证JWT令牌
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                
                if not current_user_id:
                    logger.warning("权限验证失败: 用户未认证")
                    return error_response(401, '用户未认证')
                
                # 转换为整数
                try:
                    user_id = int(current_user_id)
                except (ValueError, TypeError):
                    logger.warning(f"权限验证失败: 无效的用户ID {current_user_id}")
                    return error_response(401, '用户认证信息无效')
                
                # 检查用户权限
                has_permission = RoleService.check_user_permission(user_id, permission_code)
                
                if not has_permission:
                    logger.warning(f"权限验证失败: 用户 {user_id} 缺少权限 {permission_code}")
                    return error_response(403, f'权限不足，需要权限: {permission_code}')
                
                logger.debug(f"权限验证成功: 用户 {user_id} 具有权限 {permission_code}")
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"权限验证异常: {e}")
                return error_response(500, f'权限验证失败: {str(e)}')
        
        return decorated_function
    return decorator

def require_any_permission(*permission_codes):
    """
    需要任一权限的装饰器
    
    Args:
        *permission_codes: 权限代码列表
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证JWT令牌
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                
                if not current_user_id:
                    logger.warning("权限验证失败: 用户未认证")
                    return error_response(401, '用户未认证')
                
                try:
                    user_id = int(current_user_id)
                except (ValueError, TypeError):
                    logger.warning(f"权限验证失败: 无效的用户ID {current_user_id}")
                    return error_response(401, '用户认证信息无效')
                
                # 检查是否有任一权限
                user_permissions = RoleService.get_user_permissions(user_id)
                
                # 如果有all_permissions，直接通过
                if 'all_permissions' in user_permissions:
                    logger.debug(f"权限验证成功: 用户 {user_id} 具有all_permissions")
                    return f(*args, **kwargs)
                
                # 检查是否有任一所需权限
                for permission_code in permission_codes:
                    if permission_code in user_permissions:
                        logger.debug(f"权限验证成功: 用户 {user_id} 具有权限 {permission_code}")
                        return f(*args, **kwargs)
                
                logger.warning(f"权限验证失败: 用户 {user_id} 缺少权限 {permission_codes}")
                return error_response(403, f'权限不足，需要以下任一权限: {", ".join(permission_codes)}')
                
            except Exception as e:
                logger.error(f"权限验证异常: {e}")
                return error_response(500, f'权限验证失败: {str(e)}')
        
        return decorated_function
    return decorator

def require_all_permissions(*permission_codes):
    """
    需要所有权限的装饰器
    
    Args:
        *permission_codes: 权限代码列表
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证JWT令牌
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                
                if not current_user_id:
                    logger.warning("权限验证失败: 用户未认证")
                    return error_response(401, '用户未认证')
                
                try:
                    user_id = int(current_user_id)
                except (ValueError, TypeError):
                    logger.warning(f"权限验证失败: 无效的用户ID {current_user_id}")
                    return error_response(401, '用户认证信息无效')
                
                # 检查是否有所有权限
                user_permissions = RoleService.get_user_permissions(user_id)
                
                # 如果有all_permissions，直接通过
                if 'all_permissions' in user_permissions:
                    logger.debug(f"权限验证成功: 用户 {user_id} 具有all_permissions")
                    return f(*args, **kwargs)
                
                # 检查是否有所有所需权限
                missing_permissions = []
                for permission_code in permission_codes:
                    if permission_code not in user_permissions:
                        missing_permissions.append(permission_code)
                
                if missing_permissions:
                    logger.warning(f"权限验证失败: 用户 {user_id} 缺少权限 {missing_permissions}")
                    return error_response(403, f'权限不足，缺少权限: {", ".join(missing_permissions)}')
                
                logger.debug(f"权限验证成功: 用户 {user_id} 具有所有权限 {permission_codes}")
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"权限验证异常: {e}")
                return error_response(500, f'权限验证失败: {str(e)}')
        
        return decorated_function
    return decorator

def require_role(*role_codes):
    """
    需要特定角色的装饰器
    
    Args:
        *role_codes: 角色代码列表
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证JWT令牌
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                
                if not current_user_id:
                    logger.warning("角色验证失败: 用户未认证")
                    return error_response(401, '用户未认证')
                
                try:
                    user_id = int(current_user_id)
                except (ValueError, TypeError):
                    logger.warning(f"角色验证失败: 无效的用户ID {current_user_id}")
                    return error_response(401, '用户认证信息无效')
                
                # 获取用户角色
                from models import db, User
                from sqlalchemy import text
                
                user_role_sql = """
                SELECT sr.role_code
                FROM users u
                JOIN system_roles sr ON u.role_id = sr.id
                WHERE u.id = :user_id AND sr.is_active = true
                """
                
                result = db.session.execute(text(user_role_sql), {'user_id': user_id}).fetchone()
                
                if not result:
                    logger.warning(f"角色验证失败: 找不到用户角色 {user_id}")
                    return error_response(403, '用户角色信息无效')
                
                user_role = result[0]
                
                # 检查角色是否匹配
                if user_role not in role_codes:
                    logger.warning(f"角色验证失败: 用户 {user_id} 角色 {user_role} 不在允许的角色 {role_codes} 中")
                    return error_response(403, f'角色权限不足，需要角色: {", ".join(role_codes)}')
                
                logger.debug(f"角色验证成功: 用户 {user_id} 角色 {user_role}")
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"角色验证异常: {e}")
                return error_response(500, f'角色验证失败: {str(e)}')
        
        return decorated_function
    return decorator

def require_internal_user():
    """
    需要内部员工的装饰器
    
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证JWT令牌
                verify_jwt_in_request()
                current_user_id = get_jwt_identity()
                
                if not current_user_id:
                    logger.warning("内部用户验证失败: 用户未认证")
                    return error_response(401, '用户未认证')
                
                try:
                    user_id = int(current_user_id)
                except (ValueError, TypeError):
                    logger.warning(f"内部用户验证失败: 无效的用户ID {current_user_id}")
                    return error_response(401, '用户认证信息无效')
                
                # 检查是否为内部用户
                from models import db, User
                from sqlalchemy import text
                
                internal_check_sql = """
                SELECT sr.category
                FROM users u
                JOIN system_roles sr ON u.role_id = sr.id
                WHERE u.id = :user_id AND sr.is_active = true
                """
                
                result = db.session.execute(text(internal_check_sql), {'user_id': user_id}).fetchone()
                
                if not result:
                    logger.warning(f"内部用户验证失败: 找不到用户信息 {user_id}")
                    return error_response(403, '用户信息无效')
                
                category = result[0]
                
                if category != 'internal':
                    logger.warning(f"内部用户验证失败: 用户 {user_id} 不是内部用户 (category: {category})")
                    return error_response(403, '需要内部员工权限')
                
                logger.debug(f"内部用户验证成功: 用户 {user_id}")
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"内部用户验证异常: {e}")
                return error_response(500, f'内部用户验证失败: {str(e)}')
        
        return decorated_function
    return decorator

def get_current_user_info():
    """
    获取当前用户信息（用于在已验证的上下文中调用）
    
    Returns:
        用户信息字典
    """
    try:
        current_user_id = get_jwt_identity()
        if not current_user_id:
            return None
        
        user_id = int(current_user_id)
        
        from models import db, User
        from sqlalchemy import text
        
        user_info_sql = """
        SELECT u.id, u.username, u.email, u.real_name, u.company_name,
               sr.role_code, sr.display_name as role_name, sr.category, sr.business_type
        FROM users u
        JOIN system_roles sr ON u.role_id = sr.id
        WHERE u.id = :user_id AND u.status = 'active' AND sr.is_active = true
        """
        
        result = db.session.execute(text(user_info_sql), {'user_id': user_id}).fetchone()
        
        if not result:
            return None
        
        return {
            'id': result[0],
            'username': result[1],
            'email': result[2],
            'real_name': result[3],
            'company_name': result[4],
            'role_code': result[5],
            'role_name': result[6],
            'category': result[7],
            'business_type': result[8]
        }
        
    except Exception as e:
        logger.error(f"获取当前用户信息失败: {e}")
        return None