#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库同步工具
版本: 1.0
创建时间: 2025-07-24

提供多机器开发环境下的数据库结构和数据同步功能
支持：
1. 数据库结构迁移
2. 种子数据同步
3. 开发数据导出/导入
4. 数据库状态检查
"""

import sys
import os
import json
import subprocess
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from sqlalchemy import text
from flask import current_app

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from app import app
from models import db
from flask_migrate import upgrade, current as current_revision, heads

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseSyncManager:
    """数据库同步管理器"""
    
    def __init__(self, app=None):
        self.app = app or current_app
        self.project_root = os.path.dirname(os.path.dirname(__file__))
        self.backup_dir = Path(self.project_root) / 'backups'
        self.backup_dir.mkdir(exist_ok=True)
        
    def get_database_status(self) -> Dict:
        """
        获取数据库状态信息
        
        Returns:
            包含数据库状态的字典
        """
        with self.app.app_context():
            try:
                # 检查数据库连接
                db.session.execute(text('SELECT 1'))
                connection_status = 'connected'
                
                # 获取当前迁移版本（直接查询数据库）
                current_rev = self._get_current_revision()
                head_revs = self._get_head_revisions()
                
                # 获取表数量统计
                table_stats = self._get_table_statistics()
                
                # 安全检查迁移状态
                if current_rev and head_revs and current_rev in head_revs:
                    migration_status = 'up_to_date'
                elif current_rev and head_revs:
                    migration_status = 'pending_migrations'
                else:
                    migration_status = 'unknown'
                
                return {
                    'status': 'healthy',
                    'connection': connection_status,
                    'current_revision': current_rev or 'none',
                    'head_revisions': head_revs or [],
                    'migration_status': migration_status,
                    'table_statistics': table_stats,
                    'timestamp': datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error(f"获取数据库状态失败: {e}")
                return {
                    'status': 'error',
                    'connection': 'unknown',
                    'current_revision': 'unknown',
                    'head_revisions': [],
                    'migration_status': 'unknown',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
    
    def _get_current_revision(self) -> str:
        """直接查询数据库获取当前版本"""
        try:
            result = db.session.execute(text("SELECT version_num FROM alembic_version LIMIT 1"))
            row = result.first()
            return row[0] if row else None
        except Exception:
            return None
    
    def _get_head_revisions(self) -> List[str]:
        """扫描migrations/versions目录获取最新版本"""
        try:
            versions_dir = Path(self.project_root) / 'migrations' / 'versions'
            if not versions_dir.exists():
                return []
            
            # 读取所有.py文件获取revision信息和依赖关系
            revisions = {}  # revision -> down_revision
            for py_file in versions_dir.glob('*.py'):
                if py_file.name.startswith('__'):
                    continue
                try:
                    content = py_file.read_text()
                    revision = None
                    down_revision = None
                    
                    # 查找revision = 'xxx'和down_revision = 'xxx'行
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith('revision ='):
                            revision = line.split('=')[1].strip().strip("'\"")
                        elif line.startswith('down_revision ='):
                            down_revision = line.split('=')[1].strip().strip("'\"")
                            if down_revision == 'None':
                                down_revision = None
                    
                    if revision:
                        revisions[revision] = down_revision
                except Exception as e:
                    logger.warning(f"解析迁移文件失败 {py_file}: {e}")
                    continue
            
            # 找到头部版本（没有被其他版本依赖的版本）
            all_revisions = set(revisions.keys())
            referenced_revisions = set(filter(None, revisions.values()))
            head_revisions = all_revisions - referenced_revisions
            
            return list(head_revisions)
        except Exception as e:
            logger.warning(f"获取头部版本失败: {e}")
            return []
    
    def _get_table_statistics(self) -> Dict[str, int]:
        """获取表数据统计"""
        try:
            tables = [
                'users', 'system_roles', 'system_permissions', 'role_permissions',
                'invitation_codes', 'invitation_code_uses', 'audit_logs',
                'materials', 'orders', 'inventory_items', 'demands'
            ]
            
            stats = {}
            for table in tables:
                try:
                    result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    stats[table] = result.scalar()
                except Exception:
                    stats[table] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"获取表统计失败: {e}")
            return {}
    
    def sync_database_schema(self) -> bool:
        """
        同步数据库结构（运行迁移）
        
        Returns:
            是否成功
        """
        with self.app.app_context():
            try:
                logger.info("🔄 开始同步数据库结构...")
                
                # 检查是否有待应用的迁移
                current_rev = self._get_current_revision()
                head_revs = self._get_head_revisions()
                
                if current_rev and head_revs and current_rev in head_revs:
                    logger.info("✅ 数据库结构已是最新，无需同步")
                    return True
                
                # 应用迁移
                upgrade()
                logger.info("✅ 数据库结构同步完成")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ 数据库结构同步失败: {e}")
                return False
    
    def export_seed_data(self, output_file: Optional[str] = None) -> bool:
        """
        导出种子数据到JSON文件
        
        Args:
            output_file: 输出文件路径，如果为None则使用默认路径
            
        Returns:
            是否成功
        """
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = self.backup_dir / f'seed_data_{timestamp}.json'
        
        with self.app.app_context():
            try:
                logger.info(f"📤 开始导出种子数据到: {output_file}")
                
                seed_data = {}
                
                # 导出系统角色
                roles = db.session.execute(text("""
                    SELECT role_code, role_name, display_name, description, 
                           category, business_type, sort_order, icon_name, theme_color
                    FROM system_roles 
                    ORDER BY sort_order
                """)).fetchall()
                
                seed_data['system_roles'] = [dict(row._mapping) for row in roles]
                
                # 导出系统权限
                permissions = db.session.execute(text("""
                    SELECT permission_code, permission_name, description, category, module
                    FROM system_permissions 
                    ORDER BY category, permission_code
                """)).fetchall()
                
                seed_data['system_permissions'] = [dict(row._mapping) for row in permissions]
                
                # 导出角色权限映射
                role_permissions = db.session.execute(text("""
                    SELECT r.role_code, p.permission_code
                    FROM role_permissions rp
                    JOIN system_roles r ON rp.role_id = r.id
                    JOIN system_permissions p ON rp.permission_id = p.id
                    ORDER BY r.sort_order, p.permission_code
                """)).fetchall()
                
                seed_data['role_permissions'] = [dict(row._mapping) for row in role_permissions]
                
                # 写入文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(seed_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ 种子数据导出完成: {output_file}")
                return True
                
            except Exception as e:
                logger.error(f"❌ 导出种子数据失败: {e}")
                return False
    
    def import_seed_data(self, input_file: str, overwrite: bool = False) -> bool:
        """
        从JSON文件导入种子数据
        
        Args:
            input_file: 输入文件路径
            overwrite: 是否覆盖现有数据
            
        Returns:
            是否成功
        """
        with self.app.app_context():
            try:
                logger.info(f"📥 开始从文件导入种子数据: {input_file}")
                
                # 读取数据文件
                with open(input_file, 'r', encoding='utf-8') as f:
                    seed_data = json.load(f)
                
                # 导入系统角色
                if 'system_roles' in seed_data:
                    for role in seed_data['system_roles']:
                        if overwrite:
                            # 更新或插入
                            db.session.execute(text("""
                                INSERT INTO system_roles (role_code, role_name, display_name, description, 
                                                        category, business_type, sort_order, icon_name, theme_color)
                                VALUES (:role_code, :role_name, :display_name, :description,
                                       :category, :business_type, :sort_order, :icon_name, :theme_color)
                                ON CONFLICT (role_code) DO UPDATE SET
                                    role_name = EXCLUDED.role_name,
                                    display_name = EXCLUDED.display_name, 
                                    description = EXCLUDED.description,
                                    category = EXCLUDED.category,
                                    business_type = EXCLUDED.business_type,
                                    sort_order = EXCLUDED.sort_order,
                                    icon_name = EXCLUDED.icon_name,
                                    theme_color = EXCLUDED.theme_color,
                                    updated_at = CURRENT_TIMESTAMP
                            """), role)
                        else:
                            # 仅插入新数据
                            db.session.execute(text("""
                                INSERT INTO system_roles (role_code, role_name, display_name, description, 
                                                        category, business_type, sort_order, icon_name, theme_color)
                                VALUES (:role_code, :role_name, :display_name, :description,
                                       :category, :business_type, :sort_order, :icon_name, :theme_color)
                                ON CONFLICT (role_code) DO NOTHING
                            """), role)
                
                # 导入系统权限
                if 'system_permissions' in seed_data:
                    for perm in seed_data['system_permissions']:
                        if overwrite:
                            db.session.execute(text("""
                                INSERT INTO system_permissions (permission_code, permission_name, description, category, module)
                                VALUES (:permission_code, :permission_name, :description, :category, :module)
                                ON CONFLICT (permission_code) DO UPDATE SET
                                    permission_name = EXCLUDED.permission_name,
                                    description = EXCLUDED.description,
                                    category = EXCLUDED.category,
                                    module = EXCLUDED.module
                            """), perm)
                        else:
                            db.session.execute(text("""
                                INSERT INTO system_permissions (permission_code, permission_name, description, category, module)
                                VALUES (:permission_code, :permission_name, :description, :category, :module)
                                ON CONFLICT (permission_code) DO NOTHING
                            """), perm)
                
                # 导入角色权限映射（仅在overwrite模式下）
                if 'role_permissions' in seed_data and overwrite:
                    # 先清理现有映射
                    db.session.execute(text("DELETE FROM role_permissions"))
                    
                    # 重新建立映射
                    for rp in seed_data['role_permissions']:
                        db.session.execute(text("""
                            INSERT INTO role_permissions (role_id, permission_id)
                            SELECT r.id, p.id
                            FROM system_roles r, system_permissions p
                            WHERE r.role_code = :role_code AND p.permission_code = :permission_code
                            ON CONFLICT (role_id, permission_id) DO NOTHING
                        """), rp)
                
                db.session.commit()
                logger.info("✅ 种子数据导入完成")
                return True
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"❌ 导入种子数据失败: {e}")
                return False
    
    def backup_database(self, output_file: Optional[str] = None) -> bool:
        """
        备份数据库（PostgreSQL专用）
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            if not output_file:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = self.backup_dir / f'database_backup_{timestamp}.sql'
            
            logger.info(f"💾 开始备份数据库到: {output_file}")
            
            # 从配置获取数据库连接信息
            db_url = self.app.config['SQLALCHEMY_DATABASE_URI']
            
            # 解析数据库URL
            if db_url.startswith('postgresql://'):
                # 提取连接信息
                import urllib.parse
                parsed = urllib.parse.urlparse(db_url)
                
                host = parsed.hostname or 'localhost'
                port = parsed.port or 5432
                database = parsed.path.lstrip('/')
                username = parsed.username
                password = parsed.password
                
                # 设置环境变量避免密码提示
                env = os.environ.copy()
                if password:
                    env['PGPASSWORD'] = password
                
                # 执行pg_dump
                cmd = [
                    'pg_dump',
                    f'--host={host}',
                    f'--port={port}',
                    f'--username={username}',
                    '--format=custom',
                    '--no-password',
                    '--verbose',
                    database
                ]
                
                with open(output_file, 'wb') as f:
                    result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, 
                                          env=env, check=True)
                
                logger.info(f"✅ 数据库备份完成: {output_file}")
                return True
            else:
                logger.error("❌ 不支持的数据库类型，仅支持PostgreSQL")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 数据库备份失败: {e.stderr.decode()}")
            return False
        except Exception as e:
            logger.error(f"❌ 数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_file: str) -> bool:
        """
        恢复数据库（PostgreSQL专用）
        
        Args:
            backup_file: 备份文件路径
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"🔄 开始从备份恢复数据库: {backup_file}")
            
            # 从配置获取数据库连接信息
            db_url = self.app.config['SQLALCHEMY_DATABASE_URI']
            
            if db_url.startswith('postgresql://'):
                import urllib.parse
                parsed = urllib.parse.urlparse(db_url)
                
                host = parsed.hostname or 'localhost'
                port = parsed.port or 5432
                database = parsed.path.lstrip('/')
                username = parsed.username
                password = parsed.password
                
                # 设置环境变量
                env = os.environ.copy()
                if password:
                    env['PGPASSWORD'] = password
                
                # 执行pg_restore
                cmd = [
                    'pg_restore',
                    f'--host={host}',
                    f'--port={port}',
                    f'--username={username}',
                    '--no-password',
                    '--verbose',
                    '--clean',
                    '--if-exists',
                    f'--dbname={database}',
                    backup_file
                ]
                
                result = subprocess.run(cmd, stderr=subprocess.PIPE, env=env, check=True)
                
                logger.info("✅ 数据库恢复完成")
                return True
            else:
                logger.error("❌ 不支持的数据库类型，仅支持PostgreSQL")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 数据库恢复失败: {e.stderr.decode()}")
            return False
        except Exception as e:
            logger.error(f"❌ 数据库恢复失败: {e}")
            return False
    
    def sync_to_target_machine(self, target_config: Dict[str, str]) -> bool:
        """
        同步到目标机器
        
        Args:
            target_config: 目标机器配置
            
        Returns:
            是否成功
        """
        try:
            logger.info("🔄 开始同步到目标机器...")
            
            # 1. 导出当前数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            seed_file = self.backup_dir / f'sync_seed_{timestamp}.json'
            
            if not self.export_seed_data(seed_file):
                return False
            
            # 2. 创建同步包
            sync_package = {
                'timestamp': timestamp,
                'source_status': self.get_database_status(),
                'seed_data_file': str(seed_file),
                'target_config': target_config
            }
            
            sync_file = self.backup_dir / f'sync_package_{timestamp}.json'
            with open(sync_file, 'w', encoding='utf-8') as f:
                json.dump(sync_package, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 同步包创建完成: {sync_file}")
            logger.info("📋 请将以下文件复制到目标机器:")
            logger.info(f"   - {seed_file}")
            logger.info(f"   - {sync_file}")
            logger.info("然后在目标机器上运行同步命令")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 同步到目标机器失败: {e}")
            return False

    def export_full_data(self, output_file: Optional[str] = None, 
                         include_tables: Optional[List[str]] = None,
                         exclude_sensitive: bool = True) -> bool:
        """
        导出完整业务数据到JSON文件
        
        Args:
            output_file: 输出文件路径
            include_tables: 包含的表列表，None则包含所有业务表
            exclude_sensitive: 是否排除敏感数据
            
        Returns:
            是否成功
        """
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = self.backup_dir / f'full_data_{timestamp}.json'
        
        with self.app.app_context():
            try:
                logger.info(f"📤 开始导出完整数据到: {output_file}")
                
                # 定义业务表及其导出顺序（考虑依赖关系）
                business_tables = [
                    'users',
                    'materials', 
                    'inventory_items',
                    'demands',
                    'orders',
                    'invitation_codes',
                    'invitation_code_uses',
                    'audit_logs'
                ]
                
                if include_tables:
                    business_tables = [t for t in business_tables if t in include_tables]
                
                full_data = {
                    'metadata': {
                        'export_time': datetime.now().isoformat(),
                        'tables_included': business_tables,
                        'exclude_sensitive': exclude_sensitive,
                        'schema_version': current_revision()
                    },
                    'data': {}
                }
                
                for table in business_tables:
                    try:
                        logger.info(f"  导出表: {table}")
                        
                        # 根据表名构建查询
                        if table == 'users' and exclude_sensitive:
                            # 排除密码等敏感信息
                            query = """
                                SELECT id, username, email, company_name, contact_person, 
                                       phone, role_id, status, created_at, updated_at
                                FROM users ORDER BY id
                            """
                        else:
                            query = f"SELECT * FROM {table} ORDER BY id"
                        
                        rows = db.session.execute(text(query)).fetchall()
                        full_data['data'][table] = [dict(row._mapping) for row in rows]
                        
                        logger.info(f"    {table}: {len(rows)} 条记录")
                        
                    except Exception as e:
                        logger.warning(f"⚠️  跳过表 {table}: {e}")
                        full_data['data'][table] = []
                
                # 写入文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(full_data, f, ensure_ascii=False, indent=2, default=str)
                
                logger.info(f"✅ 完整数据导出完成: {output_file}")
                return True
                
            except Exception as e:
                logger.error(f"❌ 导出完整数据失败: {e}")
                return False
    
    def import_full_data(self, input_file: str, 
                        mode: str = 'merge',
                        validate_schema: bool = True) -> bool:
        """
        从JSON文件导入完整业务数据
        
        Args:
            input_file: 输入文件路径
            mode: 导入模式 ('merge', 'replace', 'skip_existing')
            validate_schema: 是否验证模式版本
            
        Returns:
            是否成功
        """
        with self.app.app_context():
            try:
                logger.info(f"📥 开始从文件导入完整数据: {input_file}")
                
                # 读取数据文件
                with open(input_file, 'r', encoding='utf-8') as f:
                    full_data = json.load(f)
                
                # 验证数据格式
                if 'metadata' not in full_data or 'data' not in full_data:
                    raise ValueError("数据文件格式无效")
                
                metadata = full_data['metadata']
                data = full_data['data']
                
                # 验证模式版本
                if validate_schema:
                    current_schema = current_revision()
                    export_schema = metadata.get('schema_version')
                    if export_schema != current_schema:
                        logger.warning(f"⚠️  模式版本不匹配: 当前={current_schema}, 导出={export_schema}")
                
                logger.info(f"导入数据元信息: {metadata}")
                
                # 按依赖关系顺序导入数据
                import_order = [
                    'users',
                    'materials',
                    'inventory_items', 
                    'demands',
                    'orders',
                    'invitation_codes',
                    'invitation_code_uses',
                    'audit_logs'
                ]
                
                imported_counts = {}
                
                for table in import_order:
                    if table not in data:
                        continue
                        
                    table_data = data[table]
                    if not table_data:
                        continue
                    
                    logger.info(f"  导入表: {table} ({len(table_data)} 条记录)")
                    
                    imported_count = self._import_table_data(table, table_data, mode)
                    imported_counts[table] = imported_count
                    
                    logger.info(f"    {table}: {imported_count} 条记录已导入")
                
                db.session.commit()
                
                logger.info("✅ 完整数据导入完成")
                logger.info(f"导入统计: {imported_counts}")
                return True
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"❌ 导入完整数据失败: {e}")
                return False
    
    def _import_table_data(self, table_name: str, table_data: List[Dict], mode: str) -> int:
        """
        导入单个表的数据
        
        Args:
            table_name: 表名
            table_data: 表数据
            mode: 导入模式
            
        Returns:
            成功导入的记录数
        """
        imported_count = 0
        
        for record in table_data:
            try:
                if mode == 'replace':
                    # 替换模式：先删除再插入
                    if 'id' in record:
                        db.session.execute(
                            text(f"DELETE FROM {table_name} WHERE id = :id"),
                            {'id': record['id']}
                        )
                    self._insert_record(table_name, record)
                    imported_count += 1
                    
                elif mode == 'merge':
                    # 合并模式：存在则更新，不存在则插入
                    if self._record_exists(table_name, record):
                        self._update_record(table_name, record)
                    else:
                        self._insert_record(table_name, record)
                    imported_count += 1
                    
                elif mode == 'skip_existing':
                    # 跳过现有记录模式：仅插入新记录
                    if not self._record_exists(table_name, record):
                        self._insert_record(table_name, record)
                        imported_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️  跳过记录 {record.get('id', 'unknown')}: {e}")
        
        return imported_count
    
    def _record_exists(self, table_name: str, record: Dict) -> bool:
        """检查记录是否存在"""
        if 'id' not in record:
            return False
        
        result = db.session.execute(
            text(f"SELECT 1 FROM {table_name} WHERE id = :id"),
            {'id': record['id']}
        )
        return result.fetchone() is not None
    
    def _insert_record(self, table_name: str, record: Dict):
        """插入记录"""
        columns = list(record.keys())
        placeholders = [f":{col}" for col in columns]
        
        query = f"""
            INSERT INTO {table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
        """
        
        db.session.execute(text(query), record)
    
    def _update_record(self, table_name: str, record: Dict):
        """更新记录"""
        if 'id' not in record:
            return
        
        set_clauses = [f"{col} = :{col}" for col in record.keys() if col != 'id']
        
        query = f"""
            UPDATE {table_name} 
            SET {', '.join(set_clauses)}
            WHERE id = :id
        """
        
        db.session.execute(text(query), record)
    
    def export_incremental_data(self, since_date: str, 
                               output_file: Optional[str] = None) -> bool:
        """
        导出增量数据（自指定日期以来的变更）
        
        Args:
            since_date: 起始日期 (YYYY-MM-DD格式)
            output_file: 输出文件路径
            
        Returns:
            是否成功
        """
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = self.backup_dir / f'incremental_data_{timestamp}.json'
        
        with self.app.app_context():
            try:
                logger.info(f"📤 导出增量数据（自 {since_date}）到: {output_file}")
                
                # 支持增量导出的表（需要有updated_at字段）
                incremental_tables = [
                    'users',
                    'materials',
                    'inventory_items',
                    'orders'
                ]
                
                incremental_data = {
                    'metadata': {
                        'export_time': datetime.now().isoformat(),
                        'since_date': since_date,
                        'export_type': 'incremental'
                    },
                    'data': {}
                }
                
                for table in incremental_tables:
                    try:
                        query = f"""
                            SELECT * FROM {table} 
                            WHERE updated_at >= :since_date 
                            ORDER BY updated_at
                        """
                        
                        rows = db.session.execute(
                            text(query), 
                            {'since_date': since_date}
                        ).fetchall()
                        
                        incremental_data['data'][table] = [dict(row._mapping) for row in rows]
                        logger.info(f"  {table}: {len(rows)} 条变更记录")
                        
                    except Exception as e:
                        logger.warning(f"⚠️  跳过表 {table}: {e}")
                        incremental_data['data'][table] = []
                
                # 写入文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(incremental_data, f, ensure_ascii=False, indent=2, default=str)
                
                logger.info(f"✅ 增量数据导出完成: {output_file}")
                return True
                
            except Exception as e:
                logger.error(f"❌ 导出增量数据失败: {e}")
                return False
    
    def validate_data_integrity(self) -> Dict[str, any]:
        """
        验证数据完整性
        
        Returns:
            验证结果字典
        """
        with self.app.app_context():
            try:
                logger.info("🔍 开始验证数据完整性...")
                
                validation_results = {
                    'status': 'healthy',
                    'timestamp': datetime.now().isoformat(),
                    'checks': {},
                    'errors': []
                }
                
                # 检查外键完整性
                integrity_checks = [
                    {
                        'name': 'user_role_integrity',
                        'query': """
                            SELECT COUNT(*) FROM users u 
                            LEFT JOIN system_roles r ON u.role_id = r.id 
                            WHERE u.role_id IS NOT NULL AND r.id IS NULL
                        """,
                        'description': '用户角色关联完整性'
                    },
                    {
                        'name': 'order_user_integrity', 
                        'query': """
                            SELECT COUNT(*) FROM orders o
                            LEFT JOIN users u ON o.buyer_id = u.id
                            WHERE o.buyer_id IS NOT NULL AND u.id IS NULL
                        """,
                        'description': '订单用户关联完整性'
                    },
                    {
                        'name': 'inventory_material_integrity',
                        'query': """
                            SELECT COUNT(*) FROM inventory_items i
                            LEFT JOIN materials m ON i.material_id = m.id
                            WHERE i.material_id IS NOT NULL AND m.id IS NULL
                        """,
                        'description': '库存材料关联完整性'
                    }
                ]
                
                for check in integrity_checks:
                    try:
                        result = db.session.execute(text(check['query'])).scalar()
                        validation_results['checks'][check['name']] = {
                            'description': check['description'],
                            'violations': result,
                            'status': 'pass' if result == 0 else 'fail'
                        }
                        
                        if result > 0:
                            validation_results['errors'].append(
                                f"{check['description']}: 发现 {result} 处违规"
                            )
                    
                    except Exception as e:
                        validation_results['checks'][check['name']] = {
                            'description': check['description'],
                            'status': 'error',
                            'error': str(e)
                        }
                
                # 设置总体状态
                if validation_results['errors']:
                    validation_results['status'] = 'issues_found'
                
                logger.info(f"✅ 数据完整性验证完成: {validation_results['status']}")
                return validation_results
                
            except Exception as e:
                logger.error(f"❌ 数据完整性验证失败: {e}")
                return {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }

def create_sync_manager():
    """创建同步管理器实例"""
    return DatabaseSyncManager(app)