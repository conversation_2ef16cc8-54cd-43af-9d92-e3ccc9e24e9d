#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 第一阶段权限系统升级脚本
版本: 1.0
创建时间: 2025-07-19

执行第一阶段权限系统升级，包括：
1. 创建权限相关表
2. 更新现有用户数据
3. 初始化权限数据
4. 创建默认用户权限映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from sqlalchemy import text
from app import app
from models import db, User, Permission, UserPermission
from utils.init_data import DataManager

def upgrade_database():
    """执行数据库升级"""
    print("开始执行第一阶段权限系统升级...")
    
    with app.app_context():
        try:
            # 1. 确保所有表存在
            print("1. 检查和创建数据库表...")
            db.create_all()
            print("✓ 数据库表检查完成")
            
            # 2. 检查User表是否需要新增字段
            print("2. 检查User表字段...")
            
            # 检查是否需要添加新字段
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('users')]
            
            fields_to_add = []
            if 'company_type' not in columns:
                fields_to_add.append('company_type')
            if 'permission_level' not in columns:
                fields_to_add.append('permission_level')
                
            if fields_to_add:
                print(f"需要添加字段: {', '.join(fields_to_add)}")
                # 重新创建表（开发环境中的简单方式）
                # 生产环境中应该使用ALTER TABLE
                print("在开发环境中，这些字段应该已经在模型中定义")
            else:
                print("✓ User表字段检查完成，所有必需字段已存在")
            
            # 3. 初始化权限系统
            print("3. 初始化权限数据...")
            DataManager.init_permissions()
            db.session.commit()
            print("✓ 权限数据初始化完成")
            
            # 4. 更新现有用户数据
            print("4. 更新现有用户数据...")
            update_existing_users()
            print("✓ 用户数据更新完成")
            
            # 5. 为现有管理员分配所有权限
            print("5. 为管理员分配权限...")
            assign_admin_permissions()
            print("✓ 管理员权限分配完成")
            
            # 6. 验证升级结果
            print("6. 验证升级结果...")
            verify_upgrade()
            print("✓ 升级验证完成")
            
            print("🎉 第一阶段权限系统升级成功完成！")
            
        except Exception as e:
            print(f"❌ 升级失败: {str(e)}")
            db.session.rollback()
            raise

def update_existing_users():
    """更新现有用户数据"""
    users = User.query.all()
    updated_count = 0
    
    for user in users:
        changed = False
        
        # 设置默认的company_type
        if not hasattr(user, 'company_type') or user.company_type is None:
            if user.user_type == 'admin':
                user.company_type = 'internal'
            else:
                user.company_type = 'external'
            changed = True
        
        # 设置默认的permission_level
        if not hasattr(user, 'permission_level') or user.permission_level is None:
            if user.user_type == 'admin':
                user.permission_level = 4
            elif user.user_type in ['platform_staff']:
                user.permission_level = 3
            elif user.user_type in ['airline_buyer']:
                user.permission_level = 2
            else:
                user.permission_level = 1
            changed = True
        
        # 升级旧的用户类型到新的用户类型
        old_to_new_mapping = {
            'airline': 'airline_buyer',
            'maintenance': 'maintenance_engineer'
        }
        
        if user.user_type in old_to_new_mapping:
            print(f"将用户 {user.username} 的类型从 {user.user_type} 升级到 {old_to_new_mapping[user.user_type]}")
            user.user_type = old_to_new_mapping[user.user_type]
            changed = True
        
        if changed:
            updated_count += 1
    
    if updated_count > 0:
        db.session.commit()
        print(f"更新了 {updated_count} 个用户的数据")
    else:
        print("没有用户数据需要更新")

def assign_admin_permissions():
    """为管理员分配所有权限"""
    admin_users = User.query.filter_by(user_type='admin').all()
    all_permissions_perm = Permission.query.filter_by(code='all_permissions').first()
    
    if not all_permissions_perm:
        print("警告: 未找到 'all_permissions' 权限")
        return
    
    for admin in admin_users:
        # 检查是否已经有该权限
        existing = UserPermission.query.filter_by(
            user_id=admin.id,
            permission_id=all_permissions_perm.id
        ).first()
        
        if not existing:
            user_permission = UserPermission(
                user_id=admin.id,
                permission_id=all_permissions_perm.id,
                granted_by=admin.id,  # 自己分配给自己
                is_active=True
            )
            db.session.add(user_permission)
            print(f"为管理员 {admin.username} 分配了所有权限")
    
    db.session.commit()

def verify_upgrade():
    """验证升级结果"""
    # 检查权限数量
    permissions_count = Permission.query.count()
    print(f"权限总数: {permissions_count}")
    
    # 检查用户数量和类型分布
    users_count = User.query.count()
    print(f"用户总数: {users_count}")
    
    user_types = db.session.query(
        User.user_type, 
        db.func.count(User.id)
    ).group_by(User.user_type).all()
    
    print("用户类型分布:")
    for user_type, count in user_types:
        print(f"  {user_type}: {count}")
    
    # 检查管理员权限
    admin_permissions = db.session.query(UserPermission).join(
        User, UserPermission.user_id == User.id
    ).filter(
        User.user_type == 'admin'
    ).count()
    print(f"管理员权限分配数: {admin_permissions}")
    
    # 检查数据完整性
    users_without_company_type = User.query.filter(
        (User.company_type == None) | (User.company_type == '')
    ).count()
    
    users_without_permission_level = User.query.filter(
        (User.permission_level == None) | (User.permission_level == 0)
    ).count()
    
    if users_without_company_type > 0:
        print(f"警告: {users_without_company_type} 个用户没有设置company_type")
    
    if users_without_permission_level > 0:
        print(f"警告: {users_without_permission_level} 个用户没有设置permission_level")
    
    print("验证完成")

def rollback_database():
    """回滚数据库更改（仅删除权限相关数据）"""
    print("开始回滚权限系统升级...")
    
    with app.app_context():
        try:
            # 删除用户权限
            UserPermission.query.delete()
            
            # 删除权限
            Permission.query.delete()
            
            # 提交更改
            db.session.commit()
            print("✓ 权限系统数据已回滚")
            
        except Exception as e:
            print(f"❌ 回滚失败: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='第一阶段权限系统升级脚本')
    parser.add_argument('--rollback', action='store_true', help='回滚权限系统更改')
    
    args = parser.parse_args()
    
    if args.rollback:
        confirm = input("确定要回滚权限系统更改吗？这将删除所有权限数据 (y/N): ")
        if confirm.lower() == 'y':
            rollback_database()
        else:
            print("取消回滚操作")
    else:
        upgrade_database()