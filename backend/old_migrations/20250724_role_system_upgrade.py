#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库架构升级脚本
版本: 1.0
创建时间: 2025-07-24

升级内容：
1. 创建动态角色系统表结构
2. 创建邀请码系统表结构  
3. 迁移现有用户数据
4. 初始化基础角色和权限数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from models import db
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_system_roles_table():
    """创建系统角色表"""
    logger.info("创建 system_roles 表...")
    
    sql = """
    CREATE TABLE IF NOT EXISTS system_roles (
        id SERIAL PRIMARY KEY,
        role_code VARCHAR(50) UNIQUE NOT NULL,
        role_name VARCHAR(100) NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        category VARCHAR(20) NOT NULL CHECK (category IN ('internal', 'external')),
        business_type VARCHAR(20) NOT NULL CHECK (business_type IN ('buy_only', 'sell_only', 'buy_and_sell', 'service')),
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        icon_name VARCHAR(50),
        theme_color VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    db.session.execute(text(sql))
    db.session.commit()
    logger.info("✅ system_roles 表创建成功")

def create_system_permissions_table():
    """创建系统权限表"""
    logger.info("创建 system_permissions 表...")
    
    sql = """
    CREATE TABLE IF NOT EXISTS system_permissions (
        id SERIAL PRIMARY KEY,
        permission_code VARCHAR(100) UNIQUE NOT NULL,
        permission_name VARCHAR(100) NOT NULL,
        description TEXT,
        category VARCHAR(50),
        module VARCHAR(50),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    db.session.execute(text(sql))
    db.session.commit()
    logger.info("✅ system_permissions 表创建成功")

def create_role_permissions_table():
    """创建角色权限关联表"""
    logger.info("创建 role_permissions 表...")
    
    sql = """
    CREATE TABLE IF NOT EXISTS role_permissions (
        id SERIAL PRIMARY KEY,
        role_id INTEGER REFERENCES system_roles(id) ON DELETE CASCADE,
        permission_id INTEGER REFERENCES system_permissions(id) ON DELETE CASCADE,
        granted_by INTEGER REFERENCES users(id),
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(role_id, permission_id)
    );
    """
    
    db.session.execute(text(sql))
    db.session.commit()
    logger.info("✅ role_permissions 表创建成功")

def create_invitation_codes_table():
    """创建邀请码表"""
    logger.info("创建 invitation_codes 表...")
    
    sql = """
    CREATE TABLE IF NOT EXISTS invitation_codes (
        id SERIAL PRIMARY KEY,
        code VARCHAR(20) UNIQUE NOT NULL,
        code_type VARCHAR(10) NOT NULL CHECK (code_type IN ('staff', 'admin')),
        allowed_roles TEXT[],
        created_by INTEGER REFERENCES users(id),
        expires_at TIMESTAMP NOT NULL,
        max_uses INTEGER DEFAULT 1,
        used_count INTEGER DEFAULT 0,
        ip_whitelist INET[],
        status VARCHAR(10) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'disabled')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    db.session.execute(text(sql))
    db.session.commit()
    logger.info("✅ invitation_codes 表创建成功")

def create_invitation_code_uses_table():
    """创建邀请码使用记录表"""
    logger.info("创建 invitation_code_uses 表...")
    
    sql = """
    CREATE TABLE IF NOT EXISTS invitation_code_uses (
        id SERIAL PRIMARY KEY,
        invitation_code_id INTEGER REFERENCES invitation_codes(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id),
        ip_address INET,
        user_agent TEXT,
        used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    db.session.execute(text(sql))
    db.session.commit()
    logger.info("✅ invitation_code_uses 表创建成功")

def initialize_core_roles():
    """初始化7种核心角色数据"""
    logger.info("初始化核心角色数据...")
    
    core_roles = [
        # 外部用户（公开注册）
        {
            'role_code': 'supplier',
            'role_name': '供应商',
            'display_name': '供应商',
            'description': '航材制造商，只对外销售，不采购',
            'category': 'external',
            'business_type': 'sell_only',
            'sort_order': 1,
            'icon_name': 'Factory',
            'theme_color': '#10B981'
        },
        {
            'role_code': 'distributor', 
            'role_name': '分销商',
            'display_name': '分销商',
            'description': '航材贸易商，既采购也销售，是供应链的中间环节',
            'category': 'external',
            'business_type': 'buy_and_sell',
            'sort_order': 2,
            'icon_name': 'Shop',
            'theme_color': '#F59E0B'
        },
        {
            'role_code': 'airline_buyer',
            'role_name': '航空公司',
            'display_name': '航空公司',
            'description': '航材最终使用方，主要采购航材用于维修和运营',
            'category': 'external', 
            'business_type': 'buy_only',
            'sort_order': 3,
            'icon_name': 'Plane',
            'theme_color': '#3B82F6'
        },
        
        # 内部员工（邀请码注册）
        {
            'role_code': 'platform_staff',
            'role_name': '平台员工',
            'display_name': '平台员工',
            'description': '运营管理人员，负责跨公司数据管理、订单审核、客户服务',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 4,
            'icon_name': 'UserCheck',
            'theme_color': '#8B5CF6'
        },
        {
            'role_code': 'maintenance_engineer',
            'role_name': '维修工程师', 
            'display_name': '维修工程师',
            'description': '技术支持人员，负责维修工单、技术支持、备件申请',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 5,
            'icon_name': 'Tools',
            'theme_color': '#EF4444'
        },
        {
            'role_code': 'logistics_specialist',
            'role_name': '物流专员',
            'display_name': '物流专员', 
            'description': '配送跟踪人员，负责货运跟踪、配送管理、物流更新',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 6,
            'icon_name': 'Truck',
            'theme_color': '#06B6D4'
        },
        {
            'role_code': 'admin',
            'role_name': '系统管理员',
            'display_name': '系统管理员',
            'description': '系统管理人员，负责用户管理、权限配置、系统监控',  
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 7,
            'icon_name': 'Settings',
            'theme_color': '#6B7280'
        }
    ]
    
    for role_data in core_roles:
        # 检查角色是否已存在
        existing_role = db.session.execute(
            text("SELECT id FROM system_roles WHERE role_code = :role_code"),
            {'role_code': role_data['role_code']}
        ).fetchone()
        
        if not existing_role:
            sql = """
            INSERT INTO system_roles (
                role_code, role_name, display_name, description, category, 
                business_type, sort_order, icon_name, theme_color
            ) VALUES (
                :role_code, :role_name, :display_name, :description, :category,
                :business_type, :sort_order, :icon_name, :theme_color
            )
            """
            db.session.execute(text(sql), role_data)
            logger.info(f"✅ 创建角色: {role_data['display_name']}")
        else:
            logger.info(f"⚠️  角色已存在: {role_data['display_name']}")
    
    db.session.commit()
    logger.info("✅ 核心角色数据初始化完成")

def initialize_basic_permissions():
    """初始化基础权限数据"""
    logger.info("初始化基础权限数据...")
    
    basic_permissions = [
        # 基础权限
        {'permission_code': 'view_own_data', 'permission_name': '查看自己的数据', 'category': 'basic', 'module': 'user'},
        {'permission_code': 'manage_inventory', 'permission_name': '管理库存', 'category': 'basic', 'module': 'inventory'},
        {'permission_code': 'publish_shared', 'permission_name': '发布共享件', 'category': 'basic', 'module': 'sharing'},
        
        # 业务权限
        {'permission_code': 'publish_demand', 'permission_name': '发布需求', 'category': 'business', 'module': 'demand'},
        {'permission_code': 'respond_to_inquiry', 'permission_name': '响应询价', 'category': 'business', 'module': 'inquiry'},
        {'permission_code': 'manage_orders', 'permission_name': '管理订单', 'category': 'business', 'module': 'order'},
        {'permission_code': 'aog_request', 'permission_name': 'AOG紧急请求', 'category': 'business', 'module': 'aog'},
        
        # 平台权限
        {'permission_code': 'process_all_orders', 'permission_name': '处理所有订单', 'category': 'platform', 'module': 'order'},
        {'permission_code': 'cross_company_access', 'permission_name': '跨公司数据访问', 'category': 'platform', 'module': 'data'},
        {'permission_code': 'audit_shared_materials', 'permission_name': '审核共享材料', 'category': 'platform', 'module': 'audit'},
        {'permission_code': 'customer_service', 'permission_name': '客户服务', 'category': 'platform', 'module': 'service'},
        {'permission_code': 'data_analysis', 'permission_name': '数据分析', 'category': 'platform', 'module': 'analytics'},
        
        # 维修权限
        {'permission_code': 'update_maintenance_status', 'permission_name': '更新维修状态', 'category': 'maintenance', 'module': 'maintenance'},
        {'permission_code': 'technical_support', 'permission_name': '技术支持', 'category': 'maintenance', 'module': 'support'},
        {'permission_code': 'view_maintenance_data', 'permission_name': '查看维修数据', 'category': 'maintenance', 'module': 'data'},
        {'permission_code': 'manage_work_orders', 'permission_name': '管理工单', 'category': 'maintenance', 'module': 'workorder'},
        
        # 物流权限
        {'permission_code': 'track_shipment', 'permission_name': '跟踪货运', 'category': 'logistics', 'module': 'shipment'},
        {'permission_code': 'update_logistics', 'permission_name': '更新物流信息', 'category': 'logistics', 'module': 'logistics'},
        {'permission_code': 'manage_delivery', 'permission_name': '管理配送', 'category': 'logistics', 'module': 'delivery'},
        {'permission_code': 'view_logistics_data', 'permission_name': '查看物流数据', 'category': 'logistics', 'module': 'data'},
        
        # 管理权限
        {'permission_code': 'user_management', 'permission_name': '用户管理', 'category': 'admin', 'module': 'user'},
        {'permission_code': 'role_management', 'permission_name': '角色管理', 'category': 'admin', 'module': 'role'},
        {'permission_code': 'system_config', 'permission_name': '系统配置', 'category': 'admin', 'module': 'system'},
        {'permission_code': 'all_permissions', 'permission_name': '所有权限', 'category': 'admin', 'module': 'system'}
    ]
    
    for perm_data in basic_permissions:
        # 检查权限是否已存在
        existing_perm = db.session.execute(
            text("SELECT id FROM system_permissions WHERE permission_code = :permission_code"),
            {'permission_code': perm_data['permission_code']}
        ).fetchone()
        
        if not existing_perm:
            sql = """
            INSERT INTO system_permissions (
                permission_code, permission_name, category, module, description
            ) VALUES (
                :permission_code, :permission_name, :category, :module, :permission_name
            )
            """
            db.session.execute(text(sql), perm_data)
            logger.info(f"✅ 创建权限: {perm_data['permission_name']}")
        else:
            logger.info(f"⚠️  权限已存在: {perm_data['permission_name']}")
    
    db.session.commit()
    logger.info("✅ 基础权限数据初始化完成")

def assign_role_permissions():
    """分配角色权限"""
    logger.info("分配角色权限...")
    
    # 角色权限映射
    role_permissions_mapping = {
        'supplier': [
            'view_own_data', 'manage_inventory', 'publish_shared', 
            'respond_to_inquiry', 'manage_orders'
        ],
        'distributor': [
            'view_own_data', 'manage_inventory', 'publish_shared',
            'publish_demand', 'respond_to_inquiry', 'manage_orders'
        ],
        'airline_buyer': [
            'view_own_data', 'manage_inventory', 'publish_shared',
            'publish_demand', 'manage_orders', 'aog_request'
        ],
        'platform_staff': [
            'view_own_data', 'manage_inventory', 'publish_shared', 'publish_demand',
            'respond_to_inquiry', 'manage_orders', 'process_all_orders',
            'cross_company_access', 'audit_shared_materials', 'customer_service',
            'data_analysis', 'user_management'
        ],
        'maintenance_engineer': [
            'view_own_data', 'view_maintenance_data', 'update_maintenance_status',
            'technical_support', 'manage_work_orders'
        ],
        'logistics_specialist': [
            'view_own_data', 'view_logistics_data', 'track_shipment',
            'update_logistics', 'manage_delivery'
        ],
        'admin': [
            'all_permissions'  # 管理员拥有所有权限
        ]
    }
    
    for role_code, permission_codes in role_permissions_mapping.items():
        # 获取角色ID
        role_result = db.session.execute(
            text("SELECT id FROM system_roles WHERE role_code = :role_code"),
            {'role_code': role_code}
        ).fetchone()
        
        if not role_result:
            logger.warning(f"⚠️  角色不存在: {role_code}")
            continue
            
        role_id = role_result[0]
        
        for permission_code in permission_codes:
            # 获取权限ID
            perm_result = db.session.execute(
                text("SELECT id FROM system_permissions WHERE permission_code = :permission_code"),
                {'permission_code': permission_code}
            ).fetchone()
            
            if not perm_result:
                logger.warning(f"⚠️  权限不存在: {permission_code}")
                continue
                
            permission_id = perm_result[0]
            
            # 检查是否已经分配
            existing = db.session.execute(
                text("SELECT id FROM role_permissions WHERE role_id = :role_id AND permission_id = :permission_id"),
                {'role_id': role_id, 'permission_id': permission_id}
            ).fetchone()
            
            if not existing:
                sql = """
                INSERT INTO role_permissions (role_id, permission_id, granted_at)
                VALUES (:role_id, :permission_id, CURRENT_TIMESTAMP)
                """
                db.session.execute(text(sql), {
                    'role_id': role_id,
                    'permission_id': permission_id
                })
                logger.info(f"✅ 分配权限: {role_code} -> {permission_code}")
    
    db.session.commit()
    logger.info("✅ 角色权限分配完成")

def add_role_id_to_users():
    """为用户表添加role_id字段"""
    logger.info("为用户表添加role_id字段...")
    
    try:
        # 检查字段是否已存在
        check_sql = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role_id'
        """
        result = db.session.execute(text(check_sql)).fetchone()
        
        if not result:
            # 添加role_id字段
            sql = """
            ALTER TABLE users 
            ADD COLUMN role_id INTEGER REFERENCES system_roles(id)
            """
            db.session.execute(text(sql))
            db.session.commit()
            logger.info("✅ role_id字段添加成功")
        else:
            logger.info("⚠️  role_id字段已存在")
    except Exception as e:
        logger.error(f"❌ 添加role_id字段失败: {e}")
        db.session.rollback()

def migrate_existing_users():
    """迁移现有用户数据到新的角色系统"""
    logger.info("迁移现有用户数据...")
    
    # 用户类型映射到新角色
    user_type_mapping = {
        'admin': 'admin',
        'airline': 'airline_buyer',
        'airline_buyer': 'airline_buyer', 
        'supplier': 'supplier',
        'distributor': 'distributor',
        'maintenance': 'maintenance_engineer',
        'maintenance_engineer': 'maintenance_engineer',
        'platform_staff': 'platform_staff',
        'logistics_specialist': 'logistics_specialist'
    }
    
    try:
        # 获取所有用户
        users_result = db.session.execute(
            text("SELECT id, username, user_type FROM users WHERE role_id IS NULL")
        ).fetchall()
        
        for user in users_result:
            user_id, username, user_type = user
            
            # 映射到新角色
            new_role_code = user_type_mapping.get(user_type, user_type)
            
            # 获取新角色ID
            role_result = db.session.execute(
                text("SELECT id FROM system_roles WHERE role_code = :role_code"),
                {'role_code': new_role_code}
            ).fetchone()
            
            if role_result:
                role_id = role_result[0]
                
                # 更新用户的role_id
                update_sql = """
                UPDATE users 
                SET role_id = :role_id, updated_at = CURRENT_TIMESTAMP 
                WHERE id = :user_id
                """
                db.session.execute(text(update_sql), {
                    'role_id': role_id,
                    'user_id': user_id
                })
                logger.info(f"✅ 迁移用户: {username} ({user_type} -> {new_role_code})")
            else:
                logger.warning(f"⚠️  找不到对应角色: {new_role_code} for user {username}")
        
        db.session.commit()
        logger.info("✅ 用户数据迁移完成")
        
    except Exception as e:
        logger.error(f"❌ 用户数据迁移失败: {e}")
        db.session.rollback()

def run_migration():
    """执行完整的数据库架构升级"""
    logger.info("🚀 开始数据库架构升级...")
    
    try:
        # 1. 创建新表结构
        create_system_roles_table()
        create_system_permissions_table() 
        create_role_permissions_table()
        create_invitation_codes_table()
        create_invitation_code_uses_table()
        
        # 2. 初始化基础数据
        initialize_core_roles()
        initialize_basic_permissions()
        assign_role_permissions()
        
        # 3. 用户表升级
        add_role_id_to_users()
        migrate_existing_users()
        
        logger.info("🎉 数据库架构升级完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库架构升级失败: {e}")
        db.session.rollback()
        return False

if __name__ == '__main__':
    from app import app
    with app.app_context():
        success = run_migration()
        if success:
            print("✅ 数据库架构升级成功完成")
        else:
            print("❌ 数据库架构升级失败")