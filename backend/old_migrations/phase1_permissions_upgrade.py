#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 第一阶段权限系统升级迁移脚本
版本: 1.0
创建时间: 2025-07-19

此脚本用于升级现有数据库以支持新的权限系统：
1. 为User表添加新字段（company_type, permission_level）
2. 创建新的权限相关表
3. 迁移现有用户数据到新的角色系统
4. 初始化基础权限数据

注意：执行前请备份数据库！
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase1_migration.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class Phase1PermissionsMigration:
    """第一阶段权限系统迁移类"""
    
    def __init__(self, db_path: str = 'aviation_platform.db'):
        """
        初始化迁移工具
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.backup_path = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{db_path}"
        
    def run_migration(self):
        """执行完整的迁移流程"""
        logger.info("开始执行第一阶段权限系统迁移")
        
        try:
            # 1. 备份数据库
            self._backup_database()
            
            # 2. 连接数据库
            conn = sqlite3.connect(self.db_path)
            conn.execute("PRAGMA foreign_keys = ON")
            
            # 3. 执行数据库结构变更
            self._alter_user_table(conn)
            self._create_permission_tables(conn)
            
            # 4. 迁移现有数据
            self._migrate_user_data(conn)
            self._initialize_permissions(conn)
            self._initialize_default_user_permissions(conn)
            
            # 5. 验证迁移结果
            self._verify_migration(conn)
            
            conn.commit()
            conn.close()
            
            logger.info("第一阶段权限系统迁移完成")
            
        except Exception as e:
            logger.error(f"迁移过程中出现错误: {str(e)}")
            self._rollback_changes()
            raise
    
    def _backup_database(self):
        """备份数据库"""
        logger.info(f"正在备份数据库到 {self.backup_path}")
        
        import shutil
        try:
            shutil.copy2(self.db_path, self.backup_path)
            logger.info("数据库备份完成")
        except Exception as e:
            logger.error(f"数据库备份失败: {str(e)}")
            raise
    
    def _alter_user_table(self, conn: sqlite3.Connection):
        """修改User表结构"""
        logger.info("正在修改User表结构")
        
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加company_type字段
        if 'company_type' not in columns:
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN company_type TEXT DEFAULT 'external'
                CHECK (company_type IN ('internal', 'external'))
            """)
            logger.info("添加company_type字段")
        
        # 添加permission_level字段
        if 'permission_level' not in columns:
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN permission_level INTEGER DEFAULT 1
                CHECK (permission_level BETWEEN 1 AND 4)
            """)
            logger.info("添加permission_level字段")
        
        # 更新user_type枚举值（SQLite不支持直接修改枚举，需要重新创建表）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users_new'")
        if not cursor.fetchone():
            # 创建新的users表
            cursor.execute("""
                CREATE TABLE users_new (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    user_type TEXT NOT NULL CHECK (
                        user_type IN (
                            'airline_buyer', 'platform_staff', 'maintenance_engineer', 
                            'logistics_specialist', 'admin',
                            'airline', 'supplier', 'maintenance'
                        )
                    ),
                    company_type TEXT DEFAULT 'external' CHECK (company_type IN ('internal', 'external')),
                    permission_level INTEGER DEFAULT 1 CHECK (permission_level BETWEEN 1 AND 4),
                    company_name TEXT,
                    real_name TEXT,
                    phone TEXT,
                    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 复制数据
            cursor.execute("""
                INSERT INTO users_new (
                    id, username, email, password_hash, user_type, company_type, 
                    permission_level, company_name, real_name, phone, status, 
                    last_login, created_at, updated_at
                )
                SELECT 
                    id, username, email, password_hash, user_type, 
                    COALESCE(company_type, 'external'),
                    COALESCE(permission_level, 1),
                    company_name, real_name, phone, status, 
                    last_login, created_at, updated_at
                FROM users
            """)
            
            # 删除旧表，重命名新表
            cursor.execute("DROP TABLE users")
            cursor.execute("ALTER TABLE users_new RENAME TO users")
            
            logger.info("用户表结构更新完成")
    
    def _create_permission_tables(self, conn: sqlite3.Connection):
        """创建权限相关表"""
        logger.info("正在创建权限相关表")
        
        cursor = conn.cursor()
        
        # 创建权限表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建用户权限关联表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                granted_by INTEGER,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (permission_id) REFERENCES permissions (id),
                FOREIGN KEY (granted_by) REFERENCES users (id),
                UNIQUE (user_id, permission_id)
            )
        """)
        
        # 创建数据访问日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS data_access_logs (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                resource_type TEXT NOT NULL,
                resource_id TEXT,
                action TEXT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                success BOOLEAN DEFAULT 1,
                error_message TEXT,
                additional_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # 创建公司数据范围表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS company_data_scopes (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                company_name TEXT NOT NULL,
                access_type TEXT DEFAULT 'read' CHECK (access_type IN ('read', 'write', 'admin')),
                granted_by INTEGER,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (granted_by) REFERENCES users (id),
                UNIQUE (user_id, company_name)
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_access_logs_user_id ON data_access_logs (user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_data_access_logs_created_at ON data_access_logs (created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions (user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_company_data_scopes_user_id ON company_data_scopes (user_id)")
        
        logger.info("权限相关表创建完成")
    
    def _migrate_user_data(self, conn: sqlite3.Connection):
        """迁移现有用户数据到新的角色系统"""
        logger.info("正在迁移用户数据到新的角色系统")
        
        cursor = conn.cursor()
        
        # 角色映射规则
        role_mappings = {
            'airline': 'airline_buyer',
            'supplier': 'airline_buyer',  # 供应商角色转换为航空公司采购员
            'maintenance': 'maintenance_engineer',
            'admin': 'admin'
        }
        
        # 更新用户角色和权限等级
        for old_role, new_role in role_mappings.items():
            permission_level = 4 if new_role == 'admin' else 1
            company_type = 'internal' if new_role == 'admin' else 'external'
            
            cursor.execute("""
                UPDATE users 
                SET user_type = ?, permission_level = ?, company_type = ?
                WHERE user_type = ?
            """, (new_role, permission_level, company_type, old_role))
            
            updated_count = cursor.rowcount
            if updated_count > 0:
                logger.info(f"更新了 {updated_count} 个用户从 {old_role} 到 {new_role}")
        
        # 为平台员工设置内部员工标识
        cursor.execute("""
            UPDATE users 
            SET company_type = 'internal'
            WHERE user_type IN ('platform_staff', 'admin')
        """)
        
        logger.info("用户数据迁移完成")
    
    def _initialize_permissions(self, conn: sqlite3.Connection):
        """初始化基础权限数据"""
        logger.info("正在初始化基础权限数据")
        
        cursor = conn.cursor()
        
        # 基础权限列表
        permissions = [
            # 库存管理权限
            ('manage_inventory', '管理库存', '管理航材库存信息', 'inventory'),
            ('view_inventory', '查看库存', '查看航材库存信息', 'inventory'),
            ('manage_own_inventory', '管理自有库存', '管理自己公司的库存', 'inventory'),
            
            # 订单管理权限
            ('manage_orders', '管理订单', '管理航材订单', 'orders'),
            ('view_orders', '查看订单', '查看航材订单', 'orders'),
            ('process_all_orders', '处理所有订单', '处理平台所有订单', 'orders'),
            
            # 需求管理权限
            ('publish_demand', '发布需求', '发布航材需求', 'demands'),
            ('view_demands', '查看需求', '查看航材需求', 'demands'),
            
            # 共享件权限
            ('publish_shared', '发布共享件', '发布共享航材', 'shared'),
            ('audit_shared_materials', '审核共享件', '审核共享航材', 'shared'),
            
            # 维修管理权限
            ('update_maintenance_status', '更新维修状态', '更新维修工作状态', 'maintenance'),
            ('technical_support', '技术支持', '提供技术支持服务', 'maintenance'),
            ('manage_work_orders', '管理工单', '管理维修工单', 'maintenance'),
            
            # 物流管理权限
            ('track_shipment', '跟踪货运', '跟踪货物运输状态', 'logistics'),
            ('update_logistics', '更新物流', '更新物流信息', 'logistics'),
            ('manage_delivery', '管理配送', '管理配送服务', 'logistics'),
            
            # 数据访问权限
            ('view_own_data', '查看自有数据', '查看自己的数据', 'data'),
            ('view_all_data', '查看所有数据', '查看平台所有数据', 'data'),
            ('cross_company_access', '跨公司访问', '访问其他公司数据', 'data'),
            
            # 系统管理权限
            ('system_config', '系统配置', '系统参数配置', 'system'),
            ('user_management', '用户管理', '管理平台用户', 'system'),
            ('data_analysis', '数据分析', '查看数据分析报告', 'system'),
            ('all_permissions', '所有权限', '拥有所有系统权限', 'system'),
            
            # 高级功能权限
            ('advanced_features', '高级功能', '使用高级功能', 'advanced'),
            ('priority_support', '优先支持', '享受优先支持服务', 'advanced'),
            ('admin_features', '管理功能', '使用管理功能', 'advanced'),
            ('super_admin', '超级管理员', '超级管理员权限', 'advanced')
        ]
        
        # 插入权限数据
        for code, name, description, category in permissions:
            cursor.execute("""
                INSERT OR IGNORE INTO permissions (code, name, description, category)
                VALUES (?, ?, ?, ?)
            """, (code, name, description, category))
        
        logger.info(f"初始化了 {len(permissions)} 个基础权限")
    
    def _initialize_default_user_permissions(self, conn: sqlite3.Connection):
        """为现有用户初始化默认权限"""
        logger.info("正在为现有用户初始化默认权限")
        
        cursor = conn.cursor()
        
        # 获取所有用户
        cursor.execute("SELECT id, user_type FROM users")
        users = cursor.fetchall()
        
        # 角色默认权限映射
        role_default_permissions = {
            'airline_buyer': [
                'view_inventory', 'manage_own_inventory', 'publish_demand', 
                'manage_orders', 'publish_shared', 'view_own_data'
            ],
            'platform_staff': [
                'view_inventory', 'manage_inventory', 'view_demands', 'process_all_orders',
                'audit_shared_materials', 'view_all_data', 'cross_company_access'
            ],
            'maintenance_engineer': [
                'update_maintenance_status', 'technical_support', 'manage_work_orders',
                'view_inventory', 'view_own_data'
            ],
            'logistics_specialist': [
                'track_shipment', 'update_logistics', 'manage_delivery',
                'view_orders', 'view_own_data'
            ],
            'admin': [
                'all_permissions', 'system_config', 'user_management', 'data_analysis'
            ]
        }
        
        # 为每个用户分配默认权限
        for user_id, user_type in users:
            default_permissions = role_default_permissions.get(user_type, ['view_own_data'])
            
            for permission_code in default_permissions:
                # 获取权限ID
                cursor.execute("SELECT id FROM permissions WHERE code = ?", (permission_code,))
                permission_row = cursor.fetchone()
                
                if permission_row:
                    permission_id = permission_row[0]
                    
                    # 分配权限给用户
                    cursor.execute("""
                        INSERT OR IGNORE INTO user_permissions (user_id, permission_id, granted_by)
                        VALUES (?, ?, 1)
                    """, (user_id, permission_id))
        
        logger.info(f"为 {len(users)} 个用户初始化了默认权限")
    
    def _verify_migration(self, conn: sqlite3.Connection):
        """验证迁移结果"""
        logger.info("正在验证迁移结果")
        
        cursor = conn.cursor()
        
        # 验证表结构
        tables_to_check = ['users', 'permissions', 'user_permissions', 'data_access_logs', 'company_data_scopes']
        for table in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            logger.info(f"表 {table} 包含 {count} 条记录")
        
        # 验证用户字段
        cursor.execute("SELECT COUNT(*) FROM users WHERE company_type IS NOT NULL")
        users_with_company_type = cursor.fetchone()[0]
        logger.info(f"{users_with_company_type} 个用户具有company_type字段")
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE permission_level IS NOT NULL")
        users_with_permission_level = cursor.fetchone()[0]
        logger.info(f"{users_with_permission_level} 个用户具有permission_level字段")
        
        # 验证权限分配
        cursor.execute("SELECT COUNT(DISTINCT user_id) FROM user_permissions")
        users_with_permissions = cursor.fetchone()[0]
        logger.info(f"{users_with_permissions} 个用户已分配权限")
        
        logger.info("迁移结果验证完成")
    
    def _rollback_changes(self):
        """回滚变更"""
        logger.warning("正在回滚变更")
        
        try:
            import shutil
            shutil.copy2(self.backup_path, self.db_path)
            logger.info("数据库已回滚到迁移前状态")
        except Exception as e:
            logger.error(f"回滚失败: {str(e)}")


def main():
    """主函数"""
    migration = Phase1PermissionsMigration()
    
    try:
        # 执行迁移
        migration.run_migration()
        print("\n✅ 第一阶段权限系统迁移成功完成！")
        print(f"📁 数据库备份位于: {migration.backup_path}")
        print("📝 详细日志请查看: phase1_migration.log")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {str(e)}")
        print("📁 数据库已回滚到原始状态")
        print("📝 详细错误日志请查看: phase1_migration.log")


if __name__ == "__main__":
    main()