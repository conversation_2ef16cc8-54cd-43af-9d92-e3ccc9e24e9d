#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - Flask后端应用主入口 (简化版)
版本: 1.0
创建时间: 2025-01-15
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, Blueprint
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import JWTManager, jwt_required, get_jwt_identity
from werkzeug.exceptions import HTTPException

# 导入配置
from config import Config

# 导入数据库实例
from models import db

# 导入认证路由
from routes.auth import auth_bp

def create_app(config_class=Config):
    """
    应用工厂函数 - 创建并配置Flask应用实例
    """
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    
    # 跨域请求支持
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://localhost:5173"],
            "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # JWT认证管理
    jwt = JWTManager(app)
    
    # 注册蓝图路由
    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
    
    # 根路径健康检查
    @app.route('/')
    def health_check():
        """
        健康检查接口
        """
        return jsonify({
            'error': 0,
            'message': '航材共享保障平台API服务正常运行',
            'body': {
                'service': 'Aviation Parts Sharing Platform',
                'version': '1.0',
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            },
            'success': True
        })
    
    # API版本信息
    @app.route('/api/v1')
    def api_info():
        """
        API版本信息接口
        """
        return jsonify({
            'error': 0,
            'message': 'API信息获取成功',
            'body': {
                'version': 'v1.0',
                'description': '航材共享保障平台RESTful API',
                'modules': [
                    'auth - 用户认证',
                    'materials - 航材管理',
                    'orders - 订单管理',
                    'inventory - 库存管理',
                    'demands - 需求管理',
                    'notifications - 通知管理'
                ]
            },
            'success': True
        })
    
    # 简化的材料搜索接口(无认证版本，用于测试)
    @app.route('/api/v1/materials/search', methods=['GET'])
    def search_materials():
        """
        简化的航材搜索接口
        """
        try:
            from models import Material
            from sqlalchemy import or_
            
            q = request.args.get('q', '')
            page = request.args.get('page', 1, type=int)
            size = request.args.get('size', 20, type=int)
            
            # 构建查询
            query = Material.query
            if q:
                query = query.filter(or_(
                    Material.part_name.ilike(f'%{q}%'),
                    Material.part_number.ilike(f'%{q}%'),
                    Material.description.ilike(f'%{q}%')
                ))
            
            # 分页
            pagination = query.paginate(page=page, per_page=size, error_out=False)
            
            materials = []
            for material in pagination.items:
                materials.append({
                    'id': material.id,
                    'part_number': material.part_number,
                    'part_name': material.part_name,
                    'category': material.category,
                    'manufacturer': material.manufacturer,
                    'aircraft_type': material.aircraft_type,
                    'description': material.description,
                    'created_at': material.created_at.isoformat() if material.created_at else None
                })
            
            return jsonify({
                'error': 0,
                'message': '搜索成功',
                'body': {
                    'items': materials,
                    'pagination': {
                        'page': page,
                        'size': size,
                        'total': pagination.total,
                        'pages': pagination.pages,
                        'has_prev': pagination.has_prev,
                        'has_next': pagination.has_next
                    }
                },
                'success': True
            })
            
        except Exception as e:
            return jsonify({
                'error': 500,
                'message': f'搜索失败: {str(e)}',
                'body': {},
                'success': False
            }), 500

    # 添加一个创建航材的测试接口
    @app.route('/api/v1/materials', methods=['POST'])
    def create_material():
        """
        创建航材接口(无认证版本，用于测试)
        """
        try:
            from models import Material
            
            if not request.is_json:
                return jsonify({
                    'error': 400,
                    'message': '请求必须是JSON格式',
                    'body': {},
                    'success': False
                }), 400
            
            data = request.get_json()
            
            # 验证必填字段
            required_fields = ['part_number', 'part_name', 'category']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'error': 400,
                        'message': f'缺少必填字段: {field}',
                        'body': {},
                        'success': False
                    }), 400
            
            # 检查件号是否已存在
            existing_material = Material.query.filter_by(part_number=data['part_number']).first()
            if existing_material:
                return jsonify({
                    'error': 409,
                    'message': '件号已存在',
                    'body': {},
                    'success': False
                }), 409
            
            # 创建航材
            material = Material(
                part_number=data['part_number'].strip(),
                part_name=data['part_name'].strip(),
                category=data['category'].strip(),
                manufacturer=data.get('manufacturer', '').strip(),
                aircraft_type=data.get('aircraft_type', '').strip(),
                description=data.get('description', '').strip(),
                status='active'
            )
            
            db.session.add(material)
            db.session.commit()
            
            return jsonify({
                'error': 0,
                'message': '航材创建成功',
                'body': {
                    'id': material.id,
                    'part_number': material.part_number,
                    'part_name': material.part_name,
                    'category': material.category,
                    'manufacturer': material.manufacturer,
                    'aircraft_type': material.aircraft_type,
                    'description': material.description,
                    'created_at': material.created_at.isoformat() if material.created_at else None
                },
                'success': True
            }), 201
            
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'error': 500,
                'message': f'创建航材失败: {str(e)}',
                'body': {},
                'success': False
            }), 500
    
    # 全局错误处理
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """
        HTTP异常处理器
        """
        return jsonify({
            'error': error.code,
            'message': error.description,
            'body': {},
            'success': False
        }), error.code
    
    @app.errorhandler(Exception)
    def handle_general_exception(error):
        """
        通用异常处理器
        """
        return jsonify({
            'error': 500,
            'message': '服务器内部错误',
            'body': {},
            'success': False
        }), 500
    
    # JWT错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        """
        JWT令牌过期处理
        """
        return jsonify({
            'error': 401,
            'message': '访问令牌已过期，请重新登录',
            'body': {},
            'success': False
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        """
        JWT令牌无效处理
        """
        return jsonify({
            'error': 401,
            'message': '无效的访问令牌',
            'body': {},
            'success': False
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        """
        JWT令牌缺失处理
        """
        return jsonify({
            'error': 401,
            'message': '访问此资源需要提供访问令牌',
            'body': {},
            'success': False
        }), 401
    
    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    """
    应用启动入口
    """
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 初始化基础数据
        try:
            from utils.init_data import init_database
            init_database()
        except ImportError:
            print("Warning: 初始化数据脚本未找到，跳过数据初始化")
    
    # 启动开发服务器
    print("正在启动航材共享保障平台后端服务...")
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=True,
        threaded=True
    )