#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 订单业务逻辑服务
版本: 1.0
创建时间: 2025-01-16

处理订单相关的业务逻辑，包括状态管理、审批流程、通知等
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional, Tuple

from models import (
    db, Order, OrderItem, OrderStatusHistory, OrderApproval, OrderAttachment,
    Material, User, InventoryItem, Notification
)
from utils.validators import generate_order_number


class OrderService:
    """订单业务逻辑服务"""
    
    @staticmethod
    def create_order(buyer_id: int, order_data: Dict) -> Tuple[Order, str]:
        """
        创建订单
        
        Args:
            buyer_id: 买方用户ID
            order_data: 订单数据
            
        Returns:
            (Order, message): 创建的订单对象和消息
        """
        try:
            # 验证供应商
            supplier = User.query.get(order_data['supplier_id'])
            if not supplier:
                raise ValueError('供应商不存在')
            
            # 创建订单
            order = Order(
                order_number=generate_order_number(),
                buyer_id=buyer_id,
                supplier_id=order_data['supplier_id'],
                priority=order_data.get('priority', 'normal'),
                delivery_address=order_data['delivery_address'],
                delivery_date=datetime.fromisoformat(order_data['delivery_date']) if order_data.get('delivery_date') else None,
                notes=order_data.get('notes', ''),
                status='pending'
            )
            
            db.session.add(order)
            db.session.flush()  # 获取订单ID
            
            # 创建订单项目
            total_amount = Decimal('0')
            for item_data in order_data['items']:
                # 验证航材
                material = Material.query.get(item_data['material_id'])
                if not material:
                    raise ValueError(f'航材不存在: {item_data["material_id"]}')
                
                # 检查库存
                inventory_item = InventoryItem.query.filter_by(
                    material_id=item_data['material_id'],
                    supplier_id=order_data['supplier_id']
                ).first()
                
                if inventory_item and inventory_item.current_stock < item_data['quantity']:
                    raise ValueError(f'库存不足: {material.part_name}，当前库存: {inventory_item.current_stock}')
                
                # 创建订单项目
                order_item = OrderItem(
                    order_id=order.id,
                    material_id=item_data['material_id'],
                    quantity=item_data['quantity'],
                    unit_price=Decimal(str(item_data['unit_price'])),
                    condition_code=item_data.get('condition_code', 'NE'),
                    delivery_requirement=item_data.get('delivery_requirement', '')
                )
                
                # 计算小计
                order_item.calculate_subtotal()
                total_amount += order_item.subtotal
                
                db.session.add(order_item)
            
            # 更新订单总金额
            order.total_amount = total_amount
            
            # 记录初始状态
            history = OrderStatusHistory(
                order_id=order.id,
                from_status=None,
                to_status='pending',
                operator_id=buyer_id,
                reason='订单创建',
                notes='订单初始创建'
            )
            db.session.add(history)
            
            # 如果需要审批，创建审批记录
            if order.requires_approval():
                OrderService._create_approval_records(order)
            
            db.session.commit()
            
            # 发送通知
            OrderService._send_order_notification(order, 'created')
            
            return order, '订单创建成功'
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def update_order_status(order_id: int, new_status: str, operator_id: int, 
                          reason: str = None, notes: str = None) -> Tuple[bool, str]:
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            new_status: 新状态
            operator_id: 操作人ID
            reason: 状态变更原因
            notes: 备注
            
        Returns:
            (success, message): 是否成功和消息
        """
        try:
            order = Order.query.get(order_id)
            if not order:
                return False, '订单不存在'
            
            # 检查状态转换是否合法
            if not order.can_transition_to(new_status):
                return False, f'不能从状态 {order.status} 转换到 {new_status}'
            
            # 检查审批状态
            if new_status == 'confirmed' and order.requires_approval():
                approval_status = order.get_approval_status()
                if approval_status != 'approved':
                    return False, f'订单需要审批通过才能确认，当前审批状态: {approval_status}'
            
            # 更新状态
            old_status = order.status
            order.update_status(new_status, operator_id, reason, notes)
            
            # 处理状态变更的业务逻辑
            if new_status == 'confirmed':
                OrderService._handle_order_confirmed(order)
            elif new_status == 'processing':
                OrderService._handle_order_processing(order)
            elif new_status == 'shipping':
                OrderService._handle_order_shipping(order)
            elif new_status == 'completed':
                OrderService._handle_order_completed(order)
            elif new_status == 'cancelled':
                OrderService._handle_order_cancelled(order)
            
            db.session.commit()
            
            # 发送通知
            OrderService._send_order_notification(order, 'status_updated', {
                'old_status': old_status,
                'new_status': new_status
            })
            
            return True, f'订单状态已更新为: {new_status}'
            
        except Exception as e:
            db.session.rollback()
            return False, f'更新订单状态失败: {str(e)}'
    
    @staticmethod
    def approve_order(order_id: int, approver_id: int, approval_level: int, 
                     action: str, comments: str = None) -> Tuple[bool, str]:
        """
        审批订单
        
        Args:
            order_id: 订单ID
            approver_id: 审批人ID
            approval_level: 审批级别
            action: 审批动作 ('approve' 或 'reject')
            comments: 审批意见
            
        Returns:
            (success, message): 是否成功和消息
        """
        try:
            order = Order.query.get(order_id)
            if not order:
                return False, '订单不存在'
            
            # 查找审批记录
            approval = OrderApproval.query.filter_by(
                order_id=order_id,
                approver_id=approver_id,
                approval_level=approval_level
            ).first()
            
            if not approval:
                return False, '审批记录不存在'
            
            if approval.status != 'pending':
                return False, f'审批已处理，当前状态: {approval.status}'
            
            # 更新审批状态
            approval.status = 'approved' if action == 'approve' else 'rejected'
            approval.comments = comments
            approval.approved_at = datetime.utcnow()
            
            db.session.commit()
            
            # 发送通知
            OrderService._send_order_notification(order, 'approval_updated', {
                'approval_level': approval_level,
                'action': action,
                'comments': comments
            })
            
            return True, f'审批{action}成功'
            
        except Exception as e:
            db.session.rollback()
            return False, f'审批失败: {str(e)}'
    
    @staticmethod
    def _create_approval_records(order: Order):
        """创建审批记录"""
        required_levels = order._get_required_approval_levels()
        
        for level in required_levels:
            # 根据级别找到审批人（这里简化处理，实际应该有更复杂的逻辑）
            approver = OrderService._get_approver_by_level(level, order.buyer_id)
            if approver:
                approval = OrderApproval(
                    order_id=order.id,
                    approver_id=approver.id,
                    approval_level=level,
                    status='pending'
                )
                db.session.add(approval)
    
    @staticmethod
    def _get_approver_by_level(level: int, buyer_id: int) -> Optional[User]:
        """根据级别获取审批人"""
        # 这里简化处理，实际应该根据组织架构来确定
        # 暂时返回第一个管理员用户
        return User.query.filter_by(user_type='admin').first()
    
    @staticmethod
    def _handle_order_confirmed(order: Order):
        """处理订单确认"""
        # 预留库存
        for item in order.order_items:
            inventory_item = InventoryItem.query.filter_by(
                material_id=item.material_id,
                supplier_id=order.supplier_id
            ).first()
            
            if inventory_item:
                # 这里可以添加预留库存的逻辑
                pass
    
    @staticmethod
    def _handle_order_processing(order: Order):
        """处理订单处理中"""
        # 扣减库存
        for item in order.order_items:
            inventory_item = InventoryItem.query.filter_by(
                material_id=item.material_id,
                supplier_id=order.supplier_id
            ).first()
            
            if inventory_item:
                inventory_item.current_stock -= item.quantity
                inventory_item.update_status()
    
    @staticmethod
    def _handle_order_shipping(order: Order):
        """处理订单发货"""
        # 创建物流信息等
        pass
    
    @staticmethod
    def _handle_order_completed(order: Order):
        """处理订单完成"""
        # 更新统计数据等
        pass
    
    @staticmethod
    def _handle_order_cancelled(order: Order):
        """处理订单取消"""
        # 释放预留库存等
        pass
    
    @staticmethod
    def _send_order_notification(order: Order, event_type: str, extra_data: Dict = None):
        """发送订单通知"""
        # 根据事件类型发送不同的通知
        if event_type == 'created':
            # 通知供应商
            notification = Notification(
                user_id=order.supplier_id,
                type='order',
                title='新订单通知',
                content=f'您收到一个新订单 {order.order_number}，请及时处理。',
                priority='normal',
                related_id=order.id,
                related_type='order'
            )
            db.session.add(notification)
        
        elif event_type == 'status_updated':
            # 通知买方和供应商
            for user_id in [order.buyer_id, order.supplier_id]:
                notification = Notification(
                    user_id=user_id,
                    type='order',
                    title='订单状态更新',
                    content=f'订单 {order.order_number} 状态已更新为: {extra_data["new_status"]}',
                    priority='normal',
                    related_id=order.id,
                    related_type='order'
                )
                db.session.add(notification)
        
        elif event_type == 'approval_updated':
            # 通知订单创建者
            notification = Notification(
                user_id=order.buyer_id,
                type='order',
                title='订单审批更新',
                content=f'订单 {order.order_number} 审批状态已更新',
                priority='normal',
                related_id=order.id,
                related_type='order'
            )
            db.session.add(notification)
        
        db.session.commit()
