#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 角色管理服务
版本: 1.0
创建时间: 2025-07-24

提供角色和权限的动态管理功能
"""

from datetime import datetime
from sqlalchemy import text
from models import db
from typing import List, Dict, Optional, Tuple
from utils.permission_cache import get_permission_cache
import logging

logger = logging.getLogger(__name__)

# 获取权限缓存实例
permission_cache = get_permission_cache()

class RoleService:
    """角色管理服务类"""
    
    @staticmethod
    def get_all_roles(include_inactive: bool = False) -> List[Dict]:
        """
        获取所有角色
        
        Args:
            include_inactive: 是否包含非活跃角色
            
        Returns:
            角色列表
        """
        try:
            sql = """
            SELECT id, role_code, role_name, display_name, description, 
                   category, business_type, is_active, sort_order, 
                   icon_name, theme_color, created_at, updated_at
            FROM system_roles
            """
            
            if not include_inactive:
                sql += " WHERE is_active = true"
                
            sql += " ORDER BY sort_order, id"
            
            result = db.session.execute(text(sql)).fetchall()
            
            roles = []
            for row in result:
                roles.append({
                    'id': row[0],
                    'role_code': row[1],
                    'role_name': row[2],
                    'display_name': row[3],
                    'description': row[4],
                    'category': row[5],
                    'business_type': row[6],
                    'is_active': row[7],
                    'sort_order': row[8],
                    'icon_name': row[9],
                    'theme_color': row[10],
                    'created_at': row[11].isoformat() if row[11] else None,
                    'updated_at': row[12].isoformat() if row[12] else None
                })
            
            logger.info(f"获取到 {len(roles)} 个角色")
            return roles
            
        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            raise
    
    @staticmethod
    def get_role_by_id(role_id: int) -> Optional[Dict]:
        """
        根据ID获取角色信息
        
        Args:
            role_id: 角色ID
            
        Returns:
            角色信息或None
        """
        try:
            sql = """
            SELECT id, role_code, role_name, display_name, description,
                   category, business_type, is_active, sort_order,
                   icon_name, theme_color, created_at, updated_at
            FROM system_roles
            WHERE id = :role_id
            """
            
            result = db.session.execute(text(sql), {'role_id': role_id}).fetchone()
            
            if not result:
                return None
                
            return {
                'id': result[0],
                'role_code': result[1],
                'role_name': result[2], 
                'display_name': result[3],
                'description': result[4],
                'category': result[5],
                'business_type': result[6],
                'is_active': result[7],
                'sort_order': result[8],
                'icon_name': result[9],
                'theme_color': result[10],
                'created_at': result[11].isoformat() if result[11] else None,
                'updated_at': result[12].isoformat() if result[12] else None
            }
            
        except Exception as e:
            logger.error(f"获取角色信息失败 (ID: {role_id}): {e}")
            raise
    
    @staticmethod
    def get_role_by_code(role_code: str) -> Optional[Dict]:
        """
        根据角色编码获取角色信息
        
        Args:
            role_code: 角色编码
            
        Returns:
            角色信息或None
        """
        try:
            sql = """
            SELECT id, role_code, role_name, display_name, description,
                   category, business_type, is_active, sort_order,
                   icon_name, theme_color, created_at, updated_at
            FROM system_roles
            WHERE role_code = :role_code
            """
            
            result = db.session.execute(text(sql), {'role_code': role_code}).fetchone()
            
            if not result:
                return None
                
            return {
                'id': result[0],
                'role_code': result[1],
                'role_name': result[2],
                'display_name': result[3],
                'description': result[4],
                'category': result[5],
                'business_type': result[6],
                'is_active': result[7],
                'sort_order': result[8],
                'icon_name': result[9],
                'theme_color': result[10],
                'created_at': result[11].isoformat() if result[11] else None,
                'updated_at': result[12].isoformat() if result[12] else None
            }
            
        except Exception as e:
            logger.error(f"获取角色信息失败 (Code: {role_code}): {e}")
            raise
    
    @staticmethod
    def create_role(role_data: Dict) -> Dict:
        """
        创建新角色
        
        Args:
            role_data: 角色数据
            
        Returns:
            创建的角色信息
        """
        try:
            # 验证必填字段
            required_fields = ['role_code', 'role_name', 'display_name', 'category', 'business_type']
            for field in required_fields:
                if field not in role_data or not role_data[field]:
                    raise ValueError(f"缺少必填字段: {field}")
            
            # 检查角色编码是否已存在
            existing_role = RoleService.get_role_by_code(role_data['role_code'])
            if existing_role:
                raise ValueError(f"角色编码已存在: {role_data['role_code']}")
            
            # 插入新角色
            sql = """
            INSERT INTO system_roles (
                role_code, role_name, display_name, description, category,
                business_type, sort_order, icon_name, theme_color, is_active
            ) VALUES (
                :role_code, :role_name, :display_name, :description, :category,
                :business_type, :sort_order, :icon_name, :theme_color, :is_active
            ) RETURNING id
            """
            
            # 设置默认值
            insert_data = {
                'role_code': role_data['role_code'],
                'role_name': role_data['role_name'],
                'display_name': role_data['display_name'],
                'description': role_data.get('description', ''),
                'category': role_data['category'],
                'business_type': role_data['business_type'],
                'sort_order': role_data.get('sort_order', 999),
                'icon_name': role_data.get('icon_name', 'User'),
                'theme_color': role_data.get('theme_color', '#6B7280'),
                'is_active': role_data.get('is_active', True)
            }
            
            result = db.session.execute(text(sql), insert_data)
            role_id = result.fetchone()[0]
            db.session.commit()
            
            logger.info(f"创建角色成功: {role_data['role_code']} (ID: {role_id})")
            
            # 返回创建的角色信息
            return RoleService.get_role_by_id(role_id)
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建角色失败: {e}")
            raise
    
    @staticmethod
    def update_role(role_id: int, role_data: Dict) -> Dict:
        """
        更新角色信息
        
        Args:
            role_id: 角色ID
            role_data: 更新的角色数据
            
        Returns:
            更新后的角色信息
        """
        try:
            # 检查角色是否存在
            existing_role = RoleService.get_role_by_id(role_id)
            if not existing_role:
                raise ValueError(f"角色不存在: {role_id}")
            
            # 如果更新角色编码，检查是否冲突
            if 'role_code' in role_data and role_data['role_code'] != existing_role['role_code']:
                code_conflict = RoleService.get_role_by_code(role_data['role_code'])
                if code_conflict:
                    raise ValueError(f"角色编码已存在: {role_data['role_code']}")
            
            # 构建更新SQL
            update_fields = []
            update_values = {'role_id': role_id}
            
            updatable_fields = [
                'role_code', 'role_name', 'display_name', 'description',
                'category', 'business_type', 'sort_order', 'icon_name', 
                'theme_color', 'is_active'
            ]
            
            for field in updatable_fields:
                if field in role_data:
                    update_fields.append(f"{field} = :{field}")
                    update_values[field] = role_data[field]
            
            if not update_fields:
                logger.warning("没有要更新的字段")
                return existing_role
            
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            
            sql = f"""
            UPDATE system_roles 
            SET {', '.join(update_fields)}
            WHERE id = :role_id
            """
            
            db.session.execute(text(sql), update_values)
            db.session.commit()
            
            logger.info(f"更新角色成功: {role_id}")
            
            # 返回更新后的角色信息
            return RoleService.get_role_by_id(role_id)
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新角色失败: {e}")
            raise
    
    @staticmethod
    def delete_role(role_id: int) -> bool:
        """
        删除角色
        
        Args:
            role_id: 角色ID
            
        Returns:
            是否删除成功
        """
        try:
            # 检查角色是否存在
            existing_role = RoleService.get_role_by_id(role_id)
            if not existing_role:
                raise ValueError(f"角色不存在: {role_id}")
            
            # 检查是否有用户使用该角色
            user_count_sql = "SELECT COUNT(*) FROM users WHERE role_id = :role_id"
            user_count = db.session.execute(text(user_count_sql), {'role_id': role_id}).fetchone()[0]
            
            if user_count > 0:
                raise ValueError(f"无法删除角色，仍有 {user_count} 个用户使用该角色")
            
            # 删除角色权限关联
            delete_permissions_sql = "DELETE FROM role_permissions WHERE role_id = :role_id"
            db.session.execute(text(delete_permissions_sql), {'role_id': role_id})
            
            # 删除角色
            delete_role_sql = "DELETE FROM system_roles WHERE id = :role_id"
            db.session.execute(text(delete_role_sql), {'role_id': role_id})
            
            db.session.commit()
            
            logger.info(f"删除角色成功: {role_id}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除角色失败: {e}")
            raise
    
    @staticmethod
    def get_role_permissions(role_id: int) -> List[Dict]:
        """
        获取角色的权限列表
        
        Args:
            role_id: 角色ID
            
        Returns:
            权限列表
        """
        try:
            sql = """
            SELECT sp.id, sp.permission_code, sp.permission_name, 
                   sp.description, sp.category, sp.module,
                   rp.granted_at, rp.granted_by
            FROM system_permissions sp
            JOIN role_permissions rp ON sp.id = rp.permission_id
            WHERE rp.role_id = :role_id AND sp.is_active = true
            ORDER BY sp.category, sp.module, sp.permission_code
            """
            
            result = db.session.execute(text(sql), {'role_id': role_id}).fetchall()
            
            permissions = []
            for row in result:
                permissions.append({
                    'id': row[0],
                    'permission_code': row[1],
                    'permission_name': row[2],
                    'description': row[3],
                    'category': row[4],
                    'module': row[5],
                    'granted_at': row[6].isoformat() if row[6] else None,
                    'granted_by': row[7]
                })
            
            logger.info(f"获取角色权限成功: role_id={role_id}, permissions={len(permissions)}")
            return permissions
            
        except Exception as e:
            logger.error(f"获取角色权限失败: {e}")
            raise
    
    @staticmethod
    def assign_permissions(role_id: int, permission_ids: List[int], granted_by: int = None) -> bool:
        """
        为角色分配权限
        
        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表
            granted_by: 授权人ID
            
        Returns:
            是否分配成功
        """
        try:
            # 检查角色是否存在
            role = RoleService.get_role_by_id(role_id)
            if not role:
                raise ValueError(f"角色不存在: {role_id}")
            
            # 清除现有权限
            delete_sql = "DELETE FROM role_permissions WHERE role_id = :role_id"
            db.session.execute(text(delete_sql), {'role_id': role_id})
            
            # 分配新权限
            for permission_id in permission_ids:
                # 检查权限是否存在
                perm_check_sql = "SELECT id FROM system_permissions WHERE id = :permission_id"
                perm_exists = db.session.execute(text(perm_check_sql), {'permission_id': permission_id}).fetchone()
                
                if not perm_exists:
                    logger.warning(f"权限不存在，跳过: {permission_id}")
                    continue
                
                # 插入权限关联
                insert_sql = """
                INSERT INTO role_permissions (role_id, permission_id, granted_by, granted_at)
                VALUES (:role_id, :permission_id, :granted_by, CURRENT_TIMESTAMP)
                """
                
                db.session.execute(text(insert_sql), {
                    'role_id': role_id,
                    'permission_id': permission_id,
                    'granted_by': granted_by
                })
            
            db.session.commit()
            
            logger.info(f"分配权限成功: role_id={role_id}, permissions={len(permission_ids)}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"分配权限失败: {e}")
            raise
    
    @staticmethod
    def get_user_permissions(user_id: int) -> List[str]:
        """
        获取用户的权限代码列表（带缓存优化）
        
        Args:
            user_id: 用户ID
            
        Returns:
            权限代码列表
        """
        try:
            # 尝试从缓存获取
            cached_permissions = permission_cache.get_user_permissions(user_id)
            if cached_permissions is not None:
                return cached_permissions
            
            # 缓存未命中，从数据库查询
            sql = """
            SELECT DISTINCT sp.permission_code
            FROM users u
            JOIN system_roles sr ON u.role_id = sr.id
            JOIN role_permissions rp ON sr.id = rp.role_id
            JOIN system_permissions sp ON rp.permission_id = sp.id
            WHERE u.id = :user_id AND sr.is_active = true AND sp.is_active = true
            """
            
            result = db.session.execute(text(sql), {'user_id': user_id}).fetchall()
            permissions = [row[0] for row in result]
            
            # 设置缓存
            permission_cache.set_user_permissions(user_id, permissions)
            
            logger.debug(f"获取用户权限: user_id={user_id}, permissions={len(permissions)}")
            return permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            raise
    
    @staticmethod
    def check_user_permission(user_id: int, permission_code: str) -> bool:
        """
        检查用户是否具有特定权限
        
        Args:
            user_id: 用户ID
            permission_code: 权限代码
            
        Returns:
            是否具有权限
        """
        try:
            # 获取用户权限
            user_permissions = RoleService.get_user_permissions(user_id)
            
            # 检查是否有all_permissions或特定权限
            has_permission = (
                'all_permissions' in user_permissions or 
                permission_code in user_permissions
            )
            
            logger.debug(f"权限检查: user_id={user_id}, permission={permission_code}, result={has_permission}")
            return has_permission
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False