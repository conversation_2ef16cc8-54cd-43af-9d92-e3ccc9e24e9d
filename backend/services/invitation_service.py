#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 邀请码管理服务
版本: 1.0
创建时间: 2025-07-24

提供邀请码生成、验证和管理功能
"""

import random
import string
import secrets
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import text
from models import db
from typing import List, Dict, Optional, Tuple
import logging
import ipaddress
import re

logger = logging.getLogger(__name__)

class InvitationService:
    """邀请码管理服务类"""
    
    @staticmethod
    def generate_invitation_code(code_type: str = 'staff') -> str:
        """
        生成邀请码
        格式: STAFF-YYYY-XXXX-XXXX 或 ADMIN-YYYY-XXXX-XXXX
        
        Args:
            code_type: 邀请码类型 ('staff' 或 'admin')
            
        Returns:
            邀请码字符串
        """
        try:
            # 验证类型
            if code_type not in ['staff', 'admin']:
                raise ValueError(f"无效的邀请码类型: {code_type}")
            
            # 获取当前年份
            current_year = datetime.now().year
            
            # 生成两个4位随机字符串
            def generate_segment():
                # 使用大写字母和数字，排除容易混淆的字符
                chars = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'  # 排除 I, L, O, 0, 1
                return ''.join(secrets.choice(chars) for _ in range(4))
            
            segment1 = generate_segment()
            segment2 = generate_segment()
            
            # 组装邀请码
            prefix = code_type.upper()
            invitation_code = f"{prefix}-{current_year}-{segment1}-{segment2}"
            
            # 检查是否已存在（理论上冲突概率极低）
            max_attempts = 10
            attempts = 0
            
            while attempts < max_attempts:
                # 检查数据库中是否已存在
                check_sql = "SELECT id FROM invitation_codes WHERE code = :code"
                existing = db.session.execute(text(check_sql), {'code': invitation_code}).fetchone()
                
                if not existing:
                    logger.info(f"生成邀请码成功: {invitation_code}")
                    return invitation_code
                
                # 如果存在冲突，重新生成
                attempts += 1
                segment1 = generate_segment()
                segment2 = generate_segment()
                invitation_code = f"{prefix}-{current_year}-{segment1}-{segment2}"
                
                logger.warning(f"邀请码冲突，重新生成: 尝试 {attempts}/{max_attempts}")
            
            raise Exception("生成邀请码失败：达到最大重试次数")
            
        except Exception as e:
            logger.error(f"生成邀请码失败: {e}")
            raise
    
    @staticmethod
    def create_invitation_code(
        code_type: str,
        allowed_roles: List[str],
        created_by: int,
        expires_in_days: int = 30,
        max_uses: int = 1,
        ip_whitelist: List[str] = None
    ) -> Dict:
        """
        创建邀请码记录
        
        Args:
            code_type: 邀请码类型
            allowed_roles: 允许注册的角色列表
            created_by: 创建者用户ID
            expires_in_days: 有效期天数
            max_uses: 最大使用次数
            ip_whitelist: IP白名单
            
        Returns:
            创建的邀请码信息
        """
        try:
            # 生成邀请码
            code = InvitationService.generate_invitation_code(code_type)
            
            # 计算过期时间
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
            
            # 验证IP白名单格式
            validated_ips = []
            if ip_whitelist:
                for ip in ip_whitelist:
                    try:
                        # 验证IP地址或CIDR格式
                        ipaddress.ip_network(ip, strict=False)
                        validated_ips.append(ip)
                    except ValueError:
                        logger.warning(f"无效的IP地址格式，跳过: {ip}")
            
            # 插入数据库
            insert_sql = """
            INSERT INTO invitation_codes (
                code, code_type, allowed_roles, created_by, expires_at,
                max_uses, used_count, ip_whitelist, status
            ) VALUES (
                :code, :code_type, :allowed_roles, :created_by, :expires_at,
                :max_uses, 0, :ip_whitelist, 'active'
            ) RETURNING id
            """
            
            result = db.session.execute(text(insert_sql), {
                'code': code,
                'code_type': code_type,
                'allowed_roles': allowed_roles,
                'created_by': created_by,
                'expires_at': expires_at,
                'max_uses': max_uses,
                'ip_whitelist': validated_ips
            })
            
            invitation_id = result.fetchone()[0]
            db.session.commit()
            
            logger.info(f"创建邀请码成功: {code} (ID: {invitation_id})")
            
            # 返回邀请码信息
            return {
                'id': invitation_id,
                'code': code,
                'code_type': code_type,
                'allowed_roles': allowed_roles,
                'expires_at': expires_at.isoformat(),
                'max_uses': max_uses,
                'used_count': 0,
                'ip_whitelist': validated_ips,
                'status': 'active',
                'created_by': created_by
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建邀请码失败: {e}")
            raise
    
    @staticmethod
    def validate_invitation_code(
        code: str,
        ip_address: str = None,
        user_agent: str = None
    ) -> Tuple[bool, str, Dict]:
        """
        验证邀请码
        
        Args:
            code: 邀请码
            ip_address: 客户端IP地址
            user_agent: 用户代理字符串
            
        Returns:
            (是否有效, 错误信息, 邀请码信息)
        """
        try:
            # 格式验证
            if not InvitationService._validate_code_format(code):
                return False, "邀请码格式无效", {}
            
            # 查询数据库
            query_sql = """
            SELECT id, code_type, allowed_roles, created_by, expires_at,
                   max_uses, used_count, ip_whitelist, status, created_at
            FROM invitation_codes
            WHERE code = :code
            """
            
            result = db.session.execute(text(query_sql), {'code': code}).fetchone()
            
            if not result:
                logger.warning(f"邀请码不存在: {code}")
                return False, "邀请码不存在", {}
            
            invitation_info = {
                'id': result[0],
                'code_type': result[1],
                'allowed_roles': result[2],
                'created_by': result[3],
                'expires_at': result[4],
                'max_uses': result[5],
                'used_count': result[6],
                'ip_whitelist': result[7],
                'status': result[8],
                'created_at': result[9]
            }
            
            # 状态检查
            if invitation_info['status'] != 'active':
                logger.warning(f"邀请码状态无效: {code}, status={invitation_info['status']}")
                return False, f"邀请码已{invitation_info['status']}", invitation_info
            
            # 过期检查
            if invitation_info['expires_at'] < datetime.utcnow():
                # 更新状态为过期
                InvitationService._update_invitation_status(invitation_info['id'], 'expired')
                logger.warning(f"邀请码已过期: {code}")
                return False, "邀请码已过期", invitation_info
            
            # 使用次数检查
            if invitation_info['used_count'] >= invitation_info['max_uses']:
                logger.warning(f"邀请码使用次数已达上限: {code}")
                return False, "邀请码使用次数已达上限", invitation_info
            
            # IP白名单检查
            if invitation_info['ip_whitelist'] and ip_address:
                if not InvitationService._check_ip_whitelist(ip_address, invitation_info['ip_whitelist']):
                    logger.warning(f"IP地址不在白名单中: {code}, ip={ip_address}")
                    return False, "IP地址不被允许", invitation_info
            
            logger.info(f"邀请码验证成功: {code}")
            return True, "", invitation_info
            
        except Exception as e:
            logger.error(f"邀请码验证失败: {e}")
            return False, f"验证失败: {str(e)}", {}
    
    @staticmethod
    def use_invitation_code(
        invitation_id: int,
        user_id: int,
        ip_address: str = None,
        user_agent: str = None
    ) -> bool:
        """
        使用邀请码（记录使用历史）
        
        Args:
            invitation_id: 邀请码ID
            user_id: 使用者用户ID
            ip_address: 客户端IP地址
            user_agent: 用户代理字符串
            
        Returns:
            是否使用成功
        """
        try:
            # 更新使用计数
            update_sql = """
            UPDATE invitation_codes 
            SET used_count = used_count + 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = :invitation_id
            """
            
            db.session.execute(text(update_sql), {'invitation_id': invitation_id})
            
            # 记录使用历史
            insert_usage_sql = """
            INSERT INTO invitation_code_uses (
                invitation_code_id, user_id, ip_address, user_agent, used_at
            ) VALUES (
                :invitation_code_id, :user_id, :ip_address, :user_agent, CURRENT_TIMESTAMP
            )
            """
            
            db.session.execute(text(insert_usage_sql), {
                'invitation_code_id': invitation_id,
                'user_id': user_id,
                'ip_address': ip_address,
                'user_agent': user_agent
            })
            
            db.session.commit()
            
            logger.info(f"邀请码使用记录成功: invitation_id={invitation_id}, user_id={user_id}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"邀请码使用记录失败: {e}")
            raise
    
    @staticmethod
    def get_invitation_codes(
        created_by: int = None,
        status: str = None,
        page: int = 1,
        size: int = 20
    ) -> Dict:
        """
        获取邀请码列表
        
        Args:
            created_by: 创建者ID筛选
            status: 状态筛选
            page: 页码
            size: 每页数量
            
        Returns:
            邀请码列表和分页信息
        """
        try:
            # 构建查询条件
            where_conditions = []
            params = {}
            
            if created_by:
                where_conditions.append("created_by = :created_by")
                params['created_by'] = created_by
            
            if status:
                where_conditions.append("status = :status")
                params['status'] = status
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) FROM invitation_codes {where_clause}"
            total = db.session.execute(text(count_sql), params).fetchone()[0]
            
            # 查询数据
            offset = (page - 1) * size
            params['limit'] = size
            params['offset'] = offset
            
            query_sql = f"""
            SELECT id, code, code_type, allowed_roles, created_by, expires_at,
                   max_uses, used_count, ip_whitelist, status, created_at, updated_at
            FROM invitation_codes
            {where_clause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
            """
            
            result = db.session.execute(text(query_sql), params).fetchall()
            
            invitation_codes = []
            for row in result:
                invitation_codes.append({
                    'id': row[0],
                    'code': row[1],
                    'code_type': row[2],
                    'allowed_roles': row[3],
                    'created_by': row[4],
                    'expires_at': row[5].isoformat() if row[5] else None,
                    'max_uses': row[6],
                    'used_count': row[7],
                    'ip_whitelist': row[8],
                    'status': row[9],
                    'created_at': row[10].isoformat() if row[10] else None,
                    'updated_at': row[11].isoformat() if row[11] else None
                })
            
            pages = (total + size - 1) // size
            
            return {
                'invitation_codes': invitation_codes,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': pages,
                    'has_prev': page > 1,
                    'has_next': page < pages
                }
            }
            
        except Exception as e:
            logger.error(f"获取邀请码列表失败: {e}")
            raise
    
    @staticmethod
    def disable_invitation_code(invitation_id: int) -> bool:
        """
        禁用邀请码
        
        Args:
            invitation_id: 邀请码ID
            
        Returns:
            是否禁用成功
        """
        try:
            return InvitationService._update_invitation_status(invitation_id, 'disabled')
        except Exception as e:
            logger.error(f"禁用邀请码失败: {e}")
            raise
    
    @staticmethod
    def _validate_code_format(code: str) -> bool:
        """
        验证邀请码格式
        
        Args:
            code: 邀请码
            
        Returns:
            格式是否正确
        """
        # 正则表达式匹配格式: STAFF-YYYY-XXXX-XXXX 或 ADMIN-YYYY-XXXX-XXXX
        pattern = r'^(STAFF|ADMIN)-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def _check_ip_whitelist(ip_address: str, whitelist: List[str]) -> bool:
        """
        检查IP是否在白名单中
        
        Args:
            ip_address: 客户端IP
            whitelist: IP白名单
            
        Returns:
            是否在白名单中
        """
        try:
            client_ip = ipaddress.ip_address(ip_address)
            
            for allowed_ip in whitelist:
                try:
                    # 支持单个IP和CIDR网段
                    if ipaddress.ip_address(client_ip) in ipaddress.ip_network(allowed_ip, strict=False):
                        return True
                except ValueError:
                    logger.warning(f"无效的白名单IP格式: {allowed_ip}")
                    continue
            
            return False
            
        except ValueError:
            logger.warning(f"无效的客户端IP格式: {ip_address}")
            return False
    
    @staticmethod
    def _update_invitation_status(invitation_id: int, status: str) -> bool:
        """
        更新邀请码状态
        
        Args:
            invitation_id: 邀请码ID
            status: 新状态
            
        Returns:
            是否更新成功
        """
        try:
            update_sql = """
            UPDATE invitation_codes 
            SET status = :status, updated_at = CURRENT_TIMESTAMP
            WHERE id = :invitation_id
            """
            
            db.session.execute(text(update_sql), {
                'invitation_id': invitation_id,
                'status': status
            })
            db.session.commit()
            
            logger.info(f"更新邀请码状态成功: invitation_id={invitation_id}, status={status}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新邀请码状态失败: {e}")
            raise