"""
航材共享保障平台 - 简化工作流服务模块
功能：提供简化的工作流服务，支持基本的审批流程
作者：Claude AI Assistant
创建时间：2025-07-18
"""

from datetime import datetime
from typing import Optional, Dict, List, Any
from models import db, WorkflowInstance, WorkflowTask
from flask import current_app


class WorkflowService:
    """
    简化的工作流服务类
    
    功能：
    1. 管理工作流实例
    2. 处理简单的审批流程
    3. 提供工作流任务管理接口
    """
    
    def __init__(self):
        """初始化工作流服务"""
        self.workflow_definitions = {
            'shared_material_approval': {
                'name': '共享件审批流程',
                'description': '共享件发布审批流程',
                'tasks': [
                    {
                        'key': 'review_task',
                        'name': '审核任务',
                        'assignee': 'admin',
                        'description': '审核共享件发布申请'
                    }
                ]
            },
            'order_approval': {
                'name': '订单审批流程',
                'description': '高价值订单审批流程',
                'tasks': [
                    {
                        'key': 'manager_review',
                        'name': '经理审批',
                        'assignee': 'manager',
                        'description': '经理审批订单'
                    },
                    {
                        'key': 'finance_review', 
                        'name': '财务审批',
                        'assignee': 'finance',
                        'description': '财务审批订单'
                    }
                ]
            }
        }
    
    def start_workflow(self, workflow_key: str, business_data: Dict[str, Any],
                      initiator_id: int = None) -> WorkflowInstance:
        """
        启动工作流实例
        
        参数:
            workflow_key: 工作流定义的键值
            business_data: 业务数据
            initiator_id: 发起人ID
        
        返回:
            WorkflowInstance: 工作流实例对象
        """
        try:
            if workflow_key not in self.workflow_definitions:
                raise ValueError(f"工作流定义不存在：{workflow_key}")
            
            workflow_def = self.workflow_definitions[workflow_key]
            
            # 创建工作流实例
            workflow_instance = WorkflowInstance(
                workflow_key=workflow_key,
                business_key=business_data.get('business_key', f"{workflow_key}_{datetime.now().strftime('%Y%m%d%H%M%S')}"),
                business_type=business_data.get('business_type', 'shared_material'),
                business_id=business_data.get('business_id'),
                initiator_id=initiator_id,
                status='RUNNING',
                variables=business_data,
                created_at=datetime.utcnow()
            )
            
            db.session.add(workflow_instance)
            db.session.flush()  # 获取ID
            
            # 创建第一个任务
            if workflow_def['tasks']:
                first_task = workflow_def['tasks'][0]
                task = WorkflowTask(
                    instance_id=workflow_instance.id,
                    task_id=first_task['key'],
                    task_name=first_task['name'],
                    assignee=first_task['assignee'],
                    status='READY',
                    task_data=business_data,
                    created_at=datetime.utcnow()
                )
                db.session.add(task)
            
            db.session.commit()
            
            current_app.logger.info(f"工作流实例启动成功：{workflow_instance.id}")
            return workflow_instance
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"启动工作流失败：{str(e)}")
            raise
    
    def get_workflow_tasks(self, instance_id: int) -> List[WorkflowTask]:
        """
        获取工作流任务列表
        
        参数:
            instance_id: 工作流实例ID
        
        返回:
            List[WorkflowTask]: 任务列表
        """
        try:
            tasks = WorkflowTask.query.filter_by(instance_id=instance_id).all()
            return tasks
            
        except Exception as e:
            current_app.logger.error(f"获取工作流任务失败：{str(e)}")
            raise
    
    def complete_task(self, instance_id: int, task_key: str, 
                     user_id: int, task_data: Dict[str, Any] = None) -> bool:
        """
        完成任务
        
        参数:
            instance_id: 工作流实例ID
            task_key: 任务键值
            user_id: 操作用户ID
            task_data: 任务数据
        
        返回:
            bool: 是否完成成功
        """
        try:
            # 查找任务
            task = WorkflowTask.query.filter_by(
                instance_id=instance_id,
                task_id=task_key,
                status='READY'
            ).first()
            
            if not task:
                raise ValueError(f"任务不存在或已完成：{task_key}")
            
            # 更新任务状态
            task.status = 'COMPLETED'
            task.completed_at = datetime.utcnow()
            task.comment = task_data.get('comment', '') if task_data else ''
            
            # 更新任务数据
            if task_data:
                current_data = task.get_task_data()
                current_data.update(task_data)
                task.set_task_data(current_data)
            
            # 检查工作流是否完成
            workflow_instance = WorkflowInstance.query.get(instance_id)
            if workflow_instance:
                # 简单逻辑：如果是共享件审批，直接完成
                if workflow_instance.workflow_key == 'shared_material_approval':
                    workflow_instance.status = 'COMPLETED'
                    workflow_instance.completed_at = datetime.utcnow()
                
                # 如果是订单审批，需要检查所有任务
                elif workflow_instance.workflow_key == 'order_approval':
                    remaining_tasks = WorkflowTask.query.filter_by(
                        instance_id=instance_id,
                        status='READY'
                    ).count()
                    
                    if remaining_tasks == 0:
                        workflow_instance.status = 'COMPLETED'
                        workflow_instance.completed_at = datetime.utcnow()
            
            db.session.commit()
            
            current_app.logger.info(f"任务完成：{task_key}")
            return True
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"完成任务失败：{str(e)}")
            raise
    
    def get_workflow_instance(self, instance_id: int) -> Optional[WorkflowInstance]:
        """
        获取工作流实例
        
        参数:
            instance_id: 工作流实例ID
        
        返回:
            Optional[WorkflowInstance]: 工作流实例对象
        """
        try:
            return WorkflowInstance.query.get(instance_id)
            
        except Exception as e:
            current_app.logger.error(f"获取工作流实例失败：{str(e)}")
            return None
    
    def get_workflow_definitions(self) -> Dict[str, Any]:
        """
        获取工作流定义列表
        
        返回:
            Dict[str, Any]: 工作流定义字典
        """
        return self.workflow_definitions
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """
        获取工作流统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        try:
            total_instances = WorkflowInstance.query.count()
            running_instances = WorkflowInstance.query.filter_by(status='RUNNING').count()
            completed_instances = WorkflowInstance.query.filter_by(status='COMPLETED').count()
            
            total_tasks = WorkflowTask.query.count()
            pending_tasks = WorkflowTask.query.filter_by(status='READY').count()
            completed_tasks = WorkflowTask.query.filter_by(status='COMPLETED').count()
            
            return {
                'instances': {
                    'total': total_instances,
                    'running': running_instances,
                    'completed': completed_instances
                },
                'tasks': {
                    'total': total_tasks,
                    'pending': pending_tasks,
                    'completed': completed_tasks
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"获取工作流统计失败：{str(e)}")
            return {}


# 创建全局工作流服务实例
workflow_service = WorkflowService()