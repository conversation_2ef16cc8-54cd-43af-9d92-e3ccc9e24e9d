#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据隔离服务
版本: 1.0
创建时间: 2025-07-19

提供数据隔离和权限过滤功能，确保用户只能访问授权范围内的数据
"""

from typing import Optional, List, Type
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import and_, or_
from models import User, InventoryItem, Order, Demand, SharedMaterial


class DataIsolationService:
    """
    数据隔离服务类
    提供基于用户权限的数据过滤功能
    """
    
    @staticmethod
    def filter_query_by_user(query, model: Type, user: User):
        """
        根据用户权限过滤查询
        
        Args:
            query: SQLAlchemy查询对象
            model: 数据模型类
            user: 当前用户
            
        Returns:
            过滤后的查询对象
        """
        # 管理员和平台员工可以访问所有数据
        if user.is_admin() or user.can_cross_company_access():
            return query
        
        # 根据模型类型应用不同的过滤规则
        if model == InventoryItem:
            return DataIsolationService._filter_inventory_query(query, user)
        elif model == Order:
            return DataIsolationService._filter_order_query(query, user)
        elif model == Demand:
            return DataIsolationService._filter_demand_query(query, user)
        elif model == SharedMaterial:
            return DataIsolationService._filter_shared_material_query(query, user)
        else:
            # 默认过滤：只能访问自己创建的数据
            if hasattr(model, 'user_id'):
                return query.filter(model.user_id == user.id)
            elif hasattr(model, 'supplier_id'):
                return query.filter(model.supplier_id == user.id)
            elif hasattr(model, 'owner_id'):
                return query.filter(model.owner_id == user.id)
            else:
                # 如果无法确定所有权字段，返回空查询以确保安全
                return query.filter(False)
    
    @staticmethod
    def _filter_inventory_query(query, user: User):
        """
        过滤库存查询
        用户只能查看自己公司的库存
        
        Args:
            query: 库存查询对象
            user: 当前用户
            
        Returns:
            过滤后的查询对象
        """
        # 航空公司采购员：只能查看自己公司的库存
        if user.user_type in ['airline_buyer', 'airline']:
            return query.filter(
                or_(
                    InventoryItem.supplier_id == user.id,  # 自己的库存
                    InventoryItem.company_name == user.company_name  # 同公司的库存
                )
            )
        
        # 供应商：只能查看自己的库存
        elif user.user_type in ['supplier']:
            return query.filter(InventoryItem.supplier_id == user.id)
        
        # 维修工程师：根据公司类型决定访问范围
        elif user.user_type in ['maintenance_engineer', 'maintenance']:
            if user.is_internal_staff():
                # 内部维修工程师可以查看所有库存
                return query
            else:
                # 外部维修工程师只能查看自己公司的库存
                return query.filter(InventoryItem.company_name == user.company_name)
        
        # 物流专员：只能查看需要物流服务的库存
        elif user.user_type == 'logistics_specialist':
            # 这里可以根据具体业务逻辑调整
            return query.filter(InventoryItem.status.in_(['shipped', 'in_transit']))
        
        # 默认：只能查看自己的库存
        return query.filter(InventoryItem.supplier_id == user.id)
    
    @staticmethod
    def _filter_order_query(query, user: User):
        """
        过滤订单查询
        用户只能查看作为买方或供应商的订单
        
        Args:
            query: 订单查询对象
            user: 当前用户
            
        Returns:
            过滤后的查询对象
        """
        # 所有用户都可以查看自己作为买方或供应商的订单
        base_filter = or_(
            Order.buyer_id == user.id,
            Order.supplier_id == user.id
        )
        
        # 航空公司采购员：还可以查看同公司的订单
        if user.user_type in ['airline_buyer', 'airline']:
            company_filter = and_(
                Order.buyer.has(company_name=user.company_name)
            )
            return query.filter(or_(base_filter, company_filter))
        
        # 物流专员：可以查看所有需要物流服务的订单
        elif user.user_type == 'logistics_specialist':
            if user.is_internal_staff():
                # 内部物流专员可以查看所有订单
                return query
            else:
                # 外部物流专员只能查看自己参与的订单
                return query.filter(base_filter)
        
        # 维修工程师：可以查看维修相关的订单
        elif user.user_type in ['maintenance_engineer', 'maintenance']:
            if user.is_internal_staff():
                # 内部维修工程师可以查看所有维修订单
                return query.filter(Order.order_type == 'maintenance')
            else:
                # 外部维修工程师只能查看自己参与的订单
                return query.filter(base_filter)
        
        # 默认：只能查看自己参与的订单
        return query.filter(base_filter)
    
    @staticmethod
    def _filter_demand_query(query, user: User):
        """
        过滤需求查询
        用户只能查看自己发布的需求或可以响应的需求
        
        Args:
            query: 需求查询对象
            user: 当前用户
            
        Returns:
            过滤后的查询对象
        """
        # 航空公司采购员：可以查看自己和同公司发布的需求
        if user.user_type in ['airline_buyer', 'airline']:
            return query.filter(
                or_(
                    Demand.requester_id == user.id,  # 自己发布的需求
                    Demand.requester.has(company_name=user.company_name)  # 同公司的需求
                )
            )
        
        # 供应商：可以查看所有已发布的需求（用于响应）
        elif user.user_type in ['supplier']:
            return query.filter(Demand.status == 'published')
        
        # 维修工程师：可以查看维修相关的需求
        elif user.user_type in ['maintenance_engineer', 'maintenance']:
            if user.is_internal_staff():
                # 内部维修工程师可以查看所有需求
                return query
            else:
                # 外部维修工程师只能查看自己发布的需求
                return query.filter(Demand.requester_id == user.id)
        
        # 物流专员：可以查看需要物流服务的需求
        elif user.user_type == 'logistics_specialist':
            if user.is_internal_staff():
                # 内部物流专员可以查看所有需求
                return query
            else:
                # 外部物流专员只能查看自己参与的需求
                return query.filter(Demand.requester_id == user.id)
        
        # 默认：只能查看自己发布的需求
        return query.filter(Demand.requester_id == user.id)
    
    @staticmethod
    def _filter_shared_material_query(query, user: User):
        """
        过滤共享件查询
        用户只能查看自己发布的共享件或可以申请的共享件
        
        Args:
            query: 共享件查询对象
            user: 当前用户
            
        Returns:
            过滤后的查询对象
        """
        # 航空公司采购员：可以查看自己发布的和可以申请的共享件
        if user.user_type in ['airline_buyer', 'airline']:
            return query.filter(
                or_(
                    SharedMaterial.owner_id == user.id,  # 自己发布的共享件
                    SharedMaterial.status == 'approved'  # 可以申请的共享件
                )
            )
        
        # 供应商：可以查看所有已审核的共享件
        elif user.user_type in ['supplier']:
            return query.filter(SharedMaterial.status == 'approved')
        
        # 维修工程师：可以查看维修相关的共享件
        elif user.user_type in ['maintenance_engineer', 'maintenance']:
            if user.is_internal_staff():
                # 内部维修工程师可以查看所有共享件
                return query
            else:
                # 外部维修工程师只能查看已审核的共享件
                return query.filter(SharedMaterial.status == 'approved')
        
        # 物流专员：可以查看需要物流服务的共享件
        elif user.user_type == 'logistics_specialist':
            return query.filter(SharedMaterial.status == 'approved')
        
        # 默认：只能查看自己发布的共享件
        return query.filter(SharedMaterial.owner_id == user.id)
    
    @staticmethod
    def check_resource_access(user: User, resource, resource_type: str) -> bool:
        """
        检查用户是否有权访问特定资源
        
        Args:
            user: 当前用户
            resource: 资源对象
            resource_type: 资源类型
            
        Returns:
            是否有权限访问
        """
        # 管理员和平台员工可以访问所有资源
        if user.is_admin() or user.can_cross_company_access():
            return True
        
        # 根据资源类型检查权限
        if resource_type == 'inventory':
            return DataIsolationService._check_inventory_access(user, resource)
        elif resource_type == 'order':
            return DataIsolationService._check_order_access(user, resource)
        elif resource_type == 'demand':
            return DataIsolationService._check_demand_access(user, resource)
        elif resource_type == 'shared_material':
            return DataIsolationService._check_shared_material_access(user, resource)
        else:
            # 默认检查：资源所有者或同公司用户
            if hasattr(resource, 'user_id') and resource.user_id == user.id:
                return True
            if hasattr(resource, 'supplier_id') and resource.supplier_id == user.id:
                return True
            if hasattr(resource, 'owner_id') and resource.owner_id == user.id:
                return True
            if hasattr(resource, 'company_name') and resource.company_name == user.company_name:
                return True
            
            return False
    
    @staticmethod
    def _check_inventory_access(user: User, inventory) -> bool:
        """检查库存访问权限"""
        # 库存所有者
        if inventory.supplier_id == user.id:
            return True
        
        # 同公司用户
        if hasattr(inventory, 'company_name') and inventory.company_name == user.company_name:
            return True
        
        # 内部员工可以访问所有库存
        if user.is_internal_staff():
            return True
        
        return False
    
    @staticmethod
    def _check_order_access(user: User, order) -> bool:
        """检查订单访问权限"""
        # 订单买方或供应商
        if order.buyer_id == user.id or order.supplier_id == user.id:
            return True
        
        # 同公司的航空公司用户
        if (user.user_type in ['airline_buyer', 'airline'] and 
            hasattr(order.buyer, 'company_name') and 
            order.buyer.company_name == user.company_name):
            return True
        
        # 内部物流专员可以访问所有订单
        if user.user_type == 'logistics_specialist' and user.is_internal_staff():
            return True
        
        return False
    
    @staticmethod
    def _check_demand_access(user: User, demand) -> bool:
        """检查需求访问权限"""
        # 需求发布者
        if demand.requester_id == user.id:
            return True
        
        # 供应商可以查看已发布的需求
        if user.user_type in ['supplier'] and demand.status == 'published':
            return True
        
        # 同公司的航空公司用户
        if (user.user_type in ['airline_buyer', 'airline'] and 
            hasattr(demand.requester, 'company_name') and 
            demand.requester.company_name == user.company_name):
            return True
        
        return False
    
    @staticmethod
    def _check_shared_material_access(user: User, shared_material) -> bool:
        """检查共享件访问权限"""
        # 共享件所有者
        if shared_material.owner_id == user.id:
            return True
        
        # 已审核的共享件可以被查看
        if shared_material.status == 'approved':
            return True
        
        # 内部员工可以查看所有共享件
        if user.is_internal_staff():
            return True
        
        return False
    
    @staticmethod
    def get_accessible_companies(user: User) -> List[str]:
        """
        获取用户可以访问的公司列表
        
        Args:
            user: 当前用户
            
        Returns:
            可访问的公司名称列表
        """
        if user.is_admin() or user.can_cross_company_access():
            # 管理员和平台员工可以访问所有公司
            return ['*']  # 使用通配符表示所有公司
        
        accessible_companies = []
        
        # 始终可以访问自己的公司
        if user.company_name:
            accessible_companies.append(user.company_name)
        
        # 检查是否有额外的公司访问权限
        if hasattr(user, 'data_scopes'):
            for scope in user.data_scopes:
                if scope.is_active and scope.company_name not in accessible_companies:
                    accessible_companies.append(scope.company_name)
        
        return accessible_companies
    
    @staticmethod
    def log_data_access(user: User, resource_type: str, resource_id: str, 
                       action: str, success: bool, error_message: str = None):
        """
        记录数据访问日志
        
        Args:
            user: 当前用户
            resource_type: 资源类型
            resource_id: 资源ID
            action: 操作类型
            success: 是否成功
            error_message: 错误信息
        """
        try:
            from models import DataAccessLog, db
            from flask import request
            
            log_entry = DataAccessLog(
                user_id=user.id,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent', '') if request else '',
                success=success,
                error_message=error_message
            )
            
            db.session.add(log_entry)
            db.session.commit()
            
        except Exception as e:
            # 记录日志失败不应该影响主要业务逻辑
            import logging
            logging.getLogger(__name__).error(f"记录数据访问日志失败: {str(e)}")