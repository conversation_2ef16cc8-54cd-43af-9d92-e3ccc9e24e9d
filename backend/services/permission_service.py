#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 权限管理服务
版本: 1.0
创建时间: 2025-07-22

提供用户权限管理、角色权限配置和权限验证的统一服务
"""

from typing import List, Dict, Optional, Union
from models import User, db
from datetime import datetime


class PermissionService:
    """
    权限管理服务
    负责用户权限的管理、验证和配置
    """
    
    # 角色权限映射配置
    ROLE_PERMISSIONS = {
        'airline_buyer': [
            'publish_demand', 'manage_own_inventory', 'publish_shared', 
            'manage_orders', 'view_own_data', 'aog_request'
        ],
        'platform_staff': [
            'process_all_orders', 'cross_company_access', 'audit_shared_materials',
            'publish_demand', 'manage_inventory', 'publish_shared', 'manage_orders',
            'view_all_data', 'customer_service', 'data_analysis', 'user_management'
        ],
        'maintenance_engineer': [
            'update_maintenance_status', 'technical_support', 
            'view_maintenance_data', 'manage_work_orders', 'view_own_data'
        ],
        'logistics_specialist': [
            'track_shipment', 'update_logistics', 
            'manage_delivery', 'view_logistics_data', 'view_own_data'
        ],
        'admin': [
            'system_config', 'data_analysis', 'user_management',
            'all_permissions'  # 管理员具有所有权限
        ],
        # 向后兼容的旧角色
        'airline': ['publish_demand', 'manage_own_inventory', 'manage_orders', 'view_own_data'],
        'supplier': ['manage_own_inventory', 'publish_shared', 'manage_orders', 'view_own_data'],
        'maintenance': ['update_maintenance_status', 'technical_support', 'view_maintenance_data']
    }
    
    # 权限层级配置
    PERMISSION_LEVELS = {
        1: ['basic_features'],
        2: ['advanced_features', 'priority_support'],
        3: ['admin_features', 'user_management'],
        4: ['super_admin', 'system_config', 'all_permissions']
    }
    
    # 权限描述配置
    PERMISSION_DESCRIPTIONS = {
        'publish_demand': '发布航材需求',
        'manage_own_inventory': '管理自有库存',
        'manage_inventory': '管理所有库存',
        'publish_shared': '发布共享航材',
        'manage_orders': '管理订单',
        'view_own_data': '查看自有数据',
        'view_all_data': '查看所有数据',
        'process_all_orders': '处理所有订单',
        'cross_company_access': '跨公司数据访问',
        'audit_shared_materials': '审核共享航材',
        'customer_service': '客户服务',
        'data_analysis': '数据分析',
        'user_management': '用户管理',
        'system_config': '系统配置',
        'update_maintenance_status': '更新维修状态',
        'technical_support': '技术支持',
        'view_maintenance_data': '查看维修数据',
        'manage_work_orders': '管理工单',
        'track_shipment': '跟踪货运',
        'update_logistics': '更新物流',
        'manage_delivery': '管理配送',
        'view_logistics_data': '查看物流数据',
        'aog_request': 'AOG紧急请求',
        'all_permissions': '所有权限'
    }
    
    @classmethod
    def get_user_permissions(cls, user: User) -> List[str]:
        """
        获取用户的所有权限
        
        Args:
            user: 用户对象
            
        Returns:
            用户权限列表
        """
        if not user:
            return []
            
        # 获取基础角色权限
        base_permissions = cls.ROLE_PERMISSIONS.get(user.user_type, [])
        
        # 根据权限等级添加额外权限
        level_permissions = []
        for level in range(1, user.permission_level + 1):
            level_permissions.extend(cls.PERMISSION_LEVELS.get(level, []))
        
        # 合并权限并去重
        all_permissions = list(set(base_permissions + level_permissions))
        
        return all_permissions
    
    @classmethod
    def has_permission(cls, user: User, permission: str) -> bool:
        """
        检查用户是否具有特定权限
        
        Args:
            user: 用户对象
            permission: 权限代码
            
        Returns:
            是否具有权限
        """
        if not user or user.status != 'active':
            return False
            
        user_permissions = cls.get_user_permissions(user)
        return permission in user_permissions or 'all_permissions' in user_permissions
    
    @classmethod
    def has_any_permission(cls, user: User, permissions: List[str]) -> bool:
        """
        检查用户是否具有列表中的任一权限
        
        Args:
            user: 用户对象
            permissions: 权限代码列表
            
        Returns:
            是否具有任一权限
        """
        for permission in permissions:
            if cls.has_permission(user, permission):
                return True
        return False
    
    @classmethod
    def has_all_permissions(cls, user: User, permissions: List[str]) -> bool:
        """
        检查用户是否具有列表中的所有权限
        
        Args:
            user: 用户对象
            permissions: 权限代码列表
            
        Returns:
            是否具有所有权限
        """
        for permission in permissions:
            if not cls.has_permission(user, permission):
                return False
        return True
    
    @classmethod
    def can_access_resource(cls, user: User, resource_owner_id: int, 
                          allow_cross_company: bool = False) -> bool:
        """
        检查用户是否可以访问特定资源
        
        Args:
            user: 用户对象
            resource_owner_id: 资源所有者ID
            allow_cross_company: 是否允许跨公司访问
            
        Returns:
            是否可以访问
        """
        if not user:
            return False
            
        # 管理员可以访问所有资源
        if user.is_admin():
            return True
            
        # 资源所有者可以访问
        if user.id == resource_owner_id:
            return True
            
        # 跨公司访问检查
        if allow_cross_company and user.can_cross_company_access():
            return True
            
        # 同公司访问检查
        if hasattr(user, 'company_name'):
            resource_owner = User.query.get(resource_owner_id)
            if resource_owner and resource_owner.company_name == user.company_name:
                return True
        
        return False
    
    @classmethod
    def get_accessible_resource_filter(cls, user: User, model_class, 
                                     owner_field: str = 'supplier_id') -> Optional[object]:
        """
        获取用户可访问资源的查询过滤器
        
        Args:
            user: 用户对象
            model_class: 模型类
            owner_field: 所有者字段名
            
        Returns:
            查询过滤器
        """
        if not user:
            return model_class.query.filter_by(id=-1)  # 返回空结果
            
        # 管理员可以访问所有资源
        if user.is_admin():
            return model_class.query
            
        # 平台员工可以跨公司访问
        if user.can_cross_company_access():
            return model_class.query
            
        # 普通用户只能访问自己的资源
        filter_condition = {owner_field: user.id}
        return model_class.query.filter_by(**filter_condition)
    
    @classmethod
    def assign_role_permissions(cls, user: User, role: str) -> bool:
        """
        为用户分配角色权限
        
        Args:
            user: 用户对象
            role: 角色名称
            
        Returns:
            是否成功
        """
        try:
            if role in cls.ROLE_PERMISSIONS:
                user.user_type = role
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            return False
    
    @classmethod
    def elevate_permission_level(cls, user: User, new_level: int) -> bool:
        """
        提升用户权限等级
        
        Args:
            user: 用户对象
            new_level: 新的权限等级
            
        Returns:
            是否成功
        """
        try:
            if 1 <= new_level <= 4 and new_level > user.permission_level:
                user.permission_level = new_level
                user.updated_at = datetime.utcnow()
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            return False
    
    @classmethod
    def get_role_info(cls, role: str) -> Dict:
        """
        获取角色信息
        
        Args:
            role: 角色名称
            
        Returns:
            角色信息字典
        """
        permissions = cls.ROLE_PERMISSIONS.get(role, [])
        return {
            'role': role,
            'permissions': permissions,
            'permission_count': len(permissions),
            'descriptions': [cls.PERMISSION_DESCRIPTIONS.get(p, p) for p in permissions]
        }
    
    @classmethod
    def get_all_roles(cls) -> List[Dict]:
        """
        获取所有角色信息
        
        Returns:
            所有角色信息列表
        """
        return [cls.get_role_info(role) for role in cls.ROLE_PERMISSIONS.keys()]
    
    @classmethod
    def validate_permission_request(cls, user: User, requested_permission: str, 
                                  resource_info: str = None) -> Dict:
        """
        验证权限请求
        
        Args:
            user: 用户对象
            requested_permission: 请求的权限
            resource_info: 资源信息
            
        Returns:
            验证结果字典
        """
        result = {
            'granted': False,
            'user_id': user.id if user else None,
            'permission': requested_permission,
            'resource_info': resource_info,
            'reason': '',
            'user_permissions': []
        }
        
        if not user:
            result['reason'] = '用户不存在'
            return result
            
        if user.status != 'active':
            result['reason'] = f'用户状态异常: {user.status}'
            return result
            
        user_permissions = cls.get_user_permissions(user)
        result['user_permissions'] = user_permissions
        
        if cls.has_permission(user, requested_permission):
            result['granted'] = True
            result['reason'] = '权限验证通过'
        else:
            result['reason'] = f'缺少必要权限: {requested_permission}'
            
        return result


class DataIsolationService:
    """
    数据隔离服务
    提供基于用户权限的数据访问控制
    """
    
    @staticmethod
    def filter_inventory_query(query, user: User):
        """过滤库存查询"""
        if not user:
            return query.filter_by(id=-1)
            
        if user.is_admin() or user.can_cross_company_access():
            return query
            
        return query.filter_by(supplier_id=user.id)
    
    @staticmethod
    def filter_order_query(query, user: User):
        """过滤订单查询"""
        if not user:
            return query.filter_by(id=-1)
            
        if user.is_admin() or user.can_cross_company_access():
            return query
            
        # 用户可以看到作为买方或供应商的订单
        from sqlalchemy import or_
        return query.filter(
            or_(
                query.model.buyer_id == user.id,
                query.model.supplier_id == user.id
            )
        )
    
    @staticmethod
    def filter_demand_query(query, user: User):
        """过滤需求查询"""
        if not user:
            return query.filter_by(id=-1)
            
        if user.is_admin() or user.can_cross_company_access():
            return query
            
        return query.filter_by(requester_id=user.id)
    
    @staticmethod
    def filter_shared_material_query(query, user: User):
        """过滤共享航材查询"""
        if not user:
            return query.filter_by(id=-1)
            
        if user.is_admin() or user.can_cross_company_access():
            return query
            
        return query.filter_by(shared_by=user.id)


# 导出服务实例
permission_service = PermissionService()
data_isolation_service = DataIsolationService()