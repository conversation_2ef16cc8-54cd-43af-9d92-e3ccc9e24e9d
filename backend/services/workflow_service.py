"""
航材共享保障平台 - 工作流服务模块
功能：基于SpiffWorkflow的工作流引擎服务
作者：Claude AI Assistant
创建时间：2025-07-18
"""

from SpiffWorkflow.bpmn.workflow import BpmnWorkflow
from SpiffWorkflow.bpmn.parser.BpmnParser import BpmnParser
from SpiffWorkflow.task import Task
from SpiffWorkflow.exceptions import WorkflowException
from datetime import datetime
import json
import os
from typing import Optional, Dict, List, Any
from flask import current_app
from models import db, WorkflowInstance, WorkflowTask


class WorkflowService:
    """
    工作流服务类
    
    功能：
    1. 管理工作流定义和实例
    2. 处理工作流执行和状态管理
    3. 支持三种审批流程模板
    4. 提供工作流任务管理接口
    """
    
    def __init__(self):
        """初始化工作流服务"""
        self.workflow_definitions = {}
        self.workflow_instances = {}
        self._initialized = False
    
    def _ensure_initialized(self):
        """确保工作流服务已初始化"""
        if not self._initialized:
            self._load_workflow_definitions()
            self._initialized = True
    
    def _load_workflow_definitions(self):
        """
        加载工作流定义文件
        从workflows目录读取BPMN文件并解析
        """
        # 创建workflows目录如果不存在
        workflow_dir = os.path.join(os.path.dirname(__file__), '..', 'workflows')
        
        if not os.path.exists(workflow_dir):
            os.makedirs(workflow_dir)
            self._create_default_workflows()
        
        # 加载所有BPMN文件
        for filename in os.listdir(workflow_dir):
            if filename.endswith('.bpmn'):
                workflow_key = filename[:-5]  # 去掉.bpmn后缀
                file_path = os.path.join(workflow_dir, filename)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        bpmn_content = f.read()
                    
                    parser = BpmnParser()
                    parser.add_bpmn_xml(bpmn_content)
                    self.workflow_definitions[workflow_key] = parser
                    
                    current_app.logger.info(f"已加载工作流定义：{workflow_key}")
                    
                except Exception as e:
                    current_app.logger.error(f"加载工作流定义失败 {filename}: {str(e)}")
    
    def _create_default_workflows(self):
        """
        创建默认工作流定义
        根据CLAUDE.md要求创建三种审批流程模板
        """
        workflows = {
            'simple_approval': self._create_simple_approval_workflow(),
            'multi_level_approval': self._create_multi_level_approval_workflow(),
            'parallel_approval': self._create_parallel_approval_workflow()
        }
        
        workflow_dir = os.path.join(current_app.root_path, 'workflows')
        
        for workflow_name, bpmn_content in workflows.items():
            file_path = os.path.join(workflow_dir, f"{workflow_name}.bpmn")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(bpmn_content)
            
            current_app.logger.info(f"已创建默认工作流：{workflow_name}")
    
    def _create_simple_approval_workflow(self) -> str:
        """
        创建简单审批流程BPMN定义
        流程：提交申请 -> 审批 -> 完成
        """
        return '''<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="simple_approval_def" 
                  targetNamespace="http://spiffworkflow.org/bpmn/schema/1.0">
  
  <bpmn:process id="simple_approval_process" name="简单审批流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>start_to_submit</bpmn:outgoing>
    </bpmn:startEvent>
    
    <!-- 提交申请任务 -->
    <bpmn:userTask id="submit_task" name="提交申请">
      <bpmn:incoming>start_to_submit</bpmn:incoming>
      <bpmn:outgoing>submit_to_approve</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 审批任务 -->
    <bpmn:userTask id="approve_task" name="审批">
      <bpmn:incoming>submit_to_approve</bpmn:incoming>
      <bpmn:outgoing>approve_to_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 结束事件 -->
    <bpmn:endEvent id="end_event" name="结束">
      <bpmn:incoming>approve_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="start_to_submit" sourceRef="start_event" targetRef="submit_task" />
    <bpmn:sequenceFlow id="submit_to_approve" sourceRef="submit_task" targetRef="approve_task" />
    <bpmn:sequenceFlow id="approve_to_end" sourceRef="approve_task" targetRef="end_event" />
    
  </bpmn:process>
</bpmn:definitions>'''
    
    def _create_multi_level_approval_workflow(self) -> str:
        """
        创建多级审批流程BPMN定义
        流程：提交申请 -> 部门审批 -> 财务审批 -> 总经理审批 -> 完成
        """
        return '''<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  id="multi_level_approval_def" 
                  targetNamespace="http://spiffworkflow.org/bpmn/schema/1.0">
  
  <bpmn:process id="multi_level_approval_process" name="多级审批流程" isExecutable="true">
    
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>start_to_submit</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="submit_task" name="提交申请">
      <bpmn:incoming>start_to_submit</bpmn:incoming>
      <bpmn:outgoing>submit_to_dept</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="dept_approve_task" name="部门审批">
      <bpmn:incoming>submit_to_dept</bpmn:incoming>
      <bpmn:outgoing>dept_to_finance</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="finance_approve_task" name="财务审批">
      <bpmn:incoming>dept_to_finance</bpmn:incoming>
      <bpmn:outgoing>finance_to_manager</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="manager_approve_task" name="总经理审批">
      <bpmn:incoming>finance_to_manager</bpmn:incoming>
      <bpmn:outgoing>manager_to_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:endEvent id="end_event" name="结束">
      <bpmn:incoming>manager_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:sequenceFlow id="start_to_submit" sourceRef="start_event" targetRef="submit_task" />
    <bpmn:sequenceFlow id="submit_to_dept" sourceRef="submit_task" targetRef="dept_approve_task" />
    <bpmn:sequenceFlow id="dept_to_finance" sourceRef="dept_approve_task" targetRef="finance_approve_task" />
    <bpmn:sequenceFlow id="finance_to_manager" sourceRef="finance_approve_task" targetRef="manager_approve_task" />
    <bpmn:sequenceFlow id="manager_to_end" sourceRef="manager_approve_task" targetRef="end_event" />
    
  </bpmn:process>
</bpmn:definitions>'''
    
    def _create_parallel_approval_workflow(self) -> str:
        """
        创建并行审批流程BPMN定义
        流程：提交申请 -> (技术部门 + 采购部门)并行审批 -> 汇总审批 -> 完成
        """
        return '''<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  id="parallel_approval_def" 
                  targetNamespace="http://spiffworkflow.org/bpmn/schema/1.0">
  
  <bpmn:process id="parallel_approval_process" name="并行审批流程" isExecutable="true">
    
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>start_to_submit</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="submit_task" name="提交申请">
      <bpmn:incoming>start_to_submit</bpmn:incoming>
      <bpmn:outgoing>submit_to_parallel</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:parallelGateway id="parallel_gateway_split" name="并行网关">
      <bpmn:incoming>submit_to_parallel</bpmn:incoming>
      <bpmn:outgoing>parallel_to_tech</bpmn:outgoing>
      <bpmn:outgoing>parallel_to_purchase</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <bpmn:userTask id="tech_approve_task" name="技术部门审批">
      <bpmn:incoming>parallel_to_tech</bpmn:incoming>
      <bpmn:outgoing>tech_to_join</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="purchase_approve_task" name="采购部门审批">
      <bpmn:incoming>parallel_to_purchase</bpmn:incoming>
      <bpmn:outgoing>purchase_to_join</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:parallelGateway id="parallel_gateway_join" name="汇总网关">
      <bpmn:incoming>tech_to_join</bpmn:incoming>
      <bpmn:incoming>purchase_to_join</bpmn:incoming>
      <bpmn:outgoing>join_to_final</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <bpmn:userTask id="final_approve_task" name="最终审批">
      <bpmn:incoming>join_to_final</bpmn:incoming>
      <bpmn:outgoing>final_to_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:endEvent id="end_event" name="结束">
      <bpmn:incoming>final_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:sequenceFlow id="start_to_submit" sourceRef="start_event" targetRef="submit_task" />
    <bpmn:sequenceFlow id="submit_to_parallel" sourceRef="submit_task" targetRef="parallel_gateway_split" />
    <bpmn:sequenceFlow id="parallel_to_tech" sourceRef="parallel_gateway_split" targetRef="tech_approve_task" />
    <bpmn:sequenceFlow id="parallel_to_purchase" sourceRef="parallel_gateway_split" targetRef="purchase_approve_task" />
    <bpmn:sequenceFlow id="tech_to_join" sourceRef="tech_approve_task" targetRef="parallel_gateway_join" />
    <bpmn:sequenceFlow id="purchase_to_join" sourceRef="purchase_approve_task" targetRef="parallel_gateway_join" />
    <bpmn:sequenceFlow id="join_to_final" sourceRef="parallel_gateway_join" targetRef="final_approve_task" />
    <bpmn:sequenceFlow id="final_to_end" sourceRef="final_approve_task" targetRef="end_event" />
    
  </bpmn:process>
</bpmn:definitions>'''
    
    def start_workflow(self, workflow_key: str, business_key: str, 
                      variables: Dict[str, Any] = None, 
                      initiator_id: int = None) -> str:
        """
        启动工作流实例
        
        参数:
            workflow_key: 工作流定义的键值
            business_key: 业务键值（如订单号、需求单号等）
            variables: 工作流变量
            initiator_id: 发起人ID
        
        返回:
            工作流实例ID
        """
        try:
            self._ensure_initialized()
            
            if workflow_key not in self.workflow_definitions:
                raise WorkflowException(f"工作流定义不存在：{workflow_key}")
            
            # 创建工作流实例
            parser = self.workflow_definitions[workflow_key]
            workflow = BpmnWorkflow(parser.get_spec(workflow_key + '_process'))
            
            # 设置工作流变量
            if variables:
                for key, value in variables.items():
                    workflow.data[key] = value
            
            # 保存工作流实例到数据库
            workflow_instance = WorkflowInstance(
                workflow_key=workflow_key,
                business_key=business_key,
                status='RUNNING',
                initiator_id=initiator_id,
                variables=json.dumps(variables or {}),
                created_at=datetime.utcnow()
            )
            
            db.session.add(workflow_instance)
            db.session.flush()  # 获取ID
            
            instance_id = str(workflow_instance.id)
            self.workflow_instances[instance_id] = workflow
            
            # 启动工作流
            workflow.do_engine_steps()
            
            # 保存当前任务
            self._save_current_tasks(instance_id, workflow)
            
            db.session.commit()
            
            current_app.logger.info(f"工作流实例启动成功：{instance_id}")
            return instance_id
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"启动工作流实例失败：{str(e)}")
            raise WorkflowException(f"启动工作流实例失败：{str(e)}")
    
    def get_workflow_tasks(self, instance_id: str) -> List[Dict[str, Any]]:
        """
        获取工作流实例的当前任务
        
        参数:
            instance_id: 工作流实例ID
        
        返回:
            任务列表
        """
        try:
            workflow = self._get_workflow_instance(instance_id)
            
            tasks = []
            for task in workflow.get_ready_user_tasks():
                task_info = {
                    'id': task.id,
                    'name': task.task_spec.name,
                    'description': task.task_spec.description or '',
                    'assignee': task.data.get('assignee'),
                    'created_at': task.data.get('created_at'),
                    'due_date': task.data.get('due_date'),
                    'form_data': task.data.get('form_data', {}),
                    'status': 'READY'
                }
                tasks.append(task_info)
            
            return tasks
            
        except Exception as e:
            current_app.logger.error(f"获取工作流任务失败：{str(e)}")
            raise WorkflowException(f"获取工作流任务失败：{str(e)}")
    
    def complete_task(self, instance_id: str, task_id: str, 
                     variables: Dict[str, Any] = None,
                     user_id: int = None) -> bool:
        """
        完成工作流任务
        
        参数:
            instance_id: 工作流实例ID
            task_id: 任务ID
            variables: 任务变量
            user_id: 操作用户ID
        
        返回:
            是否完成成功
        """
        try:
            workflow = self._get_workflow_instance(instance_id)
            
            # 查找任务
            task = None
            for ready_task in workflow.get_ready_user_tasks():
                if ready_task.id == task_id:
                    task = ready_task
                    break
            
            if not task:
                raise WorkflowException(f"任务不存在或已完成：{task_id}")
            
            # 设置任务数据
            if variables:
                for key, value in variables.items():
                    task.data[key] = value
            
            # 完成任务
            task.complete()
            
            # 执行工作流步骤
            workflow.do_engine_steps()
            
            # 更新数据库
            self._save_current_tasks(instance_id, workflow)
            
            # 检查工作流是否完成
            if workflow.is_completed():
                workflow_instance = WorkflowInstance.query.get(instance_id)
                workflow_instance.status = 'COMPLETED'
                workflow_instance.completed_at = datetime.utcnow()
            
            db.session.commit()
            
            current_app.logger.info(f"任务完成：{task_id}")
            return True
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"完成任务失败：{str(e)}")
            raise WorkflowException(f"完成任务失败：{str(e)}")
    
    def get_workflow_instance(self, instance_id: str) -> Optional[Dict[str, Any]]:
        """
        获取工作流实例信息
        
        参数:
            instance_id: 工作流实例ID
        
        返回:
            工作流实例信息
        """
        try:
            workflow_instance = WorkflowInstance.query.get(instance_id)
            if not workflow_instance:
                return None
            
            workflow = self._get_workflow_instance(instance_id)
            
            return {
                'id': workflow_instance.id,
                'workflow_key': workflow_instance.workflow_key,
                'business_key': workflow_instance.business_key,
                'status': workflow_instance.status,
                'initiator_id': workflow_instance.initiator_id,
                'variables': json.loads(workflow_instance.variables or '{}'),
                'created_at': workflow_instance.created_at.isoformat(),
                'completed_at': workflow_instance.completed_at.isoformat() if workflow_instance.completed_at else None,
                'current_tasks': self.get_workflow_tasks(instance_id),
                'is_completed': workflow.is_completed(),
                'progress': self._calculate_progress(workflow)
            }
            
        except Exception as e:
            current_app.logger.error(f"获取工作流实例失败：{str(e)}")
            raise WorkflowException(f"获取工作流实例失败：{str(e)}")
    
    def _get_workflow_instance(self, instance_id: str) -> BpmnWorkflow:
        """
        获取工作流实例对象
        
        参数:
            instance_id: 工作流实例ID
        
        返回:
            工作流实例对象
        """
        if instance_id not in self.workflow_instances:
            # 从数据库重新加载
            workflow_instance = WorkflowInstance.query.get(instance_id)
            if not workflow_instance:
                raise WorkflowException(f"工作流实例不存在：{instance_id}")
            
            # 重新构建工作流实例
            parser = self.workflow_definitions[workflow_instance.workflow_key]
            workflow = BpmnWorkflow(parser.get_spec(workflow_instance.workflow_key + '_process'))
            
            # 恢复工作流状态
            variables = json.loads(workflow_instance.variables or '{}')
            for key, value in variables.items():
                workflow.data[key] = value
            
            # 恢复任务状态
            tasks = WorkflowTask.query.filter_by(instance_id=instance_id).all()
            for task_record in tasks:
                if task_record.status == 'COMPLETED':
                    # 重新执行已完成的任务
                    for task in workflow.get_tasks():
                        if task.id == task_record.task_id:
                            task.complete()
            
            workflow.do_engine_steps()
            self.workflow_instances[instance_id] = workflow
        
        return self.workflow_instances[instance_id]
    
    def _save_current_tasks(self, instance_id: str, workflow: BpmnWorkflow):
        """
        保存当前任务到数据库
        
        参数:
            instance_id: 工作流实例ID
            workflow: 工作流实例
        """
        # 删除旧的任务记录
        WorkflowTask.query.filter_by(instance_id=instance_id).delete()
        
        # 保存当前任务
        for task in workflow.get_ready_user_tasks():
            workflow_task = WorkflowTask(
                instance_id=instance_id,
                task_id=task.id,
                task_name=task.task_spec.name,
                assignee=task.data.get('assignee'),
                status='READY',
                created_at=datetime.utcnow(),
                task_data=json.dumps(task.data)
            )
            db.session.add(workflow_task)
    
    def _calculate_progress(self, workflow: BpmnWorkflow) -> float:
        """
        计算工作流进度
        
        参数:
            workflow: 工作流实例
        
        返回:
            进度百分比 (0-100)
        """
        total_tasks = len(list(workflow.get_tasks()))
        completed_tasks = len([t for t in workflow.get_tasks() if t.is_completed()])
        
        if total_tasks == 0:
            return 0.0
        
        return (completed_tasks / total_tasks) * 100
    
    def get_available_workflows(self) -> List[Dict[str, str]]:
        """
        获取可用的工作流定义列表
        
        返回:
            工作流定义列表
        """
        workflows = []
        for key in self.workflow_definitions.keys():
            workflow_info = {
                'key': key,
                'name': self._get_workflow_name(key),
                'description': self._get_workflow_description(key)
            }
            workflows.append(workflow_info)
        
        return workflows
    
    def _get_workflow_name(self, workflow_key: str) -> str:
        """获取工作流名称"""
        name_map = {
            'simple_approval': '简单审批流程',
            'multi_level_approval': '多级审批流程',
            'parallel_approval': '并行审批流程'
        }
        return name_map.get(workflow_key, workflow_key)
    
    def _get_workflow_description(self, workflow_key: str) -> str:
        """获取工作流描述"""
        desc_map = {
            'simple_approval': '一级审批，适用于简单业务场景',
            'multi_level_approval': '部门→财务→总经理多级审批，适用于重要业务',
            'parallel_approval': '多部门并行审批，适用于需要多方确认的业务'
        }
        return desc_map.get(workflow_key, '工作流描述')


# 全局工作流服务实例
workflow_service = WorkflowService()