#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 库存管理API路由
版本: 1.0
创建时间: 2025-01-15

处理库存相关的所有API请求，包括查询、入库、出库、库存状态更新等操作
"""

from datetime import datetime, timedelta
from decimal import Decimal
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, or_, desc

from models import db, InventoryItem, Material, User, Notification
from utils.decorators import (
    validate_json, require_active_user, data_isolation, audit_action,
    require_permission, require_ownership_enhanced
)
from services.data_isolation_service import DataIsolationService
from utils.response import (
    success_response, error_response, paginated_response, created_response,
    not_found_response, updated_response
)
from utils.validators import paginate_query, is_valid_enum

# 创建蓝图
inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/items/', methods=['GET'])
@require_permission('view_own_inventory')
@data_isolation(InventoryItem, 'supplier_id')
@audit_action('view_inventory_list', 'inventory')
def get_inventory_items(current_user=None):
    """
    获取库存项列表
    功能描述：获取库存项列表，支持搜索、筛选和分页
    入参：{ keyword: string, category: string, status: string, location: string, page: int, size: int }
    返回参数：分页的库存项列表数据
    url地址：/api/v1/inventory/items
    请求方式：GET
    """
    try:
        # current_user由data_isolation装饰器提供
        if not current_user:
            return error_response(401, '用户认证失败')

        # 获取查询参数
        keyword = request.args.get('keyword', '').strip()
        category = request.args.get('category', '').strip()
        status = request.args.get('status', '').strip()
        location = request.args.get('location', '').strip()
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)

        # 验证分页参数
        if page < 1:
            return error_response(400, '页码必须大于0')
        if size < 1 or size > 100:
            return error_response(400, '每页数量必须在1-100之间')

        # 构建查询
        query = db.session.query(InventoryItem).join(Material)
        
        # 【第一阶段重要更新】应用数据隔离过滤
        query = DataIsolationService.filter_query_by_user(query, InventoryItem, current_user)
        
        # 记录用户访问日志
        DataIsolationService.log_data_access(
            user=current_user,
            resource_type='inventory',
            resource_id='list',
            action='view_list',
            success=True
        )

        # 关键词搜索（航材名称、零件号）
        if keyword:
            query = query.filter(
                or_(
                    Material.part_name.contains(keyword),
                    Material.part_number.contains(keyword),
                    Material.description.contains(keyword)
                )
            )

        # 航材类别筛选
        if category:
            query = query.filter(Material.category == category)

        # 库存状态筛选
        if status:
            if status in ['normal', 'warning', 'shortage', 'expired']:
                query = query.filter(InventoryItem.status == status)

        # 仓库位置筛选
        if location:
            query = query.filter(InventoryItem.location.contains(location))

        # 排序：优先显示预警和缺货的库存
        query = query.order_by(
            desc(InventoryItem.status == 'shortage'),
            desc(InventoryItem.status == 'warning'),
            desc(InventoryItem.last_updated)
        )

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=size,
            error_out=False
        )

        # 格式化数据
        items = []
        for inventory_item in pagination.items:
            item_data = inventory_item.to_dict()
            # 计算库存总值
            if inventory_item.unit_price and inventory_item.current_stock:
                item_data['total_value'] = float(inventory_item.unit_price) * inventory_item.current_stock
            else:
                item_data['total_value'] = 0
            items.append(item_data)

        # 构建分页信息
        pagination_info = {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }

        return paginated_response(
            items=items,
            pagination_info=pagination_info,
            message='库存项列表获取成功'
        )

    except Exception as e:
        return error_response(500, f'获取库存项列表失败: {str(e)}')

@inventory_bp.route('/items/<int:item_id>/', methods=['GET'])
@require_ownership_enhanced(InventoryItem, 'supplier_id', allow_admin=True, allow_cross_company=True)
@audit_action('view_inventory_detail', 'inventory')
def get_inventory_item(item_id, current_user=None):
    """
    获取库存项详情
    功能描述：获取指定库存项的详细信息
    入参：item_id (路径参数)
    返回参数：库存项详细信息
    url地址：/api/v1/inventory/items/{item_id}
    请求方式：GET
    """
    try:
        # current_user由data_isolation装饰器提供
        if not current_user:
            return error_response(401, '用户认证失败')

        # 查询库存项
        inventory_item = InventoryItem.query.get(item_id)
        if not inventory_item:
            return not_found_response('库存项')
        
        # 【第一阶段重要更新】检查用户是否有权访问该库存项
        if not DataIsolationService.check_resource_access(current_user, inventory_item, 'inventory'):
            DataIsolationService.log_data_access(
                user=current_user,
                resource_type='inventory',
                resource_id=str(item_id),
                action='view_detail_denied',
                success=False,
                error_message='权限不足'
            )
            return error_response(403, '权限不足，无法访问该库存项')

        # 获取详细信息
        item_data = inventory_item.to_dict()

        # 计算库存总值
        if inventory_item.unit_price and inventory_item.current_stock:
            item_data['total_value'] = float(inventory_item.unit_price) * inventory_item.current_stock
        else:
            item_data['total_value'] = 0

        # 添加库存历史记录（如果需要的话，这里可以扩展）
        item_data['stock_history'] = []
        
        # 记录成功访问
        DataIsolationService.log_data_access(
            user=current_user,
            resource_type='inventory',
            resource_id=str(item_id),
            action='view_detail',
            success=True
        )

        return success_response(item_data, '库存项详情获取成功')

    except Exception as e:
        return error_response(500, f'获取库存项详情失败: {str(e)}')

@inventory_bp.route('/items/<int:item_id>/inbound/', methods=['POST'])
@jwt_required()
def inbound_inventory(item_id):
    """
    库存入库操作
    功能描述：增加指定库存项的库存数量
    入参：{ quantity: int, unit_price: float, batch_number: string, expiry_date: string, notes: string }
    返回参数：更新后的库存信息
    url地址：/api/v1/inventory/items/{item_id}/inbound
    请求方式：POST
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        # 验证必填字段
        if not data or 'quantity' not in data:
            return error_response(400, '入库数量不能为空')

        quantity = data.get('quantity')
        unit_price = data.get('unit_price')
        batch_number = data.get('batch_number', '')
        expiry_date_str = data.get('expiry_date')
        notes = data.get('notes', '')

        # 验证数量
        if not isinstance(quantity, int) or quantity <= 0:
            return error_response(400, '入库数量必须是正整数')

        # 查询库存项
        inventory_item = InventoryItem.query.get(item_id)
        if not inventory_item:
            return not_found_response('库存项')

        # 更新库存数量
        inventory_item.current_stock += quantity

        # 更新单价（如果提供）
        if unit_price is not None:
            try:
                inventory_item.unit_price = Decimal(str(unit_price))
            except (ValueError, TypeError):
                return error_response(400, '单价格式不正确')

        # 更新批次号（如果提供）
        if batch_number:
            inventory_item.batch_number = batch_number

        # 更新过期日期（如果提供）
        if expiry_date_str:
            try:
                inventory_item.expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
            except ValueError:
                return error_response(400, '过期日期格式不正确，应为YYYY-MM-DD')

        # 更新库存状态
        inventory_item.update_status()
        inventory_item.last_updated = datetime.utcnow()

        # 保存到数据库
        db.session.commit()

        # 创建通知（如果库存状态改善）
        if inventory_item.status == 'normal':
            notification = Notification(
                user_id=current_user_id,
                title='库存入库成功',
                content=f'航材 {inventory_item.material.part_name} 已成功入库 {quantity} 件',
                type='inventory',
                is_read=False
            )
            db.session.add(notification)
            db.session.commit()

        return success_response(inventory_item.to_dict(), '库存入库成功')

    except Exception as e:
        db.session.rollback()
        return error_response(500, f'库存入库失败: {str(e)}')

@inventory_bp.route('/items/<int:item_id>/outbound/', methods=['POST'])
@jwt_required()
def outbound_inventory(item_id):
    """
    库存出库操作
    功能描述：减少指定库存项的库存数量
    入参：{ quantity: int, reason: string, notes: string }
    返回参数：更新后的库存信息
    url地址：/api/v1/inventory/items/{item_id}/outbound
    请求方式：POST
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        # 验证必填字段
        if not data or 'quantity' not in data:
            return error_response(400, '出库数量不能为空')

        quantity = data.get('quantity')
        reason = data.get('reason', '正常出库')
        notes = data.get('notes', '')

        # 验证数量
        if not isinstance(quantity, int) or quantity <= 0:
            return error_response(400, '出库数量必须是正整数')

        # 查询库存项
        inventory_item = InventoryItem.query.get(item_id)
        if not inventory_item:
            return not_found_response('库存项')

        # 检查库存是否充足
        if inventory_item.current_stock < quantity:
            return error_response(400, f'库存不足，当前库存：{inventory_item.current_stock}，出库数量：{quantity}')

        # 更新库存数量
        inventory_item.current_stock -= quantity

        # 更新库存状态
        inventory_item.update_status()
        inventory_item.last_updated = datetime.utcnow()

        # 保存到数据库
        db.session.commit()

        # 创建通知（如果库存状态变为预警或缺货）
        if inventory_item.status in ['warning', 'shortage']:
            status_text = '预警' if inventory_item.status == 'warning' else '缺货'
            notification = Notification(
                user_id=current_user_id,
                title=f'库存{status_text}提醒',
                content=f'航材 {inventory_item.material.part_name} 库存{status_text}，当前库存：{inventory_item.current_stock}',
                type='inventory',
                is_read=False
            )
            db.session.add(notification)
            db.session.commit()

        return success_response(inventory_item.to_dict(), '库存出库成功')

    except Exception as e:
        db.session.rollback()
        return error_response(500, f'库存出库失败: {str(e)}')

@inventory_bp.route('/statistics/', methods=['GET'])
@jwt_required()
def get_inventory_statistics():
    """
    获取库存统计信息
    功能描述：获取库存的统计数据，包括总数、预警、缺货等
    入参：无
    返回参数：库存统计信息
    url地址：/api/v1/inventory/statistics
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())

        # 基础统计
        total_items = InventoryItem.query.count()
        total_stock = db.session.query(func.sum(InventoryItem.current_stock)).scalar() or 0

        # 状态统计
        normal_count = InventoryItem.query.filter_by(status='normal').count()
        warning_count = InventoryItem.query.filter_by(status='warning').count()
        shortage_count = InventoryItem.query.filter_by(status='shortage').count()
        expired_count = InventoryItem.query.filter_by(status='expired').count()

        # 库存总值计算
        total_value = db.session.query(
            func.sum(InventoryItem.current_stock * InventoryItem.unit_price)
        ).filter(InventoryItem.unit_price.isnot(None)).scalar() or 0

        # 按类别统计
        category_stats = db.session.query(
            Material.category,
            func.count(InventoryItem.id).label('count'),
            func.sum(InventoryItem.current_stock).label('stock')
        ).join(Material).group_by(Material.category).all()

        # 按位置统计
        location_stats = db.session.query(
            InventoryItem.location,
            func.count(InventoryItem.id).label('count'),
            func.sum(InventoryItem.current_stock).label('stock')
        ).group_by(InventoryItem.location).all()

        # 周转率计算（简化版本，实际应该基于历史数据）
        # 这里使用一个估算值
        turnover_rate = 0.78  # 78%

        statistics = {
            'overview': {
                'total_items': total_items,
                'total_stock': int(total_stock),
                'total_value': float(total_value),
                'turnover_rate': turnover_rate
            },
            'status_distribution': {
                'normal': normal_count,
                'warning': warning_count,
                'shortage': shortage_count,
                'expired': expired_count
            },
            'category_stats': [
                {
                    'category': stat.category,
                    'item_count': stat.count,
                    'stock_count': int(stat.stock or 0)
                }
                for stat in category_stats
            ],
            'location_stats': [
                {
                    'location': stat.location,
                    'item_count': stat.count,
                    'stock_count': int(stat.stock or 0)
                }
                for stat in location_stats
            ]
        }

        return success_response(statistics, '库存统计信息获取成功')

    except Exception as e:
        return error_response(500, f'获取库存统计信息失败: {str(e)}')

@inventory_bp.route('/items/<int:item_id>/transfer/', methods=['POST'])
@jwt_required()
def transfer_inventory(item_id):
    """
    库存调拨操作
    功能描述：将库存从一个位置调拨到另一个位置
    入参：{ quantity: int, target_location: string, notes: string }
    返回参数：调拨结果信息
    url地址：/api/v1/inventory/items/{item_id}/transfer
    请求方式：POST
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        # 验证必填字段
        if not data or 'quantity' not in data or 'target_location' not in data:
            return error_response(400, '调拨数量和目标位置不能为空')

        quantity = data.get('quantity')
        target_location = data.get('target_location').strip()
        notes = data.get('notes', '')

        # 验证数量
        if not isinstance(quantity, int) or quantity <= 0:
            return error_response(400, '调拨数量必须是正整数')

        # 验证目标位置
        if not target_location:
            return error_response(400, '目标位置不能为空')

        # 查询源库存项
        source_item = InventoryItem.query.get(item_id)
        if not source_item:
            return not_found_response('库存项')

        # 检查库存是否充足
        if source_item.current_stock < quantity:
            return error_response(400, f'库存不足，当前库存：{source_item.current_stock}，调拨数量：{quantity}')

        # 检查是否调拨到相同位置
        if source_item.location == target_location:
            return error_response(400, '不能调拨到相同位置')

        # 查找或创建目标位置的库存项
        target_item = InventoryItem.query.filter_by(
            material_id=source_item.material_id,
            location=target_location
        ).first()

        if target_item:
            # 目标位置已有该航材，增加库存
            target_item.current_stock += quantity
            target_item.update_status()
            target_item.last_updated = datetime.utcnow()
        else:
            # 目标位置没有该航材，创建新的库存项
            target_item = InventoryItem(
                material_id=source_item.material_id,
                location=target_location,
                current_stock=quantity,
                safety_stock=source_item.safety_stock,
                unit_price=source_item.unit_price,
                condition_code=source_item.condition_code,
                is_shareable=source_item.is_shareable,
                batch_number=source_item.batch_number,
                expiry_date=source_item.expiry_date,
                supplier_id=source_item.supplier_id
            )
            target_item.update_status()
            db.session.add(target_item)

        # 减少源位置库存
        source_item.current_stock -= quantity
        source_item.update_status()
        source_item.last_updated = datetime.utcnow()

        # 保存到数据库
        db.session.commit()

        # 创建通知
        notification = Notification(
            user_id=current_user_id,
            title='库存调拨成功',
            content=f'航材 {source_item.material.part_name} 已从 {source_item.location} 调拨 {quantity} 件到 {target_location}',
            type='inventory',
            is_read=False
        )
        db.session.add(notification)
        db.session.commit()

        return success_response({
            'source_item': source_item.to_dict(),
            'target_item': target_item.to_dict(),
            'transfer_quantity': quantity
        }, '库存调拨成功')

    except Exception as e:
        db.session.rollback()
        return error_response(500, f'库存调拨失败: {str(e)}')