#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 权限管理API路由
版本: 1.0
创建时间: 2025-07-19

处理权限相关的所有API请求，包括权限查询、分配、撤销等操作
第一阶段开发：基础权限管理功能
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, desc, or_

from models import db, User, Permission, UserPermission, DataAccessLog
from utils.decorators import (
    validate_json, require_admin, require_permission, 
    audit_action, require_internal_staff
)
from utils.response import (
    success_response, error_response, paginated_response, 
    created_response, not_found_response, updated_response
)
from services.data_isolation_service import DataIsolationService

# 创建蓝图
permissions_bp = Blueprint('permissions', __name__)

@permissions_bp.route('/user/permissions', methods=['GET'])
@jwt_required()
@audit_action('view_user_permissions', 'permissions')
def get_user_permissions():
    """
    获取当前用户权限列表
    功能描述：获取当前登录用户的所有权限信息
    入参：无
    返回参数：用户权限列表和角色信息
    url地址：/api/v1/permissions/user/permissions
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return not_found_response('用户')
        
        # 获取用户的权限列表
        user_permissions = user.get_role_permissions()
        
        # 获取明确分配的权限
        explicit_permissions = db.session.query(
            UserPermission, Permission
        ).join(
            Permission, UserPermission.permission_id == Permission.id
        ).filter(
            UserPermission.user_id == user.id,
            UserPermission.is_active == True
        ).all()
        
        explicit_permission_list = []
        for user_perm, permission in explicit_permissions:
            explicit_permission_list.append({
                'id': permission.id,
                'code': permission.code,
                'name': permission.name,
                'description': permission.description,
                'category': permission.category,
                'granted_at': user_perm.granted_at.isoformat(),
                'expires_at': user_perm.expires_at.isoformat() if user_perm.expires_at else None
            })
        
        response_data = {
            'user_info': {
                'id': user.id,
                'username': user.username,
                'user_type': user.user_type,
                'company_type': user.company_type,
                'permission_level': user.permission_level,
                'company_name': user.company_name,
                'is_admin': user.is_admin(),
                'is_internal_staff': user.is_internal_staff(),
                'can_cross_company_access': user.can_cross_company_access()
            },
            'role_permissions': user_permissions,
            'explicit_permissions': explicit_permission_list,
            'accessible_companies': DataIsolationService.get_accessible_companies(user)
        }
        
        return success_response(response_data, '用户权限获取成功')
        
    except Exception as e:
        return error_response(500, f'获取用户权限失败: {str(e)}')


@permissions_bp.route('/list', methods=['GET'])
@require_permission('view_all_permissions')
@audit_action('view_permissions_list', 'permissions')
def get_permissions_list():
    """
    获取系统权限列表
    功能描述：获取系统中所有可用的权限列表
    入参：{ category: string, page: int, size: int }
    返回参数：分页的权限列表数据
    url地址：/api/v1/permissions/list
    请求方式：GET
    """
    try:
        # 获取查询参数
        category = request.args.get('category', '').strip()
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 验证分页参数
        if page < 1:
            return error_response(400, '页码必须大于0')
        if size < 1 or size > 100:
            return error_response(400, '每页数量必须在1-100之间')
        
        # 构建查询
        query = Permission.query
        
        # 分类筛选
        if category:
            query = query.filter(Permission.category == category)
        
        # 只显示激活的权限
        query = query.filter(Permission.is_active == True)
        
        # 排序
        query = query.order_by(Permission.category, Permission.name)
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=size,
            error_out=False
        )
        
        # 格式化数据
        permissions = []
        for permission in pagination.items:
            permissions.append(permission.to_dict())
        
        # 获取权限分类统计
        category_stats = db.session.query(
            Permission.category,
            func.count(Permission.id).label('count')
        ).filter(
            Permission.is_active == True
        ).group_by(
            Permission.category
        ).all()
        
        categories = [{'category': cat, 'count': count} for cat, count in category_stats]
        
        return paginated_response(
            permissions, 
            pagination.total, 
            page, 
            size,
            '权限列表获取成功',
            extra_data={'categories': categories}
        )
        
    except Exception as e:
        return error_response(500, f'获取权限列表失败: {str(e)}')


@permissions_bp.route('/assign', methods=['POST'])
@require_admin
@validate_json(['user_id', 'permission_codes'])
@audit_action('assign_permissions', 'permissions')
def assign_permissions():
    """
    分配权限给用户
    功能描述：管理员为用户分配特定权限
    入参：{ user_id: int, permission_codes: [string], expires_at: string }
    返回参数：分配结果信息
    url地址：/api/v1/permissions/assign
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        user_id = data.get('user_id')
        permission_codes = data.get('permission_codes', [])
        expires_at_str = data.get('expires_at')
        
        # 验证目标用户
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response('目标用户')
        
        # 验证权限代码
        permissions = Permission.query.filter(
            Permission.code.in_(permission_codes),
            Permission.is_active == True
        ).all()
        
        if len(permissions) != len(permission_codes):
            found_codes = [p.code for p in permissions]
            missing_codes = [code for code in permission_codes if code not in found_codes]
            return error_response(400, f'权限不存在: {", ".join(missing_codes)}')
        
        # 处理过期时间
        expires_at = None
        if expires_at_str:
            from datetime import datetime
            try:
                expires_at = datetime.strptime(expires_at_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return error_response(400, '过期时间格式不正确，应为YYYY-MM-DD HH:MM:SS')
        
        # 分配权限
        assigned_permissions = []
        for permission in permissions:
            # 检查是否已经分配
            existing = UserPermission.query.filter(
                UserPermission.user_id == user_id,
                UserPermission.permission_id == permission.id
            ).first()
            
            if existing:
                # 更新现有权限
                existing.is_active = True
                existing.granted_by = current_user_id
                existing.expires_at = expires_at
                assigned_permissions.append(permission.code)
            else:
                # 创建新权限分配
                user_permission = UserPermission(
                    user_id=user_id,
                    permission_id=permission.id,
                    granted_by=current_user_id,
                    expires_at=expires_at
                )
                db.session.add(user_permission)
                assigned_permissions.append(permission.code)
        
        db.session.commit()
        
        return success_response({
            'user_id': user_id,
            'assigned_permissions': assigned_permissions,
            'expires_at': expires_at.isoformat() if expires_at else None
        }, f'成功为用户分配 {len(assigned_permissions)} 个权限')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'分配权限失败: {str(e)}')


@permissions_bp.route('/revoke', methods=['POST'])
@require_admin
@validate_json(['user_id', 'permission_codes'])
@audit_action('revoke_permissions', 'permissions')
def revoke_permissions():
    """
    撤销用户权限
    功能描述：管理员撤销用户的特定权限
    入参：{ user_id: int, permission_codes: [string] }
    返回参数：撤销结果信息
    url地址：/api/v1/permissions/revoke
    请求方式：POST
    """
    try:
        data = request.get_json()
        
        user_id = data.get('user_id')
        permission_codes = data.get('permission_codes', [])
        
        # 验证目标用户
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response('目标用户')
        
        # 验证权限代码
        permissions = Permission.query.filter(
            Permission.code.in_(permission_codes)
        ).all()
        
        permission_ids = [p.id for p in permissions]
        
        # 撤销权限
        revoked_count = UserPermission.query.filter(
            UserPermission.user_id == user_id,
            UserPermission.permission_id.in_(permission_ids),
            UserPermission.is_active == True
        ).update(
            {'is_active': False},
            synchronize_session=False
        )
        
        db.session.commit()
        
        return success_response({
            'user_id': user_id,
            'revoked_permissions': permission_codes,
            'revoked_count': revoked_count
        }, f'成功撤销 {revoked_count} 个权限')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'撤销权限失败: {str(e)}')


@permissions_bp.route('/users/<int:user_id>/permissions', methods=['GET'])
@require_permission('user_management')
@audit_action('view_user_permissions_admin', 'permissions')
def get_user_permissions_admin(user_id):
    """
    管理员查看用户权限
    功能描述：管理员查看指定用户的权限信息
    入参：user_id (路径参数)
    返回参数：用户权限详细信息
    url地址：/api/v1/permissions/users/{user_id}/permissions
    请求方式：GET
    """
    try:
        # 查找用户
        user = User.query.get(user_id)
        if not user:
            return not_found_response('用户')
        
        # 获取用户的角色权限
        role_permissions = user.get_role_permissions()
        
        # 获取明确分配的权限
        explicit_permissions = db.session.query(
            UserPermission, Permission, User.username
        ).join(
            Permission, UserPermission.permission_id == Permission.id
        ).outerjoin(
            User, UserPermission.granted_by == User.id
        ).filter(
            UserPermission.user_id == user_id
        ).order_by(
            desc(UserPermission.granted_at)
        ).all()
        
        explicit_permission_list = []
        for user_perm, permission, grantor_username in explicit_permissions:
            explicit_permission_list.append({
                'id': permission.id,
                'code': permission.code,
                'name': permission.name,
                'description': permission.description,
                'category': permission.category,
                'granted_by': grantor_username,
                'granted_at': user_perm.granted_at.isoformat(),
                'expires_at': user_perm.expires_at.isoformat() if user_perm.expires_at else None,
                'is_active': user_perm.is_active
            })
        
        # 获取最近的访问日志
        recent_access_logs = DataAccessLog.query.filter(
            DataAccessLog.user_id == user_id
        ).order_by(
            desc(DataAccessLog.created_at)
        ).limit(10).all()
        
        access_logs = []
        for log in recent_access_logs:
            access_logs.append(log.to_dict())
        
        response_data = {
            'user_info': user.to_dict(),
            'role_permissions': role_permissions,
            'explicit_permissions': explicit_permission_list,
            'accessible_companies': DataIsolationService.get_accessible_companies(user),
            'recent_access_logs': access_logs
        }
        
        return success_response(response_data, '用户权限信息获取成功')
        
    except Exception as e:
        return error_response(500, f'获取用户权限信息失败: {str(e)}')


@permissions_bp.route('/access-logs', methods=['GET'])
@require_internal_staff
@audit_action('view_access_logs', 'permissions')
def get_access_logs():
    """
    获取数据访问日志
    功能描述：内部员工查看数据访问日志
    入参：{ user_id: int, action: string, success: bool, page: int, size: int }
    返回参数：分页的访问日志数据
    url地址：/api/v1/permissions/access-logs
    请求方式：GET
    """
    try:
        # 获取查询参数
        user_id = request.args.get('user_id', type=int)
        action = request.args.get('action', '').strip()
        success = request.args.get('success', type=bool)
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 验证分页参数
        if page < 1:
            return error_response(400, '页码必须大于0')
        if size < 1 or size > 100:
            return error_response(400, '每页数量必须在1-100之间')
        
        # 构建查询
        query = db.session.query(DataAccessLog, User.username).join(
            User, DataAccessLog.user_id == User.id
        )
        
        # 筛选条件
        if user_id:
            query = query.filter(DataAccessLog.user_id == user_id)
        
        if action:
            query = query.filter(DataAccessLog.action.contains(action))
        
        if success is not None:
            query = query.filter(DataAccessLog.success == success)
        
        # 排序
        query = query.order_by(desc(DataAccessLog.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=size,
            error_out=False
        )
        
        # 格式化数据
        logs = []
        for log, username in pagination.items:
            log_data = log.to_dict()
            log_data['username'] = username
            logs.append(log_data)
        
        return paginated_response(
            logs, 
            pagination.total, 
            page, 
            size,
            '访问日志获取成功'
        )
        
    except Exception as e:
        return error_response(500, f'获取访问日志失败: {str(e)}')