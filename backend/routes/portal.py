"""
航材共享保障平台 - 门户首页API路由
功能：门户首页专业功能接口，包括行业动态、热门航材推荐、平台统计等
作者：Claude AI Assistant
创建时间：2025-07-18
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from utils.response import success_response, error_response
from utils.decorators import handle_exceptions
from models import db, Material, Order, User, Demand, InventoryItem
from datetime import datetime, timedelta
from sqlalchemy import func, desc
import json

# 创建门户首页蓝图
portal_bp = Blueprint('portal', __name__)


@portal_bp.route('/portal/statistics', methods=['GET'])
@handle_exceptions
def get_platform_statistics():
    """
    获取平台统计数据
    功能描述：获取平台核心统计数据，用于首页展示
    入参：无
    返回参数：{
        "total_users": 1500,
        "total_materials": 50000,
        "total_orders": 8000,
        "total_transaction_amount": 500000000,
        "monthly_growth": 15.5,
        "active_suppliers": 200,
        "completed_orders": 7500,
        "avg_response_time": 2.5
    }
    url地址：/api/portal/statistics
    请求方式：GET
    """
    try:
        # 获取用户统计
        total_users = User.query.filter_by(status='active').count()
        
        # 获取航材统计
        total_materials = Material.query.count()
        
        # 获取订单统计
        total_orders = Order.query.count()
        completed_orders = Order.query.filter_by(status='completed').count()
        
        # 获取交易金额统计
        total_transaction_amount = db.session.query(
            func.sum(Order.total_amount)
        ).filter(
            Order.status == 'completed'
        ).scalar() or 0
        
        # 获取活跃供应商统计
        active_suppliers = User.query.filter_by(
            user_type='supplier',
            status='active'
        ).count()
        
        # 计算月度增长率（以订单为例）
        current_month = datetime.now().replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        
        current_month_orders = Order.query.filter(
            Order.created_at >= current_month
        ).count()
        
        last_month_orders = Order.query.filter(
            Order.created_at >= last_month,
            Order.created_at < current_month
        ).count()
        
        monthly_growth = 0
        if last_month_orders > 0:
            monthly_growth = ((current_month_orders - last_month_orders) / last_month_orders) * 100
        
        # 计算平均响应时间（小时）
        avg_response_time = 2.5  # 模拟数据，实际应根据订单处理时间计算
        
        statistics = {
            'total_users': total_users,
            'total_materials': total_materials,
            'total_orders': total_orders,
            'total_transaction_amount': float(total_transaction_amount),
            'monthly_growth': round(monthly_growth, 1),
            'active_suppliers': active_suppliers,
            'completed_orders': completed_orders,
            'avg_response_time': avg_response_time
        }
        
        return success_response(statistics)
        
    except Exception as e:
        current_app.logger.error(f"获取平台统计数据失败：{str(e)}")
        return error_response(f"获取平台统计数据失败：{str(e)}")


@portal_bp.route('/portal/hot-materials', methods=['GET'])
@handle_exceptions
def get_hot_materials():
    """
    获取热门航材推荐
    功能描述：基于搜索频率和交易量获取热门航材
    入参：{ limit: 10 }
    返回参数：{
        "materials": [
            {
                "id": 1,
                "part_number": "CFM56-001",
                "name": "发动机燃油喷嘴",
                "category": "发动机",
                "manufacturer": "CFM International",
                "search_count": 1250,
                "order_count": 85,
                "avg_price": 125000,
                "availability": "现货",
                "image_url": "/images/发动机燃油喷嘴.jpg",
                "hot_score": 95.5
            }
        ]
    }
    url地址：/api/portal/hot-materials
    请求方式：GET
    """
    try:
        limit = int(request.args.get('limit', 10))
        
        # 获取热门航材数据
        # 这里使用模拟数据，实际应该基于搜索频率和交易量计算
        hot_materials = [
            {
                'id': 1,
                'part_number': 'CFM56-001',
                'name': '发动机燃油喷嘴',
                'category': '发动机',
                'manufacturer': 'CFM International',
                'search_count': 1250,
                'order_count': 85,
                'avg_price': 125000,
                'availability': '现货',
                'image_url': '/images/发动机燃油喷嘴.jpg',
                'hot_score': 95.5
            },
            {
                'id': 2,
                'part_number': 'B737-002',
                'name': '波音737机轮',
                'category': '起落架',
                'manufacturer': 'Boeing',
                'search_count': 980,
                'order_count': 62,
                'avg_price': 85000,
                'availability': '现货',
                'image_url': '/images/机轮.jpg',
                'hot_score': 88.2
            },
            {
                'id': 3,
                'part_number': 'A320-003',
                'name': '空客A320航电设备',
                'category': '航电',
                'manufacturer': 'Airbus',
                'search_count': 876,
                'order_count': 45,
                'avg_price': 215000,
                'availability': '预订',
                'image_url': '/images/航电1.jpg',
                'hot_score': 82.3
            },
            {
                'id': 4,
                'part_number': 'GE90-004',
                'name': 'GE90发动机叶片',
                'category': '发动机',
                'manufacturer': 'General Electric',
                'search_count': 654,
                'order_count': 38,
                'avg_price': 315000,
                'availability': '现货',
                'image_url': '/images/发动机7.jpg',
                'hot_score': 79.8
            },
            {
                'id': 5,
                'part_number': 'PW4000-005',
                'name': 'PW4000发动机齿轮',
                'category': '发动机',
                'manufacturer': 'Pratt & Whitney',
                'search_count': 543,
                'order_count': 28,
                'avg_price': 89000,
                'availability': '现货',
                'image_url': '/images/齿轮.jpg',
                'hot_score': 75.6
            },
            {
                'id': 6,
                'part_number': 'B777-006',
                'name': '波音777机翼组件',
                'category': '机体',
                'manufacturer': 'Boeing',
                'search_count': 432,
                'order_count': 22,
                'avg_price': 1250000,
                'availability': '预订',
                'image_url': '/images/飞机机翼.jpg',
                'hot_score': 71.2
            },
            {
                'id': 7,
                'part_number': 'A330-007',
                'name': '空客A330液压系统',
                'category': '系统',
                'manufacturer': 'Airbus',
                'search_count': 398,
                'order_count': 19,
                'avg_price': 165000,
                'availability': '现货',
                'image_url': '/images/发动机10.jpg',
                'hot_score': 68.5
            },
            {
                'id': 8,
                'part_number': 'RR-008',
                'name': '罗罗发动机控制器',
                'category': '发动机',
                'manufacturer': 'Rolls-Royce',
                'search_count': 365,
                'order_count': 15,
                'avg_price': 245000,
                'availability': '现货',
                'image_url': '/images/发动机8.jpg',
                'hot_score': 65.3
            },
            {
                'id': 9,
                'part_number': 'B787-009',
                'name': '波音787复合材料面板',
                'category': '机体',
                'manufacturer': 'Boeing',
                'search_count': 287,
                'order_count': 12,
                'avg_price': 85000,
                'availability': '预订',
                'image_url': '/images/飞机7.jpg',
                'hot_score': 62.8
            },
            {
                'id': 10,
                'part_number': 'LEAP-010',
                'name': 'LEAP发动机风扇叶片',
                'category': '发动机',
                'manufacturer': 'CFM International',
                'search_count': 256,
                'order_count': 10,
                'avg_price': 125000,
                'availability': '现货',
                'image_url': '/images/发动机9.jpg',
                'hot_score': 58.9
            }
        ]
        
        # 限制返回数量
        limited_materials = hot_materials[:limit]
        
        return success_response({'materials': limited_materials})
        
    except Exception as e:
        current_app.logger.error(f"获取热门航材失败：{str(e)}")
        return error_response(f"获取热门航材失败：{str(e)}")


@portal_bp.route('/portal/industry-news', methods=['GET'])
@handle_exceptions
def get_industry_news():
    """
    获取行业动态新闻
    功能描述：获取航空行业最新新闻和动态
    入参：{ page: 1, per_page: 10, category: "industry" }
    返回参数：{
        "news": [
            {
                "id": 1,
                "title": "中国商飞C919获得欧洲航空安全局认证",
                "summary": "中国商飞C919客机正式获得欧洲航空安全局（EASA）的型号认证，标志着中国民用航空制造业的重要里程碑。",
                "category": "industry",
                "source": "航空工业周刊",
                "publish_date": "2025-07-18",
                "image_url": "/images/飞机6.jpg",
                "read_count": 1250,
                "importance": "high"
            }
        ],
        "total": 50,
        "page": 1,
        "per_page": 10
    }
    url地址：/api/portal/industry-news
    请求方式：GET
    """
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        category = request.args.get('category', 'industry')
        
        # 模拟行业新闻数据
        all_news = [
            {
                'id': 1,
                'title': '中国商飞C919获得欧洲航空安全局认证',
                'summary': '中国商飞C919客机正式获得欧洲航空安全局（EASA）的型号认证，标志着中国民用航空制造业的重要里程碑。',
                'category': 'industry',
                'source': '航空工业周刊',
                'publish_date': '2025-07-18',
                'image_url': '/images/飞机6.jpg',
                'read_count': 1250,
                'importance': 'high'
            },
            {
                'id': 2,
                'title': '波音737 MAX系列飞机全面复飞获批',
                'summary': '经过全面安全审查和系统升级，波音737 MAX系列飞机获得全球航空监管机构批准，正式恢复商业运营。',
                'category': 'industry',
                'source': '国际航空报',
                'publish_date': '2025-07-17',
                'image_url': '/images/飞机3.jpg',
                'read_count': 980,
                'importance': 'high'
            },
            {
                'id': 3,
                'title': '空客A350-1000完成首次跨太平洋试飞',
                'summary': '空客A350-1000宽体客机成功完成首次跨太平洋试飞，验证了其在长航程运营中的性能和可靠性。',
                'category': 'industry',
                'source': '航空技术杂志',
                'publish_date': '2025-07-16',
                'image_url': '/images/飞机4.jpg',
                'read_count': 756,
                'importance': 'medium'
            },
            {
                'id': 4,
                'title': '全球航材市场预计2025年增长12%',
                'summary': '根据最新市场分析报告，全球航材市场预计在2025年实现12%的增长，主要受益于后疫情时代的航空业复苏。',
                'category': 'market',
                'source': '航材市场研究',
                'publish_date': '2025-07-15',
                'image_url': '/images/飞机仓库.jpg',
                'read_count': 654,
                'importance': 'medium'
            },
            {
                'id': 5,
                'title': '新一代发动机LEAP-1A通过10万小时测试',
                'summary': 'CFM International的LEAP-1A发动机成功通过10万小时耐久性测试，展现出卓越的可靠性和燃油效率。',
                'category': 'technology',
                'source': '发动机技术周报',
                'publish_date': '2025-07-14',
                'image_url': '/images/发动机1.jpg',
                'read_count': 543,
                'importance': 'medium'
            },
            {
                'id': 6,
                'title': '数字化航材管理系统助力降本增效',
                'summary': '新一代数字化航材管理系统通过AI算法优化库存管理，帮助航空公司平均降低25%的库存成本。',
                'category': 'technology',
                'source': '智能制造月刊',
                'publish_date': '2025-07-13',
                'image_url': '/images/飞机及内部.jpg',
                'read_count': 432,
                'importance': 'medium'
            },
            {
                'id': 7,
                'title': '绿色航空燃料SAF商业化应用加速',
                'summary': '可持续航空燃料（SAF）在全球范围内的商业化应用步伐加快，多家航空公司宣布大规模采购计划。',
                'category': 'environmental',
                'source': '绿色航空杂志',
                'publish_date': '2025-07-12',
                'image_url': '/images/飞机5.jpg',
                'read_count': 398,
                'importance': 'high'
            },
            {
                'id': 8,
                'title': '航空维修MRO市场迎来数字化转型',
                'summary': '全球航空维修MRO市场正在经历数字化转型，预测性维护和智能诊断技术应用日益普及。',
                'category': 'maintenance',
                'source': '维修技术前沿',
                'publish_date': '2025-07-11',
                'image_url': '/images/飞机拆解1.jpg',
                'read_count': 365,
                'importance': 'medium'
            },
            {
                'id': 9,
                'title': '国际航空货运需求强劲复苏',
                'summary': '随着全球贸易复苏，国际航空货运需求呈现强劲增长态势，货运航空公司纷纷扩大机队规模。',
                'category': 'logistics',
                'source': '物流航空周报',
                'publish_date': '2025-07-10',
                'image_url': '/images/飞机2.jpg',
                'read_count': 287,
                'importance': 'medium'
            },
            {
                'id': 10,
                'title': '新型复合材料在航空制造中的应用突破',
                'summary': '新一代碳纤维复合材料在航空制造中实现重大突破，为飞机减重和性能提升提供新的解决方案。',
                'category': 'technology',
                'source': '材料科学期刊',
                'publish_date': '2025-07-09',
                'image_url': '/images/飞机拆解2.jpg',
                'read_count': 256,
                'importance': 'medium'
            }
        ]
        
        # 按类别筛选
        if category != 'all':
            filtered_news = [news for news in all_news if news['category'] == category]
        else:
            filtered_news = all_news
        
        # 计算分页
        total = len(filtered_news)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_news = filtered_news[start:end]
        
        return success_response({
            'news': paginated_news,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        current_app.logger.error(f"获取行业动态失败：{str(e)}")
        return error_response(f"获取行业动态失败：{str(e)}")


@portal_bp.route('/portal/success-cases', methods=['GET'])
@handle_exceptions
def get_success_cases():
    """
    获取成功案例
    功能描述：获取平台促成的典型交易案例
    入参：{ limit: 6 }
    返回参数：{
        "cases": [
            {
                "id": 1,
                "title": "东方航空紧急AOG航材保障",
                "description": "在48小时内为东方航空提供紧急航材，确保航班正常运行。",
                "client": "东方航空",
                "material_type": "发动机部件",
                "save_cost": 1200000,
                "response_time": "48小时",
                "success_rate": "100%",
                "image_url": "/images/发动机2.jpg",
                "case_type": "aog"
            }
        ]
    }
    url地址：/api/portal/success-cases
    请求方式：GET
    """
    try:
        limit = int(request.args.get('limit', 6))
        
        # 模拟成功案例数据
        success_cases = [
            {
                'id': 1,
                'title': '东方航空紧急AOG航材保障',
                'description': '在48小时内为东方航空提供紧急航材，确保航班正常运行，避免了大规模航班延误。',
                'client': '东方航空',
                'material_type': '发动机部件',
                'save_cost': 1200000,
                'response_time': '48小时',
                'success_rate': '100%',
                'image_url': '/images/发动机2.jpg',
                'case_type': 'aog'
            },
            {
                'id': 2,
                'title': '南方航空周转件优化方案',
                'description': '通过智能周转件管理，帮助南方航空降低35%的库存成本，提高资金周转效率。',
                'client': '南方航空',
                'material_type': '起落架组件',
                'save_cost': 2500000,
                'response_time': '7天',
                'success_rate': '98%',
                'image_url': '/images/机轮2.jpg',
                'case_type': 'cost_saving'
            },
            {
                'id': 3,
                'title': '海南航空批量采购项目',
                'description': '为海南航空提供批量航材采购服务，实现年度采购成本节约20%。',
                'client': '海南航空',
                'material_type': '航电设备',
                'save_cost': 1800000,
                'response_time': '30天',
                'success_rate': '95%',
                'image_url': '/images/航电2.jpg',
                'case_type': 'bulk_purchase'
            },
            {
                'id': 4,
                'title': '深圳航空维修外包服务',
                'description': '为深圳航空提供专业的航材维修外包服务，缩短50%的维修周期。',
                'client': '深圳航空',
                'material_type': '发动机组件',
                'save_cost': 950000,
                'response_time': '15天',
                'success_rate': '99%',
                'image_url': '/images/发动机3.jpg',
                'case_type': 'maintenance'
            },
            {
                'id': 5,
                'title': '厦门航空共享库存管理',
                'description': '建立共享库存管理系统，为厦门航空提供实时库存共享服务。',
                'client': '厦门航空',
                'material_type': '机体结构件',
                'save_cost': 1350000,
                'response_time': '即时',
                'success_rate': '97%',
                'image_url': '/images/飞机拆解3.jpg',
                'case_type': 'inventory_sharing'
            },
            {
                'id': 6,
                'title': '四川航空全流程数字化',
                'description': '为四川航空提供全流程数字化航材管理解决方案，提升运营效率。',
                'client': '四川航空',
                'material_type': '综合航材',
                'save_cost': 2200000,
                'response_time': '90天',
                'success_rate': '96%',
                'image_url': '/images/飞机1.jpg',
                'case_type': 'digital_transformation'
            }
        ]
        
        # 限制返回数量
        limited_cases = success_cases[:limit]
        
        return success_response({'cases': limited_cases})
        
    except Exception as e:
        current_app.logger.error(f"获取成功案例失败：{str(e)}")
        return error_response(f"获取成功案例失败：{str(e)}")


@portal_bp.route('/portal/market-analysis', methods=['GET'])
@handle_exceptions
def get_market_analysis():
    """
    获取市场分析数据
    功能描述：获取航材市场趋势分析数据
    入参：{ period: "monthly" }
    返回参数：{
        "analysis": {
            "market_size": 58500000000,
            "growth_rate": 12.5,
            "hot_categories": ["发动机", "航电", "起落架"],
            "regional_data": [],
            "price_trends": [],
            "demand_forecast": []
        }
    }
    url地址：/api/portal/market-analysis
    请求方式：GET
    """
    try:
        period = request.args.get('period', 'monthly')
        
        # 模拟市场分析数据
        market_analysis = {
            'market_size': 58500000000,  # 全球航材市场规模（美元）
            'growth_rate': 12.5,  # 年增长率（%）
            'hot_categories': ['发动机', '航电', '起落架', '机体结构', '液压系统'],
            'regional_data': [
                {'region': '亚太', 'market_share': 35.2, 'growth': 15.8},
                {'region': '北美', 'market_share': 28.6, 'growth': 9.2},
                {'region': '欧洲', 'market_share': 22.1, 'growth': 11.5},
                {'region': '其他', 'market_share': 14.1, 'growth': 8.3}
            ],
            'price_trends': [
                {'category': '发动机', 'trend': 'up', 'change': 8.5},
                {'category': '航电', 'trend': 'up', 'change': 12.3},
                {'category': '起落架', 'trend': 'stable', 'change': 2.1},
                {'category': '机体结构', 'trend': 'up', 'change': 6.8},
                {'category': '液压系统', 'trend': 'down', 'change': -3.2}
            ],
            'demand_forecast': [
                {'month': '2025-07', 'demand': 850},
                {'month': '2025-08', 'demand': 920},
                {'month': '2025-09', 'demand': 1050},
                {'month': '2025-10', 'demand': 1180},
                {'month': '2025-11', 'demand': 1280},
                {'month': '2025-12', 'demand': 1350}
            ]
        }
        
        return success_response({'analysis': market_analysis})
        
    except Exception as e:
        current_app.logger.error(f"获取市场分析失败：{str(e)}")
        return error_response(f"获取市场分析失败：{str(e)}")


# 注册蓝图错误处理器
@portal_bp.errorhandler(Exception)
def handle_portal_error(error):
    """门户模块统一错误处理"""
    current_app.logger.error(f"门户模块发生错误：{str(error)}")
    return error_response(f"门户操作失败：{str(error)}")