#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 需求管理API路由
版本: 1.0
创建时间: 2025-01-15

处理需求相关的所有API请求，包括发布需求、需求匹配、响应需求等操作
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_, desc, func

from models import db, Demand, DemandResponse, DemandMatch, DemandHistory, Material, InventoryItem, User, Notification
import json
from utils.decorators import (
    validate_json, require_active_user, require_permission,
    require_ownership_enhanced, data_isolation, audit_action
)
from services.data_isolation_service import DataIsolationService
from utils.response import success_response, error_response
from utils.validators import generate_demand_number

# 创建蓝图
demands_bp = Blueprint('demands', __name__)

@demands_bp.route('/', methods=['GET'])
@require_permission('view_own_data')
@data_isolation(Demand, 'requester_id')
@audit_action('view_demands_list', 'demand')
def get_demands(current_user=None):
    """
    获取需求列表
    功能描述：获取用户的需求列表，支持分页和过滤
    入参：page(页码), per_page(每页数量), status(状态过滤), type(类型过滤)
    返回参数：需求列表
    url地址：/api/v1/demands
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status_filter = request.args.get('status', '')
        type_filter = request.args.get('type', '')
        
        # 构建查询
        query = Demand.query.filter_by(requester_id=current_user_id)
        
        # 应用过滤条件
        if status_filter:
            query = query.filter_by(status=status_filter)
        if type_filter:
            query = query.filter_by(type=type_filter)
        
        # 排序（最新的在前）
        query = query.order_by(desc(Demand.created_at))
        
        # 分页
        paginated_demands = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # 格式化需求数据
        demands_data = []
        for demand in paginated_demands.items:
            # 解析联系信息JSON
            contact_info = {}
            if demand.contact_info:
                try:
                    contact_info = json.loads(demand.contact_info)
                except:
                    contact_info = {}
            
            demand_data = {
                'id': demand.id,
                'demand_id': demand.demand_number,
                'title': demand.title or demand.ensure_title(),  # 添加标题字段
                'material_info': {
                    'name': demand.material_name,
                    'part_number': demand.part_number,
                    'aircraft_type': demand.aircraft_type,
                },
                'requirements': {
                    'quantity': demand.quantity,
                    'description': demand.description,
                    'delivery_location': demand.delivery_location,
                    'expected_delivery_date': demand.delivery_time.isoformat() if demand.delivery_time else None,
                    'expected_price': demand.budget_range,
                    'acceptable_conditions': demand.quality_requirements.split(', ') if demand.quality_requirements else []
                },
                'contact_info': contact_info,
                'type': demand.type,
                'priority': demand.priority,
                'status': demand.status,
                'created_at': demand.created_at.isoformat(),
                'updated_at': demand.updated_at.isoformat() if demand.updated_at else None,
                'expires_at': demand.expires_at.isoformat() if demand.expires_at else None
            }
            demands_data.append(demand_data)
        
        # 返回结果
        result = {
            'demands': demands_data,
            'total': paginated_demands.total,
            'page': page,
            'per_page': per_page,
            'pages': paginated_demands.pages,
            'has_next': paginated_demands.has_next,
            'has_prev': paginated_demands.has_prev
        }
        
        return success_response(result, 'Demands list retrieved successfully')
        
    except Exception as e:
        return error_response(500, f'Failed to retrieve demands list: {str(e)}')


@demands_bp.route('/', methods=['POST'])
@require_permission('publish_demand')
@validate_json(['material_info'])
@audit_action('create_demand', 'demand')
def create_demand():
    """
    创建需求
    功能描述：发布新的航材需求，支持周转件、消耗件、维修、AOG等类型
    入参：{ 
        type: string, 
        priority: string, 
        material_info: object, 
        requirements: object, 
        contact_info: object 
    }
    返回参数：{ demand_id: string, message: string, matching_results: array }
    url地址：/api/v1/demands
    请求方式：POST
    """
    try:
        data = request.get_json()
        current_user_id = get_jwt_identity()  # 仍然需要获取用户ID
        
        # 生成需求编号
        demand_number = generate_demand_number()
        
        # 创建需求实例
        demand = Demand(
            demand_number=demand_number,
            requester_id=current_user_id,
            title=data.get('title', ''),  # 标题字段
            material_name=data.get('material_info', {}).get('name', ''),
            part_number=data.get('material_info', {}).get('part_number', ''),
            aircraft_type=data.get('material_info', {}).get('aircraft_type', ''),
            quantity=data.get('requirements', {}).get('quantity', 1),
            type=data.get('type', 'turnaround'),
            priority=data.get('priority', 'normal'),
            description=data.get('requirements', {}).get('description', ''),
            delivery_location=data.get('requirements', {}).get('delivery_location', ''),
            delivery_time=datetime.now() + timedelta(days=30),  # 默认30天后
            budget_range=data.get('requirements', {}).get('expected_price', ''),
            quality_requirements=', '.join(data.get('requirements', {}).get('acceptable_conditions', [])),
            contact_info=json.dumps(data.get('contact_info', {}), ensure_ascii=False),
            expires_at=datetime.now() + timedelta(days=90),  # 90天后过期
            status='published'
        )
        
        # 确保有标题（如果用户没有填写，自动生成）
        demand.ensure_title()
        
        # 保存到数据库
        db.session.add(demand)
        db.session.commit()
        
        # 记录创建历史
        DemandHistory.log_action(
            demand_id=demand.id,
            operator_id=current_user_id,
            action='created',
            description=f'需求"{demand.material_name}"已创建并发布',
            new_status=demand.status,
            details={
                'demand_number': demand.demand_number,
                'material_name': demand.material_name,
                'type': demand.type,
                'priority': demand.priority
            }
        )
        
        # 创建通知
        notification = Notification(
            user_id=current_user_id,
            title='Demand Published Successfully',
            content=f'Your demand for "{demand.material_name}" has been published successfully. Matching suppliers...',
            type='order',  # 使用模型中定义的枚举值
            is_read=False,
            created_at=datetime.now()
        )
        db.session.add(notification)
        db.session.commit()
        
        # 模拟匹配结果
        matching_results = [
            {
                'supplier_name': 'CAAC Material Supplier',
                'match_score': 95.5,
                'price_estimate': 'CNY 125,000',
                'delivery_time': '7-10 days',
                'status': 'In Stock'
            },
            {
                'supplier_name': 'Maintenance Provider',
                'match_score': 88.2,
                'price_estimate': 'CNY 118,000',
                'delivery_time': '5-7 days',
                'status': 'Need Transfer'
            }
        ]
        
        return success_response({
            'demand_id': str(demand.id),
            'message': 'Demand published successfully',
            'matching_results': matching_results
        }, 'Demand created successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'Failed to create demand: {str(e)}')


@demands_bp.route('/<int:demand_id>', methods=['PUT'])
@require_permission('publish_demand')
@require_ownership_enhanced(Demand, 'requester_id')
@validate_json(['material_info'])
@audit_action('update_demand', 'demand')
def update_demand(demand_id, current_user=None):
    """
    编辑需求
    功能描述：编辑已发布的需求信息
    入参：{ title, type, priority, material_info, requirements, contact_info }
    返回参数：{ demand_id: string, message: string }
    url地址：/api/v1/demands/{id}
    请求方式：PUT
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取需求
        demand = Demand.query.filter_by(id=demand_id, requester_id=current_user_id).first()
        if not demand:
            return error_response(404, 'Demand not found or access denied')
        
        # 检查需求状态是否允许编辑
        if demand.status not in ['published', 'matched']:
            return error_response(400, 'Demand cannot be edited in current status')
        
        data = request.get_json()
        
        # 记录变更前的状态
        old_data = {
            'title': demand.title,
            'material_name': demand.material_name,
            'part_number': demand.part_number,
            'aircraft_type': demand.aircraft_type,
            'quantity': demand.quantity,
            'type': demand.type,
            'priority': demand.priority,
            'description': demand.description,
            'delivery_location': demand.delivery_location,
            'budget_range': demand.budget_range,
            'quality_requirements': demand.quality_requirements
        }
        
        # 更新需求信息
        if 'title' in data:
            demand.title = data['title']
        
        if 'material_info' in data:
            material_info = data['material_info']
            if 'name' in material_info:
                demand.material_name = material_info['name']
            if 'part_number' in material_info:
                demand.part_number = material_info['part_number']
            if 'aircraft_type' in material_info:
                demand.aircraft_type = material_info['aircraft_type']
        
        if 'requirements' in data:
            requirements = data['requirements']
            if 'quantity' in requirements:
                demand.quantity = requirements['quantity']
            if 'description' in requirements:
                demand.description = requirements['description']
            if 'delivery_location' in requirements:
                demand.delivery_location = requirements['delivery_location']
            if 'expected_delivery_date' in requirements and requirements['expected_delivery_date']:
                try:
                    demand.delivery_time = datetime.fromisoformat(requirements['expected_delivery_date'].replace('Z', '+00:00'))
                except:
                    pass
            if 'expected_price' in requirements:
                demand.budget_range = requirements['expected_price']
            if 'acceptable_conditions' in requirements:
                demand.quality_requirements = ', '.join(requirements['acceptable_conditions'])
        
        if 'type' in data:
            demand.type = data['type']
        
        if 'priority' in data:
            demand.priority = data['priority']
        
        if 'contact_info' in data:
            demand.contact_info = json.dumps(data['contact_info'], ensure_ascii=False)
        
        # 确保有标题（如果编辑后没有标题，自动生成）
        demand.ensure_title()
        
        # 更新时间戳
        demand.updated_at = datetime.now()
        
        # 记录变更后的状态
        new_data = {
            'title': demand.title,
            'material_name': demand.material_name,
            'part_number': demand.part_number,
            'aircraft_type': demand.aircraft_type,
            'quantity': demand.quantity,
            'type': demand.type,
            'priority': demand.priority,
            'description': demand.description,
            'delivery_location': demand.delivery_location,
            'budget_range': demand.budget_range,
            'quality_requirements': demand.quality_requirements
        }
        
        # 记录历史
        changes = {}
        for key in old_data:
            if old_data[key] != new_data[key]:
                changes[key] = {'old': old_data[key], 'new': new_data[key]}
        
        if changes:
            DemandHistory.log_action(
                demand_id=demand.id,
                operator_id=current_user_id,
                action='updated',
                description=f'需求信息已更新，修改了 {len(changes)} 个字段',
                old_status=demand.status,
                new_status=demand.status,
                details={'changes': changes}
            )
        
        # 保存到数据库
        db.session.commit()
        
        return success_response({
            'demand_id': str(demand.id),
            'message': 'Demand updated successfully',
            'changes_count': len(changes)
        }, 'Demand updated successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'Failed to update demand: {str(e)}')


@demands_bp.route('/<int:demand_id>', methods=['GET'])
@require_permission('view_own_data')
@data_isolation(Demand, 'requester_id')
@audit_action('view_demand_detail', 'demand')
def get_demand_detail(demand_id, current_user=None):
    """
    获取需求详情
    功能描述：获取单个需求的详细信息
    入参：demand_id
    返回参数：需求详细信息
    url地址：/api/v1/demands/{id}
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取需求（考虑数据隔离）
        demand = Demand.query.filter_by(id=demand_id).first()
        if not demand:
            return error_response(404, 'Demand not found')
        
        # 检查访问权限（只有发布者或平台员工可以查看）
        current_user = User.query.get(current_user_id)
        if not current_user:
            return error_response(401, 'User not found')
        
        # 数据隔离检查
        if (demand.requester_id != current_user_id and 
            not current_user.can_cross_company_access()):
            return error_response(403, 'Access denied')
        
        # 解析联系信息JSON
        contact_info = {}
        if demand.contact_info:
            try:
                contact_info = json.loads(demand.contact_info)
            except:
                contact_info = {}
        
        # 格式化需求数据
        demand_data = {
            'id': demand.id,
            'demand_number': demand.demand_number,
            'title': demand.title or demand.ensure_title(),
            'material_info': {
                'name': demand.material_name,
                'part_number': demand.part_number,
                'aircraft_type': demand.aircraft_type,
            },
            'requirements': {
                'quantity': demand.quantity,
                'description': demand.description,
                'delivery_location': demand.delivery_location,
                'expected_delivery_date': demand.delivery_time.isoformat() if demand.delivery_time else None,
                'expected_price': demand.budget_range,
                'acceptable_conditions': demand.quality_requirements.split(', ') if demand.quality_requirements else []
            },
            'contact_info': contact_info,
            'type': demand.type,
            'priority': demand.priority,
            'status': demand.status,
            'requester': {
                'id': demand.requester.id,
                'name': demand.requester.real_name or demand.requester.username,
                'company': demand.requester.company_name
            } if demand.requester else None,
            'created_at': demand.created_at.isoformat(),
            'updated_at': demand.updated_at.isoformat() if demand.updated_at else None,
            'expires_at': demand.expires_at.isoformat() if demand.expires_at else None,
            # 统计信息
            'match_count': len(demand.matches) if hasattr(demand, 'matches') else 0,
            'response_count': len(demand.responses.all()) if demand.responses else 0
        }
        
        return success_response(demand_data, 'Demand detail retrieved successfully')
        
    except Exception as e:
        return error_response(500, f'Failed to retrieve demand detail: {str(e)}')


@demands_bp.route('/<int:demand_id>/match', methods=['GET'])
@jwt_required()
@require_active_user
def match_demand(demand_id):
    """
    智能需求匹配
    功能描述：根据需求条件匹配合适的供应商和航材
    入参：demand_id, filters(可选查询参数)
    返回参数：{ matches: array, match_score: number, recommendations: array }
    url地址：/api/v1/demands/{id}/match
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()

        # 添加调试日志
        print(f"🔍 需求匹配请求 - 用户ID: {current_user_id}, 需求ID: {demand_id}")

        # 获取需求（管理员可以访问所有需求）
        current_user = User.query.get(current_user_id)
        if current_user and current_user.user_type == 'admin':
            # 管理员可以访问所有需求
            demand = Demand.query.get(demand_id)
        else:
            # 普通用户只能访问自己的需求
            demand = Demand.query.filter_by(id=demand_id, requester_id=current_user_id).first()

        if not demand:
            print(f"❌ 需求未找到或无权限访问 - 需求ID: {demand_id}, 用户ID: {current_user_id}")
            return error_response(404, 'Demand not found or access denied')
        
        # 获取筛选参数
        supplier_type = request.args.get('supplier_type', '')
        location_filter = request.args.get('location', '')
        price_max = request.args.get('price_max', type=float)
        condition_filter = request.args.get('condition', '')
        
        # 执行智能匹配算法
        matches = perform_demand_matching(demand, {
            'supplier_type': supplier_type,
            'location': location_filter,
            'price_max': price_max,
            'condition': condition_filter
        })
        
        # 记录匹配历史
        DemandHistory.log_action(
            demand_id=demand.id,
            operator_id=current_user_id,
            action='matched',
            description=f'执行需求匹配，找到 {len(matches)} 个匹配结果',
            details={
                'match_count': len(matches),
                'filters': {
                    'supplier_type': supplier_type,
                    'location': location_filter,
                    'price_max': price_max,
                    'condition': condition_filter
                }
            }
        )
        
        return success_response({
            'matches': matches,
            'total_matches': len(matches),
            'demand_info': {
                'id': demand.id,
                'title': demand.title,
                'material_name': demand.material_name,
                'part_number': demand.part_number,
                'quantity': demand.quantity
            },
            'filters_applied': {
                'supplier_type': supplier_type,
                'location': location_filter,
                'price_max': price_max,
                'condition': condition_filter
            }
        }, 'Demand matching completed successfully')
        
    except Exception as e:
        return error_response(500, f'Failed to match demand: {str(e)}')


@demands_bp.route('/<int:demand_id>/rematch', methods=['POST'])
@require_permission('publish_demand')
@require_ownership_enhanced(Demand, 'requester_id')
@audit_action('rematch_demand', 'demand')
def rematch_demand(demand_id, current_user=None):
    """
    重新匹配需求
    功能描述：重新执行需求匹配，清除旧的匹配结果
    入参：demand_id
    返回参数：{ matches: array, message: string }
    url地址：/api/v1/demands/{id}/rematch
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取需求
        demand = Demand.query.filter_by(id=demand_id, requester_id=current_user_id).first()
        if not demand:
            return error_response(404, 'Demand not found or access denied')
        
        # 清除旧的匹配结果
        DemandMatch.query.filter_by(demand_id=demand_id).delete()
        
        # 执行新的匹配
        matches = perform_demand_matching(demand, {})
        
        # 更新需求状态
        if matches:
            demand.status = 'matched'
        else:
            demand.status = 'published'
        
        # 记录重新匹配历史
        DemandHistory.log_action(
            demand_id=demand.id,
            operator_id=current_user_id,
            action='rematched',
            description=f'重新执行需求匹配，找到 {len(matches)} 个新匹配结果',
            old_status=demand.status,
            new_status=demand.status,
            details={
                'new_match_count': len(matches),
                'action_type': 'rematch'
            }
        )
        
        db.session.commit()
        
        return success_response({
            'matches': matches,
            'total_matches': len(matches),
            'new_status': demand.status,
            'message': f'重新匹配完成，找到 {len(matches)} 个匹配结果'
        }, 'Demand rematching completed successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'Failed to rematch demand: {str(e)}')


def perform_demand_matching(demand, filters=None):
    """
    执行需求匹配算法
    根据需求条件匹配合适的供应商和库存
    """
    if filters is None:
        filters = {}
    
    try:
        # 基础匹配条件：零件号或航材名称
        inventory_query = InventoryItem.query.join(Material).filter(
            or_(
                Material.part_number.like(f'%{demand.part_number}%'),
                Material.name.like(f'%{demand.material_name}%'),
                Material.part_number == demand.part_number
            )
        )
        
        # 应用筛选条件
        if filters.get('location'):
            inventory_query = inventory_query.filter(
                InventoryItem.location.like(f'%{filters["location"]}%')
            )
        
        if filters.get('price_max'):
            inventory_query = inventory_query.filter(
                InventoryItem.unit_price <= filters['price_max']
            )
        
        # 只匹配有库存的项目
        inventory_query = inventory_query.filter(
            InventoryItem.current_stock >= demand.quantity
        )
        
        inventory_items = inventory_query.all()
        
        matches = []
        for item in inventory_items:
            # 计算匹配分数
            match_score = calculate_match_score(demand, item)
            
            # 获取供应商信息
            supplier = item.inventory_supplier if item.supplier_id else None
            
            match_data = {
                'id': item.id,
                'material': {
                    'id': item.material.id,
                    'name': item.material.name,
                    'part_number': item.material.part_number,
                    'manufacturer': item.material.manufacturer,
                    'category': item.material.category
                },
                'inventory': {
                    'location': item.location,
                    'current_stock': item.current_stock,
                    'unit_price': float(item.unit_price) if item.unit_price else 0,
                    'batch_number': item.batch_number,
                    'condition': item.condition,
                    'status': item.status
                },
                'supplier': {
                    'id': supplier.id if supplier else None,
                    'name': supplier.real_name or supplier.username if supplier else 'Unknown',
                    'company': supplier.company_name if supplier else 'Unknown',
                    'contact': supplier.phone if supplier else ''
                } if supplier else None,
                'match_score': match_score,
                'availability': 'available' if item.current_stock >= demand.quantity else 'insufficient',
                'estimated_delivery': '3-7天' if item.current_stock >= demand.quantity else '7-14天',
                'price_estimate': f'¥{float(item.unit_price):,.0f}' if item.unit_price else '面议'
            }
            
            # 保存匹配结果到数据库
            existing_match = DemandMatch.query.filter_by(
                demand_id=demand.id,
                supplier_id=supplier.id if supplier else None
            ).first()
            
            if not existing_match and supplier:
                demand_match = DemandMatch(
                    demand_id=demand.id,
                    supplier_id=supplier.id,
                    match_score=match_score,
                    matched_material_id=item.material.id,
                    matched_inventory_id=item.id,
                    availability_status='available' if item.current_stock >= demand.quantity else 'insufficient',
                    price_estimate=float(item.unit_price) if item.unit_price else 0,
                    estimated_delivery_time=7 if item.current_stock >= demand.quantity else 14,
                    match_details=json.dumps({
                        'location': item.location,
                        'stock': item.current_stock,
                        'condition': item.condition
                    }, ensure_ascii=False)
                )
                db.session.add(demand_match)
            
            matches.append(match_data)
        
        # 按匹配分数排序
        matches.sort(key=lambda x: x['match_score'], reverse=True)
        
        # 提交匹配结果
        db.session.commit()
        
        return matches[:20]  # 返回前20个最佳匹配
        
    except Exception as e:
        db.session.rollback()
        print(f"匹配算法执行失败: {str(e)}")
        return []


def calculate_match_score(demand, inventory_item):
    """
    计算匹配分数 (0-100)
    """
    score = 0
    
    # 零件号完全匹配 (40分)
    if demand.part_number and inventory_item.material.part_number:
        if demand.part_number.upper() == inventory_item.material.part_number.upper():
            score += 40
        elif demand.part_number.upper() in inventory_item.material.part_number.upper():
            score += 25
    
    # 航材名称匹配 (20分)
    if demand.material_name and inventory_item.material.name:
        if demand.material_name.upper() in inventory_item.material.name.upper():
            score += 20
        elif any(word in inventory_item.material.name.upper() 
                for word in demand.material_name.upper().split()):
            score += 10
    
    # 机型匹配 (15分)
    if demand.aircraft_type and inventory_item.material.aircraft_type:
        if demand.aircraft_type.upper() == inventory_item.material.aircraft_type.upper():
            score += 15
        elif demand.aircraft_type.upper() in inventory_item.material.aircraft_type.upper():
            score += 8
    
    # 库存充足性 (15分)
    if inventory_item.current_stock >= demand.quantity:
        score += 15
    elif inventory_item.current_stock >= demand.quantity * 0.5:
        score += 8
    
    # 地理位置加分 (10分)
    if demand.delivery_location and inventory_item.location:
        if demand.delivery_location in inventory_item.location:
            score += 10
        elif any(city in inventory_item.location 
                for city in ['北京', '上海', '广州', '深圳'] 
                if city in demand.delivery_location):
            score += 5
    
    return min(score, 100)  # 最高100分


# ================================
# 供应商响应功能 API
# ================================

@demands_bp.route('/available', methods=['GET'])
@require_permission('view_public_data')
@audit_action('view_available_demands', 'demand')
def get_available_demands(current_user=None):
    """
    获取可响应的需求列表 (供应商视角)
    功能描述：供应商查看所有公开的、可以响应的需求
    入参：page(页码), per_page(每页数量), type(类型过滤), priority(优先级过滤)
    返回参数：公开需求列表
    url地址：/api/v1/demands/available
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        type_filter = request.args.get('type', '')
        priority_filter = request.args.get('priority', '')
        aircraft_type_filter = request.args.get('aircraft_type', '')
        
        # 构建查询：公开状态且未过期的需求
        query = Demand.query.filter(
            and_(
                Demand.status.in_(['published', 'matched']),  # 公开或已匹配状态
                or_(
                    Demand.expires_at.is_(None),  # 无过期时间
                    Demand.expires_at > datetime.utcnow()  # 未过期
                ),
                Demand.requester_id != current_user_id  # 排除自己的需求
            )
        )
        
        # 应用过滤条件
        if type_filter:
            query = query.filter_by(type=type_filter)
        if priority_filter:
            query = query.filter_by(priority=priority_filter)
        if aircraft_type_filter:
            query = query.filter_by(aircraft_type=aircraft_type_filter)
        
        # 排序：优先级高、紧急度高、创建时间新的在前
        query = query.order_by(
            desc(Demand.priority == 'aog'),  # AOG优先
            desc(Demand.priority == 'high'),  # 高优先级
            desc(Demand.urgency == 'urgent'),  # 紧急
            desc(Demand.created_at)  # 最新创建
        )
        
        # 分页
        paginated_demands = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # 格式化需求数据
        demands_data = []
        for demand in paginated_demands.items:
            # 检查当前用户是否已经响应过
            existing_response = DemandResponse.query.filter_by(
                demand_id=demand.id,
                supplier_id=current_user_id
            ).first()
            
            demand_data = {
                'id': demand.id,
                'demand_number': demand.demand_number,
                'title': demand.title or demand.ensure_title(),
                'material_info': {
                    'name': demand.material_name,
                    'part_number': demand.part_number,
                    'aircraft_type': demand.aircraft_type,
                },
                'requirements': {
                    'quantity': demand.quantity,
                    'description': demand.description,
                    'delivery_location': demand.delivery_location,
                    'expected_delivery_date': demand.delivery_time.isoformat() if demand.delivery_time else None,
                    'expected_price': demand.budget_range,
                    'acceptable_conditions': demand.quality_requirements.split(', ') if demand.quality_requirements else []
                },
                'type': demand.type,
                'priority': demand.priority,
                'urgency': demand.urgency,
                'status': demand.status,
                'requester': {
                    'name': demand.requester.real_name or demand.requester.username,
                    'company': demand.requester.company_name,
                    'location': demand.delivery_location  # 不暴露详细联系信息
                } if demand.requester else None,
                'created_at': demand.created_at.isoformat(),
                'expires_at': demand.expires_at.isoformat() if demand.expires_at else None,
                'response_count': len(demand.responses.all()) if demand.responses else 0,
                'my_response': {
                    'id': existing_response.id,
                    'status': existing_response.status,
                    'submitted_at': existing_response.created_at.isoformat()
                } if existing_response else None
            }
            demands_data.append(demand_data)
        
        # 统计信息
        total_available = Demand.query.filter(
            and_(
                Demand.status.in_(['published', 'matched']),
                or_(
                    Demand.expires_at.is_(None),
                    Demand.expires_at > datetime.utcnow()
                ),
                Demand.requester_id != current_user_id
            )
        ).count()
        
        return success_response({
            'demands': demands_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': paginated_demands.total,
                'pages': paginated_demands.pages,
                'has_prev': paginated_demands.has_prev,
                'has_next': paginated_demands.has_next
            },
            'statistics': {
                'total_available': total_available,
                'filtered_total': paginated_demands.total
            }
        }, 'Available demands retrieved successfully')
        
    except Exception as e:
        return error_response(500, f'Failed to retrieve available demands: {str(e)}')


@demands_bp.route('/<int:demand_id>/responses', methods=['POST'])
@require_permission('create_data')
@audit_action('respond_to_demand', 'demand_response')
def respond_to_demand(demand_id, current_user=None):
    """
    供应商响应需求
    功能描述：供应商对特定需求提交响应报价
    入参：demand_id, response_data(响应信息)
    返回参数：{ response_id: string, message: string }
    url地址：/api/v1/demands/{id}/responses
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证输入数据
        required_fields = ['offered_price', 'delivery_time', 'material_condition', 'notes']
        for field in required_fields:
            if field not in data:
                return error_response(400, f'缺少必填字段: {field}')
        
        # 获取需求
        demand = Demand.query.get(demand_id)
        if not demand:
            return error_response(404, 'Demand not found')
        
        # 检查需求状态
        if demand.status not in ['published', 'matched']:
            return error_response(400, 'Demand is not available for responses')
        
        # 检查是否是自己的需求
        if demand.requester_id == current_user_id:
            return error_response(400, 'Cannot respond to your own demand')
        
        # 检查是否已经响应过
        existing_response = DemandResponse.query.filter_by(
            demand_id=demand_id,
            supplier_id=current_user_id
        ).first()
        
        if existing_response:
            return error_response(409, 'You have already responded to this demand')
        
        # 检查需求是否过期
        if demand.expires_at and demand.expires_at < datetime.utcnow():
            return error_response(400, 'Demand has expired')
        
        # 创建响应记录
        response = DemandResponse(
            demand_id=demand_id,
            supplier_id=current_user_id,
            offered_price=float(data['offered_price']),
            delivery_time_days=int(data['delivery_time']),
            material_condition=data['material_condition'],
            notes=data.get('notes', ''),
            part_number=data.get('part_number', demand.part_number),
            manufacturer=data.get('manufacturer', ''),
            location=data.get('location', ''),
            warranty_period=data.get('warranty_period', ''),
            certifications=data.get('certifications', ''),
            status='pending'
        )
        
        db.session.add(response)
        
        # 记录历史
        DemandHistory.log_action(
            demand_id=demand_id,
            operator_id=current_user_id,
            action='responded',
            description='供应商提交响应报价',
            details={
                'supplier_name': current_user.real_name or current_user.username,
                'offered_price': data['offered_price'],
                'delivery_time': data['delivery_time'],
                'condition': data['material_condition']
            }
        )
        
        # 更新需求状态为已有响应（如果之前是published）
        if demand.status == 'published':
            demand.status = 'matched'
            demand.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # 发送通知给需求发布者
        try:
            notification = Notification(
                user_id=demand.requester_id,
                type='demand_response',
                title='收到新的需求响应',
                content=f'您的需求 "{demand.title or demand.ensure_title()}" 收到了来自 {current_user.company_name or current_user.username} 的响应报价',
                related_id=response.id,
                status='unread'
            )
            db.session.add(notification)
            db.session.commit()
        except Exception as notification_error:
            # 通知发送失败不影响主流程
            print(f'Failed to send notification: {notification_error}')
        
        return success_response({
            'response_id': response.id,
            'demand_id': demand_id,
            'message': '响应提交成功',
            'status': response.status
        }, 'Response submitted successfully', 201)
        
    except ValueError as ve:
        return error_response(400, f'数据格式错误: {str(ve)}')
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'Failed to submit response: {str(e)}')


@demands_bp.route('/<int:demand_id>/responses', methods=['GET'])
@require_permission('view_own_data')
@data_isolation(Demand, 'requester_id')
@audit_action('view_demand_responses', 'demand')
def get_demand_responses(demand_id, current_user=None):
    """
    获取需求的所有响应
    功能描述：需求发布者查看收到的所有供应商响应
    入参：demand_id, status(状态过滤)
    返回参数：{ responses: array, total: number }
    url地址：/api/v1/demands/{id}/responses
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取需求
        demand = Demand.query.filter_by(id=demand_id, requester_id=current_user_id).first()
        if not demand:
            return error_response(404, 'Demand not found or access denied')
        
        # 获取查询参数
        status_filter = request.args.get('status', '')
        sort_by = request.args.get('sort_by', 'created_at')  # price, delivery_time, created_at
        order = request.args.get('order', 'desc')  # asc, desc
        
        # 构建查询
        query = DemandResponse.query.filter_by(demand_id=demand_id)
        
        if status_filter:
            query = query.filter_by(status=status_filter)
        
        # 排序
        if sort_by == 'price':
            order_by = DemandResponse.offered_price.asc() if order == 'asc' else DemandResponse.offered_price.desc()
        elif sort_by == 'delivery_time':
            order_by = DemandResponse.delivery_time_days.asc() if order == 'asc' else DemandResponse.delivery_time_days.desc()
        else:  # created_at
            order_by = DemandResponse.created_at.asc() if order == 'asc' else DemandResponse.created_at.desc()
        
        responses = query.order_by(order_by).all()
        
        # 格式化响应数据
        responses_data = []
        for response in responses:
            response_data = {
                'id': response.id,
                'supplier': {
                    'id': response.supplier.id,
                    'name': response.supplier.real_name or response.supplier.username,
                    'company': response.supplier.company_name,
                    'contact': response.supplier.phone,
                    'email': response.supplier.email
                } if response.supplier else None,
                'offered_price': response.offered_price,
                'delivery_time_days': response.delivery_time_days,
                'material_condition': response.material_condition,
                'notes': response.notes,
                'part_number': response.part_number,
                'manufacturer': response.manufacturer,
                'location': response.location,
                'warranty_period': response.warranty_period,
                'certifications': response.certifications,
                'status': response.status,
                'created_at': response.created_at.isoformat(),
                'updated_at': response.updated_at.isoformat() if response.updated_at else None,
                'accepted_at': response.accepted_at.isoformat() if response.accepted_at else None
            }
            responses_data.append(response_data)
        
        # 统计信息
        total_responses = DemandResponse.query.filter_by(demand_id=demand_id).count()
        pending_responses = DemandResponse.query.filter_by(demand_id=demand_id, status='pending').count()
        accepted_responses = DemandResponse.query.filter_by(demand_id=demand_id, status='accepted').count()
        
        return success_response({
            'responses': responses_data,
            'statistics': {
                'total': total_responses,
                'pending': pending_responses,
                'accepted': accepted_responses,
                'filtered_total': len(responses_data)
            },
            'demand_info': {
                'id': demand.id,
                'title': demand.title or demand.ensure_title(),
                'status': demand.status
            }
        }, 'Demand responses retrieved successfully')
        
    except Exception as e:
        return error_response(500, f'Failed to retrieve demand responses: {str(e)}')


@demands_bp.route('/<int:demand_id>/responses/<int:response_id>', methods=['PUT'])
@require_permission('modify_own_data')
@data_isolation(Demand, 'requester_id')
@audit_action('update_demand_response', 'demand_response')
def update_response_status(demand_id, response_id, current_user=None):
    """
    更新响应状态 (接受/拒绝供应商响应)
    功能描述：需求发布者接受或拒绝供应商的响应
    入参：demand_id, response_id, action('accept'/'reject'), notes(可选)
    返回参数：{ response_id: string, new_status: string, message: string }
    url地址：/api/v1/demands/{id}/responses/{response_id}
    请求方式：PUT
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'action' not in data:
            return error_response(400, '缺少必填字段: action')
        
        action = data['action']
        if action not in ['accept', 'reject']:
            return error_response(400, '无效的操作，只能是 accept 或 reject')
        
        # 获取需求
        demand = Demand.query.filter_by(id=demand_id, requester_id=current_user_id).first()
        if not demand:
            return error_response(404, 'Demand not found or access denied')
        
        # 获取响应
        response = DemandResponse.query.filter_by(id=response_id, demand_id=demand_id).first()
        if not response:
            return error_response(404, 'Response not found')
        
        # 检查响应状态
        if response.status != 'pending':
            return error_response(400, f'Response is already {response.status}')
        
        # 更新响应状态
        if action == 'accept':
            response.status = 'accepted'
            response.accepted_at = datetime.utcnow()
            
            # 接受响应后，可以选择拒绝其他待处理的响应
            if data.get('reject_others', True):
                other_responses = DemandResponse.query.filter(
                    and_(
                        DemandResponse.demand_id == demand_id,
                        DemandResponse.id != response_id,
                        DemandResponse.status == 'pending'
                    )
                ).all()
                
                for other_response in other_responses:
                    other_response.status = 'rejected'
                    other_response.updated_at = datetime.utcnow()
            
            # 更新需求状态为已完成
            demand.status = 'completed'
            demand.updated_at = datetime.utcnow()
            
        elif action == 'reject':
            response.status = 'rejected'
        
        response.updated_at = datetime.utcnow()
        if 'notes' in data:
            response.notes += f"\n\n处理意见: {data['notes']}"
        
        # 记录历史
        DemandHistory.log_action(
            demand_id=demand_id,
            operator_id=current_user_id,
            action=f'response_{action}ed',
            description=f'{"接受" if action == "accept" else "拒绝"}了供应商响应',
            details={
                'response_id': response_id,
                'supplier_name': response.supplier.real_name or response.supplier.username if response.supplier else 'Unknown',
                'action': action,
                'notes': data.get('notes', '')
            }
        )
        
        db.session.commit()
        
        # 发送通知给供应商
        try:
            notification = Notification(
                user_id=response.supplier_id,
                type='response_status_update',
                title=f'需求响应已被{"接受" if action == "accept" else "拒绝"}',
                content=f'您对需求 "{demand.title or demand.ensure_title()}" 的响应已被{"接受" if action == "accept" else "拒绝"}',
                related_id=response.id,
                status='unread'
            )
            db.session.add(notification)
            db.session.commit()
        except Exception as notification_error:
            print(f'Failed to send notification: {notification_error}')
        
        return success_response({
            'response_id': response.id,
            'new_status': response.status,
            'demand_status': demand.status,
            'message': f'响应已{"接受" if action == "accept" else "拒绝"}'
        }, f'Response {action}ed successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'Failed to update response status: {str(e)}')