"""
航材共享保障平台 - 工作流管理路由
功能：工作流实例管理、任务处理、流程监控
作者：Claude AI Assistant
创建时间：2025-07-18
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from utils.response import success_response, error_response
from utils.decorators import handle_exceptions
from services.workflow_service import workflow_service
from models import db, WorkflowInstance, WorkflowTask, User
from datetime import datetime
import json

# 创建工作流管理蓝图
workflow_bp = Blueprint('workflow', __name__)


@workflow_bp.route('/definitions', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_definitions():
    """
    获取工作流定义列表
    功能描述：返回所有可用的工作流定义
    入参：无
    返回参数：{
        "workflows": [
            {
                "key": "simple_approval",
                "name": "简单审批流程",
                "description": "一级审批，适用于简单业务场景"
            }
        ]
    }
    url地址：/api/workflow/definitions
    请求方式：GET
    """
    try:
        workflows = workflow_service.get_available_workflows()
        return success_response({"workflows": workflows})
    except Exception as e:
        current_app.logger.error(f"获取工作流定义失败：{str(e)}")
        return error_response(f"获取工作流定义失败：{str(e)}")


@workflow_bp.route('/start', methods=['POST'])
@jwt_required()
@handle_exceptions
def start_workflow():
    """
    启动工作流实例
    功能描述：根据工作流定义启动新的工作流实例
    入参：{
        "workflow_key": "simple_approval",
        "business_key": "ORDER_001",
        "variables": {
            "title": "订单审批",
            "amount": 10000,
            "description": "紧急航材采购订单"
        }
    }
    返回参数：{
        "instance_id": "123",
        "status": "RUNNING",
        "message": "工作流启动成功"
    }
    url地址：/api/workflow/start
    请求方式：POST
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        if not data.get('workflow_key'):
            return error_response("工作流类型不能为空")
        
        if not data.get('business_key'):
            return error_response("业务键值不能为空")
        
        # 获取当前用户
        current_user_id = get_jwt_identity()
        
        # 启动工作流
        instance_id = workflow_service.start_workflow(
            workflow_key=data['workflow_key'],
            business_key=data['business_key'],
            variables=data.get('variables', {}),
            initiator_id=current_user_id
        )
        
        return success_response({
            "instance_id": instance_id,
            "status": "RUNNING",
            "message": "工作流启动成功"
        })
        
    except Exception as e:
        current_app.logger.error(f"启动工作流失败：{str(e)}")
        return error_response(f"启动工作流失败：{str(e)}")


@workflow_bp.route('/instances/<instance_id>', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_instance(instance_id):
    """
    获取工作流实例详情
    功能描述：获取指定工作流实例的详细信息
    入参：instance_id（路径参数）
    返回参数：{
        "id": "123",
        "workflow_key": "simple_approval",
        "business_key": "ORDER_001",
        "status": "RUNNING",
        "progress": 50.0,
        "current_tasks": [],
        "variables": {},
        "created_at": "2025-07-18T10:00:00",
        "completed_at": null
    }
    url地址：/api/workflow/instances/{instance_id}
    请求方式：GET
    """
    try:
        instance = workflow_service.get_workflow_instance(instance_id)
        
        if not instance:
            return error_response("工作流实例不存在")
        
        return success_response(instance)
        
    except Exception as e:
        current_app.logger.error(f"获取工作流实例失败：{str(e)}")
        return error_response(f"获取工作流实例失败：{str(e)}")


@workflow_bp.route('/instances/<instance_id>/tasks', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_tasks(instance_id):
    """
    获取工作流实例的当前任务
    功能描述：获取指定工作流实例的待处理任务列表
    入参：instance_id（路径参数）
    返回参数：{
        "tasks": [
            {
                "id": "task_001",
                "name": "审批",
                "description": "请审批此申请",
                "assignee": "admin",
                "status": "READY",
                "created_at": "2025-07-18T10:00:00",
                "due_date": null,
                "form_data": {}
            }
        ]
    }
    url地址：/api/workflow/instances/{instance_id}/tasks
    请求方式：GET
    """
    try:
        tasks = workflow_service.get_workflow_tasks(instance_id)
        return success_response({"tasks": tasks})
        
    except Exception as e:
        current_app.logger.error(f"获取工作流任务失败：{str(e)}")
        return error_response(f"获取工作流任务失败：{str(e)}")


@workflow_bp.route('/tasks/<task_id>/complete', methods=['POST'])
@jwt_required()
@handle_exceptions
def complete_task(task_id):
    """
    完成工作流任务
    功能描述：完成指定的工作流任务
    入参：{
        "instance_id": "123",
        "variables": {
            "approved": true,
            "comment": "审批通过",
            "next_assignee": "manager"
        }
    }
    返回参数：{
        "success": true,
        "message": "任务完成成功"
    }
    url地址：/api/workflow/tasks/{task_id}/complete
    请求方式：POST
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        if not data.get('instance_id'):
            return error_response("工作流实例ID不能为空")
        
        # 获取当前用户
        current_user_id = get_jwt_identity()
        
        # 完成任务
        success = workflow_service.complete_task(
            instance_id=data['instance_id'],
            task_id=task_id,
            variables=data.get('variables', {}),
            user_id=current_user_id
        )
        
        if success:
            return success_response({
                "success": True,
                "message": "任务完成成功"
            })
        else:
            return error_response("任务完成失败")
            
    except Exception as e:
        current_app.logger.error(f"完成任务失败：{str(e)}")
        return error_response(f"完成任务失败：{str(e)}")


@workflow_bp.route('/instances', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_instances():
    """
    获取工作流实例列表
    功能描述：获取工作流实例列表，支持分页和筛选
    入参：{
        "page": 1,
        "per_page": 10,
        "status": "RUNNING",
        "workflow_key": "simple_approval",
        "business_key": "ORDER_001"
    }
    返回参数：{
        "instances": [],
        "total": 100,
        "page": 1,
        "per_page": 10,
        "pages": 10
    }
    url地址：/api/workflow/instances
    请求方式：GET
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        status = request.args.get('status')
        workflow_key = request.args.get('workflow_key')
        business_key = request.args.get('business_key')
        
        # 构建查询
        query = WorkflowInstance.query
        
        if status:
            query = query.filter(WorkflowInstance.status == status)
        
        if workflow_key:
            query = query.filter(WorkflowInstance.workflow_key == workflow_key)
        
        if business_key:
            query = query.filter(WorkflowInstance.business_key.like(f'%{business_key}%'))
        
        # 分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 构建结果
        instances = []
        for instance in pagination.items:
            instance_data = {
                'id': instance.id,
                'workflow_key': instance.workflow_key,
                'business_key': instance.business_key,
                'status': instance.status,
                'initiator_id': instance.initiator_id,
                'created_at': instance.created_at.isoformat(),
                'completed_at': instance.completed_at.isoformat() if instance.completed_at else None,
                'variables': json.loads(instance.variables or '{}')
            }
            instances.append(instance_data)
        
        return success_response({
            'instances': instances,
            'total': pagination.total,
            'page': page,
            'per_page': per_page,
            'pages': pagination.pages
        })
        
    except Exception as e:
        current_app.logger.error(f"获取工作流实例列表失败：{str(e)}")
        return error_response(f"获取工作流实例列表失败：{str(e)}")


@workflow_bp.route('/my-tasks', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_my_tasks():
    """
    获取我的待办任务
    功能描述：获取当前用户的待办任务列表
    入参：{
        "page": 1,
        "per_page": 10,
        "status": "READY"
    }
    返回参数：{
        "tasks": [
            {
                "id": "task_001",
                "instance_id": "123",
                "task_name": "审批",
                "workflow_key": "simple_approval",
                "business_key": "ORDER_001",
                "status": "READY",
                "created_at": "2025-07-18T10:00:00",
                "assignee": "admin"
            }
        ],
        "total": 10,
        "page": 1,
        "per_page": 10
    }
    url地址：/api/workflow/my-tasks
    请求方式：GET
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        status = request.args.get('status', 'READY')
        
        # 获取当前用户
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response("用户不存在")
        
        # 构建查询 - 查找分配给当前用户的任务
        query = WorkflowTask.query.filter(
            WorkflowTask.assignee == user.username,
            WorkflowTask.status == status
        )
        
        # 分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 构建结果
        tasks = []
        for task in pagination.items:
            # 获取工作流实例信息
            instance = WorkflowInstance.query.get(task.instance_id)
            
            task_data = {
                'id': task.task_id,
                'instance_id': task.instance_id,
                'task_name': task.task_name,
                'workflow_key': instance.workflow_key if instance else '',
                'business_key': instance.business_key if instance else '',
                'status': task.status,
                'created_at': task.created_at.isoformat(),
                'assignee': task.assignee,
                'task_data': json.loads(task.task_data or '{}')
            }
            tasks.append(task_data)
        
        return success_response({
            'tasks': tasks,
            'total': pagination.total,
            'page': page,
            'per_page': per_page,
            'pages': pagination.pages
        })
        
    except Exception as e:
        current_app.logger.error(f"获取我的任务失败：{str(e)}")
        return error_response(f"获取我的任务失败：{str(e)}")


@workflow_bp.route('/instances/<instance_id>/history', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_history(instance_id):
    """
    获取工作流实例历史记录
    功能描述：获取指定工作流实例的所有历史任务和操作记录
    入参：instance_id（路径参数）
    返回参数：{
        "history": [
            {
                "task_name": "提交申请",
                "assignee": "user1",
                "status": "COMPLETED",
                "completed_at": "2025-07-18T10:00:00",
                "comment": "申请提交",
                "variables": {}
            }
        ]
    }
    url地址：/api/workflow/instances/{instance_id}/history
    请求方式：GET
    """
    try:
        # 获取工作流实例
        instance = WorkflowInstance.query.get(instance_id)
        if not instance:
            return error_response("工作流实例不存在")
        
        # 获取历史任务
        history_tasks = WorkflowTask.query.filter_by(
            instance_id=instance_id
        ).order_by(WorkflowTask.created_at.asc()).all()
        
        history = []
        for task in history_tasks:
            task_data = {
                'task_id': task.task_id,
                'task_name': task.task_name,
                'assignee': task.assignee,
                'status': task.status,
                'created_at': task.created_at.isoformat(),
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'comment': task.comment or '',
                'variables': json.loads(task.task_data or '{}')
            }
            history.append(task_data)
        
        return success_response({
            'instance_id': instance_id,
            'workflow_key': instance.workflow_key,
            'business_key': instance.business_key,
            'history': history
        })
        
    except Exception as e:
        current_app.logger.error(f"获取工作流历史失败：{str(e)}")
        return error_response(f"获取工作流历史失败：{str(e)}")


@workflow_bp.route('/statistics', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_workflow_statistics():
    """
    获取工作流统计信息
    功能描述：获取工作流实例的统计数据
    入参：{
        "start_date": "2025-01-01",
        "end_date": "2025-12-31",
        "workflow_key": "simple_approval"
    }
    返回参数：{
        "total_instances": 100,
        "running_instances": 20,
        "completed_instances": 80,
        "average_completion_time": 2.5,
        "workflow_distribution": {
            "simple_approval": 60,
            "multi_level_approval": 30,
            "parallel_approval": 10
        }
    }
    url地址：/api/workflow/statistics
    请求方式：GET
    """
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        workflow_key = request.args.get('workflow_key')
        
        # 构建基础查询
        query = WorkflowInstance.query
        
        if start_date:
            query = query.filter(WorkflowInstance.created_at >= start_date)
        
        if end_date:
            query = query.filter(WorkflowInstance.created_at <= end_date)
        
        if workflow_key:
            query = query.filter(WorkflowInstance.workflow_key == workflow_key)
        
        # 计算统计数据
        total_instances = query.count()
        running_instances = query.filter(WorkflowInstance.status == 'RUNNING').count()
        completed_instances = query.filter(WorkflowInstance.status == 'COMPLETED').count()
        
        # 计算平均完成时间（天）
        completed_query = query.filter(
            WorkflowInstance.status == 'COMPLETED',
            WorkflowInstance.completed_at.isnot(None)
        )
        
        average_completion_time = 0
        if completed_instances > 0:
            total_time = 0
            for instance in completed_query:
                if instance.completed_at and instance.created_at:
                    duration = (instance.completed_at - instance.created_at).total_seconds()
                    total_time += duration
            
            if total_time > 0:
                average_completion_time = (total_time / completed_instances) / 86400  # 转换为天
        
        # 工作流类型分布
        workflow_distribution = {}
        for workflow_key in ['simple_approval', 'multi_level_approval', 'parallel_approval']:
            count = query.filter(WorkflowInstance.workflow_key == workflow_key).count()
            workflow_distribution[workflow_key] = count
        
        return success_response({
            'total_instances': total_instances,
            'running_instances': running_instances,
            'completed_instances': completed_instances,
            'average_completion_time': round(average_completion_time, 2),
            'workflow_distribution': workflow_distribution
        })
        
    except Exception as e:
        current_app.logger.error(f"获取工作流统计信息失败：{str(e)}")
        return error_response(f"获取工作流统计信息失败：{str(e)}")


# 注册蓝图错误处理器
@workflow_bp.errorhandler(Exception)
def handle_workflow_error(error):
    """工作流模块统一错误处理"""
    current_app.logger.error(f"工作流模块发生错误：{str(error)}")
    return error_response(f"工作流操作失败：{str(error)}")