#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 维修管理API路由  
版本: 1.0
创建时间: 2025-01-13

处理维修工单、技师管理、维修计划等功能
"""

from datetime import datetime, timedelta
from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_

from models import db, WorkOrder, User, LaborRecord
from utils.decorators import validate_json, require_active_user
from utils.response import success_response, error_response

# 创建蓝图
maintenance_bp = Blueprint('maintenance', __name__)

@maintenance_bp.route('/work-orders', methods=['GET'])
@jwt_required()
def get_work_orders():
    """
    获取维修工单列表
    功能描述：获取用户的维修工单列表，支持筛选和分页
    入参：status, priority, aircraft_type, technician, page, size
    返回参数：维修工单列表和统计信息
    url地址：/api/v1/maintenance/work-orders
    请求方式：GET
    """
    try:
        # 获取查询参数
        status = request.args.get('status')
        priority = request.args.get('priority')
        aircraft_type = request.args.get('aircraft_type')
        technician = request.args.get('technician')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        # 构建查询
        query = WorkOrder.query
        
        # 添加筛选条件
        if status:
            query = query.filter(WorkOrder.status == status)
        if priority:
            query = query.filter(WorkOrder.priority == priority)
        if aircraft_type:
            query = query.filter(WorkOrder.aircraft_type == aircraft_type)
        if technician:
            query = query.filter(WorkOrder.assigned_technician_id == technician)
        
        # 分页查询
        total = query.count()
        work_orders = query.offset((page - 1) * size).limit(size).all()
        
        # 统计信息
        total_count = WorkOrder.query.count()
        in_progress_count = WorkOrder.query.filter(WorkOrder.status == 'in_progress').count()
        waiting_parts_count = WorkOrder.query.filter(WorkOrder.status == 'waiting_parts').count()
        completed_today_count = WorkOrder.query.filter(
            WorkOrder.status == 'completed',
            WorkOrder.completion_time >= datetime.now().date()
        ).count()
        
        # 计算平均完成时间
        avg_completion_time = 6.8  # 模拟数据
        efficiency_rate = 92.5  # 模拟数据
        
        statistics = {
            'total_work_orders': total_count,
            'in_progress': in_progress_count,
            'waiting_parts': waiting_parts_count,
            'completed_today': completed_today_count,
            'avg_completion_time': avg_completion_time,
            'efficiency_rate': efficiency_rate
        }
        
        return success_response({
            'work_orders': [wo.to_dict() for wo in work_orders],
            'total': total,
            'page': page,
            'size': size,
            'statistics': statistics
        }, '维修工单列表获取成功')
        
    except Exception as e:
        return error_response(500, f'获取维修工单列表失败: {str(e)}')

@maintenance_bp.route('/work-orders', methods=['POST'])
@jwt_required()
@validate_json()
def create_work_order():
    """
    创建维修工单
    功能描述：创建新的维修工单
    入参：aircraft_info, fault_info, priority, estimated_hours等
    返回参数：创建的工单信息
    url地址：/api/v1/maintenance/work-orders
    请求方式：POST
    """
    try:
        data = request.get_json()
        current_user_id = get_jwt_identity()
        
        # 生成工单号
        work_order_number = f"WO{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # 创建工单
        work_order = WorkOrder(
            work_order_number=work_order_number,
            aircraft_tail=data['aircraft_info']['tail_number'],
            aircraft_type=data['aircraft_info']['aircraft_type'],
            priority=data.get('priority', 'normal'),
            status='new',
            fault_title=data['fault_info']['title'],
            fault_description=data['fault_info']['description'],
            assigned_technician_id=data.get('assigned_technician_id'),
            estimated_hours=data.get('estimated_hours', 8),
            station=data.get('work_location', ''),
            notes=data.get('notes', '')
        )
        
        # 设置预计完成时间
        if data.get('estimated_completion'):
            work_order.estimated_completion = datetime.fromisoformat(data['estimated_completion'])
        
        db.session.add(work_order)
        db.session.commit()
        
        return success_response({
            'work_order_id': work_order.id,
            'work_order_number': work_order.work_order_number,
            'status': work_order.status
        }, '维修工单创建成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'创建维修工单失败: {str(e)}')

@maintenance_bp.route('/work-orders/<int:work_order_id>', methods=['GET'])
@jwt_required()
def get_work_order_details(work_order_id):
    """
    获取工单详情
    功能描述：获取指定维修工单的详细信息
    入参：work_order_id
    返回参数：工单详情、进度历史、工时记录、使用零件
    url地址：/api/v1/maintenance/work-orders/{id}
    请求方式：GET
    """
    try:
        work_order = WorkOrder.query.get_or_404(work_order_id)
        
        # 获取工时记录
        labor_records = LaborRecord.query.filter_by(work_order_id=work_order_id).all()
        
        # 模拟进度历史和使用零件（实际应从数据库获取）
        progress_history = [
            {
                'id': 1,
                'status': 'new',
                'timestamp': work_order.created_at.isoformat(),
                'operator': '调度员',
                'notes': '工单创建，等待分配技师'
            }
        ]
        
        parts_used = []
        
        work_order_dict = work_order.to_dict()
        
        return success_response({
            'work_order': work_order_dict,
            'progress_history': progress_history,
            'labor_records': [lr.to_dict() for lr in labor_records],
            'parts_used': parts_used
        }, '工单详情获取成功')
        
    except Exception as e:
        return error_response(500, f'获取工单详情失败: {str(e)}')

@maintenance_bp.route('/work-orders/<int:work_order_id>/status', methods=['POST'])
@jwt_required()
@validate_json()
def update_work_order_status(work_order_id):
    """
    更新工单状态
    功能描述：更新维修工单的状态和进度
    入参：status, progress, notes
    返回参数：更新后的状态信息
    url地址：/api/v1/maintenance/work-orders/{id}/status
    请求方式：POST
    """
    try:
        data = request.get_json()
        current_user_id = get_jwt_identity()
        
        work_order = WorkOrder.query.get_or_404(work_order_id)
        
        # 更新状态
        if data.get('status'):
            work_order.status = data['status']
        
        # 更新进度
        if data.get('progress') is not None:
            work_order.progress = data['progress']
        
        # 更新备注
        if data.get('notes'):
            work_order.notes = data['notes']
        
        work_order.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return success_response({
            'status': work_order.status,
            'progress': work_order.progress,
            'updated_at': work_order.updated_at.isoformat()
        }, '工单状态更新成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'更新工单状态失败: {str(e)}')

@maintenance_bp.route('/work-orders/<int:work_order_id>/assign', methods=['POST'])
@jwt_required()
@validate_json()
def assign_technician(work_order_id):
    """
    分配维修技师
    功能描述：为维修工单分配技术人员
    入参：technician_id
    返回参数：分配结果
    url地址：/api/v1/maintenance/work-orders/{id}/assign
    请求方式：POST
    """
    try:
        data = request.get_json()
        current_user_id = get_jwt_identity()
        
        work_order = WorkOrder.query.get_or_404(work_order_id)
        technician = User.query.get_or_404(data['technician_id'])
        
        # 分配技师
        work_order.assigned_technician_id = technician.id
        work_order.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return success_response({
            'assigned_technician': technician.to_dict(),
            'work_order_id': work_order.id
        }, '技师分配成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'分配技师失败: {str(e)}')

@maintenance_bp.route('/work-orders/<int:work_order_id>/labor', methods=['POST'])
@jwt_required()
@validate_json()
def record_labor_time(work_order_id):
    """
    记录工时
    功能描述：记录维修工单的工时和进度
    入参：technician_id, start_time, end_time, work_description
    返回参数：工时记录ID和统计信息
    url地址：/api/v1/maintenance/work-orders/{id}/labor
    请求方式：POST
    """
    try:
        data = request.get_json()
        current_user_id = get_jwt_identity()
        
        work_order = WorkOrder.query.get_or_404(work_order_id)
        
        # 创建工时记录
        labor_record = LaborRecord(
            work_order_id=work_order_id,
            technician_id=data['technician_id'],
            start_time=datetime.fromisoformat(data['start_time']),
            end_time=datetime.fromisoformat(data['end_time']) if data.get('end_time') else None,
            work_description=data.get('work_description', '')
        )
        
        # 计算工时
        if labor_record.end_time:
            labor_record.calculate_hours()
        
        db.session.add(labor_record)
        db.session.commit()
        
        # 计算总工时
        total_hours = db.session.query(db.func.sum(LaborRecord.hours_worked)).filter_by(
            work_order_id=work_order_id
        ).scalar() or 0
        
        return success_response({
            'labor_record_id': labor_record.id,
            'total_hours': float(total_hours),
            'remaining_hours': max(0, work_order.estimated_hours - total_hours)
        }, '工时记录成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'记录工时失败: {str(e)}')

@maintenance_bp.route('/technicians', methods=['GET'])
@jwt_required()
def get_technicians():
    """
    获取技师信息
    功能描述：获取维修技师信息和技能清单
    入参：skill_category, certification_level, availability
    返回参数：技师列表和技能矩阵
    url地址：/api/v1/maintenance/technicians
    请求方式：GET
    """
    try:
        # 获取维修技师（用户类型为maintenance的用户）
        technicians = User.query.filter_by(user_type='maintenance').all()
        
        # 模拟技能矩阵
        skill_matrix = {
            '起落架系统': ['张师傅', '赵师傅'],
            '发动机系统': ['李师傅', '钱师傅'],
            '航电系统': ['王师傅', '孙师傅']
        }
        
        return success_response({
            'technicians': [tech.to_dict() for tech in technicians],
            'skill_matrix': skill_matrix
        }, '技师信息获取成功')
        
    except Exception as e:
        return error_response(500, f'获取技师信息失败: {str(e)}')

@maintenance_bp.route('/schedule', methods=['GET'])
@jwt_required()
def get_maintenance_schedule():
    """
    获取维修计划
    功能描述：获取维修计划和排程信息
    入参：time_range, aircraft_id, maintenance_type
    返回参数：维修计划列表和资源分配
    url地址：/api/v1/maintenance/schedule
    请求方式：GET
    """
    try:
        # 模拟维修计划数据
        scheduled_maintenance = [
            {
                'id': 1,
                'aircraft_tail': 'B-1234',
                'aircraft_type': 'A320',
                'maintenance_type': 'A检',
                'scheduled_date': datetime.now() + timedelta(days=3),
                'estimated_duration': 48,
                'status': 'scheduled',
                'assigned_team': '第一维修组',
                'bay': '1号机库'
            }
        ]
        
        # 转换日期格式
        for item in scheduled_maintenance:
            if isinstance(item['scheduled_date'], datetime):
                item['scheduled_date'] = item['scheduled_date'].isoformat()
        
        resource_allocation = {
            'A检': {'required_technicians': 4, 'available_technicians': 6},
            'C检': {'required_technicians': 12, 'available_technicians': 8}
        }
        
        return success_response({
            'scheduled_maintenance': scheduled_maintenance,
            'resource_allocation': resource_allocation,
            'capacity_utilization': 0.75
        }, '维修计划获取成功')
        
    except Exception as e:
        return error_response(500, f'获取维修计划失败: {str(e)}')

@maintenance_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_maintenance_statistics():
    """
    获取维修统计
    功能描述：获取维修相关的统计数据和KPI
    入参：time_range, aircraft_type, maintenance_type
    返回参数：完成率、平均维修时间、成本分析、效率趋势
    url地址：/api/v1/maintenance/statistics
    请求方式：GET
    """
    try:
        # 模拟统计数据
        statistics = {
            'overview': {
                'completion_rate': 0.925,
                'avg_repair_time': 6.8,
                'on_time_delivery': 0.89,
                'customer_satisfaction': 0.95
            },
            'monthly_data': [
                {'month': '1月', 'completed': 45, 'avg_time': 7.2, 'on_time': 0.87},
                {'month': '2月', 'completed': 52, 'avg_time': 6.9, 'on_time': 0.88},
                {'month': '3月', 'completed': 48, 'avg_time': 6.5, 'on_time': 0.91},
                {'month': '4月', 'completed': 61, 'avg_time': 6.8, 'on_time': 0.89},
                {'month': '5月', 'completed': 55, 'avg_time': 6.6, 'on_time': 0.90},
                {'month': '6月', 'completed': 58, 'avg_time': 6.4, 'on_time': 0.92},
                {'month': '7月', 'completed': 62, 'avg_time': 6.8, 'on_time': 0.89}
            ],
            'cost_analysis': {
                'labor_cost': 1250000,
                'parts_cost': 2890000,
                'overhead_cost': 560000,
                'total_cost': 4700000
            }
        }
        
        return success_response(statistics, '维修统计数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取维修统计数据失败: {str(e)}')