#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 用户管理API
版本: 1.0
创建时间: 2025-07-25

提供用户管理的API接口
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, User
from utils.response import success_response, error_response
from utils.permissions import require_permission, get_current_user_info
from sqlalchemy import text, or_, and_
import logging

logger = logging.getLogger(__name__)

# 创建用户管理蓝图
users_bp = Blueprint('users_v1', __name__)

@users_bp.route('/', methods=['GET'])
@jwt_required()
@require_permission('user_management')
def get_users():
    """
    获取用户列表
    
    查询参数:
        page: 页码 (默认1)
        per_page: 每页数量 (默认20)
        search: 搜索关键词
        user_type: 用户类型筛选
        status: 状态筛选
        company_type: 公司类型筛选
        sort_by: 排序字段 (created_at, last_login, username)
        sort_order: 排序方向 (asc, desc)
    
    返回:
        用户列表和分页信息
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        user_type = request.args.get('user_type', '').strip()
        status = request.args.get('status', '').strip()
        company_type = request.args.get('company_type', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 构建查询
        query = User.query
        
        # 搜索条件
        if search:
            search_pattern = f'%{search}%'
            query = query.filter(or_(
                User.username.ilike(search_pattern),
                User.email.ilike(search_pattern),
                User.real_name.ilike(search_pattern),
                User.company_name.ilike(search_pattern)
            ))
        
        # 筛选条件
        if user_type:
            query = query.filter(User.user_type == user_type)
        
        if status:
            query = query.filter(User.status == status)
            
        if company_type:
            query = query.filter(User.company_type == company_type)
        
        # 排序
        sort_column = getattr(User, sort_by, User.created_at)
        if sort_order.lower() == 'asc':
            query = query.order_by(sort_column.asc())
        else:
            query = query.order_by(sort_column.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        users = []
        for user in pagination.items:
            user_dict = user.to_dict()
            # 移除敏感信息
            user_dict.pop('password_hash', None)
            # 添加额外信息
            user_dict['is_online'] = getattr(user, 'is_online', False)
            user_dict['login_count'] = getattr(user, 'login_count', 0)
            users.append(user_dict)
        
        return success_response({
            'users': users,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }, '获取用户列表成功')
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return error_response(500, f'获取用户列表失败: {str(e)}')

@users_bp.route('/<int:user_id>', methods=['GET'])
@jwt_required()
@require_permission('user_management')
def get_user_detail(user_id):
    """
    获取用户详细信息
    
    路径参数:
        user_id: 用户ID
    
    返回:
        用户详细信息
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        user_dict = user.to_dict()
        # 移除敏感信息
        user_dict.pop('password_hash', None)
        
        # 添加额外信息
        user_dict['last_login_info'] = {
            'last_login': user.last_login,
            'login_count': getattr(user, 'login_count', 0),
            'is_online': getattr(user, 'is_online', False)
        }
        
        # 获取用户权限信息
        user_dict['permissions'] = user.get_permissions() if hasattr(user, 'get_permissions') else []
        
        return success_response({
            'user': user_dict
        }, '获取用户详情成功')
        
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        return error_response(500, f'获取用户详情失败: {str(e)}')

@users_bp.route('/<int:user_id>', methods=['PUT'])
@jwt_required()
@require_permission('user_management')
def update_user(user_id):
    """
    更新用户信息
    
    路径参数:
        user_id: 用户ID
    
    请求体:
        可包含任何用户字段进行部分更新
    
    返回:
        更新后的用户信息
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        data = request.get_json()
        if not data:
            return error_response(400, '请求数据不能为空')
        
        # 可更新的字段
        updatable_fields = [
            'real_name', 'email', 'phone', 'company_name', 
            'user_type', 'status', 'permission_level', 'role_id'
        ]
        
        updated_fields = []
        for field in updatable_fields:
            if field in data:
                old_value = getattr(user, field)
                new_value = data[field]
                
                # 特殊验证
                if field == 'email' and new_value:
                    # 检查邮箱是否已被其他用户使用
                    existing_user = User.query.filter(
                        User.email == new_value,
                        User.id != user_id
                    ).first()
                    if existing_user:
                        return error_response(400, '邮箱已被其他用户使用')
                
                if field == 'permission_level':
                    # 权限等级验证
                    current_user = get_current_user_info()
                    if current_user.get('permission_level', 0) <= new_value:
                        return error_response(403, '无法设置等于或高于自己的权限等级')
                
                setattr(user, field, new_value)
                if old_value != new_value:
                    updated_fields.append(f"{field}: {old_value} -> {new_value}")
        
        if updated_fields:
            db.session.commit()
            
            # 记录操作日志
            current_user = get_current_user_info()
            logger.info(f"用户 {current_user.get('username')} 更新了用户 {user.username} 的信息: {', '.join(updated_fields)}")
        
        user_dict = user.to_dict()
        user_dict.pop('password_hash', None)
        
        return success_response({
            'user': user_dict,
            'updated_fields': updated_fields
        }, '用户信息更新成功')
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新用户信息失败: {e}")
        return error_response(500, f'更新用户信息失败: {str(e)}')

@users_bp.route('/<int:user_id>/status', methods=['PUT'])
@jwt_required()
@require_permission('user_management')
def update_user_status(user_id):
    """
    更新用户状态
    
    路径参数:
        user_id: 用户ID
    
    请求体:
        status: 新状态 (active, inactive, suspended, deleted)
        reason: 状态变更原因
    
    返回:
        更新结果
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        data = request.get_json()
        new_status = data.get('status')
        reason = data.get('reason', '')
        
        if not new_status:
            return error_response(400, '状态不能为空')
        
        valid_statuses = ['active', 'inactive', 'suspended', 'deleted']
        if new_status not in valid_statuses:
            return error_response(400, f'无效的状态值，必须是: {", ".join(valid_statuses)}')
        
        old_status = user.status
        user.status = new_status
        
        db.session.commit()
        
        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username')} 将用户 {user.username} 的状态从 {old_status} 更改为 {new_status}，原因: {reason}")
        
        return success_response({
            'user_id': user_id,
            'old_status': old_status,
            'new_status': new_status,
            'reason': reason
        }, '用户状态更新成功')

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新用户状态失败: {e}")
        return error_response(500, f'更新用户状态失败: {str(e)}')

@users_bp.route('/batch-update', methods=['POST'])
@jwt_required()
@require_permission('user_management')
def batch_update_users():
    """
    批量更新用户

    请求体:
        user_ids: 用户ID列表
        action: 操作类型 (activate, deactivate, suspend, delete)
        reason: 操作原因

    返回:
        批量操作结果
    """
    try:
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        action = data.get('action')
        reason = data.get('reason', '')

        if not user_ids:
            return error_response(400, '用户ID列表不能为空')

        if not action:
            return error_response(400, '操作类型不能为空')

        # 状态映射
        status_map = {
            'activate': 'active',
            'deactivate': 'inactive',
            'suspend': 'suspended',
            'delete': 'deleted'
        }

        if action not in status_map:
            return error_response(400, f'无效的操作类型: {action}')

        new_status = status_map[action]

        # 查询要更新的用户
        users = User.query.filter(User.id.in_(user_ids)).all()

        if len(users) != len(user_ids):
            return error_response(400, '部分用户不存在')

        # 批量更新
        updated_users = []
        for user in users:
            old_status = user.status
            user.status = new_status
            updated_users.append({
                'id': user.id,
                'username': user.username,
                'old_status': old_status,
                'new_status': new_status
            })

        db.session.commit()

        # 记录操作日志
        current_user = get_current_user_info()
        usernames = [user.username for user in users]
        logger.info(f"用户 {current_user.get('username')} 批量{action}了用户: {', '.join(usernames)}，原因: {reason}")

        return success_response({
            'updated_count': len(updated_users),
            'updated_users': updated_users,
            'action': action,
            'reason': reason
        }, f'批量{action}操作成功')

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量更新用户失败: {e}")
        return error_response(500, f'批量更新用户失败: {str(e)}')

@users_bp.route('/statistics', methods=['GET'])
@jwt_required()
@require_permission('user_management')
def get_user_statistics():
    """
    获取用户统计信息

    返回:
        用户统计数据
    """
    try:
        # 基础统计
        total_users = User.query.count()
        active_users = User.query.filter(User.status == 'active').count()
        inactive_users = User.query.filter(User.status == 'inactive').count()
        suspended_users = User.query.filter(User.status == 'suspended').count()

        # 按用户类型统计
        user_type_stats = db.session.execute(text("""
            SELECT user_type, COUNT(*) as count
            FROM users
            WHERE status != 'deleted'
            GROUP BY user_type
        """)).fetchall()

        # 按公司类型统计
        company_type_stats = db.session.execute(text("""
            SELECT company_type, COUNT(*) as count
            FROM users
            WHERE status != 'deleted'
            GROUP BY company_type
        """)).fetchall()

        # 最近注册用户
        recent_users = User.query.filter(
            User.status != 'deleted'
        ).order_by(User.created_at.desc()).limit(10).all()

        recent_users_data = []
        for user in recent_users:
            user_dict = user.to_dict()
            user_dict.pop('password_hash', None)
            recent_users_data.append(user_dict)

        return success_response({
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': inactive_users,
            'suspended_users': suspended_users,
            'user_type_stats': [{'type': row[0], 'count': row[1]} for row in user_type_stats],
            'company_type_stats': [{'type': row[0], 'count': row[1]} for row in company_type_stats],
            'recent_users': recent_users_data
        }, '获取用户统计成功')

    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        return error_response(500, f'获取用户统计失败: {str(e)}')

@users_bp.route('/<int:user_id>/reset-password', methods=['POST'])
@jwt_required()
@require_permission('user_management')
def reset_user_password(user_id):
    """
    重置用户密码

    路径参数:
        user_id: 用户ID

    请求体:
        new_password: 新密码 (可选，不提供则生成随机密码)
        force_change: 是否强制用户下次登录时修改密码

    返回:
        重置结果
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')

        data = request.get_json() or {}
        new_password = data.get('new_password')
        force_change = data.get('force_change', True)

        # 如果没有提供新密码，生成随机密码
        if not new_password:
            import secrets
            import string
            alphabet = string.ascii_letters + string.digits
            new_password = ''.join(secrets.choice(alphabet) for _ in range(12))

        # 设置新密码
        user.set_password(new_password)

        # 如果强制修改，设置标记
        if force_change:
            user.force_password_change = True

        db.session.commit()

        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username')} 重置了用户 {user.username} 的密码")

        return success_response({
            'user_id': user_id,
            'username': user.username,
            'new_password': new_password,
            'force_change': force_change
        }, '密码重置成功')

    except Exception as e:
        db.session.rollback()
        logger.error(f"重置用户密码失败: {e}")
        return error_response(500, f'重置用户密码失败: {str(e)}')

@users_bp.route('/<int:user_id>/activity-logs', methods=['GET'])
@jwt_required()
@require_permission('user_management')
def get_user_activity_logs(user_id):
    """
    获取用户活动日志

    路径参数:
        user_id: 用户ID

    查询参数:
        page: 页码 (默认1)
        per_page: 每页数量 (默认20)
        action_type: 活动类型筛选
        start_date: 开始日期
        end_date: 结束日期

    返回:
        用户活动日志列表
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')

        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        action_type = request.args.get('action_type', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()

        # 这里应该查询审计日志表，暂时返回模拟数据
        # 实际实现需要根据审计日志表结构调整

        mock_logs = [
            {
                'id': 1,
                'action_type': 'login',
                'description': '用户登录',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0...',
                'created_at': '2025-07-25T10:00:00Z'
            },
            {
                'id': 2,
                'action_type': 'update_profile',
                'description': '更新个人信息',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0...',
                'created_at': '2025-07-25T09:30:00Z'
            }
        ]

        return success_response({
            'user_id': user_id,
            'username': user.username,
            'activity_logs': mock_logs,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': len(mock_logs),
                'pages': 1,
                'has_prev': False,
                'has_next': False
            }
        }, '获取用户活动日志成功')

    except Exception as e:
        logger.error(f"获取用户活动日志失败: {e}")
        return error_response(500, f'获取用户活动日志失败: {str(e)}')

@users_bp.route('/export', methods=['POST'])
@jwt_required()
@require_permission('user_management')
def export_users():
    """
    导出用户列表

    请求体:
        format: 导出格式 (csv, excel)
        filters: 筛选条件
        fields: 导出字段列表

    返回:
        导出文件下载链接
    """
    try:
        data = request.get_json()
        export_format = data.get('format', 'csv')
        filters = data.get('filters', {})
        fields = data.get('fields', ['username', 'email', 'real_name', 'company_name', 'user_type', 'status'])

        # 构建查询
        query = User.query

        # 应用筛选条件
        if filters.get('user_type'):
            query = query.filter(User.user_type == filters['user_type'])
        if filters.get('status'):
            query = query.filter(User.status == filters['status'])
        if filters.get('company_type'):
            query = query.filter(User.company_type == filters['company_type'])

        users = query.all()

        # 生成导出数据
        export_data = []
        for user in users:
            user_dict = user.to_dict()
            user_dict.pop('password_hash', None)

            # 只包含指定字段
            filtered_data = {field: user_dict.get(field, '') for field in fields}
            export_data.append(filtered_data)

        # 这里应该生成实际的文件并返回下载链接
        # 暂时返回数据结构

        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username')} 导出了用户列表，格式: {export_format}，数量: {len(export_data)}")

        return success_response({
            'export_format': export_format,
            'total_records': len(export_data),
            'fields': fields,
            'download_url': f'/api/v1/users/download/{export_format}',  # 模拟下载链接
            'data': export_data[:10]  # 返回前10条作为预览
        }, '用户列表导出成功')

    except Exception as e:
        logger.error(f"导出用户列表失败: {e}")
        return error_response(500, f'导出用户列表失败: {str(e)}')

@users_bp.route('/user-types', methods=['GET'])
@jwt_required()
def get_user_types():
    """
    获取用户类型列表

    返回:
        用户类型列表
    """
    try:
        user_types = [
            {'value': 'supplier', 'label': '供应商', 'description': '航材制造商，专注生产和销售'},
            {'value': 'distributor', 'label': '分销商', 'description': '航材贸易商，连接供需两端'},
            {'value': 'airline_buyer', 'label': '航空公司', 'description': '航材需求方，专注采购和使用'},
            {'value': 'platform_staff', 'label': '平台员工', 'description': '平台运营管理人员'},
            {'value': 'maintenance_engineer', 'label': '维修工程师', 'description': '提供技术支持和维修服务'},
            {'value': 'logistics_specialist', 'label': '物流专员', 'description': '负责配送跟踪和物流管理'},
            {'value': 'admin', 'label': '系统管理员', 'description': '系统配置和用户管理'}
        ]

        return success_response({
            'user_types': user_types
        }, '获取用户类型成功')

    except Exception as e:
        logger.error(f"获取用户类型失败: {e}")
        return error_response(500, f'获取用户类型失败: {str(e)}')
