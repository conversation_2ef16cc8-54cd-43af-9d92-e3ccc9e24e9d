#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 角色管理API
版本: 1.0
创建时间: 2025-07-24

提供角色CRUD和权限管理的API接口
"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from services.role_service import RoleService
from utils.permissions import require_permission, require_role, get_current_user_info
from utils.response import success_response, error_response
from utils.validators import validate_required_fields
import logging

logger = logging.getLogger(__name__)

# 创建角色管理蓝图
roles_bp = Blueprint('roles_v1', __name__)

@roles_bp.route('/public', methods=['GET'])
def get_public_roles():
    """
    获取公开角色列表（供注册使用）
    
    说明：
    - 不需要认证
    - 仅返回启用的角色
    - 用于用户注册时的角色选择
    """
    try:
        # 获取所有启用的角色
        roles = RoleService.get_all_roles(include_inactive=False)
        
        # 转换为前端需要的格式
        role_list = []
        for role in roles:
            role_data = {
                'id': role['id'],
                'role_code': role['role_code'],
                'role_name': role['role_name'],
                'display_name': role['display_name'],
                'description': role['description'],
                'category': role['category'],
                'business_type': role['business_type'],
                'is_active': role['is_active'],
                'sort_order': role['sort_order'],
                'icon_name': role['icon_name'],
                'theme_color': role['theme_color']
            }
            role_list.append(role_data)
        
        return success_response({
            'roles': role_list
        })
        
    except Exception as e:
        logger.error(f"获取公开角色列表失败: {str(e)}")
        return error_response(500, f"获取角色列表失败: {str(e)}")

@roles_bp.route('/', methods=['GET'])
@jwt_required()
def get_roles():
    """
    获取所有角色列表
    
    查询参数:
        include_inactive: 是否包含非活跃角色 (true/false)
    
    返回:
        角色列表
    """
    try:
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        roles = RoleService.get_all_roles(include_inactive=include_inactive)
        
        return success_response({
            'roles': roles,
            'total': len(roles)
        }, '获取角色列表成功')
        
    except Exception as e:
        logger.error(f"获取角色列表失败: {e}")
        return error_response(500, f'获取角色列表失败: {str(e)}')

@roles_bp.route('/<int:role_id>', methods=['GET'])
@jwt_required()
def get_role(role_id):
    """
    根据ID获取角色详情
    
    路径参数:
        role_id: 角色ID
    
    返回:
        角色详情信息
    """
    try:
        role = RoleService.get_role_by_id(role_id)
        
        if not role:
            return error_response(404, '角色不存在')
        
        return success_response({
            'role': role
        }, '获取角色详情成功')
        
    except Exception as e:
        logger.error(f"获取角色详情失败: {e}")
        return error_response(500, f'获取角色详情失败: {str(e)}')

@roles_bp.route('/', methods=['POST'])
@require_permission('role_management')
def create_role():
    """
    创建新角色
    
    请求体:
        role_code: 角色编码 (必填)
        role_name: 角色名称 (必填)
        display_name: 显示名称 (必填)
        description: 角色描述
        category: 角色分类 (internal/external) (必填)
        business_type: 业务类型 (buy_only/sell_only/buy_and_sell/service) (必填)
        sort_order: 排序顺序
        icon_name: 图标名称
        theme_color: 主题颜色
        is_active: 是否启用
    
    返回:
        创建的角色信息
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['role_code', 'role_name', 'display_name', 'category', 'business_type']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(400, validation_error)
        
        # 验证枚举值
        if data['category'] not in ['internal', 'external']:
            return error_response(400, 'category必须是internal或external')
        
        if data['business_type'] not in ['buy_only', 'sell_only', 'buy_and_sell', 'service']:
            return error_response(400, 'business_type必须是buy_only、sell_only、buy_and_sell或service')
        
        # 创建角色
        role = RoleService.create_role(data)
        
        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username', 'unknown')} 创建了角色: {role['role_code']}")
        
        return success_response({
            'role': role
        }, '创建角色成功', 201)
        
    except ValueError as e:
        return error_response(400, str(e))
    except Exception as e:
        logger.error(f"创建角色失败: {e}")
        return error_response(500, f'创建角色失败: {str(e)}')

@roles_bp.route('/<int:role_id>', methods=['PUT'])
@require_permission('role_management')
def update_role(role_id):
    """
    更新角色信息
    
    路径参数:
        role_id: 角色ID
    
    请求体:
        可包含任何角色字段进行部分更新
    
    返回:
        更新后的角色信息
    """
    try:
        data = request.get_json()
        
        if not data:
            return error_response(400, '请提供要更新的数据')
        
        # 验证枚举值（如果提供）
        if 'category' in data and data['category'] not in ['internal', 'external']:
            return error_response(400, 'category必须是internal或external')
        
        if 'business_type' in data and data['business_type'] not in ['buy_only', 'sell_only', 'buy_and_sell', 'service']:
            return error_response(400, 'business_type必须是buy_only、sell_only、buy_and_sell或service')
        
        # 更新角色
        role = RoleService.update_role(role_id, data)
        
        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username', 'unknown')} 更新了角色: {role_id}")
        
        return success_response({
            'role': role
        }, '更新角色成功')
        
    except ValueError as e:
        return error_response(400, str(e))
    except Exception as e:
        logger.error(f"更新角色失败: {e}")
        return error_response(500, f'更新角色失败: {str(e)}')

@roles_bp.route('/<int:role_id>', methods=['DELETE'])
@require_permission('role_management')
def delete_role(role_id):
    """
    删除角色
    
    路径参数:
        role_id: 角色ID
    
    返回:
        删除结果
    """
    try:
        success = RoleService.delete_role(role_id)
        
        if success:
            # 记录操作日志
            current_user = get_current_user_info()
            logger.info(f"用户 {current_user.get('username', 'unknown')} 删除了角色: {role_id}")
            
            return success_response({}, '删除角色成功')
        else:
            return error_response(500, '删除角色失败')
        
    except ValueError as e:
        return error_response(400, str(e))
    except Exception as e:
        logger.error(f"删除角色失败: {e}")
        return error_response(500, f'删除角色失败: {str(e)}')

@roles_bp.route('/<int:role_id>/permissions', methods=['GET'])
@jwt_required()
def get_role_permissions(role_id):
    """
    获取角色的权限列表
    
    路径参数:
        role_id: 角色ID
    
    返回:
        权限列表
    """
    try:
        permissions = RoleService.get_role_permissions(role_id)
        
        return success_response({
            'role_id': role_id,
            'permissions': permissions,
            'total': len(permissions)
        }, '获取角色权限成功')
        
    except Exception as e:
        logger.error(f"获取角色权限失败: {e}")
        return error_response(500, f'获取角色权限失败: {str(e)}')

@roles_bp.route('/<int:role_id>/permissions', methods=['PUT'])
@require_permission('role_management')
def assign_role_permissions(role_id):
    """
    为角色分配权限
    
    路径参数:
        role_id: 角色ID
    
    请求体:
        permission_ids: 权限ID列表
    
    返回:
        分配结果
    """
    try:
        data = request.get_json()
        
        if not data or 'permission_ids' not in data:
            return error_response(400, '请提供permission_ids')
        
        permission_ids = data['permission_ids']
        
        if not isinstance(permission_ids, list):
            return error_response(400, 'permission_ids必须是数组')
        
        # 获取当前用户信息用于记录授权人
        current_user = get_current_user_info()
        granted_by = current_user.get('id') if current_user else None
        
        # 分配权限
        success = RoleService.assign_permissions(role_id, permission_ids, granted_by)
        
        if success:
            logger.info(f"用户 {current_user.get('username', 'unknown')} 为角色 {role_id} 分配了 {len(permission_ids)} 个权限")
            
            return success_response({
                'role_id': role_id,
                'assigned_permissions': len(permission_ids)
            }, '权限分配成功')
        else:
            return error_response(500, '权限分配失败')
        
    except ValueError as e:
        return error_response(400, str(e))
    except Exception as e:
        logger.error(f"权限分配失败: {e}")
        return error_response(500, f'权限分配失败: {str(e)}')

@roles_bp.route('/configs', methods=['GET'])
@jwt_required()
def get_role_configs():
    """
    获取角色配置信息（用于前端动态配置）
    
    返回:
        角色配置列表（包含主题、图标等前端需要的信息）
    """
    try:
        # 获取当前用户信息，确定是否可以查看所有角色
        current_user = get_current_user_info()
        
        # 如果是内部用户，可以查看所有角色；外部用户只能查看外部角色
        if current_user and current_user.get('category') == 'internal':
            roles = RoleService.get_all_roles(include_inactive=False)
        else:
            # 外部用户只能看到外部角色
            all_roles = RoleService.get_all_roles(include_inactive=False)
            roles = [role for role in all_roles if role['category'] == 'external']
        
        # 格式化为前端需要的配置格式
        role_configs = []
        for role in roles:
            role_configs.append({
                'id': role['id'],
                'role_code': role['role_code'],
                'display_name': role['display_name'],
                'description': role['description'],
                'category': role['category'],
                'business_type': role['business_type'],
                'icon_name': role['icon_name'],
                'theme_color': role['theme_color'],
                'sort_order': role['sort_order']
            })
        
        return success_response({
            'roles': role_configs,
            'total': len(role_configs)
        }, '获取角色配置成功')
        
    except Exception as e:
        logger.error(f"获取角色配置失败: {e}")
        return error_response(500, f'获取角色配置失败: {str(e)}')