#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 认证扩展API
版本: 1.0
创建时间: 2025-07-24

提供注册验证和邀请码相关的认证接口
"""

from flask import Blueprint, request
from services.invitation_service import InvitationService
from services.role_service import RoleService
from utils.response import success_response, error_response
from utils.validators import validate_required_fields
import logging

logger = logging.getLogger(__name__)

# 创建认证扩展蓝图
auth_bp = Blueprint('auth_v1', __name__)

@auth_bp.route('/validate-invitation', methods=['POST'])
def validate_invitation_for_registration():
    """
    验证邀请码用于注册（扩展版本，包含角色信息）
    
    请求体:
        code: 邀请码 (必填)
        
    返回:
        验证结果、邀请码信息和可选角色列表
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        validation_error = validate_required_fields(data, ['code'])
        if validation_error:
            return error_response(400, validation_error)
        
        code = data['code'].strip()
        
        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent', '')
        
        # 验证邀请码
        is_valid, error_message, invitation_info = InvitationService.validate_invitation_code(
            code=code,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if not is_valid:
            logger.warning(f"注册邀请码验证失败: code={code}, ip={ip_address}, error={error_message}")
            return error_response(400, error_message)
        
        # 获取可选角色的详细信息
        allowed_role_codes = invitation_info.get('allowed_roles', [])
        available_roles = []
        
        if allowed_role_codes:
            all_roles = RoleService.get_all_roles(include_inactive=False)
            for role in all_roles:
                if role['role_code'] in allowed_role_codes:
                    available_roles.append({
                        'id': role['id'],
                        'role_code': role['role_code'],
                        'display_name': role['display_name'],
                        'description': role['description'],
                        'category': role['category'],
                        'business_type': role['business_type'],
                        'icon_name': role['icon_name'],
                        'theme_color': role['theme_color']
                    })
        
        # 根据邀请码类型提供额外的注册指导信息
        registration_guidelines = {
            'staff': {
                'title': '内部员工注册',
                'description': '您正在使用内部员工邀请码注册，请联系系统管理员完成后续认证步骤。',
                'next_steps': [
                    '完成基本信息填写',
                    '选择适合的工作角色',
                    '等待管理员审核激活'
                ]
            },
            'admin': {
                'title': '管理员注册',
                'description': '您正在使用管理员邀请码注册，注册后将拥有平台管理权限。',
                'next_steps': [
                    '完成管理员信息填写',
                    '设置安全问题和答案',
                    '激活后即可使用管理功能'
                ]
            }
        }
        
        code_type = invitation_info.get('code_type', 'staff')
        guidelines = registration_guidelines.get(code_type, registration_guidelines['staff'])
        
        logger.info(f"注册邀请码验证成功: code={code}, type={code_type}, roles={len(available_roles)}")
        
        return success_response({
            'valid': True,
            'invitation_id': invitation_info.get('id'),
            'code_type': code_type,
            'available_roles': available_roles,
            'guidelines': guidelines,
            'expires_at': invitation_info.get('expires_at').isoformat() if invitation_info.get('expires_at') else None,
            'max_uses': invitation_info.get('max_uses'),
            'used_count': invitation_info.get('used_count')
        }, '邀请码验证成功，可以继续注册')
        
    except Exception as e:
        logger.error(f"注册邀请码验证异常: {e}")
        return error_response(500, f'验证失败: {str(e)}')

@auth_bp.route('/check-role-permissions', methods=['POST'])
def check_role_permissions():
    """
    检查角色权限（供前端动态显示功能）
    
    请求体:
        role_codes: 角色代码列表 (必填)
        
    返回:
        各角色的权限信息
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        validation_error = validate_required_fields(data, ['role_codes'])
        if validation_error:
            return error_response(400, validation_error)
        
        role_codes = data['role_codes']
        if not isinstance(role_codes, list):
            return error_response(400, 'role_codes必须是数组')
        
        # 获取角色权限信息
        roles_permissions = {}
        
        for role_code in role_codes:
            role = RoleService.get_role_by_code(role_code)
            if role:
                permissions = RoleService.get_role_permissions(role['id'])
                
                # 按功能分类组织权限
                categorized_permissions = {}
                for perm in permissions:
                    category = perm['category']
                    if category not in categorized_permissions:
                        categorized_permissions[category] = []
                    categorized_permissions[category].append({
                        'permission_code': perm['permission_code'],
                        'permission_name': perm['permission_name'],
                        'description': perm['description'],
                        'module': perm['module']
                    })
                
                roles_permissions[role_code] = {
                    'role_info': {
                        'role_code': role['role_code'],
                        'display_name': role['display_name'],
                        'description': role['description'],
                        'category': role['category'],
                        'business_type': role['business_type']
                    },
                    'permissions_by_category': categorized_permissions,
                    'total_permissions': len(permissions)
                }
            else:
                roles_permissions[role_code] = None
        
        return success_response({
            'roles_permissions': roles_permissions
        }, '获取角色权限信息成功')
        
    except Exception as e:
        logger.error(f"获取角色权限信息失败: {e}")
        return error_response(500, f'获取角色权限信息失败: {str(e)}')

@auth_bp.route('/register-with-invitation', methods=['POST'])
def register_with_invitation():
    """
    使用邀请码注册用户（集成邀请码验证和用户创建）
    
    请求体:
        invitation_code: 邀请码 (必填)
        username: 用户名 (必填)
        password: 密码 (必填)
        email: 邮箱 (必填)
        full_name: 真实姓名 (必填)
        role_code: 选择的角色代码 (必填)
        phone: 电话号码 (可选)
        company: 公司名称 (可选)
        
    返回:
        注册结果和用户信息
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['invitation_code', 'username', 'password', 'email', 'full_name', 'role_code']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(400, validation_error)
        
        invitation_code = data['invitation_code'].strip()
        username = data['username'].strip()
        password = data['password']
        email = data['email'].strip()
        full_name = data['full_name'].strip()
        role_code = data['role_code'].strip()
        
        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent', '')
        
        # 验证邀请码
        is_valid, error_message, invitation_info = InvitationService.validate_invitation_code(
            code=invitation_code,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if not is_valid:
            logger.warning(f"注册时邀请码验证失败: code={invitation_code}, error={error_message}")
            return error_response(400, f'邀请码验证失败: {error_message}')
        
        # 验证选择的角色是否在允许列表中
        allowed_roles = invitation_info.get('allowed_roles', [])
        if role_code not in allowed_roles:
            return error_response(400, f'角色 {role_code} 不在此邀请码的允许范围内')
        
        # 获取角色信息
        role = RoleService.get_role_by_code(role_code)
        if not role or not role['is_active']:
            return error_response(400, f'角色 {role_code} 不存在或已禁用')
        
        # 检查用户名和邮箱是否已存在
        from sqlalchemy import text
        from models import db
        
        # 检查用户名
        username_check = db.session.execute(
            text("SELECT id FROM users WHERE username = :username"),
            {'username': username}
        ).fetchone()
        if username_check:
            return error_response(409, '用户名已存在')
        
        # 检查邮箱
        email_check = db.session.execute(
            text("SELECT id FROM users WHERE email = :email"),
            {'email': email}
        ).fetchone()
        if email_check:
            return error_response(409, '邮箱已被注册')
        
        # 创建用户
        from werkzeug.security import generate_password_hash
        import uuid
        
        # 生成用户UUID
        user_uuid = str(uuid.uuid4())
        
        # 插入用户数据
        insert_user_sql = """
        INSERT INTO users (
            uuid, username, password_hash, email, full_name, phone, company, 
            role_id, is_active, created_at
        ) VALUES (
            :uuid, :username, :password_hash, :email, :full_name, :phone, :company,
            :role_id, :is_active, CURRENT_TIMESTAMP
        ) RETURNING id
        """
        
        user_data = {
            'uuid': user_uuid,
            'username': username,
            'password_hash': generate_password_hash(password),
            'email': email,
            'full_name': full_name,
            'phone': data.get('phone', ''),
            'company': data.get('company', ''),
            'role_id': role['id'],
            'is_active': True  # 使用邀请码注册的用户直接激活
        }
        
        result = db.session.execute(text(insert_user_sql), user_data)
        user_id = result.fetchone()[0]
        
        # 记录邀请码使用
        InvitationService.use_invitation_code(
            invitation_id=invitation_info['id'],
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        db.session.commit()
        
        logger.info(f"用户注册成功: username={username}, role={role_code}, invitation_code={invitation_code}")
        
        # 返回用户信息（不包含密码）
        return success_response({
            'user': {
                'id': user_id,
                'uuid': user_uuid,
                'username': username,
                'email': email,
                'full_name': full_name,
                'role_code': role['role_code'],
                'role_name': role['display_name'],
                'is_active': True
            },
            'message': '注册成功，账户已激活，可以立即登录'
        }, '用户注册成功', 201)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"用户注册失败: {e}")
        return error_response(500, f'注册失败: {str(e)}')