#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 邀请码管理API
版本: 1.0
创建时间: 2025-07-24

提供邀请码生成、验证和管理的API接口
"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from services.invitation_service import InvitationService
from utils.permissions import require_permission, require_internal_user, get_current_user_info
from utils.response import success_response, error_response
from utils.validators import validate_required_fields
import logging

logger = logging.getLogger(__name__)

# 创建邀请码管理蓝图
invitation_codes_bp = Blueprint('invitation_codes_v1', __name__)

@invitation_codes_bp.route('/', methods=['POST'])
@require_permission('role_management')
def create_invitation_code():
    """
    生成邀请码
    
    请求体:
        code_type: 邀请码类型 (staff/admin) (必填)
        allowed_roles: 允许注册的角色列表 (必填)
        expires_in_days: 有效期天数 (默认30天)
        max_uses: 最大使用次数 (默认1次)
        ip_whitelist: IP白名单列表 (可选)
    
    返回:
        生成的邀请码信息
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['code_type', 'allowed_roles']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(400, validation_error)
        
        # 验证邀请码类型
        if data['code_type'] not in ['staff', 'admin']:
            return error_response(400, 'code_type必须是staff或admin')
        
        # 验证允许的角色列表
        if not isinstance(data['allowed_roles'], list) or not data['allowed_roles']:
            return error_response(400, 'allowed_roles必须是非空数组')
        
        # 验证角色代码是否有效
        from services.role_service import RoleService
        all_roles = RoleService.get_all_roles()
        valid_role_codes = [role['role_code'] for role in all_roles]
        
        for role_code in data['allowed_roles']:
            if role_code not in valid_role_codes:
                return error_response(400, f'无效的角色代码: {role_code}')
        
        # 获取当前用户信息
        current_user = get_current_user_info()
        created_by = current_user['id'] if current_user else None
        
        # 创建邀请码
        invitation_code = InvitationService.create_invitation_code(
            code_type=data['code_type'],
            allowed_roles=data['allowed_roles'],
            created_by=created_by,
            expires_in_days=data.get('expires_in_days', 30),
            max_uses=data.get('max_uses', 1),
            ip_whitelist=data.get('ip_whitelist', [])
        )
        
        logger.info(f"用户 {current_user.get('username', 'unknown')} 生成了邀请码: {invitation_code['code']}")
        
        return success_response({
            'invitation_code': invitation_code
        }, '生成邀请码成功', 201)
        
    except Exception as e:
        logger.error(f"生成邀请码失败: {e}")
        return error_response(500, f'生成邀请码失败: {str(e)}')

@invitation_codes_bp.route('/', methods=['GET'])
@require_internal_user()
def get_invitation_codes():
    """
    获取邀请码列表
    
    查询参数:
        status: 状态筛选 (active/expired/disabled)
        created_by: 创建者ID筛选
        page: 页码 (默认1)
        size: 每页数量 (默认20)
    
    返回:
        邀请码列表和分页信息
    """
    try:
        # 获取查询参数
        status = request.args.get('status', '').strip()
        created_by_param = request.args.get('created_by', '').strip()
        page = request.args.get('page', 1, type=int)
        size = min(request.args.get('size', 20, type=int), 100)  # 限制最大每页数量
        
        # 验证页码
        if page < 1:
            page = 1
        if size < 1:
            size = 20
        
        # 处理created_by参数
        created_by = None
        if created_by_param:
            try:
                created_by = int(created_by_param)
            except ValueError:
                return error_response(400, 'created_by必须是数字')
        
        # 获取当前用户信息
        current_user = get_current_user_info()
        
        # 如果不是管理员，只能查看自己创建的邀请码
        if current_user and not current_user.get('role_code') == 'admin':
            created_by = current_user['id']
        
        # 获取邀请码列表
        result = InvitationService.get_invitation_codes(
            created_by=created_by,
            status=status,
            page=page,
            size=size
        )
        
        return success_response(result, '获取邀请码列表成功')
        
    except Exception as e:
        logger.error(f"获取邀请码列表失败: {e}")
        return error_response(500, f'获取邀请码列表失败: {str(e)}')

@invitation_codes_bp.route('/<int:invitation_id>/disable', methods=['POST'])
@require_permission('role_management')
def disable_invitation_code(invitation_id):
    """
    禁用邀请码
    
    路径参数:
        invitation_id: 邀请码ID
    
    返回:
        禁用结果
    """
    try:
        success = InvitationService.disable_invitation_code(invitation_id)
        
        if success:
            current_user = get_current_user_info()
            logger.info(f"用户 {current_user.get('username', 'unknown')} 禁用了邀请码: {invitation_id}")
            
            return success_response({
                'invitation_id': invitation_id,
                'status': 'disabled'
            }, '禁用邀请码成功')
        else:
            return error_response(500, '禁用邀请码失败')
        
    except Exception as e:
        logger.error(f"禁用邀请码失败: {e}")
        return error_response(500, f'禁用邀请码失败: {str(e)}')

@invitation_codes_bp.route('/validate', methods=['POST'])
def validate_invitation_code():
    """
    验证邀请码（公开接口，用于注册时验证）
    
    请求体:
        code: 邀请码 (必填)
    
    返回:
        验证结果和邀请码信息
    """
    try:
        data = request.get_json()
        
        if not data or 'code' not in data or not data['code']:
            return error_response(400, '请提供邀请码')
        
        code = data['code'].strip()
        
        # 获取客户端IP地址
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent', '')
        
        # 验证邀请码
        is_valid, error_message, invitation_info = InvitationService.validate_invitation_code(
            code=code,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if is_valid:
            # 返回验证成功的信息（不包含敏感数据）
            return success_response({
                'valid': True,
                'code_type': invitation_info.get('code_type'),
                'allowed_roles': invitation_info.get('allowed_roles'),
                'expires_at': invitation_info.get('expires_at').isoformat() if invitation_info.get('expires_at') else None
            }, '邀请码验证成功')
        else:
            # 记录验证失败的尝试
            logger.warning(f"邀请码验证失败: code={code}, ip={ip_address}, error={error_message}")
            
            return error_response(400, error_message)
        
    except Exception as e:
        logger.error(f"邀请码验证异常: {e}")
        return error_response(500, f'邀请码验证失败: {str(e)}')

@invitation_codes_bp.route('/generate-batch', methods=['POST'])
@require_permission('role_management')
def generate_batch_invitation_codes():
    """
    批量生成邀请码
    
    请求体:
        code_type: 邀请码类型 (staff/admin) (必填)
        allowed_roles: 允许注册的角色列表 (必填)
        count: 生成数量 (必填，最大50)
        expires_in_days: 有效期天数 (默认30天)
        max_uses: 最大使用次数 (默认1次)
        ip_whitelist: IP白名单列表 (可选)
    
    返回:
        批量生成的邀请码列表
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['code_type', 'allowed_roles', 'count']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(400, validation_error)
        
        # 验证生成数量
        count = data['count']
        if not isinstance(count, int) or count < 1 or count > 50:
            return error_response(400, '生成数量必须是1-50之间的整数')
        
        # 验证邀请码类型
        if data['code_type'] not in ['staff', 'admin']:
            return error_response(400, 'code_type必须是staff或admin')
        
        # 验证允许的角色列表
        if not isinstance(data['allowed_roles'], list) or not data['allowed_roles']:
            return error_response(400, 'allowed_roles必须是非空数组')
        
        # 获取当前用户信息
        current_user = get_current_user_info()
        created_by = current_user['id'] if current_user else None
        
        # 批量生成邀请码
        invitation_codes = []
        failed_count = 0
        
        for i in range(count):
            try:
                invitation_code = InvitationService.create_invitation_code(
                    code_type=data['code_type'],
                    allowed_roles=data['allowed_roles'],
                    created_by=created_by,
                    expires_in_days=data.get('expires_in_days', 30),
                    max_uses=data.get('max_uses', 1),
                    ip_whitelist=data.get('ip_whitelist', [])
                )
                invitation_codes.append(invitation_code)
            except Exception as e:
                logger.error(f"批量生成邀请码失败 (第{i+1}个): {e}")
                failed_count += 1
        
        success_count = len(invitation_codes)
        
        logger.info(f"用户 {current_user.get('username', 'unknown')} 批量生成邀请码: 成功{success_count}个，失败{failed_count}个")
        
        return success_response({
            'invitation_codes': invitation_codes,
            'success_count': success_count,
            'failed_count': failed_count,
            'total_requested': count
        }, f'批量生成邀请码完成，成功{success_count}个', 201)
        
    except Exception as e:
        logger.error(f"批量生成邀请码失败: {e}")
        return error_response(500, f'批量生成邀请码失败: {str(e)}')

@invitation_codes_bp.route('/usage-stats', methods=['GET'])
@require_internal_user()
def get_invitation_usage_stats():
    """
    获取邀请码使用统计
    
    返回:
        邀请码使用统计信息
    """
    try:
        from sqlalchemy import text
        from models import db
        
        # 获取基本统计
        stats_sql = """
        SELECT 
            status,
            code_type,
            COUNT(*) as count
        FROM invitation_codes
        GROUP BY status, code_type
        ORDER BY status, code_type
        """
        
        stats_result = db.session.execute(text(stats_sql)).fetchall()
        
        # 获取使用情况统计
        usage_sql = """
        SELECT 
            ic.code_type,
            COUNT(DISTINCT ic.id) as total_codes,
            SUM(ic.used_count) as total_uses,
            COUNT(DISTINCT icu.user_id) as unique_users
        FROM invitation_codes ic
        LEFT JOIN invitation_code_uses icu ON ic.id = icu.invitation_code_id
        GROUP BY ic.code_type
        """
        
        usage_result = db.session.execute(text(usage_sql)).fetchall()
        
        # 格式化统计数据
        status_stats = {}
        for row in stats_result:
            status, code_type, count = row
            if status not in status_stats:
                status_stats[status] = {}
            status_stats[status][code_type] = count
        
        usage_stats = {}
        for row in usage_result:
            code_type, total_codes, total_uses, unique_users = row
            usage_stats[code_type] = {
                'total_codes': total_codes,
                'total_uses': total_uses or 0,
                'unique_users': unique_users or 0
            }
        
        return success_response({
            'status_stats': status_stats,
            'usage_stats': usage_stats
        }, '获取邀请码统计成功')
        
    except Exception as e:
        logger.error(f"获取邀请码统计失败: {e}")
        return error_response(500, f'获取邀请码统计失败: {str(e)}')