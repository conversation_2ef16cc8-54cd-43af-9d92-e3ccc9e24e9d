#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 权限管理API
版本: 1.0
创建时间: 2025-07-24

提供权限查询和管理的API接口
"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from services.role_service import RoleService
from utils.permissions import require_permission, get_current_user_info
from utils.response import success_response, error_response
from sqlalchemy import text
from models import db
import logging

logger = logging.getLogger(__name__)

# 创建权限管理蓝图
permissions_bp = Blueprint('permissions_v1', __name__)

@permissions_bp.route('/', methods=['GET'])
@jwt_required()
def get_permissions():
    """
    获取系统权限列表
    
    查询参数:
        category: 权限分类筛选
        module: 权限模块筛选
        include_inactive: 是否包含非活跃权限 (true/false)
    
    返回:
        权限列表
    """
    try:
        # 获取查询参数
        category = request.args.get('category', '').strip()
        module = request.args.get('module', '').strip()
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        # 构建查询条件
        conditions = []
        params = {}
        
        if not include_inactive:
            conditions.append("is_active = true")
        
        if category:
            conditions.append("category = :category")
            params['category'] = category
        
        if module:
            conditions.append("module = :module")
            params['module'] = module
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        # 查询权限
        sql = f"""
        SELECT id, permission_code, permission_name, description, 
               category, module, is_active, created_at
        FROM system_permissions
        {where_clause}
        ORDER BY category, module, permission_code
        """
        
        result = db.session.execute(text(sql), params).fetchall()
        
        permissions = []
        for row in result:
            permissions.append({
                'id': row[0],
                'permission_code': row[1],
                'permission_name': row[2],
                'description': row[3],
                'category': row[4],
                'module': row[5],
                'is_active': row[6],
                'created_at': row[7].isoformat() if row[7] else None
            })
        
        return success_response({
            'permissions': permissions,
            'total': len(permissions)
        }, '获取权限列表成功')
        
    except Exception as e:
        logger.error(f"获取权限列表失败: {e}")
        return error_response(500, f'获取权限列表失败: {str(e)}')

@permissions_bp.route('/categories', methods=['GET'])
@jwt_required()
def get_permission_categories():
    """
    获取权限分类列表
    
    返回:
        权限分类列表和每个分类下的权限数量
    """
    try:
        sql = """
        SELECT category, COUNT(*) as count
        FROM system_permissions
        WHERE is_active = true
        GROUP BY category
        ORDER BY category
        """
        
        result = db.session.execute(text(sql)).fetchall()
        
        categories = []
        for row in result:
            categories.append({
                'category': row[0],
                'count': row[1]
            })
        
        return success_response({
            'categories': categories,
            'total': len(categories)
        }, '获取权限分类成功')
        
    except Exception as e:
        logger.error(f"获取权限分类失败: {e}")
        return error_response(500, f'获取权限分类失败: {str(e)}')

@permissions_bp.route('/modules', methods=['GET'])
@jwt_required()
def get_permission_modules():
    """
    获取权限模块列表
    
    返回:
        权限模块列表和每个模块下的权限数量
    """
    try:
        sql = """
        SELECT module, category, COUNT(*) as count
        FROM system_permissions
        WHERE is_active = true
        GROUP BY module, category
        ORDER BY category, module
        """
        
        result = db.session.execute(text(sql)).fetchall()
        
        modules = []
        for row in result:
            modules.append({
                'module': row[0],
                'category': row[1],
                'count': row[2]
            })
        
        return success_response({
            'modules': modules,
            'total': len(modules)
        }, '获取权限模块成功')
        
    except Exception as e:
        logger.error(f"获取权限模块失败: {e}")
        return error_response(500, f'获取权限模块失败: {str(e)}')

@permissions_bp.route('/<int:permission_id>', methods=['GET'])
@jwt_required()
def get_permission(permission_id):
    """
    根据ID获取权限详情
    
    路径参数:
        permission_id: 权限ID
    
    返回:
        权限详情信息
    """
    try:
        sql = """
        SELECT id, permission_code, permission_name, description,
               category, module, is_active, created_at
        FROM system_permissions
        WHERE id = :permission_id
        """
        
        result = db.session.execute(text(sql), {'permission_id': permission_id}).fetchone()
        
        if not result:
            return error_response(404, '权限不存在')
        
        permission = {
            'id': result[0],
            'permission_code': result[1],
            'permission_name': result[2],
            'description': result[3],
            'category': result[4],
            'module': result[5],
            'is_active': result[6],
            'created_at': result[7].isoformat() if result[7] else None
        }
        
        return success_response({
            'permission': permission
        }, '获取权限详情成功')
        
    except Exception as e:
        logger.error(f"获取权限详情失败: {e}")
        return error_response(500, f'获取权限详情失败: {str(e)}')

@permissions_bp.route('/', methods=['POST'])
@require_permission('role_management')
def create_permission():
    """
    创建新权限
    
    请求体:
        permission_code: 权限代码 (必填)
        permission_name: 权限名称 (必填)
        description: 权限描述
        category: 权限分类 (必填)
        module: 权限模块 (必填)
        is_active: 是否启用
    
    返回:
        创建的权限信息
    """
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['permission_code', 'permission_name', 'category', 'module']
        for field in required_fields:
            if not data or field not in data or not data[field]:
                return error_response(400, f'缺少必填字段: {field}')
        
        # 检查权限代码是否已存在
        check_sql = "SELECT id FROM system_permissions WHERE permission_code = :permission_code"
        existing = db.session.execute(text(check_sql), {'permission_code': data['permission_code']}).fetchone()
        
        if existing:
            return error_response(409, f"权限代码已存在: {data['permission_code']}")
        
        # 插入新权限
        insert_sql = """
        INSERT INTO system_permissions (
            permission_code, permission_name, description, category, module, is_active
        ) VALUES (
            :permission_code, :permission_name, :description, :category, :module, :is_active
        ) RETURNING id
        """
        
        insert_data = {
            'permission_code': data['permission_code'],
            'permission_name': data['permission_name'],
            'description': data.get('description', ''),
            'category': data['category'],
            'module': data['module'],
            'is_active': data.get('is_active', True)
        }
        
        result = db.session.execute(text(insert_sql), insert_data)
        permission_id = result.fetchone()[0]
        db.session.commit()
        
        # 获取新创建的权限信息
        new_permission = db.session.execute(
            text("SELECT id, permission_code, permission_name, description, category, module, is_active, created_at FROM system_permissions WHERE id = :id"),
            {'id': permission_id}
        ).fetchone()
        
        permission_info = {
            'id': new_permission[0],
            'permission_code': new_permission[1],
            'permission_name': new_permission[2],
            'description': new_permission[3],
            'category': new_permission[4],
            'module': new_permission[5],
            'is_active': new_permission[6],
            'created_at': new_permission[7].isoformat() if new_permission[7] else None
        }
        
        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username', 'unknown')} 创建了权限: {data['permission_code']}")
        
        return success_response({
            'permission': permission_info
        }, '创建权限成功', 201)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建权限失败: {e}")
        return error_response(500, f'创建权限失败: {str(e)}')

@permissions_bp.route('/<int:permission_id>', methods=['PUT'])
@require_permission('role_management')
def update_permission(permission_id):
    """
    更新权限信息
    
    路径参数:
        permission_id: 权限ID
    
    请求体:
        可包含任何权限字段进行部分更新
    
    返回:
        更新后的权限信息
    """
    try:
        data = request.get_json()
        
        if not data:
            return error_response(400, '请提供要更新的数据')
        
        # 检查权限是否存在
        check_sql = "SELECT id FROM system_permissions WHERE id = :permission_id"
        existing = db.session.execute(text(check_sql), {'permission_id': permission_id}).fetchone()
        
        if not existing:
            return error_response(404, '权限不存在')
        
        # 如果更新权限代码，检查是否冲突
        if 'permission_code' in data:
            code_check_sql = "SELECT id FROM system_permissions WHERE permission_code = :permission_code AND id != :permission_id"
            code_conflict = db.session.execute(text(code_check_sql), {
                'permission_code': data['permission_code'],
                'permission_id': permission_id
            }).fetchone()
            
            if code_conflict:
                return error_response(409, f"权限代码已存在: {data['permission_code']}")
        
        # 构建更新SQL
        update_fields = []
        update_values = {'permission_id': permission_id}
        
        updatable_fields = ['permission_code', 'permission_name', 'description', 'category', 'module', 'is_active']
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = :{field}")
                update_values[field] = data[field]
        
        if not update_fields:
            return error_response(400, '没有要更新的字段')
        
        sql = f"""
        UPDATE system_permissions 
        SET {', '.join(update_fields)}
        WHERE id = :permission_id
        """
        
        db.session.execute(text(sql), update_values)
        db.session.commit()
        
        # 获取更新后的权限信息
        updated_permission = db.session.execute(
            text("SELECT id, permission_code, permission_name, description, category, module, is_active, created_at FROM system_permissions WHERE id = :id"),
            {'id': permission_id}
        ).fetchone()
        
        permission_info = {
            'id': updated_permission[0],
            'permission_code': updated_permission[1],
            'permission_name': updated_permission[2],
            'description': updated_permission[3],
            'category': updated_permission[4],
            'module': updated_permission[5],
            'is_active': updated_permission[6],
            'created_at': updated_permission[7].isoformat() if updated_permission[7] else None
        }
        
        # 记录操作日志
        current_user = get_current_user_info()
        logger.info(f"用户 {current_user.get('username', 'unknown')} 更新了权限: {permission_id}")
        
        return success_response({
            'permission': permission_info
        }, '更新权限成功')
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新权限失败: {e}")
        return error_response(500, f'更新权限失败: {str(e)}')

@permissions_bp.route('/my-permissions', methods=['GET'])
@jwt_required()
def get_my_permissions():
    """
    获取当前用户的权限列表
    
    返回:
        当前用户的权限列表
    """
    try:
        current_user = get_current_user_info()
        
        if not current_user:
            return error_response(401, '用户信息无效')
        
        user_id = current_user['id']
        permissions = RoleService.get_user_permissions(user_id)
        
        # 获取权限详细信息
        if permissions:
            placeholders = ', '.join([':perm' + str(i) for i in range(len(permissions))])
            params = {f'perm{i}': perm for i, perm in enumerate(permissions)}
            
            detail_sql = f"""
            SELECT permission_code, permission_name, description, category, module
            FROM system_permissions
            WHERE permission_code IN ({placeholders}) AND is_active = true
            ORDER BY category, module, permission_code
            """
            
            result = db.session.execute(text(detail_sql), params).fetchall()
            
            permission_details = []
            for row in result:
                permission_details.append({
                    'permission_code': row[0],
                    'permission_name': row[1],
                    'description': row[2],
                    'category': row[3],
                    'module': row[4]
                })
        else:
            permission_details = []
        
        return success_response({
            'user_id': user_id,
            'role_code': current_user.get('role_code'),
            'role_name': current_user.get('role_name'),
            'permissions': permission_details,
            'total': len(permission_details)
        }, '获取用户权限成功')
        
    except Exception as e:
        logger.error(f"获取用户权限失败: {e}")
        return error_response(500, f'获取用户权限失败: {str(e)}')