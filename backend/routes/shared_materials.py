"""
航材共享保障平台 - 共享件管理API路由
功能：共享件发布、审核、搜索、交易等核心业务功能
作者：Claude AI Assistant
创建时间：2025-07-18
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from utils.response import success_response, error_response
from utils.decorators import handle_exceptions, require_permission, audit_action, data_isolation
from models import db, User, Material, SharedMaterial, Order, WorkflowInstance
from services.data_isolation_service import DataIsolationService
from services.simple_workflow_service import workflow_service
from datetime import datetime, timedelta
from sqlalchemy import func, desc, or_, and_
import json

# 创建共享件管理蓝图
shared_materials_bp = Blueprint('shared_materials', __name__, url_prefix='/shared-materials')



@shared_materials_bp.route('/publish/', methods=['POST'])
@require_permission('publish_shared')
@audit_action('publish_shared_material', 'shared_material')
@handle_exceptions
def publish_shared_material():
    """
    发布共享件
    功能描述：用户发布可共享的航材到平台
    入参：{
        "material_id": 1,
        "share_type": "sale",
        "share_quantity": 5,
        "reserved_quantity": 2,
        "price": 125000,
        "description": "CFM56发动机燃油喷嘴，状态良好",
        "sharing_policy": "immediate",
        "expiry_date": "2025-12-31",
        "min_order_quantity": 1
    }
    返回参数：{
        "shared_material_id": 1,
        "status": "pending_review",
        "workflow_instance_id": 1,
        "message": "共享件发布成功，等待审核"
    }
    url地址：/api/shared-materials/publish
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['material_id', 'share_type', 'share_quantity', 'price']
        for field in required_fields:
            if field not in data:
                return error_response(f"缺少必填字段：{field}")
        
        # 验证用户权限
        user = User.query.get(current_user_id)
        if not user:
            return error_response("用户不存在")
        
        # 验证航材是否存在
        material = Material.query.get(data['material_id'])
        if not material:
            return error_response("航材不存在")
        
        # 验证共享数量
        if data['share_quantity'] <= 0:
            return error_response("共享数量必须大于0")
        
        # 验证价格
        if data['price'] <= 0:
            return error_response("价格必须大于0")
        
        # 创建共享件记录
        shared_material = SharedMaterial(
            material_id=data['material_id'],
            owner_id=current_user_id,
            share_type=data.get('share_type', 'sale'),
            share_quantity=data['share_quantity'],
            reserved_quantity=data.get('reserved_quantity', 0),
            available_quantity=data['share_quantity'],
            price=data['price'],
            description=data.get('description', ''),
            sharing_policy=data.get('sharing_policy', 'immediate'),
            expiry_date=datetime.fromisoformat(data['expiry_date']) if data.get('expiry_date') else None,
            min_order_quantity=data.get('min_order_quantity', 1),
            status='pending_review',
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.session.add(shared_material)
        db.session.flush()  # 获取shared_material.id
        
        # 启动审核工作流
        workflow_data = {
            'shared_material_id': shared_material.id,
            'material_name': material.name,
            'owner_name': user.real_name or user.username,
            'share_quantity': data['share_quantity'],
            'price': data['price'],
            'share_type': data.get('share_type', 'sale')
        }
        
        workflow_data['business_key'] = f"shared_material_{shared_material.id}"
        workflow_data['business_type'] = 'shared_material'
        workflow_data['business_id'] = shared_material.id
        
        workflow_instance = workflow_service.start_workflow(
            'shared_material_approval',
            workflow_data,
            current_user_id
        )
        
        # 更新共享件的工作流实例ID
        shared_material.workflow_instance_id = workflow_instance.id
        
        db.session.commit()
        
        return success_response({
            'shared_material_id': shared_material.id,
            'status': shared_material.status,
            'workflow_instance_id': workflow_instance.id,
            'message': '共享件发布成功，等待审核'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"发布共享件失败：{str(e)}")
        return error_response(f"发布共享件失败：{str(e)}")


@shared_materials_bp.route('/list/', methods=['GET'])
@data_isolation(SharedMaterial, 'owner_id')
@audit_action('view_shared_materials_list', 'shared_material')
@handle_exceptions
def get_shared_materials(current_user=None):
    """
    获取共享件列表
    功能描述：获取可共享的航材列表，支持搜索和筛选
    入参：{
        "page": 1,
        "per_page": 20,
        "search": "CFM56",
        "category": "发动机",
        "share_type": "sale",
        "status": "approved",
        "price_min": 10000,
        "price_max": 500000,
        "sort_by": "created_at",
        "sort_order": "desc"
    }
    返回参数：{
        "shared_materials": [...],
        "total": 100,
        "page": 1,
        "per_page": 20,
        "pages": 5
    }
    url地址：/api/shared-materials/list
    请求方式：GET
    """
    try:
        # current_user由data_isolation装饰器提供
        if not current_user:
            return error_response(401, '用户认证失败')
            
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        share_type = request.args.get('share_type', '')
        status = request.args.get('status', 'approved')
        price_min = request.args.get('price_min', type=float)
        price_max = request.args.get('price_max', type=float)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 构建查询
        query = db.session.query(SharedMaterial, Material, User).join(
            Material, SharedMaterial.material_id == Material.id
        ).join(
            User, SharedMaterial.owner_id == User.id
        )
        
        # 【第一阶段重要更新】应用数据隔离过滤
        shared_query = db.session.query(SharedMaterial)
        shared_query = DataIsolationService.filter_query_by_user(shared_query, SharedMaterial, current_user)
        shared_ids = [sm.id for sm in shared_query.all()]
        
        if shared_ids:
            query = query.filter(SharedMaterial.id.in_(shared_ids))
        else:
            # 如果用户没有权限查看任何共享件，返回空结果
            query = query.filter(False)
        
        # 记录访问日志
        DataIsolationService.log_data_access(
            user=current_user,
            resource_type='shared_material',
            resource_id='list',
            action='view_list',
            success=True
        )
        
        # 状态筛选
        if status:
            query = query.filter(SharedMaterial.status == status)
        
        # 可用数量大于0
        query = query.filter(SharedMaterial.available_quantity > 0)
        
        # 搜索条件
        if search:
            query = query.filter(
                or_(
                    Material.name.contains(search),
                    Material.part_number.contains(search),
                    Material.manufacturer.contains(search)
                )
            )
        
        # 类别筛选
        if category:
            query = query.filter(Material.category == category)
        
        # 共享类型筛选
        if share_type:
            query = query.filter(SharedMaterial.share_type == share_type)
        
        # 价格范围筛选
        if price_min is not None:
            query = query.filter(SharedMaterial.price >= price_min)
        if price_max is not None:
            query = query.filter(SharedMaterial.price <= price_max)
        
        # 过期日期筛选
        query = query.filter(
            or_(
                SharedMaterial.expiry_date.is_(None),
                SharedMaterial.expiry_date > datetime.now()
            )
        )
        
        # 排序
        if sort_by == 'price':
            if sort_order == 'asc':
                query = query.order_by(SharedMaterial.price.asc())
            else:
                query = query.order_by(SharedMaterial.price.desc())
        elif sort_by == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(SharedMaterial.created_at.asc())
            else:
                query = query.order_by(SharedMaterial.created_at.desc())
        
        # 分页
        total = query.count()
        shared_materials = query.offset((page - 1) * per_page).limit(per_page).all()
        
        # 格式化返回数据
        result = []
        for shared_material, material, owner in shared_materials:
            result.append({
                'id': shared_material.id,
                'material': {
                    'id': material.id,
                    'name': material.name,
                    'part_number': material.part_number,
                    'category': material.category,
                    'manufacturer': material.manufacturer,
                    'aircraft_type': material.aircraft_type,
                    'image_url': material.image_url
                },
                'owner': {
                    'id': owner.id,
                    'name': owner.real_name or owner.username,
                    'company': owner.company_name,
                    'user_type': owner.user_type
                },
                'share_type': shared_material.share_type,
                'share_quantity': shared_material.share_quantity,
                'available_quantity': shared_material.available_quantity,
                'reserved_quantity': shared_material.reserved_quantity,
                'price': float(shared_material.price),
                'min_order_quantity': shared_material.min_order_quantity,
                'description': shared_material.description,
                'sharing_policy': shared_material.sharing_policy,
                'status': shared_material.status,
                'expiry_date': shared_material.expiry_date.isoformat() if shared_material.expiry_date else None,
                'created_at': shared_material.created_at.isoformat(),
                'updated_at': shared_material.updated_at.isoformat()
            })
        
        return success_response({
            'shared_materials': result,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        current_app.logger.error(f"获取共享件列表失败：{str(e)}")
        return error_response(f"获取共享件列表失败：{str(e)}")


@shared_materials_bp.route('/<int:shared_material_id>/', methods=['GET'])
@handle_exceptions
def get_shared_material_detail(shared_material_id):
    """
    获取共享件详情
    功能描述：获取指定共享件的详细信息
    入参：shared_material_id (路径参数)
    返回参数：{
        "shared_material": {...},
        "material": {...},
        "owner": {...},
        "inquiry_count": 15,
        "recent_inquiries": [...]
    }
    url地址：/api/shared-materials/<int:shared_material_id>
    请求方式：GET
    """
    try:
        # 获取共享件详情
        shared_material = db.session.query(SharedMaterial, Material, User).join(
            Material, SharedMaterial.material_id == Material.id
        ).join(
            User, SharedMaterial.owner_id == User.id
        ).filter(SharedMaterial.id == shared_material_id).first()
        
        if not shared_material:
            return error_response("共享件不存在")
        
        shared_material_obj, material, owner = shared_material
        
        # 获取询价统计
        inquiry_count = 0  # 实际应该查询询价表
        
        # 格式化返回数据
        result = {
            'shared_material': {
                'id': shared_material_obj.id,
                'share_type': shared_material_obj.share_type,
                'share_quantity': shared_material_obj.share_quantity,
                'available_quantity': shared_material_obj.available_quantity,
                'reserved_quantity': shared_material_obj.reserved_quantity,
                'price': float(shared_material_obj.price),
                'min_order_quantity': shared_material_obj.min_order_quantity,
                'description': shared_material_obj.description,
                'sharing_policy': shared_material_obj.sharing_policy,
                'status': shared_material_obj.status,
                'expiry_date': shared_material_obj.expiry_date.isoformat() if shared_material_obj.expiry_date else None,
                'created_at': shared_material_obj.created_at.isoformat(),
                'updated_at': shared_material_obj.updated_at.isoformat()
            },
            'material': {
                'id': material.id,
                'name': material.name,
                'part_number': material.part_number,
                'category': material.category,
                'manufacturer': material.manufacturer,
                'aircraft_type': material.aircraft_type,
                'model': material.model,
                'specifications': material.specifications,
                'image_url': material.image_url,
                'description': material.description
            },
            'owner': {
                'id': owner.id,
                'name': owner.real_name or owner.username,
                'company': owner.company_name,
                'user_type': owner.user_type,
                'phone': owner.phone if owner.phone else None
            },
            'inquiry_count': inquiry_count,
            'recent_inquiries': []  # 实际应该查询最近询价记录
        }
        
        return success_response(result)
        
    except Exception as e:
        current_app.logger.error(f"获取共享件详情失败：{str(e)}")
        return error_response(f"获取共享件详情失败：{str(e)}")


@shared_materials_bp.route('/my-published/', methods=['GET'])
@jwt_required()
@handle_exceptions
def get_my_published_materials():
    """
    获取我发布的共享件
    功能描述：获取当前用户发布的共享件列表
    入参：{
        "page": 1,
        "per_page": 20,
        "status": "all"
    }
    返回参数：{
        "shared_materials": [...],
        "statistics": {
            "total": 50,
            "approved": 30,
            "pending": 15,
            "rejected": 5
        }
    }
    url地址：/api/shared-materials/my-published
    请求方式：GET
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        status = request.args.get('status', 'all')
        
        # 构建查询
        query = db.session.query(SharedMaterial, Material).join(
            Material, SharedMaterial.material_id == Material.id
        ).filter(SharedMaterial.owner_id == current_user_id)
        
        # 状态筛选
        if status != 'all':
            query = query.filter(SharedMaterial.status == status)
        
        # 按创建时间倒序
        query = query.order_by(SharedMaterial.created_at.desc())
        
        # 分页
        total = query.count()
        shared_materials = query.offset((page - 1) * per_page).limit(per_page).all()
        
        # 获取统计信息
        stats_query = db.session.query(
            SharedMaterial.status,
            func.count(SharedMaterial.id).label('count')
        ).filter(SharedMaterial.owner_id == current_user_id).group_by(SharedMaterial.status)
        
        statistics = {
            'total': total,
            'approved': 0,
            'pending_review': 0,
            'rejected': 0
        }
        
        for stat in stats_query.all():
            if stat.status in statistics:
                statistics[stat.status] = stat.count
        
        # 格式化返回数据
        result = []
        for shared_material, material in shared_materials:
            result.append({
                'id': shared_material.id,
                'material': {
                    'id': material.id,
                    'name': material.name,
                    'part_number': material.part_number,
                    'category': material.category,
                    'manufacturer': material.manufacturer,
                    'image_url': material.image_url
                },
                'share_type': shared_material.share_type,
                'share_quantity': shared_material.share_quantity,
                'available_quantity': shared_material.available_quantity,
                'price': float(shared_material.price),
                'status': shared_material.status,
                'created_at': shared_material.created_at.isoformat(),
                'updated_at': shared_material.updated_at.isoformat()
            })
        
        return success_response({
            'shared_materials': result,
            'statistics': statistics,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        current_app.logger.error(f"获取我发布的共享件失败：{str(e)}")
        return error_response(f"获取我发布的共享件失败：{str(e)}")


@shared_materials_bp.route('/<int:shared_material_id>/approve/', methods=['POST'])
@jwt_required()
@handle_exceptions
def approve_shared_material(shared_material_id):
    """
    审核共享件
    功能描述：管理员审核共享件发布申请
    入参：{
        "action": "approve",
        "comment": "审核通过"
    }
    返回参数：{
        "message": "审核操作成功",
        "new_status": "approved"
    }
    url地址：/api/shared-materials/<int:shared_material_id>/approve
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证用户权限
        user = User.query.get(current_user_id)
        if not user or user.user_type != 'admin':
            return error_response("无权限进行此操作")
        
        # 获取共享件
        shared_material = SharedMaterial.query.get(shared_material_id)
        if not shared_material:
            return error_response("共享件不存在")
        
        action = data.get('action')
        comment = data.get('comment', '')
        
        if action not in ['approve', 'reject']:
            return error_response("无效的审核操作")
        
        # 更新共享件状态
        if action == 'approve':
            shared_material.status = 'approved'
            shared_material.approved_at = datetime.now()
            shared_material.approved_by = current_user_id
            message = "共享件审核通过"
        else:
            shared_material.status = 'rejected'
            shared_material.rejected_at = datetime.now()
            shared_material.rejected_by = current_user_id
            message = "共享件审核拒绝"
        
        shared_material.review_comment = comment
        shared_material.updated_at = datetime.now()
        
        # 如果有工作流实例，完成相应的任务
        if shared_material.workflow_instance_id:
            try:
                # 获取当前待处理的任务
                tasks = workflow_service.get_workflow_tasks(shared_material.workflow_instance_id)
                for task in tasks:
                    if task.status == 'ready':
                        # 完成任务
                        workflow_service.complete_task(
                            shared_material.workflow_instance_id,
                            task.task_key,
                            current_user_id,
                            {
                                'action': action,
                                'comment': comment,
                                'approved': action == 'approve'
                            }
                        )
                        break
            except Exception as workflow_error:
                current_app.logger.warning(f"完成工作流任务失败：{str(workflow_error)}")
        
        db.session.commit()
        
        return success_response({
            'message': message,
            'new_status': shared_material.status
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核共享件失败：{str(e)}")
        return error_response(f"审核共享件失败：{str(e)}")


@shared_materials_bp.route('/<int:shared_material_id>/inquiry/', methods=['POST'])
@jwt_required()
@handle_exceptions
def create_inquiry(shared_material_id):
    """
    创建询价
    功能描述：用户对共享件进行询价
    入参：{
        "quantity": 2,
        "target_price": 120000,
        "message": "请问是否可以优惠？",
        "urgency": "normal"
    }
    返回参数：{
        "inquiry_id": 1,
        "message": "询价创建成功"
    }
    url地址：/api/shared-materials/<int:shared_material_id>/inquiry
    请求方式：POST
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证共享件是否存在
        shared_material = SharedMaterial.query.get(shared_material_id)
        if not shared_material:
            return error_response("共享件不存在")
        
        if shared_material.status != 'approved':
            return error_response("该共享件未通过审核")
        
        if shared_material.owner_id == current_user_id:
            return error_response("不能询价自己发布的共享件")
        
        # 验证询价数量
        quantity = data.get('quantity', 1)
        if quantity <= 0:
            return error_response("询价数量必须大于0")
        
        if quantity > shared_material.available_quantity:
            return error_response("询价数量超过可用数量")
        
        # 这里应该创建询价记录，但由于没有Inquiry模型，先返回模拟数据
        inquiry_id = 1  # 模拟询价ID
        
        return success_response({
            'inquiry_id': inquiry_id,
            'message': '询价创建成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"创建询价失败：{str(e)}")
        return error_response(f"创建询价失败：{str(e)}")


@shared_materials_bp.route('/categories/', methods=['GET'])
@handle_exceptions
def get_material_categories():
    """
    获取航材分类
    功能描述：获取所有航材分类列表
    入参：无
    返回参数：{
        "categories": [
            {
                "name": "发动机",
                "count": 150,
                "subcategories": ["燃油系统", "点火系统", "控制系统"]
            }
        ]
    }
    url地址：/api/shared-materials/categories
    请求方式：GET
    """
    try:
        # 获取分类统计
        categories_query = db.session.query(
            Material.category,
            func.count(SharedMaterial.id).label('count')
        ).join(
            SharedMaterial, Material.id == SharedMaterial.material_id
        ).filter(
            SharedMaterial.status == 'approved',
            SharedMaterial.available_quantity > 0
        ).group_by(Material.category).all()
        
        categories = []
        for category, count in categories_query:
            categories.append({
                'name': category,
                'count': count,
                'subcategories': []  # 实际应该查询子分类
            })
        
        return success_response({'categories': categories})
        
    except Exception as e:
        current_app.logger.error(f"获取航材分类失败：{str(e)}")
        return error_response(f"获取航材分类失败：{str(e)}")


@shared_materials_bp.route('/search-suggestions/', methods=['GET'])
@handle_exceptions
def get_search_suggestions():
    """
    获取搜索建议
    功能描述：根据用户输入获取搜索建议
    入参：{ "query": "CFM" }
    返回参数：{
        "suggestions": [
            {
                "type": "material",
                "text": "CFM56发动机燃油喷嘴",
                "part_number": "CFM56-001"
            }
        ]
    }
    url地址：/api/shared-materials/search-suggestions
    请求方式：GET
    """
    try:
        query = request.args.get('query', '')
        if not query or len(query) < 2:
            return success_response({'suggestions': []})
        
        # 查询匹配的航材
        materials = db.session.query(Material).join(
            SharedMaterial, Material.id == SharedMaterial.material_id
        ).filter(
            SharedMaterial.status == 'approved',
            SharedMaterial.available_quantity > 0,
            or_(
                Material.name.contains(query),
                Material.part_number.contains(query),
                Material.manufacturer.contains(query)
            )
        ).limit(10).all()
        
        suggestions = []
        for material in materials:
            suggestions.append({
                'type': 'material',
                'text': material.name,
                'part_number': material.part_number,
                'manufacturer': material.manufacturer
            })
        
        return success_response({'suggestions': suggestions})
        
    except Exception as e:
        current_app.logger.error(f"获取搜索建议失败：{str(e)}")
        return error_response(f"获取搜索建议失败：{str(e)}")


# 注册蓝图错误处理器
@shared_materials_bp.errorhandler(Exception)
def handle_shared_materials_error(error):
    """共享件模块统一错误处理"""
    current_app.logger.error(f"共享件模块发生错误：{str(error)}")
    return error_response(f"共享件操作失败：{str(error)}")