#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 用户认证路由
版本: 1.0
创建时间: 2025-01-13

处理用户登录、注册、JWT令牌管理等认证相关功能
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, create_access_token, create_refresh_token,
    get_jwt_identity, get_jwt
)
from werkzeug.security import check_password_hash, generate_password_hash

from models import db, User
from utils.validators import validate_email, validate_password
from utils.decorators import validate_json
from utils.response import success_response, error_response

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/debug/', methods=['GET'])
def debug_jwt():
    """调试JWT配置"""
    from flask import current_app
    return jsonify({
        'error': 0,
        'message': 'JWT配置信息',
        'body': {
            'JWT_SECRET_KEY': current_app.config.get('JWT_SECRET_KEY', 'NOT_SET'),
            'JWT_ALGORITHM': current_app.config.get('JWT_ALGORITHM', 'NOT_SET'),
            'JWT_CSRF_PROTECT': current_app.config.get('JWT_CSRF_PROTECT', 'NOT_SET'),
            'SECRET_KEY': current_app.config.get('SECRET_KEY', 'NOT_SET')[:10] + '...'
        },
        'success': True
    })

@auth_bp.route('/decode/', methods=['POST'])
def decode_jwt():
    """解码JWT token进行调试"""
    import jwt
    from flask import current_app

    data = request.get_json()
    if not data or 'token' not in data:
        return jsonify({
            'error': 400,
            'message': '请提供token参数',
            'success': False
        })

    token = data['token']
    secret = current_app.config.get('JWT_SECRET_KEY')

    try:
        # 解码token
        decoded = jwt.decode(token, secret, algorithms=['HS256'])
        return jsonify({
            'error': 0,
            'message': 'Token解码成功',
            'body': {
                'decoded': decoded,
                'secret_used': secret
            },
            'success': True
        })
    except jwt.ExpiredSignatureError:
        return jsonify({
            'error': 401,
            'message': 'Token已过期',
            'success': False
        })
    except jwt.InvalidTokenError as e:
        return jsonify({
            'error': 401,
            'message': f'Token无效: {str(e)}',
            'success': False
        })

@auth_bp.route('/login/', methods=['POST'])
@validate_json(['username', 'password'])
def login():
    """
    用户登录接口
    
    请求体:
        username: 用户名或邮箱
        password: 密码
        user_type: 用户类型(可选)
    
    返回:
        access_token: 访问令牌
        refresh_token: 刷新令牌
        user: 用户信息
    """
    try:
        data = request.get_json()
        
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        user_type = data.get('user_type', '').strip()
        
        # 验证输入
        if not username or not password:
            return error_response(400, 'Username and password are required')
        
        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if not user:
            return error_response(401, 'Invalid username or password')
        
        # 验证密码
        if not user.check_password(password):
            return error_response(401, 'Invalid username or password')
        
        # 检查用户状态
        if user.status != 'active':
            return error_response(403, 'Account is disabled, please contact administrator')
        
        # 检查用户类型（如果指定）
        if user_type and user.user_type != user_type:
            return error_response(403, 'User type does not match')
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 创建JWT令牌
        access_token = create_access_token(
            identity=str(user.id),  # JWT identity必须是字符串
            additional_claims={
                'user_type': user.user_type,
                'username': user.username
            }
        )
        refresh_token = create_refresh_token(identity=str(user.id))
        
        user_dict = user.to_dict()
        
        return success_response({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': 86400,  # 24小时（秒）
            'user': user_dict
        }, 'Login successful')
        
    except Exception as e:
        return error_response(500, f'Login failed: {str(e)}')

@auth_bp.route('/register/', methods=['POST'])
@validate_json(['username', 'email', 'password', 'user_type'])
def register():
    """
    用户注册接口
    
    请求体:
        username: 用户名
        email: 邮箱
        password: 密码
        user_type: 用户类型 (airline, supplier, maintenance)
        company_name: 公司名称
        real_name: 真实姓名
        phone: 联系电话
    
    返回:
        user: 注册成功的用户信息
    """
    try:
        data = request.get_json()
        
        # 获取必填字段
        username = data.get('username', '').strip()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '').strip()
        user_type = data.get('user_type', '').strip()
        
        # 获取可选字段
        company_name = data.get('company_name', '').strip()
        real_name = data.get('real_name', '').strip()
        phone = data.get('phone', '').strip()
        
        # 验证输入
        if not username or len(username) < 3:
            return error_response(400, '用户名长度至少3个字符')
        
        if not validate_email(email):
            return error_response(400, '邮箱格式不正确')
        
        if not validate_password(password):
            return error_response(400, '密码长度至少6个字符，且包含字母和数字')
        
        if user_type not in ['airline', 'supplier', 'maintenance']:
            return error_response(400, '用户类型无效')
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return error_response(409, '用户名已存在')
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return error_response(409, '邮箱已被注册')
        
        # 创建新用户
        user = User(
            username=username,
            email=email,
            user_type=user_type,
            company_name=company_name,
            real_name=real_name,
            phone=phone,
            status='active'
        )
        user.set_password(password)
        
        # 保存到数据库
        db.session.add(user)
        db.session.commit()
        
        return success_response({
            'user': user.to_dict()
        }, '注册成功', 201)
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'注册失败: {str(e)}')

@auth_bp.route('/refresh/', methods=['POST'])
@jwt_required(refresh=True)
def refresh_token():
    """
    刷新访问令牌接口
    
    返回:
        access_token: 新的访问令牌
    """
    try:
        current_user_id = get_jwt_identity()
        
        # 查找用户
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        if user.status != 'active':
            return error_response(403, '账户已被禁用')
        
        # 创建新的访问令牌
        new_access_token = create_access_token(
            identity=str(user.id),  # 确保identity是字符串类型
            additional_claims={
                'user_type': user.user_type,
                'username': user.username
            }
        )
        
        return success_response({
            'access_token': new_access_token,
            'token_type': 'Bearer',
            'expires_in': 86400
        }, '令牌刷新成功')
        
    except Exception as e:
        return error_response(500, f'令牌刷新失败: {str(e)}')

@auth_bp.route('/logout/', methods=['POST'])
@jwt_required()
def logout():
    """
    用户登出接口
    注意: 由于JWT的无状态特性，这里主要是客户端删除令牌
    如需要真正的令牌吊销，需要实现黑名单机制
    
    返回:
        success: 登出成功消息
    """
    try:
        # 这里可以添加令牌黑名单逻辑
        # jti = get_jwt()["jti"]
        # 将jti添加到黑名单
        
        return success_response({}, '登出成功')
        
    except Exception as e:
        return error_response(500, f'登出失败: {str(e)}')

@auth_bp.route('/profile/', methods=['GET'])
@jwt_required()
def get_profile():
    """
    获取当前用户信息接口
    
    返回:
        user: 当前用户信息
    """
    try:
        current_user_id = get_jwt_identity()
        
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        return success_response({
            'user': user.to_dict()
        }, '获取用户信息成功')
        
    except Exception as e:
        return error_response(500, f'获取用户信息失败: {str(e)}')

@auth_bp.route('/profile/', methods=['PUT'])
@jwt_required()
def update_profile():
    """
    更新用户信息接口
    
    请求体:
        real_name: 真实姓名
        phone: 联系电话
        company_name: 公司名称
    
    返回:
        user: 更新后的用户信息
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        # 更新可修改的字段
        if 'real_name' in data:
            user.real_name = data['real_name'].strip()
        
        if 'phone' in data:
            user.phone = data['phone'].strip()
        
        if 'company_name' in data:
            user.company_name = data['company_name'].strip()
        
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response({
            'user': user.to_dict()
        }, '用户信息更新成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'更新用户信息失败: {str(e)}')

@auth_bp.route('/change-password/', methods=['POST'])
@jwt_required()
@validate_json(['old_password', 'new_password'])
def change_password():
    """
    修改密码接口
    
    请求体:
        old_password: 旧密码
        new_password: 新密码
    
    返回:
        success: 密码修改成功消息
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        old_password = data.get('old_password', '').strip()
        new_password = data.get('new_password', '').strip()
        
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        # 验证旧密码
        if not user.check_password(old_password):
            return error_response(400, '旧密码错误')
        
        # 验证新密码
        if not validate_password(new_password):
            return error_response(400, '新密码长度至少6个字符，且包含字母和数字')
        
        if old_password == new_password:
            return error_response(400, '新密码不能与旧密码相同')
        
        # 更新密码
        user.set_password(new_password)
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response({}, '密码修改成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'密码修改失败: {str(e)}')

@auth_bp.route('/verify-token/', methods=['POST'])
@jwt_required()
def verify_token():
    """
    验证令牌有效性接口
    
    返回:
        valid: 令牌是否有效
        user: 用户信息（如果有效）
    """
    try:
        current_user_id = get_jwt_identity()
        jwt_claims = get_jwt()
        
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        if user.status != 'active':
            return error_response(403, '账户已被禁用')
        
        return success_response({
            'valid': True,
            'user': user.to_dict(),
            'claims': {
                'user_type': jwt_claims.get('user_type'),
                'username': jwt_claims.get('username'),
                'exp': jwt_claims.get('exp'),
                'iat': jwt_claims.get('iat')
            }
        }, '令牌验证成功')
        
    except Exception as e:
        return error_response(500, f'令牌验证失败: {str(e)}')

@auth_bp.route('/verify-token', methods=['GET'])
@jwt_required()
def verify_token_get():
    """
    验证令牌有效性接口 (GET版本)
    用于前端轻量级token验证检查
    
    返回:
        valid: 令牌是否有效
        user_id: 用户ID
        user_type: 用户类型
    """
    try:
        current_user_id = get_jwt_identity()
        jwt_claims = get_jwt()
        
        user = User.query.get(current_user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        if user.status != 'active':
            return error_response(403, '账户已被禁用')
        
        return success_response({
            'valid': True,
            'user_id': user.id,
            'user_type': user.user_type,
            'username': user.username
        }, '令牌验证成功')
        
    except Exception as e:
        return error_response(500, f'令牌验证失败: {str(e)}')

@auth_bp.route('/users/', methods=['GET'])
@jwt_required()
def get_users():
    """
    获取用户列表接口（管理员功能）
    
    查询参数:
        page: 页码
        size: 每页数量
        user_type: 用户类型筛选
        status: 状态筛选
        search: 搜索关键词
    
    返回:
        users: 用户列表
        pagination: 分页信息
    """
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        
        # 检查权限（仅管理员可查看所有用户）
        if current_user.user_type != 'admin':
            return error_response(403, '权限不足')
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        size = min(request.args.get('size', 20, type=int), 100)
        user_type = request.args.get('user_type', '').strip()
        status = request.args.get('status', '').strip()
        search = request.args.get('search', '').strip()
        
        # 构建查询
        query = User.query
        
        if user_type:
            query = query.filter(User.user_type == user_type)
        
        if status:
            query = query.filter(User.status == status)
        
        if search:
            query = query.filter(
                (User.username.contains(search)) |
                (User.email.contains(search)) |
                (User.real_name.contains(search)) |
                (User.company_name.contains(search))
            )
        
        # 执行分页查询
        pagination = query.order_by(User.created_at.desc()).paginate(
            page=page, per_page=size, error_out=False
        )
        
        return success_response({
            'users': [user.to_dict() for user in pagination.items],
            'pagination': {
                'page': page,
                'size': size,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }, '获取用户列表成功')
        
    except Exception as e:
        return error_response(500, f'获取用户列表失败: {str(e)}')

@auth_bp.route('/users/<int:user_id>/status/', methods=['PATCH'])
@jwt_required()
@validate_json(['status'])
def update_user_status(user_id):
    """
    更新用户状态接口（管理员功能）
    
    请求体:
        status: 新状态 (active, inactive, suspended)
    
    返回:
        user: 更新后的用户信息
    """
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        
        # 检查权限
        if current_user.user_type != 'admin':
            return error_response(403, '权限不足')
        
        data = request.get_json()
        status = data.get('status', '').strip()
        
        if status not in ['active', 'inactive', 'suspended']:
            return error_response(400, '无效的状态值')
        
        user = User.query.get(user_id)
        if not user:
            return error_response(404, '用户不存在')
        
        # 不能修改自己的状态
        if user.id == current_user_id:
            return error_response(400, '不能修改自己的状态')
        
        user.status = status
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response({
            'user': user.to_dict()
        }, '用户状态更新成功')
        
    except Exception as e:
        db.session.rollback()
        return error_response(500, f'更新用户状态失败: {str(e)}')