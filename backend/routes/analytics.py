#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据分析API路由
版本: 1.0
创建时间: 2025-01-13

处理业务数据分析、报表生成、趋势预测等功能
提供多维度的数据分析服务，支持业务决策
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, and_, or_, extract
import json
import io
import csv

from models import db, Order, Material, InventoryItem, User, AOGCase, Certificate
from utils.decorators import require_active_user, validate_json
from utils.response import success_response, error_response
from utils.validators import safe_cast

# 创建数据分析蓝图
analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/overview', methods=['GET'])
@jwt_required()
@require_active_user
def get_business_overview():
    """
    获取业务概览数据
    
    查询参数:
        time_range: 时间范围 (today, week, month, quarter, year)
        compare_previous: 是否对比上一周期 (true/false)
        
    返回:
        kpi_metrics: 核心KPI指标
        growth_rates: 增长率数据
        trend_data: 趋势数据
    """
    try:
        time_range = request.args.get('time_range', 'month')
        compare_previous = request.args.get('compare_previous', 'false').lower() == 'true'
        
        # 计算时间范围
        now = datetime.utcnow()
        if time_range == 'today':
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            prev_start = start_time - timedelta(days=1)
        elif time_range == 'week':
            start_time = now - timedelta(days=7)
            prev_start = start_time - timedelta(days=7)
        elif time_range == 'month':
            start_time = now - timedelta(days=30)
            prev_start = start_time - timedelta(days=30)
        elif time_range == 'quarter':
            start_time = now - timedelta(days=90)
            prev_start = start_time - timedelta(days=90)
        elif time_range == 'year':
            start_time = now - timedelta(days=365)
            prev_start = start_time - timedelta(days=365)
        else:
            start_time = now - timedelta(days=30)
            prev_start = start_time - timedelta(days=30)
        
        # 计算当前周期数据
        current_revenue = db.session.query(
            func.sum(Order.total_amount)
        ).filter(
            and_(
                Order.created_at >= start_time,
                Order.status.in_(['completed', 'shipping'])
            )
        ).scalar() or 0
        
        current_orders = Order.query.filter(
            Order.created_at >= start_time
        ).count()
        
        current_users = User.query.filter(
            User.last_login >= start_time
        ).count()
        
        # 计算转化率（完成订单/总订单）
        total_orders = Order.query.filter(
            Order.created_at >= start_time
        ).count()
        completed_orders = Order.query.filter(
            and_(
                Order.created_at >= start_time,
                Order.status == 'completed'
            )
        ).count()
        
        conversion_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
        
        # 计算上一周期数据（用于对比）
        growth_rates = {}
        if compare_previous:
            prev_revenue = db.session.query(
                func.sum(Order.total_amount)
            ).filter(
                and_(
                    Order.created_at >= prev_start,
                    Order.created_at < start_time,
                    Order.status.in_(['completed', 'shipping'])
                )
            ).scalar() or 0
            
            prev_orders = Order.query.filter(
                and_(
                    Order.created_at >= prev_start,
                    Order.created_at < start_time
                )
            ).count()
            
            prev_users = User.query.filter(
                and_(
                    User.last_login >= prev_start,
                    User.last_login < start_time
                )
            ).count()
            
            # 计算增长率
            growth_rates = {
                'revenue_growth': ((current_revenue - prev_revenue) / prev_revenue * 100) if prev_revenue > 0 else 0,
                'orders_growth': ((current_orders - prev_orders) / prev_orders * 100) if prev_orders > 0 else 0,
                'users_growth': ((current_users - prev_users) / prev_users * 100) if prev_users > 0 else 0
            }
        
        # 获取趋势数据（最近7天）
        trend_data = []
        for i in range(7):
            day_start = now - timedelta(days=i+1)
            day_end = day_start + timedelta(days=1)
            
            day_revenue = db.session.query(
                func.sum(Order.total_amount)
            ).filter(
                and_(
                    Order.created_at >= day_start,
                    Order.created_at < day_end,
                    Order.status.in_(['completed', 'shipping'])
                )
            ).scalar() or 0
            
            day_orders = Order.query.filter(
                and_(
                    Order.created_at >= day_start,
                    Order.created_at < day_end
                )
            ).count()
            
            trend_data.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'revenue': float(day_revenue),
                'orders': day_orders
            })
        
        trend_data.reverse()  # 按时间正序排列
        
        return success_response({
            'time_range': time_range,
            'kpi_metrics': {
                'revenue': float(current_revenue),
                'orders_count': current_orders,
                'active_users': current_users,
                'conversion_rate': round(conversion_rate, 2)
            },
            'growth_rates': growth_rates,
            'trend_data': trend_data,
            'summary': {
                'total_materials': Material.query.count(),
                'total_suppliers': User.query.filter_by(user_type='supplier').count(),
                'total_customers': User.query.filter_by(user_type='airline').count(),
                'aog_cases_resolved': AOGCase.query.filter_by(status='resolved').count()
            }
        }, '业务概览数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取业务概览失败: {str(e)}')

@analytics_bp.route('/sales', methods=['GET'])
@jwt_required()
@require_active_user
def get_sales_analytics():
    """
    获取销售分析数据
    
    查询参数:
        time_range: 时间范围
        group_by: 分组方式 (day, week, month)
        
    返回:
        sales_trend: 销售趋势
        top_products: 热销产品
        sales_funnel: 销售漏斗
        regional_sales: 区域销售分布
    """
    try:
        time_range = request.args.get('time_range', 'month')
        group_by = request.args.get('group_by', 'day')
        
        # 计算时间范围
        now = datetime.utcnow()
        if time_range == 'week':
            start_time = now - timedelta(days=7)
        elif time_range == 'month':
            start_time = now - timedelta(days=30)
        elif time_range == 'quarter':
            start_time = now - timedelta(days=90)
        else:
            start_time = now - timedelta(days=30)
        
        # 销售趋势分析
        if group_by == 'day':
            sales_trend = db.session.query(
                func.date(Order.created_at).label('date'),
                func.count(Order.id).label('orders'),
                func.sum(Order.total_amount).label('revenue')
            ).filter(
                Order.created_at >= start_time
            ).group_by(
                func.date(Order.created_at)
            ).order_by(
                func.date(Order.created_at)
            ).all()
        else:
            # 按周或月分组的逻辑可以进一步实现
            sales_trend = []
        
        trend_data = [
            {
                'date': item.date.strftime('%Y-%m-%d'),
                'orders': item.orders,
                'revenue': float(item.revenue or 0)
            }
            for item in sales_trend
        ]
        
        # 热销产品分析
        from models import OrderItem
        top_products = db.session.query(
            Material.part_number,
            Material.part_name,
            Material.category,
            func.sum(OrderItem.quantity).label('total_quantity'),
            func.sum(OrderItem.subtotal).label('total_revenue'),
            func.count(OrderItem.id).label('order_count')
        ).join(
            OrderItem, Material.id == OrderItem.material_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            Order.created_at >= start_time
        ).group_by(
            Material.id
        ).order_by(
            func.sum(OrderItem.subtotal).desc()
        ).limit(10).all()
        
        top_products_data = [
            {
                'part_number': item.part_number,
                'part_name': item.part_name,
                'category': item.category,
                'total_quantity': item.total_quantity,
                'total_revenue': float(item.total_revenue or 0),
                'order_count': item.order_count
            }
            for item in top_products
        ]
        
        # 销售漏斗分析
        total_inquiries = 1000  # 模拟数据，实际应从询价表获取
        total_quotes = Order.query.filter(
            Order.created_at >= start_time
        ).count()
        confirmed_orders = Order.query.filter(
            and_(
                Order.created_at >= start_time,
                Order.status.in_(['confirmed', 'processing', 'shipping', 'completed'])
            )
        ).count()
        completed_orders = Order.query.filter(
            and_(
                Order.created_at >= start_time,
                Order.status == 'completed'
            )
        ).count()
        
        sales_funnel = [
            {'stage': '潜在客户', 'count': total_inquiries, 'percentage': 100},
            {'stage': '询价客户', 'count': total_quotes, 'percentage': round(total_quotes/total_inquiries*100, 1)},
            {'stage': '确认订单', 'count': confirmed_orders, 'percentage': round(confirmed_orders/total_inquiries*100, 1)},
            {'stage': '完成交易', 'count': completed_orders, 'percentage': round(completed_orders/total_inquiries*100, 1)}
        ]
        
        # 区域销售分布（模拟数据）
        regional_sales = [
            {'region': '华北地区', 'revenue': 3200000, 'orders': 156, 'growth': 15.8},
            {'region': '华东地区', 'revenue': 4800000, 'orders': 234, 'growth': 22.3},
            {'region': '华南地区', 'revenue': 2800000, 'orders': 145, 'growth': 8.7},
            {'region': '西南地区', 'revenue': 1900000, 'orders': 89, 'growth': 12.1},
            {'region': '其他地区', 'revenue': 1300000, 'orders': 67, 'growth': 5.4}
        ]
        
        return success_response({
            'time_range': time_range,
            'sales_trend': trend_data,
            'top_products': top_products_data,
            'sales_funnel': sales_funnel,
            'regional_sales': regional_sales,
            'metrics': {
                'total_revenue': sum(item['revenue'] for item in trend_data),
                'total_orders': sum(item['orders'] for item in trend_data),
                'avg_order_value': sum(item['revenue'] for item in trend_data) / max(sum(item['orders'] for item in trend_data), 1),
                'conversion_rate': round(completed_orders / total_quotes * 100, 2) if total_quotes > 0 else 0
            }
        }, '销售分析数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取销售分析失败: {str(e)}')

@analytics_bp.route('/inventory', methods=['GET'])
@jwt_required()
@require_active_user
def get_inventory_analytics():
    """
    获取库存分析数据
    
    返回:
        inventory_overview: 库存概览
        turnover_analysis: 周转率分析
        stock_alerts: 库存预警
        category_distribution: 类别分布
    """
    try:
        # 库存概览统计
        total_items = InventoryItem.query.count()
        total_value = db.session.query(
            func.sum(InventoryItem.current_stock * InventoryItem.unit_price)
        ).filter(
            InventoryItem.unit_price.isnot(None)
        ).scalar() or 0
        
        low_stock_items = InventoryItem.query.filter(
            InventoryItem.current_stock <= InventoryItem.safety_stock
        ).count()
        
        out_of_stock_items = InventoryItem.query.filter(
            InventoryItem.current_stock == 0
        ).count()
        
        # 按状态分组的库存统计
        status_distribution = db.session.query(
            InventoryItem.status,
            func.count(InventoryItem.id).label('count')
        ).group_by(InventoryItem.status).all()
        
        status_data = {item.status: item.count for item in status_distribution}
        
        # 库存周转率分析（模拟计算）
        # 实际应该基于出库记录计算
        turnover_analysis = []
        categories = db.session.query(Material.category).distinct().all()
        
        for category in categories:
            category_name = category[0]
            
            # 计算该类别的平均库存
            avg_inventory = db.session.query(
                func.avg(InventoryItem.current_stock)
            ).join(
                Material, InventoryItem.material_id == Material.id
            ).filter(
                Material.category == category_name
            ).scalar() or 0
            
            # 模拟周转率计算
            turnover_rate = round(12 / max(avg_inventory, 1), 2)  # 简化计算
            
            turnover_analysis.append({
                'category': category_name,
                'avg_inventory': round(avg_inventory, 2),
                'turnover_rate': turnover_rate,
                'turnover_days': round(365 / max(turnover_rate, 0.1), 0)
            })
        
        # 库存预警清单
        stock_alerts = db.session.query(
            Material.part_number,
            Material.part_name,
            Material.category,
            InventoryItem.current_stock,
            InventoryItem.safety_stock,
            InventoryItem.location,
            InventoryItem.status
        ).join(
            Material, InventoryItem.material_id == Material.id
        ).filter(
            or_(
                InventoryItem.current_stock <= InventoryItem.safety_stock,
                InventoryItem.status.in_(['warning', 'shortage', 'expired'])
            )
        ).order_by(
            InventoryItem.current_stock.asc()
        ).limit(20).all()
        
        alerts_data = [
            {
                'part_number': item.part_number,
                'part_name': item.part_name,
                'category': item.category,
                'current_stock': item.current_stock,
                'safety_stock': item.safety_stock,
                'location': item.location,
                'status': item.status,
                'alert_level': 'critical' if item.current_stock == 0 else 'warning'
            }
            for item in stock_alerts
        ]
        
        # 类别分布分析
        category_distribution = db.session.query(
            Material.category,
            func.count(InventoryItem.id).label('item_count'),
            func.sum(InventoryItem.current_stock).label('total_stock'),
            func.sum(InventoryItem.current_stock * InventoryItem.unit_price).label('total_value')
        ).join(
            Material, InventoryItem.material_id == Material.id
        ).filter(
            InventoryItem.unit_price.isnot(None)
        ).group_by(
            Material.category
        ).order_by(
            func.sum(InventoryItem.current_stock * InventoryItem.unit_price).desc()
        ).all()
        
        category_data = [
            {
                'category': item.category,
                'item_count': item.item_count,
                'total_stock': item.total_stock,
                'total_value': float(item.total_value or 0),
                'avg_value_per_item': float(item.total_value or 0) / max(item.item_count, 1)
            }
            for item in category_distribution
        ]
        
        return success_response({
            'inventory_overview': {
                'total_items': total_items,
                'total_value': float(total_value),
                'low_stock_items': low_stock_items,
                'out_of_stock_items': out_of_stock_items,
                'status_distribution': status_data
            },
            'turnover_analysis': turnover_analysis,
            'stock_alerts': alerts_data,
            'category_distribution': category_data,
            'recommendations': [
                {
                    'type': 'reorder',
                    'message': f'有 {low_stock_items} 个项目需要补货',
                    'priority': 'high' if low_stock_items > 10 else 'medium'
                },
                {
                    'type': 'optimization',
                    'message': '建议优化周转率较低的类别库存',
                    'priority': 'medium'
                }
            ]
        }, '库存分析数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取库存分析失败: {str(e)}')

@analytics_bp.route('/financial', methods=['GET'])
@jwt_required()
@require_active_user
def get_financial_analytics():
    """
    获取财务分析数据
    
    返回:
        revenue_analysis: 收入分析
        cost_analysis: 成本分析
        profit_analysis: 利润分析
        payment_analysis: 付款分析
    """
    try:
        # 收入分析
        current_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        
        current_month_revenue = db.session.query(
            func.sum(Order.total_amount)
        ).filter(
            and_(
                Order.created_at >= current_month,
                Order.status.in_(['completed', 'shipping'])
            )
        ).scalar() or 0
        
        last_month_revenue = db.session.query(
            func.sum(Order.total_amount)
        ).filter(
            and_(
                Order.created_at >= last_month,
                Order.created_at < current_month,
                Order.status.in_(['completed', 'shipping'])
            )
        ).scalar() or 0
        
        revenue_growth = ((current_month_revenue - last_month_revenue) / last_month_revenue * 100) if last_month_revenue > 0 else 0
        
        # 按月收入趋势（最近6个月）
        revenue_trend = []
        for i in range(6):
            month_start = (current_month - timedelta(days=30*i)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            
            month_revenue = db.session.query(
                func.sum(Order.total_amount)
            ).filter(
                and_(
                    Order.created_at >= month_start,
                    Order.created_at <= month_end,
                    Order.status.in_(['completed', 'shipping'])
                )
            ).scalar() or 0
            
            revenue_trend.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(month_revenue)
            })
        
        revenue_trend.reverse()
        
        # 成本分析（模拟数据）
        cost_analysis = {
            'material_cost': float(current_month_revenue * 0.60),  # 60%材料成本
            'labor_cost': float(current_month_revenue * 0.15),    # 15%人工成本
            'logistics_cost': float(current_month_revenue * 0.08), # 8%物流成本
            'overhead_cost': float(current_month_revenue * 0.12),  # 12%管理费用
            'total_cost': float(current_month_revenue * 0.95)      # 95%总成本
        }
        
        # 利润分析
        gross_profit = current_month_revenue - cost_analysis['total_cost']
        gross_margin = (gross_profit / current_month_revenue * 100) if current_month_revenue > 0 else 0
        
        profit_analysis = {
            'gross_profit': float(gross_profit),
            'gross_margin': round(gross_margin, 2),
            'operating_profit': float(gross_profit * 0.85),  # 扣除运营费用
            'net_profit': float(gross_profit * 0.75),        # 扣除税费等
            'profit_growth': 15.3  # 模拟利润增长率
        }
        
        # 应收应付分析
        pending_payments = db.session.query(
            func.sum(Order.total_amount)
        ).filter(
            Order.status.in_(['confirmed', 'processing', 'shipping'])
        ).scalar() or 0
        
        payment_analysis = {
            'accounts_receivable': float(pending_payments),
            'accounts_payable': float(pending_payments * 0.7),  # 模拟应付账款
            'cash_flow': float(current_month_revenue - cost_analysis['total_cost']),
            'payment_terms': {
                'avg_collection_days': 30,
                'avg_payment_days': 45
            }
        }
        
        return success_response({
            'revenue_analysis': {
                'current_month': float(current_month_revenue),
                'last_month': float(last_month_revenue),
                'growth_rate': round(revenue_growth, 2),
                'trend': revenue_trend
            },
            'cost_analysis': cost_analysis,
            'profit_analysis': profit_analysis,
            'payment_analysis': payment_analysis,
            'financial_ratios': {
                'gross_margin': round(gross_margin, 2),
                'operating_margin': round(profit_analysis['operating_profit'] / current_month_revenue * 100, 2) if current_month_revenue > 0 else 0,
                'net_margin': round(profit_analysis['net_profit'] / current_month_revenue * 100, 2) if current_month_revenue > 0 else 0,
                'current_ratio': 1.85,  # 模拟流动比率
                'debt_to_equity': 0.32  # 模拟资产负债率
            }
        }, '财务分析数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取财务分析失败: {str(e)}')

@analytics_bp.route('/forecasting', methods=['GET'])
@jwt_required()
@require_active_user
def get_forecasting_analytics():
    """
    获取预测分析数据
    
    返回:
        demand_forecast: 需求预测
        revenue_forecast: 收入预测
        inventory_forecast: 库存预测
        trend_analysis: 趋势分析
    """
    try:
        # 基于历史数据的简单预测模型
        # 实际生产环境中应使用更复杂的机器学习算法
        
        # 获取历史数据
        now = datetime.utcnow()
        historical_data = []
        
        for i in range(12):  # 获取最近12个月的数据
            month_start = (now - timedelta(days=30*i)).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)
            
            month_orders = Order.query.filter(
                and_(
                    Order.created_at >= month_start,
                    Order.created_at <= month_end
                )
            ).count()
            
            month_revenue = db.session.query(
                func.sum(Order.total_amount)
            ).filter(
                and_(
                    Order.created_at >= month_start,
                    Order.created_at <= month_end,
                    Order.status.in_(['completed', 'shipping'])
                )
            ).scalar() or 0
            
            historical_data.append({
                'month': month_start.strftime('%Y-%m'),
                'orders': month_orders,
                'revenue': float(month_revenue)
            })
        
        historical_data.reverse()
        
        # 简单的线性预测（实际应使用更复杂的算法）
        if len(historical_data) >= 3:
            recent_orders = [item['orders'] for item in historical_data[-3:]]
            recent_revenue = [item['revenue'] for item in historical_data[-3:]]
            
            avg_order_growth = (recent_orders[-1] - recent_orders[0]) / 3
            avg_revenue_growth = (recent_revenue[-1] - recent_revenue[0]) / 3
        else:
            avg_order_growth = 0
            avg_revenue_growth = 0
        
        # 生成未来6个月预测
        demand_forecast = []
        revenue_forecast = []
        
        for i in range(6):
            future_month = (now + timedelta(days=30*i)).replace(day=1)
            
            predicted_orders = max(0, historical_data[-1]['orders'] + avg_order_growth * (i + 1))
            predicted_revenue = max(0, historical_data[-1]['revenue'] + avg_revenue_growth * (i + 1))
            
            demand_forecast.append({
                'month': future_month.strftime('%Y-%m'),
                'predicted_orders': int(predicted_orders),
                'confidence': max(50, 90 - i * 10)  # 置信度随时间递减
            })
            
            revenue_forecast.append({
                'month': future_month.strftime('%Y-%m'),
                'predicted_revenue': predicted_revenue,
                'confidence': max(50, 90 - i * 10)
            })
        
        # 热门产品需求预测
        from models import OrderItem
        top_materials = db.session.query(
            Material.part_number,
            Material.part_name,
            Material.category,
            func.sum(OrderItem.quantity).label('total_demand')
        ).join(
            OrderItem, Material.id == OrderItem.material_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            Order.created_at >= now - timedelta(days=90)
        ).group_by(
            Material.id
        ).order_by(
            func.sum(OrderItem.quantity).desc()
        ).limit(10).all()
        
        product_forecasts = []
        for material in top_materials:
            # 简单预测：基于过去3个月的需求趋势
            predicted_demand = material.total_demand * 1.1  # 假设10%增长
            
            product_forecasts.append({
                'part_number': material.part_number,
                'part_name': material.part_name,
                'category': material.category,
                'current_demand': material.total_demand,
                'predicted_demand': int(predicted_demand),
                'growth_rate': 10.0
            })
        
        # 趋势分析
        trend_analysis = {
            'demand_trend': 'increasing' if avg_order_growth > 0 else 'decreasing',
            'revenue_trend': 'increasing' if avg_revenue_growth > 0 else 'decreasing',
            'seasonal_factors': [
                {'month': 'Q1', 'factor': 0.9, 'description': '第一季度需求较低'},
                {'month': 'Q2', 'factor': 1.1, 'description': '第二季度需求上升'},
                {'month': 'Q3', 'factor': 1.2, 'description': '第三季度需求高峰'},
                {'month': 'Q4', 'factor': 1.0, 'description': '第四季度需求正常'}
            ],
            'risk_factors': [
                {
                    'factor': '市场竞争加剧',
                    'impact': 'medium',
                    'probability': 0.6
                },
                {
                    'factor': '原材料价格上涨',
                    'impact': 'high',
                    'probability': 0.4
                },
                {
                    'factor': '疫情影响航空业',
                    'impact': 'high',
                    'probability': 0.3
                }
            ]
        }
        
        return success_response({
            'historical_data': historical_data,
            'demand_forecast': demand_forecast,
            'revenue_forecast': revenue_forecast,
            'product_forecasts': product_forecasts,
            'trend_analysis': trend_analysis,
            'model_info': {
                'algorithm': 'Linear Regression',
                'training_period': '12个月历史数据',
                'update_frequency': '每月更新',
                'accuracy': '75-85%'
            }
        }, '预测分析数据获取成功')
        
    except Exception as e:
        return error_response(500, f'获取预测分析失败: {str(e)}')

@analytics_bp.route('/custom-report', methods=['POST'])
@jwt_required()
@require_active_user
@validate_json(['report_type', 'parameters'])
def generate_custom_report(report_type=None):
    """
    生成自定义报表
    
    请求体:
        report_type: 报表类型 (sales, inventory, financial, operational)
        parameters: 报表参数
        format: 输出格式 (json, csv, pdf)
        
    返回:
        report_data: 报表数据
        export_url: 导出链接（如果需要）
    """
    try:
        data = request.get_json()
        report_type = data.get('report_type')
        parameters = data.get('parameters', {})
        output_format = data.get('format', 'json')
        
        # 验证报表类型
        valid_types = ['sales', 'inventory', 'financial', 'operational']
        if report_type not in valid_types:
            return error_response(400, f'无效的报表类型，支持的类型: {", ".join(valid_types)}')
        
        # 解析参数
        start_date = parameters.get('start_date')
        end_date = parameters.get('end_date')
        filters = parameters.get('filters', {})
        
        if start_date:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # 根据报表类型生成数据
        if report_type == 'sales':
            # 销售报表
            query = db.session.query(
                Order.order_number,
                Order.created_at,
                Order.total_amount,
                Order.status,
                User.company_name.label('customer_name')
            ).join(
                User, Order.buyer_id == User.id
            )
            
            if start_date:
                query = query.filter(Order.created_at >= start_date)
            if end_date:
                query = query.filter(Order.created_at <= end_date)
            if filters.get('status'):
                query = query.filter(Order.status == filters['status'])
            
            results = query.order_by(Order.created_at.desc()).all()
            
            report_data = [
                {
                    'order_number': item.order_number,
                    'date': item.created_at.strftime('%Y-%m-%d'),
                    'customer': item.customer_name,
                    'amount': float(item.total_amount or 0),
                    'status': item.status
                }
                for item in results
            ]
            
        elif report_type == 'inventory':
            # 库存报表
            query = db.session.query(
                Material.part_number,
                Material.part_name,
                Material.category,
                InventoryItem.current_stock,
                InventoryItem.safety_stock,
                InventoryItem.unit_price,
                InventoryItem.location
            ).join(
                Material, InventoryItem.material_id == Material.id
            )
            
            if filters.get('category'):
                query = query.filter(Material.category == filters['category'])
            if filters.get('location'):
                query = query.filter(InventoryItem.location.contains(filters['location']))
            
            results = query.all()
            
            report_data = [
                {
                    'part_number': item.part_number,
                    'part_name': item.part_name,
                    'category': item.category,
                    'current_stock': item.current_stock,
                    'safety_stock': item.safety_stock,
                    'unit_price': float(item.unit_price or 0),
                    'location': item.location,
                    'stock_value': item.current_stock * float(item.unit_price or 0)
                }
                for item in results
            ]
            
        else:
            # 其他报表类型的实现
            report_data = []
        
        # 根据输出格式处理
        if output_format == 'csv':
            # 生成CSV文件
            output = io.StringIO()
            if report_data:
                writer = csv.DictWriter(output, fieldnames=report_data[0].keys())
                writer.writeheader()
                writer.writerows(report_data)
            
            csv_content = output.getvalue()
            output.close()
            
            return success_response({
                'report_type': report_type,
                'format': 'csv',
                'data_count': len(report_data),
                'csv_content': csv_content,
                'generated_at': datetime.utcnow().isoformat()
            }, '自定义报表生成成功')
        
        else:
            # JSON格式
            return success_response({
                'report_type': report_type,
                'format': 'json',
                'data': report_data,
                'data_count': len(report_data),
                'parameters': parameters,
                'generated_at': datetime.utcnow().isoformat()
            }, '自定义报表生成成功')
        
    except Exception as e:
        return error_response(500, f'生成自定义报表失败: {str(e)}')

@analytics_bp.route('/export', methods=['POST'])
@jwt_required()
@require_active_user
@validate_json(['data_type'])
def export_analytics_data():
    """
    导出分析数据
    
    请求体:
        data_type: 数据类型 (overview, sales, inventory, financial)
        format: 导出格式 (csv, excel, pdf)
        parameters: 导出参数
        
    返回:
        export_result: 导出结果信息
    """
    try:
        data = request.get_json()
        data_type = data.get('data_type')
        export_format = data.get('format', 'csv')
        parameters = data.get('parameters', {})
        
        # 这里实现数据导出逻辑
        # 简化实现，返回成功消息
        
        return success_response({
            'data_type': data_type,
            'format': export_format,
            'status': 'processing',
            'estimated_completion': (datetime.utcnow() + timedelta(minutes=5)).isoformat(),
            'download_url': f'/api/v1/analytics/download/{data_type}_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}.{export_format}'
        }, '数据导出任务已启动，完成后将通过邮件发送下载链接')
        
    except Exception as e:
        return error_response(500, f'导出分析数据失败: {str(e)}')