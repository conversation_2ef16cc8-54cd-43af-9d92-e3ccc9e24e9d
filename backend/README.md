# 航材共享保障平台 - 后端API文档

## 项目概述

航材共享保障平台后端是基于Python Flask框架开发的RESTful API服务，为前端Vue应用提供完整的数据服务和业务逻辑支持。

### 技术栈
- **框架**: Flask 3.0 + SQLAlchemy 2.0
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **认证**: JWT Token
- **API**: RESTful风格
- **工作流**: SpiffWorkflow

### 核心特性
- 🔐 JWT令牌认证和授权
- 📊 完整的业务数据模型
- 🚨 AOG紧急响应系统
- 📈 多维度数据分析
- 🛡️ 质量管理和认证
- 🔧 维修工单管理
- 📋 工作流审批引擎

## 项目结构

```
backend/
├── app.py                 # Flask应用主入口
├── config.py             # 配置文件
├── models.py             # 数据库模型
├── requirements.txt      # Python依赖
├── routes/              # API路由模块
│   ├── auth.py          # 用户认证API
│   ├── aog.py           # AOG紧急响应API
│   ├── analytics.py     # 数据分析API
│   ├── quality.py       # 质量管理API
│   ├── maintenance.py   # 维修管理API
│   ├── materials.py     # 航材管理API
│   ├── orders.py        # 订单管理API
│   ├── inventory.py     # 库存管理API
│   ├── logistics.py     # 物流管理API
│   ├── notifications.py # 通知管理API
│   ├── demands.py       # 需求管理API
│   └── workflow.py      # 工作流管理API
└── utils/               # 工具函数
    ├── validators.py    # 验证器函数
    ├── decorators.py    # 装饰器
    ├── response.py      # 响应处理
    └── init_data.py     # 数据初始化
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# 开发环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 数据库配置
DATABASE_URL=sqlite:///aviation_platform.db

# JWT配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 邮件配置（可选）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 3. 初始化数据库

```bash
# 运行应用（会自动创建数据库）
python app.py
```

应用启动后会自动：
- 创建所有数据库表
- 初始化基础测试数据
- 创建默认用户账号

### 4. 默认账号

| 用户类型 | 用户名 | 密码 | 说明 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 系统管理员 |
| 航空公司 | airline_user | test123 | 航空公司采购员 |
| 供应商 | supplier_user | test123 | 航材供应商 |
| 维修企业 | maintenance_user | test123 | 维修工程师 |

## API接口文档

### 统一响应格式

所有API接口遵循统一的响应格式：

```json
{
  "error": 0,           // 错误码，0表示成功
  "message": "success", // 响应消息
  "body": {},          // 响应数据
  "success": true      // 操作是否成功
}
```

### 认证相关 `/api/v1/auth`

#### 用户登录
```
POST /api/v1/auth/login
```

请求体:
```json
{
  "username": "admin",
  "password": "admin123",
  "user_type": "admin"
}
```

响应:
```json
{
  "error": 0,
  "message": "登录成功",
  "body": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 86400,
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "user_type": "admin"
    }
  },
  "success": true
}
```

#### 用户注册
```
POST /api/v1/auth/register
```

#### 刷新令牌
```
POST /api/v1/auth/refresh
```

#### 获取用户信息
```
GET /api/v1/auth/profile
```

### AOG紧急响应 `/api/v1/aog`

#### 获取AOG案例列表
```
GET /api/v1/aog/cases
```

查询参数:
- `page`: 页码
- `size`: 每页数量
- `status`: 状态筛选
- `priority`: 优先级筛选
- `search`: 搜索关键词

#### 创建AOG案例
```
POST /api/v1/aog/cases
```

请求体:
```json
{
  "aircraft_tail": "B-1234",
  "aircraft_type": "A320-200",
  "location": "北京首都机场",
  "priority": "critical",
  "fault_title": "左发动机振动异常",
  "fault_description": "发动机在巡航阶段出现异常振动",
  "contact_name": "紧急联系人",
  "contact_phone": "13800138000"
}
```

#### 响应AOG案例
```
POST /api/v1/aog/cases/{case_id}/respond
```

#### 快速零件匹配
```
POST /api/v1/aog/cases/{case_id}/quick-match
```

#### 启动紧急采购
```
POST /api/v1/aog/cases/{case_id}/emergency-purchase
```

### 数据分析 `/api/v1/analytics`

#### 业务概览
```
GET /api/v1/analytics/overview
```

#### 销售分析
```
GET /api/v1/analytics/sales
```

#### 库存分析
```
GET /api/v1/analytics/inventory
```

#### 财务分析
```
GET /api/v1/analytics/financial
```

#### 预测分析
```
GET /api/v1/analytics/forecasting
```

#### 自定义报表
```
POST /api/v1/analytics/custom-report
```

### 质量管理 `/api/v1/quality`

#### 获取证书列表
```
GET /api/v1/quality/certificates
```

#### 创建证书
```
POST /api/v1/quality/certificates
```

#### 验证证书
```
POST /api/v1/quality/certificates/{cert_id}/verify
```

#### 合规检查
```
POST /api/v1/quality/compliance-check
```

### 维修管理 `/api/v1/maintenance`

#### 获取工单列表
```
GET /api/v1/maintenance/work-orders
```

#### 创建工单
```
POST /api/v1/maintenance/work-orders
```

#### 分配技师
```
POST /api/v1/maintenance/work-orders/{work_order_id}/assign
```

#### 记录工时
```
POST /api/v1/maintenance/work-orders/{work_order_id}/labor
```

## 数据库模型

### 核心模型关系

```
User (用户)
├── orders_as_buyer (作为买方的订单)
├── orders_as_supplier (作为供应商的订单)
├── demands (发布的需求)
└── notifications (通知)

Material (航材)
├── inventory_items (库存项目)
├── order_items (订单项目)
└── certificates (证书)

Order (订单)
├── order_items (订单项目)
└── logistics (物流信息)

AOGCase (AOG案例)
├── customer (客户)
└── assigned_team (指派团队)

WorkOrder (维修工单)
├── assigned_technician (指派技师)
└── labor_records (工时记录)
```

### 主要数据表

#### users - 用户表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| username | String(50) | 用户名 |
| email | String(100) | 邮箱 |
| user_type | Enum | 用户类型(airline, supplier, maintenance, admin) |
| company_name | String(100) | 公司名称 |
| status | Enum | 状态(active, inactive, suspended) |

#### materials - 航材表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| part_number | String(50) | 零件号 |
| part_name | String(200) | 零件名称 |
| category | String(50) | 类别 |
| manufacturer | String(100) | 制造商 |
| aircraft_type | String(50) | 适用机型 |

#### orders - 订单表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| order_number | String(50) | 订单号 |
| buyer_id | Integer | 买方ID |
| supplier_id | Integer | 供应商ID |
| status | Enum | 状态 |
| priority | Enum | 优先级 |
| total_amount | Decimal | 总金额 |

#### aog_cases - AOG案例表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键 |
| case_number | String(50) | 案例编号 |
| aircraft_tail | String(20) | 飞机尾号 |
| priority | Enum | 优先级(critical, high, medium) |
| status | Enum | 状态 |
| response_time | DateTime | 响应时间 |

## 权限系统

### 用户角色

| 角色 | 权限范围 |
|------|----------|
| admin | 全系统管理权限 |
| airline | 发布需求、创建订单、查看自己的数据 |
| supplier | 响应需求、管理库存、处理订单 |
| maintenance | 维修工单管理、AOG响应 |

### 权限控制

使用装饰器进行权限控制：

```python
from utils.decorators import require_roles, require_admin

@require_roles(['admin', 'supplier'])
def supplier_only_api():
    pass

@require_admin
def admin_only_api():
    pass
```

## 错误处理

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

### 错误响应示例

```json
{
  "error": 400,
  "message": "缺少必需字段: username, password",
  "body": {
    "missing_fields": ["username", "password"]
  },
  "success": false
}
```

## 部署说明

### 开发环境

```bash
# 启动开发服务器
python app.py
```

### 生产环境

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 性能优化

### 数据库优化
- 为查询字段添加索引
- 使用连接查询避免N+1问题
- 实现分页查询

### 缓存策略
- Redis缓存热点数据
- 数据库查询结果缓存
- API响应缓存

### 监控指标
- API响应时间
- 数据库查询性能
- 内存和CPU使用率
- 错误率统计

## 开发规范

### 代码风格
- 遵循PEP 8规范
- 使用类型提示
- 添加详细的中文注释
- 函数文档字符串

### API设计
- RESTful风格
- 统一响应格式
- 完整的错误处理
- 输入验证和数据清理

### 测试策略
- 单元测试覆盖核心逻辑
- API接口测试
- 集成测试验证业务流程

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务状态

2. **JWT令牌验证失败**
   - 检查SECRET_KEY配置
   - 确认令牌格式正确

3. **权限访问被拒绝**
   - 检查用户角色配置
   - 确认API权限设置

### 日志查看

```bash
# 查看应用日志
tail -f logs/aviation_platform.log

# 查看错误日志
grep ERROR logs/aviation_platform.log
```

## 扩展开发

### 添加新的API模块

1. 在`routes/`目录创建新模块
2. 定义路由和处理函数
3. 在`app.py`中注册蓝图
4. 添加权限和验证装饰器

### 扩展数据模型

1. 在`models.py`中定义新模型
2. 创建数据库迁移
3. 更新初始化脚本
4. 添加相应的API接口

## 联系方式

如有问题或建议，请联系开发团队。