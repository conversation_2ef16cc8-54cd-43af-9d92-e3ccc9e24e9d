"""Initial migration - complete database schema

Revision ID: 1df306e08002
Revises: 
Create Date: 2025-07-24 14:04:09.128793

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1df306e08002'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('audit_logs', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='日志ID',
               existing_nullable=False,
               autoincrement=True)
        batch_op.alter_column('log_id',
               existing_type=sa.VARCHAR(length=36),
               comment='日志UUID',
               existing_nullable=False)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment='用户ID',
               existing_nullable=True)
        batch_op.alter_column('username',
               existing_type=sa.VARCHAR(length=100),
               comment='用户名',
               existing_nullable=True)
        batch_op.alter_column('user_role',
               existing_type=sa.VARCHAR(length=50),
               comment='用户角色',
               existing_nullable=True)
        batch_op.alter_column('operation_type',
               existing_type=sa.VARCHAR(length=50),
               comment='操作类型',
               existing_nullable=False)
        batch_op.alter_column('operation_desc',
               existing_type=sa.VARCHAR(length=200),
               comment='操作描述',
               existing_nullable=True)
        batch_op.alter_column('resource_type',
               existing_type=sa.VARCHAR(length=50),
               comment='资源类型',
               existing_nullable=True)
        batch_op.alter_column('resource_id',
               existing_type=sa.VARCHAR(length=100),
               comment='资源ID',
               existing_nullable=True)
        batch_op.alter_column('request_method',
               existing_type=sa.VARCHAR(length=10),
               comment='请求方法',
               existing_nullable=True)
        batch_op.alter_column('request_path',
               existing_type=sa.VARCHAR(length=500),
               comment='请求路径',
               existing_nullable=True)
        batch_op.alter_column('request_params',
               existing_type=sa.TEXT(),
               comment='请求参数',
               existing_nullable=True)
        batch_op.alter_column('ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment='IP地址',
               existing_nullable=True)
        batch_op.alter_column('user_agent',
               existing_type=sa.TEXT(),
               comment='用户代理',
               existing_nullable=True)
        batch_op.alter_column('session_id',
               existing_type=sa.VARCHAR(length=100),
               comment='会话ID',
               existing_nullable=True)
        batch_op.alter_column('before_data',
               existing_type=sa.TEXT(),
               comment='操作前数据',
               existing_nullable=True)
        batch_op.alter_column('after_data',
               existing_type=sa.TEXT(),
               comment='操作后数据',
               existing_nullable=True)
        batch_op.alter_column('risk_level',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='risk_levels'),
               comment='风险级别',
               existing_nullable=True,
               existing_server_default=sa.text("'LOW'::character varying"))
        batch_op.alter_column('is_success',
               existing_type=sa.BOOLEAN(),
               comment='是否成功',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('error_message',
               existing_type=sa.TEXT(),
               comment='错误消息',
               existing_nullable=True)
        batch_op.alter_column('execution_time_ms',
               existing_type=sa.INTEGER(),
               comment='执行时间(毫秒)',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.drop_index('idx_audit_created_at')
        batch_op.drop_index('idx_audit_operation')
        batch_op.drop_index('idx_audit_resource')
        batch_op.drop_index('idx_audit_risk_level')
        batch_op.drop_index('idx_audit_user_id')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('invitation_code_uses', schema=None) as batch_op:
        batch_op.add_column(sa.Column('invitation_id', sa.Integer(), nullable=False, comment='邀请码ID'))
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='使用记录ID',
               existing_nullable=False,
               autoincrement=True)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment='使用用户ID',
               existing_nullable=True)
        batch_op.alter_column('ip_address',
               existing_type=postgresql.INET(),
               type_=sa.String(length=45),
               comment='使用IP地址',
               existing_nullable=True)
        batch_op.alter_column('user_agent',
               existing_type=sa.TEXT(),
               comment='用户代理',
               existing_nullable=True)
        batch_op.alter_column('used_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='使用时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.drop_constraint('invitation_code_uses_invitation_code_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'invitation_codes', ['invitation_id'], ['id'], ondelete='CASCADE')
        batch_op.drop_column('invitation_code_id')

    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'))
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='邀请码ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('invitation_codes_id_seq'::regclass)"))
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=20),
               comment='邀请码',
               existing_nullable=False)
        batch_op.alter_column('code_type',
               existing_type=sa.VARCHAR(length=10),
               type_=sa.Enum('staff', 'admin', name='invitation_code_types'),
               nullable=True,
               comment='邀请码类型')
        batch_op.alter_column('allowed_roles',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               comment='允许的角色列表(JSON格式)',
               existing_nullable=True)
        batch_op.alter_column('max_uses',
               existing_type=sa.INTEGER(),
               comment='最大使用次数',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('used_count',
               existing_type=sa.INTEGER(),
               comment='已使用次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               comment='过期时间')
        batch_op.alter_column('ip_whitelist',
               existing_type=postgresql.ARRAY(postgresql.INET()),
               type_=sa.Text(),
               comment='IP白名单(JSON格式)',
               existing_nullable=True)
        batch_op.alter_column('created_by',
               existing_type=sa.INTEGER(),
               comment='创建人ID',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.drop_column('updated_at')
        batch_op.drop_column('status')

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='关联ID',
               existing_nullable=False,
               autoincrement=True)
        batch_op.alter_column('role_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               comment='角色ID')
        batch_op.alter_column('permission_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               comment='权限ID')
        batch_op.alter_column('granted_by',
               existing_type=sa.INTEGER(),
               comment='授权人ID',
               existing_nullable=True)
        batch_op.alter_column('granted_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='授权时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    with op.batch_alter_table('system_permissions', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='权限ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('system_permissions_id_seq'::regclass)"))
        batch_op.alter_column('permission_code',
               existing_type=sa.VARCHAR(length=100),
               comment='权限代码',
               existing_nullable=False)
        batch_op.alter_column('permission_name',
               existing_type=sa.VARCHAR(length=100),
               comment='权限名称',
               existing_nullable=False)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment='权限描述',
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=sa.VARCHAR(length=50),
               comment='权限分类',
               existing_nullable=True)
        batch_op.alter_column('module',
               existing_type=sa.VARCHAR(length=50),
               comment='所属模块',
               existing_nullable=True)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    with op.batch_alter_table('system_roles', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment='角色ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('system_roles_id_seq'::regclass)"))
        batch_op.alter_column('role_code',
               existing_type=sa.VARCHAR(length=50),
               comment='角色代码',
               existing_nullable=False)
        batch_op.alter_column('role_name',
               existing_type=sa.VARCHAR(length=100),
               comment='角色名称',
               existing_nullable=False)
        batch_op.alter_column('display_name',
               existing_type=sa.VARCHAR(length=100),
               comment='显示名称',
               existing_nullable=False)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment='角色描述',
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('internal', 'external', name='role_categories'),
               comment='角色分类',
               existing_nullable=False)
        batch_op.alter_column('business_type',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('buy_only', 'sell_only', 'buy_and_sell', 'service', name='business_types'),
               comment='业务类型',
               existing_nullable=False)
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('sort_order',
               existing_type=sa.INTEGER(),
               comment='排序',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('icon_name',
               existing_type=sa.VARCHAR(length=50),
               comment='图标名称',
               existing_nullable=True)
        batch_op.alter_column('theme_color',
               existing_type=sa.VARCHAR(length=20),
               comment='主题色',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('role_id',
               existing_type=sa.INTEGER(),
               comment='角色ID，关联system_roles表',
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('role_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='角色ID，关联system_roles表',
               existing_nullable=True)

    with op.batch_alter_table('system_roles', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('theme_color',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='主题色',
               existing_nullable=True)
        batch_op.alter_column('icon_name',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='图标名称',
               existing_nullable=True)
        batch_op.alter_column('sort_order',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='排序',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('business_type',
               existing_type=sa.Enum('buy_only', 'sell_only', 'buy_and_sell', 'service', name='business_types'),
               type_=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='业务类型',
               existing_nullable=False)
        batch_op.alter_column('category',
               existing_type=sa.Enum('internal', 'external', name='role_categories'),
               type_=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='角色分类',
               existing_nullable=False)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='角色描述',
               existing_nullable=True)
        batch_op.alter_column('display_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='显示名称',
               existing_nullable=False)
        batch_op.alter_column('role_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='角色名称',
               existing_nullable=False)
        batch_op.alter_column('role_code',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='角色代码',
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='角色ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('system_roles_id_seq'::regclass)"))

    with op.batch_alter_table('system_permissions', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('is_active',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('module',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='所属模块',
               existing_nullable=True)
        batch_op.alter_column('category',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='权限分类',
               existing_nullable=True)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='权限描述',
               existing_nullable=True)
        batch_op.alter_column('permission_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='权限名称',
               existing_nullable=False)
        batch_op.alter_column('permission_code',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='权限代码',
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='权限ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('system_permissions_id_seq'::regclass)"))

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.alter_column('granted_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='授权时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('granted_by',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='授权人ID',
               existing_nullable=True)
        batch_op.alter_column('permission_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               comment=None,
               existing_comment='权限ID')
        batch_op.alter_column('role_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               comment=None,
               existing_comment='角色ID')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='关联ID',
               existing_nullable=False,
               autoincrement=True)

    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', sa.VARCHAR(length=10), server_default=sa.text("'active'::character varying"), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('created_by',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='创建人ID',
               existing_nullable=True)
        batch_op.alter_column('ip_whitelist',
               existing_type=sa.Text(),
               type_=postgresql.ARRAY(postgresql.INET()),
               comment=None,
               existing_comment='IP白名单(JSON格式)',
               existing_nullable=True)
        batch_op.alter_column('expires_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               comment=None,
               existing_comment='过期时间')
        batch_op.alter_column('used_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='已使用次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('max_uses',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='最大使用次数',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('allowed_roles',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               comment=None,
               existing_comment='允许的角色列表(JSON格式)',
               existing_nullable=True)
        batch_op.alter_column('code_type',
               existing_type=sa.Enum('staff', 'admin', name='invitation_code_types'),
               type_=sa.VARCHAR(length=10),
               nullable=False,
               comment=None,
               existing_comment='邀请码类型')
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='邀请码',
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='邀请码ID',
               existing_nullable=False,
               autoincrement=True,
               existing_server_default=sa.text("nextval('invitation_codes_id_seq'::regclass)"))
        batch_op.drop_column('is_active')

    with op.batch_alter_table('invitation_code_uses', schema=None) as batch_op:
        batch_op.add_column(sa.Column('invitation_code_id', sa.INTEGER(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('invitation_code_uses_invitation_code_id_fkey', 'invitation_codes', ['invitation_code_id'], ['id'], ondelete='CASCADE')
        batch_op.alter_column('used_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='使用时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('user_agent',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='用户代理',
               existing_nullable=True)
        batch_op.alter_column('ip_address',
               existing_type=sa.String(length=45),
               type_=postgresql.INET(),
               comment=None,
               existing_comment='使用IP地址',
               existing_nullable=True)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='使用用户ID',
               existing_nullable=True)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='使用记录ID',
               existing_nullable=False,
               autoincrement=True)
        batch_op.drop_column('invitation_id')

    with op.batch_alter_table('audit_logs', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_index('idx_audit_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_audit_risk_level', ['risk_level'], unique=False)
        batch_op.create_index('idx_audit_resource', ['resource_type', 'resource_id'], unique=False)
        batch_op.create_index('idx_audit_operation', ['operation_type'], unique=False)
        batch_op.create_index('idx_audit_created_at', ['created_at'], unique=False)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('execution_time_ms',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='执行时间(毫秒)',
               existing_nullable=True)
        batch_op.alter_column('error_message',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='错误消息',
               existing_nullable=True)
        batch_op.alter_column('is_success',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否成功',
               existing_nullable=True,
               existing_server_default=sa.text('true'))
        batch_op.alter_column('risk_level',
               existing_type=sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='risk_levels'),
               type_=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='风险级别',
               existing_nullable=True,
               existing_server_default=sa.text("'LOW'::character varying"))
        batch_op.alter_column('after_data',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='操作后数据',
               existing_nullable=True)
        batch_op.alter_column('before_data',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='操作前数据',
               existing_nullable=True)
        batch_op.alter_column('session_id',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='会话ID',
               existing_nullable=True)
        batch_op.alter_column('user_agent',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='用户代理',
               existing_nullable=True)
        batch_op.alter_column('ip_address',
               existing_type=sa.VARCHAR(length=45),
               comment=None,
               existing_comment='IP地址',
               existing_nullable=True)
        batch_op.alter_column('request_params',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='请求参数',
               existing_nullable=True)
        batch_op.alter_column('request_path',
               existing_type=sa.VARCHAR(length=500),
               comment=None,
               existing_comment='请求路径',
               existing_nullable=True)
        batch_op.alter_column('request_method',
               existing_type=sa.VARCHAR(length=10),
               comment=None,
               existing_comment='请求方法',
               existing_nullable=True)
        batch_op.alter_column('resource_id',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='资源ID',
               existing_nullable=True)
        batch_op.alter_column('resource_type',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='资源类型',
               existing_nullable=True)
        batch_op.alter_column('operation_desc',
               existing_type=sa.VARCHAR(length=200),
               comment=None,
               existing_comment='操作描述',
               existing_nullable=True)
        batch_op.alter_column('operation_type',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='操作类型',
               existing_nullable=False)
        batch_op.alter_column('user_role',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='用户角色',
               existing_nullable=True)
        batch_op.alter_column('username',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='用户名',
               existing_nullable=True)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=True)
        batch_op.alter_column('log_id',
               existing_type=sa.VARCHAR(length=36),
               comment=None,
               existing_comment='日志UUID',
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='日志ID',
               existing_nullable=False,
               autoincrement=True)

    # ### end Alembic commands ###
