"""Initialize seed data - roles, permissions, and configurations

Revision ID: f03084e4266f
Revises: 1df306e08002  
Create Date: 2025-07-24 14:10:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime, timedelta

# revision identifiers, used by Alembic.
revision = 'f03084e4266f'
down_revision = '1df306e08002'
branch_labels = None
depends_on = None

def upgrade():
    # 初始化系统角色数据
    conn = op.get_bind()
    
    # 1. 初始化系统角色
    roles_data = [
        {
            'role_code': 'supplier',
            'role_name': '供应商',
            'display_name': '供应商',
            'description': '航材制造商，负责生产和销售航材产品，不进行采购活动',
            'category': 'external',
            'business_type': 'sell_only',
            'sort_order': 1,
            'icon_name': 'factory',
            'theme_color': '#E74C3C'
        },
        {
            'role_code': 'distributor', 
            'role_name': '分销商',
            'display_name': '分销商',
            'description': '航材贸易商，连接供应商和客户，进行双向贸易活动',
            'category': 'external',
            'business_type': 'buy_and_sell',
            'sort_order': 2,
            'icon_name': 'store',
            'theme_color': '#3498DB'
        },
        {
            'role_code': 'airline_buyer',
            'role_name': '航空公司',
            'display_name': '航空公司',
            'description': '航材最终使用方，主要进行航材采购和维修保障',
            'category': 'external', 
            'business_type': 'buy_only',
            'sort_order': 3,
            'icon_name': 'airplane',
            'theme_color': '#2ECC71'
        },
        {
            'role_code': 'platform_staff',
            'role_name': '平台员工',
            'display_name': '平台员工',
            'description': '平台内部业务人员，负责订单处理、客户服务等日常运营',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 4,
            'icon_name': 'user-tie',
            'theme_color': '#9B59B6'
        },
        {
            'role_code': 'maintenance_engineer',
            'role_name': '维修工程师',
            'display_name': '维修工程师',
            'description': '提供技术支持和维修服务的专业工程师',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 5,
            'icon_name': 'tools',
            'theme_color': '#F39C12'
        },
        {
            'role_code': 'logistics_specialist',
            'role_name': '物流专员',
            'display_name': '物流专员',  
            'description': '负责物流配送、跟踪和管理的专业人员',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 6,
            'icon_name': 'truck',
            'theme_color': '#16A085'
        },
        {
            'role_code': 'admin',
            'role_name': '系统管理员',
            'display_name': '系统管理员',
            'description': '拥有系统最高权限，负责系统配置、用户管理等管理工作',
            'category': 'internal',
            'business_type': 'service',
            'sort_order': 7,
            'icon_name': 'user-shield',
            'theme_color': '#E67E22'
        }
    ]
    
    # 插入角色数据
    for role in roles_data:
        conn.execute(
            sa.text("""
                INSERT INTO system_roles (role_code, role_name, display_name, description, 
                                        category, business_type, sort_order, icon_name, theme_color)
                VALUES (:role_code, :role_name, :display_name, :description,
                       :category, :business_type, :sort_order, :icon_name, :theme_color)
                ON CONFLICT (role_code) DO NOTHING
            """),
            role
        )
    
    # 2. 初始化系统权限数据
    permissions_data = [
        # 基础权限
        {'permission_code': 'view_own_data', 'permission_name': '查看自己的数据', 'description': '可以查看属于自己的数据和信息', 'category': 'basic', 'module': 'common'},
        {'permission_code': 'manage_inventory', 'permission_name': '管理库存', 'description': '管理自己的库存信息，包括添加、修改库存', 'category': 'basic', 'module': 'inventory'},
        {'permission_code': 'publish_shared_materials', 'permission_name': '发布共享件', 'description': '将库存标记为可共享，发布到平台供其他用户查看', 'category': 'basic', 'module': 'sharing'},
        {'permission_code': 'publish_demands', 'permission_name': '发布需求', 'description': '发布航材需求信息，寻求供应商', 'category': 'basic', 'module': 'demand'},
        {'permission_code': 'respond_to_inquiries', 'permission_name': '响应询价', 'description': '回复其他用户的询价请求', 'category': 'basic', 'module': 'inquiry'},
        {'permission_code': 'manage_orders', 'permission_name': '管理订单', 'description': '创建、修改、取消自己的订单', 'category': 'basic', 'module': 'order'},
        {'permission_code': 'aog_request', 'permission_name': 'AOG紧急请求', 'description': '发起AOG（Aircraft on Ground）紧急航材请求', 'category': 'basic', 'module': 'aog'},
        
        # 平台权限
        {'permission_code': 'process_all_orders', 'permission_name': '处理所有订单', 'description': '查看和处理平台上所有的订单', 'category': 'platform', 'module': 'order'},
        {'permission_code': 'cross_company_access', 'permission_name': '跨公司数据访问', 'description': '访问其他公司的数据（在权限范围内）', 'category': 'platform', 'module': 'data'},
        {'permission_code': 'audit_shared_materials', 'permission_name': '审核共享材料', 'description': '审核和管理平台上的共享材料', 'category': 'platform', 'module': 'sharing'},
        {'permission_code': 'customer_service', 'permission_name': '客户服务', 'description': '提供客户服务，处理客户问题和投诉', 'category': 'platform', 'module': 'service'},
        {'permission_code': 'data_analytics', 'permission_name': '数据分析', 'description': '查看和分析平台运营数据', 'category': 'platform', 'module': 'analytics'},
        
        # 维修权限
        {'permission_code': 'update_maintenance_status', 'permission_name': '更新维修状态', 'description': '更新航材和设备的维修状态', 'category': 'maintenance', 'module': 'maintenance'},
        {'permission_code': 'technical_support', 'permission_name': '技术支持', 'description': '提供技术支持和咨询服务', 'category': 'maintenance', 'module': 'support'},
        {'permission_code': 'view_maintenance_data', 'permission_name': '查看维修数据', 'description': '查看维修记录和相关技术数据', 'category': 'maintenance', 'module': 'maintenance'},
        {'permission_code': 'manage_work_orders', 'permission_name': '管理工单', 'description': '创建、分配和管理维修工单', 'category': 'maintenance', 'module': 'workorder'},
        
        # 物流权限
        {'permission_code': 'track_shipments', 'permission_name': '跟踪货运', 'description': '跟踪和查询货物运输状态', 'category': 'logistics', 'module': 'tracking'},
        {'permission_code': 'update_logistics_info', 'permission_name': '更新物流信息', 'description': '更新货物的物流状态和位置信息', 'category': 'logistics', 'module': 'logistics'},
        {'permission_code': 'manage_delivery', 'permission_name': '管理配送', 'description': '管理货物的配送安排和调度', 'category': 'logistics', 'module': 'delivery'},
        {'permission_code': 'view_logistics_data', 'permission_name': '查看物流数据', 'description': '查看物流统计和分析数据', 'category': 'logistics', 'module': 'logistics'},
        
        # 管理权限
        {'permission_code': 'user_management', 'permission_name': '用户管理', 'description': '管理平台用户，包括注册审核、权限设置等', 'category': 'admin', 'module': 'user'},
        {'permission_code': 'role_management', 'permission_name': '角色管理', 'description': '管理系统角色和权限配置', 'category': 'admin', 'module': 'role'},
        {'permission_code': 'system_config', 'permission_name': '系统配置', 'description': '修改系统配置参数', 'category': 'admin', 'module': 'system'},
        {'permission_code': 'all_permissions', 'permission_name': '所有权限', 'description': '拥有系统的所有权限（超级管理员）', 'category': 'admin', 'module': 'system'}
    ]
    
    # 插入权限数据
    for perm in permissions_data:
        conn.execute(
            sa.text("""
                INSERT INTO system_permissions (permission_code, permission_name, description, category, module)
                VALUES (:permission_code, :permission_name, :description, :category, :module)
                ON CONFLICT (permission_code) DO NOTHING
            """),
            perm
        )
    
    # 3. 初始化角色权限映射
    role_permission_mappings = {
        'supplier': [
            'view_own_data', 'manage_inventory', 'publish_shared_materials', 
            'respond_to_inquiries', 'manage_orders'
        ],
        'distributor': [
            'view_own_data', 'manage_inventory', 'publish_shared_materials', 
            'publish_demands', 'respond_to_inquiries', 'manage_orders'
        ],
        'airline_buyer': [
            'view_own_data', 'manage_inventory', 'publish_shared_materials',
            'publish_demands', 'manage_orders', 'aog_request'
        ],
        'platform_staff': [
            'view_own_data', 'process_all_orders', 'cross_company_access',
            'audit_shared_materials', 'customer_service', 'data_analytics'
        ],
        'maintenance_engineer': [
            'view_own_data', 'update_maintenance_status', 'technical_support',
            'view_maintenance_data', 'manage_work_orders'
        ],
        'logistics_specialist': [
            'view_own_data', 'track_shipments', 'update_logistics_info',
            'manage_delivery', 'view_logistics_data'
        ],
        'admin': [
            'all_permissions'
        ]
    }
    
    # 建立角色权限关联
    for role_code, permission_codes in role_permission_mappings.items():
        for permission_code in permission_codes:
            conn.execute(
                sa.text("""
                    INSERT INTO role_permissions (role_id, permission_id)
                    SELECT r.id, p.id
                    FROM system_roles r, system_permissions p
                    WHERE r.role_code = :role_code AND p.permission_code = :permission_code
                    ON CONFLICT (role_id, permission_id) DO NOTHING
                """),
                {'role_code': role_code, 'permission_code': permission_code}
            )
    
    # 4. 迁移现有用户数据到新角色系统
    conn.execute(
        sa.text("""
            UPDATE users SET role_id = (
                SELECT id FROM system_roles WHERE role_code = 
                CASE users.user_type
                    WHEN 'supplier' THEN 'supplier'
                    WHEN 'distributor' THEN 'distributor' 
                    WHEN 'airline' THEN 'airline_buyer'
                    WHEN 'airline_buyer' THEN 'airline_buyer'
                    WHEN 'platform_staff' THEN 'platform_staff'
                    WHEN 'maintenance' THEN 'maintenance_engineer'
                    WHEN 'maintenance_engineer' THEN 'maintenance_engineer'
                    WHEN 'logistics_specialist' THEN 'logistics_specialist'
                    WHEN 'admin' THEN 'admin'
                    ELSE 'platform_staff'
                END
            )
            WHERE role_id IS NULL
        """)
    )

def downgrade():
    # 清理种子数据（逆向操作）
    conn = op.get_bind()
    
    # 清理角色权限关联
    conn.execute(sa.text("DELETE FROM role_permissions"))
    
    # 清理权限数据
    conn.execute(sa.text("DELETE FROM system_permissions"))
    
    # 清理角色数据
    conn.execute(sa.text("DELETE FROM system_roles"))
    
    # 重置用户角色ID
    conn.execute(sa.text("UPDATE users SET role_id = NULL"))