# SpiffWorkflow工作流引擎集成文档

**文档版本**: 1.0  
**创建时间**: 2025-07-18  
**作者**: <PERSON> AI Assistant  
**项目**: 航材共享保障平台  

---

## 📋 概述

本文档详细记录了SpiffWorkflow工作流引擎在航材共享保障平台中的集成实现。SpiffWorkflow是一个基于Python的BPMN 2.0工作流引擎，用于实现复杂的业务流程管理。

### 🎯 集成目标

- 实现CLAUDE.md要求的三种审批流程模板
- 提供完整的工作流实例管理功能
- 支持任务处理和状态跟踪
- 提供工作流统计和监控功能

---

## 🔧 技术实现

### 1. 依赖安装

```bash
# 后端依赖
pip install SpiffWorkflow==3.1.1
pip install lxml
```

已更新 `backend/requirements.txt` 文件：
```
# 工作流引擎
SpiffWorkflow==3.1.1
lxml
```

### 2. 数据库模型更新

#### WorkflowInstance 模型
```python
class WorkflowInstance(db.Model):
    """工作流实例模型 - SpiffWorkflow集成版本"""
    __tablename__ = 'workflow_instances'
    
    id = db.Column(db.Integer, primary_key=True)
    workflow_key = db.Column(db.String(100), nullable=False)  # 工作流定义键值
    business_key = db.Column(db.String(100), nullable=False)  # 业务键值
    business_type = db.Column(db.String(50))                  # 业务类型
    business_id = db.Column(db.Integer)                       # 业务对象ID
    initiator_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.Enum('RUNNING', 'COMPLETED', 'CANCELLED', 'ERROR'))
    variables = db.Column(db.Text)                            # 工作流变量(JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
```

#### WorkflowTask 模型
```python
class WorkflowTask(db.Model):
    """工作流任务模型 - SpiffWorkflow集成版本"""
    __tablename__ = 'workflow_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'))
    task_id = db.Column(db.String(100), nullable=False)       # SpiffWorkflow任务ID
    task_name = db.Column(db.String(100), nullable=False)     # 任务名称
    assignee = db.Column(db.String(100))                      # 指派人
    status = db.Column(db.Enum('READY', 'COMPLETED', 'CANCELLED'))
    comment = db.Column(db.Text)                              # 处理意见
    task_data = db.Column(db.Text)                            # 任务数据(JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
```

### 3. 核心服务实现

#### WorkflowService 类
位置：`backend/services/workflow_service.py`

**主要功能：**
- 管理工作流定义和实例
- 处理工作流执行和状态管理
- 支持三种审批流程模板
- 提供工作流任务管理接口

**核心方法：**
```python
def start_workflow(workflow_key, business_key, variables, initiator_id)
def get_workflow_tasks(instance_id)
def complete_task(instance_id, task_id, variables, user_id)
def get_workflow_instance(instance_id)
```

### 4. BPMN工作流定义

#### 简单审批流程 (simple_approval)
```
开始 -> 提交申请 -> 审批 -> 结束
```

#### 多级审批流程 (multi_level_approval)
```
开始 -> 提交申请 -> 部门审批 -> 财务审批 -> 总经理审批 -> 结束
```

#### 并行审批流程 (parallel_approval)
```
开始 -> 提交申请 -> [技术部门审批 + 采购部门审批] -> 最终审批 -> 结束
```

### 5. API接口实现

#### 后端路由
位置：`backend/routes/workflow.py`

**主要接口：**
- `GET /api/workflow/definitions` - 获取工作流定义列表
- `POST /api/workflow/start` - 启动工作流实例
- `GET /api/workflow/instances/{id}` - 获取工作流实例详情
- `GET /api/workflow/instances/{id}/tasks` - 获取当前任务
- `POST /api/workflow/tasks/{id}/complete` - 完成任务
- `GET /api/workflow/my-tasks` - 获取我的待办任务
- `GET /api/workflow/statistics` - 获取工作流统计

#### 前端API
位置：`frontend/src/api/workflow.js`

**主要函数：**
```javascript
export function getWorkflowDefinitions()
export function startWorkflow(params)
export function getWorkflowInstance(instance_id)
export function getWorkflowTasks(instance_id)
export function completeTask(task_id, params)
export function getMyTasks(params)
export function getWorkflowStatistics(params)
```

---

## 🎮 使用示例

### 1. 启动工作流

```javascript
// 前端调用
const workflowData = {
  workflow_key: 'simple_approval',
  business_key: 'ORDER_001',
  variables: {
    title: '订单审批',
    amount: 10000,
    description: '紧急航材采购订单'
  }
}

const result = await startWorkflow(workflowData)
console.log('工作流实例ID:', result.instance_id)
```

### 2. 获取待办任务

```javascript
// 获取我的待办任务
const tasks = await getMyTasks({ 
  page: 1, 
  per_page: 10, 
  status: 'READY' 
})
```

### 3. 完成任务

```javascript
// 完成审批任务
const taskData = {
  instance_id: '123',
  variables: {
    approved: true,
    comment: '审批通过',
    next_assignee: 'manager'
  }
}

await completeTask('task_001', taskData)
```

---

## 📊 工作流模板配置

### 1. 简单审批流程
**适用场景：** 一级审批，适用于简单业务场景  
**流程步骤：**
1. 提交申请
2. 审批
3. 完成

### 2. 多级审批流程
**适用场景：** 部门→财务→总经理多级审批，适用于重要业务  
**流程步骤：**
1. 提交申请
2. 部门审批
3. 财务审批
4. 总经理审批
5. 完成

### 3. 并行审批流程
**适用场景：** 多部门并行审批，适用于需要多方确认的业务  
**流程步骤：**
1. 提交申请
2. 并行网关分支
   - 技术部门审批
   - 采购部门审批
3. 并行网关汇总
4. 最终审批
5. 完成

---

## 🔄 集成状态

### ✅ 已完成功能

1. **SpiffWorkflow依赖安装** - 已安装并配置
2. **数据库模型更新** - 已更新为支持SpiffWorkflow的版本
3. **WorkflowService服务** - 完整实现工作流管理服务
4. **BPMN工作流定义** - 三种审批流程模板已创建
5. **后端API接口** - 完整的工作流管理接口
6. **前端API封装** - 完整的工作流API调用封装
7. **Flask应用集成** - 工作流蓝图已注册

### 🚧 待完成功能

1. **工作流目录自动创建** - 需要在应用启动时创建workflows目录
2. **数据库迁移** - 需要执行数据库迁移更新表结构
3. **Mock数据更新** - 需要更新mockManager.js中的工作流相关Mock数据
4. **前端页面集成** - 需要在现有页面中集成工作流功能
5. **权限控制** - 需要实现基于角色的工作流权限控制

---

## 📝 下一步计划

1. **创建工作流目录** - 在应用启动时自动创建必要的目录结构
2. **数据库迁移** - 执行数据库迁移脚本
3. **Mock数据完善** - 更新前端Mock数据支持工作流测试
4. **集成测试** - 测试工作流功能的完整性
5. **文档更新** - 更新用户使用文档

---

## 🛡️ 安全考虑

1. **权限控制** - 确保只有有权限的用户能够启动和处理工作流
2. **数据验证** - 验证工作流变量和任务数据的有效性
3. **审计日志** - 记录所有工作流操作的审计信息
4. **错误处理** - 提供完善的错误处理和恢复机制

---

## 📚 相关资源

- [SpiffWorkflow官方文档](https://spiffworkflow.readthedocs.io/)
- [BPMN 2.0规范](https://www.omg.org/spec/BPMN/2.0/)
- [项目CLAUDE.md文档](../CLAUDE.md)
- [数据库模型文档](../backend/models.py)

---

**注意：** 本集成基于SpiffWorkflow 3.1.1版本，确保在生产环境中使用相同版本以保证兼容性。