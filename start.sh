#!/bin/bash
# 航材共享保障平台 - 统一启动脚本
# 确保在任何机器上都能正确启动项目

set -e  # 遇到错误立即退出

echo "🚀 启动航材共享保障平台..."

# 检查必要工具
echo "🔍 检查系统环境..."
command -v python3 >/dev/null 2>&1 || { echo "❌ Python3 未安装"; exit 1; }
command -v npm >/dev/null 2>&1 || { echo "❌ Node.js/npm 未安装"; exit 1; }
command -v psql >/dev/null 2>&1 || { echo "❌ PostgreSQL 未安装"; exit 1; }

# 检查PostgreSQL服务
echo "🔍 检查PostgreSQL服务..."
# 使用pg_isready检查服务状态，避免密码提示
if ! pg_isready >/dev/null 2>&1; then
    echo "❌ PostgreSQL服务未运行，请先启动PostgreSQL"
    
    # 检测安装方式并给出对应提示
    if [ -d "/Library/PostgreSQL" ]; then
        echo "💡 提示：检测到DMG安装的PostgreSQL"
        echo "   - 方式1：打开 系统偏好设置 > PostgreSQL，点击启动"
        echo "   - 方式2：或在应用程序中找到PostgreSQL并启动"
    elif command -v brew >/dev/null 2>&1 && brew list postgresql >/dev/null 2>&1; then
        echo "💡 提示：检测到Homebrew安装的PostgreSQL"
        echo "   - 运行：brew services start postgresql"
    elif [ -d "/Applications/Postgres.app" ]; then
        echo "💡 提示：检测到Postgres.app"
        echo "   - 请启动 /Applications/Postgres.app 应用"
    else
        echo "💡 提示：请根据你的PostgreSQL安装方式启动服务"
        echo "   - DMG安装：系统偏好设置 > PostgreSQL"
        echo "   - Homebrew：brew services start postgresql"
        echo "   - Postgres.app：启动应用程序"
    fi
    exit 1
fi

# 检查数据库是否存在
echo "🔍 检查数据库..."
# 设置PostgreSQL密码环境变量避免密码提示
export PGPASSWORD="cassdemo_password"
if ! psql -d cassdemo_dev -U cassdemo_user -h localhost -c "SELECT 1" >/dev/null 2>&1; then
    echo "⚠️  数据库不存在，正在创建..."
    echo "📝 正在创建数据库用户和数据库..."
    
    # 尝试多种连接方式创建数据库
    unset PGPASSWORD
    
    # 方法1: 尝试使用当前系统用户连接postgres数据库
    if psql -d postgres -c "SELECT 1" >/dev/null 2>&1; then
        echo "✅ 使用当前用户连接PostgreSQL"
        DB_CREATION_SUCCESS=true
    # 方法2: 尝试使用postgres用户连接
    elif psql -d postgres -U postgres -c "SELECT 1" >/dev/null 2>&1; then
        echo "✅ 使用postgres用户连接PostgreSQL"
        DB_CREATION_SUCCESS=true
    # 方法3: 尝试使用postgres用户和localhost连接
    elif psql -d postgres -U postgres -h localhost -c "SELECT 1" >/dev/null 2>&1; then
        echo "✅ 使用postgres用户和localhost连接PostgreSQL"
        DB_CREATION_SUCCESS=true
    else
        echo "❌ 无法连接到PostgreSQL，请检查："
        echo "   1. PostgreSQL服务是否正在运行"
        echo "   2. 是否有合适的用户权限"
        echo "   3. pg_hba.conf配置是否允许连接"
        echo ""
        echo "💡 手动创建数据库的命令："
        echo "   sudo -u postgres psql -c \"CREATE USER cassdemo_user WITH PASSWORD 'cassdemo_password';\""
        echo "   sudo -u postgres psql -c \"CREATE DATABASE cassdemo_dev OWNER cassdemo_user;\""
        echo "   sudo -u postgres psql -c \"GRANT ALL PRIVILEGES ON DATABASE cassdemo_dev TO cassdemo_user;\""
        exit 1
    fi
    
    if [ "$DB_CREATION_SUCCESS" = true ]; then
        echo "📝 创建数据库用户..."
        # 创建用户（如果不存在）
        psql -d postgres -c "
            DO \$\$
            BEGIN
                IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'cassdemo_user') THEN
                    CREATE USER cassdemo_user WITH PASSWORD 'cassdemo_password';
                    RAISE NOTICE '✅ 创建用户: cassdemo_user';
                ELSE
                    RAISE NOTICE '✅ 用户已存在: cassdemo_user';
                END IF;
            END
            \$\$;
        " >/dev/null 2>&1 || echo "⚠️  用户创建可能失败，继续执行..."
        
        echo "📝 创建数据库..."
        # 创建数据库（如果不存在）
        psql -d postgres -c "CREATE DATABASE cassdemo_dev OWNER cassdemo_user;" >/dev/null 2>&1 || echo "⚠️  数据库可能已存在"
        
        echo "📝 设置数据库权限..."
        # 设置权限
        psql -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE cassdemo_dev TO cassdemo_user;" >/dev/null 2>&1
        
        echo "✅ 数据库创建完成"
    fi
fi

# 重新设置密码环境变量用于后续连接测试
export PGPASSWORD="cassdemo_password"

# 确保停止旧进程
echo "🧹 清理旧进程..."
pkill -f "python3 app.py" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
lsof -i :5001 | awk 'NR>1 {print $2}' | xargs -r kill -9 2>/dev/null || true
lsof -i :3000 | awk 'NR>1 {print $2}' | xargs -r kill -9 2>/dev/null || true
sleep 2

# 启动后端
echo "🔧 启动后端服务器..."
cd backend
python3 app.py > ../backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务器启动..."
for i in {1..15}; do
    if curl -s http://127.0.0.1:5001/ >/dev/null 2>&1; then
        echo "✅ 后端服务器启动成功"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "❌ 后端服务器启动失败"
        cat backend.log
        exit 1
    fi
    sleep 2
done

# 启动前端
# 2025-07-21日以后，开发阶段的前端都用演示模式启动（演示模式）
# 仅容错备用数据
echo "🎨 启动前端服务器..."
cd frontend
# npm run dev > ../frontend.log 2>&1 &
npm run dev -- --mode staging > ../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# 等待前端启动
echo "⏳ 等待前端服务器启动..."
for i in {1..15}; do
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        echo "✅ 前端服务器启动成功"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "❌ 前端服务器启动失败"
        cat frontend.log
        exit 1
    fi
    sleep 2
done

# 输出访问信息
echo ""
echo "🎉 启动完成！"
echo ""
echo "📍 访问地址:"
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://127.0.0.1:5001"
echo ""
echo "🔑 测试账号:"
echo "   管理员: admin / admin123"
echo "   航空公司: airline_demo / demo123"
echo ""
echo "📝 进程ID:"
echo "   后端进程: $BACKEND_PID"
echo "   前端进程: $FRONTEND_PID"
echo ""
echo "⏹️  停止服务: ./stop.sh"
echo ""

# 清理密码环境变量
unset PGPASSWORD