# 航材共享保障平台 - 环境配置指南

## 🎯 目标
确保代码在任何机器（公司、家里、团队成员）上都能**一致运行**，避免环境差异导致的问题。

## 📋 环境要求

### 必需软件
- **Python 3.9+** - 后端开发语言
- **Node.js 16+** - 前端开发环境  
- **PostgreSQL 12+** - 数据库
- **Git** - 版本控制

### 推荐软件
- **Homebrew** (macOS) - 包管理器
- **VSCode** - 代码编辑器

## 🚀 一键启动（推荐）

### 启动项目
```bash
./start.sh
```

### 停止项目  
```bash
./stop.sh
```

启动脚本会自动：
1. 检查系统环境
2. 验证PostgreSQL服务
3. 创建数据库（如果不存在）
4. 启动后端和前端服务
5. 输出访问地址和测试账号

## 🔧 手动启动（备用）

### 1. 数据库准备
```bash
# 启动PostgreSQL服务
brew services start postgresql@16

# 创建数据库用户和数据库
psql -d postgres << EOF
CREATE USER cassdemo_user WITH PASSWORD 'cassdemo_password';
CREATE DATABASE cassdemo_dev OWNER cassdemo_user;
GRANT ALL PRIVILEGES ON DATABASE cassdemo_dev TO cassdemo_user;
EOF
```

### 2. 启动后端
```bash
cd backend
python3 app.py
```

### 3. 启动前端
```bash
cd frontend  
npm run dev
```

## 🌐 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://127.0.0.1:5001

## 🔑 测试账号

| 角色 | 用户名 | 密码 |
|------|--------|------|
| 管理员 | admin | admin123 |
| 航空公司 | airline_demo | demo123 |
| 供应商 | supplier_demo | demo123 |
| 维修工程师 | maintenance_demo | demo123 |

## 🛠️ 环境配置文件

### 根目录 .env
```bash
# 统一数据库配置
DATABASE_URL=postgresql://cassdemo_user:cassdemo_password@localhost:5432/cassdemo_dev

# API配置  
FLASK_ENV=development
BACKEND_PORT=5001
API_BASE_PATH=/api/v1

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### 前端 frontend/.env
```bash
# API地址
VITE_API_BASE_URL=http://127.0.0.1:5001

# Mock配置
VITE_MOCK_ENABLED=false
```

## 🔍 常见问题解决

### Q1: 端口被占用
```bash
# 清理端口占用
./stop.sh
# 或手动清理
lsof -i :5001 | awk 'NR>1 {print $2}' | xargs kill -9
```

### Q2: PostgreSQL连接失败
```bash
# 检查服务状态
brew services list | grep postgresql

# 重启服务
brew services restart postgresql@16
```

### Q3: 前端Mock数据问题
确保 `VITE_MOCK_ENABLED=false`，直接调用真实API

### Q4: API路径404错误
检查后端日志：`tail -f backend.log`

## 🔄 同步代码到不同机器

### 推送代码到公司
```bash
git add .
git commit -m "环境配置标准化"  
git push origin main
```

### 在公司机器拉取
```bash
git pull origin main
./start.sh  # 一键启动
```

## 📝 环境差异说明

**之前的问题**：
- 公司机器：development模式 + Mock启用 = 正常（掩盖了API路径错误）
- 家里机器：staging模式 + Mock禁用 = 出错（暴露API路径问题）

**现在的解决**：
- ✅ 修复了后端API路径配置
- ✅ 统一环境配置文件
- ✅ 提供一键启动脚本
- ✅ Mock数据默认禁用，直接使用真实API

## 🎉 验证环境无关性

测试以下场景确保代码环境无关：
1. ✅ 全新机器首次启动
2. ✅ 不同操作系统（macOS/Linux/Windows）
3. ✅ 不同Node.js版本
4. ✅ 不同PostgreSQL版本
5. ✅ 有无环境变量配置