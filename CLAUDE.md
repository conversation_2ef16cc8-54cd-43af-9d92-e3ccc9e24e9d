# CLAUDE.md
你是一个资深程序员，并在民航领域有丰富的专业知识。
1. 前端项目在: frontend
2. 后端项目在: backend
3.  talk.md文档是用户与AI助手的交互记录，后续进行工作也需要每次把交互内容都记录到这个文档中,并按日期排序：最新的交互写到文档前面
4. 所有数据操作都要在postgresql上进行，不要用sqlite版本了。
5. 每天开发前读talk文档前一天的记录（以日期为准）
6. 2025-07-23日期之后开发的代码里不要再添加mock数据了，完全用真实api测试。
使用中文回复用户

## git 操作
- 你完成了一项功能开发后，需要进行commit操作
- 下面是参考命令：
远程仓库地址：https://gitee.com/echoadan/cassdemo.git
user.name "echoadan"
user.email "<EMAIL>"
如果当前项目目录下没有设置git，请根据需要设置，并自行判断要加入.gitignore的文档

## 1. 项目概述

### 1.1 项目背景
这是一个**航材共享保障平台**项目，是一个B2B航材共享服务平台，连接航空公司、航材公司、维修企业和物流企业，实现航材资源的高效共享和配置。是基于现有的供应链管理系统，计划开发一个专门的航材共享门户网站，类似于ILSmart平台，将共享模块功能从原系统中拆分出来，作为门户网站的核心功能之一，同时保持与供应链管理系统的紧密集成。

### 1.2 项目目标
- 构建一个专业的航材共享门户网站，提供航材交易、共享和信息服务
- 将原供应链管理系统中的共享模块功能升级并整合到门户网站中
- 提供更丰富的行业资讯、市场分析和专业服务
- 建立航材供应商和客户之间的桥梁，促进资源高效流通
- 确保与现有供应链管理系统的无缝集成

## 2. 技术架构
### 2.1 核心技术栈
- **后端**: Python 3.9 + Flask 3.0 + PostgreSQL + SQLAlchemy
- **前端**: Vue 3 + Element Plus + Vite 
- **开发语言**: 原生JavaScript (不使用TypeScript)
- **状态管理**: Pinia
- **工作流引擎**: SpiffWorkflow (用于审批流程)
### 2.2 系统架构
- 采用MVC架构模式
- 使用Blueprint进行模块化设计 
- RESTful API设计
- 权限控制与用户认证 (JWT Token)
- 与供应链管理系统通过API网关集成

## 3. 功能模块设计

### 3.1 门户首页
- **行业动态展示**：航空业最新新闻和动态
- **热门航材推荐**：基于搜索频率和交易量的热门航材
- **平台数据概览**：注册用户数、在线航材数、累计交易额等
- **成功案例展示**：平台促成的典型交易案例
- **快速搜索入口**：航材快速搜索功能
- **用户登录/注册入口**：用户账号管理

### 3.2 航材共享交易平台（核心模块）
- **航材搜索**
  - 精准搜索：按件号、序列号精确查询
  - 高级搜索：多条件组合查询
  - 智能推荐：基于用户历史行为的推荐
  - 搜索结果筛选：按状态、价格、供应商等筛选

- **共享件管理**
  - 共享件发布：用户发布可共享航材
  - 共享件审核：平台审核流程
  - 共享件状态管理：上架/下架/已预订/已成交
  - 共享策略设置：库存保留量、价格策略等

- **交易管理**
  - 询价管理：在线询价和报价
  - 交易申请：买方发起交易申请
  - 交易确认：卖方确认交易细节
  - 订单生成：自动生成标准化订单
  - 订单跟踪：全流程订单状态跟踪

- **支付结算**
  - 多种支付方式：在线支付、线下转账等
  - 交易担保：平台担保交易安全
  - 发票管理：电子发票申请和管理
  - 退款处理：异常情况的退款流程

### 3.3 供应商展示
- **供应商目录**：按类别、地区等分类的供应商目录
- **供应商详情**：供应商基本信息、资质、产品目录等
- **供应商评级**：基于交易记录和用户评价的供应商评级
- **供应商认证**：供应商资质认证流程和展示
- **供应商搜索**：按名称、产品类型等搜索供应商

### 3.4 航材资讯中心
- **行业新闻**：航空业最新动态和新闻
- **政策法规**：航空材料相关政策和法规解读
- **市场分析**：航材市场趋势分析报告
- **技术文章**：航材技术知识和维修指南
- **活动公告**：行业展会、研讨会等活动信息

### 3.5 专业服务
- **航材鉴定**：第三方航材真伪鉴定服务
- **航材评估**：航材价值评估服务
- **物流服务**：专业航材物流解决方案
- **金融服务**：航材交易融资、保险等服务
- **技术咨询**：航材技术问题咨询服务

### 3.6 用户中心
- **账户管理**：个人/企业信息管理
- **我的航材**：已发布航材管理
- **我的订单**：交易订单管理
- **我的收藏**：收藏的航材和供应商
- **消息中心**：系统通知、交易消息等
- **资金管理**：账户余额、交易记录等

### 3.7 管理后台
- **用户管理**：平台用户管理
- **内容管理**：资讯、公告等内容管理
- **交易监控**：平台交易数据监控
- **审核管理**：共享件、供应商审核
- **数据分析**：平台运营数据分析
- **系统配置**：平台参数配置

## 4. 与供应链管理系统的集成

### 4.1 系统集成架构
- **独立认证体系**：门户网站和供应链管理系统维持独立的用户认证体系
- **数据交换层**：建立中间数据交换层，控制数据流向和访问权限
- **API网关**：所有系统间通信通过API网关进行，实施严格的访问控制
- **数据脱敏机制**：敏感数据在共享前进行脱敏处理
- **审计日志**：所有跨系统操作记录完整审计日志

### 4.2 数据同步机制
- **选择性数据同步**：供应链系统管理员可精确控制哪些数据可同步至门户网站
- **单向数据流**：主要数据流向为供应链系统→门户网站，门户网站的数据变更需经审核才能回流
- **批量同步**：非实时数据采用定时批量同步机制，减少系统耦合
- **关键数据实时同步**：库存数量、订单状态等关键数据采用准实时同步
- **数据版本控制**：实施数据版本控制，确保数据一致性和冲突解决

### 4.3 共享件发布机制
- **内部标记后审批**：内部用户在供应链系统中标记可共享库存，经审批后才能发布
- **发布控制台**：供应链系统提供专门的"门户发布控制台"，管理所有发布到门户的内容
- **自动数据过滤**：系统自动过滤敏感信息，只同步必要的产品数据到门户
- **发布规则引擎**：可配置的规则引擎，控制哪些产品在什么条件下可以发布
- **紧急下架机制**：提供一键紧急下架功能，快速从门户撤回所有或特定产品

### 4.4 订单处理集成
- **订单通知机制**：门户订单通过通知机制告知供应链系统，而非直接写入
- **订单确认流程**：供应链系统管理员需确认接受门户订单后才正式处理
- **订单映射**：门户订单与供应链系统订单通过映射关系关联，保持系统独立性
- **状态同步API**：提供受限的API仅用于更新订单状态，不暴露内部处理细节
- **异常处理机制**：订单处理异常时的回退和通知机制

### 4.5 安全控制措施
- **API访问控制**：基于令牌的API访问控制，定期轮换密钥
- **IP白名单**：限制系统间通信的IP地址范围
- **数据传输加密**：所有系统间通信采用TLS 1.3加密
- **访问频率限制**：防止过度查询和潜在的DOS攻击
- **数据访问审计**：记录所有跨系统的数据访问行为
- **漏洞扫描**：定期对集成接口进行安全漏洞扫描

### 4.6 技术实现方案
- **API网关**：使用Kong或Apigee作为API网关，管理所有系统间通信
- **消息队列**：使用RabbitMQ实现系统间的异步通信，降低耦合
- **数据同步服务**：专用的同步服务，负责数据筛选、转换和同步
- **缓存层**：使用Redis缓存频繁访问的数据，减少跨系统查询
- **监控系统**：实时监控系统集成状态，及时发现并解决问题

## 5. 用户体验设计

### 5.1 界面设计原则
- 专业性：符合航空行业专业形象
- 简洁性：清晰的信息层次和导航结构
- 易用性：简化操作流程，降低学习成本
- 响应式：适配不同设备，确保移动端体验
- 个性化：根据用户角色和偏好定制内容

### 5.2 关键页面设计
- **首页**：突出搜索功能，展示核心数据和热门航材
- **搜索结果页**：高效展示搜索结果，提供丰富的筛选选项
- **航材详情页**：完整展示航材信息，突出交易入口
- **供应商页面**：专业展示供应商资质和产品
- **用户中心**：直观管理个人航材和交易

### 5.3 交互设计
- **智能搜索**：搜索建议、历史记录、热门搜索
- **一键询价**：简化询价流程，提高效率
- **状态通知**：重要状态变更实时通知
- **操作引导**：新用户引导流程，提高上手速度
- **在线沟通**：买卖双方在线沟通工具

## 6. 开发规范

### 6.1 API开发规范
- **API文件位置**: `src/api/`
- **命名规范**: `模块名称Api.js` (例如: `UserApi.js`)
- **请求方式限制**: 仅允许使用 GET 和 POST 两种请求方式
- **Mock数据管理**: 统一在 `src/api/mockManager.js` 中管理

### 6.2 API接口注释格式
每个API接口必须包含完整的JSDoc注释：
```javascript
/**
 * 接口名称
 * 功能描述：详细描述接口的作用
 * 入参：{ param1: 类型, param2: 类型 }
 * 返回参数：返回数据结构说明
 * url地址：/api/endpoint
 * 请求方式：GET/POST
 */
export function apiFunction(params) {
  return get('/api/endpoint', params)
}
```

### 6.3 统一响应格式
```json
{
  "error": 0,
  "body": {},
  "message": "success",
  "success": true
}
```

### 6.4 Vue组件开发规范
- **组件位置**: `src/components/` 按功能模块分目录
- **页面位置**: `src/views/` 按业务模块分目录
- **使用Composition API**: 必须使用Vue 3 Composition API
- **命名规范**: 
  - 组件文件: PascalCase (如 `MyComponent.vue`)
  - 变量/函数: camelCase (如 `myVariable`, `myFunction`)
  - 模板中组件: kebab-case (如 `<my-component>`)

### 6.5 工作流设计
- 使用SpiffWorkflow作为工作流引擎
- 支持三种审批流程模板：
  - 简单审批流 (一级审批)
  - 多级审批流 (部门→财务→总经理)
  - 并行审批流 (多部门同时审批)

## 7. 开发限制

### 7.1 禁止事项
- 不允许在对话中使用 `npm run dev` 启动项目
- 不要在Vue页面中定义测试数据，所有数据必须来自后端服务或Mock接口
- 不要创建测试文档
- 页面组件嵌套不要超过三层
- 不允许使用TypeScript
- 每个方法行数不超过300行

### 7.2 必须遵循
- 所有API接口都必须在 `mockManager.js` 中提供Mock实现
- API文档变更时必须同步更新对应的API文档
- 遵循DRY原则，避免重复代码
- 为复杂逻辑添加注释
- 始终使用简体中文回复用户

## 8. 常用命令

由于此项目尚未包含具体的构建脚本，以下是通常的Flask + Vue项目命令：

### 后端开发 (Python Flask)
```bash
# 安装依赖
pip install -r requirements.txt

# 运行Flask开发服务器
python app.py
# 或
flask run
```

### 前端开发 (Vue 3)
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 前端启动（演示模式）
npm run dev -- --mode staging  # 仅容错备用数据
# 构建生产版本
npm run build

# 代码检查
npm run lint
```

## 9. 特殊功能说明

### 9.1 共享件发布机制
支持多种共享模式：
1. **库存直接共享模式** - 从库存管理界面标记为"可共享"
2. **共享发布专区** - 专门的发布界面
3. **共享管理中心** - 每个客户的共享件管理界面
4. **共享审核流程** - 自动审核和上架流程

### 9.2 紧急AOG流程
支持航空紧急情况(Aircraft on Ground)的快速响应流程：
- 紧急需求标记
- 优先匹配算法
- 快速确认流程
- 专车配送服务

### 9.3 用户角色

#### 9.3.1 核心业务角色

**🔴 供应商 (Supplier) - 纯销售角色**
- **业务定位**: 航材制造商，只对外销售，不采购
- **核心功能**: 
  - ✅ 发布销售产品和价格
  - ✅ 管理自产品库存
  - ✅ 处理销售订单和客户询价
  - ✅ 发布富余库存共享
  - ❌ 不发布采购需求
  - ❌ 不处理采购订单
- **典型用户**: 航材制造厂商、OEM厂商

**🔵 分销商 (Distributor) - 双向贸易角色**  
- **业务定位**: 航材贸易商，既采购也销售，是供应链的中间环节
- **核心功能**:
  - ✅ 发布采购需求寻找货源
  - ✅ 发布销售产品给客户
  - ✅ 双向库存管理（采购+销售）
  - ✅ 采购订单和销售订单双重管理
  - ✅ 全面的共享件管理
  - ✅ 库存周转和买卖差价分析
- **典型用户**: 航材贸易公司、代理商、经销商

**🟡 航空公司 (Airline) - 纯采购角色**
- **业务定位**: 航材最终使用方，主要采购航材用于维修和运营
- **核心功能**:
  - ✅ 发布航材需求和采购计划
  - ✅ 管理自用航材库存
  - ✅ 处理采购订单
  - ✅ 发布闲置航材共享（自用富余）
  - ❌ 不发布销售产品（非主营业务）
- **典型用户**: 各大航空公司采购部门

#### 9.3.2 平台管理角色

- **航材业务员 (Platform Staff)**: 管理平台库存、处理订单，协助各类用户完成交易。平台内部员工。
- **维修工程师 (Maintenance Engineer)**: 提供技术支持、更新维修状态，可以是平台内部或MRO企业人员。
- **物流专员 (Logistics Specialist)**: 跟踪配送状态、更新物流信息，确保航材及时送达。
- **系统管理员 (Admin)**: 平台系统配置、数据分析、用户权限管理等。

#### 9.3.3 角色业务流程关系

```
航材制造商(Supplier) → 分销商(Distributor) → 航空公司(Airline)
    ↓                    ↕️                    ↓
  只销售              双向交易               只采购
    ↓                    ↕️                    ↓
产品发布              贸易管理              需求发布
销售管理              库存周转              采购管理
```

**说明**: 
- 供应商专注生产和销售，不参与采购
- 分销商连接供需两端，既要采购货源又要销售给客户
- 航空公司专注采购和使用，偶尔共享闲置航材