#!/bin/bash
# 航材共享保障平台 - 统一停止脚本

echo "🛑 停止航材共享保障平台..."

# 停止Python进程
echo "⏹️  停止后端服务器..."
pkill -f "python3 app.py" 2>/dev/null || true

# 停止npm进程
echo "⏹️  停止前端服务器..."
pkill -f "npm run dev" 2>/dev/null || true

# 强制清理端口
echo "🧹 清理端口占用..."
lsof -i :5001 | awk 'NR>1 {print $2}' | xargs -r kill -9 2>/dev/null || true
lsof -i :3000 | awk 'NR>1 {print $2}' | xargs -r kill -9 2>/dev/null || true

sleep 1
echo "✅ 所有服务已停止"