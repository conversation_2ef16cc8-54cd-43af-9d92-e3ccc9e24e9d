#\!/bin/bash

# 数据库每日同步脚本 - 最简化版本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="daily_sync"
mkdir -p $BACKUP_DIR

echo "=== 开始数据库同步 ($DATE) ==="

# 导出数据 (仅数据，不包括表结构)
pg_dump -h localhost -U cassdemo_user -d cassdemo_dev \
  --data-only \
  --inserts \
  --column-inserts \
  -f $BACKUP_DIR/data_sync_$DATE.sql

echo "✓ 数据导出完成: $BACKUP_DIR/data_sync_$DATE.sql"
echo "=== 复制此文件到公司机器，然后运行: ==="
echo "PGPASSWORD=cassdemo_password psql -h localhost -U cassdemo_user -d cassdemo_dev -f data_sync_$DATE.sql"
EOF < /dev/null