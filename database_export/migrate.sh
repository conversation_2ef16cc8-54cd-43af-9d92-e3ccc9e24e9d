#!/bin/bash

# PostgreSQL数据库迁移脚本
# 版本: 1.0
# 日期: 2025-07-23

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="cassdemo_dev"
DB_USER="cassdemo_user"
DB_PASSWORD="cassdemo_password"
SQL_FILE="cassdemo_dev_20250723_002540.sql"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  航材共享保障平台数据库迁移脚本  ${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查SQL文件是否存在
if [[ ! -f "$SQL_FILE" ]]; then
    echo -e "${RED}错误: 找不到SQL文件: $SQL_FILE${NC}"
    echo -e "${YELLOW}请确保 $SQL_FILE 文件在当前目录中${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 找到数据库导出文件: $SQL_FILE${NC}"

# 检查PostgreSQL是否安装
if ! command -v psql &> /dev/null; then
    echo -e "${RED}错误: PostgreSQL客户端未安装${NC}"
    echo -e "${YELLOW}请先安装PostgreSQL:${NC}"
    echo -e "  macOS: brew install postgresql"
    echo -e "  Ubuntu: sudo apt install postgresql-client"
    exit 1
fi

echo -e "${GREEN}✓ PostgreSQL客户端已安装${NC}"

# 测试数据库连接
echo -e "${YELLOW}正在测试数据库连接...${NC}"
if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -c "SELECT 1;" &> /dev/null; then
    echo -e "${GREEN}✓ 数据库服务连接正常${NC}"
else
    echo -e "${RED}错误: 无法连接到数据库服务${NC}"
    echo -e "${YELLOW}请检查PostgreSQL服务是否启动:${NC}"
    echo -e "  macOS: brew services start postgresql"
    echo -e "  Linux: sudo systemctl start postgresql"
    exit 1
fi

# 创建用户和数据库
echo -e "${YELLOW}正在创建数据库用户和数据库...${NC}"

# 检查用户是否已存在
USER_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -t -c "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER';" | tr -d ' ')

if [[ "$USER_EXISTS" != "1" ]]; then
    echo -e "${YELLOW}创建数据库用户: $DB_USER${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
else
    echo -e "${GREEN}✓ 数据库用户 $DB_USER 已存在${NC}"
fi

# 检查数据库是否已存在
DB_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" | tr -d ' ')

if [[ "$DB_EXISTS" != "1" ]]; then
    echo -e "${YELLOW}创建数据库: $DB_NAME${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -c "CREATE DATABASE $DB_NAME WITH OWNER $DB_USER;"
else
    echo -e "${YELLOW}数据库 $DB_NAME 已存在，将覆盖数据${NC}"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}迁移已取消${NC}"
        exit 0
    fi
fi

# 授权
echo -e "${YELLOW}设置数据库权限...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U postgres -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"

# 导入数据库
echo -e "${YELLOW}正在导入数据库 (这可能需要几分钟)...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f $SQL_FILE

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✓ 数据库导入成功${NC}"
else
    echo -e "${RED}错误: 数据库导入失败${NC}"
    exit 1
fi

# 验证导入结果
echo -e "${YELLOW}正在验证数据导入...${NC}"
RESULT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -t -c "
SELECT 
  'users: ' || (SELECT COUNT(*) FROM users) || ', ' ||
  'materials: ' || (SELECT COUNT(*) FROM materials) || ', ' ||
  'permissions: ' || (SELECT COUNT(*) FROM permissions);
" | tr -d ' ')

echo -e "${GREEN}✓ 数据统计: $RESULT${NC}"

# 显示测试账号
echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}           迁移完成！           ${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "${GREEN}测试账号列表:${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
    username as \"用户名\", 
    user_type as \"角色类型\", 
    company_name as \"公司名称\"
FROM users 
WHERE username LIKE '%demo%' 
ORDER BY user_type;
"

echo -e "\n${YELLOW}下一步操作:${NC}"
echo -e "1. cd ../backend && python app.py"
echo -e "2. cd ../frontend && npm install && npm run dev"
echo -e "3. 使用 supplier_demo/demo123 登录测试"

echo -e "\n${GREEN}数据库连接信息:${NC}"
echo -e "Host: $DB_HOST:$DB_PORT"
echo -e "Database: $DB_NAME"
echo -e "Username: $DB_USER"
echo -e "Password: $DB_PASSWORD"

echo -e "\n${GREEN}迁移完成! 🎉${NC}"