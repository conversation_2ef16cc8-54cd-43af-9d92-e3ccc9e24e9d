@echo off
setlocal

REM PostgreSQL数据库迁移脚本 (Windows版本)
REM 版本: 1.0
REM 日期: 2025-07-23

echo ========================================
echo   航材共享保障平台数据库迁移脚本  
echo ========================================

REM 数据库配置
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=cassdemo_dev
set DB_USER=cassdemo_user
set DB_PASSWORD=cassdemo_password
set SQL_FILE=cassdemo_dev_20250723_002540.sql

REM 检查SQL文件是否存在
if not exist "%SQL_FILE%" (
    echo 错误: 找不到SQL文件: %SQL_FILE%
    echo 请确保 %SQL_FILE% 文件在当前目录中
    pause
    exit /b 1
)

echo ✓ 找到数据库导出文件: %SQL_FILE%

REM 检查PostgreSQL是否安装
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: PostgreSQL客户端未安装
    echo 请先安装PostgreSQL: https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo ✓ PostgreSQL客户端已安装

REM 设置密码环境变量
set PGPASSWORD=%DB_PASSWORD%

echo 正在测试数据库连接...
psql -h %DB_HOST% -U postgres -d postgres -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 无法连接到数据库服务
    echo 请检查PostgreSQL服务是否启动
    echo 服务管理: services.msc 查找 postgresql 服务
    pause
    exit /b 1
)

echo ✓ 数据库服务连接正常

echo 正在创建数据库用户和数据库...

REM 创建用户（忽略错误，因为用户可能已存在）
psql -h %DB_HOST% -U postgres -d postgres -c "CREATE USER %DB_USER% WITH PASSWORD '%DB_PASSWORD%';" >nul 2>&1

REM 创建数据库（忽略错误，因为数据库可能已存在）
psql -h %DB_HOST% -U postgres -d postgres -c "CREATE DATABASE %DB_NAME% WITH OWNER %DB_USER%;" >nul 2>&1

REM 授权
echo 设置数据库权限...
psql -h %DB_HOST% -U postgres -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE %DB_NAME% TO %DB_USER%;"

REM 导入数据库
echo 正在导入数据库 (这可能需要几分钟)...
psql -h %DB_HOST% -U %DB_USER% -d %DB_NAME% -f %SQL_FILE%

if %errorlevel% neq 0 (
    echo 错误: 数据库导入失败
    pause
    exit /b 1
)

echo ✓ 数据库导入成功

REM 验证导入结果
echo 正在验证数据导入...
psql -h %DB_HOST% -U %DB_USER% -d %DB_NAME% -c "SELECT 'users: ' || (SELECT COUNT(*) FROM users) || ', materials: ' || (SELECT COUNT(*) FROM materials) || ', permissions: ' || (SELECT COUNT(*) FROM permissions);"

echo.
echo ========================================
echo            迁移完成！           
echo ========================================

echo 测试账号列表:
psql -h %DB_HOST% -U %DB_USER% -d %DB_NAME% -c "SELECT username as \"用户名\", user_type as \"角色类型\", company_name as \"公司名称\" FROM users WHERE username LIKE '%%demo%%' ORDER BY user_type;"

echo.
echo 下一步操作:
echo 1. cd ..\backend ^&^& python app.py
echo 2. cd ..\frontend ^&^& npm install ^&^& npm run dev
echo 3. 使用 supplier_demo/demo123 登录测试

echo.
echo 数据库连接信息:
echo Host: %DB_HOST%:%DB_PORT%
echo Database: %DB_NAME%
echo Username: %DB_USER%
echo Password: %DB_PASSWORD%

echo.
echo 迁移完成! 🎉
pause