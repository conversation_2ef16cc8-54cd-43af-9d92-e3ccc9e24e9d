--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9 (Homebrew)
-- Dumped by pg_dump version 16.9 (Homebrew)

-- Started on 2025-07-23 00:25:40 CST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY public.workflow_tasks DROP CONSTRAINT IF EXISTS workflow_tasks_instance_id_fkey;
ALTER TABLE IF EXISTS ONLY public.workflow_instances DROP CONSTRAINT IF EXISTS workflow_instances_initiator_id_fkey;
ALTER TABLE IF EXISTS ONLY public.work_orders DROP CONSTRAINT IF EXISTS work_orders_assigned_technician_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_permission_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_granted_by_fkey;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_workflow_instance_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_rejected_by_fkey;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_owner_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_material_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_approved_by_fkey;
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS orders_supplier_id_fkey;
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS orders_buyer_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_status_history DROP CONSTRAINT IF EXISTS order_status_history_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_status_history DROP CONSTRAINT IF EXISTS order_status_history_operator_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS order_items_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS order_items_material_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_attachments DROP CONSTRAINT IF EXISTS order_attachments_uploaded_by_fkey;
ALTER TABLE IF EXISTS ONLY public.order_attachments DROP CONSTRAINT IF EXISTS order_attachments_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_approvals DROP CONSTRAINT IF EXISTS order_approvals_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.order_approvals DROP CONSTRAINT IF EXISTS order_approvals_approver_id_fkey;
ALTER TABLE IF EXISTS ONLY public.notifications DROP CONSTRAINT IF EXISTS notifications_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.logistics_info DROP CONSTRAINT IF EXISTS logistics_info_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.labor_records DROP CONSTRAINT IF EXISTS labor_records_work_order_id_fkey;
ALTER TABLE IF EXISTS ONLY public.labor_records DROP CONSTRAINT IF EXISTS labor_records_technician_id_fkey;
ALTER TABLE IF EXISTS ONLY public.inventory_items DROP CONSTRAINT IF EXISTS inventory_items_supplier_id_fkey;
ALTER TABLE IF EXISTS ONLY public.inventory_items DROP CONSTRAINT IF EXISTS inventory_items_material_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demands DROP CONSTRAINT IF EXISTS demands_requester_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_responses DROP CONSTRAINT IF EXISTS demand_responses_supplier_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_responses DROP CONSTRAINT IF EXISTS demand_responses_demand_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_matches DROP CONSTRAINT IF EXISTS demand_matches_supplier_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_matches DROP CONSTRAINT IF EXISTS demand_matches_shared_material_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_matches DROP CONSTRAINT IF EXISTS demand_matches_inventory_item_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_matches DROP CONSTRAINT IF EXISTS demand_matches_demand_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_history DROP CONSTRAINT IF EXISTS demand_history_operator_id_fkey;
ALTER TABLE IF EXISTS ONLY public.demand_history DROP CONSTRAINT IF EXISTS demand_history_demand_id_fkey;
ALTER TABLE IF EXISTS ONLY public.data_access_logs DROP CONSTRAINT IF EXISTS data_access_logs_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.company_data_scopes DROP CONSTRAINT IF EXISTS company_data_scopes_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.company_data_scopes DROP CONSTRAINT IF EXISTS company_data_scopes_granted_by_fkey;
ALTER TABLE IF EXISTS ONLY public.certificates DROP CONSTRAINT IF EXISTS certificates_material_id_fkey;
ALTER TABLE IF EXISTS ONLY public.aog_cases DROP CONSTRAINT IF EXISTS aog_cases_customer_id_fkey;
ALTER TABLE IF EXISTS ONLY public.aog_cases DROP CONSTRAINT IF EXISTS aog_cases_assigned_team_id_fkey;
ALTER TABLE IF EXISTS ONLY public.workflow_tasks DROP CONSTRAINT IF EXISTS workflow_tasks_pkey;
ALTER TABLE IF EXISTS ONLY public.workflow_instances DROP CONSTRAINT IF EXISTS workflow_instances_pkey;
ALTER TABLE IF EXISTS ONLY public.work_orders DROP CONSTRAINT IF EXISTS work_orders_work_order_number_key;
ALTER TABLE IF EXISTS ONLY public.work_orders DROP CONSTRAINT IF EXISTS work_orders_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_username_key;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_email_key;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_pkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS uk_user_permission;
ALTER TABLE IF EXISTS ONLY public.company_data_scopes DROP CONSTRAINT IF EXISTS uk_user_company_scope;
ALTER TABLE IF EXISTS ONLY public.shared_materials DROP CONSTRAINT IF EXISTS shared_materials_pkey;
ALTER TABLE IF EXISTS ONLY public.permissions DROP CONSTRAINT IF EXISTS permissions_pkey;
ALTER TABLE IF EXISTS ONLY public.permissions DROP CONSTRAINT IF EXISTS permissions_code_key;
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS orders_pkey;
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS orders_order_number_key;
ALTER TABLE IF EXISTS ONLY public.order_status_history DROP CONSTRAINT IF EXISTS order_status_history_pkey;
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS order_items_pkey;
ALTER TABLE IF EXISTS ONLY public.order_attachments DROP CONSTRAINT IF EXISTS order_attachments_pkey;
ALTER TABLE IF EXISTS ONLY public.order_approvals DROP CONSTRAINT IF EXISTS order_approvals_pkey;
ALTER TABLE IF EXISTS ONLY public.notifications DROP CONSTRAINT IF EXISTS notifications_pkey;
ALTER TABLE IF EXISTS ONLY public.materials DROP CONSTRAINT IF EXISTS materials_pkey;
ALTER TABLE IF EXISTS ONLY public.materials DROP CONSTRAINT IF EXISTS materials_part_number_key;
ALTER TABLE IF EXISTS ONLY public.logistics_info DROP CONSTRAINT IF EXISTS logistics_info_pkey;
ALTER TABLE IF EXISTS ONLY public.labor_records DROP CONSTRAINT IF EXISTS labor_records_pkey;
ALTER TABLE IF EXISTS ONLY public.inventory_items DROP CONSTRAINT IF EXISTS inventory_items_pkey;
ALTER TABLE IF EXISTS ONLY public.demands DROP CONSTRAINT IF EXISTS demands_pkey;
ALTER TABLE IF EXISTS ONLY public.demands DROP CONSTRAINT IF EXISTS demands_demand_number_key;
ALTER TABLE IF EXISTS ONLY public.demand_responses DROP CONSTRAINT IF EXISTS demand_responses_pkey;
ALTER TABLE IF EXISTS ONLY public.demand_matches DROP CONSTRAINT IF EXISTS demand_matches_pkey;
ALTER TABLE IF EXISTS ONLY public.demand_history DROP CONSTRAINT IF EXISTS demand_history_pkey;
ALTER TABLE IF EXISTS ONLY public.data_access_logs DROP CONSTRAINT IF EXISTS data_access_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.company_data_scopes DROP CONSTRAINT IF EXISTS company_data_scopes_pkey;
ALTER TABLE IF EXISTS ONLY public.certificates DROP CONSTRAINT IF EXISTS certificates_pkey;
ALTER TABLE IF EXISTS ONLY public.certificates DROP CONSTRAINT IF EXISTS certificates_certificate_number_key;
ALTER TABLE IF EXISTS ONLY public.aog_cases DROP CONSTRAINT IF EXISTS aog_cases_pkey;
ALTER TABLE IF EXISTS ONLY public.aog_cases DROP CONSTRAINT IF EXISTS aog_cases_case_number_key;
ALTER TABLE IF EXISTS public.workflow_tasks ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.workflow_instances ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.work_orders ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.users ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.user_permissions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.shared_materials ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.permissions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.orders ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.order_status_history ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.order_items ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.order_attachments ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.order_approvals ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.notifications ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.materials ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.logistics_info ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.labor_records ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.inventory_items ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.demands ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.demand_responses ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.demand_matches ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.demand_history ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.data_access_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.company_data_scopes ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.certificates ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.aog_cases ALTER COLUMN id DROP DEFAULT;
DROP SEQUENCE IF EXISTS public.workflow_tasks_id_seq;
DROP TABLE IF EXISTS public.workflow_tasks;
DROP SEQUENCE IF EXISTS public.workflow_instances_id_seq;
DROP TABLE IF EXISTS public.workflow_instances;
DROP SEQUENCE IF EXISTS public.work_orders_id_seq;
DROP TABLE IF EXISTS public.work_orders;
DROP SEQUENCE IF EXISTS public.users_id_seq;
DROP TABLE IF EXISTS public.users;
DROP SEQUENCE IF EXISTS public.user_permissions_id_seq;
DROP TABLE IF EXISTS public.user_permissions;
DROP SEQUENCE IF EXISTS public.shared_materials_id_seq;
DROP TABLE IF EXISTS public.shared_materials;
DROP SEQUENCE IF EXISTS public.permissions_id_seq;
DROP TABLE IF EXISTS public.permissions;
DROP SEQUENCE IF EXISTS public.orders_id_seq;
DROP TABLE IF EXISTS public.orders;
DROP SEQUENCE IF EXISTS public.order_status_history_id_seq;
DROP TABLE IF EXISTS public.order_status_history;
DROP SEQUENCE IF EXISTS public.order_items_id_seq;
DROP TABLE IF EXISTS public.order_items;
DROP SEQUENCE IF EXISTS public.order_attachments_id_seq;
DROP TABLE IF EXISTS public.order_attachments;
DROP SEQUENCE IF EXISTS public.order_approvals_id_seq;
DROP TABLE IF EXISTS public.order_approvals;
DROP SEQUENCE IF EXISTS public.notifications_id_seq;
DROP TABLE IF EXISTS public.notifications;
DROP SEQUENCE IF EXISTS public.materials_id_seq;
DROP TABLE IF EXISTS public.materials;
DROP SEQUENCE IF EXISTS public.logistics_info_id_seq;
DROP TABLE IF EXISTS public.logistics_info;
DROP SEQUENCE IF EXISTS public.labor_records_id_seq;
DROP TABLE IF EXISTS public.labor_records;
DROP SEQUENCE IF EXISTS public.inventory_items_id_seq;
DROP TABLE IF EXISTS public.inventory_items;
DROP SEQUENCE IF EXISTS public.demands_id_seq;
DROP TABLE IF EXISTS public.demands;
DROP SEQUENCE IF EXISTS public.demand_responses_id_seq;
DROP TABLE IF EXISTS public.demand_responses;
DROP SEQUENCE IF EXISTS public.demand_matches_id_seq;
DROP TABLE IF EXISTS public.demand_matches;
DROP SEQUENCE IF EXISTS public.demand_history_id_seq;
DROP TABLE IF EXISTS public.demand_history;
DROP SEQUENCE IF EXISTS public.data_access_logs_id_seq;
DROP TABLE IF EXISTS public.data_access_logs;
DROP SEQUENCE IF EXISTS public.company_data_scopes_id_seq;
DROP TABLE IF EXISTS public.company_data_scopes;
DROP SEQUENCE IF EXISTS public.certificates_id_seq;
DROP TABLE IF EXISTS public.certificates;
DROP SEQUENCE IF EXISTS public.aog_cases_id_seq;
DROP TABLE IF EXISTS public.aog_cases;
DROP TYPE IF EXISTS public.workflow_status;
DROP TYPE IF EXISTS public.work_order_status;
DROP TYPE IF EXISTS public.work_order_priority;
DROP TYPE IF EXISTS public.user_types;
DROP TYPE IF EXISTS public.user_status;
DROP TYPE IF EXISTS public.task_status;
DROP TYPE IF EXISTS public.sharing_policies;
DROP TYPE IF EXISTS public.shared_material_status;
DROP TYPE IF EXISTS public.shared_condition_codes;
DROP TYPE IF EXISTS public.share_types;
DROP TYPE IF EXISTS public.response_status;
DROP TYPE IF EXISTS public.order_status;
DROP TYPE IF EXISTS public.order_priority;
DROP TYPE IF EXISTS public.notification_types;
DROP TYPE IF EXISTS public.notification_priority;
DROP TYPE IF EXISTS public.match_status;
DROP TYPE IF EXISTS public.logistics_status;
DROP TYPE IF EXISTS public.inventory_status;
DROP TYPE IF EXISTS public.inventory_condition_codes;
DROP TYPE IF EXISTS public.demand_types;
DROP TYPE IF EXISTS public.demand_status;
DROP TYPE IF EXISTS public.demand_priority;
DROP TYPE IF EXISTS public.demand_actions;
DROP TYPE IF EXISTS public.company_types;
DROP TYPE IF EXISTS public.certificate_types;
DROP TYPE IF EXISTS public.certificate_status;
DROP TYPE IF EXISTS public.approval_status;
DROP TYPE IF EXISTS public.aog_status;
DROP TYPE IF EXISTS public.aog_priority;
DROP TYPE IF EXISTS public.access_types;
--
-- TOC entry 970 (class 1247 OID 16676)
-- Name: access_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.access_types AS ENUM (
    'read',
    'write',
    'admin'
);


ALTER TYPE public.access_types OWNER TO cassdemo_user;

--
-- TOC entry 925 (class 1247 OID 16518)
-- Name: aog_priority; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.aog_priority AS ENUM (
    'critical',
    'high',
    'medium'
);


ALTER TYPE public.aog_priority OWNER TO cassdemo_user;

--
-- TOC entry 928 (class 1247 OID 16526)
-- Name: aog_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.aog_status AS ENUM (
    'new',
    'responding',
    'in_progress',
    'waiting_parts',
    'resolved'
);


ALTER TYPE public.aog_status OWNER TO cassdemo_user;

--
-- TOC entry 910 (class 1247 OID 16468)
-- Name: approval_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.approval_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);


ALTER TYPE public.approval_status OWNER TO cassdemo_user;

--
-- TOC entry 916 (class 1247 OID 16486)
-- Name: certificate_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.certificate_status AS ENUM (
    'valid',
    'expiring',
    'expired',
    'suspended'
);


ALTER TYPE public.certificate_status OWNER TO cassdemo_user;

--
-- TOC entry 913 (class 1247 OID 16476)
-- Name: certificate_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.certificate_types AS ENUM (
    '8130-3',
    'caac',
    'faa',
    'easa'
);


ALTER TYPE public.certificate_types OWNER TO cassdemo_user;

--
-- TOC entry 892 (class 1247 OID 16408)
-- Name: company_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.company_types AS ENUM (
    'internal',
    'external'
);


ALTER TYPE public.company_types OWNER TO cassdemo_user;

--
-- TOC entry 976 (class 1247 OID 16694)
-- Name: demand_actions; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.demand_actions AS ENUM (
    'created',
    'updated',
    'matched',
    'responded',
    'selected',
    'cancelled',
    'completed',
    'expired'
);


ALTER TYPE public.demand_actions OWNER TO cassdemo_user;

--
-- TOC entry 934 (class 1247 OID 16548)
-- Name: demand_priority; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.demand_priority AS ENUM (
    'aog',
    'high',
    'normal',
    'low'
);


ALTER TYPE public.demand_priority OWNER TO cassdemo_user;

--
-- TOC entry 937 (class 1247 OID 16558)
-- Name: demand_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.demand_status AS ENUM (
    'published',
    'matched',
    'negotiating',
    'confirmed',
    'cancelled'
);


ALTER TYPE public.demand_status OWNER TO cassdemo_user;

--
-- TOC entry 931 (class 1247 OID 16538)
-- Name: demand_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.demand_types AS ENUM (
    'turnaround',
    'consumable',
    'maintenance',
    'aog'
);


ALTER TYPE public.demand_types OWNER TO cassdemo_user;

--
-- TOC entry 898 (class 1247 OID 16422)
-- Name: inventory_condition_codes; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.inventory_condition_codes AS ENUM (
    'NE',
    'NS',
    'OH',
    'SV',
    'AR'
);


ALTER TYPE public.inventory_condition_codes OWNER TO cassdemo_user;

--
-- TOC entry 901 (class 1247 OID 16434)
-- Name: inventory_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.inventory_status AS ENUM (
    'normal',
    'warning',
    'shortage',
    'expired'
);


ALTER TYPE public.inventory_status OWNER TO cassdemo_user;

--
-- TOC entry 943 (class 1247 OID 16580)
-- Name: logistics_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.logistics_status AS ENUM (
    'preparing',
    'shipped',
    'in_transit',
    'arrived',
    'delivered',
    'exception'
);


ALTER TYPE public.logistics_status OWNER TO cassdemo_user;

--
-- TOC entry 973 (class 1247 OID 16684)
-- Name: match_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.match_status AS ENUM (
    'active',
    'responded',
    'selected',
    'expired'
);


ALTER TYPE public.match_status OWNER TO cassdemo_user;

--
-- TOC entry 949 (class 1247 OID 16608)
-- Name: notification_priority; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.notification_priority AS ENUM (
    'low',
    'normal',
    'high',
    'urgent'
);


ALTER TYPE public.notification_priority OWNER TO cassdemo_user;

--
-- TOC entry 946 (class 1247 OID 16594)
-- Name: notification_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.notification_types AS ENUM (
    'order',
    'aog',
    'logistics',
    'quality',
    'supplier',
    'system'
);


ALTER TYPE public.notification_types OWNER TO cassdemo_user;

--
-- TOC entry 907 (class 1247 OID 16458)
-- Name: order_priority; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.order_priority AS ENUM (
    'aog',
    'high',
    'normal',
    'low'
);


ALTER TYPE public.order_priority OWNER TO cassdemo_user;

--
-- TOC entry 904 (class 1247 OID 16444)
-- Name: order_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.order_status AS ENUM (
    'pending',
    'confirmed',
    'processing',
    'shipping',
    'completed',
    'cancelled'
);


ALTER TYPE public.order_status OWNER TO cassdemo_user;

--
-- TOC entry 940 (class 1247 OID 16570)
-- Name: response_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.response_status AS ENUM (
    'pending',
    'accepted',
    'rejected',
    'expired'
);


ALTER TYPE public.response_status OWNER TO cassdemo_user;

--
-- TOC entry 958 (class 1247 OID 16636)
-- Name: share_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.share_types AS ENUM (
    'sale',
    'lease',
    'exchange',
    'loan'
);


ALTER TYPE public.share_types OWNER TO cassdemo_user;

--
-- TOC entry 964 (class 1247 OID 16654)
-- Name: shared_condition_codes; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.shared_condition_codes AS ENUM (
    'NE',
    'NS',
    'OH',
    'SV',
    'AR'
);


ALTER TYPE public.shared_condition_codes OWNER TO cassdemo_user;

--
-- TOC entry 967 (class 1247 OID 16666)
-- Name: shared_material_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.shared_material_status AS ENUM (
    'pending_review',
    'approved',
    'rejected',
    'offline'
);


ALTER TYPE public.shared_material_status OWNER TO cassdemo_user;

--
-- TOC entry 961 (class 1247 OID 16646)
-- Name: sharing_policies; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.sharing_policies AS ENUM (
    'immediate',
    'approval',
    'inquiry'
);


ALTER TYPE public.sharing_policies OWNER TO cassdemo_user;

--
-- TOC entry 955 (class 1247 OID 16628)
-- Name: task_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.task_status AS ENUM (
    'READY',
    'COMPLETED',
    'CANCELLED'
);


ALTER TYPE public.task_status OWNER TO cassdemo_user;

--
-- TOC entry 895 (class 1247 OID 16414)
-- Name: user_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.user_status AS ENUM (
    'active',
    'inactive',
    'suspended'
);


ALTER TYPE public.user_status OWNER TO cassdemo_user;

--
-- TOC entry 889 (class 1247 OID 16391)
-- Name: user_types; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.user_types AS ENUM (
    'airline_buyer',
    'platform_staff',
    'maintenance_engineer',
    'logistics_specialist',
    'admin',
    'airline',
    'supplier',
    'maintenance'
);


ALTER TYPE public.user_types OWNER TO cassdemo_user;

--
-- TOC entry 919 (class 1247 OID 16496)
-- Name: work_order_priority; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.work_order_priority AS ENUM (
    'aog',
    'high',
    'normal',
    'low'
);


ALTER TYPE public.work_order_priority OWNER TO cassdemo_user;

--
-- TOC entry 922 (class 1247 OID 16506)
-- Name: work_order_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.work_order_status AS ENUM (
    'new',
    'in_progress',
    'waiting_parts',
    'completed',
    'cancelled'
);


ALTER TYPE public.work_order_status OWNER TO cassdemo_user;

--
-- TOC entry 952 (class 1247 OID 16618)
-- Name: workflow_status; Type: TYPE; Schema: public; Owner: cassdemo_user
--

CREATE TYPE public.workflow_status AS ENUM (
    'RUNNING',
    'COMPLETED',
    'CANCELLED',
    'ERROR'
);


ALTER TYPE public.workflow_status OWNER TO cassdemo_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 230 (class 1259 OID 16815)
-- Name: aog_cases; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.aog_cases (
    id integer NOT NULL,
    case_number character varying(50) NOT NULL,
    aircraft_tail character varying(20) NOT NULL,
    aircraft_type character varying(50) NOT NULL,
    location character varying(100) NOT NULL,
    priority public.aog_priority,
    status public.aog_status,
    fault_title character varying(200) NOT NULL,
    fault_description text,
    customer_id integer NOT NULL,
    assigned_team_id integer,
    contact_name character varying(100),
    contact_phone character varying(20),
    contact_email character varying(100),
    response_time timestamp without time zone,
    estimated_resolution timestamp without time zone,
    resolution_time timestamp without time zone,
    resolution_notes text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.aog_cases OWNER TO cassdemo_user;

--
-- TOC entry 4195 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.id IS 'AOG案例ID';


--
-- TOC entry 4196 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.case_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.case_number IS '案例编号';


--
-- TOC entry 4197 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.aircraft_tail; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.aircraft_tail IS '飞机尾号';


--
-- TOC entry 4198 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.aircraft_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.aircraft_type IS '机型';


--
-- TOC entry 4199 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.location; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.location IS '位置';


--
-- TOC entry 4200 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.priority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.priority IS '优先级';


--
-- TOC entry 4201 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.status IS '状态';


--
-- TOC entry 4202 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.fault_title; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.fault_title IS '故障标题';


--
-- TOC entry 4203 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.fault_description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.fault_description IS '故障描述';


--
-- TOC entry 4204 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.customer_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.customer_id IS '客户ID';


--
-- TOC entry 4205 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.assigned_team_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.assigned_team_id IS '分配团队ID';


--
-- TOC entry 4206 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.contact_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.contact_name IS '联系人姓名';


--
-- TOC entry 4207 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.contact_phone; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.contact_phone IS '联系电话';


--
-- TOC entry 4208 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.contact_email; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.contact_email IS '联系邮箱';


--
-- TOC entry 4209 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.response_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.response_time IS '响应时间';


--
-- TOC entry 4210 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.estimated_resolution; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.estimated_resolution IS '预计解决时间';


--
-- TOC entry 4211 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.resolution_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.resolution_time IS '解决时间';


--
-- TOC entry 4212 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.resolution_notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.resolution_notes IS '解决方案备注';


--
-- TOC entry 4213 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.created_at IS '创建时间';


--
-- TOC entry 4214 (class 0 OID 0)
-- Dependencies: 230
-- Name: COLUMN aog_cases.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.aog_cases.updated_at IS '更新时间';


--
-- TOC entry 229 (class 1259 OID 16814)
-- Name: aog_cases_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.aog_cases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.aog_cases_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4215 (class 0 OID 0)
-- Dependencies: 229
-- Name: aog_cases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.aog_cases_id_seq OWNED BY public.aog_cases.id;


--
-- TOC entry 226 (class 1259 OID 16783)
-- Name: certificates; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.certificates (
    id integer NOT NULL,
    certificate_number character varying(100) NOT NULL,
    material_id integer NOT NULL,
    certificate_type public.certificate_types NOT NULL,
    issuing_authority character varying(100) NOT NULL,
    issue_date date NOT NULL,
    expiry_date date,
    status public.certificate_status,
    document_path character varying(255),
    verification_status boolean,
    notes text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.certificates OWNER TO cassdemo_user;

--
-- TOC entry 4216 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.id IS '证书ID';


--
-- TOC entry 4217 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.certificate_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.certificate_number IS '证书编号';


--
-- TOC entry 4218 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.material_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.material_id IS '航材ID';


--
-- TOC entry 4219 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.certificate_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.certificate_type IS '证书类型';


--
-- TOC entry 4220 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.issuing_authority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.issuing_authority IS '颁发机构';


--
-- TOC entry 4221 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.issue_date; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.issue_date IS '颁发日期';


--
-- TOC entry 4222 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.expiry_date; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.expiry_date IS '到期日期';


--
-- TOC entry 4223 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.status IS '证书状态';


--
-- TOC entry 4224 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.document_path; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.document_path IS '证书文件路径';


--
-- TOC entry 4225 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.verification_status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.verification_status IS '验证状态';


--
-- TOC entry 4226 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.notes IS '备注';


--
-- TOC entry 4227 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.created_at IS '创建时间';


--
-- TOC entry 4228 (class 0 OID 0)
-- Dependencies: 226
-- Name: COLUMN certificates.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.certificates.updated_at IS '更新时间';


--
-- TOC entry 225 (class 1259 OID 16782)
-- Name: certificates_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.certificates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.certificates_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4229 (class 0 OID 0)
-- Dependencies: 225
-- Name: certificates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.certificates_id_seq OWNED BY public.certificates.id;


--
-- TOC entry 242 (class 1259 OID 16918)
-- Name: company_data_scopes; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.company_data_scopes (
    id integer NOT NULL,
    user_id integer NOT NULL,
    company_name character varying(100) NOT NULL,
    access_type public.access_types,
    granted_by integer,
    granted_at timestamp without time zone,
    expires_at timestamp without time zone,
    is_active boolean
);


ALTER TABLE public.company_data_scopes OWNER TO cassdemo_user;

--
-- TOC entry 4230 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.id IS '记录ID';


--
-- TOC entry 4231 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.user_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.user_id IS '用户ID';


--
-- TOC entry 4232 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.company_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.company_name IS '公司名称';


--
-- TOC entry 4233 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.access_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.access_type IS '访问类型';


--
-- TOC entry 4234 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.granted_by; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.granted_by IS '授权人ID';


--
-- TOC entry 4235 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.granted_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.granted_at IS '授权时间';


--
-- TOC entry 4236 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.expires_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.expires_at IS '过期时间';


--
-- TOC entry 4237 (class 0 OID 0)
-- Dependencies: 242
-- Name: COLUMN company_data_scopes.is_active; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.company_data_scopes.is_active IS '是否激活';


--
-- TOC entry 241 (class 1259 OID 16917)
-- Name: company_data_scopes_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.company_data_scopes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_data_scopes_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4238 (class 0 OID 0)
-- Dependencies: 241
-- Name: company_data_scopes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.company_data_scopes_id_seq OWNED BY public.company_data_scopes.id;


--
-- TOC entry 240 (class 1259 OID 16904)
-- Name: data_access_logs; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.data_access_logs (
    id integer NOT NULL,
    user_id integer NOT NULL,
    resource_type character varying(50) NOT NULL,
    resource_id character varying(50),
    action character varying(20) NOT NULL,
    ip_address character varying(45),
    user_agent character varying(255),
    success boolean,
    error_message character varying(255),
    additional_data text,
    created_at timestamp without time zone
);


ALTER TABLE public.data_access_logs OWNER TO cassdemo_user;

--
-- TOC entry 4239 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.id IS '日志ID';


--
-- TOC entry 4240 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.user_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.user_id IS '用户ID';


--
-- TOC entry 4241 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.resource_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.resource_type IS '资源类型';


--
-- TOC entry 4242 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.resource_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.resource_id IS '资源ID';


--
-- TOC entry 4243 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.action; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.action IS '操作类型';


--
-- TOC entry 4244 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.ip_address; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.ip_address IS 'IP地址';


--
-- TOC entry 4245 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.user_agent; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.user_agent IS '用户代理';


--
-- TOC entry 4246 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.success; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.success IS '是否成功';


--
-- TOC entry 4247 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.error_message; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.error_message IS '错误信息';


--
-- TOC entry 4248 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.additional_data; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.additional_data IS '附加数据(JSON)';


--
-- TOC entry 4249 (class 0 OID 0)
-- Dependencies: 240
-- Name: COLUMN data_access_logs.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.data_access_logs.created_at IS '创建时间';


--
-- TOC entry 239 (class 1259 OID 16903)
-- Name: data_access_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.data_access_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_access_logs_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4250 (class 0 OID 0)
-- Dependencies: 239
-- Name: data_access_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.data_access_logs_id_seq OWNED BY public.data_access_logs.id;


--
-- TOC entry 262 (class 1259 OID 17113)
-- Name: demand_history; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.demand_history (
    id integer NOT NULL,
    demand_id integer NOT NULL,
    operator_id integer NOT NULL,
    action public.demand_actions NOT NULL,
    description character varying(255) NOT NULL,
    details text,
    old_status character varying(50),
    new_status character varying(50),
    related_object_type character varying(50),
    related_object_id integer,
    created_at timestamp without time zone
);


ALTER TABLE public.demand_history OWNER TO cassdemo_user;

--
-- TOC entry 4251 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.id IS '历史记录ID';


--
-- TOC entry 4252 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.demand_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.demand_id IS '需求ID';


--
-- TOC entry 4253 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.operator_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.operator_id IS '操作人ID';


--
-- TOC entry 4254 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.action; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.action IS '操作类型';


--
-- TOC entry 4255 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.description IS '操作描述';


--
-- TOC entry 4256 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.details; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.details IS '详细信息(JSON)';


--
-- TOC entry 4257 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.old_status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.old_status IS '变更前状态';


--
-- TOC entry 4258 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.new_status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.new_status IS '变更后状态';


--
-- TOC entry 4259 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.related_object_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.related_object_type IS '关联对象类型';


--
-- TOC entry 4260 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.related_object_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.related_object_id IS '关联对象ID';


--
-- TOC entry 4261 (class 0 OID 0)
-- Dependencies: 262
-- Name: COLUMN demand_history.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_history.created_at IS '创建时间';


--
-- TOC entry 261 (class 1259 OID 17112)
-- Name: demand_history_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.demand_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.demand_history_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4262 (class 0 OID 0)
-- Dependencies: 261
-- Name: demand_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.demand_history_id_seq OWNED BY public.demand_history.id;


--
-- TOC entry 264 (class 1259 OID 17132)
-- Name: demand_matches; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.demand_matches (
    id integer NOT NULL,
    demand_id integer NOT NULL,
    supplier_id integer NOT NULL,
    inventory_item_id integer,
    shared_material_id integer,
    match_score numeric(5,2) NOT NULL,
    part_number_match boolean,
    substitute_match boolean,
    location_distance numeric(8,2),
    price_estimate numeric(12,2),
    available_quantity integer,
    condition_code character varying(10),
    estimated_delivery_days integer,
    supplier_rating numeric(3,2),
    supplier_certification character varying(100),
    status public.match_status,
    matched_at timestamp without time zone,
    expires_at timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.demand_matches OWNER TO cassdemo_user;

--
-- TOC entry 4263 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.id IS '匹配ID';


--
-- TOC entry 4264 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.demand_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.demand_id IS '需求ID';


--
-- TOC entry 4265 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.supplier_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.supplier_id IS '供应商ID';


--
-- TOC entry 4266 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.inventory_item_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.inventory_item_id IS '库存项ID';


--
-- TOC entry 4267 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.shared_material_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.shared_material_id IS '共享件ID';


--
-- TOC entry 4268 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.match_score; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.match_score IS '匹配分数(0-100)';


--
-- TOC entry 4269 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.part_number_match; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.part_number_match IS '零件号是否完全匹配';


--
-- TOC entry 4270 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.substitute_match; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.substitute_match IS '是否为替代件匹配';


--
-- TOC entry 4271 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.location_distance; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.location_distance IS '地理距离(km)';


--
-- TOC entry 4272 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.price_estimate; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.price_estimate IS '预估价格';


--
-- TOC entry 4273 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.available_quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.available_quantity IS '可用数量';


--
-- TOC entry 4274 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.condition_code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.condition_code IS '状况代码';


--
-- TOC entry 4275 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.estimated_delivery_days; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.estimated_delivery_days IS '预计交货天数';


--
-- TOC entry 4276 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.supplier_rating; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.supplier_rating IS '供应商评级';


--
-- TOC entry 4277 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.supplier_certification; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.supplier_certification IS '供应商认证';


--
-- TOC entry 4278 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.status IS '匹配状态';


--
-- TOC entry 4279 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.matched_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.matched_at IS '匹配时间';


--
-- TOC entry 4280 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.expires_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.expires_at IS '匹配过期时间';


--
-- TOC entry 4281 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.created_at IS '创建时间';


--
-- TOC entry 4282 (class 0 OID 0)
-- Dependencies: 264
-- Name: COLUMN demand_matches.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_matches.updated_at IS '更新时间';


--
-- TOC entry 263 (class 1259 OID 17131)
-- Name: demand_matches_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.demand_matches_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.demand_matches_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4283 (class 0 OID 0)
-- Dependencies: 263
-- Name: demand_matches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.demand_matches_id_seq OWNED BY public.demand_matches.id;


--
-- TOC entry 254 (class 1259 OID 17032)
-- Name: demand_responses; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.demand_responses (
    id integer NOT NULL,
    demand_id integer NOT NULL,
    supplier_id integer NOT NULL,
    status public.response_status,
    unit_price numeric(12,2),
    total_price numeric(12,2),
    delivery_time timestamp without time zone,
    availability integer,
    condition_code character varying(10),
    certificate_info text,
    message text,
    valid_until timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.demand_responses OWNER TO cassdemo_user;

--
-- TOC entry 4284 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.id IS '响应ID';


--
-- TOC entry 4285 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.demand_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.demand_id IS '需求ID';


--
-- TOC entry 4286 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.supplier_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.supplier_id IS '供应商ID';


--
-- TOC entry 4287 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.status IS '响应状态';


--
-- TOC entry 4288 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.unit_price; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.unit_price IS '单价';


--
-- TOC entry 4289 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.total_price; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.total_price IS '总价';


--
-- TOC entry 4290 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.delivery_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.delivery_time IS '交货时间';


--
-- TOC entry 4291 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.availability; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.availability IS '可供应数量';


--
-- TOC entry 4292 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.condition_code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.condition_code IS '状况代码';


--
-- TOC entry 4293 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.certificate_info; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.certificate_info IS '证书信息';


--
-- TOC entry 4294 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.message; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.message IS '响应消息';


--
-- TOC entry 4295 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.valid_until; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.valid_until IS '报价有效期';


--
-- TOC entry 4296 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.created_at IS '创建时间';


--
-- TOC entry 4297 (class 0 OID 0)
-- Dependencies: 254
-- Name: COLUMN demand_responses.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demand_responses.updated_at IS '更新时间';


--
-- TOC entry 253 (class 1259 OID 17031)
-- Name: demand_responses_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.demand_responses_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.demand_responses_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4298 (class 0 OID 0)
-- Dependencies: 253
-- Name: demand_responses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.demand_responses_id_seq OWNED BY public.demand_responses.id;


--
-- TOC entry 232 (class 1259 OID 16836)
-- Name: demands; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.demands (
    id integer NOT NULL,
    demand_number character varying(50) NOT NULL,
    title character varying(300),
    requester_id integer NOT NULL,
    type public.demand_types NOT NULL,
    priority public.demand_priority,
    status public.demand_status,
    material_name character varying(200) NOT NULL,
    part_number character varying(50),
    aircraft_type character varying(50),
    quantity integer NOT NULL,
    unit character varying(20),
    description text,
    delivery_location character varying(200),
    delivery_time timestamp without time zone,
    budget_range character varying(100),
    quality_requirements text,
    contact_info text,
    expires_at timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.demands OWNER TO cassdemo_user;

--
-- TOC entry 4299 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.id IS '需求ID';


--
-- TOC entry 4300 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.demand_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.demand_number IS '需求编号';


--
-- TOC entry 4301 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.title; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.title IS '需求标题';


--
-- TOC entry 4302 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.requester_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.requester_id IS '需求方ID';


--
-- TOC entry 4303 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.type IS '需求类型';


--
-- TOC entry 4304 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.priority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.priority IS '优先级';


--
-- TOC entry 4305 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.status IS '状态';


--
-- TOC entry 4306 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.material_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.material_name IS '航材名称';


--
-- TOC entry 4307 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.part_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.part_number IS '零件号';


--
-- TOC entry 4308 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.aircraft_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.aircraft_type IS '机型';


--
-- TOC entry 4309 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.quantity IS '需求数量';


--
-- TOC entry 4310 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.unit; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.unit IS '单位';


--
-- TOC entry 4311 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.description IS '需求描述';


--
-- TOC entry 4312 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.delivery_location; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.delivery_location IS '交货地点';


--
-- TOC entry 4313 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.delivery_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.delivery_time IS '交货时间';


--
-- TOC entry 4314 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.budget_range; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.budget_range IS '预算范围';


--
-- TOC entry 4315 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.quality_requirements; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.quality_requirements IS '质量要求';


--
-- TOC entry 4316 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.contact_info; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.contact_info IS '联系信息(JSON)';


--
-- TOC entry 4317 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.expires_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.expires_at IS '需求过期时间';


--
-- TOC entry 4318 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.created_at IS '创建时间';


--
-- TOC entry 4319 (class 0 OID 0)
-- Dependencies: 232
-- Name: COLUMN demands.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.demands.updated_at IS '更新时间';


--
-- TOC entry 231 (class 1259 OID 16835)
-- Name: demands_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.demands_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.demands_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4320 (class 0 OID 0)
-- Dependencies: 231
-- Name: demands_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.demands_id_seq OWNED BY public.demands.id;


--
-- TOC entry 222 (class 1259 OID 16745)
-- Name: inventory_items; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.inventory_items (
    id integer NOT NULL,
    material_id integer NOT NULL,
    location character varying(100) NOT NULL,
    current_stock integer,
    safety_stock integer,
    unit_price numeric(12,2),
    condition_code public.inventory_condition_codes,
    is_shareable boolean,
    status public.inventory_status,
    batch_number character varying(50),
    expiry_date date,
    supplier_id integer,
    last_updated timestamp without time zone,
    created_at timestamp without time zone
);


ALTER TABLE public.inventory_items OWNER TO cassdemo_user;

--
-- TOC entry 4321 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.id IS '库存ID';


--
-- TOC entry 4322 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.material_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.material_id IS '航材ID';


--
-- TOC entry 4323 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.location; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.location IS '存储位置';


--
-- TOC entry 4324 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.current_stock; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.current_stock IS '当前库存';


--
-- TOC entry 4325 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.safety_stock; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.safety_stock IS '安全库存';


--
-- TOC entry 4326 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.unit_price; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.unit_price IS '单价';


--
-- TOC entry 4327 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.condition_code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.condition_code IS '状况代码';


--
-- TOC entry 4328 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.is_shareable; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.is_shareable IS '是否可共享';


--
-- TOC entry 4329 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.status IS '库存状态';


--
-- TOC entry 4330 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.batch_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.batch_number IS '批次号';


--
-- TOC entry 4331 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.expiry_date; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.expiry_date IS '过期日期';


--
-- TOC entry 4332 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.supplier_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.supplier_id IS '供应商ID';


--
-- TOC entry 4333 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.last_updated; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.last_updated IS '最后更新时间';


--
-- TOC entry 4334 (class 0 OID 0)
-- Dependencies: 222
-- Name: COLUMN inventory_items.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.inventory_items.created_at IS '创建时间';


--
-- TOC entry 221 (class 1259 OID 16744)
-- Name: inventory_items_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.inventory_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.inventory_items_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4335 (class 0 OID 0)
-- Dependencies: 221
-- Name: inventory_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.inventory_items_id_seq OWNED BY public.inventory_items.id;


--
-- TOC entry 252 (class 1259 OID 17013)
-- Name: labor_records; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.labor_records (
    id integer NOT NULL,
    work_order_id integer NOT NULL,
    technician_id integer NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone,
    hours_worked double precision,
    work_description text,
    labor_rate numeric(10,2),
    labor_cost numeric(10,2),
    created_at timestamp without time zone
);


ALTER TABLE public.labor_records OWNER TO cassdemo_user;

--
-- TOC entry 4336 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.id IS '工时记录ID';


--
-- TOC entry 4337 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.work_order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.work_order_id IS '工单ID';


--
-- TOC entry 4338 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.technician_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.technician_id IS '技师ID';


--
-- TOC entry 4339 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.start_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.start_time IS '开始时间';


--
-- TOC entry 4340 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.end_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.end_time IS '结束时间';


--
-- TOC entry 4341 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.hours_worked; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.hours_worked IS '工作小时数';


--
-- TOC entry 4342 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.work_description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.work_description IS '工作描述';


--
-- TOC entry 4343 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.labor_rate; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.labor_rate IS '工时费率';


--
-- TOC entry 4344 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.labor_cost; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.labor_cost IS '工时成本';


--
-- TOC entry 4345 (class 0 OID 0)
-- Dependencies: 252
-- Name: COLUMN labor_records.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.labor_records.created_at IS '创建时间';


--
-- TOC entry 251 (class 1259 OID 17012)
-- Name: labor_records_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.labor_records_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.labor_records_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4346 (class 0 OID 0)
-- Dependencies: 251
-- Name: labor_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.labor_records_id_seq OWNED BY public.labor_records.id;


--
-- TOC entry 256 (class 1259 OID 17051)
-- Name: logistics_info; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.logistics_info (
    id integer NOT NULL,
    order_id integer NOT NULL,
    tracking_number character varying(100),
    carrier character varying(100),
    status public.logistics_status,
    shipped_at timestamp without time zone,
    estimated_arrival timestamp without time zone,
    actual_arrival timestamp without time zone,
    current_location character varying(200),
    delivery_notes text,
    recipient_name character varying(100),
    recipient_phone character varying(20),
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.logistics_info OWNER TO cassdemo_user;

--
-- TOC entry 4347 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.id IS '物流ID';


--
-- TOC entry 4348 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.order_id IS '订单ID';


--
-- TOC entry 4349 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.tracking_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.tracking_number IS '运单号';


--
-- TOC entry 4350 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.carrier; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.carrier IS '承运商';


--
-- TOC entry 4351 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.status IS '物流状态';


--
-- TOC entry 4352 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.shipped_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.shipped_at IS '发货时间';


--
-- TOC entry 4353 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.estimated_arrival; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.estimated_arrival IS '预计到达时间';


--
-- TOC entry 4354 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.actual_arrival; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.actual_arrival IS '实际到达时间';


--
-- TOC entry 4355 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.current_location; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.current_location IS '当前位置';


--
-- TOC entry 4356 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.delivery_notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.delivery_notes IS '配送备注';


--
-- TOC entry 4357 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.recipient_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.recipient_name IS '收货人姓名';


--
-- TOC entry 4358 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.recipient_phone; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.recipient_phone IS '收货人电话';


--
-- TOC entry 4359 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.created_at IS '创建时间';


--
-- TOC entry 4360 (class 0 OID 0)
-- Dependencies: 256
-- Name: COLUMN logistics_info.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.logistics_info.updated_at IS '更新时间';


--
-- TOC entry 255 (class 1259 OID 17050)
-- Name: logistics_info_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.logistics_info_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.logistics_info_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4361 (class 0 OID 0)
-- Dependencies: 255
-- Name: logistics_info_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.logistics_info_id_seq OWNED BY public.logistics_info.id;


--
-- TOC entry 218 (class 1259 OID 16725)
-- Name: materials; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.materials (
    id integer NOT NULL,
    part_number character varying(50) NOT NULL,
    part_name character varying(200) NOT NULL,
    name character varying(200) NOT NULL,
    category character varying(50) NOT NULL,
    manufacturer character varying(100),
    aircraft_type character varying(50),
    model character varying(100),
    description text,
    specifications text,
    unit character varying(20),
    weight double precision,
    shelf_life_months integer,
    image_url character varying(500),
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.materials OWNER TO cassdemo_user;

--
-- TOC entry 4362 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.id IS '航材ID';


--
-- TOC entry 4363 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.part_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.part_number IS '零件号';


--
-- TOC entry 4364 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.part_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.part_name IS '零件名称';


--
-- TOC entry 4365 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.name IS '航材名称';


--
-- TOC entry 4366 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.category; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.category IS '类别';


--
-- TOC entry 4367 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.manufacturer; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.manufacturer IS '制造商';


--
-- TOC entry 4368 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.aircraft_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.aircraft_type IS '适用机型';


--
-- TOC entry 4369 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.model; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.model IS '型号';


--
-- TOC entry 4370 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.description IS '描述';


--
-- TOC entry 4371 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.specifications; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.specifications IS '规格参数(JSON)';


--
-- TOC entry 4372 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.unit; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.unit IS '计量单位';


--
-- TOC entry 4373 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.weight; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.weight IS '重量(kg)';


--
-- TOC entry 4374 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.shelf_life_months; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.shelf_life_months IS '保质期(月)';


--
-- TOC entry 4375 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.image_url; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.image_url IS '图片URL';


--
-- TOC entry 4376 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.created_at IS '创建时间';


--
-- TOC entry 4377 (class 0 OID 0)
-- Dependencies: 218
-- Name: COLUMN materials.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.materials.updated_at IS '更新时间';


--
-- TOC entry 217 (class 1259 OID 16724)
-- Name: materials_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.materials_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.materials_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4378 (class 0 OID 0)
-- Dependencies: 217
-- Name: materials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.materials_id_seq OWNED BY public.materials.id;


--
-- TOC entry 234 (class 1259 OID 16852)
-- Name: notifications; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    user_id integer NOT NULL,
    type public.notification_types NOT NULL,
    title character varying(200) NOT NULL,
    content text,
    priority public.notification_priority,
    is_read boolean,
    read_at timestamp without time zone,
    related_id integer,
    related_type character varying(50),
    expires_at timestamp without time zone,
    created_at timestamp without time zone
);


ALTER TABLE public.notifications OWNER TO cassdemo_user;

--
-- TOC entry 4379 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.id IS '通知ID';


--
-- TOC entry 4380 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.user_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.user_id IS '用户ID';


--
-- TOC entry 4381 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.type IS '通知类型';


--
-- TOC entry 4382 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.title; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.title IS '通知标题';


--
-- TOC entry 4383 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.content; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.content IS '通知内容';


--
-- TOC entry 4384 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.priority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.priority IS '优先级';


--
-- TOC entry 4385 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.is_read; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.is_read IS '是否已读';


--
-- TOC entry 4386 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.read_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.read_at IS '阅读时间';


--
-- TOC entry 4387 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.related_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.related_id IS '关联对象ID';


--
-- TOC entry 4388 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.related_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.related_type IS '关联对象类型';


--
-- TOC entry 4389 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.expires_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.expires_at IS '过期时间';


--
-- TOC entry 4390 (class 0 OID 0)
-- Dependencies: 234
-- Name: COLUMN notifications.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.notifications.created_at IS '创建时间';


--
-- TOC entry 233 (class 1259 OID 16851)
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notifications_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4391 (class 0 OID 0)
-- Dependencies: 233
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- TOC entry 248 (class 1259 OID 16975)
-- Name: order_approvals; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.order_approvals (
    id integer NOT NULL,
    order_id integer NOT NULL,
    approver_id integer NOT NULL,
    approval_level integer NOT NULL,
    status public.approval_status,
    comments text,
    approved_at timestamp without time zone,
    created_at timestamp without time zone
);


ALTER TABLE public.order_approvals OWNER TO cassdemo_user;

--
-- TOC entry 4392 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.id IS '审批记录ID';


--
-- TOC entry 4393 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.order_id IS '订单ID';


--
-- TOC entry 4394 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.approver_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.approver_id IS '审批人ID';


--
-- TOC entry 4395 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.approval_level; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.approval_level IS '审批级别';


--
-- TOC entry 4396 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.status IS '审批状态';


--
-- TOC entry 4397 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.comments; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.comments IS '审批意见';


--
-- TOC entry 4398 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.approved_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.approved_at IS '审批时间';


--
-- TOC entry 4399 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN order_approvals.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_approvals.created_at IS '创建时间';


--
-- TOC entry 247 (class 1259 OID 16974)
-- Name: order_approvals_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.order_approvals_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.order_approvals_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4400 (class 0 OID 0)
-- Dependencies: 247
-- Name: order_approvals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.order_approvals_id_seq OWNED BY public.order_approvals.id;


--
-- TOC entry 250 (class 1259 OID 16994)
-- Name: order_attachments; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.order_attachments (
    id integer NOT NULL,
    order_id integer NOT NULL,
    filename character varying(255) NOT NULL,
    original_filename character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size integer,
    file_type character varying(50),
    uploaded_by integer NOT NULL,
    description text,
    created_at timestamp without time zone
);


ALTER TABLE public.order_attachments OWNER TO cassdemo_user;

--
-- TOC entry 4401 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.id IS '附件ID';


--
-- TOC entry 4402 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.order_id IS '订单ID';


--
-- TOC entry 4403 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.filename; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.filename IS '文件名';


--
-- TOC entry 4404 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.original_filename; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.original_filename IS '原始文件名';


--
-- TOC entry 4405 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.file_path; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.file_path IS '文件路径';


--
-- TOC entry 4406 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.file_size; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.file_size IS '文件大小(字节)';


--
-- TOC entry 4407 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.file_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.file_type IS '文件类型';


--
-- TOC entry 4408 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.uploaded_by; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.uploaded_by IS '上传人ID';


--
-- TOC entry 4409 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.description IS '文件描述';


--
-- TOC entry 4410 (class 0 OID 0)
-- Dependencies: 250
-- Name: COLUMN order_attachments.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_attachments.created_at IS '上传时间';


--
-- TOC entry 249 (class 1259 OID 16993)
-- Name: order_attachments_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.order_attachments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.order_attachments_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4411 (class 0 OID 0)
-- Dependencies: 249
-- Name: order_attachments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.order_attachments_id_seq OWNED BY public.order_attachments.id;


--
-- TOC entry 244 (class 1259 OID 16937)
-- Name: order_items; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.order_items (
    id integer NOT NULL,
    order_id integer NOT NULL,
    material_id integer NOT NULL,
    quantity integer NOT NULL,
    unit_price numeric(12,2),
    subtotal numeric(12,2),
    condition_code character varying(10),
    delivery_requirement text
);


ALTER TABLE public.order_items OWNER TO cassdemo_user;

--
-- TOC entry 4412 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.id IS '订单项ID';


--
-- TOC entry 4413 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.order_id IS '订单ID';


--
-- TOC entry 4414 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.material_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.material_id IS '航材ID';


--
-- TOC entry 4415 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.quantity IS '数量';


--
-- TOC entry 4416 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.unit_price; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.unit_price IS '单价';


--
-- TOC entry 4417 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.subtotal; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.subtotal IS '小计';


--
-- TOC entry 4418 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.condition_code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.condition_code IS '状况代码';


--
-- TOC entry 4419 (class 0 OID 0)
-- Dependencies: 244
-- Name: COLUMN order_items.delivery_requirement; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_items.delivery_requirement IS '交货要求';


--
-- TOC entry 243 (class 1259 OID 16936)
-- Name: order_items_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.order_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.order_items_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4420 (class 0 OID 0)
-- Dependencies: 243
-- Name: order_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.order_items_id_seq OWNED BY public.order_items.id;


--
-- TOC entry 246 (class 1259 OID 16956)
-- Name: order_status_history; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.order_status_history (
    id integer NOT NULL,
    order_id integer NOT NULL,
    from_status character varying(20),
    to_status character varying(20) NOT NULL,
    operator_id integer,
    reason text,
    notes text,
    created_at timestamp without time zone
);


ALTER TABLE public.order_status_history OWNER TO cassdemo_user;

--
-- TOC entry 4421 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.id IS '历史记录ID';


--
-- TOC entry 4422 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.order_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.order_id IS '订单ID';


--
-- TOC entry 4423 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.from_status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.from_status IS '原状态';


--
-- TOC entry 4424 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.to_status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.to_status IS '新状态';


--
-- TOC entry 4425 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.operator_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.operator_id IS '操作人ID';


--
-- TOC entry 4426 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.reason; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.reason IS '状态变更原因';


--
-- TOC entry 4427 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.notes IS '备注';


--
-- TOC entry 4428 (class 0 OID 0)
-- Dependencies: 246
-- Name: COLUMN order_status_history.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.order_status_history.created_at IS '创建时间';


--
-- TOC entry 245 (class 1259 OID 16955)
-- Name: order_status_history_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.order_status_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.order_status_history_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4429 (class 0 OID 0)
-- Dependencies: 245
-- Name: order_status_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.order_status_history_id_seq OWNED BY public.order_status_history.id;


--
-- TOC entry 224 (class 1259 OID 16762)
-- Name: orders; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.orders (
    id integer NOT NULL,
    order_number character varying(50) NOT NULL,
    buyer_id integer NOT NULL,
    supplier_id integer,
    status public.order_status,
    priority public.order_priority,
    total_amount numeric(12,2),
    currency character varying(3),
    delivery_address text,
    delivery_date timestamp without time zone,
    notes text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.orders OWNER TO cassdemo_user;

--
-- TOC entry 4430 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.id IS '订单ID';


--
-- TOC entry 4431 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.order_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.order_number IS '订单号';


--
-- TOC entry 4432 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.buyer_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.buyer_id IS '买方ID';


--
-- TOC entry 4433 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.supplier_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.supplier_id IS '供应商ID';


--
-- TOC entry 4434 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.status IS '订单状态';


--
-- TOC entry 4435 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.priority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.priority IS '优先级';


--
-- TOC entry 4436 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.total_amount; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.total_amount IS '订单总金额';


--
-- TOC entry 4437 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.currency; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.currency IS '货币类型';


--
-- TOC entry 4438 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.delivery_address; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.delivery_address IS '交货地址';


--
-- TOC entry 4439 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.delivery_date; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.delivery_date IS '交货日期';


--
-- TOC entry 4440 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.notes IS '备注';


--
-- TOC entry 4441 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.created_at IS '创建时间';


--
-- TOC entry 4442 (class 0 OID 0)
-- Dependencies: 224
-- Name: COLUMN orders.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.orders.updated_at IS '更新时间';


--
-- TOC entry 223 (class 1259 OID 16761)
-- Name: orders_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.orders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.orders_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4443 (class 0 OID 0)
-- Dependencies: 223
-- Name: orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.orders_id_seq OWNED BY public.orders.id;


--
-- TOC entry 220 (class 1259 OID 16736)
-- Name: permissions; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.permissions (
    id integer NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    description character varying(255),
    category character varying(50),
    is_active boolean,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.permissions OWNER TO cassdemo_user;

--
-- TOC entry 4444 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.id IS '权限ID';


--
-- TOC entry 4445 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.code IS '权限代码';


--
-- TOC entry 4446 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.name IS '权限名称';


--
-- TOC entry 4447 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.description IS '权限描述';


--
-- TOC entry 4448 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.category; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.category IS '权限分类';


--
-- TOC entry 4449 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.is_active; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.is_active IS '是否激活';


--
-- TOC entry 4450 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.created_at IS '创建时间';


--
-- TOC entry 4451 (class 0 OID 0)
-- Dependencies: 220
-- Name: COLUMN permissions.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.permissions.updated_at IS '更新时间';


--
-- TOC entry 219 (class 1259 OID 16735)
-- Name: permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.permissions_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4452 (class 0 OID 0)
-- Dependencies: 219
-- Name: permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.permissions_id_seq OWNED BY public.permissions.id;


--
-- TOC entry 260 (class 1259 OID 17079)
-- Name: shared_materials; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.shared_materials (
    id integer NOT NULL,
    material_id integer NOT NULL,
    owner_id integer NOT NULL,
    share_type public.share_types,
    share_quantity integer NOT NULL,
    available_quantity integer NOT NULL,
    reserved_quantity integer,
    price numeric(12,2) NOT NULL,
    min_order_quantity integer,
    description text,
    sharing_policy public.sharing_policies,
    condition_code public.shared_condition_codes,
    location character varying(100),
    expiry_date timestamp without time zone,
    status public.shared_material_status,
    workflow_instance_id integer,
    approved_by integer,
    approved_at timestamp without time zone,
    rejected_by integer,
    rejected_at timestamp without time zone,
    review_comment text,
    view_count integer,
    inquiry_count integer,
    transaction_count integer,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    published_at timestamp without time zone
);


ALTER TABLE public.shared_materials OWNER TO cassdemo_user;

--
-- TOC entry 4453 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.id IS '共享件ID';


--
-- TOC entry 4454 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.material_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.material_id IS '航材ID';


--
-- TOC entry 4455 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.owner_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.owner_id IS '发布人ID';


--
-- TOC entry 4456 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.share_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.share_type IS '共享类型';


--
-- TOC entry 4457 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.share_quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.share_quantity IS '共享数量';


--
-- TOC entry 4458 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.available_quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.available_quantity IS '可用数量';


--
-- TOC entry 4459 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.reserved_quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.reserved_quantity IS '预留数量';


--
-- TOC entry 4460 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.price; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.price IS '单价';


--
-- TOC entry 4461 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.min_order_quantity; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.min_order_quantity IS '最小订购数量';


--
-- TOC entry 4462 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.description IS '描述信息';


--
-- TOC entry 4463 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.sharing_policy; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.sharing_policy IS '共享策略';


--
-- TOC entry 4464 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.condition_code; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.condition_code IS '状况代码';


--
-- TOC entry 4465 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.location; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.location IS '存储位置';


--
-- TOC entry 4466 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.expiry_date; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.expiry_date IS '过期日期';


--
-- TOC entry 4467 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.status IS '审核状态';


--
-- TOC entry 4468 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.workflow_instance_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.workflow_instance_id IS '工作流实例ID';


--
-- TOC entry 4469 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.approved_by; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.approved_by IS '审核人ID';


--
-- TOC entry 4470 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.approved_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.approved_at IS '审核时间';


--
-- TOC entry 4471 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.rejected_by; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.rejected_by IS '拒绝人ID';


--
-- TOC entry 4472 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.rejected_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.rejected_at IS '拒绝时间';


--
-- TOC entry 4473 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.review_comment; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.review_comment IS '审核意见';


--
-- TOC entry 4474 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.view_count; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.view_count IS '查看次数';


--
-- TOC entry 4475 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.inquiry_count; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.inquiry_count IS '询价次数';


--
-- TOC entry 4476 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.transaction_count; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.transaction_count IS '交易次数';


--
-- TOC entry 4477 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.created_at IS '创建时间';


--
-- TOC entry 4478 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.updated_at IS '更新时间';


--
-- TOC entry 4479 (class 0 OID 0)
-- Dependencies: 260
-- Name: COLUMN shared_materials.published_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.shared_materials.published_at IS '发布时间';


--
-- TOC entry 259 (class 1259 OID 17078)
-- Name: shared_materials_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.shared_materials_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.shared_materials_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4480 (class 0 OID 0)
-- Dependencies: 259
-- Name: shared_materials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.shared_materials_id_seq OWNED BY public.shared_materials.id;


--
-- TOC entry 238 (class 1259 OID 16880)
-- Name: user_permissions; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.user_permissions (
    id integer NOT NULL,
    user_id integer NOT NULL,
    permission_id integer NOT NULL,
    granted_by integer,
    granted_at timestamp without time zone,
    expires_at timestamp without time zone,
    is_active boolean
);


ALTER TABLE public.user_permissions OWNER TO cassdemo_user;

--
-- TOC entry 4481 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.id IS '记录ID';


--
-- TOC entry 4482 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.user_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.user_id IS '用户ID';


--
-- TOC entry 4483 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.permission_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.permission_id IS '权限ID';


--
-- TOC entry 4484 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.granted_by; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.granted_by IS '授权人ID';


--
-- TOC entry 4485 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.granted_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.granted_at IS '授权时间';


--
-- TOC entry 4486 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.expires_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.expires_at IS '过期时间';


--
-- TOC entry 4487 (class 0 OID 0)
-- Dependencies: 238
-- Name: COLUMN user_permissions.is_active; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.user_permissions.is_active IS '是否激活';


--
-- TOC entry 237 (class 1259 OID 16879)
-- Name: user_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.user_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_permissions_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4488 (class 0 OID 0)
-- Dependencies: 237
-- Name: user_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.user_permissions_id_seq OWNED BY public.user_permissions.id;


--
-- TOC entry 216 (class 1259 OID 16712)
-- Name: users; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password_hash character varying(255) NOT NULL,
    user_type public.user_types NOT NULL,
    company_type public.company_types,
    permission_level integer,
    company_name character varying(100),
    real_name character varying(50),
    phone character varying(20),
    status public.user_status,
    last_login timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.users OWNER TO cassdemo_user;

--
-- TOC entry 4489 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.id IS '用户ID';


--
-- TOC entry 4490 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.username; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.username IS '用户名';


--
-- TOC entry 4491 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.email; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.email IS '邮箱';


--
-- TOC entry 4492 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.password_hash; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.password_hash IS '密码哈希';


--
-- TOC entry 4493 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.user_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.user_type IS '用户类型';


--
-- TOC entry 4494 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.company_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.company_type IS '公司类型：internal-平台内部员工，external-外部客户';


--
-- TOC entry 4495 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.permission_level; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.permission_level IS '权限等级：1-基础用户，2-高级用户，3-管理员，4-超级管理员';


--
-- TOC entry 4496 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.company_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.company_name IS '公司名称';


--
-- TOC entry 4497 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.real_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.real_name IS '真实姓名';


--
-- TOC entry 4498 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.phone; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.phone IS '联系电话';


--
-- TOC entry 4499 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.status IS '用户状态';


--
-- TOC entry 4500 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.last_login; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.last_login IS '最后登录时间';


--
-- TOC entry 4501 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.created_at IS '创建时间';


--
-- TOC entry 4502 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN users.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.users.updated_at IS '更新时间';


--
-- TOC entry 215 (class 1259 OID 16711)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4503 (class 0 OID 0)
-- Dependencies: 215
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 228 (class 1259 OID 16799)
-- Name: work_orders; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.work_orders (
    id integer NOT NULL,
    work_order_number character varying(50) NOT NULL,
    aircraft_tail character varying(20) NOT NULL,
    aircraft_type character varying(50) NOT NULL,
    priority public.work_order_priority,
    status public.work_order_status,
    fault_title character varying(200) NOT NULL,
    fault_description text,
    station character varying(100),
    assigned_technician_id integer,
    estimated_hours double precision,
    actual_hours double precision,
    progress integer,
    start_time timestamp without time zone,
    estimated_completion timestamp without time zone,
    completion_time timestamp without time zone,
    notes text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.work_orders OWNER TO cassdemo_user;

--
-- TOC entry 4504 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.id IS '工单ID';


--
-- TOC entry 4505 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.work_order_number; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.work_order_number IS '工单号';


--
-- TOC entry 4506 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.aircraft_tail; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.aircraft_tail IS '飞机尾号';


--
-- TOC entry 4507 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.aircraft_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.aircraft_type IS '机型';


--
-- TOC entry 4508 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.priority; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.priority IS '优先级';


--
-- TOC entry 4509 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.status IS '工单状态';


--
-- TOC entry 4510 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.fault_title; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.fault_title IS '故障标题';


--
-- TOC entry 4511 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.fault_description; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.fault_description IS '故障描述';


--
-- TOC entry 4512 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.station; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.station IS '维修站';


--
-- TOC entry 4513 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.assigned_technician_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.assigned_technician_id IS '分配技师ID';


--
-- TOC entry 4514 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.estimated_hours; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.estimated_hours IS '预计工时';


--
-- TOC entry 4515 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.actual_hours; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.actual_hours IS '实际工时';


--
-- TOC entry 4516 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.progress; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.progress IS '进度百分比';


--
-- TOC entry 4517 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.start_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.start_time IS '开始时间';


--
-- TOC entry 4518 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.estimated_completion; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.estimated_completion IS '预计完成时间';


--
-- TOC entry 4519 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.completion_time; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.completion_time IS '实际完成时间';


--
-- TOC entry 4520 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.notes; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.notes IS '备注';


--
-- TOC entry 4521 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.created_at IS '创建时间';


--
-- TOC entry 4522 (class 0 OID 0)
-- Dependencies: 228
-- Name: COLUMN work_orders.updated_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.work_orders.updated_at IS '更新时间';


--
-- TOC entry 227 (class 1259 OID 16798)
-- Name: work_orders_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.work_orders_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.work_orders_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4523 (class 0 OID 0)
-- Dependencies: 227
-- Name: work_orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.work_orders_id_seq OWNED BY public.work_orders.id;


--
-- TOC entry 236 (class 1259 OID 16866)
-- Name: workflow_instances; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.workflow_instances (
    id integer NOT NULL,
    workflow_key character varying(100) NOT NULL,
    business_key character varying(100) NOT NULL,
    business_type character varying(50),
    business_id integer,
    initiator_id integer NOT NULL,
    status public.workflow_status,
    variables text,
    created_at timestamp without time zone,
    completed_at timestamp without time zone
);


ALTER TABLE public.workflow_instances OWNER TO cassdemo_user;

--
-- TOC entry 4524 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.id IS '工作流实例ID';


--
-- TOC entry 4525 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.workflow_key; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.workflow_key IS '工作流定义键值';


--
-- TOC entry 4526 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.business_key; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.business_key IS '业务键值';


--
-- TOC entry 4527 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.business_type; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.business_type IS '业务类型';


--
-- TOC entry 4528 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.business_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.business_id IS '业务对象ID';


--
-- TOC entry 4529 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.initiator_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.initiator_id IS '发起人ID';


--
-- TOC entry 4530 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.status IS '工作流状态';


--
-- TOC entry 4531 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.variables; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.variables IS '工作流变量(JSON)';


--
-- TOC entry 4532 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.created_at IS '创建时间';


--
-- TOC entry 4533 (class 0 OID 0)
-- Dependencies: 236
-- Name: COLUMN workflow_instances.completed_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_instances.completed_at IS '完成时间';


--
-- TOC entry 235 (class 1259 OID 16865)
-- Name: workflow_instances_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.workflow_instances_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.workflow_instances_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4534 (class 0 OID 0)
-- Dependencies: 235
-- Name: workflow_instances_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.workflow_instances_id_seq OWNED BY public.workflow_instances.id;


--
-- TOC entry 258 (class 1259 OID 17065)
-- Name: workflow_tasks; Type: TABLE; Schema: public; Owner: cassdemo_user
--

CREATE TABLE public.workflow_tasks (
    id integer NOT NULL,
    instance_id integer NOT NULL,
    task_id character varying(100) NOT NULL,
    task_name character varying(100) NOT NULL,
    assignee character varying(100),
    status public.task_status,
    comment text,
    task_data text,
    created_at timestamp without time zone,
    completed_at timestamp without time zone
);


ALTER TABLE public.workflow_tasks OWNER TO cassdemo_user;

--
-- TOC entry 4535 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.id IS '任务记录ID';


--
-- TOC entry 4536 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.instance_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.instance_id IS '工作流实例ID';


--
-- TOC entry 4537 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.task_id; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.task_id IS 'SpiffWorkflow任务ID';


--
-- TOC entry 4538 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.task_name; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.task_name IS '任务名称';


--
-- TOC entry 4539 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.assignee; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.assignee IS '指派人';


--
-- TOC entry 4540 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.status; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.status IS '任务状态';


--
-- TOC entry 4541 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.comment; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.comment IS '处理意见';


--
-- TOC entry 4542 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.task_data; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.task_data IS '任务数据(JSON)';


--
-- TOC entry 4543 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.created_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.created_at IS '创建时间';


--
-- TOC entry 4544 (class 0 OID 0)
-- Dependencies: 258
-- Name: COLUMN workflow_tasks.completed_at; Type: COMMENT; Schema: public; Owner: cassdemo_user
--

COMMENT ON COLUMN public.workflow_tasks.completed_at IS '完成时间';


--
-- TOC entry 257 (class 1259 OID 17064)
-- Name: workflow_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: cassdemo_user
--

CREATE SEQUENCE public.workflow_tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.workflow_tasks_id_seq OWNER TO cassdemo_user;

--
-- TOC entry 4545 (class 0 OID 0)
-- Dependencies: 257
-- Name: workflow_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: cassdemo_user
--

ALTER SEQUENCE public.workflow_tasks_id_seq OWNED BY public.workflow_tasks.id;


--
-- TOC entry 3865 (class 2604 OID 16818)
-- Name: aog_cases id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.aog_cases ALTER COLUMN id SET DEFAULT nextval('public.aog_cases_id_seq'::regclass);


--
-- TOC entry 3863 (class 2604 OID 16786)
-- Name: certificates id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.certificates ALTER COLUMN id SET DEFAULT nextval('public.certificates_id_seq'::regclass);


--
-- TOC entry 3871 (class 2604 OID 16921)
-- Name: company_data_scopes id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.company_data_scopes ALTER COLUMN id SET DEFAULT nextval('public.company_data_scopes_id_seq'::regclass);


--
-- TOC entry 3870 (class 2604 OID 16907)
-- Name: data_access_logs id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.data_access_logs ALTER COLUMN id SET DEFAULT nextval('public.data_access_logs_id_seq'::regclass);


--
-- TOC entry 3881 (class 2604 OID 17116)
-- Name: demand_history id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_history ALTER COLUMN id SET DEFAULT nextval('public.demand_history_id_seq'::regclass);


--
-- TOC entry 3882 (class 2604 OID 17135)
-- Name: demand_matches id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches ALTER COLUMN id SET DEFAULT nextval('public.demand_matches_id_seq'::regclass);


--
-- TOC entry 3877 (class 2604 OID 17035)
-- Name: demand_responses id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_responses ALTER COLUMN id SET DEFAULT nextval('public.demand_responses_id_seq'::regclass);


--
-- TOC entry 3866 (class 2604 OID 16839)
-- Name: demands id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demands ALTER COLUMN id SET DEFAULT nextval('public.demands_id_seq'::regclass);


--
-- TOC entry 3861 (class 2604 OID 16748)
-- Name: inventory_items id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.inventory_items ALTER COLUMN id SET DEFAULT nextval('public.inventory_items_id_seq'::regclass);


--
-- TOC entry 3876 (class 2604 OID 17016)
-- Name: labor_records id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.labor_records ALTER COLUMN id SET DEFAULT nextval('public.labor_records_id_seq'::regclass);


--
-- TOC entry 3878 (class 2604 OID 17054)
-- Name: logistics_info id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.logistics_info ALTER COLUMN id SET DEFAULT nextval('public.logistics_info_id_seq'::regclass);


--
-- TOC entry 3859 (class 2604 OID 16728)
-- Name: materials id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.materials ALTER COLUMN id SET DEFAULT nextval('public.materials_id_seq'::regclass);


--
-- TOC entry 3867 (class 2604 OID 16855)
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- TOC entry 3874 (class 2604 OID 16978)
-- Name: order_approvals id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_approvals ALTER COLUMN id SET DEFAULT nextval('public.order_approvals_id_seq'::regclass);


--
-- TOC entry 3875 (class 2604 OID 16997)
-- Name: order_attachments id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_attachments ALTER COLUMN id SET DEFAULT nextval('public.order_attachments_id_seq'::regclass);


--
-- TOC entry 3872 (class 2604 OID 16940)
-- Name: order_items id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_items ALTER COLUMN id SET DEFAULT nextval('public.order_items_id_seq'::regclass);


--
-- TOC entry 3873 (class 2604 OID 16959)
-- Name: order_status_history id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_status_history ALTER COLUMN id SET DEFAULT nextval('public.order_status_history_id_seq'::regclass);


--
-- TOC entry 3862 (class 2604 OID 16765)
-- Name: orders id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.orders ALTER COLUMN id SET DEFAULT nextval('public.orders_id_seq'::regclass);


--
-- TOC entry 3860 (class 2604 OID 16739)
-- Name: permissions id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.permissions ALTER COLUMN id SET DEFAULT nextval('public.permissions_id_seq'::regclass);


--
-- TOC entry 3880 (class 2604 OID 17082)
-- Name: shared_materials id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials ALTER COLUMN id SET DEFAULT nextval('public.shared_materials_id_seq'::regclass);


--
-- TOC entry 3869 (class 2604 OID 16883)
-- Name: user_permissions id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions ALTER COLUMN id SET DEFAULT nextval('public.user_permissions_id_seq'::regclass);


--
-- TOC entry 3858 (class 2604 OID 16715)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 3864 (class 2604 OID 16802)
-- Name: work_orders id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.work_orders ALTER COLUMN id SET DEFAULT nextval('public.work_orders_id_seq'::regclass);


--
-- TOC entry 3868 (class 2604 OID 16869)
-- Name: workflow_instances id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_instances ALTER COLUMN id SET DEFAULT nextval('public.workflow_instances_id_seq'::regclass);


--
-- TOC entry 3879 (class 2604 OID 17068)
-- Name: workflow_tasks id; Type: DEFAULT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_tasks ALTER COLUMN id SET DEFAULT nextval('public.workflow_tasks_id_seq'::regclass);


--
-- TOC entry 4155 (class 0 OID 16815)
-- Dependencies: 230
-- Data for Name: aog_cases; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.aog_cases (id, case_number, aircraft_tail, aircraft_type, location, priority, status, fault_title, fault_description, customer_id, assigned_team_id, contact_name, contact_phone, contact_email, response_time, estimated_resolution, resolution_time, resolution_notes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4151 (class 0 OID 16783)
-- Dependencies: 226
-- Data for Name: certificates; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.certificates (id, certificate_number, material_id, certificate_type, issuing_authority, issue_date, expiry_date, status, document_path, verification_status, notes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4167 (class 0 OID 16918)
-- Dependencies: 242
-- Data for Name: company_data_scopes; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.company_data_scopes (id, user_id, company_name, access_type, granted_by, granted_at, expires_at, is_active) FROM stdin;
\.


--
-- TOC entry 4165 (class 0 OID 16904)
-- Dependencies: 240
-- Data for Name: data_access_logs; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.data_access_logs (id, user_id, resource_type, resource_id, action, ip_address, user_agent, success, error_message, additional_data, created_at) FROM stdin;
\.


--
-- TOC entry 4187 (class 0 OID 17113)
-- Dependencies: 262
-- Data for Name: demand_history; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.demand_history (id, demand_id, operator_id, action, description, details, old_status, new_status, related_object_type, related_object_id, created_at) FROM stdin;
\.


--
-- TOC entry 4189 (class 0 OID 17132)
-- Dependencies: 264
-- Data for Name: demand_matches; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.demand_matches (id, demand_id, supplier_id, inventory_item_id, shared_material_id, match_score, part_number_match, substitute_match, location_distance, price_estimate, available_quantity, condition_code, estimated_delivery_days, supplier_rating, supplier_certification, status, matched_at, expires_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4179 (class 0 OID 17032)
-- Dependencies: 254
-- Data for Name: demand_responses; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.demand_responses (id, demand_id, supplier_id, status, unit_price, total_price, delivery_time, availability, condition_code, certificate_info, message, valid_until, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4157 (class 0 OID 16836)
-- Dependencies: 232
-- Data for Name: demands; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.demands (id, demand_number, title, requester_id, type, priority, status, material_name, part_number, aircraft_type, quantity, unit, description, delivery_location, delivery_time, budget_range, quality_requirements, contact_info, expires_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4147 (class 0 OID 16745)
-- Dependencies: 222
-- Data for Name: inventory_items; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.inventory_items (id, material_id, location, current_stock, safety_stock, unit_price, condition_code, is_shareable, status, batch_number, expiry_date, supplier_id, last_updated, created_at) FROM stdin;
\.


--
-- TOC entry 4177 (class 0 OID 17013)
-- Dependencies: 252
-- Data for Name: labor_records; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.labor_records (id, work_order_id, technician_id, start_time, end_time, hours_worked, work_description, labor_rate, labor_cost, created_at) FROM stdin;
\.


--
-- TOC entry 4181 (class 0 OID 17051)
-- Dependencies: 256
-- Data for Name: logistics_info; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.logistics_info (id, order_id, tracking_number, carrier, status, shipped_at, estimated_arrival, actual_arrival, current_location, delivery_notes, recipient_name, recipient_phone, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4143 (class 0 OID 16725)
-- Dependencies: 218
-- Data for Name: materials; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.materials (id, part_number, part_name, name, category, manufacturer, aircraft_type, model, description, specifications, unit, weight, shelf_life_months, image_url, created_at, updated_at) FROM stdin;
1	CFM56-7B24	CFM56发动机	CFM56发动机	发动机及部件	CFM International	B737-800	\N	CFM56-7B24发动机，适用于波音737-800机型	\N	EA	2500	120	\N	2025-07-21 13:21:01.464085	2025-07-21 13:21:01.464086
2	A320-WHL-001	主起落架机轮	主起落架机轮	起落架系统	Safran Landing Systems	A320	\N	A320主起落架机轮组件	\N	SET	85.5	60	\N	2025-07-21 13:21:01.466449	2025-07-21 13:21:01.46645
3	B777-APU-001	APU辅助动力装置	APU辅助动力装置	动力系统	Honeywell	B777-300ER	\N	B777-300ER APU辅助动力装置	\N	EA	650	96	\N	2025-07-21 13:21:01.467575	2025-07-21 13:21:01.467576
\.


--
-- TOC entry 4159 (class 0 OID 16852)
-- Dependencies: 234
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.notifications (id, user_id, type, title, content, priority, is_read, read_at, related_id, related_type, expires_at, created_at) FROM stdin;
\.


--
-- TOC entry 4173 (class 0 OID 16975)
-- Dependencies: 248
-- Data for Name: order_approvals; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.order_approvals (id, order_id, approver_id, approval_level, status, comments, approved_at, created_at) FROM stdin;
\.


--
-- TOC entry 4175 (class 0 OID 16994)
-- Dependencies: 250
-- Data for Name: order_attachments; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.order_attachments (id, order_id, filename, original_filename, file_path, file_size, file_type, uploaded_by, description, created_at) FROM stdin;
\.


--
-- TOC entry 4169 (class 0 OID 16937)
-- Dependencies: 244
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.order_items (id, order_id, material_id, quantity, unit_price, subtotal, condition_code, delivery_requirement) FROM stdin;
\.


--
-- TOC entry 4171 (class 0 OID 16956)
-- Dependencies: 246
-- Data for Name: order_status_history; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.order_status_history (id, order_id, from_status, to_status, operator_id, reason, notes, created_at) FROM stdin;
\.


--
-- TOC entry 4149 (class 0 OID 16762)
-- Dependencies: 224
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.orders (id, order_number, buyer_id, supplier_id, status, priority, total_amount, currency, delivery_address, delivery_date, notes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4145 (class 0 OID 16736)
-- Dependencies: 220
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.permissions (id, code, name, description, category, is_active, created_at, updated_at) FROM stdin;
1	user_management	用户管理	创建、编辑、删除用户	user	t	2025-07-21 13:21:00.978921	2025-07-21 13:21:00.978923
2	view_all_users	查看所有用户	查看系统中所有用户信息	user	t	2025-07-21 13:21:00.986147	2025-07-21 13:21:00.986148
3	edit_user_permissions	编辑用户权限	分配和撤销用户权限	user	t	2025-07-21 13:21:00.98688	2025-07-21 13:21:00.98688
4	view_all_permissions	查看所有权限	查看系统权限列表	permission	t	2025-07-21 13:21:00.98755	2025-07-21 13:21:00.98755
5	manage_permissions	管理权限	创建、编辑权限	permission	t	2025-07-21 13:21:00.988192	2025-07-21 13:21:00.988192
6	manage_inventory	管理库存	管理库存项目	inventory	t	2025-07-21 13:21:00.988821	2025-07-21 13:21:00.988821
7	view_own_inventory	查看自己的库存	查看自己公司的库存	inventory	t	2025-07-21 13:21:00.989587	2025-07-21 13:21:00.989588
8	view_all_inventory	查看所有库存	查看所有库存信息	inventory	t	2025-07-21 13:21:00.990236	2025-07-21 13:21:00.990236
9	audit_inventory	库存审计	进行库存审计操作	inventory	t	2025-07-21 13:21:00.990856	2025-07-21 13:21:00.990856
10	manage_orders	管理订单	创建、编辑订单	order	t	2025-07-21 13:21:00.991604	2025-07-21 13:21:00.991604
11	view_own_orders	查看自己的订单	查看作为买方或供应商的订单	order	t	2025-07-21 13:21:00.992233	2025-07-21 13:21:00.992233
12	view_all_orders	查看所有订单	查看系统中所有订单	order	t	2025-07-21 13:21:00.992848	2025-07-21 13:21:00.992848
13	process_all_orders	处理所有订单	处理和审核订单	order	t	2025-07-21 13:21:00.993468	2025-07-21 13:21:00.993469
14	approve_orders	审批订单	审批高价值或特殊订单	order	t	2025-07-21 13:21:00.99408	2025-07-21 13:21:00.994081
15	publish_demand	发布需求	发布航材需求	demand	t	2025-07-21 13:21:00.994691	2025-07-21 13:21:00.994691
16	respond_demand	响应需求	响应航材需求	demand	t	2025-07-21 13:21:00.995311	2025-07-21 13:21:00.995311
17	view_all_demands	查看所有需求	查看所有需求信息	demand	t	2025-07-21 13:21:00.995928	2025-07-21 13:21:00.995929
18	publish_shared	发布共享件	发布可共享的航材	shared	t	2025-07-21 13:21:00.996545	2025-07-21 13:21:00.996545
19	audit_shared_materials	审核共享件	审核用户发布的共享件	shared	t	2025-07-21 13:21:00.997158	2025-07-21 13:21:00.997158
20	view_all_shared	查看所有共享件	查看所有共享件信息	shared	t	2025-07-21 13:21:00.997769	2025-07-21 13:21:00.997769
21	update_maintenance_status	更新维修状态	更新维修工单状态	maintenance	t	2025-07-21 13:21:00.998379	2025-07-21 13:21:00.998379
22	technical_support	技术支持	提供技术支持服务	maintenance	t	2025-07-21 13:21:00.998984	2025-07-21 13:21:00.998984
23	view_maintenance_data	查看维修数据	查看维修相关数据	maintenance	t	2025-07-21 13:21:00.99961	2025-07-21 13:21:00.99961
24	manage_work_orders	管理工单	创建和管理维修工单	maintenance	t	2025-07-21 13:21:01.000227	2025-07-21 13:21:01.000227
25	track_shipment	跟踪配送	跟踪航材配送状态	logistics	t	2025-07-21 13:21:01.000906	2025-07-21 13:21:01.000906
26	update_logistics	更新物流	更新物流信息	logistics	t	2025-07-21 13:21:01.001516	2025-07-21 13:21:01.001517
27	manage_delivery	管理配送	管理配送流程	logistics	t	2025-07-21 13:21:01.00213	2025-07-21 13:21:01.00213
28	view_logistics_data	查看物流数据	查看物流相关数据	logistics	t	2025-07-21 13:21:01.002835	2025-07-21 13:21:01.002835
29	data_analysis	数据分析	查看和分析平台数据	analytics	t	2025-07-21 13:21:01.003415	2025-07-21 13:21:01.003415
30	view_reports	查看报表	查看各类业务报表	analytics	t	2025-07-21 13:21:01.003965	2025-07-21 13:21:01.003965
31	export_data	导出数据	导出业务数据	analytics	t	2025-07-21 13:21:01.004509	2025-07-21 13:21:01.00451
32	system_config	系统配置	配置系统参数	system	t	2025-07-21 13:21:01.005056	2025-07-21 13:21:01.005057
33	view_system_logs	查看系统日志	查看系统操作日志	system	t	2025-07-21 13:21:01.005595	2025-07-21 13:21:01.005595
34	backup_restore	备份恢复	执行系统备份和恢复	system	t	2025-07-21 13:21:01.006142	2025-07-21 13:21:01.006143
35	cross_company_access	跨公司访问	访问其他公司的数据	special	t	2025-07-21 13:21:01.006676	2025-07-21 13:21:01.006676
36	all_permissions	所有权限	拥有系统所有权限（超级管理员）	special	t	2025-07-21 13:21:01.007166	2025-07-21 13:21:01.007166
37	view_own_data	查看自己数据	查看自己创建的数据	general	t	2025-07-21 13:21:01.007659	2025-07-21 13:21:01.00766
38	advanced_features	高级功能	使用系统高级功能	general	t	2025-07-21 13:21:01.008155	2025-07-21 13:21:01.008155
39	priority_support	优先支持	获得优先技术支持	general	t	2025-07-21 13:21:01.008649	2025-07-21 13:21:01.008649
40	admin_features	管理功能	使用管理员功能	general	t	2025-07-21 13:21:01.009136	2025-07-21 13:21:01.009136
41	super_admin	超级管理员	超级管理员权限	general	t	2025-07-21 13:21:01.009632	2025-07-21 13:21:01.009632
42	customer_service	客户服务	提供客户服务	general	t	2025-07-21 13:21:01.010106	2025-07-21 13:21:01.010107
\.


--
-- TOC entry 4185 (class 0 OID 17079)
-- Dependencies: 260
-- Data for Name: shared_materials; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.shared_materials (id, material_id, owner_id, share_type, share_quantity, available_quantity, reserved_quantity, price, min_order_quantity, description, sharing_policy, condition_code, location, expiry_date, status, workflow_instance_id, approved_by, approved_at, rejected_by, rejected_at, review_comment, view_count, inquiry_count, transaction_count, created_at, updated_at, published_at) FROM stdin;
\.


--
-- TOC entry 4163 (class 0 OID 16880)
-- Dependencies: 238
-- Data for Name: user_permissions; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.user_permissions (id, user_id, permission_id, granted_by, granted_at, expires_at, is_active) FROM stdin;
\.


--
-- TOC entry 4141 (class 0 OID 16712)
-- Dependencies: 216
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.users (id, username, email, password_hash, user_type, company_type, permission_level, company_name, real_name, phone, status, last_login, created_at, updated_at) FROM stdin;
4	test_user_1	<EMAIL>	pbkdf2:sha256:600000$O6eNHD5l4h5LSKzl$74a9395d15a18459fc20e2ed0ac63f18d577e1acd0bc4ed71a31680998454fcd	supplier	external	1	测试公司1	测试用户1	13800138001	active	\N	2025-07-21 13:21:01.626268	2025-07-21 13:21:01.626271
5	test_user_2	<EMAIL>	pbkdf2:sha256:600000$baKKLAiS2ZAmYWb5$e1a6221aaa6197072477cc93704a7e9216e3aea6cd5e7ddbc1fd29373b70d9fa	maintenance	external	1	测试公司2	测试用户2	13800138002	active	\N	2025-07-21 13:21:01.774662	2025-07-21 13:21:01.774664
6	test_user_3	<EMAIL>	pbkdf2:sha256:600000$ebE9w25wOyZTeo7A$28815d861bbf83f5992599656463235811ad446670e80c0c8c9e3ede2f5cb6e3	airline	external	1	测试公司3	测试用户3	13800138003	active	\N	2025-07-21 13:21:01.924955	2025-07-21 13:21:01.92496
7	test_user_4	<EMAIL>	pbkdf2:sha256:600000$6Og87t0ehDJb5wL3$c29583fd37c8d8c4f94d026264113cda12c88b30a79d50a7b4d5bf328b2685d8	supplier	external	1	测试公司4	测试用户4	13800138004	active	\N	2025-07-21 13:21:02.073582	2025-07-21 13:21:02.073584
8	test_user_5	<EMAIL>	pbkdf2:sha256:600000$DBnUND9qv3NFxesj$569bbee24a9abaef8df00352fce1aac86cc8f37195d497c30b1a57944cc86ce8	maintenance	external	1	测试公司5	测试用户5	13800138005	active	\N	2025-07-21 13:21:02.219498	2025-07-21 13:21:02.219499
9	admin	<EMAIL>	pbkdf2:sha256:600000$odYV2DbjGISNi6JU$7b72dc01f6e454a88890f4d126682ed9d586d4ed4d694d1c2c1c931243dda0f7	admin	internal	4	系统管理	系统管理员	13800138000	active	2025-07-22 14:56:03.574556	2025-07-22 14:02:12.659712	2025-07-22 14:56:03.574972
3	maintenance_demo	<EMAIL>	pbkdf2:sha256:600000$t3GVi65WsxbAeVdw$0a6c7b2aeaccd91e81c486e4fb8d20352e757abf048f0b20cb761da0f0bf6d44	maintenance_engineer	external	1	海航技术	王维修	0898-12345678	active	2025-07-22 15:27:14.524022	2025-07-21 13:21:01.460721	2025-07-22 15:27:14.52448
1	airline_demo	<EMAIL>	pbkdf2:sha256:600000$MvuUnF3lrsrvX6zP$e8340c3eb500ec717aa6ac8f8d4a752da6598c1e922ce33510c692511626d27b	airline_buyer	external	2	中国国际航空公司	张航空	010-12345678	active	2025-07-22 15:28:04.883806	2025-07-21 13:21:01.161413	2025-07-22 15:28:04.884213
2	supplier_demo	<EMAIL>	pbkdf2:sha256:600000$L0MokNsagsRrsGqx$ec9ce3cd0b923106af70f305b261469bf584f6ea36817d5757c6f5127f946740	supplier	external	1	中航材集团	李供应	010-87654321	active	2025-07-22 15:28:42.620059	2025-07-21 13:21:01.313727	2025-07-22 15:28:42.620932
\.


--
-- TOC entry 4153 (class 0 OID 16799)
-- Dependencies: 228
-- Data for Name: work_orders; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.work_orders (id, work_order_number, aircraft_tail, aircraft_type, priority, status, fault_title, fault_description, station, assigned_technician_id, estimated_hours, actual_hours, progress, start_time, estimated_completion, completion_time, notes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4161 (class 0 OID 16866)
-- Dependencies: 236
-- Data for Name: workflow_instances; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.workflow_instances (id, workflow_key, business_key, business_type, business_id, initiator_id, status, variables, created_at, completed_at) FROM stdin;
\.


--
-- TOC entry 4183 (class 0 OID 17065)
-- Dependencies: 258
-- Data for Name: workflow_tasks; Type: TABLE DATA; Schema: public; Owner: cassdemo_user
--

COPY public.workflow_tasks (id, instance_id, task_id, task_name, assignee, status, comment, task_data, created_at, completed_at) FROM stdin;
\.


--
-- TOC entry 4546 (class 0 OID 0)
-- Dependencies: 229
-- Name: aog_cases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.aog_cases_id_seq', 1, false);


--
-- TOC entry 4547 (class 0 OID 0)
-- Dependencies: 225
-- Name: certificates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.certificates_id_seq', 1, false);


--
-- TOC entry 4548 (class 0 OID 0)
-- Dependencies: 241
-- Name: company_data_scopes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.company_data_scopes_id_seq', 1, false);


--
-- TOC entry 4549 (class 0 OID 0)
-- Dependencies: 239
-- Name: data_access_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.data_access_logs_id_seq', 1, false);


--
-- TOC entry 4550 (class 0 OID 0)
-- Dependencies: 261
-- Name: demand_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.demand_history_id_seq', 1, false);


--
-- TOC entry 4551 (class 0 OID 0)
-- Dependencies: 263
-- Name: demand_matches_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.demand_matches_id_seq', 1, false);


--
-- TOC entry 4552 (class 0 OID 0)
-- Dependencies: 253
-- Name: demand_responses_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.demand_responses_id_seq', 1, false);


--
-- TOC entry 4553 (class 0 OID 0)
-- Dependencies: 231
-- Name: demands_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.demands_id_seq', 1, false);


--
-- TOC entry 4554 (class 0 OID 0)
-- Dependencies: 221
-- Name: inventory_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.inventory_items_id_seq', 1, false);


--
-- TOC entry 4555 (class 0 OID 0)
-- Dependencies: 251
-- Name: labor_records_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.labor_records_id_seq', 1, false);


--
-- TOC entry 4556 (class 0 OID 0)
-- Dependencies: 255
-- Name: logistics_info_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.logistics_info_id_seq', 1, false);


--
-- TOC entry 4557 (class 0 OID 0)
-- Dependencies: 217
-- Name: materials_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.materials_id_seq', 3, true);


--
-- TOC entry 4558 (class 0 OID 0)
-- Dependencies: 233
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- TOC entry 4559 (class 0 OID 0)
-- Dependencies: 247
-- Name: order_approvals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.order_approvals_id_seq', 1, false);


--
-- TOC entry 4560 (class 0 OID 0)
-- Dependencies: 249
-- Name: order_attachments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.order_attachments_id_seq', 1, false);


--
-- TOC entry 4561 (class 0 OID 0)
-- Dependencies: 243
-- Name: order_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.order_items_id_seq', 1, false);


--
-- TOC entry 4562 (class 0 OID 0)
-- Dependencies: 245
-- Name: order_status_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.order_status_history_id_seq', 1, false);


--
-- TOC entry 4563 (class 0 OID 0)
-- Dependencies: 223
-- Name: orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.orders_id_seq', 1, false);


--
-- TOC entry 4564 (class 0 OID 0)
-- Dependencies: 219
-- Name: permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.permissions_id_seq', 42, true);


--
-- TOC entry 4565 (class 0 OID 0)
-- Dependencies: 259
-- Name: shared_materials_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.shared_materials_id_seq', 1, false);


--
-- TOC entry 4566 (class 0 OID 0)
-- Dependencies: 237
-- Name: user_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.user_permissions_id_seq', 1, false);


--
-- TOC entry 4567 (class 0 OID 0)
-- Dependencies: 215
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.users_id_seq', 9, true);


--
-- TOC entry 4568 (class 0 OID 0)
-- Dependencies: 227
-- Name: work_orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.work_orders_id_seq', 1, false);


--
-- TOC entry 4569 (class 0 OID 0)
-- Dependencies: 235
-- Name: workflow_instances_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.workflow_instances_id_seq', 1, false);


--
-- TOC entry 4570 (class 0 OID 0)
-- Dependencies: 257
-- Name: workflow_tasks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: cassdemo_user
--

SELECT pg_catalog.setval('public.workflow_tasks_id_seq', 1, false);


--
-- TOC entry 3912 (class 2606 OID 16824)
-- Name: aog_cases aog_cases_case_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.aog_cases
    ADD CONSTRAINT aog_cases_case_number_key UNIQUE (case_number);


--
-- TOC entry 3914 (class 2606 OID 16822)
-- Name: aog_cases aog_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.aog_cases
    ADD CONSTRAINT aog_cases_pkey PRIMARY KEY (id);


--
-- TOC entry 3904 (class 2606 OID 16792)
-- Name: certificates certificates_certificate_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.certificates
    ADD CONSTRAINT certificates_certificate_number_key UNIQUE (certificate_number);


--
-- TOC entry 3906 (class 2606 OID 16790)
-- Name: certificates certificates_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.certificates
    ADD CONSTRAINT certificates_pkey PRIMARY KEY (id);


--
-- TOC entry 3930 (class 2606 OID 16923)
-- Name: company_data_scopes company_data_scopes_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.company_data_scopes
    ADD CONSTRAINT company_data_scopes_pkey PRIMARY KEY (id);


--
-- TOC entry 3928 (class 2606 OID 16911)
-- Name: data_access_logs data_access_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.data_access_logs
    ADD CONSTRAINT data_access_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 3952 (class 2606 OID 17120)
-- Name: demand_history demand_history_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_history
    ADD CONSTRAINT demand_history_pkey PRIMARY KEY (id);


--
-- TOC entry 3954 (class 2606 OID 17137)
-- Name: demand_matches demand_matches_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches
    ADD CONSTRAINT demand_matches_pkey PRIMARY KEY (id);


--
-- TOC entry 3944 (class 2606 OID 17039)
-- Name: demand_responses demand_responses_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_responses
    ADD CONSTRAINT demand_responses_pkey PRIMARY KEY (id);


--
-- TOC entry 3916 (class 2606 OID 16845)
-- Name: demands demands_demand_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demands
    ADD CONSTRAINT demands_demand_number_key UNIQUE (demand_number);


--
-- TOC entry 3918 (class 2606 OID 16843)
-- Name: demands demands_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demands
    ADD CONSTRAINT demands_pkey PRIMARY KEY (id);


--
-- TOC entry 3898 (class 2606 OID 16750)
-- Name: inventory_items inventory_items_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_pkey PRIMARY KEY (id);


--
-- TOC entry 3942 (class 2606 OID 17020)
-- Name: labor_records labor_records_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.labor_records
    ADD CONSTRAINT labor_records_pkey PRIMARY KEY (id);


--
-- TOC entry 3946 (class 2606 OID 17058)
-- Name: logistics_info logistics_info_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.logistics_info
    ADD CONSTRAINT logistics_info_pkey PRIMARY KEY (id);


--
-- TOC entry 3890 (class 2606 OID 16734)
-- Name: materials materials_part_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.materials
    ADD CONSTRAINT materials_part_number_key UNIQUE (part_number);


--
-- TOC entry 3892 (class 2606 OID 16732)
-- Name: materials materials_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.materials
    ADD CONSTRAINT materials_pkey PRIMARY KEY (id);


--
-- TOC entry 3920 (class 2606 OID 16859)
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 3938 (class 2606 OID 16982)
-- Name: order_approvals order_approvals_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_approvals
    ADD CONSTRAINT order_approvals_pkey PRIMARY KEY (id);


--
-- TOC entry 3940 (class 2606 OID 17001)
-- Name: order_attachments order_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_attachments
    ADD CONSTRAINT order_attachments_pkey PRIMARY KEY (id);


--
-- TOC entry 3934 (class 2606 OID 16944)
-- Name: order_items order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);


--
-- TOC entry 3936 (class 2606 OID 16963)
-- Name: order_status_history order_status_history_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_pkey PRIMARY KEY (id);


--
-- TOC entry 3900 (class 2606 OID 16771)
-- Name: orders orders_order_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_order_number_key UNIQUE (order_number);


--
-- TOC entry 3902 (class 2606 OID 16769)
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- TOC entry 3894 (class 2606 OID 16743)
-- Name: permissions permissions_code_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_code_key UNIQUE (code);


--
-- TOC entry 3896 (class 2606 OID 16741)
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 3950 (class 2606 OID 17086)
-- Name: shared_materials shared_materials_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_pkey PRIMARY KEY (id);


--
-- TOC entry 3932 (class 2606 OID 16925)
-- Name: company_data_scopes uk_user_company_scope; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.company_data_scopes
    ADD CONSTRAINT uk_user_company_scope UNIQUE (user_id, company_name);


--
-- TOC entry 3924 (class 2606 OID 16887)
-- Name: user_permissions uk_user_permission; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT uk_user_permission UNIQUE (user_id, permission_id);


--
-- TOC entry 3926 (class 2606 OID 16885)
-- Name: user_permissions user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 3884 (class 2606 OID 16723)
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- TOC entry 3886 (class 2606 OID 16719)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 3888 (class 2606 OID 16721)
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- TOC entry 3908 (class 2606 OID 16806)
-- Name: work_orders work_orders_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_pkey PRIMARY KEY (id);


--
-- TOC entry 3910 (class 2606 OID 16808)
-- Name: work_orders work_orders_work_order_number_key; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_work_order_number_key UNIQUE (work_order_number);


--
-- TOC entry 3922 (class 2606 OID 16873)
-- Name: workflow_instances workflow_instances_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_instances
    ADD CONSTRAINT workflow_instances_pkey PRIMARY KEY (id);


--
-- TOC entry 3948 (class 2606 OID 17072)
-- Name: workflow_tasks workflow_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_tasks
    ADD CONSTRAINT workflow_tasks_pkey PRIMARY KEY (id);


--
-- TOC entry 3961 (class 2606 OID 16830)
-- Name: aog_cases aog_cases_assigned_team_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.aog_cases
    ADD CONSTRAINT aog_cases_assigned_team_id_fkey FOREIGN KEY (assigned_team_id) REFERENCES public.users(id);


--
-- TOC entry 3962 (class 2606 OID 16825)
-- Name: aog_cases aog_cases_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.aog_cases
    ADD CONSTRAINT aog_cases_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.users(id);


--
-- TOC entry 3959 (class 2606 OID 16793)
-- Name: certificates certificates_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.certificates
    ADD CONSTRAINT certificates_material_id_fkey FOREIGN KEY (material_id) REFERENCES public.materials(id);


--
-- TOC entry 3970 (class 2606 OID 16931)
-- Name: company_data_scopes company_data_scopes_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.company_data_scopes
    ADD CONSTRAINT company_data_scopes_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- TOC entry 3971 (class 2606 OID 16926)
-- Name: company_data_scopes company_data_scopes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.company_data_scopes
    ADD CONSTRAINT company_data_scopes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3969 (class 2606 OID 16912)
-- Name: data_access_logs data_access_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.data_access_logs
    ADD CONSTRAINT data_access_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3991 (class 2606 OID 17121)
-- Name: demand_history demand_history_demand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_history
    ADD CONSTRAINT demand_history_demand_id_fkey FOREIGN KEY (demand_id) REFERENCES public.demands(id);


--
-- TOC entry 3992 (class 2606 OID 17126)
-- Name: demand_history demand_history_operator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_history
    ADD CONSTRAINT demand_history_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES public.users(id);


--
-- TOC entry 3993 (class 2606 OID 17138)
-- Name: demand_matches demand_matches_demand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches
    ADD CONSTRAINT demand_matches_demand_id_fkey FOREIGN KEY (demand_id) REFERENCES public.demands(id);


--
-- TOC entry 3994 (class 2606 OID 17148)
-- Name: demand_matches demand_matches_inventory_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches
    ADD CONSTRAINT demand_matches_inventory_item_id_fkey FOREIGN KEY (inventory_item_id) REFERENCES public.inventory_items(id);


--
-- TOC entry 3995 (class 2606 OID 17153)
-- Name: demand_matches demand_matches_shared_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches
    ADD CONSTRAINT demand_matches_shared_material_id_fkey FOREIGN KEY (shared_material_id) REFERENCES public.shared_materials(id);


--
-- TOC entry 3996 (class 2606 OID 17143)
-- Name: demand_matches demand_matches_supplier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_matches
    ADD CONSTRAINT demand_matches_supplier_id_fkey FOREIGN KEY (supplier_id) REFERENCES public.users(id);


--
-- TOC entry 3982 (class 2606 OID 17040)
-- Name: demand_responses demand_responses_demand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_responses
    ADD CONSTRAINT demand_responses_demand_id_fkey FOREIGN KEY (demand_id) REFERENCES public.demands(id);


--
-- TOC entry 3983 (class 2606 OID 17045)
-- Name: demand_responses demand_responses_supplier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demand_responses
    ADD CONSTRAINT demand_responses_supplier_id_fkey FOREIGN KEY (supplier_id) REFERENCES public.users(id);


--
-- TOC entry 3963 (class 2606 OID 16846)
-- Name: demands demands_requester_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.demands
    ADD CONSTRAINT demands_requester_id_fkey FOREIGN KEY (requester_id) REFERENCES public.users(id);


--
-- TOC entry 3955 (class 2606 OID 16751)
-- Name: inventory_items inventory_items_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_material_id_fkey FOREIGN KEY (material_id) REFERENCES public.materials(id);


--
-- TOC entry 3956 (class 2606 OID 16756)
-- Name: inventory_items inventory_items_supplier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_supplier_id_fkey FOREIGN KEY (supplier_id) REFERENCES public.users(id);


--
-- TOC entry 3980 (class 2606 OID 17026)
-- Name: labor_records labor_records_technician_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.labor_records
    ADD CONSTRAINT labor_records_technician_id_fkey FOREIGN KEY (technician_id) REFERENCES public.users(id);


--
-- TOC entry 3981 (class 2606 OID 17021)
-- Name: labor_records labor_records_work_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.labor_records
    ADD CONSTRAINT labor_records_work_order_id_fkey FOREIGN KEY (work_order_id) REFERENCES public.work_orders(id);


--
-- TOC entry 3984 (class 2606 OID 17059)
-- Name: logistics_info logistics_info_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.logistics_info
    ADD CONSTRAINT logistics_info_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- TOC entry 3964 (class 2606 OID 16860)
-- Name: notifications notifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3976 (class 2606 OID 16988)
-- Name: order_approvals order_approvals_approver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_approvals
    ADD CONSTRAINT order_approvals_approver_id_fkey FOREIGN KEY (approver_id) REFERENCES public.users(id);


--
-- TOC entry 3977 (class 2606 OID 16983)
-- Name: order_approvals order_approvals_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_approvals
    ADD CONSTRAINT order_approvals_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- TOC entry 3978 (class 2606 OID 17002)
-- Name: order_attachments order_attachments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_attachments
    ADD CONSTRAINT order_attachments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- TOC entry 3979 (class 2606 OID 17007)
-- Name: order_attachments order_attachments_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_attachments
    ADD CONSTRAINT order_attachments_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- TOC entry 3972 (class 2606 OID 16950)
-- Name: order_items order_items_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_material_id_fkey FOREIGN KEY (material_id) REFERENCES public.materials(id);


--
-- TOC entry 3973 (class 2606 OID 16945)
-- Name: order_items order_items_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- TOC entry 3974 (class 2606 OID 16969)
-- Name: order_status_history order_status_history_operator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES public.users(id);


--
-- TOC entry 3975 (class 2606 OID 16964)
-- Name: order_status_history order_status_history_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.order_status_history
    ADD CONSTRAINT order_status_history_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id);


--
-- TOC entry 3957 (class 2606 OID 16772)
-- Name: orders orders_buyer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_buyer_id_fkey FOREIGN KEY (buyer_id) REFERENCES public.users(id);


--
-- TOC entry 3958 (class 2606 OID 16777)
-- Name: orders orders_supplier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_supplier_id_fkey FOREIGN KEY (supplier_id) REFERENCES public.users(id);


--
-- TOC entry 3986 (class 2606 OID 17102)
-- Name: shared_materials shared_materials_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.users(id);


--
-- TOC entry 3987 (class 2606 OID 17087)
-- Name: shared_materials shared_materials_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_material_id_fkey FOREIGN KEY (material_id) REFERENCES public.materials(id);


--
-- TOC entry 3988 (class 2606 OID 17092)
-- Name: shared_materials shared_materials_owner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.users(id);


--
-- TOC entry 3989 (class 2606 OID 17107)
-- Name: shared_materials shared_materials_rejected_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_rejected_by_fkey FOREIGN KEY (rejected_by) REFERENCES public.users(id);


--
-- TOC entry 3990 (class 2606 OID 17097)
-- Name: shared_materials shared_materials_workflow_instance_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.shared_materials
    ADD CONSTRAINT shared_materials_workflow_instance_id_fkey FOREIGN KEY (workflow_instance_id) REFERENCES public.workflow_instances(id);


--
-- TOC entry 3966 (class 2606 OID 16898)
-- Name: user_permissions user_permissions_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- TOC entry 3967 (class 2606 OID 16893)
-- Name: user_permissions user_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id);


--
-- TOC entry 3968 (class 2606 OID 16888)
-- Name: user_permissions user_permissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 3960 (class 2606 OID 16809)
-- Name: work_orders work_orders_assigned_technician_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_assigned_technician_id_fkey FOREIGN KEY (assigned_technician_id) REFERENCES public.users(id);


--
-- TOC entry 3965 (class 2606 OID 16874)
-- Name: workflow_instances workflow_instances_initiator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_instances
    ADD CONSTRAINT workflow_instances_initiator_id_fkey FOREIGN KEY (initiator_id) REFERENCES public.users(id);


--
-- TOC entry 3985 (class 2606 OID 17073)
-- Name: workflow_tasks workflow_tasks_instance_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: cassdemo_user
--

ALTER TABLE ONLY public.workflow_tasks
    ADD CONSTRAINT workflow_tasks_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES public.workflow_instances(id);


-- Completed on 2025-07-23 00:25:40 CST

--
-- PostgreSQL database dump complete
--

