# PostgreSQL数据库同步方案

## 📋 同步需求分析

**场景**: 家里开发 ↔ 公司开发  
**目标**: 保持两端数据库数据和结构同步  
**频率**: 每日工作切换时同步

---

## 🎯 推荐方案

### 方案一: 增量导出导入 (推荐⭐⭐⭐⭐⭐)

最简单实用的方案，每次只同步有变化的数据。

#### 1.1 创建同步脚本
```bash
# 创建每日同步脚本
cat > daily_sync.sh << 'EOF'
#!/bin/bash

# 数据库同步脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="database_sync"
mkdir -p $BACKUP_DIR

echo "=== 开始数据库同步 ($DATE) ==="

# 导出数据 (仅数据，不包括表结构)
pg_dump -h localhost -U cassdemo_user -d cassdemo_dev \
  --data-only \
  --inserts \
  --column-inserts \
  -f $BACKUP_DIR/data_sync_$DATE.sql

echo "✓ 数据导出完成: $BACKUP_DIR/data_sync_$DATE.sql"
echo "=== 请将此文件复制到另一台机器 ==="
EOF

chmod +x daily_sync.sh
```

#### 1.2 同步使用方法
```bash
# 家里机器: 导出今天的数据变更
./daily_sync.sh

# 公司机器: 导入数据
PGPASSWORD=cassdemo_password psql -h localhost -U cassdemo_user -d cassdemo_dev -f data_sync_20250723_090000.sql
```

### 方案二: 关键表数据同步 (实用⭐⭐⭐⭐)

只同步经常变化的核心表数据。

#### 2.1 关键表识别
```sql
-- 经常变化的表
users               -- 用户数据
materials          -- 航材数据  
inventory_items    -- 库存数据
shared_materials   -- 共享件数据
demands            -- 需求数据
orders             -- 订单数据
```

#### 2.2 创建表级同步脚本
```bash
cat > table_sync.sh << 'EOF'
#!/bin/bash

TABLES=("users" "materials" "inventory_items" "shared_materials" "demands" "orders")
DATE=$(date +%Y%m%d_%H%M%S)
SYNC_DIR="table_sync_$DATE"
mkdir -p $SYNC_DIR

for table in "${TABLES[@]}"; do
    echo "导出表: $table"
    pg_dump -h localhost -U cassdemo_user -d cassdemo_dev \
      --data-only --inserts --table=$table \
      -f $SYNC_DIR/${table}.sql
done

echo "✓ 表数据导出完成，目录: $SYNC_DIR"

# 创建导入脚本
cat > $SYNC_DIR/import.sh << 'IMPORT_EOF'
#!/bin/bash
echo "开始导入表数据..."
for sql_file in *.sql; do
    echo "导入: $sql_file"
    PGPASSWORD=cassdemo_password psql -h localhost -U cassdemo_user -d cassdemo_dev -f $sql_file
done
echo "✓ 表数据导入完成"
IMPORT_EOF

chmod +x $SYNC_DIR/import.sh
EOF

chmod +x table_sync.sh
```

### 方案三: 智能增量同步 (高级⭐⭐⭐⭐⭐)

基于时间戳的增量同步，只同步新增和修改的记录。

#### 3.1 创建增量同步脚本
```bash
cat > smart_sync.sh << 'EOF'
#!/bin/bash

# 设置同步时间点 (上次同步后的数据)
LAST_SYNC=${1:-"2025-07-22 00:00:00"}
DATE=$(date +%Y%m%d_%H%M%S)
SYNC_DIR="smart_sync_$DATE"
mkdir -p $SYNC_DIR

echo "=== 智能增量同步 (从 $LAST_SYNC 开始) ==="

# 同步有 created_at/updated_at 字段的表
TABLES_WITH_TIMESTAMP=("users" "materials" "inventory_items" "shared_materials" "demands" "orders" "notifications")

for table in "${TABLES_WITH_TIMESTAMP[@]}"; do
    echo "同步表 $table 的增量数据..."
    
    # 检查表是否有 updated_at 字段
    HAS_UPDATED=$(psql -h localhost -U cassdemo_user -d cassdemo_dev -t -c "
    SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_name='$table' AND column_name='updated_at';")
    
    if [ "$HAS_UPDATED" -gt 0 ]; then
        CONDITION="updated_at > '$LAST_SYNC'"
    else
        CONDITION="created_at > '$LAST_SYNC'"
    fi
    
    # 导出增量数据
    psql -h localhost -U cassdemo_user -d cassdemo_dev -c "
    \\copy (SELECT * FROM $table WHERE $CONDITION) TO '$SYNC_DIR/${table}_delta.csv' WITH CSV HEADER;
    " 
    
    # 检查是否有数据
    LINE_COUNT=$(wc -l < "$SYNC_DIR/${table}_delta.csv")
    if [ "$LINE_COUNT" -gt 1 ]; then
        echo "  ✓ $table: $((LINE_COUNT-1)) 条新记录"
    else
        echo "  - $table: 无变化"
        rm "$SYNC_DIR/${table}_delta.csv"
    fi
done

# 创建导入脚本
cat > $SYNC_DIR/import_delta.py << 'PYTHON_EOF'
import psycopg2
import pandas as pd
import os
import glob

# 数据库连接
conn = psycopg2.connect(
    host="localhost",
    database="cassdemo_dev",
    user="cassdemo_user",
    password="cassdemo_password"
)
cur = conn.cursor()

# 导入所有CSV文件
csv_files = glob.glob("*_delta.csv")
for csv_file in csv_files:
    table_name = csv_file.replace("_delta.csv", "")
    print(f"导入 {table_name} 增量数据...")
    
    # 读取CSV
    df = pd.read_csv(csv_file)
    if len(df) == 0:
        continue
    
    # UPSERT操作 (PostgreSQL 9.5+)
    # 这里需要根据具体表结构调整
    for _, row in df.iterrows():
        # 简化版本: 先删除后插入
        cur.execute(f"DELETE FROM {table_name} WHERE id = %s;", (row['id'],))
        
        # 构建插入语句
        columns = list(df.columns)
        placeholders = ','.join(['%s'] * len(columns))
        insert_query = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
        cur.execute(insert_query, tuple(row))
    
    conn.commit()
    print(f"  ✓ {table_name}: {len(df)} 条记录已更新")

cur.close()
conn.close()
print("✓ 增量数据导入完成")
PYTHON_EOF

echo "✓ 增量同步文件准备完成"
echo "使用方法: python import_delta.py"
EOF

chmod +x smart_sync.sh
```

---

## 🚀 最简单的日常使用流程

### 推荐：每日快速同步

#### 家里下班前:
```bash
# 1. 导出今天的所有变更
./daily_sync.sh

# 2. 将生成的SQL文件通过网盘/邮件/U盘带到公司
```

#### 公司上班后:
```bash
# 1. 导入昨天家里的变更
PGPASSWORD=cassdemo_password psql -h localhost -U cassdemo_user -d cassdemo_dev -f data_sync_20250723_180000.sql

# 2. 开始开发
python app.py
```

#### 公司下班前:
```bash
# 重复上述流程，导出公司的变更给家里
```

---

## ⚙️ 自动化增强

### Git + 数据库同步
```bash
# 创建完整的工作同步脚本
cat > work_sync.sh << 'EOF'
#!/bin/bash

echo "=== 工作环境同步开始 ==="

# 1. 同步代码
echo "1. 同步Git代码..."
git add .
git commit -m "工作节点同步: $(date)"
git push origin main

# 2. 导出数据库
echo "2. 导出数据库变更..."
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U cassdemo_user -d cassdemo_dev \
  --data-only --inserts -f database_sync/work_sync_$DATE.sql

echo "✓ 同步完成，请在另一台机器执行 work_restore.sh"
EOF
```

---

## 💡 小贴士

### 避免冲突的最佳实践:
1. **ID规则**: 家里用奇数ID，公司用偶数ID
2. **时间标记**: 每条记录标注创建环境
3. **定期全量**: 每周做一次完整同步
4. **备份习惯**: 同步前先备份目标数据库

### 紧急情况处理:
```bash
# 如果同步出错，快速恢复
pg_dump -h localhost -U cassdemo_user -d cassdemo_dev -f emergency_backup.sql
# 然后重新导入正确的数据
```

---

## 🎯 针对您的具体需求

由于您已经有两个环境，我建议：

1. **立即使用**: 方案一的 `daily_sync.sh`
2. **长期优化**: 逐步升级到方案三的智能同步
3. **数据安全**: 每次同步前都先备份

明天您到公司后，只需要运行导入命令即可同步家里今晚的所有工作成果！