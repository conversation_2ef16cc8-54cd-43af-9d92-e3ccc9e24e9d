# PostgreSQL数据库迁移指南

## 📋 迁移概要

**源环境**: 家里开发机器  
**目标环境**: 公司开发机器  
**数据库**: cassdemo_dev (PostgreSQL)  
**导出文件**: `cassdemo_dev_20250723_002540.sql` (175KB)  
**迁移日期**: 2025-07-23

---

## 🎯 迁移准备清单

### 1. 必需文件检查
- ✅ **数据库导出文件**: `cassdemo_dev_20250723_002540.sql`
- ✅ **项目代码**: 整个 `cassdemo` 项目目录
- ✅ **配置文件**: `backend/config.py`
- ✅ **依赖文件**: `backend/requirements.txt`

### 2. 公司机器环境要求
- **PostgreSQL**: 12+ 版本
- **Python**: 3.9+
- **Node.js**: 16+ 版本
- **npm**: 8+ 版本

---

## 🔧 公司机器上的操作步骤

### 第一步: 安装PostgreSQL (如果未安装)

#### macOS (使用Homebrew)
```bash
# 安装PostgreSQL
brew install postgresql@16

# 启动PostgreSQL服务
brew services start postgresql@16

# 连接到默认数据库
psql postgres
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Windows
下载并安装PostgreSQL官方安装包：https://www.postgresql.org/download/windows/

### 第二步: 创建数据库用户和数据库
```bash
# 切换到postgres用户 (Linux/macOS)
sudo -u postgres psql

# 或直接连接 (macOS with Homebrew)
psql postgres
```

在PostgreSQL命令行中执行：
```sql
-- 创建用户
CREATE USER cassdemo_user WITH PASSWORD 'cassdemo_password';

-- 创建数据库
CREATE DATABASE cassdemo_dev WITH OWNER cassdemo_user;

-- 授权
GRANT ALL PRIVILEGES ON DATABASE cassdemo_dev TO cassdemo_user;

-- 退出
\q
```

### 第三步: 导入数据库
```bash
# 进入项目目录
cd /path/to/cassdemo/database_export

# 导入数据库 (方式1: 使用psql)
psql -h localhost -U cassdemo_user -d cassdemo_dev -f cassdemo_dev_20250723_002540.sql

# 导入数据库 (方式2: 如果出现权限问题)
PGPASSWORD=cassdemo_password psql -h localhost -U cassdemo_user -d cassdemo_dev -f cassdemo_dev_20250723_002540.sql
```

### 第四步: 验证数据导入
```bash
# 连接数据库
psql -h localhost -U cassdemo_user -d cassdemo_dev

# 检查表是否存在
\dt

# 检查用户数据
SELECT id, username, user_type, company_name FROM users;

# 检查关键表记录数
SELECT 
  (SELECT COUNT(*) FROM users) as users_count,
  (SELECT COUNT(*) FROM materials) as materials_count,
  (SELECT COUNT(*) FROM inventory_items) as inventory_count,
  (SELECT COUNT(*) FROM permissions) as permissions_count;

# 退出
\q
```

### 第五步: 配置项目环境
```bash
# 进入后端目录
cd /path/to/cassdemo/backend

# 安装Python依赖
pip install -r requirements.txt

# 设置环境变量 (可选)
export FLASK_ENV=development
export DEV_DATABASE_URL="postgresql://cassdemo_user:cassdemo_password@localhost:5432/cassdemo_dev"

# 测试数据库连接
python -c "
from app import create_app
app = create_app()
with app.app_context():
    from models import db, User
    users = User.query.all()
    print(f'成功连接数据库，共有 {len(users)} 个用户')
    for user in users[:5]:
        print(f'- {user.username} ({user.user_type})')
"
```

### 第六步: 启动和测试
```bash
# 启动后端服务
cd backend
python app.py

# 新终端启动前端服务
cd frontend
npm install
npm run dev
```

---

## 🧪 测试账号验证

导入完成后，您可以使用以下账号测试：

### 测试账号列表
```
# 供应商账号
supplier_demo / demo123

# 分销商账号  
distributor_demo / demo123

# 航空公司账号
airline_demo / demo123

# 维修工程师
maintenance_demo / demo123

# 系统管理员
admin / admin123
```

### 验证步骤
1. **登录测试**: 使用 `supplier_demo/demo123` 登录
2. **工作台检查**: 确认显示绿色的供应商销售工作台
3. **角色切换**: 测试其他角色账号的工作台显示
4. **功能验证**: 检查各角色的权限和功能模块

---

## ⚠️ 常见问题解决

### 问题1: 连接数据库失败
```bash
# 检查PostgreSQL是否运行
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 检查端口是否正确
netstat -an | grep 5432
```

### 问题2: 权限错误
```sql
-- 重新授权
GRANT ALL PRIVILEGES ON DATABASE cassdemo_dev TO cassdemo_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cassdemo_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cassdemo_user;
```

### 问题3: 字符编码问题
```bash
# 设置环境变量
export PGCLIENTENCODING=UTF8

# 或在导入时指定
psql -h localhost -U cassdemo_user -d cassdemo_dev -f cassdemo_dev_20250723_002540.sql --set=client_encoding=UTF8
```

### 问题4: Python包缺失
```bash
# 升级pip
pip install --upgrade pip

# 强制重装所有依赖
pip install -r requirements.txt --force-reinstall

# 如果有版本冲突，使用虚拟环境
python -m venv cassdemo_env
source cassdemo_env/bin/activate  # Linux/macOS
# 或 cassdemo_env\Scripts\activate  # Windows
pip install -r requirements.txt
```

---

## 📁 重要文件位置

### 配置文件
- **数据库配置**: `backend/config.py`
- **前端配置**: `frontend/src/config/`
- **环境变量**: `.env` (如果存在)

### 关键目录
- **后端代码**: `backend/`
- **前端代码**: `frontend/src/`
- **数据库脚本**: `backend/utils/init_data.py`
- **API接口**: `backend/routes/`

---

## 🚀 迁移成功标志

当您看到以下内容时，说明迁移成功：

1. ✅ **后端启动成功**
   ```
   ✓ 数据库已经初始化，跳过重复初始化
   * Running on http://127.0.0.1:5001
   ```

2. ✅ **前端连接正常**
   ```
   supplier_demo 登录后显示绿色供应商工作台
   ```

3. ✅ **角色系统正常**
   ```
   - Supplier工作台: 绿色销售主题
   - Distributor工作台: 紫色贸易主题  
   - Airline工作台: 蓝色采购主题
   ```

---

## 📞 技术支持

如果遇到问题，请检查：

1. **日志文件**: `backend/debug.log`
2. **浏览器控制台**: 开发者工具查看前端错误
3. **数据库连接**: 使用 `psql` 手动测试连接

---

**迁移完成时间**: 预计30-60分钟  
**关键成功指标**: supplier_demo登录显示正常工作台  
**备注**: 本次迁移包含了完整的角色系统重新定义更新