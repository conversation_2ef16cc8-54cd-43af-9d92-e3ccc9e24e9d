# 航材共享平台角色功能权限对比表

**文档版本**: 1.0  
**创建日期**: 2025-07-22  
**最后更新**: 2025-07-22

---

## 📋 核心业务角色定义

### 🔴 Supplier (供应商) - 纯销售角色
- **业务模式**: 航材制造商，专注生产和销售
- **交易方向**: 只卖不买
- **典型用户**: 航材制造厂商、OEM厂商、生产企业

### 🔵 Distributor (分销商) - 双向贸易角色  
- **业务模式**: 航材贸易商，连接供需两端
- **交易方向**: 既买也卖
- **典型用户**: 航材贸易公司、代理商、经销商

### 🟡 Airline (航空公司) - 纯采购角色
- **业务模式**: 航材最终使用方
- **交易方向**: 只买不卖（偶尔共享富余）
- **典型用户**: 各大航空公司采购部门

---

## 🎯 功能权限详细对比表

| 功能模块 | 🔴 Supplier<br/>(供应商) | 🔵 Distributor<br/>(分销商) | 🟡 Airline<br/>(航空公司) | 说明 |
|---------|----------------------|-------------------------|------------------------|------|
| **需求发布** | ❌ 不需要采购 | ✅ 发布采购需求 | ✅ 发布采购需求 | 供应商专注销售，无采购需要 |
| **产品发布** | ✅ 发布自产品 | ✅ 发布代销品 | ❌ 非主营业务 | 航空公司不对外销售 |
| **库存管理** | ✅ 自产品库存 | ✅ 采购+销售库存 | ✅ 自用库存 | 分销商库存最复杂 |
| **采购订单** | ❌ 无采购业务 | ✅ 处理采购订单 | ✅ 处理采购订单 | 供应商不参与采购 |
| **销售订单** | ✅ 处理销售订单 | ✅ 处理销售订单 | ❌ 无销售业务 | 航空公司不对外销售 |
| **询价响应** | ✅ 响应客户询价 | ✅ 双向询价处理 | ✅ 发起询价 | 分销商需双向处理 |
| **共享件发布** | ✅ 富余库存共享 | ✅ 代销+富余共享 | ✅ 闲置航材共享 | 各角色共享目的不同 |
| **价格管理** | ✅ 产品定价 | ✅ 买卖价差管理 | ❌ 无定价需求 | 分销商需管理进销价差 |
| **客户管理** | ✅ 销售客户管理 | ✅ 供应商+客户管理 | ✅ 供应商管理 | 分销商管理最复杂 |
| **财务结算** | ✅ 销售收款 | ✅ 采购付款+销售收款 | ✅ 采购付款 | 分销商资金流最复杂 |
| **数据统计** | 📊 销售业绩分析 | 📊 贸易利润分析 | 📊 采购成本分析 | 各角色关注指标不同 |

---

## 🔐 后端权限代码映射

### 数据库用户类型枚举
```sql
user_type ENUM (
    'supplier',           -- 供应商
    'distributor',        -- 分销商
    'airline_buyer',      -- 航空公司采购员
    'platform_staff',     -- 平台员工
    'maintenance_engineer', -- 维修工程师
    'logistics_specialist', -- 物流专员
    'admin'               -- 系统管理员
)
```

### 权限映射表
```python
ROLE_PERMISSIONS = {
    'supplier': [
        'manage_inventory',        # 库存管理
        'publish_shared',          # 发布共享（销售产品）
        'manage_sales_orders',     # 销售订单管理
        'view_own_data',          # 查看自己数据
        'respond_to_inquiry'       # 响应询价
    ],
    'distributor': [
        'publish_demand',          # 发布采购需求
        'manage_inventory',        # 库存管理
        'publish_shared',          # 发布销售产品
        'manage_orders',           # 通用订单管理
        'manage_sales_orders',     # 销售订单管理
        'manage_purchase_orders',  # 采购订单管理
        'view_own_data',          # 查看自己数据
        'respond_to_inquiry',      # 响应询价
        'trade_analysis'           # 贸易分析
    ],
    'airline_buyer': [
        'publish_demand',          # 发布采购需求
        'manage_inventory',        # 库存管理
        'publish_shared',          # 共享闲置航材
        'manage_orders',           # 订单管理
        'view_own_data'           # 查看自己数据
    ]
}
```

### 角色判断方法
```python
def is_supplier(self):
    return self.user_type == 'supplier'
    
def is_distributor(self):
    return self.user_type == 'distributor'
    
def is_airline(self):
    return self.user_type in ['airline', 'airline_buyer']
    
def can_publish_demand(self):
    # 只有航空公司和分销商可以发布采购需求
    return self.user_type in ['airline', 'airline_buyer', 'distributor']
    
def can_publish_products(self):
    # 供应商和分销商可以发布销售产品
    return self.user_type in ['supplier', 'distributor']
    
def get_business_direction(self):
    if self.user_type == 'supplier':
        return 'sell_only'        # 只销售
    elif self.user_type == 'distributor':
        return 'buy_and_sell'     # 既买也卖
    elif self.user_type in ['airline', 'airline_buyer']:
        return 'buy_only'         # 只采购
```

---

## 🖥️ 前端工作台设计

### 工作台组件对应关系
| 角色 | 工作台组件 | 主要功能区 | UI主题色 |
|------|------------|-----------|---------|
| 🔴 Supplier | SupplierWorkspace.vue | 产品管理、销售订单、客户服务 | 绿色(销售) |
| 🔵 Distributor | DistributorWorkspace.vue | 采购+销售双业务、库存周转 | 紫色(贸易) |
| 🟡 Airline | AirlineBuyerWorkspace.vue | 需求发布、采购订单、库存共享 | 蓝色(采购) |

### 导航菜单差异化
```javascript
// 供应商导航菜单
supplier_menu = [
    '产品管理', '销售订单', '库存管理', 
    '客户询价', '共享件发布', '销售统计'
]

// 分销商导航菜单  
distributor_menu = [
    '采购需求', '销售产品', '双向订单',
    '库存周转', '询价管理', '贸易分析'
]

// 航空公司导航菜单
airline_menu = [
    '需求发布', '采购订单', '供应商管理',
    '自用库存', '共享管理', '成本分析'
]
```

---

## 📊 业务流程示例

### 典型交易流程
```
1. 航空公司发布需求 (Airline publishes demand)
    ↓
2. 分销商响应询价 (Distributor responds to inquiry)
    ↓  
3. 分销商向供应商采购 (Distributor purchases from Supplier)
    ↓
4. 供应商发货给分销商 (Supplier ships to Distributor)
    ↓
5. 分销商转售给航空公司 (Distributor resells to Airline)
```

### 角色在流程中的行为
- **航空公司**: 发起需求 → 比较报价 → 下单采购
- **分销商**: 响应需求 → 寻找货源 → 采购商品 → 转售给客户
- **供应商**: 接收订单 → 生产/备货 → 发货给客户

---

## ⚠️ 实施注意事项

### 数据迁移策略
1. **现有用户分类**:
   - 检查现有supplier用户的业务特征
   - 识别哪些应该升级为distributor
   - 保留纯制造商为supplier

2. **权限平滑过渡**:
   - 现有supplier用户保持原有权限不变
   - 新distributor用户获得扩展权限
   - 提供角色转换申请流程

3. **界面兼容性**:
   - 确保旧用户能正常访问功能
   - 新角色逐步引导使用新功能
   - 提供角色功能说明文档

### 风险控制
- ✅ 数据库迁移脚本备份和回滚
- ✅ 分阶段发布，避免系统中断  
- ✅ 用户培训和操作指南
- ✅ 监控新功能使用情况和用户反馈

---

**文档维护**: 此文档需与代码实现保持同步，如有权限或功能变更请及时更新。