<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航材共享保障平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        /* 设计系统 */
        :root {
            --primary-blue: #3B82F6;
            --primary-blue-dark: #1D4ED8;
            --success-green: #10B981;
            --font-title: 'Inter', sans-serif;
            --font-body: 'Roboto', sans-serif;
        }

        body {
            font-family: var(--font-body);
        }

        .title-font {
            font-family: var(--font-title);
        }

        .subtitle-font {
            font-family: var(--font-title);
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-family: var(--font-title);
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            border-radius: 12px;
            padding: 10px 22px;
            font-weight: 600;
            font-family: var(--font-title);
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-secondary:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-1px);
        }

        /* 现代卡片 */
        .modern-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* 页面切换 */
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 导航样式 */
        .main-nav-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            color: #6B7280;
            text-decoration: none;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 15px;
            position: relative;
        }

        .main-nav-item:hover {
            color: var(--primary-blue);
        }

        .main-nav-item.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    <script>
        // 简单的页面切换函数
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // 重新初始化图标
            setTimeout(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 100);
        }

        // 页面切换函数
        function showLogin() {
            showPage('loginpage');
        }

        function showHomepage() {
            showPage('homepage');
        }

        function showMainApp() {
            showPage('mainapp');
        }

        function switchMainSection(sectionId) {
            // 更新导航状态
            document.querySelectorAll('.main-nav-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.section === sectionId) {
                    item.classList.add('active');
                }
            });
            
            // 显示对应内容
            showAppContent(sectionId);
        }

        // 全局变量
        let currentNotification = null;

        // 切换通知下拉菜单
        function toggleNotificationDropdown() {
            const dropdown = document.getElementById('notification-dropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
            }
        }

        // 显示通知详情
        function showNotificationDetail(notificationData) {
            currentNotification = notificationData;
            showAppContent('notification-detail');
        }

        function showAppContent(contentId) {
            const content = document.getElementById('app-content');
            const contents = {
                workspace: getWorkspaceContent(),
                marketplace: getMarketplaceContent(),
                orders: getOrdersContent(),
                inventory: getInventoryContent(),
                maintenance: getMaintenanceContent(),
                quality: getQualityContent(),
                analytics: getAnalyticsContent(),
                'publish-demand': getPublishDemandContent(),
                'logistics-tracking': getLogisticsTrackingContent(),
                'emergency-response': getEmergencyResponseContent(),
                'notification-center': getNotificationCenterContent(),
                'notification-detail': getNotificationDetailContent()
            };

            if (contents[contentId]) {
                content.innerHTML = contents[contentId];
                content.classList.remove('fade-in');
                setTimeout(() => content.classList.add('fade-in'), 10);

                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        }

        // 快速操作处理函数
        function showQuickAction(actionId) {
            showAppContent(actionId);
        }
    </script>
</head>
<body>
    <!-- 应用容器 -->
    <div id="app" class="min-h-screen">
        <!-- 门户首页 -->
        <div id="homepage" class="page-content active">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
                <div class="max-w-7xl mx-auto px-6 py-4">
                    <div class="flex items-center justify-between">
                        <!-- Logo区域 -->
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                </svg>
                            </div>
                            <div>
                                <h1 class="title-font text-xl text-gray-800">航材共享保障平台</h1>
                                <p class="text-xs text-gray-500">Aviation Material Sharing Platform</p>
                            </div>
                        </div>

                        <!-- 导航菜单 -->
                        <nav class="hidden lg:flex items-center space-x-8">
                            <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">解决方案</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">平台功能</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">行业市场</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">关于我们</a>
                        </nav>

                        <!-- 用户操作区 -->
                        <div class="flex items-center space-x-4">
                            <button onclick="showLogin()" class="text-gray-700 hover:text-blue-600 font-medium">登录</button>
                            <button onclick="showLogin()" class="btn-primary">免费试用</button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容区域 -->
            <main>
                <!-- 英雄区域 -->
                <section class="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
                    <div class="max-w-7xl mx-auto px-6 text-center">
                        <h1 class="title-font text-5xl md:text-6xl font-bold mb-6">
                            智能航材共享
                            <span class="bg-gradient-to-r from-blue-200 to-green-200 bg-clip-text text-transparent">保障平台</span>
                        </h1>
                        <p class="text-xl md:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto">
                            连接航空公司、中航材、维修企业，实现航材资源高效配置，降低成本，提升效率
                        </p>

                        <!-- CTA按钮 -->
                        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
                            <button onclick="showLogin()" class="bg-white text-blue-600 hover:bg-gray-100 border-none rounded-xl px-8 py-4 text-lg font-semibold transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                                <i data-lucide="rocket" class="w-5 h-5 mr-2 inline"></i>
                                立即体验
                            </button>
                            <button class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-800 rounded-xl px-8 py-4 text-lg font-semibold transition-all duration-300">
                                <i data-lucide="play-circle" class="w-5 h-5 mr-2 inline"></i>
                                观看演示
                            </button>
                        </div>

                        <!-- 核心数据展示 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
                            <div class="text-center">
                                <div class="text-4xl font-bold text-white mb-2">50万+</div>
                                <div class="text-blue-200">航材资源</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-white mb-2">500+</div>
                                <div class="text-blue-200">合作伙伴</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-white mb-2">99.8%</div>
                                <div class="text-blue-200">服务可用性</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-white mb-2">2小时</div>
                                <div class="text-blue-200">AOG响应</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 核心解决方案 -->
                <section class="py-20 bg-white">
                    <div class="max-w-7xl mx-auto px-6">
                        <div class="text-center mb-16">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">核心解决方案</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                为航空产业链提供全方位的航材共享保障服务
                            </p>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- 周转件服务 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-64 bg-gradient-to-br from-blue-500 to-blue-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                                <i data-lucide="rotate-cw" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">周转件服务</h3>
                                            <p class="text-blue-100 mt-2">灵活服务，降低库存成本</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-8">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">智能周转件管理</h4>
                                    <p class="text-gray-600 mb-6">
                                        通过智能匹配算法，为航空公司提供高效的周转件服务，显著降低库存成本和资金占用
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            智能需求匹配，2小时内响应
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            全程质量追溯，适航证书齐全
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            灵活租期，按需付费模式
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            专业物流，全国快速配送
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-blue-600">节省成本</span> 高达40%
                                        </div>
                                        <button onclick="showLogin()" class="btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 消耗件保障 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-64 bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                                <i data-lucide="shopping-cart" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">消耗件保障</h3>
                                            <p class="text-green-100 mt-2">批量采购，优化供应链</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-8">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">一站式保障平台</h4>
                                    <p class="text-gray-600 mb-6">
                                        整合优质供应商资源，为航空公司提供消耗件批量保障服务，优化供应链管理效率
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            海量SKU，覆盖主流机型
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            批量采购，价格优势明显
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            供应商认证，质量有保障
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            智能补货，库存优化管理
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-green-600">采购效率</span> 提升60%
                                        </div>
                                        <button onclick="showLogin()" class="btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>

                            <!-- AOG紧急响应 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-64 bg-gradient-to-br from-red-500 to-red-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                                <i data-lucide="alert-triangle" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">AOG紧急响应</h3>
                                            <p class="text-red-100 mt-2">24小时，极速响应</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-8">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">7×24小时应急保障</h4>
                                    <p class="text-gray-600 mb-6">
                                        专业的AOG紧急响应团队，为航空公司提供24小时不间断的紧急航材保障服务
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            2小时内响应，快速定位资源
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            全球网络，就近调配航材
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            专业团队，经验丰富可靠
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            实时跟踪，透明化服务流程
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-red-600">响应时间</span> ≤2小时
                                        </div>
                                        <button onclick="showLogin()" class="btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 价值主张 -->
                <section class="py-20 bg-gray-50">
                    <div class="max-w-7xl mx-auto px-6">
                        <div class="text-center mb-16">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">为什么选择我们</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                专业的航材共享保障服务，助力航空产业降本增效
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <!-- 价值主张卡片1 -->
                            <div class="modern-card p-8 text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-xl transition-all">
                                    <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">极速响应</h3>
                                <p class="text-gray-600 mb-6">
                                    7×24小时在线服务，AOG紧急需求2小时内响应，平均交付时间缩短60%
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                        2小时响应
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="trending-up" class="w-4 h-4 mr-1"></i>
                                        效率提升60%
                                    </span>
                                </div>
                            </div>

                            <!-- 价值主张卡片2 -->
                            <div class="modern-card p-8 text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-xl transition-all">
                                    <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">品质保障</h3>
                                <p class="text-gray-600 mb-6">
                                    所有航材均具备完整适航证书，质量追溯体系完善，保险覆盖率100%
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="award" class="w-4 h-4 mr-1"></i>
                                        适航认证
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="umbrella" class="w-4 h-4 mr-1"></i>
                                        100%保险
                                    </span>
                                </div>
                            </div>

                            <!-- 价值主张卡片3 -->
                            <div class="modern-card p-8 text-center group">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-xl transition-all">
                                    <i data-lucide="trending-down" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">成本优化</h3>
                                <p class="text-gray-600 mb-6">
                                    通过共享模式降低库存成本30%，智能匹配算法提升资源利用率
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="dollar-sign" class="w-4 h-4 mr-1"></i>
                                        成本降低30%
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="cpu" class="w-4 h-4 mr-1"></i>
                                        智能匹配
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 服务对象 -->
                <section class="py-20 bg-white">
                    <div class="max-w-7xl mx-auto px-6">
                        <div class="text-center mb-16">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">服务对象</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                为航空产业链各环节提供专业的航材共享保障服务
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <!-- 航空公司 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute bottom-4 left-4 text-white">
                                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                                            <i data-lucide="plane" class="w-6 h-6"></i>
                                        </div>
                                        <h3 class="subtitle-font text-xl">航空公司</h3>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h4 class="subtitle-font text-lg text-gray-800 mb-3">降本增效的最佳选择</h4>
                                    <p class="text-gray-600 mb-4">
                                        通过周转件服务、消耗件保障、紧急AOG响应，帮助航空公司降低库存成本，提升运营效率
                                    </p>
                                    <ul class="space-y-2 text-sm text-gray-600 mb-6">
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            周转件服务
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            消耗件保障
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            AOG紧急响应
                                        </li>
                                    </ul>
                                    <button class="btn-primary w-full">了解详情</button>
                                </div>
                            </div>

                            <!-- 中航材 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute bottom-4 left-4 text-white">
                                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                                            <i data-lucide="building-2" class="w-6 h-6"></i>
                                        </div>
                                        <h3 class="subtitle-font text-xl">中航材</h3>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h4 class="subtitle-font text-lg text-gray-800 mb-3">资源价值最大化</h4>
                                    <p class="text-gray-600 mb-4">
                                        通过平台化运营，提升库存周转率，扩大市场覆盖，实现资源价值最大化
                                    </p>
                                    <ul class="space-y-2 text-sm text-gray-600 mb-6">
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            库存智能管理
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            需求精准匹配
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            资产价值评估
                                        </li>
                                    </ul>
                                    <button class="btn-primary w-full">了解详情</button>
                                </div>
                            </div>

                            <!-- 维修企业 -->
                            <div class="modern-card overflow-hidden group">
                                <div class="h-48 bg-gradient-to-br from-purple-500 to-purple-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute bottom-4 left-4 text-white">
                                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                                            <i data-lucide="wrench" class="w-6 h-6"></i>
                                        </div>
                                        <h3 class="subtitle-font text-xl">维修企业</h3>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h4 class="subtitle-font text-lg text-gray-800 mb-3">业务拓展新机遇</h4>
                                    <p class="text-gray-600 mb-4">
                                        接入平台生态，获得更多业务机会，提升维修服务能力和市场竞争力
                                    </p>
                                    <ul class="space-y-2 text-sm text-gray-600 mb-6">
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            维修订单对接
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            质量管理体系
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            技术支持服务
                                        </li>
                                    </ul>
                                    <button class="btn-primary w-full">了解详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 客户案例 -->
                <section class="py-20 bg-gray-50">
                    <div class="max-w-7xl mx-auto px-6">
                        <div class="text-center mb-16">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">客户成功案例</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                已为众多知名航空公司和企业提供专业的航材共享保障服务
                            </p>
                        </div>

                        <!-- 客户Logo墙 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-16">
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-600">中国国航</div>
                                    <div class="text-xs text-gray-500">Air China</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-red-600">东方航空</div>
                                    <div class="text-xs text-gray-500">China Eastern</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-700">南方航空</div>
                                    <div class="text-xs text-gray-500">China Southern</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-green-600">春秋航空</div>
                                    <div class="text-xs text-gray-500">Spring Airlines</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-orange-600">吉祥航空</div>
                                    <div class="text-xs text-gray-500">Juneyao Airlines</div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-purple-600">海航集团</div>
                                    <div class="text-xs text-gray-500">HNA Group</div>
                                </div>
                            </div>
                        </div>

                        <!-- 成功案例详情 -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- 案例1 -->
                            <div class="modern-card p-8">
                                <div class="flex items-start space-x-4 mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="plane" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="subtitle-font text-xl text-gray-800 mb-2">东方航空 - AOG紧急响应</h3>
                                        <p class="text-sm text-gray-500">2024年3月 | 上海浦东机场</p>
                                    </div>
                                </div>
                                <div class="mb-6">
                                    <h4 class="font-semibold text-gray-800 mb-3">挑战</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        东航一架A320飞机在浦东机场出现发动机故障，急需CFM56发动机叶片进行更换，传统采购渠道需要7-10天时间。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">解决方案</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        通过中航材共享保障平台AOG紧急响应服务，1.5小时内定位到北京库存，6小时内完成航材调配和运输。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">成果</h4>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-blue-600">1.5h</div>
                                            <div class="text-xs text-gray-500">响应时间</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-green-600">6h</div>
                                            <div class="text-xs text-gray-500">交付时间</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-purple-600">85%</div>
                                            <div class="text-xs text-gray-500">成本节省</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-t border-gray-200 pt-4">
                                    <p class="text-sm text-gray-600 italic">
                                        "中航材平台的AOG响应速度超出预期，帮助我们快速恢复航班运营，避免了巨大的经济损失。"
                                    </p>
                                    <p class="text-sm text-gray-500 mt-2">— 东方航空 维修工程部</p>
                                </div>
                            </div>

                            <!-- 案例2 -->
                            <div class="modern-card p-8">
                                <div class="flex items-start space-x-4 mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="rotate-cw" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="subtitle-font text-xl text-gray-800 mb-2">春秋航空 - 周转件租赁</h3>
                                        <p class="text-sm text-gray-500">2024年1-6月 | 全国网络</p>
                                    </div>
                                </div>
                                <div class="mb-6">
                                    <h4 class="font-semibold text-gray-800 mb-3">挑战</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        春秋航空机队快速扩张，传统的航材采购模式导致库存成本高企，资金占用严重，影响公司现金流。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">解决方案</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        采用中航材平台的周转件租赁服务，按需租赁高价值航材，实现"以租代购"的轻资产运营模式。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">成果</h4>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-blue-600">40%</div>
                                            <div class="text-xs text-gray-500">成本降低</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-green-600">¥2.8亿</div>
                                            <div class="text-xs text-gray-500">资金释放</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-purple-600">98.5%</div>
                                            <div class="text-xs text-gray-500">可用率</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-t border-gray-200 pt-4">
                                    <p class="text-sm text-gray-600 italic">
                                        "周转件租赁模式帮助我们大幅降低了库存成本，释放的资金用于机队扩张，实现了快速发展。"
                                    </p>
                                    <p class="text-sm text-gray-500 mt-2">— 春秋航空 运营管理部</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 立即开始 -->
                <section class="py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white">
                    <div class="max-w-4xl mx-auto px-6 text-center">
                        <h2 class="title-font text-4xl font-bold mb-6">准备好开始了吗？</h2>
                        <p class="text-xl text-blue-100 mb-12">
                            加入中航材共享保障平台，体验智能化的航材管理服务
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="user-plus" class="w-8 h-8"></i>
                                </div>
                                <h3 class="subtitle-font text-lg font-semibold mb-2">1. 注册账户</h3>
                                <p class="text-blue-200 text-sm">快速注册，5分钟完成认证</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="search" class="w-8 h-8"></i>
                                </div>
                                <h3 class="subtitle-font text-lg font-semibold mb-2">2. 发布需求</h3>
                                <p class="text-blue-200 text-sm">智能匹配，精准找到所需航材</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="check-circle" class="w-8 h-8"></i>
                                </div>
                                <h3 class="subtitle-font text-lg font-semibold mb-2">3. 完成交易</h3>
                                <p class="text-blue-200 text-sm">安全交易，专业物流配送</p>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                            <button onclick="showLogin()" class="bg-white text-blue-600 hover:bg-gray-100 border-none rounded-xl px-8 py-4 text-lg font-semibold transition-all duration-300 hover:shadow-lg">
                                <i data-lucide="rocket" class="w-5 h-5 mr-2 inline"></i>
                                立即注册试用
                            </button>
                            <button class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-800 rounded-xl px-8 py-4 text-lg font-semibold transition-all duration-300">
                                <i data-lucide="phone" class="w-5 h-5 mr-2 inline"></i>
                                联系销售顾问
                            </button>
                        </div>

                        <div class="mt-8 text-sm text-blue-200">
                            <p>已有账户？<button onclick="showLogin()" class="text-white font-semibold hover:underline">立即登录</button></p>
                        </div>
                    </div>
                </section>

                <!-- 页脚 -->
                <footer class="bg-gray-900 text-white py-16">
                    <div class="max-w-7xl mx-auto px-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <!-- 公司信息 -->
                            <div class="md:col-span-2">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="title-font text-xl">中航材共享保障平台</h3>
                                        <p class="text-gray-400 text-sm">Aviation Material Sharing Platform</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 mb-6 max-w-md">
                                    专业的航材共享保障服务平台，为航空产业链提供智能化、数字化的航材管理解决方案。
                                </p>
                                <div class="flex space-x-4">
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="mail" class="w-5 h-5"></i>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="phone" class="w-5 h-5"></i>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="map-pin" class="w-5 h-5"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品服务 -->
                            <div>
                                <h4 class="subtitle-font text-lg font-semibold mb-4">产品服务</h4>
                                <ul class="space-y-3 text-gray-400">
                                    <li><a href="#" class="hover:text-white transition-colors">周转件服务</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">消耗件保障</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">AOG紧急响应</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">库存管理</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">质量管理</a></li>
                                </ul>
                            </div>

                            <!-- 支持帮助 -->
                            <div>
                                <h4 class="subtitle-font text-lg font-semibold mb-4">支持帮助</h4>
                                <ul class="space-y-3 text-gray-400">
                                    <li><a href="#" class="hover:text-white transition-colors">帮助中心</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">API文档</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">服务条款</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">隐私政策</a></li>
                                    <li><a href="#" class="hover:text-white transition-colors">联系我们</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row items-center justify-between">
                            <p class="text-gray-400 text-sm">
                                © 2025 中航材共享保障平台. 保留所有权利.
                            </p>
                            <div class="flex items-center space-x-6 mt-4 md:mt-0">
                                <span class="text-gray-400 text-sm">京ICP备12345678号</span>
                                <span class="text-gray-400 text-sm">京公网安备 11010802012345号</span>
                            </div>
                        </div>
                    </div>
                </footer>
            </main>
        </div>

        <!-- 登录页面 -->
        <div id="loginpage" class="page-content">
            <div class="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 flex items-center justify-center p-6">
                <!-- 登录卡片 -->
                <div class="relative w-full max-w-md">
                    <!-- 返回首页按钮 -->
                    <button onclick="showHomepage()" class="absolute -top-16 left-0 flex items-center space-x-2 text-white hover:text-blue-200 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                        <span>返回首页</span>
                    </button>

                    <div class="bg-white rounded-3xl shadow-2xl p-8">
                        <!-- Logo和标题 -->
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                </svg>
                            </div>
                            <h2 class="title-font text-2xl font-bold text-gray-800 mb-2">欢迎登录</h2>
                            <p class="text-gray-600">中航材共享保障平台</p>
                        </div>

                        <!-- 登录表单 -->
                        <form class="space-y-6">
                            <!-- 用户类型 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">用户类型</label>
                                <div class="grid grid-cols-3 gap-3">
                                    <label class="relative">
                                        <input type="radio" name="userType" value="airline" class="sr-only peer" checked>
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center">
                                            <i data-lucide="plane" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">航空公司</div>
                                        </div>
                                    </label>
                                    <label class="relative">
                                        <input type="radio" name="userType" value="supplier" class="sr-only peer">
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center">
                                            <i data-lucide="building-2" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">中航材</div>
                                        </div>
                                    </label>
                                    <label class="relative">
                                        <input type="radio" name="userType" value="maintenance" class="sr-only peer">
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center">
                                            <i data-lucide="wrench" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">维修企业</div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 用户名 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-3 top-3 w-5 h-5 text-gray-400"></i>
                                    <input type="text" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="请输入用户名">
                                </div>
                            </div>

                            <!-- 密码 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                <div class="relative">
                                    <i data-lucide="lock" class="absolute left-3 top-3 w-5 h-5 text-gray-400"></i>
                                    <input type="password" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="请输入密码">
                                </div>
                            </div>

                            <!-- 登录按钮 -->
                            <button type="button" onclick="showMainApp()" class="w-full btn-primary py-3 text-lg">
                                登录
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主应用界面 -->
        <div id="mainapp" class="page-content">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-100 fixed w-full top-0 z-50">
                <div class="max-w-7xl mx-auto px-6 py-4">
                    <div class="flex items-center justify-between">
                        <!-- Logo区域 -->
                        <div class="flex items-center space-x-6">
                            <button onclick="showHomepage()" class="flex items-center space-x-3 hover:opacity-80 transition-opacity">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                    </svg>
                                </div>
                                <div class="hidden sm:block">
                                    <div class="flex items-center space-x-2">
                                        <h1 class="title-font text-lg font-bold text-gray-800">中航材</h1>
                                        <div class="w-1 h-4 bg-blue-600 rounded-full"></div>
                                        <span class="text-sm text-gray-600 font-medium">共享保障平台</span>
                                    </div>
                                </div>
                            </button>

                            <!-- 主导航 -->
                            <nav class="hidden lg:flex items-center space-x-4 ml-8 flex-nowrap">
                                <a href="#" class="main-nav-item active whitespace-nowrap" data-section="workspace" onclick="switchMainSection('workspace')">
                                    <span>工作台</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="marketplace" onclick="switchMainSection('marketplace')">
                                    <span>航材市场</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="orders" onclick="switchMainSection('orders')">
                                    <span>我的订单</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="inventory" onclick="switchMainSection('inventory')">
                                    <span>库存管理</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="maintenance" onclick="switchMainSection('maintenance')">
                                    <span>维修管理</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="quality" onclick="switchMainSection('quality')">
                                    <span>质量管理</span>
                                </a>
                                <a href="#" class="main-nav-item whitespace-nowrap" data-section="analytics" onclick="switchMainSection('analytics')">
                                    <span>数据分析</span>
                                </a>
                            </nav>
                        </div>

                        <!-- 右侧操作区 -->
                        <div class="flex items-center space-x-4">
                            <!-- 快速操作 -->
                            <button onclick="showQuickAction('publish-demand')" class="bg-blue-600 hover:bg-blue-700 text-white h-10 px-4 rounded-lg text-sm font-medium transition-colors flex items-center whitespace-nowrap">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                发布需求
                            </button>

                            <!-- 通知 -->
                            <div class="relative">
                                <button id="notification-btn" class="w-10 h-10 bg-gray-50 rounded-xl flex items-center justify-center hover:bg-gray-100 transition-colors">
                                    <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs text-white flex items-center justify-center font-bold">3</span>
                                </button>

                                <!-- 通知下拉菜单 -->
                                <div id="notification-dropdown" class="hidden absolute right-0 top-12 w-80 bg-white rounded-2xl shadow-xl border border-gray-200 z-50">
                                    <div class="p-4 border-b border-gray-100">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-800">通知消息</h3>
                                            <button class="text-sm text-blue-600 hover:text-blue-800">全部已读</button>
                                        </div>
                                    </div>

                                    <div class="max-h-96 overflow-y-auto">
                                        <!-- 通知项1 -->
                                        <div class="p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer" onclick="toggleNotificationDropdown(); showNotificationDetail({
                                            id: 'order-001234',
                                            type: 'order',
                                            title: '新订单确认',
                                            time: '2分钟前',
                                            status: 'unread',
                                            icon: 'shopping-cart',
                                            color: 'blue',
                                            content: '您的订单 #ORD-2025-001234 已确认，预计3天内发货。订单包含CFM56发动机叶片 x2，总金额 ¥128,000。',
                                            details: {
                                                orderNumber: 'ORD-2025-001234',
                                                supplier: '北京航材公司',
                                                amount: '¥128,000',
                                                items: 'CFM56发动机叶片 x2',
                                                estimatedDelivery: '3天内',
                                                priority: 'normal'
                                            }
                                        })">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <i data-lucide="shopping-cart" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800">新订单确认</p>
                                                    <p class="text-xs text-gray-600 mt-1">您的订单 #ORD-2025-001234 已确认，预计3天内发货</p>
                                                    <p class="text-xs text-gray-400 mt-1">2分钟前</p>
                                                </div>
                                                <div class="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                                            </div>
                                        </div>

                                        <!-- 通知项2 -->
                                        <div class="p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer" onclick="toggleNotificationDropdown(); showNotificationDetail({
                                            id: 'aog-b1234',
                                            type: 'aog',
                                            title: 'AOG紧急响应',
                                            time: '15分钟前',
                                            status: 'unread',
                                            icon: 'alert-triangle',
                                            color: 'orange',
                                            content: '您的AOG需求已被接受，技术团队正在处理中。预计2小时内提供解决方案，请保持电话畅通。',
                                            details: {
                                                aircraftTail: 'B-1234',
                                                airport: '上海浦东国际机场',
                                                responseTime: '< 2小时',
                                                priority: 'urgent',
                                                technician: '李工程师',
                                                contact: '+86 138-0000-1234',
                                                issue: '发动机故障代码P0420'
                                            }
                                        })">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <i data-lucide="alert-triangle" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800">AOG紧急响应</p>
                                                    <p class="text-xs text-gray-600 mt-1">您的AOG需求已被接受，技术团队正在处理中</p>
                                                    <p class="text-xs text-gray-400 mt-1">15分钟前</p>
                                                </div>
                                                <div class="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                                            </div>
                                        </div>

                                        <!-- 通知项3 -->
                                        <div class="p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer" onclick="toggleNotificationDropdown(); showNotificationDetail({
                                            id: 'logistics-sf1234567890',
                                            type: 'logistics',
                                            title: '物流更新',
                                            time: '1小时前',
                                            status: 'read',
                                            icon: 'truck',
                                            color: 'green',
                                            content: '您的包裹已到达北京分拨中心，预计今晚送达。运单号：SF1234567890',
                                            details: {
                                                trackingNumber: 'SF1234567890',
                                                carrier: '顺丰速运',
                                                status: '运输中',
                                                currentLocation: '北京分拨中心',
                                                estimatedDelivery: '今晚18:00前',
                                                recipient: '张经理',
                                                address: '北京市朝阳区xxx路xxx号'
                                            }
                                        })">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <i data-lucide="truck" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800">物流更新</p>
                                                    <p class="text-xs text-gray-600 mt-1">您的包裹已到达北京分拨中心，预计今晚送达</p>
                                                    <p class="text-xs text-gray-400 mt-1">1小时前</p>
                                                </div>
                                                <div class="w-2 h-2 bg-gray-300 rounded-full flex-shrink-0"></div>
                                            </div>
                                        </div>

                                        <!-- 通知项4 -->
                                        <div class="p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer" onclick="toggleNotificationDropdown(); showNotificationDetail({
                                            id: 'quality-cfm56-8130',
                                            type: 'quality',
                                            title: '质量认证',
                                            time: '3小时前',
                                            status: 'read',
                                            icon: 'file-text',
                                            color: 'purple',
                                            content: 'CFM56发动机叶片的8130-3证书已更新，所有质量文档已通过审核。',
                                            details: {
                                                partNumber: 'CFM56-7B26',
                                                certificateType: '8130-3适航证书',
                                                issueDate: '2025-01-08',
                                                validUntil: '2026-01-08',
                                                inspector: '王质检员',
                                                priority: 'normal'
                                            }
                                        })">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <i data-lucide="file-text" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800">质量认证</p>
                                                    <p class="text-xs text-gray-600 mt-1">CFM56发动机叶片的8130-3证书已更新</p>
                                                    <p class="text-xs text-gray-400 mt-1">3小时前</p>
                                                </div>
                                                <div class="w-2 h-2 bg-gray-300 rounded-full flex-shrink-0"></div>
                                            </div>
                                        </div>

                                        <!-- 通知项5 -->
                                        <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="toggleNotificationDropdown(); showNotificationDetail({
                                            id: 'supplier-quote-bjhc',
                                            type: 'supplier',
                                            title: '供应商消息',
                                            time: '昨天',
                                            status: 'read',
                                            icon: 'users',
                                            color: 'indigo',
                                            content: '北京航材公司向您发送了新的报价单，包含多个航材产品的最新价格信息。',
                                            details: {
                                                supplier: '北京航材公司',
                                                quoteNumber: 'QT-2025-001',
                                                itemCount: '15个产品',
                                                totalAmount: '¥2,580,000',
                                                validUntil: '2025-01-15',
                                                contact: '李经理',
                                                priority: 'normal'
                                            }
                                        })">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                    <i data-lucide="users" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800">供应商消息</p>
                                                    <p class="text-xs text-gray-600 mt-1">北京航材公司向您发送了新的报价单</p>
                                                    <p class="text-xs text-gray-400 mt-1">昨天</p>
                                                </div>
                                                <div class="w-2 h-2 bg-gray-300 rounded-full flex-shrink-0"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="p-4 border-t border-gray-100">
                                        <button onclick="showAppContent('notification-center')" class="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium">
                                            查看全部通知
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 用户菜单 -->
                            <div class="relative">
                                <button id="user-menu-btn" class="flex items-center space-x-3 bg-gray-50 px-3 py-2 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                                        <span class="text-white text-sm font-semibold">张</span>
                                    </div>
                                    <div class="text-left hidden sm:block">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm font-semibold text-gray-800">张经理</span>
                                            <div class="w-1 h-3 bg-gray-400 rounded-full"></div>
                                            <span class="text-xs text-gray-500">东方航空</span>
                                        </div>
                                    </div>
                                    <i data-lucide="chevron-down" class="w-4 h-4 text-gray-500"></i>
                                </button>

                                <!-- 下拉菜单 -->
                                <div id="user-menu-dropdown" class="hidden absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                                        个人资料
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                        <i data-lucide="settings" class="w-4 h-4 mr-3"></i>
                                        账户设置
                                    </a>
                                    <div class="border-t border-gray-200 my-1"></div>
                                    <button onclick="showHomepage()" class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 text-left">
                                        <i data-lucide="home" class="w-4 h-4 mr-3"></i>
                                        返回门户
                                    </button>
                                    <button class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 text-left">
                                        <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
                                        退出登录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <main class="pt-28 bg-gray-50 min-h-screen">
                <div class="max-w-7xl mx-auto">
                    <div id="app-content" class="fade-in p-6">
                        <!-- 动态内容将在这里显示 -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 内容生成函数
        function getWorkspaceContent() {
            return `
                <!-- 欢迎区域 -->
                <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">早上好，张经理 👋</h1>
                            <p class="text-blue-100 text-lg">今天有 12 个紧急需求等待处理，3 个订单即将交付</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="activity" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="mb-8">
                    <h2 class="subtitle-font text-xl text-gray-800 mb-6">快速操作</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <button onclick="showQuickAction('publish-demand')" class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:shadow-lg transition-all">
                                <i data-lucide="plus-circle" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">发布需求</h3>
                            <p class="text-sm text-gray-600">快速发布航材采购需求</p>
                        </button>

                        <button onclick="switchMainSection('marketplace')" class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:shadow-lg transition-all">
                                <i data-lucide="search" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">搜索航材</h3>
                            <p class="text-sm text-gray-600">在市场中查找所需航材</p>
                        </button>

                        <button onclick="showQuickAction('logistics-tracking')" class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:shadow-lg transition-all">
                                <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">物流跟踪</h3>
                            <p class="text-sm text-gray-600">实时跟踪订单物流状态</p>
                        </button>

                        <button onclick="showQuickAction('emergency-response')" class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:shadow-lg transition-all">
                                <i data-lucide="alert-triangle" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">紧急响应</h3>
                            <p class="text-sm text-gray-600">AOG紧急需求处理</p>
                        </button>
                    </div>
                </div>

                <!-- 数据概览 -->
                <div class="mb-8">
                    <h2 class="subtitle-font text-xl text-gray-800 mb-6">数据概览</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                    <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-800">12,847</div>
                                    <div class="text-sm text-gray-600">总航材数量</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                                <span class="text-green-500 font-semibold">+5.2%</span>
                                <span class="text-gray-500 ml-1">较上月</span>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-green-600"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-800">1,234</div>
                                    <div class="text-sm text-gray-600">活跃需求</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                                <span class="text-green-500 font-semibold">+12.3%</span>
                                <span class="text-gray-500 ml-1">较上月</span>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center">
                                    <i data-lucide="clock" class="w-6 h-6 text-orange-600"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-800">567</div>
                                    <div class="text-sm text-gray-600">进行中订单</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <i data-lucide="minus" class="w-4 h-4 text-gray-500 mr-1"></i>
                                <span class="text-gray-500 font-semibold">+0.8%</span>
                                <span class="text-gray-500 ml-1">较上月</span>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-800">¥2.8M</div>
                                    <div class="text-sm text-gray-600">本月交易额</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                                <span class="text-green-500 font-semibold">+18.5%</span>
                                <span class="text-gray-500 ml-1">较上月</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getMarketplaceContent() {
            return `
                <!-- Part Search 界面 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                    <!-- 标题栏 -->
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800">零件搜索</h2>
                    </div>

                    <!-- 搜索表单 -->
                    <div class="p-6">
                        <!-- Preferred Customers 行 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-2">OEM</label>
                                <div class="flex items-center justify-between">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>Honeywell</option>
                                        <option>Boeing</option>
                                        <option>Airbus</option>
                                    </select>
                                    <button class="ml-2 text-blue-600 hover:text-blue-800">
                                        <span class="text-sm">编辑</span>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-2">结果页面</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>MRO</option>
                                    <option>OEM</option>
                                    <option>Surplus</option>
                                </select>
                            </div>
                            
                        </div>

                        <!-- Cross Reference 行 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-2">交叉引用</label>
                                <div class="flex flex-wrap gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">MCRN</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">NMCRL</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">PMA</span>
                                    </label>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-2">状态代码</label>
                                <div class="flex flex-wrap gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">NE</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">NS</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">OH</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">SV</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        <span class="ml-2 text-sm">AR</span>
                                    </label>
                                </div>
                                <div class="mt-1 text-xs text-gray-500">* 必填</div>
                            </div>
                        </div>

                        <!-- 搜索选项卡 -->
                        <div class="border-b border-gray-200 mb-6">
                            <nav class="-mb-px flex space-x-8">
                                <button class="border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600 whitespace-nowrap">
                                    <i data-lucide="search" class="w-4 h-4 mr-2 inline"></i>
                                    输入件号
                                </button>
                                <button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                                    <i data-lucide="clipboard" class="w-4 h-4 mr-2 inline"></i>
                                    粘贴件号
                                </button>
                                <button class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                                    <i data-lucide="zap" class="w-4 h-4 mr-2 inline"></i>
                                    快速询价
                                </button>
                            </nav>
                        </div>

                        <!-- 零件搜索表格 -->
                        <div class="space-y-4 mb-6">
                            <div class="grid grid-cols-12 gap-4 items-center">
                                <div class="col-span-1">
                                    <label class="block text-sm font-medium text-gray-600">Item *</label>
                                </div>
                                <div class="col-span-8">
                                    <label class="block text-sm font-medium text-gray-600">Part Number, Keyword or NSN *</label>
                                </div>
                                <div class="col-span-3">
                                    <label class="block text-sm font-medium text-gray-600">Qty</label>
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-4 items-center">
                                <div class="col-span-1">
                                    <span class="text-sm text-gray-600">01</span>
                                </div>
                                <div class="col-span-8">
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="cfm56" value="cfm56">
                                </div>
                                <div class="col-span-3">
                                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1">
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-4 items-center">
                                <div class="col-span-1">
                                    <span class="text-sm text-gray-600">02</span>
                                </div>
                                <div class="col-span-8">
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter part number...">
                                </div>
                                <div class="col-span-3">
                                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1">
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-4 items-center">
                                <div class="col-span-1">
                                    <span class="text-sm text-gray-600">03</span>
                                </div>
                                <div class="col-span-8">
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter part number...">
                                </div>
                                <div class="col-span-3">
                                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1">
                                </div>
                            </div>
                        </div>

                        <!-- Show More 按钮 -->
                        <div class="mb-6">
                            <button class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <i data-lucide="chevron-down" class="w-4 h-4 mr-1"></i>
                                显示更多
                            </button>
                        </div>

                        <!-- Required Entry 提示 -->
                        <div class="mb-6">
                            <p class="text-sm text-gray-500">* 必填项</p>
                        </div>

                        <!-- Search 按钮 -->
                        <div>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2 rounded font-medium">
                                搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <!-- 结果标题栏 -->
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-800">搜索结果</h3>
                            <div class="text-sm text-gray-600">
                                显示搜索结果: <span class="font-medium">cfm56</span>
                            </div>
                        </div>
                    </div>

                    <!-- 结果表格 -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50 border-b border-gray-200">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">件号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer">CFM56-7B24</div>
                                        <div class="text-xs text-gray-500">Engine Component</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">CFM56 Engine Blade Assembly</div>
                                        <div class="text-xs text-gray-500">For Boeing 737-800/900 Series</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">NE</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$125,000</div>
                                        <div class="text-xs text-gray-500">Per unit</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">Aviation Parts Co.</div>
                                        <div class="text-xs text-gray-500">Verified Supplier</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">询价</button>
                                        <button class="text-green-600 hover:text-green-900">购买</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer">CFM56-5B4</div>
                                        <div class="text-xs text-gray-500">Engine Component</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">CFM56 Turbine Blade</div>
                                        <div class="text-xs text-gray-500">For Airbus A320 Series</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">OH</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$89,500</div>
                                        <div class="text-xs text-gray-500">Per unit</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">Global Aero Supply</div>
                                        <div class="text-xs text-gray-500">Premium Supplier</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">询价</button>
                                        <button class="text-green-600 hover:text-green-900">购买</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer">CFM56-3C1</div>
                                        <div class="text-xs text-gray-500">Engine Component</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">CFM56 Fan Blade Set</div>
                                        <div class="text-xs text-gray-500">For Boeing 737-300/400/500</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">SV</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$95,000</div>
                                        <div class="text-xs text-gray-500">Per unit</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">AeroTech Solutions</div>
                                        <div class="text-xs text-gray-500">Certified Supplier</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">询价</button>
                                        <button class="text-green-600 hover:text-green-900">购买</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">15</span> 条结果
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50" disabled>上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getOrdersContent() {
            return `
                <!-- 订单头部 -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">我的订单 📋</h1>
                            <p class="text-green-100 text-lg">管理和跟踪您的航材订单状态</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="shopping-cart" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-gray-800 mb-1">15</div>
                        <div class="text-sm text-gray-600">待确认</div>
                    </div>
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="package" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-gray-800 mb-1">23</div>
                        <div class="text-sm text-gray-600">处理中</div>
                    </div>
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-gray-800 mb-1">8</div>
                        <div class="text-sm text-gray-600">运输中</div>
                    </div>
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-2xl font-bold text-gray-800 mb-1">156</div>
                        <div class="text-sm text-gray-600">已完成</div>
                    </div>
                </div>

                <!-- 订单筛选 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="subtitle-font text-lg text-gray-800">订单筛选</h3>
                        <button class="btn-primary">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            新建订单
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部状态</option>
                                <option>待确认</option>
                                <option>处理中</option>
                                <option>运输中</option>
                                <option>已完成</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部类型</option>
                                <option>采购订单</option>
                                <option>租赁订单</option>
                                <option>维修订单</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <input type="date" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <button class="w-full btn-secondary py-3">
                                <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                                筛选
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="subtitle-font text-lg text-gray-800">最近订单</h3>
                    </div>

                    <div class="p-6">
                        <div class="space-y-4">
                            <!-- 订单项1 -->
                            <div class="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                            <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">订单 #ORD-2025-001234</h4>
                                            <p class="text-sm text-gray-600">CFM56发动机叶片 x2</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-gray-800">¥250,000</div>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            处理中
                                        </span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                                    <div>
                                        <span class="font-medium">供应商：</span>中航材北京
                                    </div>
                                    <div>
                                        <span class="font-medium">下单时间：</span>2025-01-08
                                    </div>
                                    <div>
                                        <span class="font-medium">预计交付：</span>2025-01-15
                                    </div>
                                    <div>
                                        <span class="font-medium">紧急程度：</span>
                                        <span class="text-red-600 font-semibold">紧急</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">等待供应商确认</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="btn-secondary text-sm px-4 py-2">查看详情</button>
                                        <button class="btn-primary text-sm px-4 py-2">跟踪物流</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 订单项2 -->
                            <div class="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                            <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">订单 #ORD-2025-001233</h4>
                                            <p class="text-sm text-gray-600">A320起落架轮胎 x4</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-gray-800">¥34,000</div>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            已完成
                                        </span>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                                    <div>
                                        <span class="font-medium">供应商：</span>东航技术
                                    </div>
                                    <div>
                                        <span class="font-medium">下单时间：</span>2025-01-05
                                    </div>
                                    <div>
                                        <span class="font-medium">完成时间：</span>2025-01-07
                                    </div>
                                    <div>
                                        <span class="font-medium">紧急程度：</span>
                                        <span class="text-blue-600 font-semibold">普通</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">订单已完成，货物已签收</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="btn-secondary text-sm px-4 py-2">查看详情</button>
                                        <button class="btn-secondary text-sm px-4 py-2">重新订购</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="flex items-center justify-between mt-8">
                            <div class="text-sm text-gray-600">
                                显示 1-10 条，共 156 条订单
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-2 bg-blue-600 text-white rounded-lg text-sm">1</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">2</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">3</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getInventoryContent() {
            return `
                <!-- 库存头部 -->
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">库存管理 📦</h1>
                            <p class="text-purple-100 text-lg">实时监控航材库存状态，智能预警补货</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="warehouse" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 库存概览 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">12,847</div>
                                <div class="text-sm text-gray-600">总库存数量</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+3.2%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">23</div>
                                <div class="text-sm text-gray-600">库存预警</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-red-500 mr-1"></i>
                            <span class="text-red-500 font-semibold">+15.3%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="rotate-cw" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">85.6%</div>
                                <div class="text-sm text-gray-600">周转率</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+8.1%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">¥45.8M</div>
                                <div class="text-sm text-gray-600">库存价值</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-2.3%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>
                </div>

                <!-- 库存筛选和操作 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="subtitle-font text-lg text-gray-800">库存筛选</h3>
                        <div class="flex space-x-3">
                            <button class="btn-secondary">
                                <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                导出报表
                            </button>
                            <button class="btn-primary">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                入库登记
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="搜索航材...">
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部分类</option>
                                <option>发动机部件</option>
                                <option>起落架系统</option>
                                <option>航电设备</option>
                                <option>液压系统</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部状态</option>
                                <option>正常</option>
                                <option>预警</option>
                                <option>缺货</option>
                                <option>过期</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部仓库</option>
                                <option>北京仓库</option>
                                <option>上海仓库</option>
                                <option>广州仓库</option>
                                <option>成都仓库</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                <option>全部机型</option>
                                <option>波音737</option>
                                <option>空客A320</option>
                                <option>波音777</option>
                                <option>空客A330</option>
                            </select>
                        </div>
                        <div>
                            <button class="w-full btn-secondary py-3">
                                <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 库存列表 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="subtitle-font text-lg text-gray-800">库存明细</h3>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">共 2,847 项</span>
                                <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <option>按库存量排序</option>
                                    <option>按价值排序</option>
                                    <option>按更新时间排序</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">航材信息</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前库存</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">安全库存</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库位置</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="zap" class="w-5 h-5 text-blue-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">CFM56发动机叶片</div>
                                                <div class="text-sm text-gray-500">P/N: CFM56-7B26-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">发动机部件</td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">8 件</div>
                                        <div class="text-sm text-gray-500">单价: ¥125,000</div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">5 件</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">北京仓库-A区-001</td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">入库</button>
                                            <button class="text-orange-600 hover:text-orange-900">出库</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="settings" class="w-5 h-5 text-green-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">A320起落架轮胎</div>
                                                <div class="text-sm text-gray-500">P/N: A320-LG-TIRE-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">起落架系统</td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">3 件</div>
                                        <div class="text-sm text-gray-500">单价: ¥8,500</div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">8 件</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">上海仓库-B区-025</td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            预警
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">入库</button>
                                            <button class="text-red-600 hover:text-red-900">补货</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="cpu" class="w-5 h-5 text-purple-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">飞行管理计算机</div>
                                                <div class="text-sm text-gray-500">P/N: B777-FMC-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">航电设备</td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">12 件</div>
                                        <div class="text-sm text-gray-500">单价: ¥285,000</div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">6 件</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">广州仓库-C区-108</td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            正常
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">入库</button>
                                            <button class="text-orange-600 hover:text-orange-900">出库</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                显示 1-20 条，共 2,847 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-2 bg-blue-600 text-white rounded-lg text-sm">1</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">2</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">3</button>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getMaintenanceContent() {
            return `
                <!-- 维修管理头部 -->
                <div class="bg-gradient-to-r from-teal-600 to-teal-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">维修管理 🔧</h1>
                            <p class="text-teal-100 text-lg">全生命周期维修管理，保障飞行安全</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="wrench" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 维修概览 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">156</div>
                                <div class="text-sm text-gray-600">维修计划</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+8.2%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="tool" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">23</div>
                                <div class="text-sm text-gray-600">进行中</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-12.5%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">98.5%</div>
                                <div class="text-sm text-gray-600">完成率</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+2.1%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="clock" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">4.2天</div>
                                <div class="text-sm text-gray-600">平均周期</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-15.3%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>
                </div>

                <!-- 维修工单管理 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 mb-8">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="subtitle-font text-lg text-gray-800">维修工单</h3>
                            <div class="flex space-x-3">
                                <button class="btn-secondary">
                                    <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                                    筛选
                                </button>
                                <button class="btn-primary">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    新建工单
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="space-y-4">
                            <!-- 工单1 -->
                            <div class="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center">
                                            <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">工单 #MX-2025-001234</h4>
                                            <p class="text-sm text-gray-600">B-1234 | 波音737-800</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            AOG紧急
                                        </span>
                                        <div class="text-sm text-gray-500 mt-1">预计完成：今天 18:00</div>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-800 mb-2">故障描述</h5>
                                    <p class="text-sm text-gray-600">左发动机CFM56叶片发现裂纹，需要更换叶片并进行相关检查</p>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                                    <div>
                                        <span class="font-medium">维修站：</span>北京维修基地
                                    </div>
                                    <div>
                                        <span class="font-medium">负责人：</span>李工程师
                                    </div>
                                    <div>
                                        <span class="font-medium">开始时间：</span>2025-01-08 08:00
                                    </div>
                                    <div>
                                        <span class="font-medium">工时估算：</span>8小时
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">等待航材到位</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="btn-secondary text-sm px-4 py-2">查看详情</button>
                                        <button class="btn-primary text-sm px-4 py-2">更新状态</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 工单2 -->
                            <div class="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                            <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">工单 #MX-2025-001235</h4>
                                            <p class="text-sm text-gray-600">B-5678 | 空客A320</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            计划维修
                                        </span>
                                        <div class="text-sm text-gray-500 mt-1">预计完成：明天 16:00</div>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-800 mb-2">维修内容</h5>
                                    <p class="text-sm text-gray-600">A检维修，包括起落架系统检查、轮胎更换、液压系统检测</p>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                                    <div>
                                        <span class="font-medium">维修站：</span>上海维修基地
                                    </div>
                                    <div>
                                        <span class="font-medium">负责人：</span>王工程师
                                    </div>
                                    <div>
                                        <span class="font-medium">开始时间：</span>2025-01-09 06:00
                                    </div>
                                    <div>
                                        <span class="font-medium">工时估算：</span>24小时
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">准备就绪，等待开始</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="btn-secondary text-sm px-4 py-2">查看详情</button>
                                        <button class="btn-primary text-sm px-4 py-2">开始维修</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getQualityContent() {
            return `
                <!-- 质量管理头部 -->
                <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">质量管理 🛡️</h1>
                            <p class="text-emerald-100 text-lg">全面质量控制，确保适航标准</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="shield-check" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 质量概览 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="award" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">2,847</div>
                                <div class="text-sm text-gray-600">适航证书</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+5.2%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="alert-circle" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">12</div>
                                <div class="text-sm text-gray-600">即将到期</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-yellow-500 mr-1"></i>
                            <span class="text-yellow-500 font-semibold">+20.0%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="search-check" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">156</div>
                                <div class="text-sm text-gray-600">质检任务</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-8.3%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="percent" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">99.2%</div>
                                <div class="text-sm text-gray-600">合格率</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+0.5%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>
                </div>

                <!-- 适航证书管理 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 mb-8">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="subtitle-font text-lg text-gray-800">适航证书管理</h3>
                            <div class="flex space-x-3">
                                <button class="btn-secondary">
                                    <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                                    上传证书
                                </button>
                                <button class="btn-primary">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    申请证书
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">航材信息</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书类型</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">颁发机构</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="zap" class="w-5 h-5 text-blue-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">CFM56发动机叶片</div>
                                                <div class="text-sm text-gray-500">P/N: CFM56-7B26-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">8130-3适航标签</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">CAAC</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">2025-12-31</td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            有效
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">下载</button>
                                            <button class="text-orange-600 hover:text-orange-900">续期</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center mr-4">
                                                <i data-lucide="settings" class="w-5 h-5 text-green-600"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">A320起落架轮胎</div>
                                                <div class="text-sm text-gray-500">P/N: A320-LG-TIRE-001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">TSO授权</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">FAA</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">2025-03-15</td>
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            即将到期
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">下载</button>
                                            <button class="text-red-600 hover:text-red-900">紧急续期</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function getPublishDemandContent() {
            return `
                <!-- 发布需求头部 -->
                <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">发布需求 📝</h1>
                            <p class="text-blue-100 text-lg">快速发布航材采购需求，智能匹配优质供应商</p>
                        </div>
                        <div class="hidden lg:block">
                            <button onclick="switchMainSection('workspace')" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-white transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                                返回工作台
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 需求发布表单 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <div class="max-w-4xl mx-auto">
                        <form class="space-y-8">
                            <!-- 基本信息 -->
                            <div>
                                <h3 class="subtitle-font text-lg text-gray-800 mb-6">基本信息</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">需求类型 *</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                            <option>请选择需求类型</option>
                                            <option>周转件服务</option>
                                            <option>消耗件保障</option>
                                            <option>维修服务</option>
                                            <option>AOG紧急需求</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">紧急程度 *</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                            <option>请选择紧急程度</option>
                                            <option>AOG紧急</option>
                                            <option>高优先级</option>
                                            <option>普通</option>
                                            <option>低优先级</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 航材信息 -->
                            <div>
                                <h3 class="subtitle-font text-lg text-gray-800 mb-6">航材信息</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">航材名称 *</label>
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请输入航材名称">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">件号(P/N) *</label>
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请输入件号">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">适用机型 *</label>
                                        <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                            <option>请选择机型</option>
                                            <option>波音737-800</option>
                                            <option>空客A320</option>
                                            <option>波音777-300ER</option>
                                            <option>空客A330</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">需求数量 *</label>
                                        <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请输入数量">
                                    </div>
                                </div>
                            </div>

                            <!-- 需求详情 -->
                            <div>
                                <h3 class="subtitle-font text-lg text-gray-800 mb-6">需求详情</h3>
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">需求描述 *</label>
                                        <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请详细描述您的需求，包括技术要求、质量标准等"></textarea>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">期望交付时间 *</label>
                                            <input type="datetime-local" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">交付地点 *</label>
                                            <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请输入交付地点">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 质量要求 -->
                            <div>
                                <h3 class="subtitle-font text-lg text-gray-800 mb-6">质量要求</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <input type="checkbox" id="cert1" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <label for="cert1" class="text-sm text-gray-700">需要8130-3适航标签</label>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <input type="checkbox" id="cert2" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <label for="cert2" class="text-sm text-gray-700">需要原厂质保</label>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <input type="checkbox" id="cert3" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <label for="cert3" class="text-sm text-gray-700">需要可追溯性文件</label>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                                <button type="button" onclick="switchMainSection('workspace')" class="btn-secondary">
                                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                    返回工作台
                                </button>
                                <div class="flex space-x-4">
                                    <button type="button" class="btn-secondary">
                                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                                        保存草稿
                                    </button>
                                    <button type="submit" class="btn-primary">
                                        <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                                        发布需求
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            `;
        }

        function getLogisticsTrackingContent() {
            return `
                <!-- 物流跟踪头部 -->
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">物流跟踪 🚚</h1>
                            <p class="text-purple-100 text-lg">实时跟踪航材物流状态，掌握配送进度</p>
                        </div>
                        <div class="hidden lg:block">
                            <button onclick="switchMainSection('workspace')" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-white transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                                返回工作台
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快速查询 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8">
                    <h3 class="subtitle-font text-lg text-gray-800 mb-4">快速查询</h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500" placeholder="请输入订单号或运单号">
                        </div>
                        <button class="btn-primary">
                            <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                            查询
                        </button>
                    </div>
                </div>

                <!-- 运输中的订单 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="subtitle-font text-lg text-gray-800">运输中的订单</h3>
                    </div>

                    <div class="p-6">
                        <div class="space-y-6">
                            <!-- 物流项1 -->
                            <div class="border border-gray-200 rounded-xl p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="font-semibold text-gray-800">订单 #ORD-2025-001234</h4>
                                        <p class="text-sm text-gray-600">CFM56发动机叶片 x2</p>
                                    </div>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        运输中
                                    </span>
                                </div>

                                <!-- 物流进度 -->
                                <div class="mb-6">
                                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                                        <span>物流进度</span>
                                        <span>预计到达：今天 18:00</span>
                                    </div>
                                    <div class="relative">
                                        <div class="flex items-center justify-between">
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                                <div class="text-xs text-gray-600 mt-2 text-center">已发货</div>
                                            </div>
                                            <div class="flex-1 h-0.5 bg-green-500 mx-2"></div>
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                                <div class="text-xs text-gray-600 mt-2 text-center">运输中</div>
                                            </div>
                                            <div class="flex-1 h-0.5 bg-gray-300 mx-2"></div>
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-gray-300 rounded-full"></div>
                                                <div class="text-xs text-gray-600 mt-2 text-center">待签收</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                                    <div>
                                        <span class="font-medium">运单号：</span>SF1234567890
                                    </div>
                                    <div>
                                        <span class="font-medium">承运商：</span>顺丰速运
                                    </div>
                                    <div>
                                        <span class="font-medium">当前位置：</span>北京分拨中心
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600">
                                        最后更新：2025-01-08 14:30
                                    </div>
                                    <button class="btn-primary text-sm px-4 py-2">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getEmergencyResponseContent() {
            return `
                <!-- AOG紧急响应头部 -->
                <div class="bg-gradient-to-r from-red-600 to-red-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">AOG紧急响应 🚨</h1>
                            <p class="text-red-100 text-lg">24小时紧急响应，最快2小时内解决方案</p>
                        </div>
                        <div class="hidden lg:block">
                            <button onclick="switchMainSection('workspace')" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-white transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                                返回工作台
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 紧急联系方式 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="phone" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-2">24小时热线</h3>
                        <p class="text-2xl font-bold text-red-600">************</p>
                        <p class="text-sm text-gray-600">7×24小时专线服务</p>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-2">在线客服</h3>
                        <button class="btn-primary text-sm">立即咨询</button>
                        <p class="text-sm text-gray-600 mt-2">实时在线支持</p>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="mail" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-2">邮件支持</h3>
                        <p class="text-sm text-blue-600"><EMAIL></p>
                        <p class="text-sm text-gray-600">紧急邮件支持</p>
                    </div>
                </div>

                <!-- AOG需求提交 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <h3 class="subtitle-font text-xl text-gray-800 mb-6">提交AOG紧急需求</h3>
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">机尾号 *</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500" placeholder="请输入机尾号">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">机型 *</label>
                                <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500">
                                    <option>请选择机型</option>
                                    <option>波音737-800</option>
                                    <option>空客A320</option>
                                    <option>波音777-300ER</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所在机场 *</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500" placeholder="请输入机场代码或名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">联系电话 *</label>
                                <input type="tel" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500" placeholder="请输入联系电话">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">故障描述 *</label>
                            <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-red-500" placeholder="请详细描述故障情况和所需航材"></textarea>
                        </div>

                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <button type="button" onclick="switchMainSection('workspace')" class="btn-secondary">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                返回工作台
                            </button>
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl font-semibold transition-all">
                                <i data-lucide="alert-triangle" class="w-4 h-4 mr-2 inline"></i>
                                提交AOG需求
                            </button>
                        </div>
                    </form>
                </div>
            `;
        }

        function getNotificationCenterContent() {
            return `
                <!-- 通知中心头部 -->
                <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">通知中心 🔔</h1>
                            <p class="text-indigo-100 text-lg">查看所有系统通知和消息提醒</p>
                        </div>
                        <div class="hidden lg:block">
                            <button onclick="switchMainSection('workspace')" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-white transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                                返回工作台
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 通知统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="bell" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">28</div>
                                <div class="text-sm text-gray-600">全部通知</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+12</span>
                            <span class="text-gray-500 ml-1">今日新增</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="mail" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">5</div>
                                <div class="text-sm text-gray-600">未读消息</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-3</span>
                            <span class="text-gray-500 ml-1">较昨日</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="alert-triangle" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">3</div>
                                <div class="text-sm text-gray-600">紧急通知</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-orange-500 mr-1"></i>
                            <span class="text-orange-500 font-semibold">+1</span>
                            <span class="text-gray-500 ml-1">今日新增</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">23</div>
                                <div class="text-sm text-gray-600">已处理</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+8</span>
                            <span class="text-gray-500 ml-1">今日处理</span>
                        </div>
                    </div>
                </div>

                <!-- 通知筛选和操作 -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 mb-8">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="subtitle-font text-lg text-gray-800">通知列表</h3>
                            <div class="flex space-x-3">
                                <select class="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500">
                                    <option>全部类型</option>
                                    <option>订单通知</option>
                                    <option>AOG紧急</option>
                                    <option>物流更新</option>
                                    <option>质量认证</option>
                                    <option>供应商消息</option>
                                </select>
                                <select class="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500">
                                    <option>全部状态</option>
                                    <option>未读</option>
                                    <option>已读</option>
                                </select>
                                <button class="btn-secondary text-sm">
                                    <i data-lucide="check-square" class="w-4 h-4 mr-2"></i>
                                    全部已读
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="divide-y divide-gray-100">
                        <!-- 通知项1 - 未读 -->
                        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="showNotificationDetail({
                            id: 'order-001234',
                            type: 'order',
                            title: '新订单确认',
                            time: '2分钟前',
                            status: 'unread',
                            icon: 'shopping-cart',
                            color: 'blue',
                            content: '您的订单 #ORD-2025-001234 已确认，预计3天内发货。订单包含CFM56发动机叶片 x2，总金额 ¥128,000。',
                            details: {
                                orderNumber: 'ORD-2025-001234',
                                supplier: '北京航材公司',
                                amount: '¥128,000',
                                items: 'CFM56发动机叶片 x2',
                                estimatedDelivery: '3天内',
                                priority: 'normal'
                            }
                        })">
                            <div class="flex items-start space-x-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-semibold text-gray-800">新订单确认</h4>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">2分钟前</span>
                                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-3">您的订单 #ORD-2025-001234 已确认，预计3天内发货。订单包含CFM56发动机叶片 x2，总金额 ¥128,000。</p>
                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        <span>订单编号：ORD-2025-001234</span>
                                        <span>供应商：北京航材公司</span>
                                        <span>金额：¥128,000</span>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="event.stopPropagation(); showAppContent('orders')" class="text-blue-600 hover:text-blue-800 text-sm">查看订单</button>
                                    <button onclick="event.stopPropagation()" class="text-gray-400 hover:text-gray-600">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 通知项2 - 未读 -->
                        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="showNotificationDetail({
                            id: 'aog-b1234',
                            type: 'aog',
                            title: 'AOG紧急响应',
                            time: '15分钟前',
                            status: 'unread',
                            icon: 'alert-triangle',
                            color: 'orange',
                            content: '您的AOG需求已被接受，技术团队正在处理中。预计2小时内提供解决方案，请保持电话畅通。',
                            details: {
                                aircraftTail: 'B-1234',
                                airport: '上海浦东国际机场',
                                responseTime: '< 2小时',
                                priority: 'urgent',
                                technician: '李工程师',
                                contact: '+86 138-0000-1234',
                                issue: '发动机故障代码P0420'
                            }
                        })">
                            <div class="flex items-start space-x-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i data-lucide="alert-triangle" class="w-5 h-5 text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-semibold text-gray-800">AOG紧急响应</h4>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">15分钟前</span>
                                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-3">您的AOG需求已被接受，技术团队正在处理中。预计2小时内提供解决方案，请保持电话畅通。</p>
                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        <span>机尾号：B-1234</span>
                                        <span>机场：上海浦东</span>
                                        <span>响应时间：< 2小时</span>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="event.stopPropagation(); showAppContent('emergency-response')" class="text-orange-600 hover:text-orange-800 text-sm">查看详情</button>
                                    <button onclick="event.stopPropagation()" class="text-gray-400 hover:text-gray-600">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 通知项3 - 已读 -->
                        <div class="p-6 hover:bg-gray-50 cursor-pointer opacity-75" onclick="showNotificationDetail({
                            id: 'logistics-sf1234567890',
                            type: 'logistics',
                            title: '物流更新',
                            time: '1小时前',
                            status: 'read',
                            icon: 'truck',
                            color: 'green',
                            content: '您的包裹已到达北京分拨中心，预计今晚送达。运单号：SF1234567890',
                            details: {
                                trackingNumber: 'SF1234567890',
                                carrier: '顺丰速运',
                                status: '运输中',
                                currentLocation: '北京分拨中心',
                                estimatedDelivery: '今晚18:00前',
                                recipient: '张经理',
                                address: '北京市朝阳区xxx路xxx号'
                            }
                        })">
                            <div class="flex items-start space-x-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i data-lucide="truck" class="w-5 h-5 text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-semibold text-gray-800">物流更新</h4>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">1小时前</span>
                                            <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-3">您的包裹已到达北京分拨中心，预计今晚送达。运单号：SF1234567890</p>
                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        <span>运单号：SF1234567890</span>
                                        <span>承运商：顺丰速运</span>
                                        <span>状态：运输中</span>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="event.stopPropagation(); showAppContent('logistics-tracking')" class="text-green-600 hover:text-green-800 text-sm">跟踪物流</button>
                                    <button onclick="event.stopPropagation()" class="text-gray-400 hover:text-gray-600">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getNotificationDetailContent() {
            if (!currentNotification) {
                return `
                    <div class="text-center py-20">
                        <i data-lucide="bell-off" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-600 mb-2">未选择通知</h3>
                        <p class="text-gray-500">请从通知列表中选择一个通知查看详情</p>
                        <button onclick="showAppContent('notification-center')" class="btn-primary mt-4">
                            返回通知中心
                        </button>
                    </div>
                `;
            }

            const notification = currentNotification;
            const colorClasses = {
                blue: 'from-blue-500 to-blue-600',
                orange: 'from-orange-500 to-orange-600',
                green: 'from-green-500 to-green-600',
                red: 'from-red-500 to-red-600',
                purple: 'from-purple-500 to-purple-600'
            };

            const priorityColors = {
                urgent: 'bg-red-100 text-red-800',
                high: 'bg-orange-100 text-orange-800',
                normal: 'bg-blue-100 text-blue-800',
                low: 'bg-gray-100 text-gray-800'
            };

            return `
                <!-- 通知详情头部 -->
                <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-gradient-to-br ${colorClasses[notification.color] || colorClasses.blue} rounded-2xl flex items-center justify-center">
                                <i data-lucide="${notification.icon}" class="w-8 h-8 text-white"></i>
                            </div>
                            <div>
                                <h1 class="title-font text-3xl mb-2">${notification.title}</h1>
                                <div class="flex items-center space-x-4 text-indigo-100">
                                    <span>${notification.time}</span>
                                    <div class="w-1 h-4 bg-indigo-300 rounded-full"></div>
                                    <span class="px-2 py-1 bg-white bg-opacity-20 rounded-lg text-sm">
                                        ${notification.status === 'unread' ? '未读' : '已读'}
                                    </span>
                                    ${notification.details.priority ? `
                                        <div class="w-1 h-4 bg-indigo-300 rounded-full"></div>
                                        <span class="px-2 py-1 ${priorityColors[notification.details.priority] || priorityColors.normal} rounded-lg text-sm font-medium">
                                            ${notification.details.priority === 'urgent' ? '紧急' :
                                              notification.details.priority === 'high' ? '高优先级' :
                                              notification.details.priority === 'normal' ? '普通' : '低优先级'}
                                        </span>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <button onclick="showAppContent('notification-center')" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-white transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                                返回通知中心
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 通知内容 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 主要内容 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-8">
                            <h3 class="subtitle-font text-xl text-gray-800 mb-6">通知详情</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-700 text-lg leading-relaxed">${notification.content}</p>
                            </div>
                        </div>

                        <!-- 详细信息 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                            <h3 class="subtitle-font text-xl text-gray-800 mb-6">详细信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                ${Object.entries(notification.details).map(([key, value]) => {
                                    const labels = {
                                        orderNumber: '订单编号',
                                        supplier: '供应商',
                                        amount: '金额',
                                        items: '商品',
                                        estimatedDelivery: '预计交付',
                                        aircraftTail: '机尾号',
                                        airport: '机场',
                                        responseTime: '响应时间',
                                        technician: '技术员',
                                        contact: '联系电话',
                                        issue: '故障描述',
                                        trackingNumber: '运单号',
                                        carrier: '承运商',
                                        status: '状态',
                                        currentLocation: '当前位置',
                                        recipient: '收件人',
                                        address: '收货地址',
                                        priority: '优先级'
                                    };

                                    if (key === 'priority') return ''; // 优先级已在头部显示

                                    return `
                                        <div class="flex flex-col">
                                            <span class="text-sm font-medium text-gray-500 mb-1">${labels[key] || key}</span>
                                            <span class="text-gray-800 font-semibold">${value}</span>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- 侧边栏 -->
                    <div class="space-y-6">
                        <!-- 快速操作 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                            <h4 class="subtitle-font text-lg text-gray-800 mb-4">快速操作</h4>
                            <div class="space-y-3">
                                ${notification.type === 'order' ? `
                                    <button onclick="showAppContent('orders')" class="w-full btn-primary text-sm">
                                        <i data-lucide="shopping-cart" class="w-4 h-4 mr-2"></i>
                                        查看订单详情
                                    </button>
                                    <button class="w-full btn-secondary text-sm">
                                        <i data-lucide="message-circle" class="w-4 h-4 mr-2"></i>
                                        联系供应商
                                    </button>
                                ` : ''}
                                ${notification.type === 'aog' ? `
                                    <button onclick="showAppContent('emergency-response')" class="w-full btn-primary text-sm">
                                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-2"></i>
                                        查看AOG详情
                                    </button>
                                    <button class="w-full btn-secondary text-sm">
                                        <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                                        联系技术员
                                    </button>
                                ` : ''}
                                ${notification.type === 'logistics' ? `
                                    <button onclick="showAppContent('logistics-tracking')" class="w-full btn-primary text-sm">
                                        <i data-lucide="truck" class="w-4 h-4 mr-2"></i>
                                        跟踪物流
                                    </button>
                                    <button class="w-full btn-secondary text-sm">
                                        <i data-lucide="map-pin" class="w-4 h-4 mr-2"></i>
                                        查看位置
                                    </button>
                                ` : ''}
                                <button class="w-full btn-outline text-sm">
                                    <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                                    标记为已读
                                </button>
                            </div>
                        </div>

                        <!-- 相关通知 -->
                        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                            <h4 class="subtitle-font text-lg text-gray-800 mb-4">相关通知</h4>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="package" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-800">发货通知</div>
                                        <div class="text-xs text-gray-500">30分钟前</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="check-circle" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-800">质量检验通过</div>
                                        <div class="text-xs text-gray-500">2小时前</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getAnalyticsContent() {
            return `
                <!-- 数据分析头部 -->
                <div class="bg-gradient-to-r from-orange-600 to-orange-700 rounded-3xl p-8 mb-8 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="title-font text-3xl mb-2">数据分析 📊</h1>
                            <p class="text-orange-100 text-lg">深度洞察业务数据，驱动智能决策</p>
                        </div>
                        <div class="hidden lg:block">
                            <div class="w-32 h-32 bg-white bg-opacity-10 rounded-2xl flex items-center justify-center">
                                <i data-lucide="bar-chart-3" class="w-16 h-16 text-white opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心指标 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">¥12.5M</div>
                                <div class="text-sm text-gray-600">本月交易额</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+18.5%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="users" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">1,247</div>
                                <div class="text-sm text-gray-600">活跃用户</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+12.3%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="clock" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">1.8h</div>
                                <div class="text-sm text-gray-600">平均响应时间</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-down" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">-25.4%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center">
                                <i data-lucide="percent" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-800">96.8%</div>
                                <div class="text-sm text-gray-600">订单完成率</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-green-500 font-semibold">+2.1%</span>
                            <span class="text-gray-500 ml-1">较上月</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- 交易趋势图 -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="subtitle-font text-lg text-gray-800">交易趋势</h3>
                            <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                            </select>
                        </div>
                        <div class="h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center">
                            <div class="text-center">
                                <i data-lucide="trending-up" class="w-16 h-16 text-blue-600 mx-auto mb-4"></i>
                                <p class="text-gray-600">交易趋势图表</p>
                                <p class="text-sm text-gray-500">显示最近7天的交易数据变化</p>
                            </div>
                        </div>
                    </div>

                    <!-- 航材分类分布 -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="subtitle-font text-lg text-gray-800">航材分类分布</h3>
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-semibold">查看详情</button>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                    <span class="text-sm text-gray-700">发动机部件</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-24 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-800">45%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-green-500 rounded"></div>
                                    <span class="text-sm text-gray-700">起落架系统</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-24 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 28%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-800">28%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-purple-500 rounded"></div>
                                    <span class="text-sm text-gray-700">航电设备</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-24 bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-500 h-2 rounded-full" style="width: 18%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-800">18%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-orange-500 rounded"></div>
                                    <span class="text-sm text-gray-700">其他</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-24 bg-gray-200 rounded-full h-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 9%"></div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-800">9%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细报表 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 热门航材排行 -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="subtitle-font text-lg text-gray-800">热门航材排行</h3>
                            <span class="text-sm text-gray-500">本月</span>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">CFM56发动机叶片</div>
                                        <div class="text-xs text-gray-600">交易 156 次</div>
                                    </div>
                                </div>
                                <div class="text-sm font-bold text-gray-800">¥19.5M</div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center text-white text-xs font-bold">2</div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">A320起落架轮胎</div>
                                        <div class="text-xs text-gray-600">交易 89 次</div>
                                    </div>
                                </div>
                                <div class="text-sm font-bold text-gray-800">¥756K</div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">3</div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">飞行管理计算机</div>
                                        <div class="text-xs text-gray-600">交易 45 次</div>
                                    </div>
                                </div>
                                <div class="text-sm font-bold text-gray-800">¥12.8M</div>
                            </div>
                        </div>
                    </div>

                    <!-- 供应商表现 -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="subtitle-font text-lg text-gray-800">供应商表现</h3>
                            <span class="text-sm text-gray-500">本月</span>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="building-2" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">中航材北京</div>
                                        <div class="text-xs text-gray-600">响应时间: 1.2h</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-bold text-green-600">98.5%</div>
                                    <div class="text-xs text-gray-500">完成率</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="building-2" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">东航技术</div>
                                        <div class="text-xs text-gray-600">响应时间: 2.1h</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-bold text-green-600">96.8%</div>
                                    <div class="text-xs text-gray-500">完成率</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                        <i data-lucide="building-2" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">霍尼韦尔</div>
                                        <div class="text-xs text-gray-600">响应时间: 1.8h</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-bold text-green-600">94.2%</div>
                                    <div class="text-xs text-gray-500">完成率</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速报表 -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="subtitle-font text-lg text-gray-800">快速报表</h3>
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-semibold">生成报表</button>
                        </div>
                        <div class="space-y-3">
                            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="file-text" class="w-5 h-5 text-blue-600"></i>
                                    <span class="text-sm font-medium text-gray-800">月度交易报表</span>
                                </div>
                                <i data-lucide="download" class="w-4 h-4 text-gray-400"></i>
                            </button>
                            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="bar-chart" class="w-5 h-5 text-green-600"></i>
                                    <span class="text-sm font-medium text-gray-800">库存分析报表</span>
                                </div>
                                <i data-lucide="download" class="w-4 h-4 text-gray-400"></i>
                            </button>
                            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="users" class="w-5 h-5 text-purple-600"></i>
                                    <span class="text-sm font-medium text-gray-800">供应商绩效报表</span>
                                </div>
                                <i data-lucide="download" class="w-4 h-4 text-gray-400"></i>
                            </button>
                            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="clock" class="w-5 h-5 text-orange-600"></i>
                                    <span class="text-sm font-medium text-gray-800">响应时间报表</span>
                                </div>
                                <i data-lucide="download" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示工作台内容
            showAppContent('workspace');

            // 用户菜单下拉功能
            const userMenuBtn = document.getElementById('user-menu-btn');
            const userMenuDropdown = document.getElementById('user-menu-dropdown');

            if (userMenuBtn && userMenuDropdown) {
                userMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    userMenuDropdown.classList.toggle('hidden');
                    // 关闭通知菜单
                    const notificationDropdown = document.getElementById('notification-dropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.add('hidden');
                    }
                });

                // 阻止菜单内部点击事件冒泡
                userMenuDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // 通知菜单下拉功能
            const notificationBtn = document.getElementById('notification-btn');
            const notificationDropdown = document.getElementById('notification-dropdown');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationDropdown.classList.toggle('hidden');
                    // 关闭用户菜单
                    if (userMenuDropdown) {
                        userMenuDropdown.classList.add('hidden');
                    }
                });

                notificationDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // 点击其他地方关闭所有菜单
            document.addEventListener('click', function() {
                if (userMenuDropdown) {
                    userMenuDropdown.classList.add('hidden');
                }
                if (notificationDropdown) {
                    notificationDropdown.classList.add('hidden');
                }
            });

            // 初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>
