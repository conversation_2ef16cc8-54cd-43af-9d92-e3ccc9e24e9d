/* 航材共享保障平台 - 优化样式文件 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
}

body {
    font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
}

/* 设计系统变量 */
:root {
    --primary-blue: #3B82F6;
    --primary-blue-dark: #1D4ED8;
    --primary-blue-light: #60A5FA;
    --success-green: #10B981;
    --warning-orange: #F59E0B;
    --error-red: #EF4444;
    --neutral-gray: #6B7280;
    --neutral-light: #F3F4F6;
    --white: #FFFFFF;
    --black: #111827;
    
    /* 字体变量 */
    --font-title: 'Inter', 'PingFang SC', sans-serif;
    --font-body: '<PERSON>o', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* 渐变背景 */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    --gradient-hero: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);
}

/* 实用工具类 */
.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.title-font {
    font-family: var(--font-title);
    font-weight: 700;
}

.subtitle-font {
    font-family: var(--font-title);
    font-weight: 600;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* 间距系统 */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

.m-4 { margin: 1rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* 响应式网格 */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* 颜色系统 */
.text-white { color: var(--white); }
.text-gray-600 { color: var(--neutral-gray); }
.text-gray-800 { color: var(--black); }
.text-blue-600 { color: var(--primary-blue); }
.text-blue-100 { color: var(--primary-blue-light); }

.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--neutral-light); }

/* 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-family: var(--font-title);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-1px);
}

/* 卡片系统 */
.card {
    background: var(--white);
    border-radius: 1.25rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.modern-card {
    background: var(--white);
    border-radius: 1.25rem;
    box-shadow: var(--shadow-md);
    border: 1px solid #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 头部导航 */
.header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(10px);
}

.nav-link {
    color: var(--neutral-gray);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-blue);
    background-color: var(--neutral-light);
}

/* 英雄区域增强样式 */
.hero-section {
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
}

/* 统计数据卡片 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--white);
    margin-bottom: 0.5rem;
    display: block;
}

.stats-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
}

/* 解决方案卡片增强 */
.solution-card {
    position: relative;
    overflow: hidden;
    border-radius: 1.25rem;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.solution-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.solution-header {
    height: 16rem;
    position: relative;
    overflow: hidden;
}

.solution-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.solution-content {
    padding: 2rem;
}

.solution-icon {
    width: 5rem;
    height: 5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.solution-card:hover .solution-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 价值主张卡片 */
.value-card {
    text-align: center;
    padding: 2rem;
    background: var(--white);
    border-radius: 1.25rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.value-card:hover::before {
    transform: scaleX(1);
}

.value-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
}

.value-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.value-card:hover .value-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* 客户案例卡片 */
.case-card {
    background: var(--white);
    border-radius: 1.25rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.case-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.case-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.case-metric {
    text-align: center;
    padding: 1rem;
    background: var(--neutral-light);
    border-radius: 0.75rem;
}

.case-metric-value {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 0.25rem;
}

.case-metric-label {
    font-size: 0.75rem;
    color: var(--neutral-gray);
    font-weight: 500;
}

/* CTA区域 */
.cta-section {
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.cta-steps {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.cta-step {
    text-align: center;
    position: relative;
}

.cta-step-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    backdrop-filter: blur(10px);
}

.cta-step-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0.5rem;
}

.cta-step-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

/* 页脚 */
.footer {
    background: #111827;
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-link {
    color: #9CA3AF;
    text-decoration: none;
    transition: color 0.2s ease;
    display: block;
    padding: 0.5rem 0;
}

.footer-link:hover {
    color: var(--white);
}

/* 响应式设计 */
@media (min-width: 640px) {
    .grid-cols-sm-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .hero-title { font-size: 4rem; }
}

@media (min-width: 768px) {
    .grid-cols-md-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-md-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .grid-cols-md-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .hero-title { font-size: 4.5rem; }
    .hero-subtitle { font-size: 1.5rem; }
}

@media (min-width: 1024px) {
    .grid-cols-lg-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-lg-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .grid-cols-lg-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .grid-cols-lg-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .hero-title { font-size: 5rem; }
}

@media (min-width: 1280px) {
    .container { max-width: 1280px; }
}

/* 动画系统 */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slide-up {
    animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(50px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* 特殊效果 */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--success-green) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 页面切换 */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 导航样式增强 */
.main-nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    color: var(--neutral-gray);
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 0.9375rem;
    position: relative;
}

.main-nav-item::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-blue);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.main-nav-item:hover::before {
    width: 100%;
}

.main-nav-item:hover {
    color: var(--primary-blue);
}

.main-nav-item.active {
    color: var(--primary-blue);
}

.main-nav-item.active::before {
    width: 100%;
}

/* 优化的加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 改进的表单样式 */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.5rem;
}

/* 成功/错误状态 */
.success {
    color: var(--success-green);
    background-color: rgba(16, 185, 129, 0.1);
    border-color: var(--success-green);
}

.error {
    color: var(--error-red);
    background-color: rgba(239, 68, 68, 0.1);
    border-color: var(--error-red);
}

.warning {
    color: var(--warning-orange);
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-orange);
}

/* 状态徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-blue);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-orange);
}

.badge-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error-red);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--neutral-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--neutral-gray);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-blue);
}

/* 焦点可见性 */
.focus-visible:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* 无障碍性改进 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid var(--black);
    }
    
    .card {
        border: 2px solid var(--neutral-gray);
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 打印样式 */
@media print {
    .btn, .nav-link {
        color: var(--black) !important;
        background: transparent !important;
    }
    
    .hero-section, .cta-section {
        background: var(--white) !important;
        color: var(--black) !important;
    }
    
    .card {
        border: 1px solid var(--neutral-gray);
        box-shadow: none;
    }
}