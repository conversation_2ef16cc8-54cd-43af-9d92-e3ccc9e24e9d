<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航材共享保障平台 - 智能航材共享解决方案</title>
    
    <!-- 本地化样式文件 -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- 图标库 -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        /* 页面切换增强动画 */
        .page-content {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-content.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        /* 英雄区域增强背景 */
        .hero-section {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(168, 85, 247, 0.2) 0%, transparent 50%);
            animation: backgroundFlow 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundFlow {
            0% { transform: scale(1) rotate(0deg); }
            100% { transform: scale(1.1) rotate(5deg); }
        }

        /* 浮动动画 */
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* 渐变文字效果 */
        .gradient-text-enhanced {
            background: linear-gradient(135deg, #60A5FA 0%, #34D399 50%, #A78BFA 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite alternate;
        }

        @keyframes gradientShift {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(30deg); }
        }

        /* 卡片悬停增强 */
        .enhanced-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .enhanced-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            transition: left 0.5s;
        }

        .enhanced-card:hover::before {
            left: 100%;
        }

        .enhanced-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 按钮增强动画 */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 50%, #1E40AF 100%);
            background-size: 200% 200%;
            animation: gradientMove 3s ease infinite;
        }

        @keyframes gradientMove {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        /* 统计数字动画 */
        .counter {
            transition: all 0.3s ease;
        }

        .counter:hover {
            transform: scale(1.1);
        }

        /* 解决方案卡片特殊效果 */
        .solution-card-enhanced {
            position: relative;
            overflow: hidden;
            background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .solution-card-enhanced::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .solution-card-enhanced:hover::after {
            opacity: 1;
        }

        .solution-card-enhanced:hover {
            transform: translateY(-15px) rotateX(5deg);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }

        /* Logo区域增强 */
        .logo-enhanced {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .logo-enhanced:hover {
            transform: rotate(5deg) scale(1.05);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        /* 客户logo动画 */
        .customer-logo {
            transition: all 0.3s ease;
            filter: grayscale(100%);
        }

        .customer-logo:hover {
            filter: grayscale(0%);
            transform: scale(1.05);
        }

        /* 步骤指示器 */
        .step-indicator {
            position: relative;
            z-index: 1;
        }

        .step-indicator::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -50%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.5) 0%, transparent 100%);
            transform: translateY(-50%);
        }

        .step-indicator:last-child::after {
            display: none;
        }

        /* 导航增强效果 */
        .nav-item-enhanced {
            position: relative;
            overflow: hidden;
        }

        .nav-item-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item-enhanced:hover::before {
            left: 100%;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem !important;
            }
            
            .hero-subtitle {
                font-size: 1.125rem !important;
            }
            
            .solution-card-enhanced:hover {
                transform: translateY(-8px);
            }
        }

        /* 性能优化：减少重绘 */
        .optimized-transform {
            transform: translateZ(0);
            will-change: transform;
        }
    </style>

    <script>
        // 简单的页面切换函数
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // 重新初始化图标
            setTimeout(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 100);
        }

        // 页面切换函数
        function showLogin() {
            showPage('loginpage');
        }

        function showHomepage() {
            showPage('homepage');
        }

        function showMainApp() {
            showPage('mainapp');
        }

        // 数字动画计数器
        function animateCounter(element, start, end, duration) {
            const startTime = Date.now();
            const startValue = parseInt(start);
            const endValue = parseInt(end);
            
            function updateCounter() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(startValue + (endValue - startValue) * progress);
                element.textContent = current.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }
            
            updateCounter();
        }

        // 滚动触发动画
        function handleScrollAnimations() {
            const elements = document.querySelectorAll('.fade-in, .slide-up');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });
            
            elements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                observer.observe(el);
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            
            // 初始化滚动动画
            handleScrollAnimations();
            
            // 初始化计数器动画
            setTimeout(() => {
                const counters = document.querySelectorAll('.counter');
                counters.forEach(counter => {
                    const endValue = counter.textContent.replace(/[^0-9]/g, '');
                    if (endValue) {
                        animateCounter(counter, 0, endValue, 2000);
                    }
                });
            }, 500);
        });
    </script>
</head>
<body>
    <!-- 应用容器 -->
    <div id="app" class="min-h-screen">
        <!-- 门户首页 -->
        <div id="homepage" class="page-content active">
            <!-- 顶部导航栏 -->
            <header class="header">
                <div class="container">
                    <div class="flex items-center justify-between py-4">
                        <!-- Logo区域 -->
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 logo-enhanced rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                </svg>
                            </div>
                            <div>
                                <h1 class="title-font text-xl text-gray-800">航材共享保障平台</h1>
                                <p class="text-xs text-gray-500">Aviation Material Sharing Platform</p>
                            </div>
                        </div>

                        <!-- 导航菜单 -->
                        <nav class="hidden lg:flex items-center space-x-8">
                            <a href="#solutions" class="nav-link nav-item-enhanced">解决方案</a>
                            <a href="#features" class="nav-link nav-item-enhanced">平台功能</a>
                            <a href="#market" class="nav-link nav-item-enhanced">行业市场</a>
                            <a href="#about" class="nav-link nav-item-enhanced">关于我们</a>
                        </nav>

                        <!-- 用户操作区 -->
                        <div class="flex items-center space-x-4">
                            <button onclick="showLogin()" class="nav-link">登录</button>
                            <button onclick="showLogin()" class="btn btn-primary btn-enhanced">免费试用</button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容区域 -->
            <main>
                <!-- 英雄区域 -->
                <section class="hero-section py-20">
                    <div class="container text-center hero-content">
                        <h1 class="hero-title title-font">
                            智能航材共享
                            <span class="gradient-text-enhanced">保障平台</span>
                        </h1>
                        <p class="hero-subtitle">
                            连接航空公司、中航材、维修企业，实现航材资源高效配置，降低成本，提升效率
                        </p>

                        <!-- CTA按钮 -->
                        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
                            <button onclick="showLogin()" class="btn btn-primary btn-enhanced">
                                <i data-lucide="rocket" class="w-5 h-5 mr-2"></i>
                                立即体验
                            </button>
                            <button class="btn btn-secondary">
                                <i data-lucide="play-circle" class="w-5 h-5 mr-2"></i>
                                观看演示
                            </button>
                        </div>

                        <!-- 核心数据展示 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
                            <div class="stats-card">
                                <div class="stats-number counter">500000</div>
                                <div class="stats-label">航材资源</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number counter">500</div>
                                <div class="stats-label">合作伙伴</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">99.8%</div>
                                <div class="stats-label">服务可用性</div>
                            </div>
                            <div class="stats-card">
                                <div class="stats-number">2小时</div>
                                <div class="stats-label">AOG响应</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 核心解决方案 -->
                <section id="solutions" class="py-20 bg-white">
                    <div class="container">
                        <div class="text-center mb-16 fade-in">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">核心解决方案</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                为航空产业链提供全方位的航材共享保障服务
                            </p>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- 周转件服务 -->
                            <div class="solution-card-enhanced enhanced-card slide-up">
                                <div class="solution-header bg-gradient-to-br from-blue-500 to-blue-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="solution-icon float-animation">
                                                <i data-lucide="rotate-cw" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">周转件服务</h3>
                                            <p class="text-blue-100 mt-2">灵活服务，降低库存成本</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="solution-content">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">智能周转件管理</h4>
                                    <p class="text-gray-600 mb-6">
                                        通过智能匹配算法，为航空公司提供高效的周转件服务，显著降低库存成本和资金占用
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            智能需求匹配，2小时内响应
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            全程质量追溯，适航证书齐全
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            灵活租期，按需付费模式
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            专业物流，全国快速配送
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-blue-600">节省成本</span> 高达40%
                                        </div>
                                        <button onclick="showLogin()" class="btn btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 消耗件保障 -->
                            <div class="solution-card-enhanced enhanced-card slide-up">
                                <div class="solution-header bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="solution-icon float-animation">
                                                <i data-lucide="shopping-cart" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">消耗件保障</h3>
                                            <p class="text-green-100 mt-2">批量采购，优化供应链</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="solution-content">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">一站式保障平台</h4>
                                    <p class="text-gray-600 mb-6">
                                        整合优质供应商资源，为航空公司提供消耗件批量保障服务，优化供应链管理效率
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            海量SKU，覆盖主流机型
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            批量采购，价格优势明显
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            供应商认证，质量有保障
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            智能补货，库存优化管理
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-green-600">采购效率</span> 提升60%
                                        </div>
                                        <button onclick="showLogin()" class="btn btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>

                            <!-- AOG紧急响应 -->
                            <div class="solution-card-enhanced enhanced-card slide-up">
                                <div class="solution-header bg-gradient-to-br from-red-500 to-red-600 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <div class="solution-icon float-animation">
                                                <i data-lucide="alert-triangle" class="w-10 h-10"></i>
                                            </div>
                                            <h3 class="subtitle-font text-2xl font-bold">AOG紧急响应</h3>
                                            <p class="text-red-100 mt-2">24小时，极速响应</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="solution-content">
                                    <h4 class="subtitle-font text-xl text-gray-800 mb-4">7×24小时应急保障</h4>
                                    <p class="text-gray-600 mb-6">
                                        专业的AOG紧急响应团队，为航空公司提供24小时不间断的紧急航材保障服务
                                    </p>
                                    <ul class="space-y-3 text-sm text-gray-600 mb-8">
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            2小时内响应，快速定位资源
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            全球网络，就近调配航材
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            专业团队，经验丰富可靠
                                        </li>
                                        <li class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-3"></i>
                                            实时跟踪，透明化服务流程
                                        </li>
                                    </ul>
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm text-gray-500">
                                            <span class="font-semibold text-red-600">响应时间</span> ≤2小时
                                        </div>
                                        <button onclick="showLogin()" class="btn btn-primary">立即体验</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 价值主张 -->
                <section class="py-20 bg-gray-50">
                    <div class="container">
                        <div class="text-center mb-16 fade-in">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">为什么选择我们</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                专业的航材共享保障服务，助力航空产业降本增效
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div class="value-card slide-up">
                                <div class="value-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                    <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">极速响应</h3>
                                <p class="text-gray-600 mb-6">
                                    7×24小时在线服务，AOG紧急需求2小时内响应，平均交付时间缩短60%
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                        2小时响应
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="trending-up" class="w-4 h-4 mr-1"></i>
                                        效率提升60%
                                    </span>
                                </div>
                            </div>

                            <div class="value-card slide-up">
                                <div class="value-icon bg-gradient-to-br from-green-500 to-green-600">
                                    <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">品质保障</h3>
                                <p class="text-gray-600 mb-6">
                                    所有航材均具备完整适航证书，质量追溯体系完善，保险覆盖率100%
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="award" class="w-4 h-4 mr-1"></i>
                                        适航认证
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="umbrella" class="w-4 h-4 mr-1"></i>
                                        100%保险
                                    </span>
                                </div>
                            </div>

                            <div class="value-card slide-up">
                                <div class="value-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                    <i data-lucide="trending-down" class="w-8 h-8 text-white"></i>
                                </div>
                                <h3 class="subtitle-font text-xl text-gray-800 mb-4">成本优化</h3>
                                <p class="text-gray-600 mb-6">
                                    通过共享模式降低库存成本30%，智能匹配算法提升资源利用率
                                </p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                                    <span class="flex items-center">
                                        <i data-lucide="dollar-sign" class="w-4 h-4 mr-1"></i>
                                        成本降低30%
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="cpu" class="w-4 h-4 mr-1"></i>
                                        智能匹配
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 客户案例 -->
                <section class="py-20 bg-white">
                    <div class="container">
                        <div class="text-center mb-16 fade-in">
                            <h2 class="title-font text-4xl font-bold text-gray-800 mb-6">客户成功案例</h2>
                            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                                已为众多知名航空公司和企业提供专业的航材共享保障服务
                            </p>
                        </div>

                        <!-- 客户Logo墙 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-16">
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-600">中国国航</div>
                                    <div class="text-xs text-gray-500">Air China</div>
                                </div>
                            </div>
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-red-600">东方航空</div>
                                    <div class="text-xs text-gray-500">China Eastern</div>
                                </div>
                            </div>
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-blue-700">南方航空</div>
                                    <div class="text-xs text-gray-500">China Southern</div>
                                </div>
                            </div>
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-green-600">春秋航空</div>
                                    <div class="text-xs text-gray-500">Spring Airlines</div>
                                </div>
                            </div>
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-orange-600">吉祥航空</div>
                                    <div class="text-xs text-gray-500">Juneyao Airlines</div>
                                </div>
                            </div>
                            <div class="customer-logo bg-white rounded-xl p-6 shadow-sm border border-gray-200 flex items-center justify-center h-20">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-purple-600">海航集团</div>
                                    <div class="text-xs text-gray-500">HNA Group</div>
                                </div>
                            </div>
                        </div>

                        <!-- 成功案例详情 -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- 案例1 -->
                            <div class="case-card slide-up">
                                <div class="flex items-start space-x-4 mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="plane" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="subtitle-font text-xl text-gray-800 mb-2">东方航空 - AOG紧急响应</h3>
                                        <p class="text-sm text-gray-500">2024年3月 | 上海浦东机场</p>
                                    </div>
                                </div>
                                <div class="mb-6">
                                    <h4 class="font-semibold text-gray-800 mb-3">挑战</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        东航一架A320飞机在浦东机场出现发动机故障，急需CFM56发动机叶片进行更换，传统采购渠道需要7-10天时间。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">解决方案</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        通过中航材共享保障平台AOG紧急响应服务，1.5小时内定位到北京库存，6小时内完成航材调配和运输。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">成果</h4>
                                    <div class="case-metrics">
                                        <div class="case-metric">
                                            <div class="case-metric-value text-blue-600">1.5h</div>
                                            <div class="case-metric-label">响应时间</div>
                                        </div>
                                        <div class="case-metric">
                                            <div class="case-metric-value text-green-600">6h</div>
                                            <div class="case-metric-label">交付时间</div>
                                        </div>
                                        <div class="case-metric">
                                            <div class="case-metric-value text-purple-600">85%</div>
                                            <div class="case-metric-label">成本节省</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-t border-gray-200 pt-4">
                                    <p class="text-sm text-gray-600 italic">
                                        "中航材平台的AOG响应速度超出预期，帮助我们快速恢复航班运营，避免了巨大的经济损失。"
                                    </p>
                                    <p class="text-sm text-gray-500 mt-2">— 东方航空 维修工程部</p>
                                </div>
                            </div>

                            <!-- 案例2 -->
                            <div class="case-card slide-up">
                                <div class="flex items-start space-x-4 mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="rotate-cw" class="w-8 h-8 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="subtitle-font text-xl text-gray-800 mb-2">春秋航空 - 周转件租赁</h3>
                                        <p class="text-sm text-gray-500">2024年1-6月 | 全国网络</p>
                                    </div>
                                </div>
                                <div class="mb-6">
                                    <h4 class="font-semibold text-gray-800 mb-3">挑战</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        春秋航空机队快速扩张，传统的航材采购模式导致库存成本高企，资金占用严重，影响公司现金流。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">解决方案</h4>
                                    <p class="text-gray-600 text-sm mb-4">
                                        采用中航材平台的周转件租赁服务，按需租赁高价值航材，实现"以租代购"的轻资产运营模式。
                                    </p>
                                    <h4 class="font-semibold text-gray-800 mb-3">成果</h4>
                                    <div class="case-metrics">
                                        <div class="case-metric">
                                            <div class="case-metric-value text-blue-600">40%</div>
                                            <div class="case-metric-label">成本降低</div>
                                        </div>
                                        <div class="case-metric">
                                            <div class="case-metric-value text-green-600">¥2.8亿</div>
                                            <div class="case-metric-label">资金释放</div>
                                        </div>
                                        <div class="case-metric">
                                            <div class="case-metric-value text-purple-600">98.5%</div>
                                            <div class="case-metric-label">可用率</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-t border-gray-200 pt-4">
                                    <p class="text-sm text-gray-600 italic">
                                        "周转件租赁模式帮助我们大幅降低了库存成本，释放的资金用于机队扩张，实现了快速发展。"
                                    </p>
                                    <p class="text-sm text-gray-500 mt-2">— 春秋航空 运营管理部</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 立即开始 -->
                <section class="cta-section py-20">
                    <div class="container text-center">
                        <h2 class="title-font text-4xl font-bold mb-6 text-white">准备好开始了吗？</h2>
                        <p class="text-xl text-blue-100 mb-12">
                            加入中航材共享保障平台，体验智能化的航材管理服务
                        </p>

                        <div class="cta-steps">
                            <div class="cta-step step-indicator">
                                <div class="cta-step-icon">
                                    <i data-lucide="user-plus" class="w-8 h-8"></i>
                                </div>
                                <h3 class="cta-step-title">1. 注册账户</h3>
                                <p class="cta-step-desc">快速注册，5分钟完成认证</p>
                            </div>
                            <div class="cta-step step-indicator">
                                <div class="cta-step-icon">
                                    <i data-lucide="search" class="w-8 h-8"></i>
                                </div>
                                <h3 class="cta-step-title">2. 发布需求</h3>
                                <p class="cta-step-desc">智能匹配，精准找到所需航材</p>
                            </div>
                            <div class="cta-step">
                                <div class="cta-step-icon">
                                    <i data-lucide="check-circle" class="w-8 h-8"></i>
                                </div>
                                <h3 class="cta-step-title">3. 完成交易</h3>
                                <p class="cta-step-desc">安全交易，专业物流配送</p>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                            <button onclick="showLogin()" class="btn btn-primary btn-enhanced">
                                <i data-lucide="rocket" class="w-5 h-5 mr-2"></i>
                                立即注册试用
                            </button>
                            <button class="btn btn-secondary">
                                <i data-lucide="phone" class="w-5 h-5 mr-2"></i>
                                联系销售顾问
                            </button>
                        </div>

                        <div class="mt-8 text-sm text-blue-200">
                            <p>已有账户？<button onclick="showLogin()" class="text-white font-semibold hover:underline">立即登录</button></p>
                        </div>
                    </div>
                </section>

                <!-- 页脚 -->
                <footer class="footer">
                    <div class="container">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <!-- 公司信息 -->
                            <div class="md:col-span-2">
                                <div class="flex items-center space-x-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="title-font text-xl">中航材共享保障平台</h3>
                                        <p class="text-gray-400 text-sm">Aviation Material Sharing Platform</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 mb-6 max-w-md">
                                    专业的航材共享保障服务平台，为航空产业链提供智能化、数字化的航材管理解决方案。
                                </p>
                                <div class="flex space-x-4">
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="mail" class="w-5 h-5"></i>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="phone" class="w-5 h-5"></i>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                                        <i data-lucide="map-pin" class="w-5 h-5"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品服务 -->
                            <div class="footer-section">
                                <h4 class="footer-title">产品服务</h4>
                                <ul class="space-y-3 text-gray-400">
                                    <li><a href="#" class="footer-link">周转件服务</a></li>
                                    <li><a href="#" class="footer-link">消耗件保障</a></li>
                                    <li><a href="#" class="footer-link">AOG紧急响应</a></li>
                                    <li><a href="#" class="footer-link">库存管理</a></li>
                                    <li><a href="#" class="footer-link">质量管理</a></li>
                                </ul>
                            </div>

                            <!-- 支持帮助 -->
                            <div class="footer-section">
                                <h4 class="footer-title">支持帮助</h4>
                                <ul class="space-y-3 text-gray-400">
                                    <li><a href="#" class="footer-link">帮助中心</a></li>
                                    <li><a href="#" class="footer-link">API文档</a></li>
                                    <li><a href="#" class="footer-link">服务条款</a></li>
                                    <li><a href="#" class="footer-link">隐私政策</a></li>
                                    <li><a href="#" class="footer-link">联系我们</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row items-center justify-between">
                            <p class="text-gray-400 text-sm">
                                © 2025 中航材共享保障平台. 保留所有权利.
                            </p>
                            <div class="flex items-center space-x-6 mt-4 md:mt-0">
                                <span class="text-gray-400 text-sm">京ICP备12345678号</span>
                                <span class="text-gray-400 text-sm">京公网安备 11010802012345号</span>
                            </div>
                        </div>
                    </div>
                </footer>
            </main>
        </div>

        <!-- 登录页面（保持原有结构，添加增强样式） -->
        <div id="loginpage" class="page-content">
            <div class="min-h-screen hero-section flex items-center justify-center p-6">
                <!-- 登录卡片 -->
                <div class="relative w-full max-w-md">
                    <!-- 返回首页按钮 -->
                    <button onclick="showHomepage()" class="absolute -top-16 left-0 flex items-center space-x-2 text-white hover:text-blue-200 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                        <span>返回首页</span>
                    </button>

                    <div class="enhanced-card p-8">
                        <!-- Logo和标题 -->
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                </svg>
                            </div>
                            <h2 class="title-font text-2xl font-bold text-gray-800 mb-2">欢迎登录</h2>
                            <p class="text-gray-600">中航材共享保障平台</p>
                        </div>

                        <!-- 登录表单 -->
                        <form class="space-y-6">
                            <!-- 用户类型 -->
                            <div>
                                <label class="form-label">用户类型</label>
                                <div class="grid grid-cols-3 gap-3">
                                    <label class="relative">
                                        <input type="radio" name="userType" value="airline" class="sr-only peer" checked>
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center enhanced-card">
                                            <i data-lucide="plane" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">航空公司</div>
                                        </div>
                                    </label>
                                    <label class="relative">
                                        <input type="radio" name="userType" value="supplier" class="sr-only peer">
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center enhanced-card">
                                            <i data-lucide="building-2" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">中航材</div>
                                        </div>
                                    </label>
                                    <label class="relative">
                                        <input type="radio" name="userType" value="maintenance" class="sr-only peer">
                                        <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all text-center enhanced-card">
                                            <i data-lucide="wrench" class="w-5 h-5 mx-auto mb-1 text-gray-600 peer-checked:text-blue-600"></i>
                                            <div class="text-xs font-medium text-gray-700">维修企业</div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 用户名 -->
                            <div>
                                <label class="form-label">用户名</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-3 top-3 w-5 h-5 text-gray-400"></i>
                                    <input type="text" class="form-input pl-10" placeholder="请输入用户名">
                                </div>
                            </div>

                            <!-- 密码 -->
                            <div>
                                <label class="form-label">密码</label>
                                <div class="relative">
                                    <i data-lucide="lock" class="absolute left-3 top-3 w-5 h-5 text-gray-400"></i>
                                    <input type="password" class="form-input pl-10" placeholder="请输入密码">
                                </div>
                            </div>

                            <!-- 登录按钮 -->
                            <button type="button" onclick="showMainApp()" class="w-full btn btn-primary btn-enhanced py-3 text-lg">
                                登录
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主应用界面（保持原有结构） -->
        <div id="mainapp" class="page-content">
            <!-- 这里保持原有的主应用界面结构 -->
            <div class="text-center py-20">
                <h2 class="title-font text-3xl font-bold text-gray-800 mb-4">主应用界面</h2>
                <p class="text-gray-600 mb-8">这里是主应用的内容区域</p>
                <button onclick="showHomepage()" class="btn btn-primary">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    返回首页
                </button>
            </div>
        </div>
    </div>
</body>
</html>