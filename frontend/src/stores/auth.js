import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || '')
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!token.value
  })

  // 角色权限映射配置
  const ROLE_PERMISSIONS = {
    'airline_buyer': [
      'publish_demand', 'manage_own_inventory', 'publish_shared', 
      'manage_orders', 'view_own_data', 'aog_request'
    ],
    'platform_staff': [
      'process_all_orders', 'cross_company_access', 'audit_shared_materials',
      'publish_demand', 'manage_inventory', 'publish_shared', 'manage_orders',
      'view_all_data', 'customer_service', 'data_analysis', 'user_management'
    ],
    'maintenance_engineer': [
      'update_maintenance_status', 'technical_support', 
      'view_maintenance_data', 'manage_work_orders', 'view_own_data'
    ],
    'logistics_specialist': [
      'track_shipment', 'update_logistics', 
      'manage_delivery', 'view_logistics_data', 'view_own_data'
    ],
    'admin': [
      'system_config', 'data_analysis', 'user_management',
      'all_permissions'  // 管理员具有所有权限
    ],
    // 向后兼容的旧角色
    'airline': ['publish_demand', 'manage_own_inventory', 'manage_orders', 'view_own_data'],
    'supplier': ['manage_own_inventory', 'publish_shared', 'manage_orders', 'view_own_data'],
    'maintenance': ['update_maintenance_status', 'technical_support', 'view_maintenance_data']
  }

  // 权限等级配置
  const PERMISSION_LEVELS = {
    1: ['basic_features'],
    2: ['advanced_features', 'priority_support'],
    3: ['admin_features', 'user_management'],
    4: ['super_admin', 'system_config', 'all_permissions']
  }

  // 获取用户权限列表
  const userPermissions = computed(() => {
    if (!user.value) return []
    
    // 获取基础角色权限
    const basePermissions = ROLE_PERMISSIONS[user.value.user_type] || []
    
    // 根据权限等级添加额外权限
    const levelPermissions = []
    const userLevel = user.value.permission_level || 1
    
    for (let level = 1; level <= userLevel; level++) {
      levelPermissions.push(...(PERMISSION_LEVELS[level] || []))
    }
    
    // 合并权限并去重
    return [...new Set([...basePermissions, ...levelPermissions])]
  })

  // 检查用户是否具有特定权限
  const hasPermission = (permission) => {
    if (!user.value) return false
    const permissions = userPermissions.value
    return permissions.includes(permission) || permissions.includes('all_permissions')
  }

  // 检查用户是否具有任一权限
  const hasAnyPermission = (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.some(permission => hasPermission(permission))
  }

  // 检查用户是否具有全部权限
  const hasAllPermissions = (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.every(permission => hasPermission(permission))
  }

  // 检查用户角色
  const hasRole = (roles) => {
    if (!user.value) return false
    if (typeof roles === 'string') {
      return user.value.user_type === roles
    }
    if (Array.isArray(roles)) {
      return roles.includes(user.value.user_type)
    }
    return false
  }

  // 检查是否为管理员
  const isAdmin = computed(() => {
    return user.value && (user.value.user_type === 'admin' || user.value.permission_level >= 3)
  })

  // 检查是否为内部员工
  const isInternalStaff = computed(() => {
    return user.value && user.value.company_type === 'internal'
  })

  // 检查是否可以跨公司访问
  const canCrossCompanyAccess = computed(() => {
    if (!user.value) return false
    return isInternalStaff.value || user.value.user_type === 'platform_staff' || isAdmin.value
  })

  // 操作
  const login = async (credentials) => {
    // 清理旧数据
    token.value = ''
    refreshToken.value = ''
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    
    try {
      const response = await authApi.login(credentials)
      
      // API返回的数据在body字段中
      const { access_token, refresh_token, user: userData } = response.body

      // 设置新的数据
      token.value = access_token
      refreshToken.value = refresh_token
      user.value = userData

      // 保存到localStorage
      localStorage.setItem('token', access_token)
      localStorage.setItem('refreshToken', refresh_token)
      localStorage.setItem('user', JSON.stringify(userData))

      return { success: true }
    } catch (error) {
      console.error('Login failed:', error)
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || '登录失败' 
      }
    }
  }

  const logout = async () => {
    try {
      // 只有在有token的情况下才调用logout API
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      // 即使API失败也要清理本地状态
      console.error('Logout API failed, cleaning local state:', error)
    } finally {
      // 清空状态
      token.value = ''
      refreshToken.value = ''
      user.value = null

      // 清空localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
    }
  }

  const refreshTokens = async () => {
    try {
      // 检查是否有refresh token
      if (!refreshToken.value) {
        return false
      }

      const response = await authApi.refreshToken(refreshToken.value)

      // API返回的数据在body字段中
      const { access_token } = response.body

      token.value = access_token
      localStorage.setItem('token', access_token)

      return true
    } catch (error) {
      console.error('Token refresh failed:', error)

      // 刷新失败时，只清理本地状态，不调用logout API
      token.value = ''
      refreshToken.value = ''
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      return false
    }
  }

  const reloadFromStorage = () => {
    token.value = localStorage.getItem('token') || ''
    refreshToken.value = localStorage.getItem('refreshToken') || ''
    user.value = JSON.parse(localStorage.getItem('user') || 'null')
  }

  return {
    token,
    refreshToken,
    user,
    isAuthenticated,
    userPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isAdmin,
    isInternalStaff,
    canCrossCompanyAccess,
    login,
    logout,
    refreshTokens,
    reloadFromStorage
  }
})