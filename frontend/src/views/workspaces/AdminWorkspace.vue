<!--
系统管理员专属工作台
版本: 1.0
创建时间: 2025-07-22

专为系统管理员设计的高权限管理工作台
特点：
1. 权限管理中心
2. 系统配置管理
3. 高级数据分析
4. 安全监控面板
5. 数据备份管理
-->

<template>
  <div class="admin-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            系统管理中心
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.username }}
          </p>
          <p class="text-white/80 mt-2 flex items-center">
            <Star class="w-4 h-4 mr-2" />
            权限等级: {{ userStore.user?.permission_level }} {{ getSuperAdminBadge() }}
          </p>
        </div>
        <div class="welcome-stats hidden lg:flex space-x-8">
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.systemHealth || '98' }}%</div>
            <div class="text-white/80 text-sm">系统健康</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.onlineUsers || 0 }}</div>
            <div class="text-white/80 text-sm">在线用户</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.todayOperations || 0 }}</div>
            <div class="text-white/80 text-sm">今日操作</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧管理面板 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 权限管理面板 -->
        <div class="permission-management" v-if="canManagePermissions">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">权限管理中心</h2>
            <el-badge :value="pendingPermissionRequests" :max="99" class="badge-warning">
              <el-button size="small" type="primary" @click="handlePermissionManagement">
                管理权限
              </el-button>
            </el-badge>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- 角色权限配置 -->
            <div class="management-card" @click="handleRolePermissions">
              <div class="management-icon bg-purple-100 text-purple-600">
                <User class="w-6 h-6" />
              </div>
              <h3 class="management-title">角色权限</h3>
              <p class="management-desc">配置系统角色和权限矩阵</p>
              <div class="management-stats">
                <span class="text-sm text-purple-600">{{ stats.totalRoles || 5 }} 个角色</span>
              </div>
            </div>

            <!-- 用户权限审计 -->
            <div class="management-card" @click="handlePermissionAudit">
              <div class="management-icon bg-blue-100 text-blue-600">
                <Search class="w-6 h-6" />
              </div>
              <h3 class="management-title">权限审计</h3>
              <p class="management-desc">查看权限变更历史</p>
              <div class="management-stats">
                <span class="text-sm text-blue-600">{{ stats.todayAudits || 0 }} 条记录</span>
              </div>
            </div>

            <!-- 临时权限管理 -->
            <div class="management-card" @click="handleTempPermissions">
              <div class="management-icon bg-orange-100 text-orange-600">
                <Clock class="w-6 h-6" />
              </div>
              <h3 class="management-title">临时权限</h3>
              <p class="management-desc">管理临时权限授权</p>
              <div class="management-stats">
                <span class="text-sm text-orange-600">{{ stats.activeTemp || 0 }} 个活跃</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统配置管理 -->
        <div class="system-config">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">系统配置管理</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 应用配置 -->
            <div class="config-card" @click="handleAppConfig">
              <div class="config-icon bg-primary-100 text-primary-600">
                <Setting class="w-5 h-5" />
              </div>
              <div class="config-content">
                <h3 class="config-title">应用配置</h3>
                <p class="config-desc">基础系统参数设置</p>
              </div>
            </div>

            <!-- 数据库配置 (仅超级管理员) -->
            <div 
              class="config-card" 
              v-if="isSuperAdmin" 
              @click="handleDatabaseConfig"
            >
              <div class="config-icon bg-red-100 text-red-600">
                <House class="w-5 h-5" />
              </div>
              <div class="config-content">
                <h3 class="config-title">数据库配置</h3>
                <p class="config-desc">数据库连接和性能</p>
              </div>
            </div>

            <!-- API配置 -->
            <div class="config-card" @click="handleApiConfig">
              <div class="config-icon bg-green-100 text-green-600">
                <Tools class="w-5 h-5" />
              </div>
              <div class="config-content">
                <h3 class="config-title">API管理</h3>
                <p class="config-desc">接口权限和限流</p>
              </div>
            </div>

            <!-- 安全配置 -->
            <div class="config-card" @click="handleSecurityConfig">
              <div class="config-icon bg-yellow-100 text-yellow-600">
                <Lock class="w-5 h-5" />
              </div>
              <div class="config-content">
                <h3 class="config-title">安全配置</h3>
                <p class="config-desc">安全策略和规则</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据管理中心 (仅超级管理员) -->
        <div class="data-management" v-if="isSuperAdmin">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">数据管理中心</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 数据备份 -->
            <div class="data-card backup" @click="handleDataBackup">
              <div class="data-icon">
                <FolderOpened class="w-6 h-6" />
              </div>
              <h3 class="data-title">数据备份</h3>
              <p class="data-desc">创建和管理数据备份</p>
              <div class="data-status">
                <span class="status-dot bg-green-500"></span>
                <span class="status-text">上次备份: {{ lastBackupTime }}</span>
              </div>
            </div>

            <!-- 数据恢复 -->
            <div class="data-card recovery" @click="handleDataRecovery">
              <div class="data-icon">
                <Refresh class="w-6 h-6" />
              </div>
              <h3 class="data-title">数据恢复</h3>
              <p class="data-desc">从备份恢复数据</p>
              <div class="data-status">
                <span class="status-dot bg-blue-500"></span>
                <span class="status-text">{{ stats.backupCount || 0 }} 个备份可用</span>
              </div>
            </div>

            <!-- 数据清理 -->
            <div class="data-card cleanup" @click="handleDataCleanup">
              <div class="data-icon">
                <Delete class="w-6 h-6" />
              </div>
              <h3 class="data-title">数据清理</h3>
              <p class="data-desc">清理过期和垃圾数据</p>
              <div class="data-status">
                <span class="status-dot bg-orange-500"></span>
                <span class="status-text">{{ stats.cleanableData || 0 }} MB 可清理</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 高级分析面板 -->
        <div class="advanced-analytics">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">高级数据分析</h2>
          
          <div class="analytics-tabs">
            <el-tabs v-model="activeAnalyticsTab" @tab-change="switchAnalyticsTab">
              <el-tab-pane label="用户分析" name="users">
                <div class="analytics-content">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="chart-card">
                      <h4 class="chart-title">用户增长趋势</h4>
                      <div class="chart-container">
                        <canvas ref="userGrowthChart" width="400" height="200"></canvas>
                      </div>
                    </div>
                    <div class="chart-card">
                      <h4 class="chart-title">用户活跃度分布</h4>
                      <div class="chart-container">
                        <canvas ref="userMonitorChart" width="400" height="200"></canvas>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="系统性能" name="performance">
                <div class="analytics-content">
                  <div class="performance-metrics grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="metric-card">
                      <div class="metric-value">{{ systemMetrics.avgResponseTime || 0 }}ms</div>
                      <div class="metric-label">平均响应时间</div>
                    </div>
                    <div class="metric-card">
                      <div class="metric-value">{{ systemMetrics.errorRate || 0 }}%</div>
                      <div class="metric-label">错误率</div>
                    </div>
                    <div class="metric-card">
                      <div class="metric-value">{{ systemMetrics.throughput || 0 }}/s</div>
                      <div class="metric-label">吞吐量</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="业务指标" name="business">
                <div class="analytics-content">
                  <div class="business-stats grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="business-card">
                      <div class="business-value">¥{{ formatCurrency(businessMetrics.revenue) }}</div>
                      <div class="business-label">总收入</div>
                      <div class="business-trend positive">+12.3%</div>
                    </div>
                    <div class="business-card">
                      <div class="business-value">{{ businessMetrics.transactions || 0 }}</div>
                      <div class="business-label">交易笔数</div>
                      <div class="business-trend positive">+8.7%</div>
                    </div>
                    <div class="business-card">
                      <div class="business-value">{{ businessMetrics.activeUsers || 0 }}</div>
                      <div class="business-label">活跃用户</div>
                      <div class="business-trend negative">-2.1%</div>
                    </div>
                    <div class="business-card">
                      <div class="business-value">{{ businessMetrics.satisfaction || 0 }}%</div>
                      <div class="business-label">满意度</div>
                      <div class="business-trend positive">+5.2%</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 安全监控面板 -->
        <div class="security-monitor modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Warning class="w-5 h-5 mr-2 text-red-500" />
            安全监控
          </h3>
          <div class="space-y-4">
            <div class="security-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">异常登录</span>
                <span class="text-sm font-medium text-red-600">{{ securityStats.suspiciousLogins || 0 }}</span>
              </div>
            </div>
            <div class="security-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">API异常调用</span>
                <span class="text-sm font-medium text-orange-600">{{ securityStats.anomalousApiCalls || 0 }}</span>
              </div>
            </div>
            <div class="security-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">权限越级尝试</span>
                <span class="text-sm font-medium text-red-600">{{ securityStats.privilegeEscalation || 0 }}</span>
              </div>
            </div>
          </div>
          <el-button size="small" type="danger" class="w-full mt-4" @click="handleSecurityDetails">
            查看详细报告
          </el-button>
        </div>

        <!-- 系统性能面板 -->
        <div class="system-performance modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">系统性能</h3>
          <div class="space-y-4">
            <div class="performance-item">
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm text-gray-600">CPU 使用率</span>
                <span class="text-sm font-medium">{{ systemStatus.cpu || 0 }}%</span>
              </div>
              <el-progress 
                :percentage="systemStatus.cpu || 0" 
                :show-text="false" 
                :stroke-width="6"
                :color="getPerformanceColor(systemStatus.cpu)"
              />
            </div>
            
            <div class="performance-item">
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm text-gray-600">内存使用率</span>
                <span class="text-sm font-medium">{{ systemStatus.memory || 0 }}%</span>
              </div>
              <el-progress 
                :percentage="systemStatus.memory || 0" 
                :show-text="false" 
                :stroke-width="6"
                :color="getPerformanceColor(systemStatus.memory)"
              />
            </div>
            
            <div class="performance-item">
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm text-gray-600">数据库连接</span>
                <span class="text-sm font-medium">{{ systemStatus.dbConnections || 0 }}/100</span>
              </div>
              <el-progress 
                :percentage="(systemStatus.dbConnections || 0)" 
                :show-text="false" 
                :stroke-width="6"
                :color="getPerformanceColor(systemStatus.dbConnections)"
              />
            </div>
          </div>
        </div>

        <!-- 操作审计日志 -->
        <div class="audit-log modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">操作审计</h3>
          <div class="audit-list space-y-3 max-h-64 overflow-y-auto">
            <div 
              v-for="log in auditLogs" 
              :key="log.id"
              class="audit-item p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium">{{ log.operation }}</span>
                <span class="text-xs text-gray-500">{{ log.time }}</span>
              </div>
              <div class="text-xs text-gray-600">
                {{ log.user }} • {{ log.details }}
              </div>
            </div>
          </div>
          <el-button size="small" text type="primary" class="w-full mt-3" @click="handleViewAllAudits">
            查看全部审计日志
          </el-button>
        </div>

        <!-- 快速操作工具 -->
        <div class="quick-tools modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速操作</h3>
          <div class="space-y-3">
            <button class="quick-tool-btn" @click="handleEmergencyMaintenance">
              <Warning class="w-4 h-4" />
              <span>紧急维护模式</span>
            </button>
            <button class="quick-tool-btn" @click="handleSystemRestart">
              <Refresh class="w-4 h-4" />
              <span>重启服务</span>
            </button>
            <button class="quick-tool-btn" @click="handleClearCache">
              <Delete class="w-4 h-4" />
              <span>清理缓存</span>
            </button>
            <button class="quick-tool-btn" @click="handleExportLogs">
              <Download class="w-4 h-4" />
              <span>导出日志</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Box, User, Search, Clock, Setting, House, 
  Tools, Lock, FolderOpened, Refresh, Delete, Warning, 
  Download, Star
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { roleName } = useRoleWorkspace()

// 响应式数据
const stats = ref({})
const pendingPermissionRequests = ref(3)
const systemStatus = ref({})
const systemMetrics = ref({})
const businessMetrics = ref({})
const securityStats = ref({})
const auditLogs = ref([])
const lastBackupTime = ref('2小时前')
const activeAnalyticsTab = ref('users')

// 图表引用
const userGrowthChart = ref(null)
const userMonitorChart = ref(null)

// 计算属性
const welcomeStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, #7c3aed, #2563eb)`
  }
})

const canManagePermissions = computed(() => {
  return userStore.user?.permission_level >= 3
})

const isSuperAdmin = computed(() => {
  return userStore.user?.permission_level >= 4
})

const getSuperAdminBadge = () => {
  if (userStore.user?.permission_level >= 4) {
    return '(超级管理员)'
  }
  return '(管理员)'
}

// 方法
const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const getPerformanceColor = (percentage) => {
  if (percentage < 60) return '#52c41a'
  if (percentage < 80) return '#faad14'
  return '#ff4d4f'
}

// 权限管理相关
const handlePermissionManagement = () => {
  router.push('/app/admin/permissions')
}

const handleRolePermissions = () => {
  router.push('/app/admin/roles')
}

const handlePermissionAudit = () => {
  router.push('/app/admin/audit')
}

const handleTempPermissions = () => {
  router.push('/app/admin/temp-permissions')
}

// 系统配置相关
const handleAppConfig = () => {
  router.push('/app/admin/config/app')
}

const handleDatabaseConfig = () => {
  if (!isSuperAdmin.value) {
    ElMessage.warning('仅超级管理员可访问数据库配置')
    return
  }
  router.push('/app/admin/config/database')
}

const handleApiConfig = () => {
  router.push('/app/admin/config/api')
}

const handleSecurityConfig = () => {
  router.push('/app/admin/config/security')
}

// 数据管理相关
const handleDataBackup = async () => {
  try {
    await ElMessageBox.confirm('确认要创建数据备份吗？', '数据备份', {
      type: 'warning'
    })
    ElMessage.success('数据备份已开始，请稍后查看结果')
  } catch {
    // 用户取消
  }
}

const handleDataRecovery = () => {
  router.push('/app/admin/data-recovery')
}

const handleDataCleanup = async () => {
  try {
    await ElMessageBox.confirm('确认要执行数据清理吗？此操作不可逆。', '数据清理', {
      type: 'warning'
    })
    ElMessage.success('数据清理任务已启动')
  } catch {
    // 用户取消
  }
}

// 分析面板相关
const switchAnalyticsTab = (tabName) => {
  nextTick(() => {
    if (tabName === 'users') {
      initUserCharts()
    }
  })
}

const initUserCharts = () => {
  // 这里将来会初始化Chart.js图表
  console.log('初始化用户分析图表')
}

// 安全监控相关
const handleSecurityDetails = () => {
  router.push('/app/admin/security/reports')
}

// 审计日志相关
const handleViewAllAudits = () => {
  router.push('/app/admin/audit-logs')
}

// 快速操作相关
const handleEmergencyMaintenance = async () => {
  try {
    await ElMessageBox.confirm('确认要启用紧急维护模式吗？这将影响所有用户的正常使用。', '紧急维护', {
      type: 'warning'
    })
    ElMessage.success('紧急维护模式已启用')
  } catch {
    // 用户取消
  }
}

const handleSystemRestart = async () => {
  try {
    await ElMessageBox.confirm('确认要重启系统服务吗？这将导致短暂的服务中断。', '系统重启', {
      type: 'error'
    })
    ElMessage.success('系统重启指令已发送')
  } catch {
    // 用户取消
  }
}

const handleClearCache = () => {
  ElMessage.success('缓存清理完成')
}

const handleExportLogs = () => {
  ElMessage.success('日志导出已开始，完成后将通知您')
}

// 初始化工作台数据
const initializeWorkspace = async () => {
  try {
    // 加载基础统计数据
    stats.value = {
      systemHealth: 98,
      onlineUsers: 156,
      todayOperations: 234,
      totalRoles: 5,
      todayAudits: 45,
      activeTemp: 3,
      backupCount: 12,
      cleanableData: 245
    }
    
    // 加载系统状态
    systemStatus.value = {
      cpu: 35,
      memory: 68,
      dbConnections: 25
    }
    
    // 加载系统性能指标
    systemMetrics.value = {
      avgResponseTime: 185,
      errorRate: 0.3,
      throughput: 450
    }
    
    // 加载业务指标
    businessMetrics.value = {
      revenue: 2350000,
      transactions: 1245,
      activeUsers: 892,
      satisfaction: 94
    }
    
    // 加载安全统计
    securityStats.value = {
      suspiciousLogins: 2,
      anomalousApiCalls: 5,
      privilegeEscalation: 0
    }
    
    // 加载审计日志
    auditLogs.value = [
      {
        id: 1,
        operation: '用户权限变更',
        user: 'admin',
        details: '修改了用户张三的权限级别',
        time: '10分钟前'
      },
      {
        id: 2,
        operation: '系统配置更新',
        user: 'admin',
        details: '更新了API限流配置',
        time: '25分钟前'
      },
      {
        id: 3,
        operation: '数据备份',
        user: 'system',
        details: '执行了定时数据备份任务',
        time: '2小时前'
      }
    ]
    
  } catch (error) {
    console.error('管理员工作台初始化失败:', error)
    ElMessage.error('工作台数据加载失败')
  }
}

onMounted(() => {
  initializeWorkspace()
  
  // 初始化用户图表
  nextTick(() => {
    if (activeAnalyticsTab.value === 'users') {
      initUserCharts()
    }
  })
})
</script>

<style scoped>
.admin-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.permission-management {
  @apply modern-card p-6;
}

.management-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.management-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.management-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.management-desc {
  @apply text-gray-600 text-sm mb-3;
}

.management-stats {
  @apply text-xs;
}

.system-config {
  @apply modern-card p-6;
}

.config-card {
  @apply modern-card p-4 cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 flex items-center space-x-3;
}

.config-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0;
}

.config-title {
  @apply font-semibold text-gray-800;
}

.config-desc {
  @apply text-sm text-gray-600;
}

.data-management {
  @apply modern-card p-6;
}

.data-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 text-center;
}

.data-icon {
  @apply w-12 h-12 mx-auto mb-4 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center;
}

.data-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.data-desc {
  @apply text-gray-600 text-sm mb-4;
}

.data-status {
  @apply flex items-center justify-center space-x-2;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-text {
  @apply text-xs text-gray-500;
}

.advanced-analytics {
  @apply modern-card p-6;
}

.analytics-content {
  @apply mt-4;
}

.chart-card {
  @apply modern-card p-4;
}

.chart-title {
  @apply text-base font-semibold text-gray-800 mb-3;
}

.chart-container {
  @apply h-48 flex items-center justify-center text-gray-400;
}

.performance-metrics,
.business-stats {
  @apply mt-4;
}

.metric-card,
.business-card {
  @apply modern-card p-4 text-center;
}

.metric-value,
.business-value {
  @apply text-2xl font-bold text-primary-600;
}

.metric-label,
.business-label {
  @apply text-sm text-gray-600 mt-1;
}

.business-trend {
  @apply text-xs font-medium mt-1;
}

.business-trend.positive {
  @apply text-green-600;
}

.business-trend.negative {
  @apply text-red-600;
}

.security-monitor {
  @apply space-y-4;
}

.security-item {
  @apply py-2;
}

.system-performance {
  @apply space-y-4;
}

.performance-item {
  @apply space-y-2;
}

.audit-log {
  @apply space-y-4;
}

.audit-item {
  @apply transition-colors hover:bg-gray-100;
}

.quick-tools {
  @apply space-y-3;
}

.quick-tool-btn {
  @apply w-full flex items-center space-x-3 p-3 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors;
}
</style>