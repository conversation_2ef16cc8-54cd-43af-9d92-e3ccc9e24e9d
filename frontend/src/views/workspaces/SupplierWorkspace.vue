<!--
供应商专属工作台
版本: 1.0
创建时间: 2025-07-22

专为航材供应商（制造商）设计的纯销售工作台
角色特点：
1. 只销售，不采购
2. 专注产品管理和销售订单
3. 库存共享发布
4. 客户询价响应
5. 销售数据统计
-->

<template>
  <div class="supplier-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            欢迎回来，{{ userStore.user?.username }}
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.company_name || '航材制造商' }}
          </p>
          <p class="text-white/80 mt-2">
            管理您的产品销售，响应客户询价，实现航材高效销售
          </p>
        </div>
        <div class="welcome-stats hidden md:flex space-x-6">
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.totalProducts || 0 }}</div>
            <div class="text-white/80 text-sm">在售产品</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.activeOrders || 0 }}</div>
            <div class="text-white/80 text-sm">活跃订单</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">¥{{ formatCurrency(stats.monthlyRevenue) }}</div>
            <div class="text-white/80 text-sm">本月收益</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧主要操作区 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 核心销售功能 -->
        <div class="sales-functions">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">销售中心</h2>
            <div class="flex space-x-2">
              <el-tag type="success" size="small">{{ stats.onlineProducts || 0 }} 个在线产品</el-tag>
              <el-tag type="warning" size="small">{{ stats.pendingInquiries || 0 }} 个待回复询价</el-tag>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 产品管理 -->
            <div class="action-card primary" @click="handleProductManagement">
              <div class="action-icon">
                <Box class="w-6 h-6" />
              </div>
              <h3 class="action-title">产品管理</h3>
              <p class="action-desc">管理产品目录和库存信息</p>
              <div class="action-stats">
                <span class="text-sm text-primary-600">{{ stats.totalProducts || 0 }} 个产品在售</span>
              </div>
            </div>

            <!-- 销售订单 -->
            <div class="action-card success" @click="handleSalesOrders">
              <div class="action-icon">
                <ShoppingCart class="w-6 h-6" />
              </div>
              <h3 class="action-title">销售订单</h3>
              <p class="action-desc">查看和处理销售订单</p>
              <div class="action-stats">
                <span class="text-sm text-success-600">{{ stats.pendingOrders || 0 }} 个待处理</span>
              </div>
            </div>

            <!-- 库存管理 -->
            <div class="action-card blue" @click="handleInventoryManagement">
              <div class="action-icon">
                <Folder class="w-6 h-6" />
              </div>
              <h3 class="action-title">库存管理</h3>
              <p class="action-desc">监控库存状态和预警</p>
              <div class="action-stats">
                <span class="text-sm text-blue-600">{{ stats.inventoryAlerts || 0 }} 个库存预警</span>
              </div>
            </div>

            <!-- 客户询价 -->
            <div class="action-card warning" @click="handleCustomerInquiries">
              <div class="action-icon">
                <Message class="w-6 h-6" />
              </div>
              <h3 class="action-title">客户询价</h3>
              <p class="action-desc">响应客户的产品询价</p>
              <div class="action-stats">
                <span class="text-sm text-warning-600">{{ stats.newInquiries || 0 }} 个新询价</span>
              </div>
            </div>

            <!-- 共享件发布 -->
            <div class="action-card purple" @click="handlePublishSharedMaterials">
              <div class="action-icon">
                <Share class="w-6 h-6" />
              </div>
              <h3 class="action-title">共享件发布</h3>
              <p class="action-desc">将富余库存发布为共享件</p>
              <div class="action-stats">
                <span class="text-sm text-purple-600">{{ stats.sharedItems || 0 }} 个已发布</span>
              </div>
            </div>

            <!-- 销售统计 -->
            <div class="action-card orange" @click="handleSalesAnalytics">
              <div class="action-icon">
                <DataAnalysis class="w-6 h-6" />
              </div>
              <h3 class="action-title">销售统计</h3>
              <p class="action-desc">查看销售业绩和趋势分析</p>
              <div class="action-stats">
                <span class="text-sm text-orange-600">本月增长 {{ stats.growthRate || 0 }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activities">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">最近活动</h2>
            <el-button text type="primary" @click="viewAllActivities">
              查看全部
              <Refresh class="w-4 h-4 ml-1" />
            </el-button>
          </div>
          
          <div class="activity-list space-y-4">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.iconColor">
                <component :is="activity.icon" class="w-4 h-4" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-action">
                <el-button 
                  v-if="activity.actionable" 
                  size="small" 
                  text 
                  type="primary"
                  @click="handleMonitorAction(activity)"
                >
                  {{ activity.actionText }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 业务概览卡片 -->
        <div class="overview-cards grid grid-cols-2 gap-4">
          <div class="overview-card">
            <div class="overview-value text-primary-600">{{ stats.onlineProducts || 0 }}</div>
            <div class="overview-label">在线产品</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-success-600">{{ stats.activeOrders || 0 }}</div>
            <div class="overview-label">活跃订单</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-warning-600">{{ stats.pendingInquiries || 0 }}</div>
            <div class="overview-label">待回复询价</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-blue-600">{{ stats.completedOrders || 0 }}</div>
            <div class="overview-label">已完成订单</div>
          </div>
        </div>

        <!-- 销售业绩趋势 -->
        <div class="revenue-chart modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">销售业绩趋势</h3>
          <div class="chart-placeholder">
            <div class="chart-bars flex items-end justify-between space-x-2 h-32">
              <div 
                v-for="(value, index) in revenueData" 
                :key="index"
                class="chart-bar bg-gradient-to-t from-green-500 to-green-400 rounded-t"
                :style="{ height: `${(value / Math.max(...revenueData)) * 100}%`, minHeight: '8px' }"
              ></div>
            </div>
            <div class="chart-labels flex justify-between text-xs text-gray-500 mt-2">
              <span v-for="month in ['1月', '2月', '3月', '4月', '5月', '6月']" :key="month">
                {{ month }}
              </span>
            </div>
          </div>
        </div>

        <!-- 热门产品排行 -->
        <div class="hot-products modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">热门产品排行</h3>
          <div class="space-y-3">
            <div 
              v-for="(product, index) in hotProducts" 
              :key="product.id"
              class="hot-product-item flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div class="ranking" :class="getRankingClass(index)">{{ index + 1 }}</div>
                <div>
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-sales text-xs text-gray-500">销量 {{ product.sales }}</div>
                </div>
              </div>
              <div class="product-revenue text-sm font-semibold text-green-600">
                ¥{{ formatCurrency(product.revenue) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速链接</h3>
          <div class="space-y-3">
            <a href="#" class="quick-link">
              <Document class="w-4 h-4" />
              <span>产品发布指南</span>
            </a>
            <a href="#" class="quick-link">
              <Download class="w-4 h-4" />
              <span>销售业务帮助</span>
            </a>
            <a href="#" class="quick-link">
              <Tools class="w-4 h-4" />
              <span>客服支持</span>
            </a>
            <a href="#" class="quick-link">
              <Setting class="w-4 h-4" />
              <span>账户设置</span>
            </a>
          </div>
        </div>

        <!-- AOG紧急响应 -->
        <div class="aog-panel modern-card p-6 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <Warning class="w-4 h-4 text-red-600" />
            </div>
            <h3 class="text-lg font-semibold text-red-800">AOG紧急响应</h3>
          </div>
          <p class="text-red-700 text-sm mb-4">
            客户飞机停场紧急情况？提供24/7快速供货服务
          </p>
          <el-button type="danger" size="small" class="w-full" @click="handleAOGResponse">
            <Warning class="w-4 h-4 mr-2" />
            响应AOG需求
          </el-button>
        </div>
      </div>
    </div>

    <!-- 发布共享件弹窗 -->
    <SharedMaterialPublishForm v-model="showSharedDialog" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Box, ShoppingCart, Folder, Message, Share, DataAnalysis,
  Document, Download, Tools, Setting, Warning, Refresh
} from '@element-plus/icons-vue'
import SharedMaterialPublishForm from '@/components/SharedMaterialPublishForm.vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const showSharedDialog = ref(false)
const stats = ref({})
const recentActivities = ref([])
const revenueData = ref([180, 220, 280, 320, 380, 420]) // 销售增长趋势
const hotProducts = ref([
  { id: 1, name: 'CFM56发动机部件', sales: 156, revenue: 2340000 },
  { id: 2, name: '航电系统模块', sales: 89, revenue: 1560000 },
  { id: 3, name: '液压系统组件', sales: 67, revenue: 890000 },
  { id: 4, name: '起落架部件', sales: 45, revenue: 670000 },
  { id: 5, name: '燃油系统部件', sales: 34, revenue: 540000 }
])

// 计算属性
const welcomeStyle = computed(() => {
  // 使用绿色主题 - 供应商销售主题
  return {
    background: 'linear-gradient(135deg, #10b981, #059669)'
  }
})

// 方法
const handleProductManagement = () => {
  router.push('/app/inventory?view=products')
}

const handleSalesOrders = () => {
  router.push('/app/orders?type=sales')
}

const handleInventoryManagement = () => {
  router.push('/app/inventory')
}

const handleCustomerInquiries = () => {
  router.push('/app/inquiries')
}

const handlePublishSharedMaterials = () => {
  showSharedDialog.value = true
}

const handleSalesAnalytics = () => {
  router.push('/app/analytics?focus=sales')
}

const handleAOGResponse = () => {
  ElMessage.warning('正在准备AOG紧急供货响应...')
  // 这里可以打开AOG专用对话框或跳转到AOG响应页面
}

const viewAllActivities = () => {
  router.push('/app/activities')
}

const handleMonitorAction = (activity) => {
  ElMessage.info(`处理活动: ${activity.title}`)
  // 根据活动类型执行相应操作
}

const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const getRankingClass = (index) => {
  if (index === 0) return 'bg-yellow-500 text-white'
  if (index === 1) return 'bg-gray-400 text-white'
  if (index === 2) return 'bg-amber-600 text-white'
  return 'bg-gray-200 text-gray-700'
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载供应商统计数据
    const supplierStats = await getWorkspaceStats('supplierStats')
    stats.value = supplierStats || {
      totalProducts: 245,
      onlineProducts: 198,
      activeOrders: 34,
      pendingOrders: 12,
      pendingInquiries: 8,
      newInquiries: 3,
      inventoryAlerts: 5,
      sharedItems: 23,
      completedOrders: 156,
      monthlyRevenue: 3480000,
      growthRate: 15.6
    }
    
    // 加载最近活动数据
    recentActivities.value = [
      {
        id: 1,
        title: '新订单确认',
        description: 'CFM56发动机部件订单已确认，金额 ¥234,000',
        time: '1小时前',
        icon: ShoppingCart,
        iconColor: 'bg-green-500',
        actionable: true,
        actionText: '查看订单'
      },
      {
        id: 2,
        title: '客户询价',
        description: '东航询价液压系统组件，数量50套',
        time: '2小时前',
        icon: Message,
        iconColor: 'bg-blue-500',
        actionable: true,
        actionText: '回复询价'
      },
      {
        id: 3,
        title: '库存预警',
        description: '航电系统模块库存低于安全线',
        time: '4小时前',
        icon: Warning,
        iconColor: 'bg-warning-500',
        actionable: true,
        actionText: '处理预警'
      },
      {
        id: 4,
        title: '共享件售出',
        description: '起落架部件共享件售出，收益 ¥67,000',
        time: '6小时前',
        icon: Share,
        iconColor: 'bg-purple-500',
        actionable: false
      },
      {
        id: 5,
        title: '产品上架',
        description: '新燃油系统部件产品已成功上架',
        time: '1天前',
        icon: Box,
        iconColor: 'bg-primary-500',
        actionable: false
      }
    ]
    
  } catch (error) {
    console.error('供应商工作台初始化失败:', error)
  }
}

onMounted(() => {
  initializeWorkspace()
})
</script>

<style scoped>
.supplier-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.sales-functions {
  @apply modern-card p-6;
}

.action-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.action-card.primary {
  @apply border-l-4 border-green-500 hover:bg-green-50;
}

.action-card.success {
  @apply border-l-4 border-green-600 hover:bg-green-50;
}

.action-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.action-card.warning {
  @apply border-l-4 border-yellow-500 hover:bg-yellow-50;
}

.action-card.purple {
  @apply border-l-4 border-purple-500 hover:bg-purple-50;
}

.action-card.orange {
  @apply border-l-4 border-orange-500 hover:bg-orange-50;
}

.action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.action-card.primary .action-icon {
  @apply bg-green-100 text-green-600;
}

.action-card.success .action-icon {
  @apply bg-green-100 text-green-700;
}

.action-card.blue .action-icon {
  @apply bg-blue-100 text-blue-600;
}

.action-card.warning .action-icon {
  @apply bg-yellow-100 text-yellow-600;
}

.action-card.purple .action-icon {
  @apply bg-purple-100 text-purple-600;
}

.action-card.orange .action-icon {
  @apply bg-orange-100 text-orange-600;
}

.action-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.action-desc {
  @apply text-gray-600 text-sm mb-3;
}

.action-stats {
  @apply text-xs;
}

.recent-activities {
  @apply modern-card p-6;
}

.activity-item {
  @apply flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors;
}

.activity-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center text-white flex-shrink-0;
}

.activity-content {
  @apply flex-1;
}

.activity-title {
  @apply font-medium text-gray-800;
}

.activity-desc {
  @apply text-sm text-gray-600 mt-1;
}

.activity-time {
  @apply text-xs text-gray-500 mt-2;
}

.overview-cards {
  @apply space-y-4;
}

.overview-card {
  @apply modern-card p-4 text-center;
}

.overview-value {
  @apply text-2xl font-bold;
}

.overview-label {
  @apply text-sm text-gray-600 mt-1;
}

.chart-bars {
  @apply w-full;
}

.chart-bar {
  @apply flex-1 mx-1;
}

.hot-products {
  @apply space-y-3;
}

.hot-product-item {
  @apply p-3 rounded-lg hover:bg-gray-50 transition-colors;
}

.ranking {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold;
}

.product-name {
  @apply font-medium text-gray-800 text-sm;
}

.product-sales {
  @apply text-xs text-gray-500;
}

.product-revenue {
  @apply text-sm font-semibold text-green-600;
}

.quick-links {
  @apply space-y-3;
}

.quick-link {
  @apply flex items-center space-x-3 text-gray-700 hover:text-primary-600 transition-colors p-2 rounded-lg hover:bg-gray-50;
}

.aog-panel {
  @apply shadow-sm;
}
</style>