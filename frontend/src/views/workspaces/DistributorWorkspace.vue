<!--
分销商专属工作台
版本: 1.0
创建时间: 2025-07-22

专为航材分销商设计的双向贸易工作台
角色特点：
1. 既买也卖，连接供需两端
2. 采购需求发布和产品销售并重
3. 复杂的库存周转管理
4. 双向订单处理和价格管理
5. 贸易利润分析
-->

<template>
  <div class="distributor-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            欢迎回来，{{ userStore.user?.username }}
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.company_name || '航材分销商' }}
          </p>
          <p class="text-white/80 mt-2">
            连接航材供需两端，实现高效贸易周转和利润最大化
          </p>
        </div>
        <div class="welcome-stats hidden md:flex space-x-6">
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.activePurchases || 0 }}</div>
            <div class="text-white/80 text-sm">进行采购</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.activeSales || 0 }}</div>
            <div class="text-white/80 text-sm">进行销售</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.profitMargin || 0 }}%</div>
            <div class="text-white/80 text-sm">利润率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧主要操作区 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 业务方向切换 -->
        <div class="business-direction-switcher">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">贸易中心</h2>
            <div class="flex items-center space-x-2">
              <el-button-group>
                <el-button 
                  :type="activeDirection === 'purchasing' ? 'primary' : ''" 
                  @click="switchDirection('purchasing')"
                  class="direction-btn"
                >
                  <ShoppingCart class="w-4 h-4 mr-2" />
                  采购方向
                </el-button>
                <el-button 
                  :type="activeDirection === 'selling' ? 'primary' : ''" 
                  @click="switchDirection('selling')"
                  class="direction-btn"
                >
                  <House class="w-4 h-4 mr-2" />
                  销售方向
                </el-button>
                <el-button 
                  :type="activeDirection === 'overview' ? 'primary' : ''" 
                  @click="switchDirection('overview')"
                  class="direction-btn"
                >
                  <DataAnalysis class="w-4 h-4 mr-2" />
                  全局视图
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 采购方向视图 -->
          <div v-show="activeDirection === 'purchasing'" class="purchasing-section">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 发布采购需求 -->
              <div class="action-card primary" @click="handlePublishDemand">
                <div class="action-icon">
                  <Plus class="w-6 h-6" />
                </div>
                <h3 class="action-title">发布采购需求</h3>
                <p class="action-desc">寻找优质航材供应商</p>
                <div class="action-stats">
                  <span class="text-sm text-primary-600">本月采购 {{ stats.demandStats?.thisMonth || 0 }} 次</span>
                </div>
              </div>

              <!-- 供应商管理 -->
              <div class="action-card blue" @click="handleSupplierManagement">
                <div class="action-icon">
                  <Folder class="w-6 h-6" />
                </div>
                <h3 class="action-title">供应商管理</h3>
                <p class="action-desc">管理和评价供应商</p>
                <div class="action-stats">
                  <span class="text-sm text-blue-600">{{ stats.supplierCount || 0 }} 个合作供应商</span>
                </div>
              </div>

              <!-- 采购订单 -->
              <div class="action-card warning" @click="handlePurchaseOrders">
                <div class="action-icon">
                  <ShoppingCart class="w-6 h-6" />
                </div>
                <h3 class="action-title">采购订单</h3>
                <p class="action-desc">管理进货订单状态</p>
                <div class="action-stats">
                  <span class="text-sm text-warning-600">{{ stats.purchaseOrders?.pending || 0 }} 个待到货</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 销售方向视图 -->
          <div v-show="activeDirection === 'selling'" class="selling-section">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 产品发布 -->
              <div class="action-card success" @click="handleProductPublish">
                <div class="action-icon">
                  <Box class="w-6 h-6" />
                </div>
                <h3 class="action-title">产品发布</h3>
                <p class="action-desc">发布航材产品到市场</p>
                <div class="action-stats">
                  <span class="text-sm text-success-600">{{ stats.onlineProducts || 0 }} 个在线产品</span>
                </div>
              </div>

              <!-- 客户管理 -->
              <div class="action-card purple" @click="handleCustomerManagement">
                <div class="action-icon">
                  <User class="w-6 h-6" />
                </div>
                <h3 class="action-title">客户管理</h3>
                <p class="action-desc">维护客户关系和服务</p>
                <div class="action-stats">
                  <span class="text-sm text-purple-600">{{ stats.customerCount || 0 }} 个活跃客户</span>
                </div>
              </div>

              <!-- 销售订单 -->
              <div class="action-card orange" @click="handleSalesOrders">
                <div class="action-icon">
                  <Star class="w-6 h-6" />
                </div>
                <h3 class="action-title">销售订单</h3>
                <p class="action-desc">处理客户销售订单</p>
                <div class="action-stats">
                  <span class="text-sm text-orange-600">{{ stats.salesOrders?.pending || 0 }} 个待发货</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 全局视图 -->
          <div v-show="activeDirection === 'overview'" class="overview-section">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 库存周转 -->
              <div class="action-card blue" @click="handleInventoryTurnover">
                <div class="action-icon">
                  <DataLine class="w-6 h-6" />
                </div>
                <h3 class="action-title">库存周转</h3>
                <p class="action-desc">监控库存流转效率</p>
                <div class="action-stats">
                  <span class="text-sm text-blue-600">周转率 {{ stats.turnoverRate || 0 }}天</span>
                </div>
              </div>

              <!-- 询价管理 -->
              <div class="action-card purple" @click="handleInquiryManagement">
                <div class="action-icon">
                  <Message class="w-6 h-6" />
                </div>
                <h3 class="action-title">询价管理</h3>
                <p class="action-desc">处理双向询价业务</p>
                <div class="action-stats">
                  <span class="text-sm text-purple-600">{{ stats.totalInquiries || 0 }} 个待处理</span>
                </div>
              </div>

              <!-- 贸易分析 -->
              <div class="action-card orange" @click="handleTradeAnalysis">
                <div class="action-icon">
                  <DataAnalysis class="w-6 h-6" />
                </div>
                <h3 class="action-title">贸易分析</h3>
                <p class="action-desc">分析贸易数据和利润</p>
                <div class="action-stats">
                  <span class="text-sm text-orange-600">本月利润 ¥{{ formatCurrency(stats.monthlyProfit) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activities">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">最近活动</h2>
            <el-button text type="primary" @click="viewAllActivities">
              查看全部
              <Refresh class="w-4 h-4 ml-1" />
            </el-button>
          </div>
          
          <div class="activity-list space-y-4">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.iconColor">
                <component :is="activity.icon" class="w-4 h-4" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-action">
                <el-button 
                  v-if="activity.actionable" 
                  size="small" 
                  text 
                  type="primary"
                  @click="handleMonitorAction(activity)"
                >
                  {{ activity.actionText }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 贸易概览卡片 -->
        <div class="overview-cards grid grid-cols-2 gap-4">
          <div class="overview-card">
            <div class="overview-value text-primary-600">{{ stats.activePurchases || 0 }}</div>
            <div class="overview-label">进行采购</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-success-600">{{ stats.activeSales || 0 }}</div>
            <div class="overview-label">进行销售</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-warning-600">{{ stats.totalInquiries || 0 }}</div>
            <div class="overview-label">待处理询价</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-purple-600">{{ stats.inventoryValue || 0 }}万</div>
            <div class="overview-label">库存价值</div>
          </div>
        </div>

        <!-- 利润分析图表 -->
        <div class="profit-chart modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">月度利润趋势</h3>
          <div class="chart-placeholder">
            <div class="chart-bars flex items-end justify-between space-x-2 h-32">
              <div 
                v-for="(value, index) in profitData" 
                :key="index"
                class="chart-bar bg-gradient-to-t from-purple-500 to-purple-400 rounded-t"
                :style="{ height: `${(value / Math.max(...profitData)) * 100}%`, minHeight: '8px' }"
              ></div>
            </div>
            <div class="chart-labels flex justify-between text-xs text-gray-500 mt-2">
              <span v-for="month in ['1月', '2月', '3月', '4月', '5月', '6月']" :key="month">
                {{ month }}
              </span>
            </div>
          </div>
        </div>

        <!-- 热门交易品类 -->
        <div class="hot-categories modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">热门交易品类</h3>
          <div class="space-y-3">
            <div 
              v-for="(category, index) in hotCategories" 
              :key="category.id"
              class="category-item flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div class="category-icon" :style="{ backgroundColor: category.color }">
                  <component :is="category.icon" class="w-4 h-4 text-white" />
                </div>
                <div>
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-volume text-xs text-gray-500">交易量 {{ category.volume }}</div>
                </div>
              </div>
              <div class="category-profit text-sm font-semibold text-purple-600">
                ¥{{ formatCurrency(category.profit) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 价格监控 -->
        <div class="price-monitor modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">价格监控</h3>
          <div class="space-y-3">
            <div 
              v-for="item in priceMonitor" 
              :key="item.id"
              class="price-item"
            >
              <div class="flex justify-between items-center">
                <span class="price-item-name">{{ item.name }}</span>
                <div class="price-trend" :class="item.trend === 'up' ? 'text-red-500' : 'text-green-500'">
                  <component :is="item.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" class="w-3 h-3 inline" />
                  {{ item.change }}%
                </div>
              </div>
              <div class="price-range text-xs text-gray-500">
                采购价: ¥{{ formatCurrency(item.purchasePrice) }} | 销售价: ¥{{ formatCurrency(item.salePrice) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速链接</h3>
          <div class="space-y-3">
            <a href="#" class="quick-link">
              <Document class="w-4 h-4" />
              <span>分销商指南</span>
            </a>
            <a href="#" class="quick-link">
              <Download class="w-4 h-4" />
              <span>贸易帮助文档</span>
            </a>
            <a href="#" class="quick-link">
              <Tools class="w-4 h-4" />
              <span>客服支持</span>
            </a>
            <a href="#" class="quick-link">
              <Setting class="w-4 h-4" />
              <span>账户设置</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布需求弹窗 -->
    <PublishDemandDialog v-model="showPublishDialog" />
    
    <!-- 发布共享件弹窗 -->
    <SharedMaterialPublishForm v-model="showSharedDialog" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Plus, ShoppingCart, House, Box, User, Star, Folder, Message, 
  DataAnalysis, DataLine, Document, Download, Tools, Setting, 
  Warning, Refresh, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import PublishDemandDialog from '@/components/PublishDemandDialog.vue'
import SharedMaterialPublishForm from '@/components/SharedMaterialPublishForm.vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const activeDirection = ref('overview') // 'purchasing' | 'selling' | 'overview'
const showPublishDialog = ref(false)
const showSharedDialog = ref(false)
const stats = ref({})
const recentActivities = ref([])
const profitData = ref([85, 120, 165, 210, 285, 340]) // 利润增长趋势
const hotCategories = ref([
  { id: 1, name: '发动机部件', volume: 245, profit: 1240000, color: '#8B5CF6', icon: 'DataAnalysis' },
  { id: 2, name: '航电设备', volume: 189, profit: 980000, color: '#10B981', icon: 'Box' },
  { id: 3, name: '液压系统', volume: 156, profit: 720000, color: '#F59E0B', icon: 'Tools' },
  { id: 4, name: '起落架', volume: 89, profit: 560000, color: '#EF4444', icon: 'Star' },
  { id: 5, name: '燃油系统', volume: 67, profit: 430000, color: '#3B82F6', icon: 'Folder' }
])
const priceMonitor = ref([
  { id: 1, name: 'CFM56发动机叶片', purchasePrice: 45000, salePrice: 58000, trend: 'up', change: 2.3 },
  { id: 2, name: '737航电模块', purchasePrice: 28000, salePrice: 35000, trend: 'down', change: 1.5 },
  { id: 3, name: '液压泵组件', purchasePrice: 15000, salePrice: 19500, trend: 'up', change: 3.2 },
  { id: 4, name: '起落架轮胎', purchasePrice: 8500, salePrice: 11000, trend: 'down', change: 0.8 }
])

// 计算属性
const welcomeStyle = computed(() => {
  // 使用紫色主题 - 分销商贸易主题
  return {
    background: 'linear-gradient(135deg, #8B5CF6, #7C3AED)'
  }
})

// 方法
const switchDirection = (direction) => {
  activeDirection.value = direction
  const directionNames = {
    purchasing: '采购方向',
    selling: '销售方向', 
    overview: '全局视图'
  }
  ElMessage.success(`已切换到${directionNames[direction]}`)
  loadDirectionData(direction)
}

const loadDirectionData = async (direction) => {
  // 根据方向加载不同的数据
  try {
    // 这里可以调用相应的API获取不同方向的数据
    console.log(`加载${direction}方向的数据`)
  } catch (error) {
    console.error('加载方向数据失败:', error)
  }
}

// 采购方向操作
const handlePublishDemand = () => {
  showPublishDialog.value = true
}

const handleSupplierManagement = () => {
  router.push('/app/suppliers')
}

const handlePurchaseOrders = () => {
  router.push('/app/orders?type=purchase')
}

// 销售方向操作
const handleProductPublish = () => {
  router.push('/app/inventory?action=publish')
}

const handleCustomerManagement = () => {
  router.push('/app/customers')
}

const handleSalesOrders = () => {
  router.push('/app/orders?type=sales')
}

// 全局操作
const handleInventoryTurnover = () => {
  router.push('/app/analytics?focus=inventory')
}

const handleInquiryManagement = () => {
  router.push('/app/inquiries')
}

const handleTradeAnalysis = () => {
  router.push('/app/analytics?focus=trade')
}

const viewAllActivities = () => {
  router.push('/app/activities')
}

const handleMonitorAction = (activity) => {
  ElMessage.info(`处理活动: ${activity.title}`)
}

const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载分销商统计数据
    const distributorStats = await getWorkspaceStats('distributorStats')
    stats.value = distributorStats || {
      activePurchases: 23,
      activeSales: 34,
      profitMargin: 18.5,
      supplierCount: 56,
      customerCount: 89,
      onlineProducts: 178,
      purchaseOrders: { pending: 12, completed: 45 },
      salesOrders: { pending: 18, completed: 67 },
      demandStats: { thisMonth: 15 },
      turnoverRate: 25,
      totalInquiries: 8,
      inventoryValue: 856,
      monthlyProfit: 1250000
    }
    
    // 加载最近活动数据
    recentActivities.value = [
      {
        id: 1,
        title: '新采购需求发布',
        description: '发布CFM56发动机部件采购需求，预算230万',
        time: '30分钟前',
        icon: Plus,
        iconColor: 'bg-purple-500',
        actionable: true,
        actionText: '查看需求'
      },
      {
        id: 2,
        title: '销售订单确认',
        description: '南航订购航电设备，金额156万元',
        time: '1小时前',
        icon: ShoppingCart,
        iconColor: 'bg-green-500',
        actionable: true,
        actionText: '处理订单'
      },
      {
        id: 3,
        title: '供应商报价',
        description: 'ABC供应商对液压系统组件进行报价',
        time: '2小时前',
        icon: Message,
        iconColor: 'bg-blue-500',
        actionable: true,
        actionText: '查看报价'
      },
      {
        id: 4,
        title: '库存周转提醒',
        description: '起落架部件库存周转周期延长',
        time: '3小时前',
        icon: Warning,
        iconColor: 'bg-warning-500',
        actionable: true,
        actionText: '优化库存'
      },
      {
        id: 5,
        title: '价格波动警报',
        description: 'CFM56发动机叶片市场价格上涨2.3%',
        time: '4小时前',
        icon: DataLine,
        iconColor: 'bg-red-500',
        actionable: true,
        actionText: '查看详情'
      }
    ]
    
  } catch (error) {
    console.error('分销商工作台初始化失败:', error)
  }
}

onMounted(() => {
  initializeWorkspace()
})
</script>

<style scoped>
.distributor-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.business-direction-switcher {
  @apply modern-card p-6;
}

.direction-btn {
  @apply px-4 py-2;
}

.action-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.action-card.primary {
  @apply border-l-4 border-purple-500 hover:bg-purple-50;
}

.action-card.success {
  @apply border-l-4 border-green-500 hover:bg-green-50;
}

.action-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.action-card.warning {
  @apply border-l-4 border-yellow-500 hover:bg-yellow-50;
}

.action-card.purple {
  @apply border-l-4 border-purple-600 hover:bg-purple-50;
}

.action-card.orange {
  @apply border-l-4 border-orange-500 hover:bg-orange-50;
}

.action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.action-card.primary .action-icon {
  @apply bg-purple-100 text-purple-600;
}

.action-card.success .action-icon {
  @apply bg-green-100 text-green-600;
}

.action-card.blue .action-icon {
  @apply bg-blue-100 text-blue-600;
}

.action-card.warning .action-icon {
  @apply bg-yellow-100 text-yellow-600;
}

.action-card.purple .action-icon {
  @apply bg-purple-100 text-purple-700;
}

.action-card.orange .action-icon {
  @apply bg-orange-100 text-orange-600;
}

.action-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.action-desc {
  @apply text-gray-600 text-sm mb-3;
}

.action-stats {
  @apply text-xs;
}

.recent-activities {
  @apply modern-card p-6;
}

.activity-item {
  @apply flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors;
}

.activity-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center text-white flex-shrink-0;
}

.activity-content {
  @apply flex-1;
}

.activity-title {
  @apply font-medium text-gray-800;
}

.activity-desc {
  @apply text-sm text-gray-600 mt-1;
}

.activity-time {
  @apply text-xs text-gray-500 mt-2;
}

.overview-cards {
  @apply space-y-4;
}

.overview-card {
  @apply modern-card p-4 text-center;
}

.overview-value {
  @apply text-2xl font-bold;
}

.overview-label {
  @apply text-sm text-gray-600 mt-1;
}

.chart-bars {
  @apply w-full;
}

.chart-bar {
  @apply flex-1 mx-1;
}

.hot-categories {
  @apply space-y-3;
}

.category-item {
  @apply p-3 rounded-lg hover:bg-gray-50 transition-colors;
}

.category-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.category-name {
  @apply font-medium text-gray-800 text-sm;
}

.category-volume {
  @apply text-xs text-gray-500;
}

.price-monitor {
  @apply space-y-3;
}

.price-item {
  @apply p-3 rounded-lg bg-gray-50;
}

.price-item-name {
  @apply font-medium text-gray-800 text-sm;
}

.price-range {
  @apply text-xs text-gray-500 mt-1;
}

.price-trend {
  @apply text-xs font-semibold flex items-center space-x-1;
}

.quick-links {
  @apply space-y-3;
}

.quick-link {
  @apply flex items-center space-x-3 text-gray-700 hover:text-primary-600 transition-colors p-2 rounded-lg hover:bg-gray-50;
}
</style>