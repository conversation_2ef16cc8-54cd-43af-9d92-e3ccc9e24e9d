<!--
物流专员专属工作台
版本: 2.0
创建时间: 2025-07-19

专为物流专员设计的工作台，专注于物流配送和运输跟踪
特点：
1. 实时货运跟踪
2. 配送计划管理
3. 运输状态监控
4. 物流数据分析
-->

<template>
  <div class="logistics-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            物流配送中心
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.username }}
          </p>
          <p class="text-white/80 mt-2">
            高效管理航材物流配送，确保准时安全送达
          </p>
        </div>
        <div class="welcome-stats hidden lg:flex space-x-8">
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.activeShipments || 0 }}</div>
            <div class="text-white/80 text-sm">运输中</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.deliveredToday || 0 }}</div>
            <div class="text-white/80 text-sm">今日送达</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.onTimeRate || 0 }}%</div>
            <div class="text-white/80 text-sm">准时率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧主要操作区 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 紧急配送警告 -->
        <div class="urgent-deliveries" v-if="urgentDeliveries.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-red-600 flex items-center">
              <Warning class="w-5 h-5 mr-2" />
              紧急配送
            </h2>
            <el-badge :value="urgentDeliveries.length" class="badge-danger">
              <el-button size="small" type="danger" @click="viewAllUrgentDeliveries">
                查看全部
              </el-button>
            </el-badge>
          </div>
          
          <div class="urgent-delivery-list space-y-3">
            <div 
              v-for="delivery in urgentDeliveries.slice(0, 3)" 
              :key="delivery.id"
              class="urgent-delivery-item"
              @click="handleUrgentDelivery(delivery)"
            >
              <div class="urgent-indicator">
                <component :is="delivery.icon" class="w-4 h-4" />
              </div>
              <div class="urgent-content">
                <div class="urgent-title">{{ delivery.title }}</div>
                <div class="urgent-desc">{{ delivery.description }}</div>
                <div class="urgent-time">{{ delivery.deadline }}</div>
              </div>
              <div class="urgent-priority" :class="delivery.priorityClass">
                {{ delivery.priority }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作面板 -->
        <div class="quick-operations">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">快速操作</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 跟踪货运 -->
            <div class="operation-card primary" @click="handleTrackShipment">
              <div class="operation-icon">
                <Box class="w-6 h-6" />
              </div>
              <h3 class="operation-title">跟踪货运</h3>
              <p class="operation-desc">实时跟踪货物运输状态</p>
              <div class="operation-stats">
                <span class="text-sm text-primary-600">{{ stats.activeShipments || 0 }} 个运输中</span>
              </div>
            </div>

            <!-- 安排取件 -->
            <div class="operation-card success" @click="handleSchedulePickup">
              <div class="operation-icon">
                <Plus class="w-6 h-6" />
              </div>
              <h3 class="operation-title">安排取件</h3>
              <p class="operation-desc">安排货物取件服务</p>
              <div class="operation-stats">
                <span class="text-sm text-success-600">{{ stats.pendingPickups || 0 }} 个待取件</span>
              </div>
            </div>

            <!-- 更新配送 -->
            <div class="operation-card warning" @click="handleUpdateDelivery">
              <div class="operation-icon">
                <User class="w-6 h-6" />
              </div>
              <h3 class="operation-title">更新配送</h3>
              <p class="operation-desc">更新配送状态信息</p>
              <div class="operation-stats">
                <span class="text-sm text-warning-600">{{ stats.pendingUpdates || 0 }} 个待更新</span>
              </div>
            </div>

            <!-- 路线优化 -->
            <div class="operation-card blue" @click="handleRouteOptimization">
              <div class="operation-icon">
                <House class="w-6 h-6" />
              </div>
              <h3 class="operation-title">路线优化</h3>
              <p class="operation-desc">优化配送路线规划</p>
              <div class="operation-stats">
                <span class="text-sm text-blue-600">智能规划</span>
              </div>
            </div>

            <!-- 配送报告 -->
            <div class="operation-card purple" @click="handleDeliveryReport">
              <div class="operation-icon">
                <Document class="w-6 h-6" />
              </div>
              <h3 class="operation-title">配送报告</h3>
              <p class="operation-desc">生成配送统计报告</p>
              <div class="operation-stats">
                <span class="text-sm text-purple-600">数据导出</span>
              </div>
            </div>

            <!-- 异常处理 -->
            <div class="operation-card orange" @click="handleExceptionHandling">
              <div class="operation-icon">
                <Warning class="w-6 h-6" />
              </div>
              <h3 class="operation-title">异常处理</h3>
              <p class="operation-desc">处理配送异常情况</p>
              <div class="operation-stats">
                <span class="text-sm text-orange-600">{{ stats.exceptions || 0 }} 个异常</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时配送地图 -->
        <div class="delivery-map">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">实时配送地图</h2>
            <div class="flex items-center space-x-4">
              <el-switch
                v-model="realTimeTracking"
                active-text="实时跟踪"
                @change="toggleRealTimeTracking"
              />
              <el-button size="small" @click="refreshMap">
                <Refresh class="w-4 h-4 mr-1" />
                刷新
              </el-button>
            </div>
          </div>

          <div class="map-container">
            <!-- 地图占位符 -->
            <div class="map-placeholder">
              <div class="map-content">
                <div class="delivery-points">
                  <div 
                    v-for="point in deliveryPoints" 
                    :key="point.id"
                    class="delivery-point"
                    :class="point.statusClass"
                    :style="{ 
                      left: point.x + '%', 
                      top: point.y + '%' 
                    }"
                    @click="showPointDetails(point)"
                  >
                    <component :is="point.icon" class="w-4 h-4" />
                    <div class="point-tooltip">
                      <div class="tooltip-title">{{ point.title }}</div>
                      <div class="tooltip-status">{{ point.status }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- 地图图例 -->
                <div class="map-legend">
                  <div class="legend-item">
                    <div class="legend-color bg-blue-500"></div>
                    <span>运输中</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color bg-green-500"></div>
                    <span>已送达</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color bg-red-500"></div>
                    <span>延误</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color bg-yellow-500"></div>
                    <span>待取件</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 今日配送计划 -->
        <div class="delivery-schedule">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">今日配送计划</h2>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600">
                完成: {{ completedDeliveries }}/{{ totalDeliveries }} ({{ deliveryProgress }}%)
              </span>
              <el-button size="small" @click="refreshSchedule">
                <Refresh class="w-4 h-4 mr-1" />
                刷新
              </el-button>
            </div>
          </div>

          <div class="schedule-progress mb-6">
            <el-progress 
              :percentage="deliveryProgress" 
              :stroke-width="8"
              :color="getProgressColor(deliveryProgress)"
            />
          </div>

          <div class="schedule-list space-y-4">
            <div 
              v-for="schedule in todaySchedule" 
              :key="schedule.id"
              class="schedule-item"
              :class="schedule.status"
            >
              <div class="schedule-time">{{ schedule.time }}</div>
              <div class="schedule-content">
                <div class="schedule-header">
                  <span class="schedule-title">{{ schedule.title }}</span>
                  <span class="schedule-status-badge" :class="schedule.statusClass">
                    {{ schedule.statusText }}
                  </span>
                </div>
                <div class="schedule-details">
                  <div class="schedule-route">
                    <span class="route-from">{{ schedule.from }}</span>
                    <Tools class="w-4 h-4 mx-2 text-gray-400" />
                    <span class="route-to">{{ schedule.to }}</span>
                  </div>
                  <div class="schedule-meta">
                    <span class="schedule-distance">{{ schedule.distance }}</span>
                    <span class="schedule-duration">{{ schedule.duration }}</span>
                  </div>
                </div>
              </div>
              <div class="schedule-actions">
                <el-button 
                  v-if="schedule.actionable"
                  size="small" 
                  :type="schedule.actionType"
                  @click="handleScheduleAction(schedule)"
                >
                  {{ schedule.actionText }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 配送统计卡片 -->
        <div class="delivery-stats grid grid-cols-2 gap-4">
          <div class="stat-card primary">
            <div class="stat-icon">
              <Box class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.activeShipments || 0 }}</div>
              <div class="stat-label">运输中</div>
            </div>
          </div>
          
          <div class="stat-card success">
            <div class="stat-icon">
              <Check class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.deliveredToday || 0 }}</div>
              <div class="stat-label">今日送达</div>
            </div>
          </div>
          
          <div class="stat-card warning">
            <div class="stat-icon">
              <Warning class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.delayedShipments || 0 }}</div>
              <div class="stat-label">延误货物</div>
            </div>
          </div>
          
          <div class="stat-card blue">
            <div class="stat-icon">
              <Clock class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.avgDeliveryTime || 0 }}h</div>
              <div class="stat-label">平均时长</div>
            </div>
          </div>
        </div>

        <!-- 配送车辆状态 -->
        <div class="vehicle-status modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">配送车辆状态</h3>
          <div class="vehicle-list space-y-3">
            <div 
              v-for="vehicle in vehicleStatus" 
              :key="vehicle.id"
              class="vehicle-item"
              :class="vehicle.statusClass"
            >
              <div class="vehicle-info">
                <div class="vehicle-number">{{ vehicle.number }}</div>
                <div class="vehicle-driver">{{ vehicle.driver }}</div>
              </div>
              <div class="vehicle-status-info">
                <div class="vehicle-location">{{ vehicle.location }}</div>
                <div class="vehicle-status-badge" :class="vehicle.statusClass">
                  {{ vehicle.status }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能指标 -->
        <div class="performance-metrics modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">本月性能指标</h3>
          <div class="metrics-chart space-y-4">
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-label">准时率</span>
                <span class="metric-value">{{ performanceMetrics.onTimeRate }}%</span>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-progress on-time" 
                  :style="{ width: `${performanceMetrics.onTimeRate}%` }"
                ></div>
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-label">客户满意度</span>
                <span class="metric-value">{{ performanceMetrics.satisfaction }}%</span>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-progress satisfaction" 
                  :style="{ width: `${performanceMetrics.satisfaction}%` }"
                ></div>
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-label">成本效率</span>
                <span class="metric-value">{{ performanceMetrics.costEfficiency }}%</span>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-progress efficiency" 
                  :style="{ width: `${performanceMetrics.costEfficiency}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速工具 -->
        <div class="quick-tools modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速工具</h3>
          <div class="tool-list space-y-3">
            <button class="tool-item" @click="openDeliveryCalculator">
              <Setting class="w-4 h-4" />
              <span>运费计算器</span>
              <Tools class="w-4 h-4" />
            </button>
            
            <button class="tool-item" @click="openRouteOptimizer">
              <House class="w-4 h-4" />
              <span>路线优化器</span>
              <Tools class="w-4 h-4" />
            </button>
            
            <button class="tool-item" @click="openWeatherInfoFilled">
              <Star class="w-4 h-4" />
              <span>天气信息</span>
              <Tools class="w-4 h-4" />
            </button>
            
            <button class="tool-item" @click="openEmergencyContacts">
              <Search class="w-4 h-4" />
              <span>紧急联系人</span>
              <Tools class="w-4 h-4" />
            </button>
            
            <button class="tool-item" @click="openHelpCenter">
              <Download class="w-4 h-4" />
              <span>帮助中心</span>
              <Tools class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- 最近配送记录 -->
        <div class="recent-deliveries modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">最近配送记录</h3>
          <div class="delivery-history space-y-3">
            <div 
              v-for="delivery in recentDeliveries" 
              :key="delivery.id"
              class="delivery-record"
            >
              <div class="record-status" :class="delivery.statusClass">
                <component :is="delivery.icon" class="w-3 h-3" />
              </div>
              <div class="record-content">
                <div class="record-title">{{ delivery.title }}</div>
                <div class="record-destination">{{ delivery.destination }}</div>
                <div class="record-time">{{ delivery.completedTime }}</div>
              </div>
              <div class="record-duration">{{ delivery.duration }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Box, Plus, User, House, Document, Warning, Refresh,
  Tools, Check, Clock, Setting, Star, Search, Download
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const stats = ref({})
const urgentDeliveries = ref([])
const deliveryPoints = ref([])
const todaySchedule = ref([])
const vehicleStatus = ref([])
const recentDeliveries = ref([])
const performanceMetrics = ref({})
const realTimeTracking = ref(true)
const trackingClock = ref(null)

// 计算属性
const welcomeStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${theme.value.gradientFrom || '#fa8c16'}, ${theme.value.gradientTo || '#faad14'})`
  }
})

const completedDeliveries = computed(() => {
  return todaySchedule.value.filter(item => item.status === 'completed').length
})

const totalDeliveries = computed(() => {
  return todaySchedule.value.length
})

const deliveryProgress = computed(() => {
  if (totalDeliveries.value === 0) return 0
  return Math.round((completedDeliveries.value / totalDeliveries.value) * 100)
})

// 方法
const getProgressColor = (percentage) => {
  if (percentage < 60) return '#ff4d4f'
  if (percentage < 80) return '#faad14'
  return '#52c41a'
}

// 紧急配送处理
const handleUrgentDelivery = (delivery) => {
  ElMessage.warning(`处理紧急配送: ${delivery.title}`)
  router.push(`/app/logistics/delivery/${delivery.id}`)
}

const viewAllUrgentDeliveries = () => {
  router.push('/app/logistics?priority=urgent')
}

// 快速操作处理
const handleTrackShipment = () => {
  router.push('/app/logistics/tracking')
}

const handleSchedulePickup = () => {
  router.push('/app/logistics/pickup')
}

const handleUpdateDelivery = () => {
  router.push('/app/logistics/update')
}

const handleRouteOptimization = () => {
  router.push('/app/logistics/route-optimization')
}

const handleDeliveryReport = () => {
  router.push('/app/logistics/reports')
}

const handleExceptionHandling = () => {
  router.push('/app/logistics/exceptions')
}

// 地图操作
const toggleRealTimeTracking = (enabled) => {
  if (enabled) {
    startRealTimeTracking()
  } else {
    stopRealTimeTracking()
  }
}

const startRealTimeTracking = () => {
  trackingClock.value = setInterval(updateDeliveryPoints, 30000) // 每30秒更新
}

const stopRealTimeTracking = () => {
  if (trackingClock.value) {
    clearInterval(trackingClock.value)
    trackingClock.value = null
  }
}

const updateDeliveryPoints = () => {
  // 模拟更新配送点位置
  deliveryPoints.value.forEach(point => {
    if (point.status === '运输中') {
      // 模拟移动
      point.x = Math.max(0, Math.min(100, point.x + (Math.random() - 0.5) * 10))
      point.y = Math.max(0, Math.min(100, point.y + (Math.random() - 0.5) * 10))
    }
  })
}

const refreshMap = () => {
  ElMessage.info('刷新配送地图')
  updateDeliveryPoints()
}

const showPointDetails = (point) => {
  ElMessage.info(`查看配送详情: ${point.title}`)
}

// 配送计划处理
const handleScheduleAction = (schedule) => {
  if (schedule.status === 'pending') {
    schedule.status = 'in-progress'
    schedule.statusText = '配送中'
    schedule.statusClass = 'status-in-progress'
    schedule.actionText = '完成'
    ElMessage.success(`开始配送: ${schedule.title}`)
  } else if (schedule.status === 'in-progress') {
    schedule.status = 'completed'
    schedule.statusText = '已完成'
    schedule.statusClass = 'status-completed'
    schedule.actionable = false
    ElMessage.success(`完成配送: ${schedule.title}`)
  }
}

const refreshSchedule = () => {
  ElMessage.info('刷新配送计划')
  initializeSchedule()
}

// 工具操作
const openDeliveryCalculator = () => {
  ElMessage.info('打开运费计算器')
}

const openRouteOptimizer = () => {
  ElMessage.info('打开路线优化器')
}

const openWeatherInfoFilled = () => {
  ElMessage.info('查看天气信息')
}

const openEmergencyContacts = () => {
  ElMessage.info('查看紧急联系人')
}

const openHelpCenter = () => {
  ElMessage.info('打开帮助中心')
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载基础统计数据
    stats.value = {
      activeShipments: 34,
      deliveredToday: 12,
      onTimeRate: 94,
      delayedShipments: 3,
      avgDeliveryTime: 6.5,
      pendingPickups: 8,
      pendingUpdates: 5,
      exceptions: 2
    }
    
    // 加载紧急配送
    urgentDeliveries.value = [
      {
        id: 1,
        title: 'AOG - 发动机部件紧急配送',
        description: '国航CA1234发动机部件需紧急配送至首都机场',
        deadline: '剩余2小时',
        priority: 'AOG',
        priorityClass: 'priority-aog',
        icon: Warning
      },
      {
        id: 2,
        title: '航电设备配送延误',
        description: '航电设备配送遇交通堵塞，需重新安排路线',
        deadline: '已延误30分钟',
        priority: '延误',
        priorityClass: 'priority-delayed',
        icon: Van
      }
    ]
    
    initializeDeliveryPoints()
    initializeSchedule()
    initializeVehicleStatus()
    initializeRecentDeliveries()
    initializePerformanceMetrics()
    
    // 启动实时跟踪
    if (realTimeTracking.value) {
      startRealTimeTracking()
    }
    
  } catch (error) {
    console.error('工作台初始化失败:', error)
  }
}

const initializeDeliveryPoints = () => {
  deliveryPoints.value = [
    {
      id: 1,
      title: '北京首都机场',
      status: '运输中',
      statusClass: 'point-shipping',
      x: 75,
      y: 25,
      icon: Van
    },
    {
      id: 2,
      title: '上海浦东机场',
      status: '已送达',
      statusClass: 'point-delivered',
      x: 85,
      y: 65,
      icon: Check
    },
    {
      id: 3,
      title: '广州白云机场',
      status: '延误',
      statusClass: 'point-delayed',
      x: 55,
      y: 85,
      icon: Warning
    },
    {
      id: 4,
      title: '成都双流机场',
      status: '待取件',
      statusClass: 'point-pickup',
      x: 35,
      y: 55,
      icon: Plus
    }
  ]
}

const initializeSchedule = () => {
  todaySchedule.value = [
    {
      id: 1,
      time: '08:00',
      title: 'CFM56发动机部件配送',
      from: '中航材仓库',
      to: '首都机场',
      distance: '45km',
      duration: '2小时',
      status: 'completed',
      statusText: '已完成',
      statusClass: 'status-completed',
      actionable: false
    },
    {
      id: 2,
      time: '10:30',
      title: '航电设备配送',
      from: '供应商仓库',
      to: '浦东机场',
      distance: '32km',
      duration: '1.5小时',
      status: 'in-progress',
      statusText: '配送中',
      statusClass: 'status-in-progress',
      actionable: true,
      actionType: 'success',
      actionText: '完成'
    },
    {
      id: 3,
      time: '14:00',
      title: '轮胎配送',
      from: '轮胎供应商',
      to: '白云机场',
      distance: '28km',
      duration: '1小时',
      status: 'pending',
      statusText: '待配送',
      statusClass: 'status-pending',
      actionable: true,
      actionType: 'primary',
      actionText: '开始'
    },
    {
      id: 4,
      time: '16:30',
      title: '起落架部件配送',
      from: '维修基地',
      to: '双流机场',
      distance: '55km',
      duration: '2.5小时',
      status: 'pending',
      statusText: '待配送',
      statusClass: 'status-pending',
      actionable: true,
      actionType: 'primary',
      actionText: '开始'
    }
  ]
}

const initializeVehicleStatus = () => {
  vehicleStatus.value = [
    {
      id: 1,
      number: '京A·12345',
      driver: '张师傅',
      location: '首都机场附近',
      status: '配送中',
      statusClass: 'vehicle-busy'
    },
    {
      id: 2,
      number: '京A·67890',
      driver: '李师傅',
      location: '返回仓库',
      status: '返程中',
      statusClass: 'vehicle-returning'
    },
    {
      id: 3,
      number: '京A·11111',
      driver: '王师傅',
      location: '仓库待命',
      status: '空闲',
      statusClass: 'vehicle-idle'
    },
    {
      id: 4,
      number: '京A·22222',
      driver: '赵师傅',
      location: '维修保养',
      status: '维护中',
      statusClass: 'vehicle-maintenance'
    }
  ]
}

const initializeRecentDeliveries = () => {
  recentDeliveries.value = [
    {
      id: 1,
      title: '发动机燃油泵',
      destination: '首都机场',
      completedTime: '今天 15:30',
      duration: '2.5h',
      statusClass: 'record-completed',
      icon: Check
    },
    {
      id: 2,
      title: '航电显示器',
      destination: '浦东机场',
      completedTime: '今天 12:15',
      duration: '1.8h',
      statusClass: 'record-completed',
      icon: Check
    },
    {
      id: 3,
      title: '起落架轴承',
      destination: '白云机场',
      completedTime: '昨天 16:45',
      duration: '3.2h',
      statusClass: 'record-completed',
      icon: Check
    }
  ]
}

const initializePerformanceMetrics = () => {
  performanceMetrics.value = {
    onTimeRate: 94,
    satisfaction: 96,
    costEfficiency: 88
  }
}

onMounted(() => {
  initializeWorkspace()
})

onUnmounted(() => {
  stopRealTimeTracking()
})
</script>

<style scoped>
.logistics-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.urgent-deliveries {
  @apply modern-card p-6 border-l-4 border-red-500 bg-red-50;
}

.urgent-delivery-item {
  @apply flex items-center space-x-4 p-4 rounded-lg bg-white border hover:shadow-md transition-shadow cursor-pointer;
}

.urgent-indicator {
  @apply w-8 h-8 bg-red-100 text-red-600 rounded-lg flex items-center justify-center;
}

.urgent-content {
  @apply flex-1;
}

.urgent-title {
  @apply font-semibold text-gray-800;
}

.urgent-desc {
  @apply text-sm text-gray-600 mt-1;
}

.urgent-time {
  @apply text-xs text-red-600 mt-1 font-medium;
}

.urgent-priority {
  @apply px-2 py-1 rounded text-xs font-bold;
}

.priority-aog {
  @apply bg-red-600 text-white;
}

.priority-delayed {
  @apply bg-orange-500 text-white;
}

.quick-operations {
  @apply modern-card p-6;
}

.operation-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.operation-card.primary {
  @apply border-l-4 border-primary-500 hover:bg-primary-50;
}

.operation-card.success {
  @apply border-l-4 border-success-500 hover:bg-success-50;
}

.operation-card.warning {
  @apply border-l-4 border-warning-500 hover:bg-warning-50;
}

.operation-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.operation-card.purple {
  @apply border-l-4 border-purple-500 hover:bg-purple-50;
}

.operation-card.orange {
  @apply border-l-4 border-orange-500 hover:bg-orange-50;
}

.operation-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.operation-card.primary .operation-icon {
  @apply bg-primary-100 text-primary-600;
}

.operation-card.success .operation-icon {
  @apply bg-success-100 text-success-600;
}

.operation-card.warning .operation-icon {
  @apply bg-warning-100 text-warning-600;
}

.operation-card.blue .operation-icon {
  @apply bg-blue-100 text-blue-600;
}

.operation-card.purple .operation-icon {
  @apply bg-purple-100 text-purple-600;
}

.operation-card.orange .operation-icon {
  @apply bg-orange-100 text-orange-600;
}

.operation-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.operation-desc {
  @apply text-gray-600 text-sm mb-3;
}

.operation-stats {
  @apply text-xs;
}

.delivery-map {
  @apply modern-card p-6;
}

.map-container {
  @apply h-96 bg-gray-100 rounded-lg overflow-hidden relative;
}

.map-placeholder {
  @apply w-full h-full bg-gradient-to-br from-blue-100 to-green-100 relative;
}

.map-content {
  @apply w-full h-full relative;
}

.delivery-points {
  @apply absolute inset-0;
}

.delivery-point {
  @apply absolute w-8 h-8 rounded-full flex items-center justify-center text-white cursor-pointer transform -translate-x-1/2 -translate-y-1/2 transition-all hover:scale-110;
}

.point-shipping {
  @apply bg-blue-500;
}

.point-delivered {
  @apply bg-green-500;
}

.point-delayed {
  @apply bg-red-500;
}

.point-pickup {
  @apply bg-yellow-500;
}

.point-tooltip {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 hover:opacity-100 transition-opacity whitespace-nowrap;
}

.map-legend {
  @apply absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-md;
}

.legend-item {
  @apply flex items-center space-x-2 mb-1 last:mb-0;
}

.legend-color {
  @apply w-3 h-3 rounded-full;
}

.delivery-schedule {
  @apply modern-card p-6;
}

.schedule-item {
  @apply flex items-center space-x-4 p-4 rounded-lg border transition-colors;
}

.schedule-item.pending {
  @apply border-gray-200 bg-gray-50;
}

.schedule-item.in-progress {
  @apply border-blue-200 bg-blue-50;
}

.schedule-item.completed {
  @apply border-green-200 bg-green-50;
}

.schedule-time {
  @apply text-sm font-medium text-gray-700 w-16 flex-shrink-0;
}

.schedule-content {
  @apply flex-1;
}

.schedule-header {
  @apply flex items-center justify-between mb-2;
}

.schedule-title {
  @apply font-semibold text-gray-800;
}

.schedule-status-badge {
  @apply text-xs px-2 py-1 rounded;
}

.status-pending {
  @apply bg-gray-200 text-gray-700;
}

.status-in-progress {
  @apply bg-blue-200 text-blue-700;
}

.status-completed {
  @apply bg-green-200 text-green-700;
}

.schedule-details {
  @apply space-y-1;
}

.schedule-route {
  @apply flex items-center text-sm text-gray-600;
}

.route-from, .route-to {
  @apply font-medium;
}

.schedule-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.delivery-stats {
  @apply grid-cols-2 gap-4;
}

.stat-card {
  @apply modern-card p-4 flex items-center space-x-3;
}

.stat-card.primary {
  @apply border-l-4 border-primary-500;
}

.stat-card.success {
  @apply border-l-4 border-success-500;
}

.stat-card.warning {
  @apply border-l-4 border-warning-500;
}

.stat-card.blue {
  @apply border-l-4 border-blue-500;
}

.stat-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.stat-card.primary .stat-icon {
  @apply bg-primary-100 text-primary-600;
}

.stat-card.success .stat-icon {
  @apply bg-success-100 text-success-600;
}

.stat-card.warning .stat-icon {
  @apply bg-warning-100 text-warning-600;
}

.stat-card.blue .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.stat-value {
  @apply text-xl font-bold text-gray-800;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.vehicle-item {
  @apply flex items-center justify-between p-3 rounded-lg;
}

.vehicle-item.vehicle-busy {
  @apply bg-blue-50 border border-blue-200;
}

.vehicle-item.vehicle-returning {
  @apply bg-yellow-50 border border-yellow-200;
}

.vehicle-item.vehicle-idle {
  @apply bg-green-50 border border-green-200;
}

.vehicle-item.vehicle-maintenance {
  @apply bg-gray-50 border border-gray-200;
}

.vehicle-number {
  @apply font-medium text-gray-800;
}

.vehicle-driver {
  @apply text-sm text-gray-600;
}

.vehicle-location {
  @apply text-sm text-gray-600;
}

.vehicle-status-badge {
  @apply text-xs px-2 py-1 rounded font-medium;
}

.vehicle-item.vehicle-busy .vehicle-status-badge {
  @apply bg-blue-200 text-blue-700;
}

.vehicle-item.vehicle-returning .vehicle-status-badge {
  @apply bg-yellow-200 text-yellow-700;
}

.vehicle-item.vehicle-idle .vehicle-status-badge {
  @apply bg-green-200 text-green-700;
}

.vehicle-item.vehicle-maintenance .vehicle-status-badge {
  @apply bg-gray-200 text-gray-700;
}

.metrics-chart {
  @apply space-y-4;
}

.metric-item {
  @apply space-y-2;
}

.metric-header {
  @apply flex items-center justify-between;
}

.metric-label {
  @apply text-sm font-medium text-gray-700;
}

.metric-value {
  @apply text-sm font-semibold text-gray-800;
}

.metric-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.metric-progress {
  @apply h-full transition-all duration-300;
}

.metric-progress.on-time {
  @apply bg-green-500;
}

.metric-progress.satisfaction {
  @apply bg-blue-500;
}

.metric-progress.efficiency {
  @apply bg-purple-500;
}

.tool-item {
  @apply w-full flex items-center justify-between p-3 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors;
}

.delivery-record {
  @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors;
}

.record-status {
  @apply w-6 h-6 rounded-full flex items-center justify-center;
}

.record-status.record-completed {
  @apply bg-green-100 text-green-600;
}

.record-title {
  @apply font-medium text-gray-800;
}

.record-destination {
  @apply text-sm text-gray-600;
}

.record-time {
  @apply text-xs text-gray-500;
}

.record-duration {
  @apply text-sm text-gray-600 font-medium;
}
</style>