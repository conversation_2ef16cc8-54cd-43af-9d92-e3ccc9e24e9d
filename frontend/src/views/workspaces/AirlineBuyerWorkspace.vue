<!--
航空公司采购员专属工作台
版本: 2.0
创建时间: 2025-07-19

专为航空公司采购员设计的纯采购工作台，专注采购需求和库存管理
特点：
1. 采购需求发布和管理
2. 供应商管理和评价
3. 采购订单跟踪
4. 闲置航材共享（偶尔）
5. 采购成本分析
-->

<template>
  <div class="airline-buyer-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            欢迎回来，{{ userStore.user?.username }}
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.company_name || '航空公司' }}
          </p>
          <p class="text-white/80 mt-2">
            管理航材采购需求，寻找优质供应商，实现成本优化采购
          </p>
        </div>
        <div class="welcome-stats hidden md:flex space-x-6">
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.activeDemands || 0 }}</div>
            <div class="text-white/80 text-sm">活跃需求</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">{{ stats.totalSuppliers || 0 }}</div>
            <div class="text-white/80 text-sm">合作供应商</div>
          </div>
          <div class="stat-item">
            <div class="text-2xl font-bold text-white">¥{{ formatCurrency(stats.monthlySavings) }}</div>
            <div class="text-white/80 text-sm">本月节约</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧主要操作区 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 核心采购功能 -->
        <div class="procurement-center">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">采购中心</h2>
            <div class="flex space-x-2">
              <el-tag type="primary" size="small">{{ stats.activeDemands || 0 }} 个活跃需求</el-tag>
              <el-tag type="warning" size="small">{{ stats.pendingOrders || 0 }} 个待收货</el-tag>
            </div>
          </div>

          <!-- 采购功能区 -->
          <div class="procurement-section">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 发布需求 -->
              <div class="action-card primary" @click="handlePublishDemand">
                <div class="action-icon">
                  <Plus class="w-6 h-6" />
                </div>
                <h3 class="action-title">发布需求</h3>
                <p class="action-desc">发布新的航材采购需求</p>
                <div class="action-stats">
                  <span class="text-sm text-primary-600">本月发布 {{ stats.demandStats?.thisMonth || 0 }} 个</span>
                </div>
              </div>

              <!-- 浏览市场 -->
              <div class="action-card success" @click="handleBrowseMarket">
                <div class="action-icon">
                  <Search class="w-6 h-6" />
                </div>
                <h3 class="action-title">浏览市场</h3>
                <p class="action-desc">查找可用的航材资源</p>
                <div class="action-stats">
                  <span class="text-sm text-success-600">{{ stats.availableMaterials || 0 }} 个可选项</span>
                </div>
              </div>

              <!-- 采购订单 -->
              <div class="action-card warning" @click="handleViewPurchaseOrders">
                <div class="action-icon">
                  <ShoppingCart class="w-6 h-6" />
                </div>
                <h3 class="action-title">采购订单</h3>
                <p class="action-desc">管理采购订单状态</p>
                <div class="action-stats">
                  <span class="text-sm text-warning-600">{{ stats.purchaseOrders?.pending || 0 }} 个待收货</span>
                </div>
              </div>

              <!-- 供应商管理 -->
              <div class="action-card blue" @click="handleSupplierManagement">
                <div class="action-icon">
                  <Folder class="w-6 h-6" />
                </div>
                <h3 class="action-title">供应商管理</h3>
                <p class="action-desc">管理供应商信息和评价</p>
                <div class="action-stats">
                  <span class="text-sm text-blue-600">{{ stats.totalSuppliers || 0 }} 个合作伙伴</span>
                </div>
              </div>

              <!-- 库存管理 -->
              <div class="action-card purple" @click="handleInventoryManagement">
                <div class="action-icon">
                  <Box class="w-6 h-6" />
                </div>
                <h3 class="action-title">库存管理</h3>
                <p class="action-desc">管理自用航材库存</p>
                <div class="action-stats">
                  <span class="text-sm text-purple-600">{{ stats.inventoryAlerts || 0 }} 个库存预警</span>
                </div>
              </div>

              <!-- 闲置共享 -->
              <div class="action-card orange" @click="handleIdleSharing">
                <div class="action-icon">
                  <Share class="w-6 h-6" />
                </div>
                <h3 class="action-title">闲置共享</h3>
                <p class="action-desc">共享富余航材获得收益</p>
                <div class="action-stats">
                  <span class="text-sm text-orange-600">{{ stats.idleItems || 0 }} 项可共享</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activities">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">最近活动</h2>
            <el-button text type="primary" @click="viewAllActivities">
              查看全部
              <Refresh class="w-4 h-4 ml-1" />
            </el-button>
          </div>
          
          <div class="activity-list space-y-4">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.iconColor">
                <component :is="activity.icon" class="w-4 h-4" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-action">
                <el-button 
                  v-if="activity.actionable" 
                  size="small" 
                  text 
                  type="primary"
                  @click="handleMonitorAction(activity)"
                >
                  {{ activity.actionText }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 业务概览卡片 -->
        <div class="overview-cards grid grid-cols-2 gap-4">
          <div class="overview-card">
            <div class="overview-value text-primary-600">{{ stats.activeDemands || 0 }}</div>
            <div class="overview-label">活跃需求</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-success-600">{{ stats.totalSuppliers || 0 }}</div>
            <div class="overview-label">合作供应商</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-warning-600">{{ stats.pendingOrders || 0 }}</div>
            <div class="overview-label">待收货订单</div>
          </div>
          <div class="overview-card">
            <div class="overview-value text-blue-600">{{ stats.completedOrders || 0 }}</div>
            <div class="overview-label">已完成采购</div>
          </div>
        </div>

        <!-- 月度采购成本趋势 -->
        <div class="cost-chart modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">月度采购成本</h3>
          <div class="chart-placeholder">
            <div class="chart-bars flex items-end justify-between space-x-2 h-32">
              <div 
                v-for="(value, index) in revenueData" 
                :key="index"
                class="chart-bar bg-gradient-to-t from-blue-500 to-blue-400 rounded-t"
                :style="{ height: `${(value / Math.max(...revenueData)) * 100}%`, minHeight: '8px' }"
              ></div>
            </div>
            <div class="chart-labels flex justify-between text-xs text-gray-500 mt-2">
              <span v-for="month in ['1月', '2月', '3月', '4月', '5月', '6月']" :key="month">
                {{ month }}
              </span>
            </div>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">快速链接</h3>
          <div class="space-y-3">
            <a href="#" class="quick-link">
              <Document class="w-4 h-4" />
              <span>航材采购指南</span>
            </a>
            <a href="#" class="quick-link">
              <Download class="w-4 h-4" />
              <span>供应商管理手册</span>
            </a>
            <a href="#" class="quick-link">
              <Tools class="w-4 h-4" />
              <span>客服支持</span>
            </a>
            <a href="#" class="quick-link">
              <Setting class="w-4 h-4" />
              <span>账户设置</span>
            </a>
          </div>
        </div>

        <!-- AOG紧急响应 -->
         <!--
        <div class="aog-panel modern-card p-6 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <Warning class="w-4 h-4 text-red-600" />
            </div>
            <h3 class="text-lg font-semibold text-red-800">AOG紧急响应</h3>
          </div>
          <p class="text-red-700 text-sm mb-4">
            飞机停场紧急情况？启动24/7快速响应服务
          </p>
          <el-button type="danger" size="small" class="w-full" @click="handleAOGResponse">
            <Warning class="w-4 h-4 mr-2" />
            启动AOG响应
          </el-button>
        </div>
      -->
      </div>
    </div>

    <!-- 发布需求弹窗 -->
    <PublishDemandDialog v-model="showPublishDialog" />
    
    <!-- 发布共享件弹窗 -->
    <SharedMaterialPublishForm v-model="showSharedDialog" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Plus, ShoppingCart, House, Search, Box, User, Folder, Star,
  Document, Download, Tools, Setting, Warning, Refresh
} from '@element-plus/icons-vue'
import PublishDemandDialog from '@/components/PublishDemandDialog.vue'
import SharedMaterialPublishForm from '@/components/SharedMaterialPublishForm.vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const activeIdentity = ref('buyer') // 'buyer' | 'supplier'
const showPublishDialog = ref(false)
const showSharedDialog = ref(false)
const stats = ref({})
const recentActivities = ref([])
const revenueData = ref([120, 150, 180, 220, 280, 320])

// 计算属性
const welcomeStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${theme.value.gradientFrom || '#1890ff'}, ${theme.value.gradientTo || '#722ed1'})`
  }
})

// 方法
const switchIdentity = (identity) => {
  activeIdentity.value = identity
  ElMessage.success(`已切换到${identity === 'buyer' ? '采购方' : '供应方'}视图`)
  loadIdentityData(identity)
}

const loadIdentityData = async (identity) => {
  // 根据身份加载不同的数据
  try {
    if (identity === 'buyer') {
      // 加载采购相关数据
      const buyerData = await getWorkspaceStats('buyerData')
      stats.value = { ...stats.value, ...buyerData }
    } else {
      // 加载供应相关数据  
      const supplierData = await getWorkspaceStats('supplierData')
      stats.value = { ...stats.value, ...supplierData }
    }
  } catch (error) {
    console.error('加载身份数据失败:', error)
  }
}

// 业务操作方法
const handlePublishDemand = () => {
  showPublishDialog.value = true
}

const handleBrowseMarket = () => {
  router.push('/app/marketplace')
}

const handleViewPurchaseOrders = () => {
  router.push('/app/orders?type=purchase')
}

const handleSupplierManagement = () => {
  router.push('/app/suppliers')
}

const handleInventoryManagement = () => {
  router.push('/app/inventory')
}

const handleIdleSharing = () => {
  showSharedDialog.value = true
}

const handleAOGResponse = () => {
  ElMessage.warning('正在启动AOG紧急响应流程...')
  // 这里可以打开AOG专用对话框或跳转到AOG页面
}

const viewAllActivities = () => {
  router.push('/app/activities')
}

const handleMonitorAction = (activity) => {
  ElMessage.info(`处理活动: ${activity.title}`)
}

const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载航空公司采购统计数据
    const baseStats = await getWorkspaceStats('airlineProcurementStats')
    stats.value = baseStats || {
      activeDemands: 12,
      totalSuppliers: 34,
      pendingOrders: 8,
      completedOrders: 145,
      availableMaterials: 2340,
      inventoryAlerts: 3,
      idleItems: 28,
      monthlySavings: 1560000,
      demandStats: { thisMonth: 15 }
    }
    
    // 加载活动数据
    recentActivities.value = [
      {
        id: 1,
        title: '新需求发布成功',
        description: 'CFM56发动机燃油喷嘴采购需求已发布',
        time: '2小时前',
        icon: Plus,
        iconColor: 'bg-primary-500',
        actionable: true,
        actionText: '查看详情'
      },
      {
        id: 2,
        title: '供应商报价',
        description: '收到CFM56发动机部件的竞价报价',
        time: '4小时前',
        icon: ShoppingCart,
        iconColor: 'bg-success-500',
        actionable: true,
        actionText: '查看报价'
      },
      {
        id: 3,
        title: '采购订单确认',
        description: '液压系统组件采购订单已确认',
        time: '6小时前',
        icon: Box,
        iconColor: 'bg-primary-500',
        actionable: true,
        actionText: '查看订单'
      },
      {
        id: 4,
        title: '新供应商加入',
        description: '新的航材供应商申请合作',
        time: '1天前',
        icon: Folder,
        iconColor: 'bg-blue-500',
        actionable: true,
        actionText: '审核申请'
      }
    ]
    
    // 加载采购相关数据
    
  } catch (error) {
    console.error('工作台初始化失败:', error)
  }
}

onMounted(() => {
  initializeWorkspace()
})
</script>

<style scoped>
.airline-buyer-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.procurement-center {
  @apply modern-card p-6;
}

.action-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.action-card.primary {
  @apply border-l-4 border-primary-500 hover:bg-primary-50;
}

.action-card.success {
  @apply border-l-4 border-success-500 hover:bg-success-50;
}

.action-card.warning {
  @apply border-l-4 border-warning-500 hover:bg-warning-50;
}

.action-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.action-card.purple {
  @apply border-l-4 border-purple-500 hover:bg-purple-50;
}

.action-card.orange {
  @apply border-l-4 border-orange-500 hover:bg-orange-50;
}

.action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.action-card.primary .action-icon {
  @apply bg-primary-100 text-primary-600;
}

.action-card.success .action-icon {
  @apply bg-success-100 text-success-600;
}

.action-card.warning .action-icon {
  @apply bg-warning-100 text-warning-600;
}

.action-card.blue .action-icon {
  @apply bg-blue-100 text-blue-600;
}

.action-card.purple .action-icon {
  @apply bg-purple-100 text-purple-600;
}

.action-card.orange .action-icon {
  @apply bg-orange-100 text-orange-600;
}

.action-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.action-desc {
  @apply text-gray-600 text-sm mb-3;
}

.action-stats {
  @apply text-xs;
}

.recent-activities {
  @apply modern-card p-6;
}

.activity-item {
  @apply flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors;
}

.activity-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center text-white flex-shrink-0;
}

.activity-content {
  @apply flex-1;
}

.activity-title {
  @apply font-medium text-gray-800;
}

.activity-desc {
  @apply text-sm text-gray-600 mt-1;
}

.activity-time {
  @apply text-xs text-gray-500 mt-2;
}

.overview-cards {
  @apply space-y-4;
}

.overview-card {
  @apply modern-card p-4 text-center;
}

.overview-value {
  @apply text-2xl font-bold;
}

.overview-label {
  @apply text-sm text-gray-600 mt-1;
}

.chart-bars {
  @apply w-full;
}

.chart-bar {
  @apply flex-1 mx-1;
}

.quick-links {
  @apply space-y-3;
}

.quick-link {
  @apply flex items-center space-x-3 text-gray-700 hover:text-primary-600 transition-colors p-2 rounded-lg hover:bg-gray-50;
}

.aog-panel {
  @apply shadow-sm;
}
</style>