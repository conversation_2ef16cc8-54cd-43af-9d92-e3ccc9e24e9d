<!--
维修工程师专属工作台
版本: 2.0
创建时间: 2025-07-19

专为维修工程师设计的工作台，专注于维修任务管理和技术支持
特点：
1. 维修工单管理
2. 工作进度跟踪
3. 技术文档支持
4. 设备状态监控
-->

<template>
  <div class="maintenance-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            维修工作中心
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.username }}
          </p>
          <p class="text-white/80 mt-2">
            高效管理维修任务，确保航材设备安全可靠运行
          </p>
        </div>
        <div class="welcome-stats hidden lg:flex space-x-8">
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.activeWorkOrders || 0 }}</div>
            <div class="text-white/80 text-sm">进行中工单</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.completedToday || 0 }}</div>
            <div class="text-white/80 text-sm">今日完成</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.efficiencyRate || 0 }}%</div>
            <div class="text-white/80 text-sm">完成效率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧工作区 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 紧急任务面板 -->
        <div class="urgent-tasks" v-if="urgentTasks.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-red-600 flex items-center">
              <Warning class="w-5 h-5 mr-2" />
              紧急任务
            </h2>
            <el-badge :value="urgentTasks.length" class="badge-danger">
              <el-button size="small" type="danger" @click="viewAllUrgentTasks">
                查看全部
              </el-button>
            </el-badge>
          </div>
          
          <div class="urgent-task-list space-y-3">
            <div 
              v-for="task in urgentTasks.slice(0, 3)" 
              :key="task.id"
              class="urgent-task-item"
              @click="handleUrgentTask(task)"
            >
              <div class="urgent-indicator">
                <component :is="task.icon" class="w-4 h-4" />
              </div>
              <div class="urgent-content">
                <div class="urgent-title">{{ task.title }}</div>
                <div class="urgent-desc">{{ task.description }}</div>
                <div class="urgent-time">剩余时间: {{ task.remainingTime }}</div>
              </div>
              <div class="urgent-priority" :class="task.priorityClass">
                {{ task.priority }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作面板 -->
        <div class="quick-actions">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">快速操作</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 创建工单 -->
            <div class="action-card primary" @click="handleCreateWorkOrder">
              <div class="action-icon">
                <Plus class="w-6 h-6" />
              </div>
              <h3 class="action-title">创建工单</h3>
              <p class="action-desc">创建新的维修工作单</p>
              <div class="action-stats">
                <span class="text-sm text-primary-600">本月已创建 {{ stats.monthlyCreated || 0 }} 个</span>
              </div>
            </div>

            <!-- 更新进度 -->
            <div class="action-card success" @click="handleUpdateProgress">
              <div class="action-icon">
                <User class="w-6 h-6" />
              </div>
              <h3 class="action-title">更新进度</h3>
              <p class="action-desc">更新维修工作进度</p>
              <div class="action-stats">
                <span class="text-sm text-success-600">{{ stats.pendingUpdates || 0 }} 个待更新</span>
              </div>
            </div>

            <!-- 申请备件 -->
            <div class="action-card warning" @click="handleRequestParts">
              <div class="action-icon">
                <ShoppingCart class="w-6 h-6" />
              </div>
              <h3 class="action-title">申请备件</h3>
              <p class="action-desc">申请维修所需备件</p>
              <div class="action-stats">
                <span class="text-sm text-warning-600">{{ stats.pendingRequests || 0 }} 个待审批</span>
              </div>
            </div>

            <!-- 技术支持 -->
            <div class="action-card blue" @click="handleTechnicalSupport">
              <div class="action-icon">
                <House class="w-6 h-6" />
              </div>
              <h3 class="action-title">技术支持</h3>
              <p class="action-desc">提供技术咨询服务</p>
              <div class="action-stats">
                <span class="text-sm text-blue-600">{{ stats.supportRequests || 0 }} 个咨询中</span>
              </div>
            </div>

            <!-- 技术文档 -->
            <div class="action-card purple" @click="handleTechnicalDocs">
              <div class="action-icon">
                <Document class="w-6 h-6" />
              </div>
              <h3 class="action-title">技术文档</h3>
              <p class="action-desc">查看维修技术文档</p>
              <div class="action-stats">
                <span class="text-sm text-purple-600">{{ stats.docCount || 0 }} 个文档</span>
              </div>
            </div>

            <!-- 质量检查 -->
            <div class="action-card orange" @click="handleQualityCheck">
              <div class="action-icon">
                <Search class="w-6 h-6" />
              </div>
              <h3 class="action-title">质量检查</h3>
              <p class="action-desc">执行维修质量检查</p>
              <div class="action-stats">
                <span class="text-sm text-orange-600">{{ stats.pendingChecks || 0 }} 个待检查</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 今日工作计划 -->
        <div class="today-schedule">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">今日工作计划</h2>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600">
                进度: {{ scheduleProgress }}% ({{ completedTasks }}/{{ totalTasks }})
              </span>
              <el-button size="small" @click="refreshSchedule">
                <Refresh class="w-4 h-4 mr-1" />
                刷新
              </el-button>
            </div>
          </div>

          <div class="schedule-timeline">
            <div class="timeline-progress">
              <el-progress 
                :percentage="scheduleProgress" 
                :stroke-width="8"
                :color="getProgressColor(scheduleProgress)"
              />
            </div>
            
            <div class="timeline-items space-y-4">
              <div 
                v-for="item in todaySchedule" 
                :key="item.id"
                class="timeline-item"
                :class="item.status"
              >
                <div class="timeline-indicator">
                  <component :is="item.icon" class="w-4 h-4" />
                </div>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="timeline-time">{{ item.time }}</span>
                    <span class="timeline-status">{{ item.statusText }}</span>
                  </div>
                  <div class="timeline-title">{{ item.title }}</div>
                  <div class="timeline-desc">{{ item.description }}</div>
                  <div class="timeline-meta">
                    <span class="timeline-location">{{ item.location }}</span>
                    <span class="timeline-duration">预计 {{ item.duration }}</span>
                  </div>
                </div>
                <div class="timeline-actions">
                  <el-button 
                    v-if="item.actionable && item.status === 'pending'"
                    size="small" 
                    type="primary"
                    @click="handleScheduleAction(item)"
                  >
                    开始
                  </el-button>
                  <el-button 
                    v-else-if="item.actionable && item.status === 'in-progress'"
                    size="small" 
                    type="success"
                    @click="handleScheduleAction(item)"
                  >
                    完成
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 工作统计卡片 -->
        <div class="work-stats grid grid-cols-2 gap-4">
          <div class="stat-card primary">
            <div class="stat-icon">
              <Tools class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.activeWorkOrders || 0 }}</div>
              <div class="stat-label">进行中工单</div>
            </div>
          </div>
          
          <div class="stat-card success">
            <div class="stat-icon">
              <Check class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completedToday || 0 }}</div>
              <div class="stat-label">今日完成</div>
            </div>
          </div>
          
          <div class="stat-card warning">
            <div class="stat-icon">
              <Warning class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.urgentTasks || 0 }}</div>
              <div class="stat-label">紧急任务</div>
            </div>
          </div>
          
          <div class="stat-card blue">
            <div class="stat-icon">
              <Clock class="w-6 h-6" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.avgDuration || 0 }}h</div>
              <div class="stat-label">平均用时</div>
            </div>
          </div>
        </div>

        <!-- 设备状态监控 -->
        <div class="equipment-status modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">设备状态监控</h3>
          <div class="equipment-list space-y-3">
            <div 
              v-for="equipment in equipmentStatus" 
              :key="equipment.id"
              class="equipment-item"
              :class="equipment.statusClass"
            >
              <div class="equipment-info">
                <div class="equipment-name">{{ equipment.name }}</div>
                <div class="equipment-location">{{ equipment.location }}</div>
              </div>
              <div class="equipment-status-indicator">
                <span class="status-dot" :class="equipment.statusClass"></span>
                <span class="status-text">{{ equipment.status }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具与资源 -->
        <div class="tools-resources modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">工具与资源</h3>
          <div class="resource-list space-y-3">
            <button class="resource-item" @click="openMaintenanceManual">
              <Document class="w-4 h-4" />
              <span>维修手册</span>
              <Download class="w-4 h-4" />
            </button>
            
            <button class="resource-item" @click="openPartsCatalog">
              <Folder class="w-4 h-4" />
              <span>备件目录</span>
              <Download class="w-4 h-4" />
            </button>
            
            <button class="resource-item" @click="openToolInventory">
              <Tools class="w-4 h-4" />
              <span>工具清单</span>
              <Download class="w-4 h-4" />
            </button>
            
            <button class="resource-item" @click="openSafetyGuidelines">
              <Warning class="w-4 h-4" />
              <span>安全指南</span>
              <Download class="w-4 h-4" />
            </button>
            
            <button class="resource-item" @click="openTechnicalSupport">
              <House class="w-4 h-4" />
              <span>技术支持</span>
              <Download class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- 近期工作历史 -->
        <div class="work-history modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">近期工作历史</h3>
          <div class="history-list space-y-3">
            <div 
              v-for="work in workHistory" 
              :key="work.id"
              class="history-item"
            >
              <div class="history-status" :class="work.statusClass">
                <component :is="work.icon" class="w-3 h-3" />
              </div>
              <div class="history-content">
                <div class="history-title">{{ work.title }}</div>
                <div class="history-time">{{ work.completedTime }}</div>
              </div>
              <div class="history-duration">{{ work.duration }}</div>
            </div>
          </div>
        </div>

        <!-- 性能指标 -->
        <div class="performance-metrics modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">本月性能指标</h3>
          <div class="metrics-chart">
            <div class="metric-item">
              <div class="metric-label">完成率</div>
              <div class="metric-bar">
                <div class="metric-progress" :style="{ width: `${performanceMetrics.completionRate}%` }"></div>
              </div>
              <div class="metric-value">{{ performanceMetrics.completionRate }}%</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">准时率</div>
              <div class="metric-bar">
                <div class="metric-progress on-time" :style="{ width: `${performanceMetrics.onTimeRate}%` }"></div>
              </div>
              <div class="metric-value">{{ performanceMetrics.onTimeRate }}%</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">质量分</div>
              <div class="metric-bar">
                <div class="metric-progress quality" :style="{ width: `${performanceMetrics.qualityScore}%` }"></div>
              </div>
              <div class="metric-value">{{ performanceMetrics.qualityScore }}分</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单操作弹窗 -->
    <CreateWorkOrderDialog v-model="showCreateWorkOrderDialog" />
    <ProgressUpdateDialog v-model="showProgressUpdateDialog" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Plus, Warning, User, ShoppingCart, House, Document, Search, Tools,
  Check, Clock, Refresh, Download, Folder
} from '@element-plus/icons-vue'
import CreateWorkOrderDialog from '@/components/CreateWorkOrderDialog.vue'
import ProgressUpdateDialog from '@/components/ProgressUpdateDialog.vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const stats = ref({})
const urgentTasks = ref([])
const todaySchedule = ref([])
const equipmentStatus = ref([])
const workHistory = ref([])
const performanceMetrics = ref({})
const showCreateWorkOrderDialog = ref(false)
const showProgressUpdateDialog = ref(false)

// 计算属性
const welcomeStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${theme.value.gradientFrom || '#722ed1'}, ${theme.value.gradientTo || '#eb2f96'})`
  }
})

const completedTasks = computed(() => {
  return todaySchedule.value.filter(item => item.status === 'completed').length
})

const totalTasks = computed(() => {
  return todaySchedule.value.length
})

const scheduleProgress = computed(() => {
  if (totalTasks.value === 0) return 0
  return Math.round((completedTasks.value / totalTasks.value) * 100)
})

// 方法
const getProgressColor = (percentage) => {
  if (percentage < 60) return '#ff4d4f'
  if (percentage < 80) return '#faad14'
  return '#52c41a'
}

// 紧急任务处理
const handleUrgentTask = (task) => {
  ElMessage.warning(`处理紧急任务: ${task.title}`)
  router.push(`/app/work-orders/${task.workOrderId}`)
}

const viewAllUrgentTasks = () => {
  router.push('/app/work-orders?priority=urgent')
}

// 快速操作处理
const handleCreateWorkOrder = () => {
  showCreateWorkOrderDialog.value = true
}

const handleUpdateProgress = () => {
  showProgressUpdateDialog.value = true
}

const handleRequestParts = () => {
  router.push('/app/parts-request')
}

const handleTechnicalSupport = () => {
  router.push('/app/technical-support')
}

const handleTechnicalDocs = () => {
  router.push('/app/technical-docs')
}

const handleQualityCheck = () => {
  router.push('/app/quality-check')
}

// 工作计划处理
const handleScheduleAction = (item) => {
  if (item.status === 'pending') {
    item.status = 'in-progress'
    item.statusText = '进行中'
    ElMessage.success(`开始工作: ${item.title}`)
  } else if (item.status === 'in-progress') {
    item.status = 'completed'
    item.statusText = '已完成'
    ElMessage.success(`完成工作: ${item.title}`)
  }
}

const refreshSchedule = () => {
  ElMessage.info('刷新工作计划')
  initializeSchedule()
}

// 资源链接处理
const openMaintenanceManual = () => {
  router.push('/app/maintenance-manual')
}

const openPartsCatalog = () => {
  router.push('/app/parts-catalog')
}

const openToolInventory = () => {
  router.push('/app/tool-inventory')
}

const openSafetyGuidelines = () => {
  router.push('/app/safety-guidelines')
}

const openTechnicalSupport = () => {
  router.push('/app/technical-support')
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载基础统计数据
    const baseStats = await getWorkspaceStats('activeWorkOrders')
    stats.value = {
      activeWorkOrders: 18,
      completedToday: 6,
      efficiencyRate: 95,
      monthlyCreated: 45,
      pendingUpdates: 8,
      pendingRequests: 3,
      supportRequests: 5,
      docCount: 128,
      pendingChecks: 4,
      urgentTasks: 3,
      avgDuration: 4.2
    }
    
    // 加载紧急任务
    urgentTasks.value = [
      {
        id: 1,
        workOrderId: 'WO-2024-001',
        title: 'AOG - 发动机燃油泵故障',
        description: '国航CA1234航班发动机燃油泵故障，飞机停场',
        remainingTime: '2小时30分',
        priority: 'AOG',
        priorityClass: 'priority-aog',
        icon: Warning
      },
      {
        id: 2,
        workOrderId: 'WO-2024-002',
        title: '起落架系统检查',
        description: '定期起落架系统检查，发现异常需立即处理',
        remainingTime: '4小时15分',
        priority: '紧急',
        priorityClass: 'priority-urgent',
        icon: Tools
      },
      {
        id: 3,
        workOrderId: 'WO-2024-003',
        title: '航电设备校准',
        description: '导航设备校准超期，需紧急处理',
        remainingTime: '6小时',
        priority: '高',
        priorityClass: 'priority-high',
        icon: Search
      }
    ]
    
    initializeSchedule()
    initializeEquipmentStatus()
    initializeWorkHistory()
    initializePerformanceMetrics()
    
  } catch (error) {
    console.error('工作台初始化失败:', error)
  }
}

const initializeSchedule = () => {
  todaySchedule.value = [
    {
      id: 1,
      time: '08:00',
      title: '班前安全会议',
      description: '参加每日安全简报会议',
      location: '维修车间',
      duration: '30分钟',
      status: 'completed',
      statusText: '已完成',
      actionable: false,
      icon: Document
    },
    {
      id: 2,
      time: '08:30',
      title: '发动机部件检查',
      description: 'CFM56发动机燃油喷嘴例行检查',
      location: '1号机库',
      duration: '2小时',
      status: 'in-progress',
      statusText: '进行中',
      actionable: true,
      icon: Tools
    },
    {
      id: 3,
      time: '11:00',
      title: '轮胎更换作业',
      description: 'A320主起落架轮胎更换',
      location: '2号机库',
      duration: '1.5小时',
      status: 'pending',
      statusText: '待开始',
      actionable: true,
      icon: Check
    },
    {
      id: 4,
      time: '14:00',
      title: '航电设备测试',
      description: '导航系统功能测试和校准',
      location: '航电车间',
      duration: '2小时',
      status: 'pending',
      statusText: '待开始',
      actionable: true,
      icon: Search
    },
    {
      id: 5,
      time: '16:30',
      title: '工作总结报告',
      description: '完成今日工作总结和明日计划',
      location: '办公室',
      duration: '30分钟',
      status: 'pending',
      statusText: '待开始',
      actionable: false,
      icon: Document
    }
  ]
}

const initializeEquipmentStatus = () => {
  equipmentStatus.value = [
    {
      id: 1,
      name: 'CFM56发动机',
      location: '1号机库',
      status: '正常',
      statusClass: 'status-normal'
    },
    {
      id: 2,
      name: '液压测试台',
      location: '液压车间',
      status: '维护中',
      statusClass: 'status-maintenance'
    },
    {
      id: 3,
      name: '起落架测试设备',
      location: '2号机库',
      status: '故障',
      statusClass: 'status-fault'
    },
    {
      id: 4,
      name: '航电测试系统',
      location: '航电车间',
      status: '正常',
      statusClass: 'status-normal'
    }
  ]
}

const initializeWorkHistory = () => {
  workHistory.value = [
    {
      id: 1,
      title: '发动机滑油滤芯更换',
      completedTime: '昨天 16:30',
      duration: '2.5h',
      statusClass: 'status-completed',
      icon: Check
    },
    {
      id: 2,
      title: '航电设备检查',
      completedTime: '昨天 14:00',
      duration: '1.8h',
      statusClass: 'status-completed',
      icon: Search
    },
    {
      id: 3,
      title: '轮胎气压检查',
      completedTime: '昨天 10:30',
      duration: '0.5h',
      statusClass: 'status-completed',
      icon: Check
    }
  ]
}

const initializePerformanceMetrics = () => {
  performanceMetrics.value = {
    completionRate: 95,
    onTimeRate: 88,
    qualityScore: 92
  }
}

onMounted(() => {
  initializeWorkspace()
})
</script>

<style scoped>
.maintenance-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.urgent-tasks {
  @apply modern-card p-6 border-l-4 border-red-500 bg-red-50;
}

.urgent-task-item {
  @apply flex items-center space-x-4 p-4 rounded-lg bg-white border hover:shadow-md transition-shadow cursor-pointer;
}

.urgent-indicator {
  @apply w-8 h-8 bg-red-100 text-red-600 rounded-lg flex items-center justify-center;
}

.urgent-content {
  @apply flex-1;
}

.urgent-title {
  @apply font-semibold text-gray-800;
}

.urgent-desc {
  @apply text-sm text-gray-600 mt-1;
}

.urgent-time {
  @apply text-xs text-red-600 mt-1 font-medium;
}

.urgent-priority {
  @apply px-2 py-1 rounded text-xs font-bold;
}

.priority-aog {
  @apply bg-red-600 text-white;
}

.priority-urgent {
  @apply bg-orange-500 text-white;
}

.priority-high {
  @apply bg-yellow-500 text-black;
}

.quick-actions {
  @apply modern-card p-6;
}

.action-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.action-card.primary {
  @apply border-l-4 border-primary-500 hover:bg-primary-50;
}

.action-card.success {
  @apply border-l-4 border-success-500 hover:bg-success-50;
}

.action-card.warning {
  @apply border-l-4 border-warning-500 hover:bg-warning-50;
}

.action-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.action-card.purple {
  @apply border-l-4 border-purple-500 hover:bg-purple-50;
}

.action-card.orange {
  @apply border-l-4 border-orange-500 hover:bg-orange-50;
}

.action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.action-card.primary .action-icon {
  @apply bg-primary-100 text-primary-600;
}

.action-card.success .action-icon {
  @apply bg-success-100 text-success-600;
}

.action-card.warning .action-icon {
  @apply bg-warning-100 text-warning-600;
}

.action-card.blue .action-icon {
  @apply bg-blue-100 text-blue-600;
}

.action-card.purple .action-icon {
  @apply bg-purple-100 text-purple-600;
}

.action-card.orange .action-icon {
  @apply bg-orange-100 text-orange-600;
}

.action-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.action-desc {
  @apply text-gray-600 text-sm mb-3;
}

.action-stats {
  @apply text-xs;
}

.today-schedule {
  @apply modern-card p-6;
}

.timeline-progress {
  @apply mb-6;
}

.timeline-item {
  @apply flex items-start space-x-4 p-4 rounded-lg transition-colors;
}

.timeline-item.pending {
  @apply bg-gray-50 border border-gray-200;
}

.timeline-item.in-progress {
  @apply bg-blue-50 border border-blue-200;
}

.timeline-item.completed {
  @apply bg-success-50 border border-success-200;
}

.timeline-indicator {
  @apply w-8 h-8 rounded-lg flex items-center justify-center text-white flex-shrink-0;
}

.timeline-item.pending .timeline-indicator {
  @apply bg-gray-400;
}

.timeline-item.in-progress .timeline-indicator {
  @apply bg-blue-500;
}

.timeline-item.completed .timeline-indicator {
  @apply bg-success-500;
}

.timeline-content {
  @apply flex-1;
}

.timeline-header {
  @apply flex items-center justify-between mb-2;
}

.timeline-time {
  @apply text-sm font-medium text-gray-700;
}

.timeline-status {
  @apply text-xs px-2 py-1 rounded;
}

.timeline-item.pending .timeline-status {
  @apply bg-gray-200 text-gray-700;
}

.timeline-item.in-progress .timeline-status {
  @apply bg-blue-200 text-blue-700;
}

.timeline-item.completed .timeline-status {
  @apply bg-success-200 text-success-700;
}

.timeline-title {
  @apply font-semibold text-gray-800 mb-1;
}

.timeline-desc {
  @apply text-sm text-gray-600 mb-2;
}

.timeline-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.work-stats {
  @apply grid-cols-2 gap-4;
}

.stat-card {
  @apply modern-card p-4 flex items-center space-x-3;
}

.stat-card.primary {
  @apply border-l-4 border-primary-500;
}

.stat-card.success {
  @apply border-l-4 border-success-500;
}

.stat-card.warning {
  @apply border-l-4 border-warning-500;
}

.stat-card.blue {
  @apply border-l-4 border-blue-500;
}

.stat-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.stat-card.primary .stat-icon {
  @apply bg-primary-100 text-primary-600;
}

.stat-card.success .stat-icon {
  @apply bg-success-100 text-success-600;
}

.stat-card.warning .stat-icon {
  @apply bg-warning-100 text-warning-600;
}

.stat-card.blue .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.stat-value {
  @apply text-xl font-bold text-gray-800;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.equipment-item {
  @apply flex items-center justify-between p-3 rounded-lg;
}

.equipment-item.status-normal {
  @apply bg-success-50 border border-success-200;
}

.equipment-item.status-maintenance {
  @apply bg-warning-50 border border-warning-200;
}

.equipment-item.status-fault {
  @apply bg-red-50 border border-red-200;
}

.equipment-name {
  @apply font-medium text-gray-800;
}

.equipment-location {
  @apply text-sm text-gray-600;
}

.equipment-status-indicator {
  @apply flex items-center space-x-2;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot.status-normal {
  @apply bg-success-500;
}

.status-dot.status-maintenance {
  @apply bg-warning-500;
}

.status-dot.status-fault {
  @apply bg-red-500;
}

.status-text {
  @apply text-sm font-medium;
}

.equipment-item.status-normal .status-text {
  @apply text-success-600;
}

.equipment-item.status-maintenance .status-text {
  @apply text-warning-600;
}

.equipment-item.status-fault .status-text {
  @apply text-red-600;
}

.resource-item {
  @apply w-full flex items-center justify-between p-3 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors;
}

.history-item {
  @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors;
}

.history-status {
  @apply w-6 h-6 rounded-full flex items-center justify-center;
}

.history-status.status-completed {
  @apply bg-success-100 text-success-600;
}

.history-title {
  @apply font-medium text-gray-800;
}

.history-time {
  @apply text-sm text-gray-500;
}

.history-duration {
  @apply text-sm text-gray-600 font-medium;
}

.metrics-chart {
  @apply space-y-4;
}

.metric-item {
  @apply space-y-2;
}

.metric-label {
  @apply text-sm font-medium text-gray-700;
}

.metric-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.metric-progress {
  @apply h-full bg-primary-500 transition-all duration-300;
}

.metric-progress.on-time {
  @apply bg-success-500;
}

.metric-progress.quality {
  @apply bg-blue-500;
}

.metric-value {
  @apply text-sm font-semibold text-gray-800;
}
</style>