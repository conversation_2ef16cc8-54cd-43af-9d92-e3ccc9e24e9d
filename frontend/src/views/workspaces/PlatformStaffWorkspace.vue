<!--
平台员工专属工作台
版本: 2.0
创建时间: 2025-07-19

专为平台员工设计的运营管理工作台，具备跨公司数据访问权限
特点：
1. 平台运营监控
2. 用户管理服务
3. 订单审核处理
4. 数据分析统计
-->

<template>
  <div class="platform-staff-workspace">
    <!-- 欢迎区域 -->
    <div class="welcome-section" :style="welcomeStyle">
      <div class="flex items-center justify-between">
        <div class="welcome-content">
          <h1 class="text-3xl font-bold text-white mb-2">
            运营管理中心
          </h1>
          <p class="text-white/90 text-lg">
            {{ roleName }} • {{ userStore.user?.username }}
          </p>
          <p class="text-white/80 mt-2">
            全面监控平台运营状况，提供高效的客户服务和业务支持
          </p>
        </div>
        <div class="welcome-stats hidden lg:flex space-x-8">
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.totalUsers || 0 }}</div>
            <div class="text-white/80 text-sm">平台用户</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">{{ stats.dailyOrders || 0 }}</div>
            <div class="text-white/80 text-sm">今日订单</div>
          </div>
          <div class="stat-item">
            <div class="text-3xl font-bold text-white">¥{{ formatCurrency(stats.dailyRevenue) }}</div>
            <div class="text-white/80 text-sm">今日收益</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="main-content grid grid-cols-1 xl:grid-cols-12 gap-8">
      <!-- 左侧操作面板 -->
      <div class="xl:col-span-8 space-y-8">
        <!-- 待处理任务面板 -->
        <div class="pending-tasks">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">待处理任务</h2>
            <el-badge :value="totalPendingTasks" :max="99" class="badge-primary">
              <el-button size="small" @click="viewAllTasks">查看全部</el-button>
            </el-badge>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 待审核共享件 -->
            <div class="task-card primary" @click="handlePendingSharedMaterials">
              <div class="task-header">
                <div class="task-icon">
                  <Warning class="w-5 h-5" />
                </div>
                <div class="task-count">{{ pendingTasks.sharedMaterials || 0 }}</div>
              </div>
              <div class="task-content">
                <h3 class="task-title">共享件审核</h3>
                <p class="task-desc">待审核的共享件发布申请</p>
              </div>
            </div>

            <!-- 待处理订单 -->
            <div class="task-card success" @click="handlePendingOrders">
              <div class="task-header">
                <div class="task-icon">
                  <Box class="w-5 h-5" />
                </div>
                <div class="task-count">{{ pendingTasks.orders || 0 }}</div>
              </div>
              <div class="task-content">
                <h3 class="task-title">订单处理</h3>
                <p class="task-desc">需要处理的平台订单</p>
              </div>
            </div>

            <!-- 用户审核 -->
            <div class="task-card warning" @click="handlePendingUsers">
              <div class="task-header">
                <div class="task-icon">
                  <User class="w-5 h-5" />
                </div>
                <div class="task-count">{{ pendingTasks.users || 0 }}</div>
              </div>
              <div class="task-content">
                <h3 class="task-title">用户审核</h3>
                <p class="task-desc">待审核的新用户注册</p>
              </div>
            </div>

            <!-- 客服工单 -->
            <div class="task-card blue" @click="handleCustomerService">
              <div class="task-header">
                <div class="task-icon">
                  <House class="w-5 h-5" />
                </div>
                <div class="task-count">{{ pendingTasks.tickets || 0 }}</div>
              </div>
              <div class="task-content">
                <h3 class="task-title">客服工单</h3>
                <p class="task-desc">待处理的客户问题</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速管理操作 -->
        <div class="quick-management">
          <h2 class="text-xl font-semibold text-gray-800 mb-6">快速管理</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 用户管理 -->
            <div class="management-card" @click="handleUserManagement">
              <div class="management-icon bg-primary-100 text-primary-600">
                <User class="w-6 h-6" />
              </div>
              <h3 class="management-title">用户管理</h3>
              <p class="management-desc">管理平台用户账户和权限</p>
              <div class="management-stats">
                <span class="text-sm text-primary-600">总计 {{ stats.totalUsers || 0 }} 用户</span>
              </div>
            </div>

            <!-- 库存统筹 -->
            <div class="management-card" @click="handleInventoryManagement">
              <div class="management-icon bg-success-100 text-success-600">
                <Folder class="w-6 h-6" />
              </div>
              <h3 class="management-title">库存统筹</h3>
              <p class="management-desc">跨公司库存统筹管理</p>
              <div class="management-stats">
                <span class="text-sm text-success-600">{{ stats.totalInventory || 0 }} 个库存项</span>
              </div>
            </div>

            <!-- 数据分析 -->
            <div class="management-card" @click="handleDataAnalysis">
              <div class="management-icon bg-blue-100 text-blue-600">
                <Tools class="w-6 h-6" />
              </div>
              <h3 class="management-title">数据分析</h3>
              <p class="management-desc">查看平台运营数据</p>
              <div class="management-stats">
                <span class="text-sm text-blue-600">实时监控</span>
              </div>
            </div>

            <!-- 系统配置 -->
            <div class="management-card" @click="handleSystemConfig">
              <div class="management-icon bg-purple-100 text-purple-600">
                <Setting class="w-6 h-6" />
              </div>
              <h3 class="management-title">系统配置</h3>
              <p class="management-desc">管理平台参数配置</p>
              <div class="management-stats">
                <span class="text-sm text-purple-600">系统健康</span>
              </div>
            </div>

            <!-- 内容管理 -->
            <div class="management-card" @click="handleContentManagement">
              <div class="management-icon bg-orange-100 text-orange-600">
                <Document class="w-6 h-6" />
              </div>
              <h3 class="management-title">内容管理</h3>
              <p class="management-desc">管理资讯和公告内容</p>
              <div class="management-stats">
                <span class="text-sm text-orange-600">{{ stats.pendingContent || 0 }} 个待审核</span>
              </div>
            </div>

            <!-- 消息通知 -->
            <div class="management-card" @click="handleNotificationManagement">
              <div class="management-icon bg-red-100 text-red-600">
                <Search class="w-6 h-6" />
              </div>
              <h3 class="management-title">消息通知</h3>
              <p class="management-desc">发送系统通知消息</p>
              <div class="management-stats">
                <span class="text-sm text-red-600">批量发送</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 平台活动监控 -->
        <div class="platform-activities">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">平台活动监控</h2>
            <div class="flex items-center space-x-4">
              <el-switch
                v-model="realTimeMonitoring"
                active-text="实时监控"
                @change="toggleRealTimeMonitoring"
              />
              <el-button size="small" @click="refreshActivities">
                <Refresh class="w-4 h-4 mr-1" />
                刷新
              </el-button>
            </div>
          </div>

          <div class="activity-monitor">
            <div class="activity-tabs mb-4">
              <el-button-group>
                <el-button 
                  v-for="tab in activityTabs"
                  :key="tab.key"
                  :type="activeMonitorTab === tab.key ? 'primary' : ''"
                  size="small"
                  @click="switchMonitorTab(tab.key)"
                >
                  {{ tab.label }}
                  <el-badge v-if="tab.count" :value="tab.count" class="ml-2" />
                </el-button>
              </el-button-group>
            </div>

            <div class="activity-content">
              <div 
                v-for="activity in filteredActivities" 
                :key="activity.id"
                class="activity-item"
                :class="`activity-${activity.level}`"
              >
                <div class="activity-indicator"></div>
                <div class="activity-details">
                  <div class="activity-header">
                    <span class="activity-type">{{ activity.type }}</span>
                    <span class="activity-time">{{ activity.time }}</span>
                  </div>
                  <div class="activity-message">{{ activity.message }}</div>
                  <div class="activity-meta">
                    <span class="activity-user">{{ activity.user }}</span>
                    <span class="activity-source">{{ activity.source }}</span>
                  </div>
                </div>
                <div class="activity-actions">
                  <el-button 
                    v-if="activity.actionable"
                    size="small" 
                    text 
                    type="primary"
                    @click="handleMonitorAction(activity)"
                  >
                    处理
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="xl:col-span-4 space-y-6">
        <!-- 系统状态卡片 -->
        <div class="system-status modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">系统状态</h3>
          <div class="space-y-4">
            <div class="status-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">CPU 使用率</span>
                <span class="text-sm font-medium">{{ systemStatus.cpu || 0 }}%</span>
              </div>
              <el-progress 
                :percentage="systemStatus.cpu || 0" 
                :show-text="false" 
                :stroke-width="6"
                :color="getProgressColor(systemStatus.cpu)"
              />
            </div>
            
            <div class="status-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">内存使用率</span>
                <span class="text-sm font-medium">{{ systemStatus.memory || 0 }}%</span>
              </div>
              <el-progress 
                :percentage="systemStatus.memory || 0" 
                :show-text="false" 
                :stroke-width="6"
                :color="getProgressColor(systemStatus.memory)"
              />
            </div>
            
            <div class="status-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">在线用户</span>
                <span class="text-sm font-medium text-success-600">{{ systemStatus.onlineUsers || 0 }}</span>
              </div>
            </div>
            
            <div class="status-item">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">响应时间</span>
                <span class="text-sm font-medium">{{ systemStatus.responseTime || 0 }}ms</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 业务统计 -->
        <div class="business-stats modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">今日业务统计</h3>
          <div class="stats-grid grid grid-cols-2 gap-4">
            <div class="stat-item text-center">
              <div class="stat-value text-primary-600">{{ todayStats.newUsers || 0 }}</div>
              <div class="stat-label">新增用户</div>
            </div>
            <div class="stat-item text-center">
              <div class="stat-value text-success-600">{{ todayStats.newOrders || 0 }}</div>
              <div class="stat-label">新增订单</div>
            </div>
            <div class="stat-item text-center">
              <div class="stat-value text-warning-600">{{ todayStats.completedOrders || 0 }}</div>
              <div class="stat-label">完成订单</div>
            </div>
            <div class="stat-item text-center">
              <div class="stat-value text-blue-600">{{ todayStats.activeDeals || 0 }}</div>
              <div class="stat-label">活跃交易</div>
            </div>
          </div>
        </div>

        <!-- 热门操作 -->
        <div class="hot-actions modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">热门操作</h3>
          <div class="space-y-3">
            <button class="hot-action-btn" @click="handleBulkMessage">
              <Search class="w-4 h-4" />
              <span>批量发送消息</span>
            </button>
            <button class="hot-action-btn" @click="handleExportData">
              <Download class="w-4 h-4" />
              <span>导出数据报表</span>
            </button>
            <button class="hot-action-btn" @click="handleSystemMaintenance">
              <Tools class="w-4 h-4" />
              <span>系统维护模式</span>
            </button>
            <button class="hot-action-btn" @click="handleBackupData">
              <FolderOpened class="w-4 h-4" />
              <span>数据备份</span>
            </button>
          </div>
        </div>

        <!-- 客户反馈 -->
        <div class="customer-feedback modern-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">客户反馈</h3>
          <div class="feedback-summary">
            <div class="feedback-score">
              <span class="score-value">4.8</span>
              <span class="score-label">平均评分</span>
            </div>
            <div class="feedback-stats">
              <div class="feedback-stat">
                <span class="stat-label">满意</span>
                <span class="stat-value text-success-600">{{ feedbackStats.satisfied || 0 }}</span>
              </div>
              <div class="feedback-stat">
                <span class="stat-label">一般</span>
                <span class="stat-value text-warning-600">{{ feedbackStats.neutral || 0 }}</span>
              </div>
              <div class="feedback-stat">
                <span class="stat-label">不满意</span>
                <span class="stat-value text-error-600">{{ feedbackStats.unsatisfied || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import {
  Warning, Box, User, House, Folder, Tools, Setting, Document,
  Search, Refresh, Download, FolderOpened
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()
const { theme, roleName, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const stats = ref({})
const pendingTasks = ref({})
const systemStatus = ref({})
const todayStats = ref({})
const feedbackStats = ref({})
const platformActivities = ref([])
const realTimeMonitoring = ref(true)
const activeMonitorTab = ref('all')
const refreshTimer = ref(null)

// 活动监控标签配置
const activityTabs = ref([
  { key: 'all', label: '全部', count: 0 },
  { key: 'orders', label: '订单', count: 0 },
  { key: 'users', label: '用户', count: 0 },
  { key: 'system', label: '系统', count: 0 },
  { key: 'errors', label: '异常', count: 0 }
])

// 计算属性
const welcomeStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${theme.value.gradientFrom || '#52c41a'}, ${theme.value.gradientTo || '#1890ff'})`
  }
})

const totalPendingTasks = computed(() => {
  return Object.values(pendingTasks.value).reduce((sum, count) => sum + (count || 0), 0)
})

const filteredActivities = computed(() => {
  if (activeMonitorTab.value === 'all') {
    return platformActivities.value
  }
  return platformActivities.value.filter(activity => activity.category === activeMonitorTab.value)
})

// 方法
const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const getProgressColor = (percentage) => {
  if (percentage < 60) return '#52c41a'
  if (percentage < 80) return '#faad14'
  return '#ff4d4f'
}

// 待处理任务操作
const handlePendingSharedMaterials = () => {
  router.push('/app/shared-materials/review')
}

const handlePendingOrders = () => {
  router.push('/app/orders?status=pending')
}

const handlePendingUsers = () => {
  router.push('/app/admin/users?status=pending')
}

const handleCustomerService = () => {
  router.push('/app/customer-service')
}

// 快速管理操作
const handleUserManagement = () => {
  router.push('/app/admin/users')
}

const handleInventoryManagement = () => {
  router.push('/app/inventory?view=platform')
}

const handleDataAnalysis = () => {
  router.push('/app/analytics')
}

const handleSystemConfig = () => {
  router.push('/app/admin/config')
}

const handleContentManagement = () => {
  router.push('/app/admin/content')
}

const handleNotificationManagement = () => {
  router.push('/app/admin/notifications')
}

// 活动监控
const switchMonitorTab = (tabKey) => {
  activeMonitorTab.value = tabKey
}

const toggleRealTimeMonitoring = (enabled) => {
  if (enabled) {
    startRealTimeMonitoring()
  } else {
    stopRealTimeMonitoring()
  }
}

const startRealTimeMonitoring = () => {
  refreshTimer.value = setInterval(refreshActivities, 10000) // 每10秒刷新
}

const stopRealTimeMonitoring = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const refreshActivities = async () => {
  try {
    // 模拟获取最新活动数据
    const newActivities = await getWorkspaceStats('platformActivities')
    if (newActivities) {
      platformActivities.value = newActivities
    }
  } catch (error) {
    console.error('刷新活动数据失败:', error)
  }
}

const handleMonitorAction = (activity) => {
  ElMessage.info(`处理活动: ${activity.message}`)
}

// 热门操作
const handleBulkMessage = () => {
  ElMessage.info('打开批量消息发送功能')
}

const handleExportData = () => {
  ElMessage.info('开始导出数据报表')
}

const handleSystemMaintenance = () => {
  ElMessage.warning('系统维护模式功能')
}

const handleBackupData = () => {
  ElMessage.info('开始数据备份')
}

const viewAllTasks = () => {
  router.push('/app/admin/tasks')
}

// 初始化数据
const initializeWorkspace = async () => {
  try {
    // 加载基础统计数据
    const baseStats = await getWorkspaceStats('platformOrderStats')
    stats.value = baseStats || {}
    
    // 加载待处理任务
    pendingTasks.value = {
      sharedMaterials: 8,
      orders: 12,
      users: 5,
      tickets: 7
    }
    
    // 加载系统状态
    systemStatus.value = {
      cpu: 45,
      memory: 62,
      onlineUsers: 156,
      responseTime: 245
    }
    
    // 加载今日统计
    todayStats.value = {
      newUsers: 23,
      newOrders: 45,
      completedOrders: 38,
      activeDeals: 67
    }
    
    // 加载客户反馈统计
    feedbackStats.value = {
      satisfied: 245,
      neutral: 32,
      unsatisfied: 8
    }
    
    // 加载平台活动
    platformActivities.value = [
      {
        id: 1,
        type: '订单处理',
        message: '用户张三创建了新的采购订单',
        time: '刚刚',
        level: 'info',
        category: 'orders',
        user: '张三',
        source: '航材市场',
        actionable: true
      },
      {
        id: 2,
        type: '用户注册',
        message: '新用户"上海航空维修公司"申请注册',
        time: '2分钟前',
        level: 'success',
        category: 'users',
        user: '系统',
        source: '用户注册',
        actionable: true
      },
      {
        id: 3,
        type: '系统异常',
        message: '支付接口响应超时，订单ID: 2024071901',
        time: '5分钟前',
        level: 'error',
        category: 'errors',
        user: '系统',
        source: '支付系统',
        actionable: true
      },
      {
        id: 4,
        type: '共享件审核',
        message: '发动机燃油喷嘴共享件申请审核',
        time: '10分钟前',
        level: 'warning',
        category: 'orders',
        user: '李四',
        source: '共享管理',
        actionable: true
      }
    ]
    
    // 更新活动标签计数
    updateMonitorTabCounts()
    
    // 启动实时监控
    if (realTimeMonitoring.value) {
      startRealTimeMonitoring()
    }
    
  } catch (error) {
    console.error('工作台初始化失败:', error)
  }
}

const updateMonitorTabCounts = () => {
  const counts = {
    all: platformActivities.value.length,
    orders: platformActivities.value.filter(a => a.category === 'orders').length,
    users: platformActivities.value.filter(a => a.category === 'users').length,
    system: platformActivities.value.filter(a => a.category === 'system').length,
    errors: platformActivities.value.filter(a => a.category === 'errors').length
  }
  
  activityTabs.value.forEach(tab => {
    tab.count = counts[tab.key] || 0
  })
}

onMounted(() => {
  initializeWorkspace()
})

onUnmounted(() => {
  stopRealTimeMonitoring()
})
</script>

<style scoped>
.platform-staff-workspace {
  @apply space-y-8;
}

.welcome-section {
  @apply rounded-xl p-8 shadow-lg;
}

.stat-item {
  @apply text-center;
}

.pending-tasks {
  @apply modern-card p-6;
}

.task-card {
  @apply modern-card p-4 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.task-card.primary {
  @apply border-l-4 border-primary-500 hover:bg-primary-50;
}

.task-card.success {
  @apply border-l-4 border-success-500 hover:bg-success-50;
}

.task-card.warning {
  @apply border-l-4 border-warning-500 hover:bg-warning-50;
}

.task-card.blue {
  @apply border-l-4 border-blue-500 hover:bg-blue-50;
}

.task-header {
  @apply flex items-center justify-between mb-3;
}

.task-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.task-card.primary .task-icon {
  @apply bg-primary-100 text-primary-600;
}

.task-card.success .task-icon {
  @apply bg-success-100 text-success-600;
}

.task-card.warning .task-icon {
  @apply bg-warning-100 text-warning-600;
}

.task-card.blue .task-icon {
  @apply bg-blue-100 text-blue-600;
}

.task-count {
  @apply text-xl font-bold text-gray-800;
}

.task-title {
  @apply font-semibold text-gray-800 mb-1;
}

.task-desc {
  @apply text-sm text-gray-600;
}

.quick-management {
  @apply modern-card p-6;
}

.management-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.management-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.management-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.management-desc {
  @apply text-gray-600 text-sm mb-3;
}

.management-stats {
  @apply text-xs;
}

.platform-activities {
  @apply modern-card p-6;
}

.activity-monitor {
  @apply space-y-4;
}

.activity-item {
  @apply flex items-start space-x-4 p-4 rounded-lg border-l-4 transition-colors;
}

.activity-item.activity-info {
  @apply border-blue-500 bg-blue-50;
}

.activity-item.activity-success {
  @apply border-success-500 bg-success-50;
}

.activity-item.activity-warning {
  @apply border-warning-500 bg-warning-50;
}

.activity-item.activity-error {
  @apply border-red-500 bg-red-50;
}

.activity-indicator {
  @apply w-2 h-2 rounded-full mt-2 flex-shrink-0;
}

.activity-item.activity-info .activity-indicator {
  @apply bg-blue-500;
}

.activity-item.activity-success .activity-indicator {
  @apply bg-success-500;
}

.activity-item.activity-warning .activity-indicator {
  @apply bg-warning-500;
}

.activity-item.activity-error .activity-indicator {
  @apply bg-red-500;
}

.activity-details {
  @apply flex-1;
}

.activity-header {
  @apply flex items-center justify-between mb-1;
}

.activity-type {
  @apply text-sm font-medium text-gray-800;
}

.activity-time {
  @apply text-xs text-gray-500;
}

.activity-message {
  @apply text-sm text-gray-700 mb-2;
}

.activity-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.system-status {
  @apply space-y-4;
}

.status-item {
  @apply space-y-2;
}

.business-stats {
  @apply space-y-4;
}

.stat-value {
  @apply text-2xl font-bold;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.hot-actions {
  @apply space-y-3;
}

.hot-action-btn {
  @apply w-full flex items-center space-x-3 p-3 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors;
}

.customer-feedback {
  @apply space-y-4;
}

.feedback-summary {
  @apply space-y-4;
}

.feedback-score {
  @apply text-center p-4 bg-gray-50 rounded-lg;
}

.score-value {
  @apply block text-3xl font-bold text-primary-600;
}

.score-label {
  @apply text-sm text-gray-600;
}

.feedback-stats {
  @apply grid grid-cols-3 gap-2 text-center;
}

.feedback-stat {
  @apply p-2 rounded-lg bg-gray-50;
}

.feedback-stat .stat-label {
  @apply block text-xs text-gray-600;
}

.feedback-stat .stat-value {
  @apply block text-lg font-semibold;
}
</style>