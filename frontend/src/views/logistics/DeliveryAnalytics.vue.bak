<!--
配送效率统计和分析面板
版本: 1.0
创建时间: 2025-07-23

物流专员专用的配送效率分析和统计报告界面
特点：
1. 配送效率多维度分析和对比
2. 时效性统计和趋势分析
3. 成本效益分析和优化建议
4. 区域配送表现对比
5. 可视化图表和数据导出
-->

<template>
  <div class="delivery-analytics">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">配送效率分析</h1>
          <p class="page-subtitle">深度分析配送效率，优化运营策略，提升整体配送表现</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshData">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleExportReport">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
          <el-button @click="handleScheduleReport" type="primary">
            <Calendar class="w-4 h-4 mr-2" />
            定时报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 时间筛选器 -->
    <div class="time-filter-panel">
      <div class="filter-content">
        <div class="filter-left">
          <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="quarter">本季</el-radio-button>
            <el-radio-button label="custom">自定义</el-radio-button>
          </el-radio-group>
          
          <div v-if="timeRange === 'custom'" class="custom-date-range">
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleCustomDateChange"
            />
          </div>
        </div>
        
        <div class="filter-right">
          <el-select v-model="selectedRegion" placeholder="选择区域" size="default">
            <el-option label="全部区域" value="all" />
            <el-option label="华东区域" value="east" />
            <el-option label="华南区域" value="south" />
            <el-option label="华北区域" value="north" />
            <el-option label="西南区域" value="southwest" />
          </el-select>
          
          <el-button @click="applyFilters" type="primary">
            <Filter class="w-4 h-4 mr-2" />
            应用筛选
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="metric-card delivery-rate">
          <div class="card-header">
            <div class="card-icon bg-green-100 text-green-600">
              <CircleCheck class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span :class="{ 'trend-up': metrics.deliveryRate.trend > 0, 'trend-down': metrics.deliveryRate.trend < 0 }">
                {{ metrics.deliveryRate.trend > 0 ? '+' : '' }}{{ metrics.deliveryRate.trend }}%
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ metrics.deliveryRate.value }}%</div>
            <div class="card-label">配送成功率</div>
            <div class="card-subtitle">较上期 {{ metrics.deliveryRate.comparison }}</div>
          </div>
        </div>
        
        <div class="metric-card on-time-rate">
          <div class="card-header">
            <div class="card-icon bg-blue-100 text-blue-600">
              <Clock class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span :class="{ 'trend-up': metrics.onTimeRate.trend > 0, 'trend-down': metrics.onTimeRate.trend < 0 }">
                {{ metrics.onTimeRate.trend > 0 ? '+' : '' }}{{ metrics.onTimeRate.trend }}%
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ metrics.onTimeRate.value }}%</div>
            <div class="card-label">准时交付率</div>
            <div class="card-subtitle">较上期 {{ metrics.onTimeRate.comparison }}</div>
          </div>
        </div>
        
        <div class="metric-card avg-time">
          <div class="card-header">
            <div class="card-icon bg-orange-100 text-orange-600">
              <Truck class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span :class="{ 'trend-up': metrics.avgDeliveryTime.trend > 0, 'trend-down': metrics.avgDeliveryTime.trend < 0 }">
                {{ metrics.avgDeliveryTime.trend > 0 ? '+' : '' }}{{ metrics.avgDeliveryTime.trend }}h
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ metrics.avgDeliveryTime.value }}h</div>
            <div class="card-label">平均配送时长</div>
            <div class="card-subtitle">较上期 {{ metrics.avgDeliveryTime.comparison }}</div>
          </div>
        </div>
        
        <div class="metric-card cost-efficiency">
          <div class="card-header">
            <div class="card-icon bg-purple-100 text-purple-600">
              <TrendingUp class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span :class="{ 'trend-up': metrics.costEfficiency.trend > 0, 'trend-down': metrics.costEfficiency.trend < 0 }">
                {{ metrics.costEfficiency.trend > 0 ? '+' : '' }}{{ metrics.costEfficiency.trend }}%
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ metrics.costEfficiency.value }}%</div>
            <div class="card-label">成本效率</div>
            <div class="card-subtitle">较上期 {{ metrics.costEfficiency.comparison }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要分析内容 -->
    <div class="main-content">
      <el-tabs v-model="activeAnalyticsTab" @tab-change="handleTabChange">
        <!-- 效率趋势分析 -->
        <el-tab-pane label="效率趋势" name="trends">
          <div class="trends-panel">
            <!-- 趋势图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <h3 class="chart-title">配送效率趋势分析</h3>
                <div class="chart-controls">
                  <el-radio-group v-model="trendMetric" size="small">
                    <el-radio-button label="delivery_rate">成功率</el-radio-button>
                    <el-radio-button label="on_time_rate">准时率</el-radio-button>
                    <el-radio-button label="avg_time">平均时长</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              
              <div class="chart-container">
                <!-- 图表组件占位 -->
                <div class="chart-placeholder">
                  <BarChart class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500 mb-2">配送效率趋势图表</p>
                  <p class="text-sm text-gray-400">集成图表库显示趋势数据和对比分析</p>
                  
                  <!-- 模拟图表数据展示 -->
                  <div class="trend-data">
                    <div class="data-points">
                      <div
                        v-for="(point, index) in trendData"
                        :key="index"
                        class="data-point"
                      >
                        <div class="point-date">{{ point.date }}</div>
                        <div class="point-value">{{ point.value }}{{ getTrendUnit(trendMetric) }}</div>
                        <div class="point-change" :class="{ positive: point.change > 0, negative: point.change < 0 }">
                          {{ point.change > 0 ? '+' : '' }}{{ point.change }}%
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 趋势分析总结 -->
            <div class="trend-summary">
              <div class="summary-cards">
                <div class="summary-card">
                  <div class="summary-header">
                    <TrendingUp class="w-5 h-5 text-green-500" />
                    <span class="summary-title">最佳表现</span>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ trendSummary.best.value }}</div>
                    <div class="summary-date">{{ trendSummary.best.date }}</div>
                  </div>
                </div>
                
                <div class="summary-card">
                  <div class="summary-header">
                    <TrendingDown class="w-5 h-5 text-red-500" />
                    <span class="summary-title">最低表现</span>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ trendSummary.worst.value }}</div>
                    <div class="summary-date">{{ trendSummary.worst.date }}</div>
                  </div>
                </div>
                
                <div class="summary-card">
                  <div class="summary-header">
                    <Monitor class="w-5 h-5 text-blue-500" />
                    <span class="summary-title">平均水平</span>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ trendSummary.average.value }}</div>
                    <div class="summary-date">期间平均</div>
                  </div>
                </div>
                
                <div class="summary-card">
                  <div class="summary-header">
                    <Target class="w-5 h-5 text-purple-500" />
                    <span class="summary-title">改善幅度</span>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ trendSummary.improvement.value }}%</div>
                    <div class="summary-date">{{ trendSummary.improvement.period }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 区域对比分析 -->
        <el-tab-pane label="区域对比" name="regions">
          <div class="regions-panel">
            <!-- 区域性能对比 -->
            <div class="region-comparison">
              <div class="comparison-header">
                <h3 class="text-lg font-semibold text-gray-800">区域配送表现对比</h3>
                <div class="comparison-controls">
                  <el-select v-model="comparisonMetric" size="small">
                    <el-option label="配送成功率" value="success_rate" />
                    <el-option label="准时交付率" value="on_time_rate" />
                    <el-option label="平均配送时长" value="avg_time" />
                    <el-option label="客户满意度" value="satisfaction" />
                  </el-select>
                </div>
              </div>
              
              <div class="region-list">
                <div
                  v-for="region in regionData"
                  :key="region.id"
                  class="region-item"
                  :class="getRegionRankClass(region.rank)"
                >
                  <div class="region-info">
                    <div class="region-name">{{ region.name }}</div>
                    <div class="region-details">
                      <span class="delivery-count">{{ region.deliveryCount }} 单</span>
                      <span class="coverage-area">覆盖 {{ region.coverageArea }} 个区域</span>
                    </div>
                  </div>
                  
                  <div class="region-metrics">
                    <div class="metric-value">{{ region.mainMetric }}{{ getMetricUnit(comparisonMetric) }}</div>
                    <div class="metric-trend" :class="{ positive: region.trend > 0, negative: region.trend < 0 }">
                      {{ region.trend > 0 ? '+' : '' }}{{ region.trend }}%
                    </div>
                  </div>
                  
                  <div class="region-rank">
                    <div class="rank-badge" :class="getRankBadgeClass(region.rank)">
                      {{ region.rank }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 区域详细统计 -->
            <div class="region-details-stats">
              <h4 class="text-md font-medium text-gray-700 mb-4">详细统计对比</h4>
              <div class="stats-table">
                <el-table :data="regionData" style="width: 100%">
                  <el-table-column prop="name" label="区域" width="120" />
                  <el-table-column prop="deliveryCount" label="配送量" width="100">
                    <template #default="{ row }">
                      {{ row.deliveryCount }} 单
                    </template>
                  </el-table-column>
                  <el-table-column prop="successRate" label="成功率" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'text-green-600': row.successRate >= 95, 'text-orange-600': row.successRate < 90 }">
                        {{ row.successRate }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="onTimeRate" label="准时率" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'text-green-600': row.onTimeRate >= 90, 'text-orange-600': row.onTimeRate < 80 }">
                        {{ row.onTimeRate }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="avgTime" label="平均时长" width="100">
                    <template #default="{ row }">
                      {{ row.avgTime }}h
                    </template>
                  </el-table-column>
                  <el-table-column prop="satisfaction" label="满意度" width="100">
                    <template #default="{ row }">
                      <el-rate
                        v-model="row.satisfaction"
                        disabled
                        show-score
                        text-color="#ff9900"
                        size="small"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="cost" label="平均成本" width="120">
                    <template #default="{ row }">
                      ¥{{ row.cost }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button @click="viewRegionDetails(row)" size="small">详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 成本效益分析 -->
        <el-tab-pane label="成本效益" name="cost-benefit">
          <div class="cost-benefit-panel">
            <!-- 成本结构分析 -->
            <div class="cost-structure">
              <div class="structure-header">
                <h3 class="text-lg font-semibold text-gray-800">配送成本结构分析</h3>
                <div class="structure-controls">
                  <el-radio-group v-model="costViewType" size="small">
                    <el-radio-button label="absolute">绝对值</el-radio-button>
                    <el-radio-button label="percentage">占比</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              
              <div class="cost-breakdown">
                <div class="breakdown-chart">
                  <!-- 饼图占位 -->
                  <div class="pie-chart-placeholder">
                    <PieChart class="w-20 h-20 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">成本结构饼图</p>
                  </div>
                </div>
                
                <div class="breakdown-list">
                  <div
                    v-for="(cost, index) in costBreakdown"
                    :key="index"
                    class="cost-item"
                  >
                    <div class="cost-category">
                      <div class="category-color" :style="{ backgroundColor: cost.color }"></div>
                      <span class="category-name">{{ cost.category }}</span>
                    </div>
                    <div class="cost-value">
                      <span class="amount">{{ costViewType === 'absolute' ? `¥${cost.amount}` : `${cost.percentage}%` }}</span>
                      <span class="change" :class="{ positive: cost.change > 0, negative: cost.change < 0 }">
                        {{ cost.change > 0 ? '+' : '' }}{{ cost.change }}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 成本效益指标 -->
            <div class="cost-efficiency-metrics">
              <h4 class="text-md font-medium text-gray-700 mb-4">关键效益指标</h4>
              <div class="metrics-grid">
                <div class="efficiency-card">
                  <div class="card-icon bg-green-100 text-green-600">
                    <DollarSign class="w-6 h-6" />
                  </div>
                  <div class="card-content">
                    <div class="card-value">¥{{ costEfficiencyMetrics.costPerDelivery }}</div>
                    <div class="card-label">单次配送成本</div>
                    <div class="card-change positive">-{{ costEfficiencyMetrics.costReduction }}% 本月</div>
                  </div>
                </div>
                
                <div class="efficiency-card">
                  <div class="card-icon bg-blue-100 text-blue-600">
                    <Truck class="w-6 h-6" />
                  </div>
                  <div class="card-content">
                    <div class="card-value">{{ costEfficiencyMetrics.deliveriesPerVehicle }}</div>
                    <div class="card-label">车均配送量</div>
                    <div class="card-change positive">+{{ costEfficiencyMetrics.vehicleEfficiency }}% 提升</div>
                  </div>
                </div>
                
                <div class="efficiency-card">
                  <div class="card-icon bg-orange-100 text-orange-600">
                    <Clock class="w-6 h-6" />
                  </div>
                  <div class="card-content">
                    <div class="card-value">{{ costEfficiencyMetrics.hoursUtilization }}%</div>
                    <div class="card-label">时间利用率</div>
                    <div class="card-change positive">+{{ costEfficiencyMetrics.timeImprovement }}% 改善</div>
                  </div>
                </div>
                
                <div class="efficiency-card">
                  <div class="card-icon bg-purple-100 text-purple-600">
                    <TrendingUp class="w-6 h-6" />
                  </div>
                  <div class="card-content">
                    <div class="card-value">{{ costEfficiencyMetrics.roi }}%</div>
                    <div class="card-label">投资回报率</div>
                    <div class="card-change positive">+{{ costEfficiencyMetrics.roiGrowth }}% 增长</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 优化建议 -->
            <div class="optimization-suggestions">
              <h4 class="text-md font-medium text-gray-700 mb-4">优化建议</h4>
              <div class="suggestions-list">
                <div
                  v-for="suggestion in optimizationSuggestions"
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="getSuggestionPriorityClass(suggestion.priority)"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-icon">
                      <component :is="getSuggestionIcon(suggestion.type)" class="w-5 h-5" />
                    </div>
                    <div class="suggestion-title">{{ suggestion.title }}</div>
                    <div class="suggestion-priority">
                      <el-tag :type="getSuggestionTagType(suggestion.priority)" size="small">
                        {{ getSuggestionPriorityText(suggestion.priority) }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="suggestion-content">
                    <p class="suggestion-description">{{ suggestion.description }}</p>
                    <div class="suggestion-impact">
                      <span class="impact-label">预期效果:</span>
                      <span class="impact-value">{{ suggestion.expectedImpact }}</span>
                    </div>
                  </div>
                  <div class="suggestion-actions">
                    <el-button @click="implementSuggestion(suggestion)" size="small" type="primary">
                      实施
                    </el-button>
                    <el-button @click="viewSuggestionDetails(suggestion)" size="small">
                      详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 性能报告 -->
        <el-tab-pane label="性能报告" name="reports">
          <div class="reports-panel">
            <!-- 报告生成器 -->
            <div class="report-generator">
              <div class="generator-header">
                <h3 class="text-lg font-semibold text-gray-800">自定义分析报告</h3>
                <el-button @click="generateReport" type="primary">
                  <Document class="w-4 h-4 mr-2" />
                  生成报告
                </el-button>
              </div>
              
              <div class="generator-options">
                <div class="option-group">
                  <label class="option-label">报告类型:</label>
                  <el-checkbox-group v-model="reportOptions.types">
                    <el-checkbox label="efficiency">效率分析</el-checkbox>
                    <el-checkbox label="cost">成本分析</el-checkbox>
                    <el-checkbox label="regional">区域对比</el-checkbox>
                    <el-checkbox label="trends">趋势分析</el-checkbox>
                  </el-checkbox-group>
                </div>
                
                <div class="option-group">
                  <label class="option-label">时间范围:</label>
                  <el-date-picker
                    v-model="reportOptions.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  />
                </div>
                
                <div class="option-group">
                  <label class="option-label">输出格式:</label>
                  <el-radio-group v-model="reportOptions.format">
                    <el-radio label="pdf">PDF</el-radio>
                    <el-radio label="excel">Excel</el-radio>
                    <el-radio label="html">HTML</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>

            <!-- 历史报告 -->
            <div class="historical-reports">
              <h4 class="text-md font-medium text-gray-700 mb-4">历史报告</h4>
              <div class="reports-table">
                <el-table :data="historicalReports" style="width: 100%">
                  <el-table-column prop="name" label="报告名称" width="200" />
                  <el-table-column prop="type" label="类型" width="120">
                    <template #default="{ row }">
                      <el-tag size="small">{{ row.type }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dateRange" label="时间范围" width="180" />
                  <el-table-column prop="generatedAt" label="生成时间" width="150" />
                  <el-table-column prop="size" label="文件大小" width="100" />
                  <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                      <el-button @click="downloadReport(row)" size="small" type="primary">
                        <Download class="w-3 h-3 mr-1" />
                        下载
                      </el-button>
                      <el-button @click="viewReport(row)" size="small">
                        <Eye class="w-3 h-3 mr-1" />
                        查看
                      </el-button>
                      <el-button @click="deleteReport(row)" size="small" type="danger">
                        <Trash class="w-3 h-3 mr-1" />
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Refresh, Download, Calendar, Filter, CircleCheck, Clock, Truck, TrendingUp,
  TrendingDown, Monitor, Target, BarChart, PieChart, DollarSign, Document,
  Eye, Trash, MapPin, User, Settings, Zap, Warning
} from 'lucide-vue-next'

// 响应式数据
const activeAnalyticsTab = ref('trends')
const timeRange = ref('month')
const customDateRange = ref([])
const selectedRegion = ref('all')
const trendMetric = ref('delivery_rate')
const comparisonMetric = ref('success_rate')
const costViewType = ref('absolute')

// 核心指标数据
const metrics = reactive({
  deliveryRate: {
    value: 97.5,
    trend: 2.3,
    comparison: '提升2.3%'
  },
  onTimeRate: {
    value: 94.2,
    trend: 1.8,
    comparison: '提升1.8%'
  },
  avgDeliveryTime: {
    value: 4.2,
    trend: -0.5,
    comparison: '缩短0.5h'
  },
  costEfficiency: {
    value: 89.6,
    trend: 3.1,
    comparison: '提升3.1%'
  }
})

// 趋势数据
const trendData = ref([
  { date: '07-16', value: 96.2, change: 1.2 },
  { date: '07-17', value: 97.1, change: 0.9 },
  { date: '07-18', value: 96.8, change: -0.3 },
  { date: '07-19', value: 98.2, change: 1.4 },
  { date: '07-20', value: 97.9, change: -0.3 },
  { date: '07-21', value: 98.5, change: 0.6 },
  { date: '07-22', value: 97.5, change: -1.0 }
])

// 趋势总结
const trendSummary = reactive({
  best: { value: '98.5%', date: '07-21' },
  worst: { value: '96.2%', date: '07-16' },
  average: { value: '97.4%' },
  improvement: { value: '1.3', period: '本周' }
})

// 区域数据
const regionData = ref([
  {
    id: 1,
    name: '华东区域',
    deliveryCount: 1248,
    coverageArea: 15,
    mainMetric: 98.2,
    successRate: 98.2,
    onTimeRate: 95.8,
    avgTime: 3.8,
    satisfaction: 4.6,
    cost: 85.5,
    trend: 2.1,
    rank: 1
  },
  {
    id: 2,
    name: '华南区域',
    deliveryCount: 987,
    coverageArea: 12,
    mainMetric: 97.5,
    successRate: 97.5,
    onTimeRate: 94.2,
    avgTime: 4.2,
    satisfaction: 4.4,
    cost: 92.3,
    trend: 1.8,
    rank: 2
  },
  {
    id: 3,
    name: '华北区域',
    deliveryCount: 1156,
    coverageArea: 18,
    mainMetric: 96.8,
    successRate: 96.8,
    onTimeRate: 92.5,
    avgTime: 4.6,
    satisfaction: 4.2,
    cost: 98.7,
    trend: -0.5,
    rank: 3
  },
  {
    id: 4,
    name: '西南区域',
    deliveryCount: 743,
    coverageArea: 10,
    mainMetric: 95.2,
    successRate: 95.2,
    onTimeRate: 89.8,
    avgTime: 5.2,
    satisfaction: 4.0,
    cost: 106.2,
    trend: -1.2,
    rank: 4
  }
])

// 成本结构数据
const costBreakdown = ref([
  { category: '燃油费用', amount: 12580, percentage: 35.2, change: 2.3, color: '#3b82f6' },
  { category: '人工成本', amount: 9840, percentage: 27.5, change: 1.8, color: '#10b981' },
  { category: '车辆折旧', amount: 6230, percentage: 17.4, change: -0.5, color: '#f59e0b' },
  { category: '保险费用', amount: 3960, percentage: 11.1, change: 0.2, color: '#ef4444' },
  { category: '维修保养', amount: 2180, percentage: 6.1, change: -1.2, color: '#8b5cf6' },
  { category: '其他费用', amount: 980, percentage: 2.7, change: 0.8, color: '#64748b' }
])

// 成本效益指标
const costEfficiencyMetrics = reactive({
  costPerDelivery: 28.5,
  costReduction: 3.2,
  deliveriesPerVehicle: 24.6,
  vehicleEfficiency: 8.5,
  hoursUtilization: 87.3,
  timeImprovement: 5.2,
  roi: 125.8,
  roiGrowth: 12.3
})

// 优化建议
const optimizationSuggestions = ref([
  {
    id: 1,
    type: 'route',
    priority: 'high',
    title: '优化配送路线规划',
    description: '采用AI智能算法优化路线，预计可减少15%的行驶距离，降低燃油成本',
    expectedImpact: '节省成本15%，减少配送时间20分钟'
  },
  {
    id: 2,
    type: 'schedule',
    priority: 'medium',
    title: '调整配送时间窗口',
    description: '根据交通流量数据调整配送时间，避开高峰期，提升配送效率',
    expectedImpact: '提升准时率8%，降低等待时间30%'
  },
  {
    id: 3,
    type: 'vehicle',
    priority: 'high',
    title: '增加新能源车辆',
    description: '逐步替换传统燃油车为新能源车辆，降低运营成本和环境影响',
    expectedImpact: '降低燃油成本40%，减少碳排放60%'
  },
  {
    id: 4,
    type: 'technology',
    priority: 'low',
    title: '引入智能调度系统',
    description: '升级调度系统，实现实时动态调度，提升整体运营效率',
    expectedImpact: '提升调度效率25%，减少空载率18%'
  }
])

// 报告选项
const reportOptions = reactive({
  types: ['efficiency', 'cost'],
  dateRange: [],
  format: 'pdf'
})

// 历史报告
const historicalReports = ref([
  {
    id: 1,
    name: '7月配送效率分析报告',
    type: '效率分析',
    dateRange: '2025-07-01 至 2025-07-22',
    generatedAt: '2025-07-23 09:30',
    size: '2.5MB'
  },
  {
    id: 2,
    name: '区域配送对比报告',
    type: '区域分析',
    dateRange: '2025-07-01 至 2025-07-15',
    generatedAt: '2025-07-16 14:20',
    size: '1.8MB'
  },
  {
    id: 3,
    name: '成本效益优化报告',
    type: '成本分析',
    dateRange: '2025-06-01 至 2025-06-30',
    generatedAt: '2025-07-01 16:45',
    size: '3.2MB'
  }
])

// 方法定义
const handleTabChange = (tabName) => {
  activeAnalyticsTab.value = tabName
}

const handleRefreshData = () => {
  ElMessage.success('数据已刷新')
}

const handleExportReport = () => {
  ElMessage.success('报告导出中...')
}

const handleScheduleReport = () => {
  ElMessage.info('设置定时报告')
}

const handleTimeRangeChange = (value) => {
  timeRange.value = value
  if (value !== 'custom') {
    customDateRange.value = []
  }
}

const handleCustomDateChange = (value) => {
  if (value) {
    ElMessage.success(`已选择时间范围: ${value[0]} 至 ${value[1]}`)
  }
}

const applyFilters = () => {
  ElMessage.success('筛选条件已应用')
}

const viewRegionDetails = (region) => {
  ElMessage.success(`查看 ${region.name} 详细信息`)
}

const implementSuggestion = (suggestion) => {
  ElMessage.success(`开始实施: ${suggestion.title}`)
}

const viewSuggestionDetails = (suggestion) => {
  ElMessage.info(`查看建议详情: ${suggestion.title}`)
}

const generateReport = () => {
  if (reportOptions.types.length === 0) {
    ElMessage.warning('请至少选择一种报告类型')
    return
  }
  ElMessage.success('报告生成中，请稍候...')
}

const downloadReport = (report) => {
  ElMessage.success(`下载报告: ${report.name}`)
}

const viewReport = (report) => {
  ElMessage.info(`查看报告: ${report.name}`)
}

const deleteReport = (report) => {
  ElMessageBox.confirm(
    `确定要删除报告 "${report.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('报告已删除')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 辅助方法
const getTrendUnit = (metric) => {
  const units = {
    'delivery_rate': '%',
    'on_time_rate': '%',
    'avg_time': 'h'
  }
  return units[metric] || ''
}

const getMetricUnit = (metric) => {
  const units = {
    'success_rate': '%',
    'on_time_rate': '%',
    'avg_time': 'h',
    'satisfaction': '分'
  }
  return units[metric] || ''
}

const getRegionRankClass = (rank) => {
  const classes = {
    1: 'rank-first',
    2: 'rank-second',
    3: 'rank-third'
  }
  return classes[rank] || 'rank-other'
}

const getRankBadgeClass = (rank) => {
  const classes = {
    1: 'badge-gold',
    2: 'badge-silver',
    3: 'badge-bronze'
  }
  return classes[rank] || 'badge-default'
}

const getSuggestionPriorityClass = (priority) => {
  return `suggestion-${priority}`
}

const getSuggestionIcon = (type) => {
  const icons = {
    'route': MapPin,
    'schedule': Clock,
    'vehicle': Truck,
    'technology': Settings
  }
  return icons[type] || Settings
}

const getSuggestionTagType = (priority) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[priority] || 'info'
}

const getSuggestionPriorityText = (priority) => {
  const texts = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return texts[priority] || priority
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  console.log('配送效率分析组件已加载')
})
</script>

<style scoped>
.delivery-analytics {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.time-filter-panel {
  background: white;
  border-radius: 12px;
  padding: 20px 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.custom-date-range {
  margin-left: 16px;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metrics-overview {
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-trend {
  font-size: 14px;
  font-weight: 600;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.card-content {
  text-align: left;
}

.card-value {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 8px;
}

.card-label {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 14px;
  color: #94a3b8;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-section {
  margin-bottom: 32px;
  padding: 0 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.chart-container {
  background: #f8fafc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.chart-placeholder {
  text-align: center;
}

.trend-data {
  margin-top: 32px;
}

.data-points {
  display: flex;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
}

.data-point {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 80px;
}

.point-date {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.point-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.point-change {
  font-size: 12px;
  font-weight: 500;
}

.point-change.positive {
  color: #10b981;
}

.point-change.negative {
  color: #ef4444;
}

.trend-summary {
  padding: 0 24px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  text-align: center;
}

.summary-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.summary-title {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.summary-date {
  font-size: 12px;
  color: #94a3b8;
}

.regions-panel {
  padding: 24px;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.region-list {
  margin-bottom: 32px;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-bottom: 12px;
  transition: all 0.2s;
}

.region-item:hover {
  border-color: #3b82f6;
  transform: translateX(4px);
}

.region-item.rank-first {
  border-color: #fbbf24;
  background: #fffbeb;
}

.region-item.rank-second {
  border-color: #94a3b8;
  background: #f8fafc;
}

.region-item.rank-third {
  border-color: #cd7c0e;
  background: #fef3c7;
}

.region-info {
  flex: 1;
}

.region-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.region-details {
  font-size: 13px;
  color: #64748b;
}

.region-details span {
  margin-right: 16px;
}

.region-metrics {
  text-align: center;
  margin: 0 24px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #ef4444;
}

.region-rank {
  text-align: center;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
}

.badge-gold {
  background: #fbbf24;
  border: 2px solid #f59e0b;
}

.badge-silver {
  background: #94a3b8;
  border: 2px solid #6b7280;
}

.badge-bronze {
  background: #cd7c0e;
  border: 2px solid #92400e;
}

.badge-default {
  background: #64748b;
  border: 2px solid #475569;
}

.region-details-stats {
  margin-top: 32px;
}

.stats-table {
  margin-top: 16px;
}

.cost-benefit-panel {
  padding: 24px;
}

.cost-structure {
  margin-bottom: 32px;
}

.structure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.cost-breakdown {
  display: flex;
  gap: 32px;
  align-items: center;
}

.breakdown-chart {
  flex-shrink: 0;
}

.pie-chart-placeholder {
  width: 200px;
  height: 200px;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
}

.breakdown-list {
  flex: 1;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-category {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.category-name {
  font-weight: 500;
  color: #1a202c;
}

.cost-value {
  text-align: right;
}

.amount {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-right: 8px;
}

.change {
  font-size: 12px;
  font-weight: 500;
}

.change.positive {
  color: #10b981;
}

.change.negative {
  color: #ef4444;
}

.cost-efficiency-metrics {
  margin-bottom: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.efficiency-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.card-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-change {
  font-size: 12px;
  font-weight: 500;
}

.card-change.positive {
  color: #10b981;
}

.optimization-suggestions {
  margin-top: 32px;
}

.suggestions-list {
  margin-top: 16px;
}

.suggestion-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.suggestion-item.suggestion-high {
  border-left: 4px solid #ef4444;
}

.suggestion-item.suggestion-medium {
  border-left: 4px solid #f59e0b;
}

.suggestion-item.suggestion-low {
  border-left: 4px solid #3b82f6;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.suggestion-icon {
  color: #64748b;
}

.suggestion-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.suggestion-content {
  margin-bottom: 16px;
}

.suggestion-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 8px;
}

.suggestion-impact {
  font-size: 14px;
}

.impact-label {
  color: #64748b;
  margin-right: 8px;
}

.impact-value {
  color: #10b981;
  font-weight: 500;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.reports-panel {
  padding: 24px;
}

.report-generator {
  background: #f8fafc;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
}

.generator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.generator-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.option-label {
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.historical-reports {
  margin-top: 32px;
}

.reports-table {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filter-left {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .filter-right {
    width: 100%;
    justify-content: flex-start;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cost-breakdown {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .delivery-analytics {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .data-points {
    flex-direction: column;
    gap: 12px;
  }
  
  .region-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .region-metrics {
    margin: 0;
  }
  
  .generator-options {
    gap: 16px;
  }
  
  .option-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .option-label {
    min-width: auto;
  }
}
</style>