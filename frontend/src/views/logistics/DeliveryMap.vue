<!--
配送地图和实时跟踪功能
版本: 1.0
创建时间: 2025-07-23

物流专员专用的配送地图和实时位置跟踪界面
特点：
1. 可视化地图显示配送路线和位置
2. 实时GPS跟踪和位置更新
3. 配送进度可视化和状态展示
4. 路径优化和导航支持
5. 地理围栏和区域管理
-->

<template>
  <div class="delivery-map">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">配送地图中心</h1>
          <p class="page-subtitle">实时可视化配送路线，精确跟踪货物位置，优化配送效率</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshMap">
            <Refresh class="w-4 h-4 mr-2" />
            刷新地图
          </el-button>
          <el-button @click="handleFullScreen">
            <Maximize class="w-4 h-4 mr-2" />
            全屏显示
          </el-button>
          <el-button @click="handleEmergencyMode" type="danger">
            <Warning class="w-4 h-4 mr-2" />
            紧急模式
          </el-button>
        </div>
      </div>
    </div>

    <!-- 地图控制面板 -->
    <div class="map-control-panel">
      <div class="control-left">
        <!-- 地图筛选器 -->
        <div class="map-filters">
          <el-select v-model="selectedVehicleType" placeholder="车辆类型" size="small">
            <el-option label="全部车辆" value="all" />
            <el-option label="货车" value="truck" />
            <el-option label="快递车" value="express" />
            <el-option label="专车" value="special" />
          </el-select>
          
          <el-select v-model="selectedStatus" placeholder="配送状态" size="small">
            <el-option label="全部状态" value="all" />
            <el-option label="配送中" value="delivering" />
            <el-option label="等待装货" value="loading" />
            <el-option label="返回途中" value="returning" />
            <el-option label="已完成" value="completed" />
          </el-select>
          
          <el-select v-model="selectedRegion" placeholder="配送区域" size="small">
            <el-option label="全部区域" value="all" />
            <el-option label="华东区域" value="east" />
            <el-option label="华南区域" value="south" />
            <el-option label="华北区域" value="north" />
            <el-option label="西南区域" value="southwest" />
          </el-select>
          
          <el-button @click="applyFilters" type="primary" size="small">
            <Filter class="w-4 h-4 mr-1" />
            应用筛选
          </el-button>
        </div>
      </div>
      
      <div class="control-right">
        <!-- 地图视图控制 -->
        <div class="view-controls">
          <el-radio-group v-model="mapViewMode" size="small">
            <el-radio-button label="satellite">卫星</el-radio-button>
            <el-radio-button label="roadmap">路网</el-radio-button>
            <el-radio-button label="terrain">地形</el-radio-button>
          </el-radio-group>
          
          <div class="zoom-controls">
            <el-button-group size="small">
              <el-button @click="zoomIn">
                <Plus class="w-4 h-4" />
              </el-button>
              <el-button @click="zoomOut">
                <Minus class="w-4 h-4" />
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 主地图区域 -->
    <div class="map-container">
      <div class="map-wrapper">
        <!-- 地图组件 -->
        <div class="map-canvas" ref="mapCanvas">
          <div class="map-placeholder">
            <div class="placeholder-content">
              <Map class="w-24 h-24 text-gray-400 mx-auto mb-6" />
              <h3 class="text-xl font-semibold text-gray-600 mb-3">实时配送地图</h3>
              <p class="text-gray-500 mb-6">集成高德地图/百度地图 API，显示实时配送路线和车辆位置</p>
              
              <!-- 模拟地图数据展示 -->
              <div class="map-simulation">
                <div class="simulation-vehicles">
                  <h4 class="text-lg font-medium text-gray-700 mb-4">当前配送车辆</h4>
                  <div class="vehicle-list">
                    <div
                      v-for="vehicle in visibleVehicles"
                      :key="vehicle.id"
                      class="vehicle-item"
                      :class="{ active: selectedVehicle?.id === vehicle.id }"
                      @click="selectVehicle(vehicle)"
                    >
                      <div class="vehicle-icon">
                        <Van :class="`w-5 h-5 ${getVehicleStatusColor(vehicle.status)}`" />
                      </div>
                      <div class="vehicle-info">
                        <div class="vehicle-id">{{ vehicle.plateNumber }}</div>
                        <div class="vehicle-status">{{ getVehicleStatusText(vehicle.status) }}</div>
                      </div>
                      <div class="vehicle-location">{{ vehicle.currentLocation }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 地图图例 -->
        <div class="map-legend">
          <div class="legend-title">图例说明</div>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color bg-blue-500"></div>
              <span>配送中</span>
            </div>
            <div class="legend-item">
              <div class="legend-color bg-green-500"></div>
              <span>已到达</span>
            </div>
            <div class="legend-item">
              <div class="legend-color bg-orange-500"></div>
              <span>等待中</span>
            </div>
            <div class="legend-item">
              <div class="legend-color bg-red-500"></div>
              <span>异常</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧信息面板 -->
      <div class="info-panel">
        <!-- 实时统计 -->
        <div class="panel-section">
          <div class="section-header">
            <h3 class="section-title">实时统计</h3>
            <Monitor class="w-5 h-5 text-blue-500" />
          </div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ mapStats.activeVehicles }}</div>
              <div class="stat-label">配送车辆</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ mapStats.totalRoutes }}</div>
              <div class="stat-label">配送路线</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ mapStats.completedDeliveries }}</div>
              <div class="stat-label">完成配送</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ mapStats.avgSpeed }} km/h</div>
              <div class="stat-label">平均速度</div>
            </div>
          </div>
        </div>
        
        <!-- 选中车辆详情 -->
        <div v-if="selectedVehicle" class="panel-section">
          <div class="section-header">
            <h3 class="section-title">车辆详情</h3>
            <Van class="w-5 h-5 text-green-500" />
          </div>
          <div class="vehicle-details">
            <div class="detail-row">
              <span class="detail-label">车牌号:</span>
              <span class="detail-value">{{ selectedVehicle.plateNumber }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">司机:</span>
              <span class="detail-value">{{ selectedVehicle.driverName }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">状态:</span>
              <el-tag :type="getVehicleTagType(selectedVehicle.status)" size="small">
                {{ getVehicleStatusText(selectedVehicle.status) }}
              </el-tag>
            </div>
            <div class="detail-row">
              <span class="detail-label">当前位置:</span>
              <span class="detail-value">{{ selectedVehicle.currentLocation }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">速度:</span>
              <span class="detail-value">{{ selectedVehicle.speed }} km/h</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">载货量:</span>
              <span class="detail-value">{{ selectedVehicle.loadCapacity }}%</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">预计到达:</span>
              <span class="detail-value">{{ selectedVehicle.estimatedArrival }}</span>
            </div>
          </div>
          <div class="vehicle-actions">
            <el-button @click="contactDriver" size="small" type="primary">
              <Phone class="w-4 h-4 mr-1" />
              联系司机
            </el-button>
            <el-button @click="viewVehicleRoute" size="small">
              <Route class="w-4 h-4 mr-1" />
              查看路线
            </el-button>
            <el-button @click="trackVehicle" size="small">
              <Position class="w-4 h-4 mr-1" />
              实时跟踪
            </el-button>
          </div>
        </div>
        
        <!-- 配送任务列表 -->
        <div class="panel-section">
          <div class="section-header">
            <h3 class="section-title">今日任务</h3>
            <CircleCheck class="w-5 h-5 text-orange-500" />
          </div>
          <div class="task-list">
            <div
              v-for="task in todayTasks"
              :key="task.id"
              class="task-item"
              :class="getTaskStatusClass(task.status)"
            >
              <div class="task-header">
                <span class="task-id">{{ task.taskNumber }}</span>
                <el-tag :type="getTaskTagType(task.status)" size="small">
                  {{ getTaskStatusText(task.status) }}
                </el-tag>
              </div>
              <div class="task-details">
                <div class="task-customer">{{ task.customerName }}</div>
                <div class="task-address">{{ task.deliveryAddress }}</div>
                <div class="task-time">{{ task.scheduledTime }}</div>
              </div>
              <div class="task-progress">
                <el-progress
                  :percentage="task.progress"
                  :color="getTaskProgressColor(task.progress)"
                  :show-text="false"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 异常告警 -->
        <div v-if="alerts.length > 0" class="panel-section alert-section">
          <div class="section-header">
            <h3 class="section-title">异常告警</h3>
            <Warning class="w-5 h-5 text-red-500" />
          </div>
          <div class="alert-list">
            <div
              v-for="alert in alerts"
              :key="alert.id"
              class="alert-item"
              :class="getAlertLevelClass(alert.level)"
            >
              <div class="alert-icon">
                <component :is="getAlertIcon(alert.type)" class="w-4 h-4" />
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-description">{{ alert.description }}</div>
                <div class="alert-time">{{ alert.timestamp }}</div>
              </div>
              <div class="alert-actions">
                <el-button @click="handleAlert(alert)" size="small">处理</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图工具栏 -->
    <div class="map-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button @click="showAllVehicles">
            <View class="w-4 h-4 mr-1" />
            显示全部
          </el-button>
          <el-button @click="hideAllVehicles">
            <Hide class="w-4 h-4 mr-1" />
            隐藏全部
          </el-button>
          <el-button @click="centerMap">
            <Target class="w-4 h-4 mr-1" />
            居中显示
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <div class="live-indicator">
          <div class="live-dot"></div>
          <span>实时更新中</span>
          <span class="update-time">最后更新: {{ lastUpdateTime }}</span>
        </div>
      </div>
      
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="exportMapData">
            <Download class="w-4 h-4 mr-1" />
            导出数据
          </el-button>
          <el-button @click="shareMap">
            <Share class="w-4 h-4 mr-1" />
            分享地图
          </el-button>
          <el-button @click="mapSetting">
            <Setting class="w-4 h-4 mr-1" />
            地图设置
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 车辆路线详情对话框 -->
    <el-dialog
      v-model="routeDetailsVisible"
      title="车辆路线详情"
      width="60%"
      :before-close="handleCloseRouteDetails"
    >
      <div v-if="selectedVehicleRoute" class="route-details">
        <!-- 路线基本信息 -->
        <div class="route-basic-info">
          <div class="info-grid">
            <div class="info-item">
              <label>车辆:</label>
              <span>{{ selectedVehicleRoute.plateNumber }}</span>
            </div>
            <div class="info-item">
              <label>起点:</label>
              <span>{{ selectedVehicleRoute.startPoint }}</span>
            </div>
            <div class="info-item">
              <label>终点:</label>
              <span>{{ selectedVehicleRoute.endPoint }}</span>
            </div>
            <div class="info-item">
              <label>总距离:</label>
              <span>{{ selectedVehicleRoute.totalDistance }} km</span>
            </div>
            <div class="info-item">
              <label>预计用时:</label>
              <span>{{ selectedVehicleRoute.estimatedTime }} 小时</span>
            </div>
            <div class="info-item">
              <label>已行驶:</label>
              <span>{{ selectedVehicleRoute.completedDistance }} km</span>
            </div>
          </div>
        </div>

        <!-- 途经点列表 -->
        <div class="waypoints-section">
          <h4 class="section-title">配送途经点</h4>
          <div class="waypoints-list">
            <div
              v-for="(waypoint, index) in selectedVehicleRoute.waypoints"
              :key="index"
              class="waypoint-item"
              :class="{ completed: waypoint.completed, current: waypoint.current }"
            >
              <div class="waypoint-index">{{ index + 1 }}</div>
              <div class="waypoint-content">
                <div class="waypoint-name">{{ waypoint.name }}</div>
                <div class="waypoint-address">{{ waypoint.address }}</div>
                <div class="waypoint-time">
                  {{ waypoint.completed ? '已完成' : waypoint.current ? '进行中' : waypoint.scheduledTime }}
                </div>
              </div>
              <div class="waypoint-status">
                <component
                  :is="waypoint.completed ? CircleCheck : waypoint.current ? Clock : Position"
                  :class="`w-5 h-5 ${waypoint.completed ? 'text-green-500' : waypoint.current ? 'text-blue-500' : 'text-gray-400'}`"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 路线统计 -->
        <div class="route-statistics">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon bg-blue-100 text-blue-600">
                <Route class="w-6 h-6" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ selectedVehicleRoute.progress }}%</div>
                <div class="stat-label">完成进度</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon bg-green-100 text-green-600">
                <CircleCheck class="w-6 h-6" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ selectedVehicleRoute.completedStops }}</div>
                <div class="stat-label">已完成停靠点</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon bg-orange-100 text-orange-600">
                <Clock class="w-6 h-6" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ selectedVehicleRoute.remainingTime }}h</div>
                <div class="stat-label">剩余时间</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="routeDetailsVisible = false">关闭</el-button>
          <el-button @click="optimizeRoute" type="primary">优化路线</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
// 图标已全局注册，无需单独导入

// 响应式数据
const selectedVehicleType = ref('all')
const selectedStatus = ref('all')
const selectedRegion = ref('all')
const mapViewMode = ref('roadmap')
const selectedVehicle = ref(null)
const routeDetailsVisible = ref(false)
const selectedVehicleRoute = ref(null)
const lastUpdateTime = ref('')
const mapCanvas = ref(null)

// 地图统计数据
const mapStats = reactive({
  activeVehicles: 48,
  totalRoutes: 23,
  completedDeliveries: 156,
  avgSpeed: 45.2
})

// 车辆数据
const vehicles = ref([
  {
    id: 1,
    plateNumber: '京A12345',
    driverName: '张师傅',
    status: 'delivering',
    currentLocation: '北京市朝阳区建国门',
    speed: 35,
    loadCapacity: 75,
    estimatedArrival: '15:30',
    type: 'truck'
  },
  {
    id: 2,
    plateNumber: '沪B67890',
    driverName: '李师傅',
    status: 'loading',
    currentLocation: '上海市浦东新区陆家嘴',
    speed: 0,
    loadCapacity: 20,
    estimatedArrival: '16:45',
    type: 'express'
  },
  {
    id: 3,
    plateNumber: '粤C54321',
    driverName: '王师傅',
    status: 'returning',
    currentLocation: '广州市天河区珠江新城',
    speed: 28,
    loadCapacity: 10,
    estimatedArrival: '17:20',
    type: 'special'
  },
  {
    id: 4,
    plateNumber: '川D98765',
    driverName: '刘师傅',
    status: 'completed',
    currentLocation: '成都市锦江区春熙路',
    speed: 0,
    loadCapacity: 0,
    estimatedArrival: '已完成',
    type: 'truck'
  }
])

// 今日任务数据
const todayTasks = ref([
  {
    id: 1,
    taskNumber: 'T2025072301',
    customerName: '东方航空',
    deliveryAddress: '上海浦东机场T1航站楼',
    scheduledTime: '14:30',
    status: 'in-progress',
    progress: 65
  },
  {
    id: 2,
    taskNumber: 'T2025072302',
    customerName: '南方航空',
    deliveryAddress: '广州白云机场T2航站楼',
    scheduledTime: '16:00',
    status: 'pending',
    progress: 0
  },
  {
    id: 3,
    taskNumber: 'T2025072303',
    customerName: '国航',
    deliveryAddress: '北京首都机场T3航站楼',
    scheduledTime: '10:45',
    status: 'completed',
    progress: 100
  },
  {
    id: 4,
    taskNumber: 'T2025072304',
    customerName: '厦门航空',
    deliveryAddress: '厦门高崎机场',
    scheduledTime: '18:30',
    status: 'delayed',
    progress: 30
  }
])

// 异常告警数据
const alerts = ref([
  {
    id: 1,
    type: 'traffic',
    level: 'warning',
    title: '交通拥堵告警',
    description: '京A12345 所在路段出现严重拥堵，预计延误30分钟',
    timestamp: '14:25'
  },
  {
    id: 2,
    type: 'vehicle',
    level: 'error',
    title: '车辆异常',
    description: '沪B67890 车辆GPS信号丢失，无法获取实时位置',
    timestamp: '13:50'
  },
  {
    id: 3,
    type: 'delivery',
    level: 'info',
    title: '配送提醒',
    description: '粤C54321 即将到达目的地，请准备卸货',
    timestamp: '15:10'
  }
])

// 计算属性
const visibleVehicles = computed(() => {
  let filtered = vehicles.value
  
  if (selectedVehicleType.value !== 'all') {
    filtered = filtered.filter(v => v.type === selectedVehicleType.value)
  }
  
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(v => v.status === selectedStatus.value)
  }
  
  return filtered
})

// 方法定义
const handleRefreshMap = () => {
  ElMessage.success('地图数据已刷新')
  updateLastUpdateTime()
}

const handleFullScreen = () => {
  ElMessage.info('切换全屏模式')
}

const handleEmergencyMode = () => {
  ElMessage.warning('启动紧急模式')
}

const applyFilters = () => {
  ElMessage.success('筛选条件已应用')
}

const zoomIn = () => {
  ElMessage.info('地图放大')
}

const zoomOut = () => {
  ElMessage.info('地图缩小')
}

const selectVehicle = (vehicle) => {
  selectedVehicle.value = vehicle
  ElMessage.success(`已选择车辆: ${vehicle.plateNumber}`)
}

const contactDriver = () => {
  if (selectedVehicle.value) {
    ElMessage.success(`正在联系司机: ${selectedVehicle.value.driverName}`)
  }
}

const viewVehicleRoute = () => {
  if (selectedVehicle.value) {
    // 模拟路线数据
    selectedVehicleRoute.value = {
      plateNumber: selectedVehicle.value.plateNumber,
      startPoint: '北京物流中心',
      endPoint: '上海配送中心',
      totalDistance: 1218,
      estimatedTime: 15.5,
      completedDistance: 789,
      progress: 65,
      completedStops: 3,
      remainingTime: 5.4,
      waypoints: [
        {
          name: '北京物流中心',
          address: '北京市大兴区亦庄经济开发区',
          scheduledTime: '08:00',
          completed: true,
          current: false
        },
        {
          name: '天津中转站',
          address: '天津市滨海新区保税区',
          scheduledTime: '11:30',
          completed: true,
          current: false
        },
        {
          name: '济南配送点',
          address: '济南市历下区经十路',
          scheduledTime: '15:20',
          completed: false,
          current: true
        },
        {
          name: '南京中转站',
          address: '南京市江宁区开发区',
          scheduledTime: '19:00',
          completed: false,
          current: false
        },
        {
          name: '上海配送中心',
          address: '上海市浦东新区张江高科',
          scheduledTime: '23:30',
          completed: false,
          current: false
        }
      ]
    }
    routeDetailsVisible.value = true
  }
}

const trackVehicle = () => {
  if (selectedVehicle.value) {
    ElMessage.success(`开始实时跟踪: ${selectedVehicle.value.plateNumber}`)
  }
}

const handleCloseRouteDetails = () => {
  routeDetailsVisible.value = false
  selectedVehicleRoute.value = null
}

const optimizeRoute = () => {
  ElMessage.success('路线优化完成')
}

const showAllVehicles = () => {
  ElMessage.info('显示所有车辆')
}

const hideAllVehicles = () => {
  ElMessage.info('隐藏所有车辆')
}

const centerMap = () => {
  ElMessage.info('地图已居中')
}

const exportMapData = () => {
  ElMessage.success('地图数据已导出')
}

const shareMap = () => {
  ElMessage.info('生成分享链接')
}

const mapSetting = () => {
  ElMessage.info('打开地图设置')
}

const handleAlert = (alert) => {
  ElMessage.success(`处理告警: ${alert.title}`)
}

const updateLastUpdateTime = () => {
  const now = new Date()
  lastUpdateTime.value = now.toLocaleTimeString()
}

// 辅助方法
const getVehicleStatusColor = (status) => {
  const colors = {
    'delivering': 'text-blue-500',
    'loading': 'text-orange-500',
    'returning': 'text-green-500',
    'completed': 'text-gray-500'
  }
  return colors[status] || 'text-gray-500'
}

const getVehicleStatusText = (status) => {
  const texts = {
    'delivering': '配送中',
    'loading': '装货中',
    'returning': '返回中',
    'completed': '已完成'
  }
  return texts[status] || status
}

const getVehicleTagType = (status) => {
  const types = {
    'delivering': 'primary',
    'loading': 'warning',
    'returning': 'success',
    'completed': 'info'
  }
  return types[status] || 'info'
}

const getTaskStatusClass = (status) => {
  return `task-${status}`
}

const getTaskTagType = (status) => {
  const types = {
    'pending': 'info',
    'in-progress': 'primary',
    'completed': 'success',
    'delayed': 'danger'
  }
  return types[status] || 'info'
}

const getTaskStatusText = (status) => {
  const texts = {
    'pending': '待配送',
    'in-progress': '配送中',
    'completed': '已完成',
    'delayed': '已延误'
  }
  return texts[status] || status
}

const getTaskProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#f56c6c'
}

const getAlertLevelClass = (level) => {
  return `alert-${level}`
}

const getAlertIcon = (type) => {
  const icons = {
    'traffic': Warning,
    'vehicle': Van,
    'delivery': CircleCheck
  }
  return icons[type] || InfoFilled
}

// 定时器
let updateTimer = null

// 组件挂载
onMounted(() => {
  updateLastUpdateTime()
  
  // 每30秒更新一次时间
  updateTimer = setInterval(() => {
    updateLastUpdateTime()
  }, 30000)
})

// 组件卸载
onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})
</script>

<style scoped>
.delivery-map {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.map-control-panel {
  background: white;
  border-radius: 12px;
  padding: 16px 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.zoom-controls {
  margin-left: 12px;
}

.map-container {
  display: flex;
  gap: 24px;
  height: 600px;
}

.map-wrapper {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.map-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.placeholder-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;
}

.map-simulation {
  margin-top: 32px;
}

.vehicle-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.vehicle-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
}

.vehicle-item:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.vehicle-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.vehicle-icon {
  flex-shrink: 0;
}

.vehicle-info {
  flex: 1;
}

.vehicle-id {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.vehicle-status {
  font-size: 13px;
  color: #64748b;
}

.vehicle-location {
  font-size: 13px;
  color: #64748b;
  text-align: right;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.legend-title {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #64748b;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.info-panel {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
}

.panel-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.vehicle-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  font-size: 13px;
  color: #1a202c;
}

.vehicle-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.task-list {
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.task-item:last-child {
  border-bottom: none;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-id {
  font-size: 13px;
  font-weight: 600;
  color: #1a202c;
}

.task-details {
  margin-bottom: 8px;
}

.task-customer {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 2px;
}

.task-address {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
}

.task-time {
  font-size: 12px;
  color: #64748b;
}

.task-progress {
  margin-top: 8px;
}

.alert-section {
  border-left: 4px solid #ef4444;
}

.alert-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.alert-description {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #94a3b8;
}

.alert-actions {
  flex-shrink: 0;
}

.alert-warning .alert-icon {
  color: #f59e0b;
}

.alert-error .alert-icon {
  color: #ef4444;
}

.alert-info .alert-icon {
  color: #3b82f6;
}

.map-toolbar {
  background: white;
  border-radius: 12px;
  padding: 16px 24px;
  margin-top: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.update-time {
  font-size: 12px;
  color: #94a3b8;
  margin-left: 4px;
}

.route-details {
  max-height: 600px;
  overflow-y: auto;
}

.route-basic-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.info-item span {
  color: #1a202c;
}

.waypoints-section {
  margin-bottom: 24px;
}

.waypoints-list {
  margin-top: 16px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid #e2e8f0;
}

.waypoint-item.completed {
  background: #f0fdf4;
  border-color: #10b981;
}

.waypoint-item.current {
  background: #eff6ff;
  border-color: #3b82f6;
}

.waypoint-index {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #64748b;
}

.waypoint-item.completed .waypoint-index {
  background: #10b981;
  color: white;
}

.waypoint-item.current .waypoint-index {
  background: #3b82f6;
  color: white;
}

.waypoint-content {
  flex: 1;
}

.waypoint-name {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.waypoint-address {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 4px;
}

.waypoint-time {
  font-size: 12px;
  color: #94a3b8;
}

.waypoint-status {
  flex-shrink: 0;
}

.route-statistics {
  background: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .map-container {
    flex-direction: column;
    height: auto;
  }
  
  .map-wrapper {
    height: 400px;
  }
  
  .info-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .delivery-map {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .map-control-panel {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .map-filters {
    flex-wrap: wrap;
  }
  
  .map-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .vehicle-list {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>