<!--
物流伙伴管理和评估系统
版本: 1.0
创建时间: 2025-07-23

物流专员专用的合作伙伴管理和绩效评估界面
特点：
1. 物流供应商信息管理和维护
2. 合作伙伴绩效评估和排名
3. 服务质量监控和分析
4. 合同管理和费用结算
5. 供应商准入和退出管理
-->

<template>
  <div class="partner-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">物流伙伴管理</h1>
          <p class="page-subtitle">管理物流合作伙伴，评估服务质量，优化供应商网络</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshPartners">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleAddPartner">
            <User class="w-4 h-4 mr-2" />
            新增伙伴
          </el-button>
          <el-button @click="handleExportPartners" type="primary">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 合作伙伴概览 -->
    <div class="partners-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card total-partners">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ partnerStats.totalPartners }}</div>
            <div class="card-label">合作伙伴总数</div>
            <div class="card-detail">活跃: {{ partnerStats.activePartners }}</div>
          </div>
        </div>
        
        <div class="overview-card service-score">
          <div class="card-icon bg-green-100 text-green-600">
            <Star class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ partnerStats.avgServiceScore }}</div>
            <div class="card-label">平均服务评分</div>
            <div class="card-trend positive">+{{ partnerStats.scoreImprovement }} 本月</div>
          </div>
        </div>
        
        <div class="overview-card on-time-delivery">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ partnerStats.onTimeDelivery }}%</div>
            <div class="card-label">准时交付率</div>
            <div class="card-trend positive">+{{ partnerStats.deliveryImprovement }}% 本月</div>
          </div>
        </div>
        
        <div class="overview-card cost-efficiency">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendCharts class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ partnerStats.avgCostPerKm }}</div>
            <div class="card-label">平均每公里成本</div>
            <div class="card-trend negative">-{{ partnerStats.costReduction }}% 优化</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activePartnerTab" @tab-change="handleTabChange">
        <!-- 伙伴列表 -->
        <el-tab-pane label="伙伴列表" name="partners">
          <div class="partners-panel">
            <!-- 搜索和筛选 -->
            <div class="search-filter-bar">
              <div class="search-section">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索伙伴名称、联系人或区域..."
                  @keyup.enter="handleSearch"
                  size="default"
                  class="search-input"
                >
                  <template #prefix>
                    <Search class="w-5 h-5 text-gray-400" />
                  </template>
                </el-input>
              </div>
              
              <div class="filter-section">
                <el-select v-model="statusFilter" placeholder="状态筛选" size="default">
                  <el-option label="全部状态" value="all" />
                  <el-option label="活跃" value="active" />
                  <el-option label="暂停" value="suspended" />
                  <el-option label="审核中" value="pending" />
                  <el-option label="已停用" value="inactive" />
                </el-select>
                
                <el-select v-model="regionFilter" placeholder="服务区域" size="default">
                  <el-option label="全部区域" value="all" />
                  <el-option label="华东区域" value="east" />
                  <el-option label="华南区域" value="south" />
                  <el-option label="华北区域" value="north" />
                  <el-option label="西南区域" value="southwest" />
                </el-select>
                
                <el-select v-model="ratingFilter" placeholder="评分筛选" size="default">
                  <el-option label="全部评分" value="all" />
                  <el-option label="5星" value="5" />
                  <el-option label="4星及以上" value="4" />
                  <el-option label="3星及以上" value="3" />
                  <el-option label="3星以下" value="below3" />
                </el-select>
                
                <el-button @click="applyFilters" type="primary">
                  <SemiSelect class="w-4 h-4 mr-2" />
                  应用筛选
                </el-button>
              </div>
            </div>

            <!-- 伙伴列表 -->
            <div class="partners-list">
              <div class="list-header">
                <h3 class="text-lg font-semibold text-gray-800">物流合作伙伴</h3>
                <div class="list-actions">
                  <el-button @click="handleBatchAction" size="small">
                    批量操作
                  </el-button>
                  <el-dropdown @command="handleBulkAction">
                    <el-button size="small">
                      更多操作
                      <ArrowDown class="w-3 h-3 ml-1" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="export">导出选中</el-dropdown-item>
                        <el-dropdown-item command="evaluate">批量评估</el-dropdown-item>
                        <el-dropdown-item command="notify">发送通知</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>

              <div class="partners-table">
                <el-table
                  :data="filteredPartners"
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" label="伙伴名称" width="180">
                    <template #default="{ row }">
                      <div class="partner-info">
                        <div class="partner-name">{{ row.name }}</div>
                        <div class="partner-code">{{ row.code }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="contact" label="联系信息" width="160">
                    <template #default="{ row }">
                      <div class="contact-info">
                        <div class="contact-name">{{ row.contactPerson }}</div>
                        <div class="contact-phone">{{ row.contactPhone }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="serviceArea" label="服务区域" width="120">
                    <template #default="{ row }">
                      <el-tag size="small">{{ row.serviceArea }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="rating" label="服务评分" width="120">
                    <template #default="{ row }">
                      <div class="rating-display">
                        <el-rate
                          v-model="row.rating"
                          disabled
                          size="small"
                          :colors="['#ff9900', '#ff9900', '#ff9900']"
                        />
                        <span class="rating-score">({{ row.rating }})</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getStatusType(row.status)">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="monthlyVolume" label="月配送量" width="120">
                    <template #default="{ row }">
                      {{ row.monthlyVolume }} 单
                    </template>
                  </el-table-column>
                  <el-table-column prop="onTimeRate" label="准时率" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'text-green-600': row.onTimeRate >= 95, 'text-orange-600': row.onTimeRate < 90, 'text-red-600': row.onTimeRate < 80 }">
                        {{ row.onTimeRate }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastEvaluated" label="最近评估" width="120" />
                  <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="viewPartnerDetails(row)" size="small">
                        详情
                      </el-button>
                      <el-button @click="evaluatePartner(row)" size="small" type="success">
                        评估
                      </el-button>
                      <el-dropdown @command="(command) => handlePartnerAction(row, command)">
                        <el-button size="small">
                          更多
                          <ArrowDown class="w-3 h-3 ml-1" />
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                            <el-dropdown-item command="contract">合同管理</el-dropdown-item>
                            <el-dropdown-item command="history">合作历史</el-dropdown-item>
                            <el-dropdown-item command="suspend" :disabled="row.status === 'suspended'">
                              暂停合作
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalPartners"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 绩效评估 -->
        <el-tab-pane label="绩效评估" name="evaluation">
          <div class="evaluation-panel">
            <!-- 综合排名 -->
            <div class="ranking-section">
              <div class="ranking-header">
                <h3 class="text-lg font-semibold text-gray-800">合作伙伴综合排名</h3>
                <div class="ranking-controls">
                  <el-select v-model="rankingPeriod" size="small">
                    <el-option label="本月" value="month" />
                    <el-option label="本季度" value="quarter" />
                    <el-option label="本年度" value="year" />
                  </el-select>
                  <el-select v-model="rankingMetric" size="small">
                    <el-option label="综合评分" value="overall" />
                    <el-option label="服务质量" value="quality" />
                    <el-option label="成本效益" value="cost" />
                    <el-option label="准时交付" value="timeliness" />
                  </el-select>
                </div>
              </div>
              
              <div class="ranking-list">
                <div
                  v-for="(partner, index) in rankedPartners"
                  :key="partner.id"
                  class="ranking-item"
                  :class="getRankingClass(index + 1)"
                >
                  <div class="rank-position">
                    <div class="rank-number" :class="getRankBadgeClass(index + 1)">
                      {{ index + 1 }}
                    </div>
                  </div>
                  
                  <div class="partner-basic-info">
                    <div class="partner-name">{{ partner.name }}</div>
                    <div class="partner-area">{{ partner.serviceArea }}</div>
                  </div>
                  
                  <div class="performance-metrics">
                    <div class="metric-item">
                      <span class="metric-label">综合评分</span>
                      <span class="metric-value">{{ partner.overallScore }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">服务质量</span>
                      <span class="metric-value">{{ partner.qualityScore }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">成本效益</span>
                      <span class="metric-value">{{ partner.costScore }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">准时率</span>
                      <span class="metric-value">{{ partner.timelinessScore }}%</span>
                    </div>
                  </div>
                  
                  <div class="ranking-trend">
                    <div class="trend-indicator" :class="{ 'trend-up': partner.rankChange > 0, 'trend-down': partner.rankChange < 0, 'trend-same': partner.rankChange === 0 }">
                      <component :is="getTrendIcon(partner.rankChange)" class="w-4 h-4" />
                      <span class="trend-text">
                        {{ partner.rankChange === 0 ? '持平' : (partner.rankChange > 0 ? `上升${partner.rankChange}位` : `下降${Math.abs(partner.rankChange)}位`) }}
                      </span>
                    </div>
                  </div>
                  
                  <div class="ranking-actions">
                    <el-button @click="viewEvaluationDetails(partner)" size="small">
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 评估指标分析 -->
            <div class="metrics-analysis">
              <h4 class="text-md font-medium text-gray-700 mb-4">关键指标分析</h4>
              <div class="metrics-charts">
                <div class="chart-container">
                  <!-- 图表占位 -->
                  <div class="chart-placeholder">
                    <Histogram class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">绩效对比图表</p>
                    <p class="text-sm text-gray-400">可视化展示各合作伙伴关键指标表现</p>
                    
                    <!-- 模拟数据展示 -->
                    <div class="metrics-summary">
                      <div class="summary-stats">
                        <div class="stat-group">
                          <div class="stat-title">平均指标表现</div>
                          <div class="stat-items">
                            <div class="stat-item">
                              <span class="stat-name">服务质量:</span>
                              <span class="stat-value">4.2/5.0</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-name">成本效益:</span>
                              <span class="stat-value">3.8/5.0</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-name">准时交付:</span>
                              <span class="stat-value">92.5%</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-name">客户满意:</span>
                              <span class="stat-value">4.1/5.0</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 合同管理 -->
        <el-tab-pane label="合同管理" name="contracts">
          <div class="contracts-panel">
            <!-- 合同概览 -->
            <div class="contracts-overview">
              <div class="overview-stats">
                <div class="stat-card">
                  <div class="stat-icon bg-green-100 text-green-600">
                    <Document class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ contractStats.activeContracts }}</div>
                    <div class="stat-label">有效合同</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon bg-orange-100 text-orange-600">
                    <Warning class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ contractStats.expiringContracts }}</div>
                    <div class="stat-label">即将到期</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon bg-blue-100 text-blue-600">
                    <Coin class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">¥{{ contractStats.totalValue }}万</div>
                    <div class="stat-label">合同总价值</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon bg-purple-100 text-purple-600">
                    <Calendar class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ contractStats.avgDuration }}年</div>
                    <div class="stat-label">平均合同期限</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 合同列表 -->
            <div class="contracts-list">
              <div class="list-header">
                <h4 class="text-md font-medium text-gray-700">合作合同管理</h4>
                <el-button @click="handleCreateContract" type="primary" size="small">
                  <Plus class="w-4 h-4 mr-1" />
                  新建合同
                </el-button>
              </div>
              
              <div class="contracts-table">
                <el-table :data="contracts" style="width: 100%">
                  <el-table-column prop="contractNumber" label="合同编号" width="140" />
                  <el-table-column prop="partnerName" label="合作伙伴" width="160" />
                  <el-table-column prop="contractType" label="合同类型" width="120">
                    <template #default="{ row }">
                      <el-tag size="small">{{ row.contractType }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="startDate" label="开始日期" width="120" />
                  <el-table-column prop="endDate" label="结束日期" width="120" />
                  <el-table-column prop="contractValue" label="合同金额" width="120">
                    <template #default="{ row }">
                      ¥{{ row.contractValue }}万
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getContractStatusType(row.status)">
                        {{ getContractStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="remainingDays" label="剩余天数" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'text-red-600': row.remainingDays <= 30, 'text-orange-600': row.remainingDays <= 90 }">
                        {{ row.remainingDays }}天
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="viewContract(row)" size="small">
                        查看
                      </el-button>
                      <el-button @click="editContract(row)" size="small" type="primary">
                        编辑
                      </el-button>
                      <el-dropdown @command="(command) => handleContractAction(row, command)">
                        <el-button size="small">
                          更多
                          <ArrowDown class="w-3 h-3 ml-1" />
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="renew">续签</el-dropdown-item>
                            <el-dropdown-item command="terminate">终止</el-dropdown-item>
                            <el-dropdown-item command="download">下载</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 质量监控 -->
        <el-tab-pane label="质量监控" name="monitoring">
          <div class="monitoring-panel">
            <!-- 实时监控指标 -->
            <div class="realtime-metrics">
              <h4 class="text-md font-medium text-gray-700 mb-4">实时服务质量监控</h4>
              <div class="metrics-grid">
                <div class="metric-card delivery-success">
                  <div class="metric-header">
                    <CircleCheck class="w-5 h-5 text-green-500" />
                    <span class="metric-title">配送成功率</span>
                  </div>
                  <div class="metric-value">97.8%</div>
                  <div class="metric-trend positive">+2.1% 本周</div>
                </div>
                
                <div class="metric-card response-time">
                  <div class="metric-header">
                    <Clock class="w-5 h-5 text-blue-500" />
                    <span class="metric-title">平均响应时间</span>
                  </div>
                  <div class="metric-value">2.3小时</div>
                  <div class="metric-trend negative">-0.5h 改善</div>
                </div>
                
                <div class="metric-card customer-satisfaction">
                  <div class="metric-header">
                    <Star class="w-5 h-5 text-yellow-500" />
                    <span class="metric-title">客户满意度</span>
                  </div>
                  <div class="metric-value">4.2/5.0</div>
                  <div class="metric-trend positive">+0.3 提升</div>
                </div>
                
                <div class="metric-card complaint-rate">
                  <div class="metric-header">
                    <Warning class="w-5 h-5 text-red-500" />
                    <span class="metric-title">投诉率</span>
                  </div>
                  <div class="metric-value">1.2%</div>
                  <div class="metric-trend negative">-0.8% 降低</div>
                </div>
              </div>
            </div>

            <!-- 质量问题跟踪 -->
            <div class="quality-issues">
              <div class="issues-header">
                <h4 class="text-md font-medium text-gray-700">质量问题跟踪</h4>
                <el-button @click="handleReportIssue" size="small" type="warning">
                  <Warning class="w-4 h-4 mr-1" />
                  上报问题
                </el-button>
              </div>
              
              <div class="issues-list">
                <div
                  v-for="issue in qualityIssues"
                  :key="issue.id"
                  class="issue-item"
                  :class="getIssueSeverityClass(issue.severity)"
                >
                  <div class="issue-header">
                    <div class="issue-id">{{ issue.id }}</div>
                    <div class="issue-title">{{ issue.title }}</div>
                    <el-tag :type="getIssueSeverityTag(issue.severity)" size="small">
                      {{ getIssueSeverityText(issue.severity) }}
                    </el-tag>
                  </div>
                  <div class="issue-content">
                    <div class="issue-partner">合作伙伴: {{ issue.partnerName }}</div>
                    <div class="issue-description">{{ issue.description }}</div>
                    <div class="issue-meta">
                      <span class="issue-reporter">上报人: {{ issue.reporter }}</span>
                      <span class="issue-time">{{ issue.reportTime }}</span>
                      <span class="issue-status">状态: {{ getIssueStatusText(issue.status) }}</span>
                    </div>
                  </div>
                  <div class="issue-actions">
                    <el-button @click="handleIssue(issue)" size="small" type="primary">
                      处理
                    </el-button>
                    <el-button @click="viewIssueDetails(issue)" size="small">
                      详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 伙伴详情对话框 -->
    <el-dialog
      v-model="partnerDetailsVisible"
      title="合作伙伴详情"
      width="70%"
      :before-close="handleClosePartnerDetails"
    >
      <div v-if="selectedPartner" class="partner-details">
        <!-- 基本信息 -->
        <div class="basic-info-section">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>伙伴名称:</label>
              <span>{{ selectedPartner.name }}</span>
            </div>
            <div class="info-item">
              <label>伙伴代码:</label>
              <span>{{ selectedPartner.code }}</span>
            </div>
            <div class="info-item">
              <label>联系人:</label>
              <span>{{ selectedPartner.contactPerson }}</span>
            </div>
            <div class="info-item">
              <label>联系电话:</label>
              <span>{{ selectedPartner.contactPhone }}</span>
            </div>
            <div class="info-item">
              <label>服务区域:</label>
              <span>{{ selectedPartner.serviceArea }}</span>
            </div>
            <div class="info-item">
              <label>合作状态:</label>
              <el-tag :type="getStatusType(selectedPartner.status)">
                {{ getStatusText(selectedPartner.status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 绩效数据 -->
        <div class="performance-section">
          <h4 class="section-title">绩效表现</h4>
          <div class="performance-stats">
            <div class="stat-row">
              <div class="stat-item">
                <label>服务评分:</label>
                <div class="rating-display">
                  <el-rate
                    v-model="selectedPartner.rating"
                    disabled
                    size="small"
                  />
                  <span>({{ selectedPartner.rating }})</span>
                </div>
              </div>
              <div class="stat-item">
                <label>月配送量:</label>
                <span>{{ selectedPartner.monthlyVolume }} 单</span>
              </div>
              <div class="stat-item">
                <label>准时交付率:</label>
                <span :class="{ 'text-green-600': selectedPartner.onTimeRate >= 95 }">
                  {{ selectedPartner.onTimeRate }}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近合作记录 -->
        <div class="cooperation-history">
          <h4 class="section-title">最近合作记录</h4>
          <div class="history-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="record in selectedPartner.recentHistory"
                :key="record.id"
                :timestamp="record.date"
              >
                <div class="timeline-content">
                  <div class="record-title">{{ record.title }}</div>
                  <div class="record-description">{{ record.description }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="partnerDetailsVisible = false">关闭</el-button>
          <el-button @click="editPartnerInfoFilled" type="primary">编辑信息</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
// 所有图标已全局注册，无需导入

// 响应式数据
const activePartnerTab = ref('partners')
const searchQuery = ref('')
const statusFilter = ref('all')
const regionFilter = ref('all')
const ratingFilter = ref('all')
const rankingPeriod = ref('month')
const rankingMetric = ref('overall')
const currentPage = ref(1)
const pageSize = ref(20)
const totalPartners = ref(0)
const selectedPartners = ref([])
const partnerDetailsVisible = ref(false)
const selectedPartner = ref(null)

// 伙伴统计数据
const partnerStats = reactive({
  totalPartners: 48,
  activePartners: 42,
  avgServiceScore: 4.2,
  scoreImprovement: 0.3,
  onTimeDelivery: 94.2,
  deliveryImprovement: 2.1,
  avgCostPerKm: 5.8,
  costReduction: 8.5
})

// 合同统计数据
const contractStats = reactive({
  activeContracts: 38,
  expiringContracts: 6,
  totalValue: 2850,
  avgDuration: 2.5
})

// 伙伴列表数据
const partners = ref([
  {
    id: 1,
    name: '顺丰速运',
    code: 'SF001',
    contactPerson: '张经理',
    contactPhone: '138-0000-1234',
    serviceArea: '华东区域',
    rating: 4.8,
    status: 'active',
    monthlyVolume: 2850,
    onTimeRate: 97.5,
    lastEvaluated: '2025-07-20',
    overallScore: 4.8,
    qualityScore: 4.9,
    costScore: 4.5,
    timelinessScore: 97.5,
    rankChange: 1,
    recentHistory: [
      {
        id: 1,
        date: '2025-07-22',
        title: '完成大批量紧急配送',
        description: '成功在24小时内完成500单紧急配送任务'
      },
      {
        id: 2,
        date: '2025-07-15',
        title: '服务质量评估',
        description: '月度服务质量评估获得4.8分，表现优秀'
      }
    ]
  },
  {
    id: 2,
    name: '德邦物流',
    code: 'DB002',
    contactPerson: '李总监',
    contactPhone: '139-0000-5678',
    serviceArea: '华南区域',
    rating: 4.5,
    status: 'active',
    monthlyVolume: 1980,
    onTimeRate: 94.2,
    lastEvaluated: '2025-07-18',
    overallScore: 4.5,
    qualityScore: 4.6,
    costScore: 4.8,
    timelinessScore: 94.2,
    rankChange: 0,
    recentHistory: [
      {
        id: 1,
        date: '2025-07-21',
        title: '区域扩展完成',
        description: '成功将服务范围扩展至3个新城市'
      }
    ]
  },
  {
    id: 3,
    name: '京东物流',
    code: 'JD003',
    contactPerson: '王主管',
    contactPhone: '137-0000-9012',
    serviceArea: '华北区域',
    rating: 4.3,
    status: 'active',
    monthlyVolume: 2240,
    onTimeRate: 92.8,
    lastEvaluated: '2025-07-16',
    overallScore: 4.3,
    qualityScore: 4.4,
    costScore: 4.1,
    timelinessScore: 92.8,
    rankChange: -1,
    recentHistory: []
  },
  {
    id: 4,
    name: '圆通速递',
    code: 'YT004',
    contactPerson: '赵经理',
    contactPhone: '135-0000-3456',
    serviceArea: '西南区域',
    rating: 3.9,
    status: 'suspended',
    monthlyVolume: 1560,
    onTimeRate: 88.5,
    lastEvaluated: '2025-07-10',
    overallScore: 3.9,
    qualityScore: 4.0,
    costScore: 4.2,
    timelinessScore: 88.5,
    rankChange: -2,
    recentHistory: []
  }
])

// 合同列表数据
const contracts = ref([
  {
    id: 1,
    contractNumber: 'CT2025001',
    partnerName: '顺丰速运',
    contractType: '年度合作',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    contractValue: 850,
    status: 'active',
    remainingDays: 162
  },
  {
    id: 2,
    contractNumber: 'CT2025002',
    partnerName: '德邦物流',
    contractType: '季度合作',
    startDate: '2025-07-01',
    endDate: '2025-09-30',
    contractValue: 280,
    status: 'active',
    remainingDays: 68
  },
  {
    id: 3,
    contractNumber: 'CT2025003',
    partnerName: '京东物流',
    contractType: '年度合作',
    startDate: '2024-10-01',
    endDate: '2025-09-30',
    contractValue: 960,
    status: 'expiring',
    remainingDays: 68
  },
  {
    id: 4,
    contractNumber: 'CT2024005',
    partnerName: '圆通速递',
    contractType: '半年合作',
    startDate: '2025-02-01',
    endDate: '2025-07-31',
    contractValue: 340,
    status: 'expired',
    remainingDays: 0
  }
])

// 质量问题数据
const qualityIssues = ref([
  {
    id: 'QI001',
    title: '配送延误问题',
    partnerName: '圆通速递',
    severity: 'high',
    description: '连续3天出现配送延误,影响客户满意度',
    reporter: '张专员',
    reportTime: '2025-07-22 14:30',
    status: 'processing'
  },
  {
    id: 'QI002',
    title: '包装破损率偏高',
    partnerName: '中通快运',
    severity: 'medium',
    description: '本月包装破损率达到2.5%,超出标准要求',
    reporter: '李专员',
    reportTime: '2025-07-21 09:15',
    status: 'pending'
  },
  {
    id: 'QI003',
    title: '服务态度投诉',
    partnerName: '韵达快递',
    severity: 'low',
    description: '客户反馈配送员服务态度不佳',
    reporter: '王专员',
    reportTime: '2025-07-20 16:45',
    status: 'resolved'
  }
])

// 计算属性
const filteredPartners = computed(() => {
  let filtered = partners.value
  
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(p => p.status === statusFilter.value)
  }
  
  if (regionFilter.value !== 'all') {
    filtered = filtered.filter(p => p.serviceArea.includes(regionFilter.value))
  }
  
  if (ratingFilter.value !== 'all') {
    if (ratingFilter.value === 'below3') {
      filtered = filtered.filter(p => p.rating < 3)
    } else {
      const minRating = parseInt(ratingFilter.value)
      filtered = filtered.filter(p => p.rating >= minRating)
    }
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.name.toLowerCase().includes(query) ||
      p.contactPerson.toLowerCase().includes(query) ||
      p.serviceArea.toLowerCase().includes(query)
    )
  }
  
  totalPartners.value = filtered.length
  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const rankedPartners = computed(() => {
  return [...partners.value]
    .filter(p => p.status === 'active')
    .sort((a, b) => b.overallScore - a.overallScore)
})

// 方法定义
const handleTabChange = (tabName) => {
  activePartnerTab.value = tabName
}

const handleRefreshPartners = () => {
  ElMessage.success('合作伙伴数据已刷新')
}

const handleAddPartner = () => {
  ElMessage.info('新增合作伙伴')
}

const handleExportPartners = () => {
  ElMessage.success('合作伙伴报告导出中...')
}

const handleSearch = () => {
  currentPage.value = 1
  ElMessage.success(`搜索到 ${filteredPartners.value.length} 个合作伙伴`)
}

const applyFilters = () => {
  currentPage.value = 1
  ElMessage.success('筛选条件已应用')
}

const handleSelectionChange = (selection) => {
  selectedPartners.value = selection
}

const handleBatchAction = () => {
  if (selectedPartners.value.length === 0) {
    ElMessage.warning('请先选择要操作的合作伙伴')
    return
  }
  ElMessage.success(`批量操作 ${selectedPartners.value.length} 个合作伙伴`)
}

const handleBulkAction = (command) => {
  if (selectedPartners.value.length === 0) {
    ElMessage.warning('请先选择要操作的合作伙伴')
    return
  }
  
  switch (command) {
    case 'export':
      ElMessage.success('导出选中合作伙伴信息')
      break
    case 'evaluate':
      ElMessage.success('批量评估选中合作伙伴')
      break
    case 'notify':
      ElMessage.success('向选中合作伙伴发送通知')
      break
  }
}

const viewPartnerDetails = (partner) => {
  selectedPartner.value = partner
  partnerDetailsVisible.value = true
}

const evaluatePartner = (partner) => {
  ElMessage.success(`开始评估: ${partner.name}`)
}

const handlePartnerAction = (partner, command) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑: ${partner.name}`)
      break
    case 'contract':
      ElMessage.info(`合同管理: ${partner.name}`)
      break
    case 'history':
      ElMessage.info(`查看合作历史: ${partner.name}`)
      break
    case 'suspend':
      ElMessage.warning(`暂停合作: ${partner.name}`)
      break
  }
}

const handleClosePartnerDetails = () => {
  partnerDetailsVisible.value = false
  selectedPartner.value = null
}

const editPartnerInfoFilled = () => {
  ElMessage.info('编辑合作伙伴信息')
}

const viewEvaluationDetails = (partner) => {
  ElMessage.success(`查看 ${partner.name} 详细评估报告`)
}

const handleCreateContract = () => {
  ElMessage.info('创建新合同')
}

const viewContract = (contract) => {
  ElMessage.info(`查看合同: ${contract.contractNumber}`)
}

const editContract = (contract) => {
  ElMessage.info(`编辑合同: ${contract.contractNumber}`)
}

const handleContractAction = (contract, command) => {
  switch (command) {
    case 'renew':
      ElMessage.success(`续签合同: ${contract.contractNumber}`)
      break
    case 'terminate':
      ElMessage.warning(`终止合同: ${contract.contractNumber}`)
      break
    case 'download':
      ElMessage.success(`下载合同: ${contract.contractNumber}`)
      break
  }
}

const handleReportIssue = () => {
  ElMessage.info('上报质量问题')
}

const handleIssue = (issue) => {
  ElMessage.success(`处理问题: ${issue.id}`)
}

const viewIssueDetails = (issue) => {
  ElMessage.info(`查看问题详情: ${issue.id}`)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 辅助方法
const getStatusType = (status) => {
  const types = {
    'active': 'success',
    'suspended': 'warning',
    'pending': 'info',
    'inactive': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'active': '活跃',
    'suspended': '暂停',
    'pending': '审核中',
    'inactive': '已停用'
  }
  return texts[status] || status
}

const getRankingClass = (rank) => {
  if (rank <= 3) return `ranking-top-${rank}`
  return 'ranking-normal'
}

const getRankBadgeClass = (rank) => {
  const classes = {
    1: 'rank-gold',
    2: 'rank-silver',
    3: 'rank-bronze'
  }
  return classes[rank] || 'rank-normal'
}

const getTrendIcon = (change) => {
  if (change > 0) return TrendingUp
  if (change < 0) return TrendingDown
  return Minus
}

const getContractStatusType = (status) => {
  const types = {
    'active': 'success',
    'expiring': 'warning',
    'expired': 'danger',
    'terminated': 'info'
  }
  return types[status] || 'info'
}

const getContractStatusText = (status) => {
  const texts = {
    'active': '有效',
    'expiring': '即将到期',
    'expired': '已过期',
    'terminated': '已终止'
  }
  return texts[status] || status
}

const getIssueSeverityClass = (severity) => {
  return `issue-${severity}`
}

const getIssueSeverityTag = (severity) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[severity] || 'info'
}

const getIssueSeverityText = (severity) => {
  const texts = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return texts[severity] || severity
}

const getIssueStatusText = (status) => {
  const texts = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决'
  }
  return texts[status] || status
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  console.log('物流伙伴管理组件已加载')
})
</script>

<style scoped>
.partner-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.partners-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-detail {
  font-size: 13px;
  color: #94a3b8;
}

.card-trend {
  font-size: 13px;
  font-weight: 500;
}

.card-trend.positive {
  color: #10b981;
}

.card-trend.negative {
  color: #ef4444;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.partners-panel {
  padding: 24px;
}

.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
}

.filter-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.partners-list {
  background: white;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-actions {
  display: flex;
  gap: 8px;
}

.partners-table {
  margin-bottom: 24px;
}

.partner-info {
  text-align: left;
}

.partner-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 2px;
}

.partner-code {
  font-size: 12px;
  color: #64748b;
}

.contact-info {
  text-align: left;
}

.contact-name {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 2px;
}

.contact-phone {
  font-size: 12px;
  color: #64748b;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-score {
  font-size: 12px;
  color: #64748b;
}

.pagination-wrapper {
  background: #fafbfc;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
}

.evaluation-panel {
  padding: 24px;
}

.ranking-section {
  margin-bottom: 32px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.ranking-controls {
  display: flex;
  gap: 12px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ranking-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.2s;
}

.ranking-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ranking-item.ranking-top-1 {
  border-color: #ffd700;
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
}

.ranking-item.ranking-top-2 {
  border-color: #c0c0c0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.ranking-item.ranking-top-3 {
  border-color: #cd7f32;
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%);
}

.rank-position {
  flex-shrink: 0;
}

.rank-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  color: white;
}

.rank-number.rank-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  color: #8b5a00;
}

.rank-number.rank-silver {
  background: linear-gradient(135deg, #c0c0c0 0%, #e2e8f0 100%);
  color: #475569;
}

.rank-number.rank-bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #f59e0b 100%);
  color: #78350f;
}

.rank-number.rank-normal {
  background: #64748b;
}

.partner-basic-info {
  flex: 1;
}

.partner-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.partner-area {
  font-size: 14px;
  color: #64748b;
}

.performance-metrics {
  display: flex;
  gap: 24px;
}

.metric-item {
  text-align: center;
  min-width: 80px;
}

.metric-label {
  font-size: 12px;
  color: #64748b;
  display: block;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.ranking-trend {
  display: flex;
  align-items: center;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 16px;
}

.trend-indicator.trend-up {
  background: #dcfce7;
  color: #166534;
}

.trend-indicator.trend-down {
  background: #fee2e2;
  color: #dc2626;
}

.trend-indicator.trend-same {
  background: #f1f5f9;
  color: #64748b;
}

.ranking-actions {
  flex-shrink: 0;
}

.metrics-analysis {
  margin-top: 32px;
}

.metrics-charts {
  margin-top: 16px;
}

.chart-container {
  background: #f8fafc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.chart-placeholder {
  text-align: center;
}

.metrics-summary {
  margin-top: 32px;
}

.summary-stats {
  max-width: 600px;
  margin: 0 auto;
}

.stat-group {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
  text-align: center;
}

.stat-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-name {
  font-size: 14px;
  color: #64748b;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.contracts-panel {
  padding: 24px;
}

.contracts-overview {
  margin-bottom: 32px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 13px;
  color: #64748b;
}

.contracts-list {
  margin-top: 32px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.contracts-table {
  margin-top: 16px;
}

.monitoring-panel {
  padding: 24px;
}

.realtime-metrics {
  margin-bottom: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  text-align: center;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-title {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #10b981;
}

.quality-issues {
  margin-top: 32px;
}

.issues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.issue-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.issue-item.issue-high {
  border-left: 4px solid #ef4444;
}

.issue-item.issue-medium {
  border-left: 4px solid #f59e0b;
}

.issue-item.issue-low {
  border-left: 4px solid #3b82f6;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.issue-id {
  font-weight: 600;
  color: #1a202c;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.issue-title {
  flex: 1;
  font-weight: 500;
  color: #1a202c;
}

.issue-content {
  flex: 1;
}

.issue-partner {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 500;
  margin-bottom: 8px;
}

.issue-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 8px;
}

.issue-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #94a3b8;
}

.issue-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.partner-details {
  max-height: 600px;
  overflow-y: auto;
}

.basic-info-section,
.performance-section,
.cooperation-history {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.info-item span {
  color: #1a202c;
}

.performance-stats {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-row .stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.timeline-content {
  padding-left: 16px;
}

.record-title {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.record-description {
  color: #64748b;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .search-filter-bar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-section {
    width: 100%;
    max-width: none;
  }
  
  .filter-section {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .performance-metrics {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .partner-management {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .ranking-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .performance-metrics {
    justify-content: space-between;
    width: 100%;
  }
  
  .ranking-trend {
    width: 100%;
    justify-content: flex-start;
  }
  
  .ranking-actions {
    width: 100%;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .stat-items {
    grid-template-columns: 1fr;
  }
}
</style>