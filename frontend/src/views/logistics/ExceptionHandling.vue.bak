<!--
配送异常处理和应急响应系统
版本: 1.0
创建时间: 2025-07-23

物流专员专用的异常处理和应急响应管理界面
特点：
1. 实时异常监控和预警机制
2. 多级异常处理流程和升级
3. 应急响应预案和快速处置
4. 异常数据分析和预测
5. 客户通知和沟通管理
-->

<template>
  <div class="exception-handling">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">异常处理中心</h1>
          <p class="page-subtitle">快速响应配送异常，建立完善的应急处理机制，确保服务连续性</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshExceptions">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleCreateException">
            <Plus class="w-4 h-4 mr-2" />
            新建异常
          </el-button>
          <el-button @click="handleEmergencyProtocol" type="danger">
            <Warning class="w-4 h-4 mr-2" />
            启动应急
          </el-button>
        </div>
      </div>
    </div>

    <!-- 异常状态概览 -->
    <div class="exception-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card urgent-exceptions">
          <div class="card-icon bg-red-100 text-red-600">
            <Warning class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ exceptionStats.urgent }}</div>
            <div class="card-label">紧急异常</div>
            <div class="card-detail">需立即处理</div>
          </div>
          <div class="card-indicator urgent-pulse"></div>
        </div>
        
        <div class="overview-card processing-exceptions">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ exceptionStats.processing }}</div>
            <div class="card-label">处理中</div>
            <div class="card-detail">平均处理时间: {{ exceptionStats.avgProcessTime }}h</div>
          </div>
        </div>
        
        <div class="overview-card resolved-today">
          <div class="card-icon bg-green-100 text-green-600">
            <CircleCheck class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ exceptionStats.resolvedToday }}</div>
            <div class="card-label">今日已解决</div>
            <div class="card-trend positive">解决率: {{ exceptionStats.resolutionRate }}%</div>
          </div>
        </div>
        
        <div class="overview-card response-time">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Zap class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ exceptionStats.avgResponseTime }}分钟</div>
            <div class="card-label">平均响应时间</div>
            <div class="card-trend negative">-{{ exceptionStats.responseImprovement }}% 优化</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时警报区域 -->
    <div v-if="activeAlerts.length > 0" class="alert-banner">
      <div class="alert-content">
        <Warning class="w-5 h-5 text-red-500 flex-shrink-0" />
        <div class="alert-messages">
          <div 
            v-for="alert in activeAlerts" 
            :key="alert.id"
            class="alert-message"
          >
            <span class="alert-time">{{ alert.time }}</span>
            <span class="alert-text">{{ alert.message }}</span>
            <el-button @click="handleAlert(alert)" size="small" type="danger">
              立即处理
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeExceptionTab" @tab-change="handleTabChange">
        <!-- 异常监控 -->
        <el-tab-pane label="异常监控" name="monitoring">
          <div class="monitoring-panel">
            <!-- 异常筛选器 -->
            <div class="exception-filters">
              <div class="filter-bar">
                <el-select v-model="severityFilter" placeholder="严重程度" size="default">
                  <el-option label="全部程度" value="all" />
                  <el-option label="紧急" value="urgent" />
                  <el-option label="高" value="high" />
                  <el-option label="中" value="medium" />
                  <el-option label="低" value="low" />
                </el-select>
                
                <el-select v-model="typeFilter" placeholder="异常类型" size="default">
                  <el-option label="全部类型" value="all" />
                  <el-option label="配送延误" value="delay" />
                  <el-option label="货物丢失" value="lost" />
                  <el-option label="货物损坏" value="damage" />
                  <el-option label="地址错误" value="address" />
                  <el-option label="客户拒收" value="refuse" />
                  <el-option label="车辆故障" value="vehicle" />
                  <el-option label="天气影响" value="weather" />
                </el-select>
                
                <el-select v-model="statusFilter" placeholder="处理状态" size="default">
                  <el-option label="全部状态" value="all" />
                  <el-option label="待处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="已升级" value="escalated" />
                  <el-option label="已解决" value="resolved" />
                  <el-option label="已关闭" value="closed" />
                </el-select>
                
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="default"
                />
                
                <el-button @click="applyFilters" type="primary">
                  <Filter class="w-4 h-4 mr-2" />
                  应用筛选
                </el-button>
              </div>
            </div>

            <!-- 异常列表 -->
            <div class="exceptions-list">
              <div class="list-header">
                <h3 class="text-lg font-semibold text-gray-800">异常事件处理</h3>
                <div class="list-actions">
                  <el-button @click="handleBatchProcess" size="small">
                    批量处理
                  </el-button>
                  <el-dropdown @command="handleBulkAction">
                    <el-button size="small">
                      批量操作
                      <ArrowDown class="w-3 h-3 ml-1" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="assign">批量分配</el-dropdown-item>
                        <el-dropdown-item command="escalate">批量升级</el-dropdown-item>
                        <el-dropdown-item command="notify">通知客户</el-dropdown-item>
                        <el-dropdown-item command="export">导出数据</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>

              <div class="exceptions-table">
                <el-table
                  :data="filteredExceptions"
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                  :row-class-name="getRowClassName"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="id" label="异常ID" width="120">
                    <template #default="{ row }">
                      <el-link @click="viewExceptionDetails(row)" type="primary">
                        {{ row.id }}
                      </el-link>
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="异常类型" width="120">
                    <template #default="{ row }">
                      <div class="exception-type">
                        <component :is="getExceptionIcon(row.type)" class="w-4 h-4 mr-2" />
                        {{ getExceptionTypeText(row.type) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="severity" label="严重程度" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getSeverityTagType(row.severity)" size="small">
                        {{ getSeverityText(row.severity) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="orderNumber" label="关联订单" width="140">
                    <template #default="{ row }">
                      <el-link @click="viewOrderDetails(row.orderNumber)" type="primary">
                        {{ row.orderNumber }}
                      </el-link>
                    </template>
                  </el-table-column>
                  <el-table-column prop="customer" label="客户" width="120" />
                  <el-table-column prop="description" label="异常描述" min-width="200">
                    <template #default="{ row }">
                      <div class="exception-description">
                        <p class="description-text">{{ row.description }}</p>
                        <div v-if="row.location" class="description-meta">
                          <MapPin class="w-3 h-3 mr-1" />
                          {{ row.location }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="reportTime" label="上报时间" width="140" />
                  <el-table-column prop="status" label="处理状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getStatusTagType(row.status)">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="assignee" label="处理人" width="100">
                    <template #default="{ row }">
                      <div v-if="row.assignee" class="assignee-info">
                        <el-avatar :size="24" :src="row.assignee.avatar">
                          {{ row.assignee.name.charAt(0) }}
                        </el-avatar>
                        <span class="assignee-name">{{ row.assignee.name }}</span>
                      </div>
                      <span v-else class="text-gray-400">未分配</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="handleException(row)" size="small" type="primary">
                        处理
                      </el-button>
                      <el-button @click="escalateException(row)" size="small" type="warning">
                        升级
                      </el-button>
                      <el-dropdown @command="(command) => handleExceptionAction(row, command)">
                        <el-button size="small">
                          更多
                          <ArrowDown class="w-3 h-3 ml-1" />
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="assign">分配处理人</el-dropdown-item>
                            <el-dropdown-item command="timeline">查看时间线</el-dropdown-item>
                            <el-dropdown-item command="notify">通知客户</el-dropdown-item>
                            <el-dropdown-item command="duplicate">标记重复</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalExceptions"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 应急响应 -->
        <el-tab-pane label="应急响应" name="emergency">
          <div class="emergency-panel">
            <!-- 应急预案 -->
            <div class="emergency-protocols">
              <div class="protocols-header">
                <h3 class="text-lg font-semibold text-gray-800">应急响应预案</h3>
                <el-button @click="handleCreateProtocol" type="primary" size="small">
                  <Plus class="w-4 h-4 mr-1" />
                  新建预案
                </el-button>
              </div>
              
              <div class="protocols-grid">
                <div
                  v-for="protocol in emergencyProtocols"
                  :key="protocol.id"
                  class="protocol-card"
                  :class="getProtocolLevelClass(protocol.level)"
                >
                  <div class="protocol-header">
                    <div class="protocol-icon">
                      <component :is="getProtocolIcon(protocol.type)" class="w-6 h-6" />
                    </div>
                    <div class="protocol-info">
                      <div class="protocol-title">{{ protocol.title }}</div>
                      <div class="protocol-subtitle">{{ protocol.scenario }}</div>
                    </div>
                    <div class="protocol-level">
                      <el-tag :type="getProtocolTagType(protocol.level)" size="small">
                        {{ getProtocolLevelText(protocol.level) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="protocol-content">
                    <div class="protocol-description">{{ protocol.description }}</div>
                    <div class="protocol-stats">
                      <div class="stat-item">
                        <span class="stat-label">启动次数:</span>
                        <span class="stat-value">{{ protocol.activations }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">平均处理时间:</span>
                        <span class="stat-value">{{ protocol.avgTime }}h</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">成功率:</span>
                        <span class="stat-value">{{ protocol.successRate }}%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="protocol-actions">
                    <el-button @click="activateProtocol(protocol)" type="danger" size="small">
                      <Zap class="w-4 h-4 mr-1" />
                      启动预案
                    </el-button>
                    <el-button @click="viewProtocolDetails(protocol)" size="small">
                      查看详情
                    </el-button>
                    <el-dropdown @command="(command) => handleProtocolAction(protocol, command)">
                      <el-button size="small">
                        更多
                        <ArrowDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="edit">编辑预案</el-dropdown-item>
                          <el-dropdown-item command="test">测试演练</el-dropdown-item>
                          <el-dropdown-item command="history">启动历史</el-dropdown-item>
                          <el-dropdown-item command="duplicate">复制预案</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>

            <!-- 应急处理记录 -->
            <div class="emergency-history">
              <h4 class="text-md font-medium text-gray-700 mb-4">最近应急处理记录</h4>
              <div class="history-timeline">
                <el-timeline>
                  <el-timeline-item
                    v-for="record in emergencyHistory"
                    :key="record.id"
                    :timestamp="record.timestamp"
                    :type="getTimelineType(record.level)"
                  >
                    <div class="timeline-content">
                      <div class="timeline-header">
                        <span class="timeline-title">{{ record.title }}</span>
                        <el-tag :type="getTimelineTagType(record.status)" size="small">
                          {{ record.status }}
                        </el-tag>
                      </div>
                      <div class="timeline-description">{{ record.description }}</div>
                      <div class="timeline-meta">
                        <span class="meta-item">处理人: {{ record.handler }}</span>
                        <span class="meta-item">耗时: {{ record.duration }}</span>
                        <span class="meta-item">影响范围: {{ record.impact }}</span>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据分析 -->
        <el-tab-pane label="数据分析" name="analytics">
          <div class="analytics-panel">
            <!-- 异常趋势分析 -->
            <div class="trend-analysis">
              <div class="analysis-header">
                <h3 class="text-lg font-semibold text-gray-800">异常趋势分析</h3>
                <div class="analysis-controls">
                  <el-radio-group v-model="trendPeriod" size="small">
                    <el-radio-button label="7d">近7天</el-radio-button>
                    <el-radio-button label="30d">近30天</el-radio-button>
                    <el-radio-button label="90d">近90天</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              
              <div class="trend-chart">
                <!-- 图表组件占位 -->
                <div class="chart-placeholder">
                  <BarChart class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500 mb-2">异常趋势分析图表</p>
                  <p class="text-sm text-gray-400">展示不同类型异常的发生趋势和处理效率</p>
                  
                  <!-- 模拟数据展示 -->
                  <div class="trend-summary">
                    <div class="summary-cards">
                      <div class="summary-card">
                        <div class="summary-title">异常总数</div>
                        <div class="summary-value">{{ trendSummary.totalExceptions }}</div>
                        <div class="summary-change" :class="{ positive: trendSummary.exceptionChange > 0, negative: trendSummary.exceptionChange < 0 }">
                          {{ trendSummary.exceptionChange > 0 ? '+' : '' }}{{ trendSummary.exceptionChange }}%
                        </div>
                      </div>
                      
                      <div class="summary-card">
                        <div class="summary-title">处理效率</div>
                        <div class="summary-value">{{ trendSummary.efficiency }}%</div>
                        <div class="summary-change positive">
                          +{{ trendSummary.efficiencyImprovement }}%
                        </div>
                      </div>
                      
                      <div class="summary-card">
                        <div class="summary-title">平均响应时间</div>
                        <div class="summary-value">{{ trendSummary.avgResponse }}分钟</div>
                        <div class="summary-change negative">
                          -{{ trendSummary.responseImprovement }}%
                        </div>
                      </div>
                      
                      <div class="summary-card">
                        <div class="summary-title">客户满意度</div>
                        <div class="summary-value">{{ trendSummary.satisfaction }}/5.0</div>
                        <div class="summary-change positive">
                          +{{ trendSummary.satisfactionImprovement }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 异常类型分布 -->
            <div class="type-distribution">
              <h4 class="text-md font-medium text-gray-700 mb-4">异常类型分布</h4>
              <div class="distribution-chart">
                <div class="chart-container">
                  <!-- 饼图占位 -->
                  <div class="pie-chart-placeholder">
                    <PieChart class="w-20 h-20 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">异常类型分布图</p>
                  </div>
                </div>
                
                <div class="distribution-legend">
                  <div
                    v-for="(item, index) in typeDistribution"
                    :key="index"
                    class="legend-item"
                  >
                    <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                    <div class="legend-info">
                      <div class="legend-label">{{ item.type }}</div>
                      <div class="legend-stats">
                        <span class="legend-count">{{ item.count }}次</span>
                        <span class="legend-percentage">{{ item.percentage }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 预测分析 -->
            <div class="prediction-analysis">
              <h4 class="text-md font-medium text-gray-700 mb-4">异常预测分析</h4>
              <div class="prediction-cards">
                <div
                  v-for="prediction in predictions"
                  :key="prediction.id"
                  class="prediction-card"
                  :class="getPredictionRiskClass(prediction.risk)"
                >
                  <div class="prediction-header">
                    <div class="prediction-icon">
                      <component :is="getPredictionIcon(prediction.type)" class="w-5 h-5" />
                    </div>
                    <div class="prediction-title">{{ prediction.title }}</div>
                    <div class="prediction-risk">
                      <el-tag :type="getPredictionTagType(prediction.risk)" size="small">
                        {{ getPredictionRiskText(prediction.risk) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="prediction-content">
                    <div class="prediction-description">{{ prediction.description }}</div>
                    <div class="prediction-probability">
                      <span class="probability-label">发生概率:</span>
                      <span class="probability-value">{{ prediction.probability }}%</span>
                    </div>
                    <div class="prediction-timeline">
                      <span class="timeline-label">预计时间:</span>
                      <span class="timeline-value">{{ prediction.timeframe }}</span>
                    </div>
                  </div>
                  
                  <div class="prediction-actions">
                    <el-button @click="createPreventiveAction(prediction)" size="small" type="primary">
                      制定预防措施
                    </el-button>
                    <el-button @click="viewPredictionDetails(prediction)" size="small">
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 客户沟通 -->
        <el-tab-pane label="客户沟通" name="communication">
          <div class="communication-panel">
            <!-- 通知模板 -->
            <div class="notification-templates">
              <div class="templates-header">
                <h3 class="text-lg font-semibold text-gray-800">异常通知模板</h3>
                <el-button @click="handleCreateTemplate" type="primary" size="small">
                  <Plus class="w-4 h-4 mr-1" />
                  新建模板
                </el-button>
              </div>
              
              <div class="templates-list">
                <div
                  v-for="template in notificationTemplates"
                  :key="template.id"
                  class="template-card"
                >
                  <div class="template-header">
                    <div class="template-title">{{ template.title }}</div>
                    <div class="template-type">
                      <el-tag size="small">{{ template.type }}</el-tag>
                    </div>
                  </div>
                  
                  <div class="template-content">
                    <div class="template-preview">{{ template.content }}</div>
                    <div class="template-stats">
                      <span class="stat-item">使用次数: {{ template.usage }}</span>
                      <span class="stat-item">满意度: {{ template.satisfaction }}/5.0</span>
                    </div>
                  </div>
                  
                  <div class="template-actions">
                    <el-button @click="useTemplate(template)" size="small" type="primary">
                      使用模板
                    </el-button>
                    <el-button @click="editTemplate(template)" size="small">
                      编辑
                    </el-button>
                    <el-dropdown @command="(command) => handleTemplateAction(template, command)">
                      <el-button size="small">
                        更多
                        <ArrowDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="preview">预览效果</el-dropdown-item>
                          <el-dropdown-item command="duplicate">复制模板</el-dropdown-item>
                          <el-dropdown-item command="statistics">使用统计</el-dropdown-item>
                          <el-dropdown-item command="delete">删除模板</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>

            <!-- 客户反馈 -->
            <div class="customer-feedback">
              <h4 class="text-md font-medium text-gray-700 mb-4">客户反馈管理</h4>
              <div class="feedback-list">
                <div
                  v-for="feedback in customerFeedback"
                  :key="feedback.id"
                  class="feedback-item"
                  :class="getFeedbackSentimentClass(feedback.sentiment)"
                >
                  <div class="feedback-header">
                    <div class="feedback-customer">
                      <el-avatar :size="32" :src="feedback.customerAvatar">
                        {{ feedback.customerName.charAt(0) }}
                      </el-avatar>
                      <div class="customer-info">
                        <div class="customer-name">{{ feedback.customerName }}</div>
                        <div class="feedback-time">{{ feedback.timestamp }}</div>
                      </div>
                    </div>
                    
                    <div class="feedback-rating">
                      <el-rate
                        v-model="feedback.rating"
                        disabled
                        size="small"
                        :colors="['#ff9900', '#ff9900', '#ff9900']"
                      />
                    </div>
                    
                    <div class="feedback-sentiment">
                      <el-tag :type="getFeedbackSentimentTag(feedback.sentiment)" size="small">
                        {{ getFeedbackSentimentText(feedback.sentiment) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="feedback-content">
                    <div class="feedback-exception">
                      关联异常: <el-link @click="viewException(feedback.exceptionId)" type="primary">{{ feedback.exceptionId }}</el-link>
                    </div>
                    <div class="feedback-message">{{ feedback.message }}</div>
                  </div>
                  
                  <div class="feedback-actions">
                    <el-button @click="replyToFeedback(feedback)" size="small" type="primary">
                      回复
                    </el-button>
                    <el-button @click="escalateFeedback(feedback)" size="small" type="warning">
                      升级处理
                    </el-button>
                    <el-button @click="markFeedbackResolved(feedback)" size="small">
                      标记已解决
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 异常详情对话框 -->
    <el-dialog
      v-model="exceptionDetailsVisible"
      title="异常详情"
      width="70%"
      :before-close="handleCloseExceptionDetails"
    >
      <div v-if="selectedException" class="exception-details">
        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-group">
              <label>异常ID:</label>
              <span>{{ selectedException.id }}</span>
            </div>
            <div class="info-group">
              <label>异常类型:</label>
              <span>{{ getExceptionTypeText(selectedException.type) }}</span>
            </div>
            <div class="info-group">
              <label>严重程度:</label>
              <el-tag :type="getSeverityTagType(selectedException.severity)">
                {{ getSeverityText(selectedException.severity) }}
              </el-tag>
            </div>
            <div class="info-group">
              <label>当前状态:</label>
              <el-tag :type="getStatusTagType(selectedException.status)">
                {{ getStatusText(selectedException.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-group">
              <label>关联订单:</label>
              <el-link @click="viewOrderDetails(selectedException.orderNumber)" type="primary">
                {{ selectedException.orderNumber }}
              </el-link>
            </div>
            <div class="info-group">
              <label>客户:</label>
              <span>{{ selectedException.customer }}</span>
            </div>
            <div class="info-group">
              <label>上报时间:</label>
              <span>{{ selectedException.reportTime }}</span>
            </div>
            <div class="info-group">
              <label>处理人:</label>
              <span>{{ selectedException.assignee?.name || '未分配' }}</span>
            </div>
          </div>
        </div>

        <!-- 详细描述 -->
        <div class="detailed-description">
          <h4 class="section-title">异常详细描述</h4>
          <div class="description-content">
            <p>{{ selectedException.description }}</p>
            <div v-if="selectedException.location" class="location-info">
              <MapPin class="w-4 h-4 mr-2" />
              <span>发生地点: {{ selectedException.location }}</span>
            </div>
          </div>
        </div>

        <!-- 处理记录 -->
        <div class="processing-history">
          <h4 class="section-title">处理记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="record in selectedException.processingHistory"
              :key="record.id"
              :timestamp="record.timestamp"
              :type="getTimelineType(record.action)"
            >
              <div class="timeline-record">
                <div class="record-action">{{ record.action }}</div>
                <div class="record-operator">操作人: {{ record.operator }}</div>
                <div v-if="record.note" class="record-note">备注: {{ record.note }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exceptionDetailsVisible = false">关闭</el-button>
          <el-button @click="handleExceptionInDialog" type="primary">处理异常</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Refresh, Plus, Warning, Clock, CircleCheck, Zap, Filter, ArrowDown,
  MapPin, BarChart, PieChart, Truck, Package, Navigation, User, MessageCircle,
  Document, Settings, TrendingUp, Eye, Heart, Phone
} from 'lucide-vue-next'

// 响应式数据
const activeExceptionTab = ref('monitoring')
const severityFilter = ref('all')
const typeFilter = ref('all')
const statusFilter = ref('all')
const dateRange = ref([])
const trendPeriod = ref('30d')
const currentPage = ref(1)
const pageSize = ref(20)
const totalExceptions = ref(0)
const selectedExceptions = ref([])
const exceptionDetailsVisible = ref(false)
const selectedException = ref(null)

// 异常统计数据
const exceptionStats = reactive({
  urgent: 3,
  processing: 15,
  resolvedToday: 28,
  avgProcessTime: 2.5,
  resolutionRate: 94.2,
  avgResponseTime: 18,
  responseImprovement: 12.5
})

// 实时警报
const activeAlerts = ref([
  {
    id: 1,
    time: '14:35',
    message: '车辆A12345在高速公路发生故障，影响3个订单配送'
  },
  {
    id: 2,
    time: '14:28',
    message: '华东区域出现大面积交通拥堵，预计延误30分钟'
  }
])

// 异常列表数据
const exceptions = ref([
  {
    id: 'EX2025072301',
    type: 'vehicle',
    severity: 'urgent',
    orderNumber: 'OR2025072301',
    customer: '东方航空',
    description: '配送车辆在途中发生故障，无法继续配送，需要紧急调配备用车辆',
    location: '京沪高速K186处',
    reportTime: '2025-07-23 14:30',
    status: 'processing',
    assignee: {
      name: '张专员',
      avatar: '/avatars/zhang.jpg'
    },
    processingHistory: [
      {
        id: 1,
        timestamp: '2025-07-23 14:35',
        action: '异常升级为紧急',
        operator: '系统自动',
        note: '根据影响范围自动升级'
      },
      {
        id: 2,
        timestamp: '2025-07-23 14:32',
        action: '分配给张专员处理',
        operator: '李主管',
        note: '经验丰富，处理过类似问题'
      },
      {
        id: 3,
        timestamp: '2025-07-23 14:30',
        action: '异常上报',
        operator: '王司机',
        note: '车辆无法启动，请求支援'
      }
    ]
  },
  {
    id: 'EX2025072302',
    type: 'delay',
    severity: 'high',
    orderNumber: 'OR2025072302',
    customer: '南方航空',
    description: '由于交通拥堵导致配送延误超过2小时，客户投诉',
    location: '广州市天河区',
    reportTime: '2025-07-23 13:45',
    status: 'resolved',
    assignee: {
      name: '李专员',
      avatar: '/avatars/li.jpg'
    },
    processingHistory: []
  },
  {
    id: 'EX2025072303',
    type: 'damage',
    severity: 'medium',
    orderNumber: 'OR2025072303',
    customer: '厦门航空',
    description: '货物包装在运输过程中出现破损，需要检查内件完整性',
    location: '厦门配送中心',
    reportTime: '2025-07-23 12:20',
    status: 'pending',
    assignee: null,
    processingHistory: []
  },
  {
    id: 'EX2025072304',
    type: 'address',
    severity: 'low',
    orderNumber: 'OR2025072304',
    customer: '深圳航空',
    description: '收货地址信息不完整，无法正常配送',
    location: '深圳宝安区',
    reportTime: '2025-07-23 11:30',
    status: 'processing',
    assignee: {
      name: '王专员',
      avatar: '/avatars/wang.jpg'
    },
    processingHistory: []
  }
])

// 应急预案数据
const emergencyProtocols = ref([
  {
    id: 1,
    title: '车辆故障应急预案',
    scenario: '配送车辆在途中发生故障',
    type: 'vehicle',
    level: 'critical',
    description: '当配送车辆发生故障时，立即启动备用车辆调配流程，确保货物及时配送',
    activations: 12,
    avgTime: 1.5,
    successRate: 95.8
  },
  {
    id: 2,
    title: '恶劣天气应急预案',
    scenario: '台风、暴雨等恶劣天气影响配送',
    type: 'weather',
    level: 'high',
    description: '恶劣天气条件下，调整配送路线，确保人员和货物安全',
    activations: 8,
    avgTime: 2.3,
    successRate: 92.5
  },
  {
    id: 3,
    title: '大规模延误应急预案',
    scenario: '区域性交通拥堵导致大量订单延误',
    type: 'delay',
    level: 'medium',
    description: '启动客户批量通知机制，重新安排配送时间，减少客户投诉',
    activations: 15,
    avgTime: 3.2,
    successRate: 89.3
  },
  {
    id: 4,
    title: '货物丢失应急预案',
    scenario: '配送过程中货物丢失',
    type: 'lost',
    level: 'critical',
    description: '立即启动货物追踪流程，通知相关部门和客户，启动理赔程序',
    activations: 3,
    avgTime: 4.8,
    successRate: 85.7
  }
])

// 应急处理历史
const emergencyHistory = ref([
  {
    id: 1,
    timestamp: '2025-07-23 14:30',
    title: '车辆故障应急预案启动',
    description: '京A12345车辆故障，成功调配备用车辆，避免配送延误',
    handler: '张专员',
    duration: '45分钟',
    impact: '3个订单',
    level: 'critical',
    status: '已完成'
  },
  {
    id: 2,
    timestamp: '2025-07-22 09:15',
    title: '恶劣天气应急预案启动',
    description: '台风影响华南区域配送，调整路线确保安全',
    handler: '李主管',
    duration: '3小时',
    impact: '68个订单',
    level: 'high',
    status: '已完成'
  }
])

// 趋势分析数据
const trendSummary = reactive({
  totalExceptions: 156,
  exceptionChange: -8.5,
  efficiency: 94.2,
  efficiencyImprovement: 5.3,
  avgResponse: 18,
  responseImprovement: 12.5,
  satisfaction: 4.3,
  satisfactionImprovement: 0.2
})

// 异常类型分布
const typeDistribution = ref([
  { type: '配送延误', count: 45, percentage: 35.2, color: '#f59e0b' },
  { type: '车辆故障', count: 28, percentage: 21.9, color: '#ef4444' },
  { type: '货物损坏', count: 22, percentage: 17.2, color: '#8b5cf6' },
  { type: '地址错误', count: 18, percentage: 14.1, color: '#3b82f6' },
  { type: '客户拒收', count: 10, percentage: 7.8, color: '#10b981' },
  { type: '其他', count: 5, percentage: 3.8, color: '#64748b' }
])

// 预测分析数据
const predictions = ref([
  {
    id: 1,
    type: 'weather',
    title: '台风天气影响配送',
    description: '预计本周末台风将影响华南区域，可能导致大范围配送延误',
    risk: 'high',
    probability: 78,
    timeframe: '本周末'
  },
  {
    id: 2,
    type: 'traffic',
    title: '节假日交通拥堵',
    description: '即将到来的长假期间，主要城市交通拥堵概率增加',
    risk: 'medium',
    probability: 65,
    timeframe: '下周'
  },
  {
    id: 3,
    type: 'vehicle',
    title: '车辆维护期到期',
    description: '多辆配送车辆即将到达维护期，需提前安排维护避免故障',
    risk: 'low',
    probability: 45,
    timeframe: '本月底'
  }
])

// 通知模板数据
const notificationTemplates = ref([
  {
    id: 1,
    title: '配送延误通知',
    type: '延误通知',
    content: '尊敬的客户，由于不可抗力因素，您的订单可能会延误配送，我们深表歉意...',
    usage: 156,
    satisfaction: 4.2
  },
  {
    id: 2,
    title: '货物损坏处理',
    type: '损坏通知',
    content: '很抱歉通知您，您的货物在运输过程中出现损坏，我们将立即启动理赔流程...',
    usage: 23,
    satisfaction: 4.0
  },
  {
    id: 3,
    title: '异常解决确认',
    type: '解决确认',
    content: '您好，关于您反馈的配送问题，我们已经妥善处理，请确认是否满意...',
    usage: 89,
    satisfaction: 4.5
  }
])

// 客户反馈数据
const customerFeedback = ref([
  {
    id: 1,
    customerName: '东方航空采购部',
    customerAvatar: '/avatars/dongfang.jpg',
    exceptionId: 'EX2025072301',
    rating: 3,
    sentiment: 'neutral',
    message: '虽然出现了故障，但处理还算及时，希望后续能加强车辆维护',
    timestamp: '2025-07-23 15:20'
  },
  {
    id: 2,
    customerName: '南方航空',
    customerAvatar: '/avatars/nanfang.jpg',
    exceptionId: 'EX2025072302',
    rating: 4,
    sentiment: 'positive',
    message: '配送延误得到了及时解决，客服态度很好，满意',
    timestamp: '2025-07-23 14:45'
  },
  {
    id: 3,
    customerName: '厦门航空',
    customerAvatar: '/avatars/xiamen.jpg',
    exceptionId: 'EX2025072303',
    rating: 2,
    sentiment: 'negative',
    message: '货物损坏影响了我们的正常工作，希望改进包装质量',
    timestamp: '2025-07-23 13:30'
  }
])

// 计算属性
const filteredExceptions = computed(() => {
  let filtered = exceptions.value
  
  if (severityFilter.value !== 'all') {
    filtered = filtered.filter(e => e.severity === severityFilter.value)
  }
  
  if (typeFilter.value !== 'all') {
    filtered = filtered.filter(e => e.type === typeFilter.value)
  }
  
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(e => e.status === statusFilter.value)
  }
  
  totalExceptions.value = filtered.length
  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

// 方法定义
const handleTabChange = (tabName) => {
  activeExceptionTab.value = tabName
}

const handleRefreshExceptions = () => {
  ElMessage.success('异常数据已刷新')
}

const handleCreateException = () => {
  ElMessage.info('创建新异常记录')
}

const handleEmergencyProtocol = () => {
  ElMessage.warning('启动应急响应协议')
}

const handleAlert = (alert) => {
  ElMessage.success(`正在处理警报: ${alert.message}`)
  // 移除已处理的警报
  const index = activeAlerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    activeAlerts.value.splice(index, 1)
  }
}

const applyFilters = () => {
  currentPage.value = 1
  ElMessage.success('筛选条件已应用')
}

const handleSelectionChange = (selection) => {
  selectedExceptions.value = selection
}

const handleBatchProcess = () => {
  if (selectedExceptions.value.length === 0) {
    ElMessage.warning('请先选择要处理的异常')
    return
  }
  ElMessage.success(`批量处理 ${selectedExceptions.value.length} 个异常`)
}

const handleBulkAction = (command) => {
  if (selectedExceptions.value.length === 0) {
    ElMessage.warning('请先选择要操作的异常')
    return
  }
  
  switch (command) {
    case 'assign':
      ElMessage.success('批量分配处理人')
      break
    case 'escalate':
      ElMessage.warning('批量升级异常')
      break
    case 'notify':
      ElMessage.success('批量通知客户')
      break
    case 'export':
      ElMessage.success('导出异常数据')
      break
  }
}

const viewExceptionDetails = (exception) => {
  selectedException.value = exception
  exceptionDetailsVisible.value = true
}

const handleException = (exception) => {
  ElMessage.success(`开始处理异常: ${exception.id}`)
}

const escalateException = (exception) => {
  ElMessage.warning(`升级异常: ${exception.id}`)
}

const handleExceptionAction = (exception, command) => {
  switch (command) {
    case 'assign':
      ElMessage.success(`分配处理人: ${exception.id}`)
      break
    case 'timeline':
      ElMessage.info(`查看时间线: ${exception.id}`)
      break
    case 'notify':
      ElMessage.success(`通知客户: ${exception.id}`)
      break
    case 'duplicate':
      ElMessage.info(`标记重复: ${exception.id}`)
      break
  }
}

const handleCloseExceptionDetails = () => {
  exceptionDetailsVisible.value = false
  selectedException.value = null
}

const handleExceptionInDialog = () => {
  if (selectedException.value) {
    ElMessage.success(`处理异常: ${selectedException.value.id}`)
    exceptionDetailsVisible.value = false
  }
}

const viewOrderDetails = (orderNumber) => {
  ElMessage.info(`查看订单详情: ${orderNumber}`)
}

const activateProtocol = (protocol) => {
  ElMessage.warning(`启动应急预案: ${protocol.title}`)
}

const viewProtocolDetails = (protocol) => {
  ElMessage.info(`查看预案详情: ${protocol.title}`)
}

const handleProtocolAction = (protocol, command) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑预案: ${protocol.title}`)
      break
    case 'test':
      ElMessage.success(`测试演练: ${protocol.title}`)
      break
    case 'history':
      ElMessage.info(`查看启动历史: ${protocol.title}`)
      break
    case 'duplicate':
      ElMessage.success(`复制预案: ${protocol.title}`)
      break
  }
}

const handleCreateProtocol = () => {
  ElMessage.info('创建新应急预案')
}

const createPreventiveAction = (prediction) => {
  ElMessage.success(`制定预防措施: ${prediction.title}`)
}

const viewPredictionDetails = (prediction) => {
  ElMessage.info(`查看预测详情: ${prediction.title}`)
}

const handleCreateTemplate = () => {
  ElMessage.info('创建新通知模板')
}

const useTemplate = (template) => {
  ElMessage.success(`使用模板: ${template.title}`)
}

const editTemplate = (template) => {
  ElMessage.info(`编辑模板: ${template.title}`)
}

const handleTemplateAction = (template, command) => {
  switch (command) {
    case 'preview':
      ElMessage.info(`预览模板: ${template.title}`)
      break
    case 'duplicate':
      ElMessage.success(`复制模板: ${template.title}`)
      break
    case 'statistics':
      ElMessage.info(`查看统计: ${template.title}`)
      break
    case 'delete':
      ElMessage.warning(`删除模板: ${template.title}`)
      break
  }
}

const replyToFeedback = (feedback) => {
  ElMessage.success(`回复客户反馈: ${feedback.customerName}`)
}

const escalateFeedback = (feedback) => {
  ElMessage.warning(`升级客户反馈: ${feedback.customerName}`)
}

const markFeedbackResolved = (feedback) => {
  ElMessage.success(`标记反馈已解决: ${feedback.customerName}`)
}

const viewException = (exceptionId) => {
  ElMessage.info(`查看异常: ${exceptionId}`)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 辅助方法
const getRowClassName = ({ row }) => {
  if (row.severity === 'urgent') return 'exception-row-urgent'
  if (row.severity === 'high') return 'exception-row-high'
  return ''
}

const getExceptionIcon = (type) => {
  const icons = {
    'vehicle': Truck,
    'delay': Clock,
    'damage': Package,
    'address': MapPin,
    'refuse': User,
    'lost': Warning,
    'weather': Settings
  }
  return icons[type] || Warning
}

const getExceptionTypeText = (type) => {
  const texts = {
    'vehicle': '车辆故障',
    'delay': '配送延误',
    'damage': '货物损坏',
    'address': '地址错误',
    'refuse': '客户拒收',
    'lost': '货物丢失',
    'weather': '天气影响'
  }
  return texts[type] || type
}

const getSeverityTagType = (severity) => {
  const types = {
    'urgent': 'danger',
    'high': 'warning',
    'medium': 'primary',
    'low': 'info'
  }
  return types[severity] || 'info'
}

const getSeverityText = (severity) => {
  const texts = {
    'urgent': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return texts[severity] || severity
}

const getStatusTagType = (status) => {
  const types = {
    'pending': 'info',
    'processing': 'warning',
    'escalated': 'danger',
    'resolved': 'success',
    'closed': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '待处理',
    'processing': '处理中',
    'escalated': '已升级',
    'resolved': '已解决',
    'closed': '已关闭'
  }
  return texts[status] || status
}

const getProtocolLevelClass = (level) => {
  return `protocol-${level}`
}

const getProtocolIcon = (type) => {
  const icons = {
    'vehicle': Truck,
    'weather': Settings,
    'delay': Clock,
    'lost': Warning
  }
  return icons[type] || Settings
}

const getProtocolTagType = (level) => {
  const types = {
    'critical': 'danger',
    'high': 'warning',
    'medium': 'primary',
    'low': 'info'
  }
  return types[level] || 'info'
}

const getProtocolLevelText = (level) => {
  const texts = {
    'critical': '关键',
    'high': '高级',
    'medium': '中级',
    'low': '基础'
  }
  return texts[level] || level
}

const getTimelineType = (action) => {
  if (action.includes('紧急') || action.includes('升级')) return 'danger'
  if (action.includes('分配') || action.includes('处理')) return 'warning'
  if (action.includes('解决') || action.includes('完成')) return 'success'
  return 'primary'
}

const getTimelineTagType = (status) => {
  const types = {
    '已完成': 'success',
    '进行中': 'warning',
    '已取消': 'info'
  }
  return types[status] || 'primary'
}

const getPredictionRiskClass = (risk) => {
  return `prediction-${risk}`
}

const getPredictionIcon = (type) => {
  const icons = {
    'weather': Settings,
    'traffic': Navigation,
    'vehicle': Truck
  }
  return icons[type] || Warning
}

const getPredictionTagType = (risk) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[risk] || 'info'
}

const getPredictionRiskText = (risk) => {
  const texts = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return texts[risk] || risk
}

const getFeedbackSentimentClass = (sentiment) => {
  return `feedback-${sentiment}`
}

const getFeedbackSentimentTag = (sentiment) => {
  const types = {
    'positive': 'success',
    'neutral': 'warning',
    'negative': 'danger'
  }
  return types[sentiment] || 'info'
}

const getFeedbackSentimentText = (sentiment) => {
  const texts = {
    'positive': '满意',
    'neutral': '一般',
    'negative': '不满'
  }
  return texts[sentiment] || sentiment
}

// 定时器
let alertTimer = null

// 组件挂载
onMounted(() => {
  // 模拟实时警报更新
  alertTimer = setInterval(() => {
    // 这里可以添加实时获取警报的逻辑
  }, 30000)
})

// 组件卸载
onUnmounted(() => {
  if (alertTimer) {
    clearInterval(alertTimer)
  }
})
</script>

<style scoped>
.exception-handling {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.exception-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-detail {
  font-size: 13px;
  color: #94a3b8;
}

.card-trend {
  font-size: 13px;
  font-weight: 500;
}

.card-trend.positive {
  color: #10b981;
}

.card-trend.negative {
  color: #10b981;
}

.card-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
}

.urgent-pulse {
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.alert-banner {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #f87171;
  border-radius: 12px;
  padding: 16px 24px;
  margin-bottom: 24px;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.alert-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-time {
  font-weight: 600;
  color: #dc2626;
  min-width: 50px;
}

.alert-text {
  flex: 1;
  color: #1a202c;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.monitoring-panel {
  padding: 24px;
}

.exception-filters {
  margin-bottom: 24px;
}

.filter-bar {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.exceptions-list {
  background: white;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-actions {
  display: flex;
  gap: 8px;
}

.exceptions-table {
  margin-bottom: 24px;
}

.exception-type {
  display: flex;
  align-items: center;
}

.exception-description {
  max-width: 300px;
}

.description-text {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.description-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #64748b;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 12px;
  color: #64748b;
}

.pagination-wrapper {
  background: #fafbfc;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
}

/* 异常行样式 */
:deep(.exception-row-urgent) {
  background-color: #fef2f2 !important;
  border-left: 4px solid #ef4444;
}

:deep(.exception-row-high) {
  background-color: #fffbeb !important;
  border-left: 4px solid #f59e0b;
}

.emergency-panel {
  padding: 24px;
}

.emergency-protocols {
  margin-bottom: 32px;
}

.protocols-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.protocols-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.protocol-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.protocol-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.protocol-card.protocol-critical {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.protocol-card.protocol-high {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.protocol-card.protocol-medium {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.protocol-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.protocol-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #64748b;
}

.protocol-info {
  flex: 1;
}

.protocol-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.protocol-subtitle {
  font-size: 13px;
  color: #64748b;
}

.protocol-level {
  flex-shrink: 0;
}

.protocol-content {
  margin-bottom: 16px;
}

.protocol-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 12px;
}

.protocol-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.stat-item {
  display: flex;
  gap: 4px;
}

.stat-label {
  color: #64748b;
}

.stat-value {
  color: #1a202c;
  font-weight: 500;
}

.protocol-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.emergency-history {
  margin-top: 32px;
}

.history-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.timeline-content {
  padding-left: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 500;
  color: #1a202c;
}

.timeline-description {
  color: #64748b;
  margin-bottom: 8px;
  line-height: 1.5;
}

.timeline-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #94a3b8;
}

.meta-item {
  white-space: nowrap;
}

.analytics-panel {
  padding: 24px;
}

.trend-analysis {
  margin-bottom: 32px;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.trend-chart {
  background: #f8fafc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.chart-placeholder {
  text-align: center;
}

.trend-summary {
  margin-top: 32px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.summary-change {
  font-size: 12px;
  font-weight: 500;
}

.summary-change.positive {
  color: #10b981;
}

.summary-change.negative {
  color: #10b981;
}

.type-distribution {
  margin-bottom: 32px;
}

.distribution-chart {
  display: flex;
  gap: 32px;
  align-items: center;
  margin-top: 16px;
}

.chart-container {
  flex-shrink: 0;
}

.pie-chart-placeholder {
  width: 200px;
  height: 200px;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
}

.distribution-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.legend-item:last-child {
  border-bottom: none;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend-label {
  font-weight: 500;
  color: #1a202c;
}

.legend-stats {
  display: flex;
  gap: 12px;
  font-size: 13px;
}

.legend-count {
  color: #64748b;
}

.legend-percentage {
  color: #1a202c;
  font-weight: 500;
}

.prediction-analysis {
  margin-top: 32px;
}

.prediction-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.prediction-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.prediction-card.prediction-high {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.prediction-card.prediction-medium {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.prediction-card.prediction-low {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.prediction-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.prediction-icon {
  color: #64748b;
}

.prediction-title {
  flex: 1;
  font-weight: 600;
  color: #1a202c;
}

.prediction-risk {
  flex-shrink: 0;
}

.prediction-content {
  margin-bottom: 16px;
}

.prediction-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 12px;
}

.prediction-probability,
.prediction-timeline {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 4px;
}

.probability-label,
.timeline-label {
  color: #64748b;
}

.probability-value,
.timeline-value {
  color: #1a202c;
  font-weight: 500;
}

.prediction-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.communication-panel {
  padding: 24px;
}

.notification-templates {
  margin-bottom: 32px;
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.templates-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-title {
  font-weight: 600;
  color: #1a202c;
}

.template-type {
  flex-shrink: 0;
}

.template-content {
  margin-bottom: 16px;
}

.template-preview {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #94a3b8;
}

.template-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.customer-feedback {
  margin-top: 32px;
}

.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.feedback-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.feedback-item.feedback-positive {
  border-left: 4px solid #10b981;
}

.feedback-item.feedback-neutral {
  border-left: 4px solid #f59e0b;
}

.feedback-item.feedback-negative {
  border-left: 4px solid #ef4444;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.feedback-customer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-name {
  font-weight: 500;
  color: #1a202c;
}

.feedback-time {
  font-size: 12px;
  color: #64748b;
}

.feedback-rating {
  margin: 0 16px;
}

.feedback-sentiment {
  flex-shrink: 0;
}

.feedback-content {
  margin-bottom: 16px;
}

.feedback-exception {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 8px;
}

.feedback-message {
  color: #1a202c;
  line-height: 1.6;
}

.feedback-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.exception-details {
  max-height: 600px;
  overflow-y: auto;
}

.basic-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-group label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.info-group span {
  color: #1a202c;
}

.detailed-description,
.processing-history {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.description-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.location-info {
  display: flex;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f1f5f9;
  color: #64748b;
  font-size: 14px;
}

.timeline-record {
  padding-left: 16px;
}

.record-action {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.record-operator {
  color: #64748b;
  font-size: 13px;
  margin-bottom: 4px;
}

.record-note {
  color: #64748b;
  font-size: 13px;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .protocols-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .distribution-chart {
    flex-direction: column;
    align-items: center;
  }
  
  .prediction-cards {
    grid-template-columns: 1fr;
  }
  
  .templates-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .exception-handling {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .info-row {
    grid-template-columns: 1fr;
  }
  
  .alert-message {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .feedback-customer {
    width: 100%;
  }
}
</style>