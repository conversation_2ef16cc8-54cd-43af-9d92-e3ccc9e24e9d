<!--
配送成本分析和优化建议系统
版本: 1.0
创建时间: 2025-07-23

物流专员专用的配送成本分析和优化建议界面
特点：
1. 多维度成本分析和成本构成分解
2. 成本趋势预测和预算管理
3. 智能优化建议和实施方案
4. ROI分析和效益评估
5. 成本对比和标杆分析
-->

<template>
  <div class="cost-optimization">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">成本优化中心</h1>
          <p class="page-subtitle">深度分析配送成本，制定优化策略，提升运营效益和盈利能力</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshCosts">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleGenerateReport">
            <Document class="w-4 h-4 mr-2" />
            成本报告
          </el-button>
          <el-button @click="handleOptimizationPlan" type="primary">
            <Target class="w-4 h-4 mr-2" />
            优化方案
          </el-button>
        </div>
      </div>
    </div>

    <!-- 成本概览仪表板 -->
    <div class="cost-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card total-cost">
          <div class="card-icon bg-blue-100 text-blue-600">
            <DollarSign class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(costStats.totalCost) }}</div>
            <div class="card-label">总配送成本</div>
            <div class="card-trend" :class="{ positive: costStats.costChange < 0, negative: costStats.costChange > 0 }">
              {{ costStats.costChange > 0 ? '+' : '' }}{{ costStats.costChange }}% 本月
            </div>
          </div>
        </div>
        
        <div class="overview-card cost-per-order">
          <div class="card-icon bg-green-100 text-green-600">
            <TrendingDown class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ costStats.costPerOrder }}</div>
            <div class="card-label">单均配送成本</div>
            <div class="card-trend positive">-{{ costStats.unitCostReduction }}% 优化</div>
          </div>
        </div>
        
        <div class="overview-card efficiency-index">
          <div class="card-icon bg-orange-100 text-orange-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ costStats.efficiencyIndex }}</div>
            <div class="card-label">成本效率指数</div>
            <div class="card-trend positive">+{{ costStats.efficiencyImprovement }} 提升</div>
          </div>
        </div>
        
        <div class="overview-card savings-potential">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Zap class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(costStats.savingsPotential) }}</div>
            <div class="card-label">优化潜力</div>
            <div class="card-detail">预计年节省</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeCostTab" @tab-change="handleTabChange">
        <!-- 成本分析 -->
        <el-tab-pane label="成本分析" name="analysis">
          <div class="analysis-panel">
            <!-- 时间筛选器 -->
            <div class="time-filter">
              <div class="filter-controls">
                <el-radio-group v-model="analysisTimeRange" @change="handleTimeRangeChange">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="quarter">本季</el-radio-button>
                  <el-radio-button label="year">本年</el-radio-button>
                  <el-radio-button label="custom">自定义</el-radio-button>
                </el-radio-group>
                
                <div v-if="analysisTimeRange === 'custom'" class="custom-date-range">
                  <el-date-picker
                    v-model="customDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handleCustomDateChange"
                  />
                </div>
                
                <el-select v-model="selectedCostCategory" placeholder="成本类别" size="default">
                  <el-option label="全部类别" value="all" />
                  <el-option label="燃油成本" value="fuel" />
                  <el-option label="人工成本" value="labor" />
                  <el-option label="车辆成本" value="vehicle" />
                  <el-option label="保险成本" value="insurance" />
                  <el-option label="维修成本" value="maintenance" />
                  <el-option label="其他成本" value="other" />
                </el-select>
              </div>
            </div>

            <!-- 成本构成分析 -->
            <div class="cost-composition">
              <div class="composition-header">
                <h3 class="text-lg font-semibold text-gray-800">成本构成分析</h3>
                <div class="composition-controls">
                  <el-radio-group v-model="compositionView" size="small">
                    <el-radio-button label="amount">金额</el-radio-button>
                    <el-radio-button label="percentage">占比</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              
              <div class="composition-content">
                <div class="composition-chart">
                  <!-- 饼图组件占位 -->
                  <div class="pie-chart-placeholder">
                    <PieChart class="w-20 h-20 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500 mb-2">成本构成饼图</p>
                    <p class="text-sm text-gray-400">直观展示各类成本的分布情况</p>
                    
                    <!-- 模拟数据展示 -->
                    <div class="composition-summary">
                      <div class="summary-title">成本构成概览</div>
                      <div class="cost-breakdown">
                        <div
                          v-for="(item, index) in costComposition"
                          :key="index"
                          class="breakdown-item"
                        >
                          <div class="item-color" :style="{ backgroundColor: item.color }"></div>
                          <div class="item-info">
                            <div class="item-label">{{ item.category }}</div>
                            <div class="item-value">
                              {{ compositionView === 'amount' ? `¥${formatNumber(item.amount)}` : `${item.percentage}%` }}
                            </div>
                          </div>
                          <div class="item-trend" :class="{ positive: item.change < 0, negative: item.change > 0 }">
                            {{ item.change > 0 ? '+' : '' }}{{ item.change }}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="composition-details">
                  <h4 class="details-title">成本明细分析</h4>
                  <div class="details-table">
                    <el-table :data="costComposition" style="width: 100%">
                      <el-table-column prop="category" label="成本类别" width="120" />
                      <el-table-column prop="amount" label="金额" width="120">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.amount) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="percentage" label="占比" width="80">
                        <template #default="{ row }">
                          {{ row.percentage }}%
                        </template>
                      </el-table-column>
                      <el-table-column prop="change" label="变化" width="100">
                        <template #default="{ row }">
                          <span :class="{ 'text-green-600': row.change < 0, 'text-red-600': row.change > 0 }">
                            {{ row.change > 0 ? '+' : '' }}{{ row.change }}%
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="unitCost" label="单位成本" width="120">
                        <template #default="{ row }">
                          ¥{{ row.unitCost }}/{{ row.unit }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="benchmark" label="行业基准" width="120">
                        <template #default="{ row }">
                          ¥{{ row.benchmark }}/{{ row.unit }}
                        </template>
                      </el-table-column>
                      <el-table-column label="对比" width="100">
                        <template #default="{ row }">
                          <el-tag :type="getBenchmarkTagType(row.unitCost, row.benchmark)" size="small">
                            {{ getBenchmarkText(row.unitCost, row.benchmark) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>

            <!-- 成本趋势分析 -->
            <div class="cost-trends">
              <div class="trends-header">
                <h3 class="text-lg font-semibold text-gray-800">成本趋势分析</h3>
                <div class="trends-controls">
                  <el-select v-model="trendMetric" size="small">
                    <el-option label="总成本" value="total" />
                    <el-option label="单位成本" value="unit" />
                    <el-option label="成本效率" value="efficiency" />
                  </el-select>
                </div>
              </div>
              
              <div class="trends-chart">
                <!-- 折线图组件占位 -->
                <div class="line-chart-placeholder">
                  <BarChart class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500 mb-2">成本趋势图表</p>
                  <p class="text-sm text-gray-400">展示成本随时间的变化趋势和预测</p>
                  
                  <!-- 模拟数据展示 -->
                  <div class="trend-data">
                    <div class="data-grid">
                      <div
                        v-for="(point, index) in trendData"
                        :key="index"
                        class="data-point"
                      >
                        <div class="point-date">{{ point.date }}</div>
                        <div class="point-value">¥{{ formatNumber(point.value) }}</div>
                        <div class="point-change" :class="{
                          positive: point.change < 0,
                          negative: point.change > 0,
                          neutral: point.change === 0
                        }">
                          {{ point.change === 0 ? '持平' : (point.change > 0 ? `+${point.change}%` : `${point.change}%`) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 优化建议 -->
        <el-tab-pane label="优化建议" name="optimization">
          <div class="optimization-panel">
            <!-- 智能分析结果 -->
            <div class="analysis-results">
              <div class="results-header">
                <h3 class="text-lg font-semibold text-gray-800">智能分析结果</h3>
                <el-button @click="handleReanalyze" size="small">
                  <Zap class="w-4 h-4 mr-1" />
                  重新分析
                </el-button>
              </div>
              
              <div class="analysis-summary">
                <div class="summary-cards">
                  <div class="summary-card identified-issues">
                    <div class="card-header">
                      <Warning class="w-6 h-6 text-orange-500" />
                      <span class="card-title">识别问题</span>
                    </div>
                    <div class="card-value">{{ analysisResults.identifiedIssues }}</div>
                    <div class="card-description">项成本优化机会</div>
                  </div>
                  
                  <div class="summary-card potential-savings">
                    <div class="card-header">
                      <TrendingDown class="w-6 h-6 text-green-500" />
                      <span class="card-title">潜在节省</span>
                    </div>
                    <div class="card-value">¥{{ formatNumber(analysisResults.potentialSavings) }}</div>
                    <div class="card-description">年度节省预期</div>
                  </div>
                  
                  <div class="summary-card roi-estimate">
                    <div class="card-header">
                      <Target class="w-6 h-6 text-blue-500" />
                      <span class="card-title">投资回报</span>
                    </div>
                    <div class="card-value">{{ analysisResults.roiEstimate }}%</div>
                    <div class="card-description">预计年化回报率</div>
                  </div>
                  
                  <div class="summary-card implementation-time">
                    <div class="card-header">
                      <Clock class="w-6 h-6 text-purple-500" />
                      <span class="card-title">实施周期</span>
                    </div>
                    <div class="card-value">{{ analysisResults.implementationTime }}</div>
                    <div class="card-description">个月完成优化</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 优化建议列表 -->
            <div class="optimization-suggestions">
              <div class="suggestions-header">
                <h4 class="text-md font-medium text-gray-700">优化建议详情</h4>
                <div class="suggestions-filters">
                  <el-select v-model="suggestionFilter" size="small">
                    <el-option label="全部建议" value="all" />
                    <el-option label="高优先级" value="high" />
                    <el-option label="中优先级" value="medium" />
                    <el-option label="低优先级" value="low" />
                  </el-select>
                  
                  <el-select v-model="categoryFilter" size="small">
                    <el-option label="全部类别" value="all" />
                    <el-option label="路线优化" value="route" />
                    <el-option label="车辆管理" value="vehicle" />
                    <el-option label="成本控制" value="cost" />
                    <el-option label="技术升级" value="technology" />
                  </el-select>
                </div>
              </div>
              
              <div class="suggestions-list">
                <div
                  v-for="suggestion in filteredSuggestions"
                  :key="suggestion.id"
                  class="suggestion-card"
                  :class="getSuggestionPriorityClass(suggestion.priority)"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-icon">
                      <component :is="getSuggestionIcon(suggestion.category)" class="w-6 h-6" />
                    </div>
                    <div class="suggestion-info">
                      <div class="suggestion-title">{{ suggestion.title }}</div>
                      <div class="suggestion-category">{{ getSuggestionCategoryText(suggestion.category) }}</div>
                    </div>
                    <div class="suggestion-priority">
                      <el-tag :type="getSuggestionTagType(suggestion.priority)" size="small">
                        {{ getSuggestionPriorityText(suggestion.priority) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="suggestion-content">
                    <div class="suggestion-description">{{ suggestion.description }}</div>
                    
                    <div class="suggestion-metrics">
                      <div class="metric-item">
                        <span class="metric-label">预期节省:</span>
                        <span class="metric-value savings">¥{{ formatNumber(suggestion.expectedSavings) }}/年</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">投资成本:</span>
                        <span class="metric-value cost">¥{{ formatNumber(suggestion.investmentCost) }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">回收期:</span>
                        <span class="metric-value">{{ suggestion.paybackPeriod }}个月</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">实施难度:</span>
                        <span class="metric-value difficulty">{{ getDifficultyText(suggestion.difficulty) }}</span>
                      </div>
                    </div>
                    
                    <div class="suggestion-impact">
                      <div class="impact-header">
                        <span class="impact-title">预期影响:</span>
                      </div>
                      <div class="impact-items">
                        <div
                          v-for="impact in suggestion.expectedImpacts"
                          :key="impact.id"
                          class="impact-item"
                        >
                          <div class="impact-metric">{{ impact.metric }}</div>
                          <div class="impact-change" :class="{ positive: impact.change > 0, negative: impact.change < 0 }">
                            {{ impact.change > 0 ? '+' : '' }}{{ impact.change }}{{ impact.unit }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="suggestion-actions">
                    <el-button @click="viewImplementationPlan(suggestion)" size="small" type="primary">
                      查看实施方案
                    </el-button>
                    <el-button @click="startImplementation(suggestion)" size="small" type="success">
                      开始实施
                    </el-button>
                    <el-dropdown @command="(command) => handleSuggestionAction(suggestion, command)">
                      <el-button size="small">
                        更多
                        <ArrowDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="details">详细分析</el-dropdown-item>
                          <el-dropdown-item command="simulate">效果模拟</el-dropdown-item>
                          <el-dropdown-item command="schedule">制定计划</el-dropdown-item>
                          <el-dropdown-item command="dismiss">忽略建议</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- ROI分析 -->
        <el-tab-pane label="ROI分析" name="roi">
          <div class="roi-panel">
            <!-- ROI概览 -->
            <div class="roi-overview">
              <div class="overview-header">
                <h3 class="text-lg font-semibold text-gray-800">投资回报分析</h3>
                <div class="overview-controls">
                  <el-select v-model="roiTimeframe" size="small">
                    <el-option label="1年期" value="1year" />
                    <el-option label="2年期" value="2years" />
                    <el-option label="3年期" value="3years" />
                    <el-option label="5年期" value="5years" />
                  </el-select>
                </div>
              </div>
              
              <div class="roi-metrics">
                <div class="metrics-grid">
                  <div class="metric-card total-investment">
                    <div class="metric-icon bg-red-100 text-red-600">
                      <DollarSign class="w-6 h-6" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">¥{{ formatNumber(roiMetrics.totalInvestment) }}</div>
                      <div class="metric-label">总投资额</div>
                      <div class="metric-detail">优化项目投入</div>
                    </div>
                  </div>
                  
                  <div class="metric-card total-savings">
                    <div class="metric-icon bg-green-100 text-green-600">
                      <TrendingDown class="w-6 h-6" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">¥{{ formatNumber(roiMetrics.totalSavings) }}</div>
                      <div class="metric-label">累计节省</div>
                      <div class="metric-detail">{{ roiTimeframe }} 预期</div>
                    </div>
                  </div>
                  
                  <div class="metric-card net-benefit">
                    <div class="metric-icon bg-blue-100 text-blue-600">
                      <TrendingUp class="w-6 h-6" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">¥{{ formatNumber(roiMetrics.netBenefit) }}</div>
                      <div class="metric-label">净收益</div>
                      <div class="metric-detail">节省 - 投入</div>
                    </div>
                  </div>
                  
                  <div class="metric-card roi-percentage">
                    <div class="metric-icon bg-purple-100 text-purple-600">
                      <Target class="w-6 h-6" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">{{ roiMetrics.roiPercentage }}%</div>
                      <div class="metric-label">投资回报率</div>
                      <div class="metric-detail">年化收益率</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- ROI项目分析 -->
            <div class="roi-projects">
              <h4 class="text-md font-medium text-gray-700 mb-4">ROI项目明细</h4>
              <div class="projects-table">
                <el-table :data="roiProjects" style="width: 100%">
                  <el-table-column prop="name" label="优化项目" width="200" />
                  <el-table-column prop="investment" label="投资金额" width="120">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.investment) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="annualSavings" label="年节省额" width="120">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.annualSavings) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="paybackPeriod" label="回收期" width="100">
                    <template #default="{ row }">
                      {{ row.paybackPeriod }}个月
                    </template>
                  </el-table-column>
                  <el-table-column prop="roi" label="ROI" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'text-green-600': row.roi > 20, 'text-orange-600': row.roi < 10 }">
                        {{ row.roi }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="riskLevel" label="风险等级" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getRiskTagType(row.riskLevel)" size="small">
                        {{ getRiskText(row.riskLevel) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="实施状态" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getStatusTagType(row.status)">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="viewProjectDetails(row)" size="small">
                        详情
                      </el-button>
                      <el-button 
                        @click="approveProject(row)" 
                        size="small" 
                        type="success"
                        :disabled="row.status !== 'pending'"
                      >
                        批准
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- ROI预测图表 -->
            <div class="roi-forecast">
              <h4 class="text-md font-medium text-gray-700 mb-4">ROI预测分析</h4>
              <div class="forecast-chart">
                <!-- 图表组件占位 -->
                <div class="chart-placeholder">
                  <BarChart class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500 mb-2">ROI预测图表</p>
                  <p class="text-sm text-gray-400">展示投资回报的时间分布和累计效益</p>
                  
                  <!-- 模拟数据展示 -->
                  <div class="forecast-data">
                    <div class="forecast-timeline">
                      <div
                        v-for="(period, index) in roiForecast"
                        :key="index"
                        class="forecast-period"
                      >
                        <div class="period-label">{{ period.period }}</div>
                        <div class="period-investment">投入: ¥{{ formatNumber(period.investment) }}</div>
                        <div class="period-savings">节省: ¥{{ formatNumber(period.savings) }}</div>
                        <div class="period-cumulative">累计: ¥{{ formatNumber(period.cumulative) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 对比分析 -->
        <el-tab-pane label="对比分析" name="comparison">
          <div class="comparison-panel">
            <!-- 基准对比 -->
            <div class="benchmark-comparison">
              <div class="comparison-header">
                <h3 class="text-lg font-semibold text-gray-800">行业基准对比</h3>
                <div class="comparison-controls">
                  <el-select v-model="benchmarkType" size="small">
                    <el-option label="同行业平均" value="industry" />
                    <el-option label="最佳实践" value="best-practice" />
                    <el-option label="历史表现" value="historical" />
                  </el-select>
                </div>
              </div>
              
              <div class="benchmark-metrics">
                <div class="metrics-comparison">
                  <div
                    v-for="metric in benchmarkMetrics"
                    :key="metric.id"
                    class="comparison-item"
                  >
                    <div class="metric-info">
                      <div class="metric-name">{{ metric.name }}</div>
                      <div class="metric-unit">{{ metric.unit }}</div>
                    </div>
                    
                    <div class="metric-values">
                      <div class="current-value">
                        <span class="value-label">当前</span>
                        <span class="value-number">{{ metric.current }}</span>
                      </div>
                      <div class="benchmark-value">
                        <span class="value-label">基准</span>
                        <span class="value-number">{{ metric.benchmark }}</span>
                      </div>
                    </div>
                    
                    <div class="metric-performance">
                      <div class="performance-indicator" :class="getPerformanceClass(metric.performance)">
                        <component :is="getPerformanceIcon(metric.performance)" class="w-4 h-4" />
                        <span class="performance-text">{{ getPerformanceText(metric.performance) }}</span>
                      </div>
                      <div class="performance-gap">
                        <span :class="{ 'text-green-600': metric.gap < 0, 'text-red-600': metric.gap > 0 }">
                          {{ metric.gap > 0 ? '+' : '' }}{{ metric.gap }}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 竞争对手分析 -->
            <div class="competitor-analysis">
              <h4 class="text-md font-medium text-gray-700 mb-4">竞争对手成本分析</h4>
              <div class="competitor-table">
                <el-table :data="competitorData" style="width: 100%">
                  <el-table-column prop="company" label="公司" width="150" />
                  <el-table-column prop="totalCost" label="总成本指数" width="120" align="center">
                    <template #default="{ row }">
                      <div class="cost-index" :class="getCostIndexClass(row.totalCost)">
                        {{ row.totalCost }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="efficiency" label="效率指数" width="120" align="center">
                    <template #default="{ row }">
                      <div class="efficiency-index" :class="getEfficiencyIndexClass(row.efficiency)">
                        {{ row.efficiency }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="fuelCost" label="燃油成本" width="120" align="center" />
                  <el-table-column prop="laborCost" label="人工成本" width="120" align="center" />
                  <el-table-column prop="technologyLevel" label="技术水平" width="120" align="center">
                    <template #default="{ row }">
                      <el-rate
                        v-model="row.technologyLevel"
                        disabled
                        size="small"
                        :max="5"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="marketShare" label="市场份额" width="120" align="center">
                    <template #default="{ row }">
                      {{ row.marketShare }}%
                    </template>
                  </el-table-column>
                  <el-table-column label="综合评价" width="120" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getOverallRatingType(row.overallRating)">
                        {{ getOverallRatingText(row.overallRating) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- 改进机会分析 -->
            <div class="improvement-opportunities">
              <h4 class="text-md font-medium text-gray-700 mb-4">改进机会识别</h4>
              <div class="opportunities-grid">
                <div
                  v-for="opportunity in improvementOpportunities"
                  :key="opportunity.id"
                  class="opportunity-card"
                >
                  <div class="opportunity-header">
                    <div class="opportunity-icon">
                      <component :is="getOpportunityIcon(opportunity.type)" class="w-6 h-6" />
                    </div>
                    <div class="opportunity-title">{{ opportunity.title }}</div>
                    <div class="opportunity-priority">
                      <el-tag :type="getPriorityTagType(opportunity.priority)" size="small">
                        {{ getPriorityText(opportunity.priority) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="opportunity-content">
                    <div class="opportunity-description">{{ opportunity.description }}</div>
                    <div class="opportunity-metrics">
                      <div class="metric-row">
                        <span class="metric-name">改进潜力:</span>
                        <span class="metric-value">{{ opportunity.potential }}%</span>
                      </div>
                      <div class="metric-row">
                        <span class="metric-name">预期节省:</span>
                        <span class="metric-value">¥{{ formatNumber(opportunity.expectedSavings) }}</span>
                      </div>
                      <div class="metric-row">
                        <span class="metric-name">实施周期:</span>
                        <span class="metric-value">{{ opportunity.timeframe }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="opportunity-actions">
                    <el-button @click="analyzeOpportunity(opportunity)" size="small" type="primary">
                      深入分析
                    </el-button>
                    <el-button @click="createActionPlan(opportunity)" size="small">
                      制定计划
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Refresh, Document, Target, DollarSign, TrendingDown, TrendingUp, Zap,
  PieChart, BarChart, Warning, Clock, ArrowDown, Settings, Navigation,
  Truck, User, CircleCheck, CircleClose, ArrowUp
} from 'lucide-vue-next'

// 响应式数据
const activeCostTab = ref('analysis')
const analysisTimeRange = ref('month')
const customDateRange = ref([])
const selectedCostCategory = ref('all')
const compositionView = ref('amount')
const trendMetric = ref('total')
const suggestionFilter = ref('all')
const categoryFilter = ref('all')
const roiTimeframe = ref('1year')
const benchmarkType = ref('industry')

// 成本统计数据
const costStats = reactive({
  totalCost: 1256800,
  costChange: -3.2,
  costPerOrder: 28.5,
  unitCostReduction: 8.5,
  efficiencyIndex: 127.8,
  efficiencyImprovement: 12.3,
  savingsPotential: 256000
})

// 成本构成数据
const costComposition = ref([
  {
    category: '燃油成本',
    amount: 452800,
    percentage: 36.0,
    change: -2.1,
    unitCost: 6.8,
    unit: '升',
    benchmark: 7.2,
    color: '#ef4444'
  },
  {
    category: '人工成本',
    amount: 376200,
    percentage: 29.9,
    change: 3.5,
    unitCost: 158.5,
    unit: '小时',
    benchmark: 165.0,
    color: '#3b82f6'
  },
  {
    category: '车辆成本',
    amount: 251600,
    percentage: 20.0,
    change: -1.8,
    unitCost: 2.5,
    unit: '公里',
    benchmark: 2.8,
    color: '#10b981'
  },
  {
    category: '保险成本',
    amount: 100600,
    percentage: 8.0,
    change: 0.5,
    unitCost: 8500,
    unit: '年',
    benchmark: 9200,
    color: '#f59e0b'
  },
  {
    category: '维修成本',
    amount: 50300,
    percentage: 4.0,
    change: -5.2,
    unitCost: 0.8,
    unit: '公里',
    benchmark: 1.0,
    color: '#8b5cf6'
  },
  {
    category: '其他成本',
    amount: 25300,
    percentage: 2.1,
    change: 1.2,
    unitCost: 120,
    unit: '月',
    benchmark: 150,
    color: '#64748b'
  }
])

// 趋势数据
const trendData = ref([
  { date: '01月', value: 1180000, change: 0 },
  { date: '02月', value: 1220000, change: 3.4 },
  { date: '03月', value: 1195000, change: -2.0 },
  { date: '04月', value: 1260000, change: 5.4 },
  { date: '05月', value: 1240000, change: -1.6 },
  { date: '06月', value: 1210000, change: -2.4 },
  { date: '07月', value: 1256800, change: 3.9 }
])

// 智能分析结果
const analysisResults = reactive({
  identifiedIssues: 8,
  potentialSavings: 256000,
  roiEstimate: 145.8,
  implementationTime: 6
})

// 优化建议数据
const optimizationSuggestions = ref([
  {
    id: 1,
    title: '智能路线优化系统',
    category: 'route',
    priority: 'high',
    description: '采用AI算法优化配送路线，减少空驶里程，提高车辆利用率',
    expectedSavings: 86000,
    investmentCost: 45000,
    paybackPeriod: 6.3,
    difficulty: 'medium',
    expectedImpacts: [
      { id: 1, metric: '燃油成本', change: -15, unit: '%' },
      { id: 2, metric: '配送时间', change: -20, unit: '%' },
      { id: 3, metric: '车辆利用率', change: 25, unit: '%' }
    ]
  },
  {
    id: 2,
    title: '新能源车辆替换',
    category: 'vehicle',
    priority: 'high',
    description: '逐步替换燃油车为新能源车辆，降低燃油成本和维护成本',
    expectedSavings: 125000,
    investmentCost: 180000,
    paybackPeriod: 17.3,
    difficulty: 'high',
    expectedImpacts: [
      { id: 1, metric: '燃油成本', change: -40, unit: '%' },
      { id: 2, metric: '维修成本', change: -25, unit: '%' },
      { id: 3, metric: '环保指数', change: 60, unit: '%' }
    ]
  },
  {
    id: 3,
    title: '预防性维护系统',
    category: 'vehicle',
    priority: 'medium',
    description: '建立预防性维护体系，减少突发故障，延长车辆使用寿命',
    expectedSavings: 32000,
    investmentCost: 15000,
    paybackPeriod: 5.6,
    difficulty: 'low',
    expectedImpacts: [
      { id: 1, metric: '维修成本', change: -35, unit: '%' },
      { id: 2, metric: '车辆可用率', change: 15, unit: '%' },
      { id: 3, metric: '紧急维修', change: -50, unit: '%' }
    ]
  },
  {
    id: 4,
    title: '司机培训优化',
    category: 'cost',
    priority: 'medium',
    description: '加强司机节油驾驶培训，提高驾驶技能，降低燃油消耗',
    expectedSavings: 28000,
    investmentCost: 8000,
    paybackPeriod: 3.4,
    difficulty: 'low',
    expectedImpacts: [
      { id: 1, metric: '燃油消耗', change: -12, unit: '%' },
      { id: 2, metric: '事故率', change: -20, unit: '%' },
      { id: 3, metric: '司机满意度', change: 15, unit: '%' }
    ]
  },
  {
    id: 5,
    title: '智能调度系统',
    category: 'technology',
    priority: 'low',
    description: '升级调度系统，实现智能派单和动态调度，提高整体效率',
    expectedSavings: 45000,
    investmentCost: 25000,
    paybackPeriod: 6.7,
    difficulty: 'medium',
    expectedImpacts: [
      { id: 1, metric: '调度效率', change: 30, unit: '%' },
      { id: 2, metric: '客户满意度', change: 20, unit: '%' },
      { id: 3, metric: '空载率', change: -18, unit: '%' }
    ]
  }
])

// ROI指标
const roiMetrics = reactive({
  totalInvestment: 273000,
  totalSavings: 398000,
  netBenefit: 125000,
  roiPercentage: 145.8
})

// ROI项目数据
const roiProjects = ref([
  {
    id: 1,
    name: '智能路线优化',
    investment: 45000,
    annualSavings: 86000,
    paybackPeriod: 6.3,
    roi: 191.1,
    riskLevel: 'low',
    status: 'approved'
  },
  {
    id: 2,
    name: '新能源车辆',
    investment: 180000,
    annualSavings: 125000,
    paybackPeriod: 17.3,
    roi: 69.4,
    riskLevel: 'medium',
    status: 'pending'
  },
  {
    id: 3,
    name: '预防性维护',
    investment: 15000,
    annualSavings: 32000,
    paybackPeriod: 5.6,
    roi: 213.3,
    riskLevel: 'low',
    status: 'implementing'
  },
  {
    id: 4,
    name: '司机培训',
    investment: 8000,
    annualSavings: 28000,
    paybackPeriod: 3.4,
    roi: 350.0,
    riskLevel: 'low',
    status: 'completed'
  },
  {
    id: 5,
    name: '智能调度',
    investment: 25000,
    annualSavings: 45000,
    paybackPeriod: 6.7,
    roi: 180.0,
    riskLevel: 'medium',
    status: 'planning'
  }
])

// ROI预测数据
const roiForecast = ref([
  { period: 'Q1', investment: 68000, savings: 15000, cumulative: -53000 },
  { period: 'Q2', investment: 52000, savings: 35000, cumulative: -70000 },
  { period: 'Q3', investment: 78000, savings: 58000, cumulative: -90000 },
  { period: 'Q4', investment: 75000, savings: 82000, cumulative: -83000 },
  { period: 'Y2', investment: 0, savings: 156000, cumulative: 73000 },
  { period: 'Y3', investment: 0, savings: 186000, cumulative: 259000 }
])

// 基准对比指标
const benchmarkMetrics = ref([
  {
    id: 1,
    name: '单位配送成本',
    unit: '元/单',
    current: 28.5,
    benchmark: 32.1,
    performance: 'good',
    gap: -11.2
  },
  {
    id: 2,
    name: '燃油效率',
    unit: 'L/100km',
    current: 12.8,
    benchmark: 11.5,
    performance: 'poor',
    gap: 11.3
  },
  {
    id: 3,
    name: '车辆利用率',
    unit: '%',
    current: 78.5,
    benchmark: 82.0,
    performance: 'average',
    gap: -4.3
  },
  {
    id: 4,
    name: '维修成本比',
    unit: '%',
    current: 4.0,
    benchmark: 5.2,
    performance: 'excellent',
    gap: -23.1
  }
])

// 竞争对手数据
const competitorData = ref([
  {
    company: '我司',
    totalCost: 100,
    efficiency: 127.8,
    fuelCost: 6.8,
    laborCost: 158.5,
    technologyLevel: 4,
    marketShare: 15.8,
    overallRating: 'good'
  },
  {
    company: '顺丰速运',
    totalCost: 95,
    efficiency: 145.2,
    fuelCost: 6.2,
    laborCost: 172.0,
    technologyLevel: 5,
    marketShare: 32.5,
    overallRating: 'excellent'
  },
  {
    company: '德邦物流',
    totalCost: 102,
    efficiency: 118.6,
    fuelCost: 7.1,
    laborCost: 155.8,
    technologyLevel: 3,
    marketShare: 18.3,
    overallRating: 'good'
  },
  {
    company: '京东物流',
    totalCost: 98,
    efficiency: 138.9,
    fuelCost: 6.5,
    laborCost: 165.2,
    technologyLevel: 4,
    marketShare: 22.1,
    overallRating: 'excellent'
  },
  {
    company: '圆通速递',
    totalCost: 108,
    efficiency: 112.4,
    fuelCost: 7.8,
    laborCost: 148.9,
    technologyLevel: 3,
    marketShare: 11.3,
    overallRating: 'average'
  }
])

// 改进机会
const improvementOpportunities = ref([
  {
    id: 1,
    type: 'fuel',
    title: '燃油效率提升',
    description: '通过路线优化和驾驶培训，提升燃油使用效率',
    priority: 'high',
    potential: 15,
    expectedSavings: 68000,
    timeframe: '3-6个月'
  },
  {
    id: 2,
    type: 'technology',
    title: '数字化升级',
    description: '投资新技术系统，提升整体运营效率',
    priority: 'medium',
    potential: 25,
    expectedSavings: 125000,
    timeframe: '6-12个月'
  },
  {
    id: 3,
    type: 'process',
    title: '流程优化',
    description: '优化内部流程，减少不必要的环节和等待时间',
    priority: 'medium',
    potential: 12,
    expectedSavings: 45000,
    timeframe: '2-4个月'
  },
  {
    id: 4,
    type: 'maintenance',
    title: '维护策略改进',
    description: '从被动维修转向预防性维护，降低总体维护成本',
    priority: 'low',
    potential: 8,
    expectedSavings: 32000,
    timeframe: '4-8个月'
  }
])

// 计算属性
const filteredSuggestions = computed(() => {
  let filtered = optimizationSuggestions.value
  
  if (suggestionFilter.value !== 'all') {
    filtered = filtered.filter(s => s.priority === suggestionFilter.value)
  }
  
  if (categoryFilter.value !== 'all') {
    filtered = filtered.filter(s => s.category === categoryFilter.value)
  }
  
  return filtered
})

// 方法定义
const handleTabChange = (tabName) => {
  activeCostTab.value = tabName
}

const handleRefreshCosts = () => {
  ElMessage.success('成本数据已刷新')
}

const handleGenerateReport = () => {
  ElMessage.success('成本报告生成中...')
}

const handleOptimizationPlan = () => {
  ElMessage.info('制定优化方案')
}

const handleTimeRangeChange = (value) => {
  analysisTimeRange.value = value
  if (value !== 'custom') {
    customDateRange.value = []
  }
}

const handleCustomDateChange = (value) => {
  if (value) {
    ElMessage.success(`已选择时间范围: ${value[0]} 至 ${value[1]}`)
  }
}

const handleReanalyze = () => {
  ElMessage.success('重新分析成本数据...')
}

const viewImplementationPlan = (suggestion) => {
  ElMessage.success(`查看实施方案: ${suggestion.title}`)
}

const startImplementation = (suggestion) => {
  ElMessage.success(`开始实施: ${suggestion.title}`)
}

const handleSuggestionAction = (suggestion, command) => {
  switch (command) {
    case 'details':
      ElMessage.info(`详细分析: ${suggestion.title}`)
      break
    case 'simulate':
      ElMessage.info(`效果模拟: ${suggestion.title}`)
      break
    case 'schedule':
      ElMessage.success(`制定计划: ${suggestion.title}`)
      break
    case 'dismiss':
      ElMessage.warning(`忽略建议: ${suggestion.title}`)
      break
  }
}

const viewProjectDetails = (project) => {
  ElMessage.info(`查看项目详情: ${project.name}`)
}

const approveProject = (project) => {
  ElMessage.success(`批准项目: ${project.name}`)
  project.status = 'approved'
}

const analyzeOpportunity = (opportunity) => {
  ElMessage.success(`深入分析: ${opportunity.title}`)
}

const createActionPlan = (opportunity) => {
  ElMessage.success(`制定计划: ${opportunity.title}`)
}

// 辅助方法
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

const getBenchmarkTagType = (current, benchmark) => {
  const ratio = current / benchmark
  if (ratio <= 0.9) return 'success'
  if (ratio <= 1.1) return 'warning'
  return 'danger'
}

const getBenchmarkText = (current, benchmark) => {
  const ratio = current / benchmark
  if (ratio <= 0.9) return '优于基准'
  if (ratio <= 1.1) return '接近基准'
  return '高于基准'
}

const getSuggestionPriorityClass = (priority) => {
  return `suggestion-${priority}`
}

const getSuggestionIcon = (category) => {
  const icons = {
    'route': Navigation,
    'vehicle': Truck,
    'cost': DollarSign,
    'technology': Settings
  }
  return icons[category] || Settings
}

const getSuggestionTagType = (priority) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[priority] || 'info'
}

const getSuggestionPriorityText = (priority) => {
  const texts = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return texts[priority] || priority
}

const getSuggestionCategoryText = (category) => {
  const texts = {
    'route': '路线优化',
    'vehicle': '车辆管理',
    'cost': '成本控制',
    'technology': '技术升级'
  }
  return texts[category] || category
}

const getDifficultyText = (difficulty) => {
  const texts = {
    'low': '简单',
    'medium': '中等',
    'high': '困难'
  }
  return texts[difficulty] || difficulty
}

const getRiskTagType = (riskLevel) => {
  const types = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return types[riskLevel] || 'info'
}

const getRiskText = (riskLevel) => {
  const texts = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return texts[riskLevel] || riskLevel
}

const getStatusTagType = (status) => {
  const types = {
    'pending': 'info',
    'approved': 'success',
    'implementing': 'warning',
    'completed': 'success',
    'planning': 'primary'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '待批准',
    'approved': '已批准',
    'implementing': '实施中',
    'completed': '已完成',
    'planning': '规划中'
  }
  return texts[status] || status
}

const getPerformanceClass = (performance) => {
  return `performance-${performance}`
}

const getPerformanceIcon = (performance) => {
  const icons = {
    'excellent': CircleCheck,
    'good': TrendingUp,
    'average': ArrowUp,
    'poor': CircleClose
  }
  return icons[performance] || ArrowUp
}

const getPerformanceText = (performance) => {
  const texts = {
    'excellent': '优秀',
    'good': '良好',
    'average': '一般',
    'poor': '较差'
  }
  return texts[performance] || performance
}

const getCostIndexClass = (index) => {
  if (index <= 95) return 'text-green-600'
  if (index <= 105) return 'text-orange-600'
  return 'text-red-600'
}

const getEfficiencyIndexClass = (index) => {
  if (index >= 130) return 'text-green-600'
  if (index >= 115) return 'text-orange-600'
  return 'text-red-600'
}

const getOverallRatingType = (rating) => {
  const types = {
    'excellent': 'success',
    'good': 'primary',
    'average': 'warning',
    'poor': 'danger'
  }
  return types[rating] || 'info'
}

const getOverallRatingText = (rating) => {
  const texts = {
    'excellent': '优秀',
    'good': '良好',
    'average': '一般',
    'poor': '较差'
  }
  return texts[rating] || rating
}

const getOpportunityIcon = (type) => {
  const icons = {
    'fuel': Zap,
    'technology': Settings,
    'process': Target,
    'maintenance': Truck
  }
  return icons[type] || Target
}

const getPriorityTagType = (priority) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return texts[priority] || priority
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  console.log('成本优化组件已加载')
})
</script>

<style scoped>
.cost-optimization {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.cost-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-detail {
  font-size: 13px;
  color: #94a3b8;
}

.card-trend {
  font-size: 13px;
  font-weight: 500;
}

.card-trend.positive {
  color: #10b981;
}

.card-trend.negative {
  color: #ef4444;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analysis-panel {
  padding: 24px;
}

.time-filter {
  margin-bottom: 24px;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.custom-date-range {
  margin-left: 16px;
}

.cost-composition {
  margin-bottom: 32px;
}

.composition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.composition-content {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.composition-chart {
  flex-shrink: 0;
}

.pie-chart-placeholder {
  width: 300px;
  height: 300px;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
  position: relative;
}

.composition-summary {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
  text-align: center;
}

.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.item-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-label {
  font-size: 12px;
  color: #64748b;
}

.item-value {
  font-size: 12px;
  font-weight: 600;
  color: #1a202c;
}

.item-trend {
  font-size: 11px;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.item-trend.positive {
  color: #10b981;
}

.item-trend.negative {
  color: #ef4444;
}

.composition-details {
  flex: 1;
}

.details-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.details-table {
  margin-top: 16px;
}

.cost-trends {
  margin-top: 32px;
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.trends-chart {
  background: #f8fafc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.line-chart-placeholder {
  text-align: center;
}

.trend-data {
  margin-top: 32px;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  max-width: 900px;
  margin: 0 auto;
}

.data-point {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.point-date {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.point-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.point-change {
  font-size: 12px;
  font-weight: 500;
}

.point-change.positive {
  color: #10b981;
}

.point-change.negative {
  color: #ef4444;
}

.point-change.neutral {
  color: #64748b;
}

.optimization-panel {
  padding: 24px;
}

.analysis-results {
  margin-bottom: 32px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.analysis-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-title {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.card-description {
  font-size: 13px;
  color: #64748b;
}

.optimization-suggestions {
  margin-top: 32px;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.suggestions-filters {
  display: flex;
  gap: 12px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.suggestion-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.suggestion-card.suggestion-high {
  border-left: 4px solid #ef4444;
}

.suggestion-card.suggestion-medium {
  border-left: 4px solid #f59e0b;
}

.suggestion-card.suggestion-low {
  border-left: 4px solid #3b82f6;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.suggestion-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  flex-shrink: 0;
}

.suggestion-info {
  flex: 1;
}

.suggestion-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.suggestion-category {
  font-size: 14px;
  color: #64748b;
}

.suggestion-priority {
  flex-shrink: 0;
}

.suggestion-content {
  margin-bottom: 20px;
}

.suggestion-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 16px;
}

.suggestion-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.metric-label {
  font-size: 13px;
  color: #64748b;
}

.metric-value {
  font-size: 13px;
  font-weight: 600;
  color: #1a202c;
}

.metric-value.savings {
  color: #10b981;
}

.metric-value.cost {
  color: #ef4444;
}

.metric-value.difficulty {
  color: #64748b;
}

.suggestion-impact {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.impact-header {
  margin-bottom: 12px;
}

.impact-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.impact-items {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.impact-metric {
  font-size: 12px;
  color: #64748b;
}

.impact-change {
  font-size: 12px;
  font-weight: 600;
}

.impact-change.positive {
  color: #10b981;
}

.impact-change.negative {
  color: #ef4444;
}

.suggestion-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.roi-panel {
  padding: 24px;
}

.roi-overview {
  margin-bottom: 32px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.roi-metrics {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 2px;
}

.metric-detail {
  font-size: 12px;
  color: #94a3b8;
}

.roi-projects {
  margin-bottom: 32px;
}

.projects-table {
  margin-top: 16px;
}

.roi-forecast {
  margin-top: 32px;
}

.forecast-chart {
  background: #f8fafc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
}

.chart-placeholder {
  text-align: center;
}

.forecast-data {
  margin-top: 32px;
}

.forecast-timeline {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  max-width: 900px;
  margin: 0 auto;
}

.forecast-period {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.period-label {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.period-investment {
  font-size: 12px;
  color: #ef4444;
  margin-bottom: 4px;
}

.period-savings {
  font-size: 12px;
  color: #10b981;
  margin-bottom: 4px;
}

.period-cumulative {
  font-size: 12px;
  font-weight: 600;
  color: #1a202c;
}

.comparison-panel {
  padding: 24px;
}

.benchmark-comparison {
  margin-bottom: 32px;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.benchmark-metrics {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
}

.metrics-comparison {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-info {
  flex: 1;
  min-width: 150px;
}

.metric-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.metric-unit {
  font-size: 13px;
  color: #64748b;
}

.metric-values {
  display: flex;
  gap: 24px;
  align-items: center;
}

.current-value,
.benchmark-value {
  text-align: center;
  min-width: 80px;
}

.value-label {
  font-size: 12px;
  color: #64748b;
  display: block;
  margin-bottom: 4px;
}

.value-number {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.metric-performance {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 150px;
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.performance-excellent {
  background: #dcfce7;
  color: #166534;
}

.performance-good {
  background: #dbeafe;
  color: #1d4ed8;
}

.performance-average {
  background: #fef3c7;
  color: #92400e;
}

.performance-poor {
  background: #fee2e2;
  color: #dc2626;
}

.performance-gap {
  font-size: 14px;
  font-weight: 600;
}

.competitor-analysis {
  margin-bottom: 32px;
}

.competitor-table {
  margin-top: 16px;
}

.cost-index,
.efficiency-index {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f1f5f9;
}

.improvement-opportunities {
  margin-top: 32px;
}

.opportunities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.opportunity-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.opportunity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.opportunity-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.opportunity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  flex-shrink: 0;
}

.opportunity-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.opportunity-priority {
  flex-shrink: 0;
}

.opportunity-content {
  margin-bottom: 16px;
}

.opportunity-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 12px;
}

.opportunity-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-name {
  color: #64748b;
}

.metric-value {
  color: #1a202c;
  font-weight: 500;
}

.opportunity-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .composition-content {
    flex-direction: column;
    align-items: center;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .suggestion-metrics {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metrics-comparison .comparison-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .opportunities-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .cost-optimization {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-values {
    flex-direction: column;
    gap: 8px;
  }
  
  .forecast-timeline {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .suggestion-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .suggestion-actions {
    width: 100%;
  }
}
</style>