<!--
货运跟踪和配送管理系统
版本: 1.0
创建时间: 2025-07-23

物流专员专用的货运跟踪和配送管理界面
特点：
1. 实时货运状态跟踪和更新
2. 配送路线规划和优化
3. 物流信息管理和异常处理
4. 配送时效监控和分析
5. 客户通知和服务跟进
-->

<template>
  <div class="shipment-tracking">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">货运跟踪中心</h1>
          <p class="page-subtitle">实时跟踪货运状态，优化配送路线，确保准时安全交付</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshTracking">
            <Refresh class="w-4 h-4 mr-2" />
            刷新跟踪
          </el-button>
          <el-button @click="handleRouteOptimization">
            <Route class="w-4 h-4 mr-2" />
            路线优化
          </el-button>
          <el-button @click="handleEmergencyDispatch" type="danger">
            <Warning class="w-4 h-4 mr-2" />
            紧急调度
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配送概览仪表板 -->
    <div class="tracking-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card in-transit">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Truck class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ trackingStats.inTransit }}</div>
            <div class="card-label">运输中</div>
            <div class="card-detail">预计今日到达: {{ trackingStats.todayArrivals }}</div>
          </div>
        </div>
        
        <div class="overview-card delivered">
          <div class="card-icon bg-green-100 text-green-600">
            <CircleCheck class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ trackingStats.delivered }}</div>
            <div class="card-label">已交付</div>
            <div class="card-detail">本月完成率: {{ trackingStats.deliveryRate }}%</div>
          </div>
        </div>
        
        <div class="overview-card delayed">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ trackingStats.delayed }}</div>
            <div class="card-label">延误订单</div>
            <div class="card-detail">需要关注</div>
          </div>
        </div>
        
        <div class="overview-card efficiency">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ trackingStats.onTimeRate }}%</div>
            <div class="card-label">准时率</div>
            <div class="card-trend positive">+{{ trackingStats.rateImprovement }}% 本月</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTrackingTab" @tab-change="handleTabChange">
        <!-- 实时跟踪 -->
        <el-tab-pane label="实时跟踪" name="real-time">
          <div class="real-time-panel">
            <!-- 跟踪搜索 -->
            <div class="tracking-search">
              <div class="search-bar">
                <el-input
                  v-model="trackingQuery"
                  placeholder="输入运单号、订单号或客户信息进行搜索..."
                  @keyup.enter="handleTrackingSearch"
                  size="large"
                  class="search-input"
                >
                  <template #prefix>
                    <Search class="w-5 h-5 text-gray-400" />
                  </template>
                  <template #append>
                    <el-button @click="handleTrackingSearch" type="primary">
                      搜索跟踪
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>

            <!-- 运输状态列表 -->
            <div class="shipment-list">
              <div class="list-header">
                <h3 class="text-lg font-semibold text-gray-800">实时运输状态</h3>
                <div class="list-actions">
                  <el-select v-model="statusFilter" placeholder="状态筛选" size="small">
                    <el-option label="全部状态" value="all" />
                    <el-option label="待发货" value="pending" />
                    <el-option label="运输中" value="in-transit" />
                    <el-option label="配送中" value="delivering" />
                    <el-option label="已签收" value="delivered" />
                    <el-option label="异常" value="exception" />
                  </el-select>
                  <el-button @click="handleBatchUpdate" size="small">
                    批量更新
                  </el-button>
                </div>
              </div>

              <div class="shipment-table">
                <el-table
                  :data="filteredShipments"
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="trackingNumber" label="运单号" width="150">
                    <template #default="{ row }">
                      <el-link @click="showTrackingDetails(row)" type="primary">
                        {{ row.trackingNumber }}
                      </el-link>
                    </template>
                  </el-table-column>
                  <el-table-column prop="customer" label="客户" width="140" />
                  <el-table-column prop="destination" label="目的地" width="120" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getStatusType(row.status)">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="progress" label="进度" width="120">
                    <template #default="{ row }">
                      <el-progress
                        :percentage="row.progress"
                        :color="getProgressColor(row.progress)"
                        :show-text="false"
                        size="small"
                      />
                      <span class="ml-2 text-sm">{{ row.progress }}%</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="currentLocation" label="当前位置" width="160" />
                  <el-table-column prop="estimatedArrival" label="预计到达" width="140" />
                  <el-table-column prop="carrier" label="承运商" width="120" />
                  <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="updateShipmentStatus(row)" size="small">
                        更新状态
                      </el-button>
                      <el-button @click="contactCustomer(row)" size="small" type="success">
                        联系客户
                      </el-button>
                      <el-dropdown @command="(command) => handleShipmentAction(row, command)">
                        <el-button size="small">
                          更多
                          <ArrowDown class="w-3 h-3 ml-1" />
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="details">查看详情</el-dropdown-item>
                            <el-dropdown-item command="route">查看路线</el-dropdown-item>
                            <el-dropdown-item command="history">历史记录</el-dropdown-item>
                            <el-dropdown-item command="exception">异常处理</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalShipments"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 路线管理 -->
        <el-tab-pane label="路线管理" name="routes">
          <div class="routes-panel">
            <!-- 路线规划工具 -->
            <div class="route-planning">
              <div class="planning-header">
                <h3 class="text-lg font-semibold text-gray-800">配送路线规划</h3>
                <div class="planning-actions">
                  <el-button @click="handleAutoOptimize" type="primary">
                    <Navigation class="w-4 h-4 mr-2" />
                    智能优化
                  </el-button>
                  <el-button @click="handleCreateRoute">
                    <Plus class="w-4 h-4 mr-2" />
                    新建路线
                  </el-button>
                </div>
              </div>

              <!-- 路线地图区域 -->
              <div class="route-map">
                <div class="map-container">
                  <!-- 地图组件占位 -->
                  <div class="map-placeholder">
                    <MapPin class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-500">配送路线地图</p>
                    <p class="text-sm text-gray-400">集成地图服务显示实时路线和位置</p>
                  </div>
                </div>
                
                <div class="map-controls">
                  <div class="route-filters">
                    <el-select v-model="selectedRoute" placeholder="选择路线" size="small">
                      <el-option
                        v-for="route in routes"
                        :key="route.id"
                        :label="route.name"
                        :value="route.id"
                      />
                    </el-select>
                    <el-date-picker
                      v-model="routeDate"
                      type="date"
                      placeholder="选择日期"
                      size="small"
                    />
                  </div>
                  
                  <div class="map-actions">
                    <el-button-group size="small">
                      <el-button @click="zoomIn">放大</el-button>
                      <el-button @click="zoomOut">缩小</el-button>
                      <el-button @click="resetView">重置</el-button>
                    </el-button-group>
                  </div>
                </div>
              </div>
            </div>

            <!-- 路线统计 -->
            <div class="route-statistics">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-title">总距离</span>
                    <Truck class="w-5 h-5 text-blue-500" />
                  </div>
                  <div class="stat-value">{{ routeStats.totalDistance }} km</div>
                  <div class="stat-detail">预计用时: {{ routeStats.estimatedTime }}h</div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-title">配送点数</span>
                    <MapPin class="w-5 h-5 text-green-500" />
                  </div>
                  <div class="stat-value">{{ routeStats.deliveryPoints }}</div>
                  <div class="stat-detail">平均间隔: {{ routeStats.avgInterval }} km</div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-title">预计成本</span>
                    <DollarSign class="w-5 h-5 text-orange-500" />
                  </div>
                  <div class="stat-value">¥{{ routeStats.estimatedCost }}</div>
                  <div class="stat-detail">
                    <span :class="{ 'text-green-600': routeStats.costSaving > 0, 'text-red-600': routeStats.costSaving < 0 }">
                      {{ routeStats.costSaving > 0 ? '节省' : '增加' }}: ¥{{ Math.abs(routeStats.costSaving) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 异常处理 -->
        <el-tab-pane label="异常处理" name="exceptions">
          <div class="exceptions-panel">
            <!-- 异常概览 -->
            <div class="exception-overview">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="exception-stat urgent">
                  <div class="stat-icon">
                    <Warning class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ exceptionStats.urgent }}</div>
                    <div class="stat-label">紧急异常</div>
                  </div>
                </div>
                
                <div class="exception-stat high">
                  <div class="stat-icon">
                    <Clock class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ exceptionStats.delayed }}</div>
                    <div class="stat-label">延误配送</div>
                  </div>
                </div>
                
                <div class="exception-stat medium">
                  <div class="stat-icon">
                    <AlertCircle class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ exceptionStats.damaged }}</div>
                    <div class="stat-label">货物损坏</div>
                  </div>
                </div>
                
                <div class="exception-stat low">
                  <div class="stat-icon">
                    <InfoFilled class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ exceptionStats.other }}</div>
                    <div class="stat-label">其他异常</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 异常处理列表 -->
            <div class="exception-list">
              <div class="list-header">
                <h3 class="text-lg font-semibold text-gray-800">异常处理清单</h3>
                <div class="list-filters">
                  <el-select v-model="exceptionTypeFilter" placeholder="异常类型" size="small">
                    <el-option label="全部类型" value="all" />
                    <el-option label="运输延误" value="delay" />
                    <el-option label="货物损坏" value="damage" />
                    <el-option label="地址错误" value="address" />
                    <el-option label="客户拒收" value="refuse" />
                    <el-option label="车辆故障" value="vehicle" />
                  </el-select>
                  <el-select v-model="priorityFilter" placeholder="优先级" size="small">
                    <el-option label="全部优先级" value="all" />
                    <el-option label="紧急" value="urgent" />
                    <el-option label="高" value="high" />
                    <el-option label="中" value="medium" />
                    <el-option label="低" value="low" />
                  </el-select>
                </div>
              </div>

              <div class="exception-table">
                <el-table :data="filteredExceptions" style="width: 100%">
                  <el-table-column prop="id" label="异常ID" width="120" />
                  <el-table-column prop="trackingNumber" label="运单号" width="140" />
                  <el-table-column prop="type" label="异常类型" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getExceptionTypeColor(row.type)">
                        {{ getExceptionTypeText(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="priority" label="优先级" width="80">
                    <template #default="{ row }">
                      <el-tag :type="getPriorityColor(row.priority)" size="small">
                        {{ getPriorityText(row.priority) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="异常描述" min-width="200" />
                  <el-table-column prop="reportTime" label="上报时间" width="140" />
                  <el-table-column prop="status" label="处理状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getHandleStatusColor(row.status)">
                        {{ getHandleStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                      <el-button @click="handleException(row)" size="small" type="primary">
                        处理
                      </el-button>
                      <el-button @click="escalateException(row)" size="small" type="warning">
                        上报
                      </el-button>
                      <el-button @click="viewExceptionDetails(row)" size="small">
                        详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 客户服务 -->
        <el-tab-pane label="客户服务" name="customer-service">
          <div class="customer-service-panel">
            <!-- 客户通知 -->
            <div class="customer-notifications">
              <div class="notification-header">
                <h3 class="text-lg font-semibold text-gray-800">客户通知管理</h3>
                <div class="notification-actions">
                  <el-button @click="sendBatchNotifications" type="primary">
                    <Mail class="w-4 h-4 mr-2" />
                    批量通知
                  </el-button>
                  <el-button @click="createNotificationTemplate">
                    <Document class="w-4 h-4 mr-2" />
                    通知模板
                  </el-button>
                </div>
              </div>

              <!-- 通知配置 -->
              <div class="notification-config">
                <div class="config-section">
                  <h4 class="text-md font-medium text-gray-700 mb-3">自动通知设置</h4>
                  <div class="config-options">
                    <el-checkbox v-model="notificationSettings.shipmentStart">发货通知</el-checkbox>
                    <el-checkbox v-model="notificationSettings.inTransit">运输中更新</el-checkbox>
                    <el-checkbox v-model="notificationSettings.nearDelivery">即将到达</el-checkbox>
                    <el-checkbox v-model="notificationSettings.delivered">签收确认</el-checkbox>
                    <el-checkbox v-model="notificationSettings.exception">异常提醒</el-checkbox>
                  </div>
                </div>
              </div>

              <!-- 最近通知记录 -->
              <div class="notification-history">
                <h4 class="text-md font-medium text-gray-700 mb-3">最近通知记录</h4>
                <div class="notification-list">
                  <div
                    v-for="notification in recentNotifications"
                    :key="notification.id"
                    class="notification-item"
                  >
                    <div class="notification-content">
                      <div class="notification-title">{{ notification.title }}</div>
                      <div class="notification-details">
                        <span class="customer">{{ notification.customerName }}</span>
                        <span class="tracking">{{ notification.trackingNumber }}</span>
                        <span class="time">{{ notification.sentTime }}</span>
                      </div>
                    </div>
                    <div class="notification-status">
                      <el-tag :type="notification.status === 'sent' ? 'success' : 'warning'" size="small">
                        {{ notification.status === 'sent' ? '已发送' : '待发送' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 跟踪详情对话框 -->
    <el-dialog
      v-model="trackingDetailsVisible"
      title="货运跟踪详情"
      width="70%"
      :before-close="handleCloseTrackingDetails"
    >
      <div v-if="selectedShipment" class="tracking-details">
        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="info-grid">
            <div class="info-item">
              <label>运单号:</label>
              <span>{{ selectedShipment.trackingNumber }}</span>
            </div>
            <div class="info-item">
              <label>客户:</label>
              <span>{{ selectedShipment.customer }}</span>
            </div>
            <div class="info-item">
              <label>目的地:</label>
              <span>{{ selectedShipment.destination }}</span>
            </div>
            <div class="info-item">
              <label>承运商:</label>
              <span>{{ selectedShipment.carrier }}</span>
            </div>
          </div>
        </div>

        <!-- 跟踪时间线 -->
        <div class="tracking-timeline">
          <h4 class="timeline-title">跟踪记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="record in trackingHistory"
              :key="record.id"
              :timestamp="record.time"
              :type="getTimelineType(record.status)"
            >
              <div class="timeline-content">
                <div class="timeline-status">{{ record.status }}</div>
                <div class="timeline-location">{{ record.location }}</div>
                <div class="timeline-note">{{ record.note }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="trackingDetailsVisible = false">关闭</el-button>
          <el-button @click="printTrackingDetails" type="primary">打印详情</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Refresh, Route, Warning, Truck, CircleCheck, Clock, TrendingUp,
  Search, ArrowDown, Navigation, Plus, MapPin, DollarSign, User,
  AlertCircle, InfoFilled, Mail, Document, Shield, Package, BookOpen,
  Monitor, Flag, Edit, Download, Headphones, Heart
} from 'lucide-vue-next'

// 响应式数据
const activeTrackingTab = ref('real-time')
const trackingQuery = ref('')
const statusFilter = ref('all')
const exceptionTypeFilter = ref('all')
const priorityFilter = ref('all')
const selectedRoute = ref('')
const routeDate = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalShipments = ref(0)
const selectedShipments = ref([])
const trackingDetailsVisible = ref(false)
const selectedShipment = ref(null)

// 跟踪统计数据
const trackingStats = reactive({
  inTransit: 156,
  delivered: 2847,
  delayed: 23,
  todayArrivals: 45,
  deliveryRate: 96.8,
  onTimeRate: 94.2,
  rateImprovement: 2.3
})

// 路线统计数据
const routeStats = reactive({
  totalDistance: 1847,
  estimatedTime: 28.5,
  deliveryPoints: 67,
  avgInterval: 27.5,
  estimatedCost: 15680,
  costSaving: 2340
})

// 异常统计数据
const exceptionStats = reactive({
  urgent: 5,
  delayed: 18,
  damaged: 3,
  other: 12
})

// 通知设置
const notificationSettings = reactive({
  shipmentStart: true,
  inTransit: true,
  nearDelivery: true,
  delivered: true,
  exception: true
})

// 货运列表数据
const shipments = ref([
  {
    id: 1,
    trackingNumber: 'AM2025072301',
    customer: '东方航空',
    destination: '上海浦东',
    status: 'in-transit',
    progress: 75,
    currentLocation: '南京中转站',
    estimatedArrival: '2025-07-24 14:30',
    carrier: '顺丰航运'
  },
  {
    id: 2,
    trackingNumber: 'AM2025072302',
    customer: '南方航空',
    destination: '广州白云',
    status: 'delivering',
    progress: 90,
    currentLocation: '广州配送中心',
    estimatedArrival: '2025-07-23 16:45',
    carrier: '德邦物流'
  },
  {
    id: 3,
    trackingNumber: 'AM2025072303',
    customer: '国航',
    destination: '北京首都',
    status: 'delivered',
    progress: 100,
    currentLocation: '已签收',
    estimatedArrival: '2025-07-23 10:20',
    carrier: '京东物流'
  },
  {
    id: 4,
    trackingNumber: 'AM2025072304',
    customer: '厦门航空',
    destination: '厦门高崎',
    status: 'exception',
    progress: 45,
    currentLocation: '福州中转站',
    estimatedArrival: '2025-07-24 18:00',
    carrier: '中通快运'
  },
  {
    id: 5,
    trackingNumber: 'AM2025072305',
    customer: '深圳航空',
    destination: '深圳宝安',
    status: 'pending',
    progress: 0,
    currentLocation: '待发货',
    estimatedArrival: '2025-07-25 09:30',
    carrier: '圆通速递'
  }
])

// 路线数据
const routes = ref([
  { id: 1, name: '华东配送线路' },
  { id: 2, name: '华南配送线路' },
  { id: 3, name: '华北配送线路' },
  { id: 4, name: '西南配送线路' }
])

// 异常列表数据
const exceptions = ref([
  {
    id: 'EX001',
    trackingNumber: 'AM2025072301',
    type: 'delay',
    priority: 'high',
    description: '因天气原因导致航班延误，预计延迟4小时到达',
    reportTime: '2025-07-23 12:30',
    status: 'processing'
  },
  {
    id: 'EX002',
    trackingNumber: 'AM2025072304',
    type: 'damage',
    priority: 'urgent',
    description: '运输过程中外包装出现破损，需要检查内件完整性',
    reportTime: '2025-07-23 09:15',
    status: 'pending'
  },
  {
    id: 'EX003',
    trackingNumber: 'AM2025072306',
    type: 'address',
    priority: 'medium',
    description: '收货地址信息不完整，无法正常配送',
    reportTime: '2025-07-23 08:45',
    status: 'resolved'
  }
])

// 最近通知记录
const recentNotifications = ref([
  {
    id: 1,
    title: '货物即将到达通知',
    customerName: '东方航空',
    trackingNumber: 'AM2025072301',
    sentTime: '2025-07-23 14:20',
    status: 'sent'
  },
  {
    id: 2,
    title: '货物发货通知',
    customerName: '南方航空',
    trackingNumber: 'AM2025072307',
    sentTime: '2025-07-23 11:30',
    status: 'sent'
  },
  {
    id: 3,
    title: '异常情况通知',
    customerName: '厦门航空',
    trackingNumber: 'AM2025072304',
    sentTime: '2025-07-23 09:20',
    status: 'pending'
  }
])

// 跟踪历史记录
const trackingHistory = ref([])

// 计算属性
const filteredShipments = computed(() => {
  let filtered = shipments.value
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }
  if (trackingQuery.value) {
    const query = trackingQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.trackingNumber.toLowerCase().includes(query) ||
      item.customer.toLowerCase().includes(query) ||
      item.destination.toLowerCase().includes(query)
    )
  }
  totalShipments.value = filtered.length
  return filtered.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const filteredExceptions = computed(() => {
  let filtered = exceptions.value
  if (exceptionTypeFilter.value !== 'all') {
    filtered = filtered.filter(item => item.type === exceptionTypeFilter.value)
  }
  if (priorityFilter.value !== 'all') {
    filtered = filtered.filter(item => item.priority === priorityFilter.value)
  }
  return filtered
})

// 方法定义
const handleTabChange = (tabName) => {
  activeTrackingTab.value = tabName
  if (tabName === 'real-time') {
    loadShipmentData()
  } else if (tabName === 'routes') {
    loadRouteData()
  } else if (tabName === 'exceptions') {
    loadExceptionData()
  }
}

const handleRefreshTracking = () => {
  ElMessage.success('跟踪数据已刷新')
  loadShipmentData()
}

const handleRouteOptimization = () => {
  ElMessage.info('正在进行路线优化...')
  // 路线优化逻辑
}

const handleEmergencyDispatch = () => {
  ElMessage.warning('启动紧急调度流程')
  // 紧急调度逻辑
}

const handleTrackingSearch = () => {
  currentPage.value = 1
  ElMessage.success(`搜索到 ${filteredShipments.value.length} 条记录`)
}

const handleSelectionChange = (selection) => {
  selectedShipments.value = selection
}

const handleBatchUpdate = () => {
  if (selectedShipments.value.length === 0) {
    ElMessage.warning('请先选择要更新的货运记录')
    return
  }
  ElMessage.success(`批量更新 ${selectedShipments.value.length} 条记录`)
}

const updateShipmentStatus = (shipment) => {
  ElMessage.success(`更新货运状态: ${shipment.trackingNumber}`)
}

const contactCustomer = (shipment) => {
  ElMessage.success(`联系客户: ${shipment.customer}`)
}

const handleShipmentAction = (shipment, command) => {
  switch (command) {
    case 'details':
      showTrackingDetails(shipment)
      break
    case 'route':
      ElMessage.info(`查看路线: ${shipment.trackingNumber}`)
      break
    case 'history':
      ElMessage.info(`查看历史: ${shipment.trackingNumber}`)
      break
    case 'exception':
      ElMessage.info(`异常处理: ${shipment.trackingNumber}`)
      break
  }
}

const showTrackingDetails = (shipment) => {
  selectedShipment.value = shipment
  loadTrackingHistory(shipment.trackingNumber)
  trackingDetailsVisible.value = true
}

const handleCloseTrackingDetails = () => {
  trackingDetailsVisible.value = false
  selectedShipment.value = null
  trackingHistory.value = []
}

const loadTrackingHistory = (trackingNumber) => {
  // 模拟跟踪历史数据
  trackingHistory.value = [
    {
      id: 1,
      time: '2025-07-23 15:30',
      status: '运输中',
      location: '南京中转站',
      note: '货物已到达中转站，正在分拣'
    },
    {
      id: 2,
      time: '2025-07-23 08:45',
      status: '已发货',
      location: '北京仓储中心',
      note: '货物已从仓储中心发出'
    },
    {
      id: 3,
      time: '2025-07-22 16:20',
      status: '已揽收',
      location: '北京仓储中心',
      note: '货物已入库，等待发货'
    }
  ]
}

const printTrackingDetails = () => {
  ElMessage.success('打印跟踪详情')
}

const handleAutoOptimize = () => {
  ElMessage.success('智能路线优化完成')
}

const handleCreateRoute = () => {
  ElMessage.info('创建新配送路线')
}

const zoomIn = () => {
  ElMessage.info('地图放大')
}

const zoomOut = () => {
  ElMessage.info('地图缩小')
}

const resetView = () => {
  ElMessage.info('重置地图视图')
}

const handleException = (exception) => {
  ElMessage.success(`处理异常: ${exception.id}`)
}

const escalateException = (exception) => {
  ElMessage.warning(`上报异常: ${exception.id}`)
}

const viewExceptionDetails = (exception) => {
  ElMessage.info(`查看异常详情: ${exception.id}`)
}

const sendBatchNotifications = () => {
  ElMessage.success('批量通知发送成功')
}

const createNotificationTemplate = () => {
  ElMessage.info('创建通知模板')
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 辅助方法
const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'in-transit': 'primary',
    'delivering': 'warning',
    'delivered': 'success',
    'exception': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '待发货',
    'in-transit': '运输中',
    'delivering': '配送中',
    'delivered': '已签收',
    'exception': '异常'
  }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 90) return '#67c23a'
  if (progress >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getExceptionTypeColor = (type) => {
  const colors = {
    'delay': 'warning',
    'damage': 'danger',
    'address': 'info',
    'refuse': 'warning',
    'vehicle': 'danger'
  }
  return colors[type] || 'info'
}

const getExceptionTypeText = (type) => {
  const texts = {
    'delay': '运输延误',
    'damage': '货物损坏',
    'address': '地址错误',
    'refuse': '客户拒收',
    'vehicle': '车辆故障'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    'urgent': 'danger',
    'high': 'warning',
    'medium': 'primary',
    'low': 'info'
  }
  return colors[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = {
    'urgent': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return texts[priority] || priority
}

const getHandleStatusColor = (status) => {
  const colors = {
    'pending': 'warning',
    'processing': 'primary',
    'resolved': 'success'
  }
  return colors[status] || 'info'
}

const getHandleStatusText = (status) => {
  const texts = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决'
  }
  return texts[status] || status
}

const getTimelineType = (status) => {
  const types = {
    '已揽收': 'primary',
    '已发货': 'success',
    '运输中': 'warning',
    '已签收': 'success'
  }
  return types[status] || 'primary'
}

// 数据加载方法
const loadShipmentData = () => {
  // 模拟数据加载
  console.log('加载货运数据')
}

const loadRouteData = () => {
  // 模拟路线数据加载
  console.log('加载路线数据')
}

const loadExceptionData = () => {
  // 模拟异常数据加载
  console.log('加载异常数据')
}

// 组件挂载
onMounted(() => {
  loadShipmentData()
})
</script>

<style scoped>
.shipment-tracking {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area .page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.title-area .page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.tracking-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-detail {
  font-size: 13px;
  color: #94a3b8;
}

.card-trend {
  font-size: 13px;
  font-weight: 500;
}

.card-trend.positive {
  color: #10b981;
}

.card-trend.negative {
  color: #ef4444;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tracking-search {
  margin-bottom: 24px;
}

.search-input {
  width: 100%;
}

.shipment-list {
  background: white;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 24px;
}

.list-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.shipment-table {
  padding: 0 24px;
}

.pagination-wrapper {
  padding: 24px;
  background: #fafbfc;
  border-top: 1px solid #e2e8f0;
}

.route-planning {
  margin-bottom: 24px;
}

.planning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 24px;
}

.planning-actions {
  display: flex;
  gap: 12px;
}

.route-map {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 24px 24px 24px;
}

.map-container {
  height: 400px;
  position: relative;
}

.map-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
}

.map-controls {
  padding: 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.route-filters {
  display: flex;
  gap: 12px;
}

.route-statistics {
  padding: 0 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-title {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-detail {
  font-size: 13px;
  color: #64748b;
}

.exception-overview {
  margin-bottom: 24px;
  padding: 0 24px;
}

.exception-stat {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.exception-stat.urgent .stat-icon {
  color: #ef4444;
}

.exception-stat.high .stat-icon {
  color: #f59e0b;
}

.exception-stat.medium .stat-icon {
  color: #3b82f6;
}

.exception-stat.low .stat-icon {
  color: #64748b;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

.exception-list {
  padding: 0 24px;
}

.list-filters {
  display: flex;
  gap: 12px;
}

.exception-table {
  margin-top: 16px;
}

.customer-service-panel {
  padding: 24px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.notification-actions {
  display: flex;
  gap: 12px;
}

.notification-config {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.config-options {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.notification-history {
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 20px;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.notification-details {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #64748b;
}

.tracking-details {
  max-height: 600px;
  overflow-y: auto;
}

.basic-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.info-item span {
  color: #1a202c;
}

.tracking-timeline {
  background: white;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.timeline-content {
  padding-left: 16px;
}

.timeline-status {
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.timeline-location {
  color: #3b82f6;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-note {
  color: #64748b;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shipment-tracking {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .config-options {
    flex-direction: column;
    gap: 12px;
  }
}
</style>