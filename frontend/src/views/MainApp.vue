<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 统一导航栏 -->
    <UnifiedHeader 
      :user-avatar="userAvatar"
      :unread-count="unreadCount"
      @quick-action="handleQuickAction"
      @toggle-notifications="toggleNotifications"
    />

    <!-- 主内容区 -->
    <main class="pt-20 bg-gray-50 min-h-screen">
      <div class="max-w-7xl mx-auto">
        <!-- 主要内容 -->
        <div class="p-6">
          <router-view />
        </div>
      </div>
    </main>

    <!-- 发布需求弹窗 -->
    <PublishDemandDialog v-model="showPublishDialog" />

    <!-- 通知面板 -->
    <NotificationPanel v-model="showNotificationPanel" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import UnifiedHeader from '@/components/UnifiedHeader.vue'
import PublishDemandDialog from '@/components/PublishDemandDialog.vue'
import NotificationPanel from '@/components/NotificationPanel.vue'

const router = useRouter()
const userStore = useAuthStore()

// 状态
const showPublishDialog = ref(false)
const showNotificationPanel = ref(false)
const unreadCount = ref(3)
const userAvatar = ref('')

// 计算属性
const getUserTypeLabel = (type) => {
  const labels = {
    airline_buyer: '航空公司采购员',
    platform_staff: '平台员工',
    maintenance_engineer: '维修工程师',
    logistics_specialist: '物流专员',
    admin: '系统管理员',
    // 兼容旧版本
    airline: '航空公司',
    supplier: '中航材',
    maintenance: '维修企业'
  }
  return labels[type] || type
}

// 方法
const showPublishDemand = () => {
  showPublishDialog.value = true
}

const toggleNotifications = () => {
  showNotificationPanel.value = !showNotificationPanel.value
}

const handleQuickAction = (action) => {
  if (action === 'publish-demand') {
    showPublishDemand()
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退出登录失败')
    }
  }
}

onMounted(() => {
  // 默认跳转到工作台
  if (router.currentRoute.value.path === '/app') {
    router.replace('/app/workspace')
  }
})
</script>

<style scoped>
.main-nav-item {
  @apply flex items-center px-4 py-2 text-gray-600 font-semibold text-sm rounded-lg transition-all duration-200 hover:text-primary-600 hover:bg-primary-50;
}

.main-nav-item.active {
  @apply text-primary-600 bg-primary-50;
}
</style>