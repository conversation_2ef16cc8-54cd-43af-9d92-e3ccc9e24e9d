<template>
  <div class="workspace">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex items-center justify-center h-64">
      <el-loading-indicator />
      <span class="ml-4 text-gray-600">正在加载工作台...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center p-8">
      <el-alert 
        :title="error" 
        type="error" 
        :closable="false"
        show-icon
      />
      <el-button @click="reloadWorkspace" class="mt-4">重新加载</el-button>
    </div>

    <!-- 工作台内容 -->
    <div v-else class="workspace-content">


      <!-- 专属功能区域 - 根据角色显示不同内容 -->
      <div class="role-specific-section">
        <!-- 航空公司采购员专属 -->
        <template v-if="currentUserRole === 'airline_buyer'">
          <AirlineBuyerWorkspace />
        </template>

        <!-- 平台员工专属 -->
        <template v-else-if="currentUserRole === 'platform_staff'">
          <PlatformStaffWorkspace />
        </template>

        <!-- 维修工程师专属 -->
        <template v-else-if="currentUserRole === 'maintenance_engineer'">
          <MaintenanceWorkspace />
        </template>

        <!-- 物流专员专属 -->
        <template v-else-if="currentUserRole === 'logistics_specialist'">
          <LogisticsWorkspace />
        </template>

        <!-- 供应商专属 -->
        <template v-else-if="currentUserRole === 'supplier'">
          <SupplierWorkspace />
        </template>

        <!-- 分销商专属 -->
        <template v-else-if="currentUserRole === 'distributor'">
          <DistributorWorkspace />
        </template>

        <!-- 系统管理员专属 -->
        <template v-else-if="currentUserRole === 'admin' || userStore.user?.permission_level >= 3">
          <AdminWorkspace />
        </template>

        <!-- 未知角色的默认处理 -->
        <template v-else>
          <div class="unknown-role-workspace modern-card p-8 text-center">
            <div class="mb-4">
              <Warning class="w-16 h-16 text-yellow-500 mx-auto" />
            </div>
            <h2 class="text-xl font-semibold text-gray-800 mb-2">工作台正在准备中</h2>
            <p class="text-gray-600 mb-4">
              您的角色 "{{ currentUserRole }}" 的专属工作台正在开发中
            </p>
            <el-button type="primary" @click="reloadWorkspace">刷新页面</el-button>
          </div>
        </template>
      </div>

      <!-- AOG紧急响应 - 仅对有权限的用户显示 -->
      <PermissionGate permission="aog_request">
        <div class="aog-section">
          <div class="modern-card p-6 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <Warning class="w-4 h-4 text-red-600" />
              </div>
              <h3 class="text-lg font-semibold text-red-800">AOG紧急响应</h3>
            </div>
            <p class="text-red-700 text-sm mb-4">
              飞机停场紧急情况？我们提供24/7快速响应服务
            </p>
            <el-button type="danger" size="small" class="w-full" @click="handleAOGResponse">
              启动AOG响应
            </el-button>
          </div>
        </div>
      </PermissionGate>
    </div>

    <!-- 弹窗 -->
    <PublishDemandDialog v-model="showPublishDialog" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import { 
  Plus, ShoppingCart, Box, Folder, Tools, DataAnalysis,
  Document, QuestionFilled, Phone, Warning
} from '@element-plus/icons-vue'

// 组件导入
import PermissionGate from '@/components/PermissionGate.vue'
import RoleDashboard from '@/components/RoleDashboard.vue'
import PublishDemandDialog from '@/components/PublishDemandDialog.vue'

// 角色专属工作台组件
import AirlineBuyerWorkspace from '@/views/workspaces/AirlineBuyerWorkspace.vue'
import PlatformStaffWorkspace from '@/views/workspaces/PlatformStaffWorkspace.vue'
import MaintenanceWorkspace from '@/views/workspaces/MaintenanceWorkspace.vue'
import LogisticsWorkspace from '@/views/workspaces/LogisticsWorkspace.vue'
import AdminWorkspace from '@/views/workspaces/AdminWorkspace.vue'
import SupplierWorkspace from '@/views/workspaces/SupplierWorkspace.vue'
import DistributorWorkspace from '@/views/workspaces/DistributorWorkspace.vue'

const router = useRouter()
const userStore = useAuthStore()

// 使用角色工作台配置
const {
  workspaceConfig,
  quickActions,
  dashboardWidgets,
  theme,
  isLoading,
  error,
  currentUserRole,
  roleName,
  roleDescription,
  getWorkspaceStats,
  reloadWorkspace,
  executeQuickAction
} = useRoleWorkspace()

// 本地状态
const showPublishDialog = ref(false)
const dashboardStats = ref([])
const recentActivities = ref([])

// 计算属性
const welcomeStyle = computed(() => ({
  background: `linear-gradient(135deg, ${theme.value.gradientFrom || '#1890ff'}, ${theme.value.gradientTo || '#722ed1'})`,
  borderRadius: '16px',
  padding: '32px',
  marginBottom: '32px',
  color: 'white'
}))

// 方法
const handleQuickAction = (action) => {
  // 执行快速操作
  
  // 执行权限检查和操作
  const result = executeQuickAction(action.id)
  if (!result) return

  // 根据操作类型执行相应的逻辑
  switch (action.action) {
    case 'openPublishDemandDialog':
      showPublishDialog.value = true
      break
    case 'navigateToMarketplace':
      router.push('/app/marketplace')
      break
    case 'navigateToInventory':
      router.push('/app/inventory')
      break
    case 'navigateToOrders':
      router.push('/app/orders')
      break
    case 'openPublishSharedDialog':
      // TODO: 打开发布共享件对话框
      ElMessage.info('发布共享件功能开发中...')
      break
    case 'openAOGDialog':
      handleAOGResponse()
      break
    case 'navigateToOrderReview':
      router.push('/app/orders?filter=pending')
      break
    case 'navigateToSharedReview':
      router.push('/app/shared-materials/review')
      break
    case 'navigateToInventoryManagement':
      router.push('/app/inventory?mode=management')
      break
    case 'openCustomerServicePanel':
      ElMessage.info('客户服务面板开发中...')
      break
    case 'navigateToAnalytics':
      router.push('/app/analytics')
      break
    case 'navigateToSystemConfig':
      router.push('/app/admin/config')
      break
    case 'openCreateWorkOrderDialog':
      ElMessage.info('创建工单功能开发中...')
      break
    case 'openProgressUpdateDialog':
      ElMessage.info('进度更新功能开发中...')
      break
    case 'openTechnicalSupportPanel':
      ElMessage.info('技术支持面板开发中...')
      break
    case 'openSparePartsRequest':
      ElMessage.info('备件申请功能开发中...')
      break
    case 'openShipmentTracking':
      ElMessage.info('货运跟踪功能开发中...')
      break
    case 'openDeliveryUpdate':
      ElMessage.info('配送更新功能开发中...')
      break
    case 'openPickupSchedule':
      ElMessage.info('取件安排功能开发中...')
      break
    case 'generateDeliveryReport':
      ElMessage.info('配送报告生成中...')
      break
    default:
      ElMessage.info(`功能 ${action.title} 开发中...`)
  }
}

const handleAOGResponse = () => {
  ElMessage({
    type: 'warning',
    message: '正在启动AOG紧急响应流程...',
    duration: 2000
  })
  
  setTimeout(() => {
    ElMessage({
      type: 'success', 
      message: 'AOG响应流程已启动，我们将在15分钟内与您联系',
      duration: 3000
    })
  }, 2000)
}

// 加载工作台统计数据
const loadDashboardStats = async () => {
  try {
    const stats = await getWorkspaceStats('dashboardStats')
    
    // 根据角色显示不同的统计数据
    const statConfigs = {
      airline_buyer: [
        { key: 'demands', label: '活跃需求', value: stats?.demandStats?.active || 0 },
        { key: 'orders', label: '待处理订单', value: stats?.orderStats?.processing || 0 },
        { key: 'shared', label: '共享收益', value: `¥${stats?.sharedStats?.revenue || 0}` }
      ],
      platform_staff: [
        { key: 'users', label: '活跃用户', value: stats?.activeUsers?.today || 0 },
        { key: 'revenue', label: '日收益', value: `¥${stats?.revenueStats?.today || 0}` },
        { key: 'transactions', label: '交易数', value: stats?.platformOrderStats?.total || 0 }
      ],
      maintenance_engineer: [
        { key: 'workorders', label: '工单数', value: stats?.activeWorkOrders?.total || 0 },
        { key: 'completion', label: '完成率', value: `${stats?.completedToday?.efficiency || 0}%` },
        { key: 'urgent', label: '紧急任务', value: stats?.urgentTasks?.aog || 0 }
      ],
      logistics_specialist: [
        { key: 'shipments', label: '运输中', value: stats?.activeShipments?.total || 0 },
        { key: 'delivered', label: '今日送达', value: stats?.deliveredToday?.count || 0 },
        { key: 'ontime', label: '准时率', value: `${stats?.deliveryRate?.today || 0}%` }
      ],
      admin: [
        { key: 'users', label: '总用户', value: stats?.totalUsers?.count || 0 },
        { key: 'health', label: '系统健康', value: `${stats?.systemHealth?.status || 'N/A'}` },
        { key: 'transactions', label: '日交易', value: stats?.dailyTransactions?.count || 0 }
      ]
    }
    
    dashboardStats.value = statConfigs[currentUserRole.value] || []
  } catch (error) {
    // 加载统计数据失败
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    const activities = await getWorkspaceStats('recentActivities')
    
    if (activities && Array.isArray(activities)) {
      recentActivities.value = activities.map(activity => ({
        ...activity,
        icon: getMonitorIcon(activity.type),
        color: getMonitorColor(activity.type)
      }))
    } else {
      // 默认活动数据
      recentActivities.value = [
        {
          id: 1,
          title: '工作台加载完成',
          description: `${roleName.value}工作台初始化成功`,
          time: '刚刚',
          icon: Plus,
          color: 'bg-primary-500'
        }
      ]
    }
  } catch (error) {
    // 加载最近活动失败
  }
}

// 辅助函数
const getMonitorIcon = (type) => {
  const iconMap = {
    demand: Plus,
    order: Box,
    shared: ShoppingCart,
    maintenance: Tools,
    logistics: Folder,
    system: DataAnalysis
  }
  return iconMap[type] || Document
}

const getMonitorColor = (type) => {
  const colorMap = {
    demand: 'bg-primary-500',
    order: 'bg-warning-500',
    shared: 'bg-success-500',
    maintenance: 'bg-purple-500',
    logistics: 'bg-blue-500',
    system: 'bg-gray-500'
  }
  return colorMap[type] || 'bg-gray-500'
}

onMounted(async () => {
  // 工作台初始化
  
  // 等待角色配置加载完成
  await new Promise(resolve => {
    const checkConfig = () => {
      if (!isLoading.value) {
        resolve()
      } else {
        setTimeout(checkConfig, 100)
      }
    }
    checkConfig()
  })
  
  // 加载统计数据和活动
  await Promise.all([
    loadDashboardStats(),
    loadRecentActivities()
  ])
  
  // 工作台数据加载完成
})
</script>

<style scoped>
.workspace {
  @apply min-h-screen;
}
</style>