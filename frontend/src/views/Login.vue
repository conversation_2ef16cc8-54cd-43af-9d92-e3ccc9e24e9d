<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 flex items-center justify-center p-6 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 装饰圆圈 -->
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-10 rounded-full"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>
      <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>

      <!-- 网格背景 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="relative w-full max-w-md z-10">
      <!-- 返回首页按钮 -->
      <button @click="$router.push('/')" class="absolute -top-16 left-0 flex items-center space-x-2 text-white hover:text-blue-200 transition-colors">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        <span>返回首页</span>
      </button>

      <div class="bg-white rounded-3xl shadow-2xl p-8">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
            </svg>
          </div>
          <h2 class="title-font text-2xl font-bold text-gray-800 mb-2">欢迎登录</h2>
          <p class="text-gray-600">中航材共享保障平台</p>
        </div>

        <!-- 登录表单 -->
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" label-width="0" size="large">
          <!-- 用户名 -->
          <el-form-item prop="username" class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password" class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <!-- 登录按钮 -->
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              style="width: 100%; height: 48px;"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 注册链接 -->
        <div class="text-center mt-6">
          <p class="text-sm text-gray-600">
            还没有账户？
            <router-link to="/register" class="text-primary-600 hover:text-primary-800 font-semibold">立即注册</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const loginFormRef = ref(null)

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true
    
    // 强制清理所有可能的缓存数据
    localStorage.clear()
    sessionStorage.clear()
    
    // 只发送后端需要的基本字段
    const loginData = {
      username: loginForm.username,
      password: loginForm.password
    }
    
    const result = await authStore.login(loginData)
    
    if (result.success) {
      ElMessage.success('登录成功')
      
      // 使用window.location进行强制跳转
      setTimeout(() => {
        window.location.href = '/app/workspace'
      }, 500)
    } else {
      ElMessage.error(result.error)
    }
  } catch (error) {
    console.error('Login failed:', error)
    ElMessage.error('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 网格背景图案 */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 登录卡片动画 */
.relative.w-full.max-w-md.z-10 {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 装饰圆圈动画 */
.absolute.rounded-full {
  animation: float 6s ease-in-out infinite;
}

.absolute.rounded-full:nth-child(2) {
  animation-delay: -2s;
}

.absolute.rounded-full:nth-child(3) {
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 主色调变量 */
.from-primary-600 {
  --tw-gradient-from: #3B82F6;
}

.to-primary-700 {
  --tw-gradient-to: #1D4ED8;
}

.border-primary-500 {
  border-color: #3B82F6;
}

.bg-primary-50 {
  background-color: rgba(59, 130, 246, 0.05);
}

.hover\:border-primary-300:hover {
  border-color: #93C5FD;
}

.text-primary-600 {
  color: #3B82F6;
}

.hover\:text-primary-800:hover {
  color: #1E40AF;
}
</style>