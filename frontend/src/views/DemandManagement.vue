<template>
  <div class="demand-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">需求管理</h1>
        <p class="text-gray-600 mt-2">管理需求发布、匹配和响应</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showPublishDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          发布需求
        </el-button>
        <el-button @click="refreshMatches">
          <Refresh class="w-4 h-4 mr-2" />
          刷新匹配
        </el-button>
      </div>
    </div>

    <!-- 需求统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Document class="w-6 h-6 text-primary-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.totalDemands }}</div>
        <div class="text-gray-600">总需求</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Clock class="w-6 h-6 text-warning-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.activeDemands }}</div>
        <div class="text-gray-600">进行中</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Aim class="w-6 h-6 text-success-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.matchedDemands }}</div>
        <div class="text-gray-600">已匹配</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <DataLine class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.matchRate }}%</div>
        <div class="text-gray-600">匹配率</div>
      </div>
    </div>

    <!-- 需求列表 -->
    <div class="modern-card">
      <div class="p-6 border-b border-gray-200">
        <div class="flex flex-col lg:flex-row gap-4">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索需求标题、零件号..."
            clearable
            style="max-width: 300px"
          >
            <template #prefix>
              <Search class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>

          <el-select v-model="searchForm.status" placeholder="需求状态" style="width: 150px">
            <el-option label="全部状态" value="" />
            <el-option label="已发布" value="published" />
            <el-option label="匹配中" value="matching" />
            <el-option label="已匹配" value="matched" />
            <el-option label="已完成" value="completed" />
          </el-select>

          <el-select v-model="searchForm.type" placeholder="需求类型" style="width: 150px">
            <el-option label="全部类型" value="" />
            <el-option label="周转件" value="turnaround" />
            <el-option label="消耗件" value="consumable" />
            <el-option label="维修" value="maintenance" />
            <el-option label="AOG" value="aog" />
          </el-select>

          <el-button @click="handleSearch">搜索</el-button>
        </div>
      </div>

      <el-table :data="demands" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="需求ID" width="120" />
        
        <el-table-column prop="title" label="需求标题" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" @click="viewDemandDetail(row.id)">
              {{ row.title }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="match_count" label="匹配数" width="80" align="center">
          <template #default="{ row }">
            <span class="font-medium" :class="row.match_count > 0 ? 'text-green-600' : 'text-gray-500'">
              {{ row.match_count }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="发布时间" width="120">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">{{ formatDate(row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex space-x-2">
              <el-button link type="primary" size="small" @click="viewMatches(row.id)">
                查看匹配
              </el-button>
              <el-button 
                v-if="row.match_count > 0" 
                link 
                type="success" 
                size="small" 
                @click="viewResponses(row.id)"
              >
                响应({{ row.response_count }})
              </el-button>
              <el-dropdown>
                <el-button link size="small">
                  更多
                  <ArrowDown class="w-3 h-3 ml-1" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="triggerMatch(row.id)">
                      重新匹配
                    </el-dropdown-item>
                    <el-dropdown-item @click="editDemand(row.id)">
                      编辑需求
                    </el-dropdown-item>
                    <el-dropdown-item @click="viewHistory(row.id)">
                      查看历史
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 发布需求弹窗 -->
    <PublishDemandDialog v-model="showPublishDialog" @success="handleDemandPublished" />
    
    <!-- 编辑需求弹窗 -->
    <EditDemandDialog 
      v-model="showEditDialog" 
      :demand-data="selectedDemandData" 
      @success="handleDemandUpdated" 
    />
    
    <!-- 需求匹配弹窗 -->
    <DemandMatchDialog v-model="showMatchDialog" :demand-id="selectedDemandId" />
    
    <!-- 需求响应弹窗 -->
    <DemandResponseDialog v-model="showResponseDialog" :demand-id="selectedDemandId" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { demandsApi } from '@/api/demands-enhanced'
import { ElMessage } from 'element-plus'
import { 
  Plus, Refresh, Document, Clock, Aim, DataLine, 
  Search, ArrowDown 
} from '@element-plus/icons-vue'
import PublishDemandDialog from '@/components/PublishDemandDialog.vue'
import EditDemandDialog from '@/components/EditDemandDialog.vue'
import DemandMatchDialog from '@/components/DemandMatchDialog.vue'
import DemandResponseDialog from '@/components/DemandResponseDialog.vue'

// 状态
const loading = ref(false)
const showPublishDialog = ref(false)
const showEditDialog = ref(false)
const showMatchDialog = ref(false)
const showResponseDialog = ref(false)
const selectedDemandId = ref(null)
const selectedDemandData = ref({})

// 搜索表单
const searchForm = reactive({
  search: '',
  status: '',
  type: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const statistics = ref({
  totalDemands: 125,
  activeDemands: 45,
  matchedDemands: 67,
  matchRate: 78
})

// 需求列表
const demands = ref([])

// 获取需求数据
const loadDemands = async () => {
  try {
    const response = await demandsApi.getDemands()
    if (response.success) {
      demands.value = response.body.demands || []
      pagination.total = response.body.total || 0
      console.log('加载需求列表成功:', {
        count: demands.value.length,
        total: pagination.total
      })
    }
  } catch (error) {
    console.error('获取需求列表失败:', error)
    ElMessage.error('获取需求列表失败')
  }
}

// 获取统计数据
const loadStatistics = async () => {
  try {
    const response = await demandsApi.getDemandStatistics()
    if (response.success) {
      Object.assign(statistics, response.body)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 模拟数据 (作为备用)
const initializeMockData = () => {
  if (demands.value.length === 0) {
    demands.value = [
      {
        id: 'DEM-2025-001',
        title: 'AOG紧急-发动机高压压气机叶片-紧急',
        type: 'aog',
        priority: 'urgent',
        status: 'matched',
        match_count: 3,
        response_count: 2,
        created_at: '2025-01-13'
      },
      {
        id: 'DEM-2025-002', 
        title: '周转件需求-主起落架减震支柱-A320',
        type: 'turnaround',
        priority: 'high',
        status: 'matching',
        match_count: 5,
        response_count: 1,
        created_at: '2025-01-12'
      },
      {
        id: 'DEM-2025-003',
        title: '消耗件需求-轮胎组件',
        type: 'consumable',
        priority: 'normal',
        status: 'published',
        match_count: 0,
        response_count: 0,
        created_at: '2025-01-11'
      }
    ]
    pagination.total = demands.value.length
  }
}

// 方法
const handleSearch = async () => {
  try {
    loading.value = true
    console.log('搜索需求:', searchForm)
    ElMessage.success('搜索完成')
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const refreshMatches = async () => {
  try {
    ElMessage.info('正在刷新匹配...')
    // 实际项目中调用API刷新匹配
    setTimeout(() => {
      ElMessage.success('匹配已刷新')
    }, 2000)
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const viewDemandDetail = (demandId) => {
  ElMessage.info(`查看需求详情: ${demandId}`)
}

const viewMatches = (demandId) => {
  selectedDemandId.value = demandId
  showMatchDialog.value = true
}

const viewResponses = (demandId) => {
  selectedDemandId.value = demandId
  showResponseDialog.value = true
}

const triggerMatch = async (demandId) => {
  try {
    console.log('触发需求重新匹配:', demandId)
    const response = await demandsApi.rematchDemand(demandId)
    
    if (response.success) {
      ElMessage.success(response.body.message || '重新匹配已完成')
      // 重新加载需求列表以更新状态
      await loadDemands()
    } else {
      ElMessage.error(response.message || '重新匹配失败')
    }
  } catch (error) {
    console.error('重新匹配失败:', error)
    ElMessage.error('重新匹配失败')
  }
}

const editDemand = async (demandId) => {
  try {
    // 获取需求详情
    const response = await demandsApi.getDemandDetail(demandId)
    if (response.success) {
      selectedDemandData.value = response.body
      showEditDialog.value = true
    } else {
      ElMessage.error('获取需求详情失败')
    }
  } catch (error) {
    console.error('获取需求详情失败:', error)
    ElMessage.error('获取需求详情失败')
  }
}

const viewHistory = (demandId) => {
  ElMessage.info(`查看历史: ${demandId}`)
}

const handleDemandPublished = async (demandData) => {
  console.log('需求发布成功:', demandData)
  // 重新加载需求列表和统计数据
  await loadDemands()
  await loadStatistics()
  // 关闭发布对话框
  showPublishDialog.value = false
}

const handleDemandUpdated = async (updatedDemand) => {
  console.log('需求更新成功:', updatedDemand)
  // 重新加载需求列表
  await loadDemands()
  // 关闭编辑对话框
  showEditDialog.value = false
  // 显示成功消息
  ElMessage.success('需求更新成功')
}

const handleSizeChange = (size) => {
  pagination.size = size
}

const handleCurrentChange = (page) => {
  pagination.page = page
}

// 辅助函数
const getTypeColor = (type) => {
  const colors = {
    turnaround: 'primary',
    consumable: 'success',
    maintenance: 'warning',
    aog: 'danger'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    turnaround: '周转件',
    consumable: '消耗件',
    maintenance: '维修',
    aog: 'AOG'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getStatusColor = (status) => {
  const colors = {
    published: 'warning',
    matching: 'primary',
    matched: 'success',
    completed: 'success'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    published: '已发布',
    matching: '匹配中',
    matched: '已匹配',
    completed: '已完成'
  }
  return texts[status] || status
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(async () => {
  await loadDemands()
  await loadStatistics()
  // 如果API没有数据，则加载模拟数据
  if (demands.value.length === 0) {
    initializeMockData()
  }
})
</script>