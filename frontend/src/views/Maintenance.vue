<template>
  <div class="maintenance-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">维修管理</h1>
      <p class="page-subtitle">维修工单管理、进度跟踪和资源调度</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Tools /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.total_work_orders }}</div>
          <div class="stat-label">总工单数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon processing">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.in_progress }}</div>
          <div class="stat-label">进行中</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon warning">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.waiting_parts }}</div>
          <div class="stat-label">待料中</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.completed_today }}</div>
          <div class="stat-label">今日完成</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon info">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.avg_completion_time }}h</div>
          <div class="stat-label">平均完成时间</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon efficiency">
          <el-icon><DataLine /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.efficiency_rate }}%</div>
          <div class="stat-label">效率评分</div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" class="maintenance-tabs">
        <el-tab-pane label="工单管理" name="workOrders">
          <div class="work-orders-section">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <div class="filter-row">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="搜索工单号、飞机号或故障描述"
                  clearable
                  class="search-input"
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                
                <el-select v-model="searchForm.status" placeholder="工单状态" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="新建" value="new" />
                  <el-option label="进行中" value="in_progress" />
                  <el-option label="待料中" value="waiting_parts" />
                  <el-option label="已完成" value="completed" />
                </el-select>
                
                <el-select v-model="searchForm.priority" placeholder="优先级" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="AOG" value="aog" />
                  <el-option label="高优先级" value="high" />
                  <el-option label="普通" value="normal" />
                  <el-option label="低优先级" value="low" />
                </el-select>
                
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                
                <el-button type="success" @click="showCreateDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建工单
                </el-button>
              </div>
            </div>

            <!-- 工单列表 -->
            <div class="work-orders-table">
              <el-table :data="workOrders" v-loading="loading" stripe>
                <el-table-column prop="work_order_number" label="工单号" width="140" />
                <el-table-column prop="aircraft_tail" label="飞机号" width="100" />
                <el-table-column prop="aircraft_type" label="机型" width="80" />
                <el-table-column prop="fault_title" label="故障描述" min-width="200" />
                <el-table-column prop="priority" label="优先级" width="100">
                  <template #default="scope">
                    <el-tag 
                      :type="getPriorityTagType(scope.row.priority)"
                      :effect="scope.row.priority === 'aog' ? 'dark' : 'plain'"
                    >
                      {{ getPriorityLabel(scope.row.priority) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="120">
                  <template #default="scope">
                    <el-tag :type="getStatusTagType(scope.row.status)">
                      {{ getStatusLabel(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="assigned_technician" label="技师" width="120">
                  <template #default="scope">
                    {{ scope.row.assigned_technician?.name || '未分配' }}
                  </template>
                </el-table-column>
                <el-table-column prop="progress" label="进度" width="120">
                  <template #default="scope">
                    <el-progress 
                      :percentage="scope.row.progress" 
                      :color="getProgressColor(scope.row.progress)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="estimated_completion" label="预计完成" width="140">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.estimated_completion) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="scope">
                    <el-button link type="primary" @click="viewWorkOrder(scope.row)">
                      查看
                    </el-button>
                    <el-button link type="warning" @click="editWorkOrder(scope.row)">
                      编辑
                    </el-button>
                    <el-button link type="success" @click="updateProgress(scope.row)">
                      更新进度
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="技师管理" name="technicians">
          <div class="technicians-section">
            <div class="technicians-grid">
              <div 
                v-for="technician in technicians" 
                :key="technician.id"
                class="technician-card"
              >
                <div class="technician-avatar">
                  <el-avatar :size="60">{{ technician.name.charAt(0) }}</el-avatar>
                </div>
                <div class="technician-info">
                  <h3 class="technician-name">{{ technician.name }}</h3>
                  <p class="technician-level">{{ technician.level }}</p>
                  <p class="technician-specialty">{{ technician.specialty }}</p>
                  <div class="technician-status">
                    <el-tag 
                      :type="technician.availability === 'available' ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ technician.availability === 'available' ? '空闲' : '忙碌' }}
                    </el-tag>
                    <span class="workload">工单数: {{ technician.current_workload }}</span>
                  </div>
                  <div class="technician-certifications">
                    <el-tag 
                      v-for="cert in technician.certifications" 
                      :key="cert"
                      size="small"
                      type="info"
                    >
                      {{ cert }}
                    </el-tag>
                  </div>
                </div>
                <div class="technician-actions">
                  <el-button size="small" @click="assignToTechnician(technician)">
                    分配工单
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="维修计划" name="schedule">
          <div class="schedule-section">
            <div class="schedule-header">
              <h3>维修计划</h3>
              <div class="header-actions">
                <el-button type="success" @click="showResourceDialog = true">
                  <el-icon><Calendar /></el-icon>
                  资源调度
                </el-button>
                <el-button type="primary" @click="createSchedule">
                  <el-icon><Plus /></el-icon>
                  新建计划
                </el-button>
              </div>
            </div>
            
            <div class="schedule-table">
              <el-table :data="maintenanceSchedule" stripe>
                <el-table-column prop="aircraft_tail" label="飞机号" width="100" />
                <el-table-column prop="aircraft_type" label="机型" width="80" />
                <el-table-column prop="maintenance_type" label="维修类型" width="100" />
                <el-table-column prop="scheduled_date" label="计划日期" width="150">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.scheduled_date) }}
                  </template>
                </el-table-column>
                <el-table-column prop="estimated_duration" label="预计时长" width="120">
                  <template #default="scope">
                    {{ scope.row.estimated_duration }}小时
                  </template>
                </el-table-column>
                <el-table-column prop="assigned_team" label="分配团队" width="120" />
                <el-table-column prop="bay" label="机位" width="100" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getScheduleStatusTagType(scope.row.status)">
                      {{ getScheduleStatusLabel(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button link type="primary" @click="viewSchedule(scope.row)">
                      查看
                    </el-button>
                    <el-button link type="warning" @click="editSchedule(scope.row)">
                      编辑
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="统计分析" name="analytics">
          <div class="analytics-section">
            <div class="analytics-grid">
              <div class="analytics-card">
                <h3>维修效率趋势</h3>
                <div class="chart-container">
                  <div class="chart-placeholder">
                    📊 图表展示区域
                  </div>
                </div>
              </div>
              
              <div class="analytics-card">
                <h3>成本分析</h3>
                <div class="cost-breakdown">
                  <div class="cost-item">
                    <span class="cost-label">人工成本</span>
                    <span class="cost-value">¥{{ formatCurrency(costAnalysis.labor_cost) }}</span>
                  </div>
                  <div class="cost-item">
                    <span class="cost-label">零件成本</span>
                    <span class="cost-value">¥{{ formatCurrency(costAnalysis.parts_cost) }}</span>
                  </div>
                  <div class="cost-item">
                    <span class="cost-label">管理成本</span>
                    <span class="cost-value">¥{{ formatCurrency(costAnalysis.overhead_cost) }}</span>
                  </div>
                  <div class="cost-item total">
                    <span class="cost-label">总成本</span>
                    <span class="cost-value">¥{{ formatCurrency(costAnalysis.total_cost) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 工单详情对话框 -->
    <WorkOrderDetailDialog 
      v-model="showDetailDialog" 
      :work-order="selectedWorkOrder"
      @refresh="loadWorkOrders"
      @labor-management="handleLaborManagement"
    />

    <!-- 创建工单对话框 -->
    <CreateWorkOrderDialog 
      v-model="showCreateDialog"
      @refresh="loadWorkOrders"
    />

    <!-- 进度更新对话框 -->
    <ProgressUpdateDialog 
      v-model="showProgressDialog"
      :work-order="selectedWorkOrder"
      @refresh="loadWorkOrders"
    />

    <!-- 工时管理对话框 -->
    <LaborTimeDialog 
      v-model="showLaborDialog"
      :work-order="selectedWorkOrder"
      @refresh="loadWorkOrders"
    />

    <!-- 维修计划对话框 -->
    <MaintenanceScheduleDialog 
      v-model="showScheduleDialog"
      :schedule-data="selectedSchedule"
      :is-edit="isEditSchedule"
      @refresh="loadMaintenanceSchedule"
    />

    <!-- 计划详情对话框 -->
    <ScheduleDetailDialog 
      v-model="showScheduleDetailDialog"
      :schedule="selectedSchedule"
      @refresh="loadMaintenanceSchedule"
      @edit="handleScheduleEdit"
    />

    <!-- 资源调度对话框 -->
    <ResourceSchedulingDialog 
      v-model="showResourceDialog"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { 
  Tools, Loading, Clock, Check, Timer, DataLine, 
  Search, Plus, Calendar
} from '@element-plus/icons-vue'
import { 
  getWorkOrders, 
  getWorkOrderDetails, 
  getTechnicians, 
  getMaintenanceSchedule,
  getMaintenanceStatistics
} from '@/api/maintenance'
import WorkOrderDetailDialog from '@/components/WorkOrderDetailDialog.vue'
import CreateWorkOrderDialog from '@/components/CreateWorkOrderDialog.vue'
import ProgressUpdateDialog from '@/components/ProgressUpdateDialog.vue'
import LaborTimeDialog from '@/components/LaborTimeDialog.vue'
import MaintenanceScheduleDialog from '@/components/MaintenanceScheduleDialog.vue'
import ScheduleDetailDialog from '@/components/ScheduleDetailDialog.vue'
import ResourceSchedulingDialog from '@/components/ResourceSchedulingDialog.vue'

// 响应式数据
const activeTab = ref('workOrders')
const loading = ref(false)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showProgressDialog = ref(false)
const showLaborDialog = ref(false)
const showScheduleDialog = ref(false)
const showScheduleDetailDialog = ref(false)
const showResourceDialog = ref(false)
const selectedWorkOrder = ref(null)
const selectedSchedule = ref(null)
const isEditSchedule = ref(false)

// 统计数据
const statistics = reactive({
  total_work_orders: 0,
  in_progress: 0,
  waiting_parts: 0,
  completed_today: 0,
  avg_completion_time: 0,
  efficiency_rate: 0
})

// 工单数据
const workOrders = ref([])
const technicians = ref([])
const maintenanceSchedule = ref([])
const costAnalysis = reactive({
  labor_cost: 0,
  parts_cost: 0,
  overhead_cost: 0,
  total_cost: 0
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  priority: ''
})

// 生命周期钩子
onMounted(() => {
  loadWorkOrders()
  loadTechnicians()
  loadMaintenanceSchedule()
  loadStatistics()
})

// 加载工单列表
const loadWorkOrders = async () => {
  try {
    loading.value = true
    const response = await getWorkOrders()
    if (response.success) {
      workOrders.value = response.body.work_orders
      Object.assign(statistics, response.body.statistics)
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载技师列表
const loadTechnicians = async () => {
  try {
    const response = await getTechnicians()
    if (response.success) {
      technicians.value = response.body.technicians
    }
  } catch (error) {
    console.error('加载技师列表失败:', error)
  }
}

// 加载维修计划
const loadMaintenanceSchedule = async () => {
  try {
    const response = await getMaintenanceSchedule()
    if (response.success) {
      maintenanceSchedule.value = response.body.scheduled_maintenance
    }
  } catch (error) {
    console.error('加载维修计划失败:', error)
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getMaintenanceStatistics()
    if (response.success) {
      Object.assign(costAnalysis, response.body.cost_analysis)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  loadWorkOrders()
}

// 查看工单详情
const viewWorkOrder = async (workOrder) => {
  selectedWorkOrder.value = workOrder
  showDetailDialog.value = true
}

// 编辑工单
const editWorkOrder = (workOrder) => {
  // 实现编辑逻辑
  console.log('编辑工单:', workOrder)
}

// 更新进度
const updateProgress = (workOrder) => {
  selectedWorkOrder.value = workOrder
  showProgressDialog.value = true
}

// 分配技师
const assignToTechnician = (technician) => {
  // 实现分配逻辑
  console.log('分配技师:', technician)
}

// 创建计划
const createSchedule = () => {
  selectedSchedule.value = null
  isEditSchedule.value = false
  showScheduleDialog.value = true
}

// 查看计划
const viewSchedule = (schedule) => {
  selectedSchedule.value = schedule
  showScheduleDetailDialog.value = true
}

// 编辑计划
const editSchedule = (schedule) => {
  selectedSchedule.value = schedule
  isEditSchedule.value = true
  showScheduleDialog.value = true
}

// 工时管理
const handleLaborManagement = (workOrder) => {
  selectedWorkOrder.value = workOrder
  showLaborDialog.value = true
}

// 计划详情编辑
const handleScheduleEdit = (schedule) => {
  selectedSchedule.value = schedule
  isEditSchedule.value = true
  showScheduleDetailDialog.value = false
  showScheduleDialog.value = true
}

// 工具函数
const getPriorityTagType = (priority) => {
  const types = {
    aog: 'danger',
    high: 'warning',
    normal: 'info',
    low: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityLabel = (priority) => {
  const labels = {
    aog: 'AOG',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return labels[priority] || priority
}

const getStatusTagType = (status) => {
  const types = {
    new: 'info',
    in_progress: 'warning',
    waiting_parts: 'danger',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    new: '新建',
    in_progress: '进行中',
    waiting_parts: '待料中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getScheduleStatusTagType = (status) => {
  const types = {
    scheduled: 'info',
    preparing: 'warning',
    in_progress: 'primary',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getScheduleStatusLabel = (status) => {
  const labels = {
    scheduled: '已计划',
    preparing: '准备中',
    in_progress: '进行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}
</script>

<style scoped>
.maintenance-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #666;
  font-size: 14px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-icon.processing { background: #f59e0b; }
.stat-icon.warning { background: #ef4444; }
.stat-icon.success { background: #10b981; }
.stat-icon.info { background: #6366f1; }
.stat-icon.efficiency { background: #8b5cf6; }

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.maintenance-tabs {
  padding: 16px;
}

.search-filters {
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  flex: 1;
  max-width: 300px;
}

.work-orders-table {
  margin-top: 16px;
}

.technicians-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.technician-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.technician-avatar {
  text-align: center;
  margin-bottom: 12px;
}

.technician-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.technician-level {
  color: #6366f1;
  font-size: 14px;
  margin-bottom: 4px;
}

.technician-specialty {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.technician-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.workload {
  font-size: 12px;
  color: #666;
}

.technician-certifications {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 12px;
}

.technician-actions {
  text-align: center;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 16px;
}

.analytics-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.analytics-card h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  font-size: 24px;
  color: #9ca3af;
}

.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.cost-item.total {
  border-bottom: none;
  border-top: 2px solid #e5e7eb;
  font-weight: bold;
}

.cost-label {
  color: #666;
}

.cost-value {
  font-weight: bold;
  color: #333;
}
</style>