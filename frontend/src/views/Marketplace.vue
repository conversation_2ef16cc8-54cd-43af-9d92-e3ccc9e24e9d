<template>
  <div class="marketplace">
    <!-- 搜索和筛选区域 -->
    <div class="modern-card p-6 mb-8">
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- 主搜索框 -->
        <div class="flex-1">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索航材零件号、名称或描述..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <Search class="w-5 h-5 text-gray-400" />
            </template>
            <template #suffix>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>

        <!-- 快速筛选 -->
        <div class="flex gap-4">
          <el-select v-model="searchForm.category" placeholder="航材类别" size="large" style="width: 150px">
            <el-option label="全部类别" value="" />
            <el-option label="发动机" value="engine" />
            <el-option label="起落架" value="landing_gear" />
            <el-option label="航电设备" value="avionics" />
            <el-option label="液压系统" value="hydraulic" />
            <el-option label="轮胎刹车" value="wheels_brakes" />
          </el-select>

          <el-select v-model="searchForm.aircraft_type" placeholder="机型" size="large" style="width: 120px">
            <el-option label="全部机型" value="" />
            <el-option label="A320" value="A320" />
            <el-option label="A330" value="A330" />
            <el-option label="B737" value="B737" />
            <el-option label="B777" value="B777" />
          </el-select>

          <el-button @click="showAdvancedFilter = !showAdvancedFilter" size="large">
            <Filter class="w-4 h-4 mr-2" />
            高级筛选
          </el-button>
        </div>
      </div>

      <!-- 高级筛选 -->
      <div v-if="showAdvancedFilter" class="mt-6 p-4 bg-gray-50 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <el-select v-model="searchForm.condition" placeholder="航材状态">
            <el-option label="全部状态" value="" />
            <el-option label="全新" value="new" />
            <el-option label="服务件" value="serviceable" />
            <el-option label="修理件" value="repaired" />
          </el-select>

          <el-input v-model="searchForm.min_price" placeholder="最低价格" type="number" />
          <el-input v-model="searchForm.max_price" placeholder="最高价格" type="number" />

          <el-select v-model="searchForm.location" placeholder="所在地区">
            <el-option label="全部地区" value="" />
            <el-option label="北京" value="beijing" />
            <el-option label="上海" value="shanghai" />
            <el-option label="广州" value="guangzhou" />
            <el-option label="深圳" value="shenzhen" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 结果统计和排序 -->
    <div class="flex justify-between items-center mb-6">
      <div class="text-gray-600">
        找到 <span class="font-semibold text-gray-800">{{ pagination.total }}</span> 个相关航材
      </div>
      <div class="flex items-center space-x-4">
        <span class="text-gray-600">排序:</span>
        <el-select v-model="sortBy" @change="handleSort" style="width: 150px">
          <el-option label="综合排序" value="relevance" />
          <el-option label="价格从低到高" value="price_asc" />
          <el-option label="价格从高到低" value="price_desc" />
          <el-option label="最新发布" value="created_desc" />
        </el-select>
      </div>
    </div>

    <!-- 航材列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div
        v-for="material in materials"
        :key="material.id"
        class="modern-card hover:shadow-lg transition-shadow cursor-pointer"
        @click="viewMaterialDetail(material.id)"
      >
        <!-- 图片区域 -->
        <div class="h-48 bg-gray-100 rounded-t-xl overflow-hidden relative">
          <img
            v-if="material.image"
            :src="material.image"
            :alt="material.name"
            class="w-full h-full object-cover"
          />
          <div v-else class="w-full h-full flex items-center justify-center">
            <Box class="w-16 h-16 text-gray-400" />
          </div>
          
          <!-- 状态标签 -->
          <div class="absolute top-3 left-3">
            <span class="px-2 py-1 rounded-full text-xs font-medium" :class="getStatusColor(material.condition)">
              {{ getStatusText(material.condition) }}
            </span>
          </div>

          <!-- 收藏按钮 -->
          <button class="absolute top-3 right-3 w-8 h-8 bg-white bg-opacity-80 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all">
            <Star class="w-4 h-4 text-gray-600" />
          </button>
        </div>

        <!-- 内容区域 -->
        <div class="p-6">
          <div class="flex justify-between items-start mb-2">
            <h3 class="text-lg font-semibold text-gray-800 truncate">{{ material.name || '未知航材' }}</h3>
            <span class="text-lg font-bold text-primary-600">¥{{ material.price || 0 }}</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-3">零件号: {{ material.part_number || 'N/A' }}</p>
          <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ material.description || '暂无描述' }}</p>
          
          <!-- 规格信息 -->
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ material.aircraft_type || '通用' }}</span>
            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ material.category || '其他' }}</span>
          </div>

          <!-- 供应商信息 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                <span class="text-white text-xs font-medium">{{ material.supplier?.name?.charAt(0) || 'S' }}</span>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-800">{{ material.supplier?.name || '未知供应商' }}</p>
                <p class="text-xs text-gray-500">{{ material.location || '未知位置' }}</p>
              </div>
            </div>
            <el-button type="primary" size="small" @click.stop="addToCart(material)">
              加入采购
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-center">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[12, 24, 48, 96]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 航材详情弹窗 -->
    <MaterialDetailDialog v-model="showDetailDialog" :material-id="selectedMaterialId" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import { materialsApi } from '@/api/materials'
import MaterialDetailDialog from '@/components/MaterialDetailDialog.vue'

const router = useRouter()

// 状态
const loading = ref(false)
const showAdvancedFilter = ref(false)
const showDetailDialog = ref(false)
const selectedMaterialId = ref(null)
const sortBy = ref('relevance')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  aircraft_type: '',
  condition: '',
  min_price: '',
  max_price: '',
  location: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 12,
  total: 0
})

// 航材列表
const materials = ref([])

// 模拟数据
const initializeMockData = () => {
  materials.value = [
    {
      id: 1,
      name: '发动机高压压气机叶片',
      part_number: 'CFM56-7B-001',
      description: '适用于CFM56-7B系列发动机的高压压气机叶片，材质为钛合金，经过严格的质量检测。',
      price: 35600,
      condition: 'new',
      aircraft_type: 'B737',
      category: 'engine',
      location: '北京',
      image: '/images/发动机4.jpg',
      supplier: {
        name: '中航材北京公司',
        rating: 4.8
      },
      stock: 5
    },
    {
      id: 2,
      name: '主起落架减震支柱',
      part_number: 'A320-32-1001',
      description: '空客A320主起落架减震支柱，全新原厂件，提供完整的适航证书。',
      price: 128000,
      condition: 'new',
      aircraft_type: 'A320',
      category: 'landing_gear',
      location: '上海',
      image: '/images/机轮2.jpg',
      supplier: {
        name: '上海航材贸易',
        rating: 4.9
      },
      stock: 2
    },
    {
      id: 3,
      name: '轮胎组件',
      part_number: 'MLG-TIRE-001',
      description: '主起落架轮胎，米其林航空轮胎，适用于多种机型，质量可靠。',
      price: 8900,
      condition: 'serviceable',
      aircraft_type: 'B777',
      category: 'wheels_brakes',
      location: '广州',
      image: '/images/机轮.jpg',
      supplier: {
        name: '广州维修基地',
        rating: 4.7
      },
      stock: 8
    },
    {
      id: 4,
      name: '航电显示器',
      part_number: 'EFIS-DU-001',
      description: '电子飞行显示器，LCD彩色显示屏，支持多种显示模式。',
      price: 45000,
      condition: 'repaired',
      aircraft_type: 'A330',
      category: 'avionics',
      location: '深圳',
      image: '/images/航电1.jpg',
      supplier: {
        name: '深圳航电维修',
        rating: 4.6
      },
      stock: 3
    }
  ]
  pagination.total = materials.value.length
}

// 方法
const handleSearch = async () => {
  try {
    loading.value = true
    const response = await materialsApi.searchMaterials({
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
      sort: sortBy.value
    })
    
    if (response.success) {
      materials.value = response.body.materials || []
      pagination.total = response.body.total || 0
    }
    
    console.log('搜索航材:', searchForm)
    ElMessage.success('搜索完成')
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const handleSort = (value) => {
  console.log('排序方式:', value)
  // 重新加载数据
}

const handleSizeChange = (size) => {
  pagination.size = size
  // 重新加载数据
}

const handleCurrentChange = (page) => {
  pagination.page = page
  // 重新加载数据
}

const viewMaterialDetail = (materialId) => {
  selectedMaterialId.value = materialId
  showDetailDialog.value = true
}

const addToCart = (material) => {
  ElMessage.success(`已将 ${material.name} 加入采购清单`)
}

const getStatusColor = (condition) => {
  const colors = {
    new: 'bg-success-100 text-success-800',
    serviceable: 'bg-primary-100 text-primary-800',
    repaired: 'bg-warning-100 text-warning-800'
  }
  return colors[condition] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (condition) => {
  const texts = {
    new: '全新',
    serviceable: '服务件',
    repaired: '修理件'
  }
  return texts[condition] || condition
}

// 加载航材数据
const loadMaterials = async () => {
  try {
    loading.value = true
    const response = await materialsApi.getMaterials({
      page: pagination.page,
      per_page: pagination.size
    })
    if (response.success) {
      materials.value = response.body.materials || []
      pagination.total = response.body.total || 0
    }
  } catch (error) {
    console.error('获取航材列表失败:', error)
    ElMessage.error('获取航材列表失败')
    // 失败时使用模拟数据
    initializeMockData()
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await loadMaterials()
  // 如果API没有返回数据，使用模拟数据
  if (materials.value.length === 0) {
    initializeMockData()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>