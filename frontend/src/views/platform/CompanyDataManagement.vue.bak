<!--
多公司数据管理页面
版本: 1.0
创建时间: 2025-07-23

平台员工专用的跨公司数据查看和管理界面
特点：
1. 跨公司数据统一查看
2. 权限动态控制
3. 数据对比分析
4. 批量操作支持
5. 实时数据同步
-->

<template>
  <div class="company-data-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">跨公司数据管理</h1>
          <p class="page-subtitle">统一管理和监控平台所有公司的业务数据，提供数据分析和业务支持</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshData">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleDataSync">
            <Sync class="w-4 h-4 mr-2" />
            数据同步
          </el-button>
          <el-button @click="handleExportReport" type="primary">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据概览仪表板 -->
    <div class="data-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="overview-card">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Building class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ overviewStats.totalCompanies }}</div>
            <div class="card-label">合作公司</div>
            <div class="card-trend positive">+{{ overviewStats.newCompanies }} 本月新增</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-green-100 text-green-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ overviewStats.totalUser }}</div>
            <div class="card-label">活跃用户</div>
            <div class="card-trend positive">{{ overviewStats.activeRate }}% 活跃率</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Package class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ overviewStats.totalOrders }}</div>
            <div class="card-label">总订单数</div>
            <div class="card-trend neutral">本月 {{ overviewStats.monthlyOrders }} 单</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatCurrency(overviewStats.totalRevenue) }}</div>
            <div class="card-label">总交易额</div>
            <div class="card-trend positive">+{{ overviewStats.revenueGrowth }}% 增长</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公司筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">公司类型</label>
            <el-select v-model="filters.companyType" placeholder="选择公司类型" clearable>
              <el-option label="全部类型" value="" />
              <el-option label="供应商" value="supplier" />
              <el-option label="分销商" value="distributor" />
              <el-option label="航空公司" value="airline" />
              <el-option label="维修公司" value="maintenance" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">业务状态</label>
            <el-select v-model="filters.businessStatus" placeholder="选择业务状态" clearable>
              <el-option label="全部状态" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="非活跃" value="inactive" />
              <el-option label="暂停" value="suspended" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">注册时间</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </div>
          
          <div class="filter-group">
            <label class="filter-label">搜索</label>
            <el-input
              v-model="filters.searchKeyword"
              placeholder="搜索公司名称或用户名"
              @keyup.enter="handleSearch"
              clearable
            >
              <template #suffix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
          
          <div class="filter-actions">
            <el-button @click="handleSearch" type="primary">搜索</el-button>
            <el-button @click="handleResetFilters">重置</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要数据表格 -->
    <div class="data-table-section">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 公司数据管理 -->
        <el-tab-pane label="公司数据" name="companies">
          <div class="table-container">
            <div class="table-header">
              <div class="table-title">公司数据管理</div>
              <div class="table-actions">
                <el-button @click="handleBatchOperation" :disabled="!selectedCompanies.length">
                  批量操作
                </el-button>
                <el-button @click="handleCompanyAnalysis">
                  数据分析
                </el-button>
              </div>
            </div>
            
            <el-table 
              :data="companyData" 
              @selection-change="handleCompanySelectionChange"
              row-key="id"
              stripe
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="company_name" label="公司名称" width="200">
                <template #default="{ row }">
                  <div class="company-info">
                    <div class="company-name">{{ row.company_name }}</div>
                    <div class="company-code">{{ row.company_code }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="company_type" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCompanyTypeTagType(row.company_type)">
                    {{ getCompanyTypeText(row.company_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="user_count" label="用户数" width="100" />
              <el-table-column prop="order_count" label="订单数" width="100" />
              <el-table-column prop="revenue" label="交易额" width="120">
                <template #default="{ row }">
                  ¥{{ formatCurrency(row.revenue) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="last_active" label="最后活跃" width="150" />
              <el-table-column prop="created_at" label="注册时间" width="150" />
              <el-table-column label="操作" width="180" fixed="right">
                <template #default="{ row }">
                  <el-button @click="handleViewCompanyDetail(row)" size="small">
                    详情
                  </el-button>
                  <el-button @click="handleManageCompany(row)" size="small" type="primary">
                    管理
                  </el-button>
                  <el-dropdown @command="(command) => handleCompanyAction(command, row)">
                    <el-button size="small">
                      更多<ChevronDown class="w-3 h-3 ml-1" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="suspend">暂停服务</el-dropdown-item>
                        <el-dropdown-item command="activate">激活服务</el-dropdown-item>
                        <el-dropdown-item command="export">导出数据</el-dropdown-item>
                        <el-dropdown-item command="contact">联系客户</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="table-pagination">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>

        <!-- 用户数据管理 -->
        <el-tab-pane label="用户数据" name="users">
          <div class="table-container">
            <div class="table-header">
              <div class="table-title">跨公司用户数据</div>
              <div class="table-actions">
                <el-input
                  v-model="userSearchKeyword"
                  placeholder="搜索用户"
                  style="width: 200px; margin-right: 12px;"
                  @keyup.enter="handleUserSearch"
                >
                  <template #suffix>
                    <Search class="w-4 h-4 text-gray-400" />
                  </template>
                </el-input>
                <el-button @click="handleUserAnalysis">用户分析</el-button>
              </div>
            </div>
            
            <el-table :data="userData" stripe>
              <el-table-column prop="username" label="用户名" width="150" />
              <el-table-column prop="company_name" label="所属公司" width="180" />
              <el-table-column prop="role" label="角色" width="120">
                <template #default="{ row }">
                  <el-tag :type="getRoleTagType(row.role)">
                    {{ getRoleText(row.role) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="permission_level" label="权限等级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getPermissionTagType(row.permission_level)">
                    Level {{ row.permission_level }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="order_count" label="订单数" width="100" />
              <el-table-column prop="last_login" label="最后登录" width="150" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button @click="handleViewUserDetail(row)" size="small">
                    详情
                  </el-button>
                  <el-button @click="handleManageUser(row)" size="small" type="primary">
                    管理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 订单数据管理 -->
        <el-tab-pane label="订单数据" name="orders">
          <div class="table-container">
            <div class="table-header">
              <div class="table-title">跨公司订单数据</div>
              <div class="table-actions">
                <el-select v-model="orderStatusFilter" placeholder="订单状态" style="width: 150px; margin-right: 12px;">
                  <el-option label="全部状态" value="" />
                  <el-option label="待确认" value="pending" />
                  <el-option label="已确认" value="confirmed" />
                  <el-option label="配送中" value="shipping" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
                <el-button @click="handleOrderAnalysis">订单分析</el-button>
              </div>
            </div>
            
            <el-table :data="orderData" stripe>
              <el-table-column prop="order_no" label="订单号" width="150">
                <template #default="{ row }">
                  <el-link @click="handleViewOrderDetail(row)" type="primary">
                    {{ row.order_no }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="buyer_company" label="买方公司" width="150" />
              <el-table-column prop="seller_company" label="卖方公司" width="150" />
              <el-table-column prop="material_name" label="航材名称" width="200" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="total_amount" label="总金额" width="120">
                <template #default="{ row }">
                  ¥{{ formatCurrency(row.total_amount) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="订单状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getOrderStatusTagType(row.status)">
                    {{ getOrderStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="下单时间" width="150" />
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button @click="handleProcessOrder(row)" size="small" type="primary">
                    处理
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 数据分析 -->
        <el-tab-pane label="数据分析" name="analytics">
          <div class="analytics-container">
            <!-- 业务趋势图表 -->
            <div class="analytics-section">
              <h3 class="analytics-title">业务趋势分析</h3>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="chart-container">
                  <div class="chart-header">
                    <h4 class="chart-title">订单量趋势</h4>
                    <el-select v-model="chartTimeRange" size="small">
                      <el-option label="最近7天" value="7d" />
                      <el-option label="最近30天" value="30d" />
                      <el-option label="最近90天" value="90d" />
                    </el-select>
                  </div>
                  <div class="chart-content">
                    <!-- 这里可以集成图表库，如 ECharts -->
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <TrendingUp class="w-16 h-16 text-gray-300" />
                        <p class="text-gray-500 mt-2">订单量趋势图表</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="chart-container">
                  <div class="chart-header">
                    <h4 class="chart-title">收入分析</h4>
                    <el-select v-model="revenueAnalysisType" size="small">
                      <el-option label="按公司类型" value="company_type" />
                      <el-option label="按地区" value="region" />
                      <el-option label="按产品类型" value="product_type" />
                    </el-select>
                  </div>
                  <div class="chart-content">
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <BarChart class="w-16 h-16 text-gray-300" />
                        <p class="text-gray-500 mt-2">收入分析图表</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 关键指标对比 -->
            <div class="analytics-section">
              <h3 class="analytics-title">关键指标对比</h3>
              <div class="metrics-comparison">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">公司活跃度</h4>
                      <Monitor class="w-5 h-5 text-blue-600" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">{{ analyticsData.companyMonitor }}%</div>
                      <div class="metric-comparison">
                        <span class="comparison-label">较上月</span>
                        <span class="comparison-value positive">+5.2%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">订单转化率</h4>
                      <Target class="w-5 h-5 text-green-600" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">{{ analyticsData.orderConversion }}%</div>
                      <div class="metric-comparison">
                        <span class="comparison-label">较上月</span>
                        <span class="comparison-value positive">+2.8%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">客户满意度</h4>
                      <Star class="w-5 h-5 text-yellow-600" />
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">{{ analyticsData.customerSatisfaction }}</div>
                      <div class="metric-comparison">
                        <span class="comparison-label">较上月</span>
                        <span class="comparison-value neutral">-0.1</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 热门数据排行 -->
            <div class="analytics-section">
              <h3 class="analytics-title">热门数据排行</h3>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="ranking-container">
                  <h4 class="ranking-title">热门航材排行</h4>
                  <div class="ranking-list">
                    <div 
                      v-for="(item, index) in hotMaterials" 
                      :key="item.id"
                      class="ranking-item"
                    >
                      <div class="ranking-number">{{ index + 1 }}</div>
                      <div class="ranking-content">
                        <div class="ranking-name">{{ item.name }}</div>
                        <div class="ranking-stats">订单数: {{ item.order_count }}</div>
                      </div>
                      <div class="ranking-change">
                        <TrendingUp v-if="item.trend === 'up'" class="w-4 h-4 text-green-600" />
                        <TrendingDown v-else-if="item.trend === 'down'" class="w-4 h-4 text-red-600" />
                        <Minus v-else class="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="ranking-container">
                  <h4 class="ranking-title">活跃公司排行</h4>
                  <div class="ranking-list">
                    <div 
                      v-for="(company, index) in activeCompanies" 
                      :key="company.id"
                      class="ranking-item"
                    >
                      <div class="ranking-number">{{ index + 1 }}</div>
                      <div class="ranking-content">
                        <div class="ranking-name">{{ company.name }}</div>
                        <div class="ranking-stats">交易额: ¥{{ formatCurrency(company.revenue) }}</div>
                      </div>
                      <div class="ranking-badge">
                        <el-tag :type="getCompanyTypeTagType(company.type)" size="small">
                          {{ getCompanyTypeText(company.type) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 公司详情对话框 -->
    <el-dialog
      v-model="companyDetailDialogVisible"
      title="公司详细信息"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="company-detail-content" v-if="selectedCompanyDetail">
        <div class="detail-section">
          <h3 class="detail-title">基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">公司名称:</label>
              <span class="detail-value">{{ selectedCompanyDetail.company_name }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">统一社会信用代码:</label>
              <span class="detail-value">{{ selectedCompanyDetail.company_code }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">公司类型:</label>
              <el-tag :type="getCompanyTypeTagType(selectedCompanyDetail.company_type)">
                {{ getCompanyTypeText(selectedCompanyDetail.company_type) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label class="detail-label">注册时间:</label>
              <span class="detail-value">{{ selectedCompanyDetail.created_at }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3 class="detail-title">业务数据</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">用户数量:</label>
              <span class="detail-value">{{ selectedCompanyDetail.user_count }} 人</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">总订单数:</label>
              <span class="detail-value">{{ selectedCompanyDetail.order_count }} 单</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">累计交易额:</label>
              <span class="detail-value">¥{{ formatCurrency(selectedCompanyDetail.revenue) }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">最后活跃:</label>
              <span class="detail-value">{{ selectedCompanyDetail.last_active }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3 class="detail-title">联系信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">联系人:</label>
              <span class="detail-value">{{ selectedCompanyDetail.contact_person || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">联系电话:</label>
              <span class="detail-value">{{ selectedCompanyDetail.contact_phone || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">邮箱地址:</label>
              <span class="detail-value">{{ selectedCompanyDetail.contact_email || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label class="detail-label">公司地址:</label>
              <span class="detail-value">{{ selectedCompanyDetail.address || '未设置' }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="companyDetailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleManageCompany(selectedCompanyDetail)">
            管理公司
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Building, User, Package, TrendingUp, TrendingDown, Minus,
  Refresh, Sync, Download, Search, ChevronDown, Monitor,
  Target, Star, BarChart
} from 'lucide-vue-next'

// 页面状态
const activeTab = ref('companies')
const loading = ref(false)

// 筛选条件
const filters = reactive({
  companyType: '',
  businessStatus: '',
  dateRange: null,
  searchKeyword: ''
})

// 搜索关键词
const userSearchKeyword = ref('')
const orderStatusFilter = ref('')

// 图表配置
const chartTimeRange = ref('30d')
const revenueAnalysisType = ref('company_type')

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 选中的公司
const selectedCompanies = ref([])

// 概览统计数据
const overviewStats = reactive({
  totalCompanies: 156,
  newCompanies: 8,
  totalUsers: 2840,
  activeRate: 87.3,
  totalOrders: 15620,
  monthlyOrders: 1230,
  totalRevenue: 45680000,
  revenueGrowth: 15.8
})

// 公司数据
const companyData = ref([
  {
    id: 1,
    company_name: '中航材料科技有限公司',
    company_code: '91110000MA01234567',
    company_type: 'supplier',
    user_count: 45,
    order_count: 156,
    revenue: 2860000,
    status: 'active',
    last_active: '2025-07-23 14:30',
    created_at: '2024-03-15 09:20',
    contact_person: '张工程师',
    contact_phone: '010-88888888',
    contact_email: '<EMAIL>',
    address: '北京市朝阳区航空材料产业园'
  },
  {
    id: 2,
    company_name: '东方航空器材贸易公司',
    company_code: '91310000MA01234568',
    company_type: 'distributor',
    user_count: 32,
    order_count: 289,
    revenue: 4520000,
    status: 'active',
    last_active: '2025-07-23 16:45',
    created_at: '2024-01-20 11:15',
    contact_person: '李经理',
    contact_phone: '021-66666666',
    contact_email: '<EMAIL>',
    address: '上海市浦东新区航空贸易区'
  },
  {
    id: 3,
    company_name: '南方航空公司',
    company_code: '91440000MA01234569',
    company_type: 'airline',
    user_count: 78,
    order_count: 420,
    revenue: 8960000,
    status: 'active',
    last_active: '2025-07-23 13:20',
    created_at: '2023-11-08 15:30',
    contact_person: '王采购',
    contact_phone: '020-77777777',
    contact_email: '<EMAIL>',
    address: '广州市白云区机场路'
  }
])

// 用户数据
const userData = ref([
  {
    id: 1,
    username: 'zhang_engineer',
    company_name: '中航材料科技有限公司',
    role: 'supplier',
    permission_level: 2,
    order_count: 23,
    last_login: '2025-07-23 14:30',
    status: 'active'
  },
  {
    id: 2,
    username: 'li_manager',
    company_name: '东方航空器材贸易公司',
    role: 'distributor',
    permission_level: 2,
    order_count: 45,
    last_login: '2025-07-23 16:45',
    status: 'active'
  },
  {
    id: 3,
    username: 'wang_purchaser',
    company_name: '南方航空公司',
    role: 'airline',
    permission_level: 2,
    order_count: 67,
    last_login: '2025-07-23 13:20',
    status: 'active'
  }
])

// 订单数据
const orderData = ref([
  {
    id: 1,
    order_no: 'ORD202507230001',
    buyer_company: '南方航空公司',
    seller_company: '中航材料科技有限公司',
    material_name: '涡轮叶片 T2000-A',
    quantity: 2,
    total_amount: 580000,
    status: 'confirmed',
    created_at: '2025-07-23 09:15'
  },
  {
    id: 2,
    order_no: 'ORD202507230002',
    buyer_company: '东方航空公司',
    seller_company: '东方航空器材贸易公司',
    material_name: '燃油泵组件 FP-8800',
    quantity: 1,
    total_amount: 320000,
    status: 'shipping',
    created_at: '2025-07-23 11:30'
  }
])

// 分析数据
const analyticsData = reactive({
  companyMonitor: 87.3,
  orderConversion: 64.2,
  customerSatisfaction: 4.6
})

// 热门航材
const hotMaterials = ref([
  { id: 1, name: '涡轮叶片 T2000-A', order_count: 45, trend: 'up' },
  { id: 2, name: '燃油泵组件 FP-8800', order_count: 38, trend: 'up' },
  { id: 3, name: '液压缸 HC-600', order_count: 32, trend: 'down' },
  { id: 4, name: '起落架部件 LG-450', order_count: 28, trend: 'neutral' },
  { id: 5, name: '航电设备 AV-200', order_count: 25, trend: 'up' }
])

// 活跃公司
const activeCompanies = ref([
  { id: 1, name: '南方航空公司', revenue: 8960000, type: 'airline' },
  { id: 2, name: '东方航空器材贸易公司', revenue: 4520000, type: 'distributor' },
  { id: 3, name: '中航材料科技有限公司', revenue: 2860000, type: 'supplier' },
  { id: 4, name: '海航维修工程', revenue: 2100000, type: 'maintenance' },
  { id: 5, name: '顺丰航空物流', revenue: 1680000, type: 'logistics' }
])

// 对话框状态
const companyDetailDialogVisible = ref(false)
const selectedCompanyDetail = ref(null)

// 计算属性
const filteredCompanyData = computed(() => {
  let data = companyData.value
  
  if (filters.companyType) {
    data = data.filter(company => company.company_type === filters.companyType)
  }
  
  if (filters.businessStatus) {
    data = data.filter(company => company.status === filters.businessStatus)
  }
  
  if (filters.searchKeyword) {
    const keyword = filters.searchKeyword.toLowerCase()
    data = data.filter(company => 
      company.company_name.toLowerCase().includes(keyword) ||
      company.company_code.toLowerCase().includes(keyword)
    )
  }
  
  return data
})

// 方法
const handleRefreshData = () => {
  loading.value = true
  ElMessage.info('正在刷新数据...')
  
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1500)
}

const handleDataSync = async () => {
  try {
    ElMessage.info('开始数据同步...')
    // 模拟数据同步过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据同步完成')
  } catch (error) {
    ElMessage.error('数据同步失败: ' + error.message)
  }
}

const handleExportReport = () => {
  ElMessage.info('正在生成报告...')
  setTimeout(() => {
    ElMessage.success('报告导出完成')
  }, 2000)
}

const handleSearch = () => {
  pagination.currentPage = 1
  ElMessage.success('搜索完成')
}

const handleResetFilters = () => {
  filters.companyType = ''
  filters.businessStatus = ''
  filters.dateRange = null
  filters.searchKeyword = ''
  ElMessage.success('筛选条件已重置')
}

const handleTabChange = (tabName) => {
  console.log(`切换到标签页: ${tabName}`)
}

const handleCompanySelectionChange = (selection) => {
  selectedCompanies.value = selection
}

const handleBatchOperation = () => {
  if (selectedCompanies.value.length === 0) {
    ElMessage.warning('请选择要操作的公司')
    return
  }
  
  ElMessage.info(`准备对 ${selectedCompanies.value.length} 家公司执行批量操作`)
}

const handleCompanyAnalysis = () => {
  ElMessage.info('生成公司数据分析报告...')
}

const handleViewCompanyDetail = (company) => {
  selectedCompanyDetail.value = company
  companyDetailDialogVisible.value = true
}

const handleManageCompany = (company) => {
  ElMessage.info(`管理公司: ${company.company_name}`)
}

const handleCompanyAction = (command, company) => {
  switch (command) {
    case 'suspend':
      ElMessage.warning(`暂停 ${company.company_name} 的服务`)
      break
    case 'activate':
      ElMessage.success(`激活 ${company.company_name} 的服务`)
      break
    case 'export':
      ElMessage.info(`导出 ${company.company_name} 的数据`)
      break
    case 'contact':
      ElMessage.info(`联系 ${company.company_name}`)
      break
  }
}

const handleUserSearch = () => {
  ElMessage.success('用户搜索完成')
}

const handleUserAnalysis = () => {
  ElMessage.info('生成用户分析报告...')
}

const handleViewUserDetail = (user) => {
  ElMessage.info(`查看用户详情: ${user.username}`)
}

const handleManageUser = (user) => {
  ElMessage.info(`管理用户: ${user.username}`)
}

const handleOrderAnalysis = () => {
  ElMessage.info('生成订单分析报告...')
}

const handleViewOrderDetail = (order) => {
  ElMessage.info(`查看订单详情: ${order.order_no}`)
}

const handleProcessOrder = (order) => {
  ElMessage.info(`处理订单: ${order.order_no}`)
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

// 工具方法
const formatCurrency = (amount) => {
  if (!amount) return '0'
  return (amount / 10000).toFixed(1) + '万'
}

const getCompanyTypeTagType = (type) => {
  const types = {
    supplier: 'primary',
    distributor: 'success',
    airline: 'warning',
    maintenance: 'info'
  }
  return types[type] || 'info'
}

const getCompanyTypeText = (type) => {
  const texts = {
    supplier: '供应商',
    distributor: '分销商',
    airline: '航空公司',
    maintenance: '维修公司',
    logistics: '物流公司'
  }
  return texts[type] || type
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    inactive: 'info',
    suspended: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '暂停'
  }
  return texts[status] || status
}

const getRoleTagType = (role) => {
  const types = {
    supplier: 'primary',
    distributor: 'success',
    airline: 'warning',
    maintenance: 'info',
    logistics: 'purple'
  }
  return types[role] || 'info'
}

const getRoleText = (role) => {
  const texts = {
    supplier: '供应商',
    distributor: '分销商',
    airline: '航空公司',
    maintenance: '维修工程师',
    logistics: '物流专员',
    platform_staff: '平台员工',
    admin: '管理员'
  }
  return texts[role] || role
}

const getPermissionTagType = (level) => {
  if (level >= 4) return 'danger'
  if (level >= 3) return 'warning'
  if (level >= 2) return 'primary'
  return 'info'
}

const getOrderStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    confirmed: 'primary',
    shipping: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    shipping: '配送中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  console.log('多公司数据管理页面已加载')
  pagination.total = companyData.value.length
})
</script>

<style scoped>
.company-data-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-area {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 数据概览 */
.data-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 12px;
}

.card-trend.positive {
  color: #059669;
}

.card-trend.neutral {
  color: #6b7280;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

/* 数据表格区域 */
.data-table-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.table-container {
  margin-top: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-weight: 500;
  color: #1f2937;
}

.company-code {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

/* 分析区域 */
.analytics-container {
  padding: 20px 0;
}

.analytics-section {
  margin-bottom: 32px;
}

.analytics-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.chart-container {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.chart-content {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e5e7eb;
  border-radius: 6px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 指标对比 */
.metrics-comparison {
  margin-top: 16px;
}

.metric-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.metric-comparison {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comparison-label {
  font-size: 12px;
  color: #6b7280;
}

.comparison-value {
  font-size: 12px;
  font-weight: 500;
}

.comparison-value.positive {
  color: #059669;
}

.comparison-value.neutral {
  color: #6b7280;
}

/* 排行榜 */
.ranking-container {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.ranking-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 16px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.ranking-number {
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-content {
  flex: 1;
}

.ranking-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.ranking-stats {
  font-size: 12px;
  color: #6b7280;
}

.ranking-change,
.ranking-badge {
  flex-shrink: 0;
}

/* 公司详情对话框 */
.company-detail-content {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-data-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>