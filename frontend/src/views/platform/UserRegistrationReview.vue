<!--
用户注册审核页面
版本: 1.0
创建时间: 2025-07-23

平台员工专用的用户注册申请审核界面
特点：
1. 待审核申请列表管理
2. 详细资料审核流程
3. 批量审核操作
4. 审核历史记录
5. 自动化审核规则
-->

<template>
  <div class="user-registration-review">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">用户注册审核</h1>
          <p class="page-subtitle">审核新用户注册申请，确保平台用户质量和合规性</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshApplications">
            <Refresh class="w-4 h-4 mr-2" />
            刷新列表
          </el-button>
          <el-button @click="handleBatchReview" :disabled="!selectedApplications.length">
            <Check class="w-4 h-4 mr-2" />
            批量审核
          </el-button>
          <el-button @click="handleExportReviewData" type="primary">
            <Download class="w-4 h-4 mr-2" />
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 审核状态概览 -->
    <div class="review-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="overview-card pending">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ reviewStats.pendingCount }}</div>
            <div class="card-label">待审核申请</div>
            <div class="card-detail">需要处理</div>
          </div>
        </div>
        
        <div class="overview-card approved">
          <div class="card-icon bg-green-100 text-green-600">
            <CircleCheck class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ reviewStats.approvedToday }}</div>
            <div class="card-label">今日已通过</div>
            <div class="card-detail">审核通过</div>
          </div>
        </div>
        
        <div class="overview-card rejected">
          <div class="card-icon bg-red-100 text-red-600">
            <CircleClose class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ reviewStats.rejectedToday }}</div>
            <div class="card-label">今日已拒绝</div>
            <div class="card-detail">审核不通过</div>
          </div>
        </div>
        
        <div class="overview-card efficiency">
          <div class="card-icon bg-blue-100 text-blue-600">
            <TrendCharts class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ reviewStats.averageTime }}</div>
            <div class="card-label">平均审核时间</div>
            <div class="card-detail">分钟</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">审核状态</label>
            <el-select v-model="filters.reviewStatus" placeholder="选择审核状态" clearable>
              <el-option label="全部状态" value="" />
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="需补充" value="incomplete" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">申请类型</label>
            <el-select v-model="filters.applicationType" placeholder="选择申请类型" clearable>
              <el-option label="全部类型" value="" />
              <el-option label="供应商" value="supplier" />
              <el-option label="分销商" value="distributor" />
              <el-option label="航空公司" value="airline" />
              <el-option label="维修公司" value="maintenance" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">申请时间</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </div>
          
          <div class="filter-group">
            <label class="filter-label">紧急程度</label>
            <el-select v-model="filters.priority" placeholder="选择紧急程度" clearable>
              <el-option label="全部" value="" />
              <el-option label="普通" value="normal" />
              <el-option label="紧急" value="urgent" />
              <el-option label="加急" value="rush" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">搜索</label>
            <el-input
              v-model="filters.searchKeyword"
              placeholder="搜索公司名称或联系人"
              @keyup.enter="handleSearch"
              clearable
            >
              <template #suffix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
          
          <div class="filter-actions">
            <el-button @click="handleSearch" type="primary">搜索</el-button>
            <el-button @click="handleResetFilters">重置</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核申请列表 -->
    <div class="applications-table-section">
      <div class="table-container">
        <div class="table-header">
          <div class="table-title">注册申请列表</div>
          <div class="table-actions">
            <el-button @click="handleAutoReview" :loading="autoReviewLoading">
              <Timer class="w-4 h-4 mr-2" />
              智能审核
            </el-button>
            <el-dropdown @command="handleQuickAction">
              <el-button>
                快速操作<ArrowDown class="w-3 h-3 ml-1" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="approve_all">批量通过</el-dropdown-item>
                  <el-dropdown-item command="request_more_info">要求补充资料</el-dropdown-item>
                  <el-dropdown-item command="priority_review">优先审核</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <el-table 
          :data="applicationData" 
          @selection-change="handleSelectionChange"
          row-key="id"
          stripe
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="{ row }">
              <el-tag 
                :type="getPriorityTagType(row.priority)" 
                size="small"
                v-if="row.priority !== 'normal'"
              >
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="company_name" label="公司名称" width="200">
            <template #default="{ row }">
              <div class="company-info">
                <div class="company-name">{{ row.company_name }}</div>
                <div class="company-type">{{ getCompanyTypeText(row.company_type) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="contact_person" label="联系人" width="120">
            <template #default="{ row }">
              <div class="contact-info">
                <div class="contact-name">{{ row.contact_person }}</div>
                <div class="contact-phone">{{ row.contact_phone }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="business_license" label="营业执照" width="120">
            <template #default="{ row }">
              <el-button 
                @click="handleViewDocument(row, 'business_license')" 
                size="small" 
                type="primary" 
                link
              >
                查看证件
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="completeness" label="资料完整度" width="120">
            <template #default="{ row }">
              <div class="completeness-info">
                <el-progress 
                  :percentage="row.completeness" 
                  :stroke-width="6"
                  :show-text="false"
                  :color="getCompletenessColor(row.completeness)"
                />
                <span class="completeness-text">{{ row.completeness }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="submitted_at" label="申请时间" width="150">
            <template #default="{ row }">
              <div class="time-info">
                <div class="submit-time">{{ row.submitted_at }}</div>
                <div class="waiting-time">等待 {{ getWaitingTime(row.submitted_at) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="risk_score" label="风险评分" width="100">
            <template #default="{ row }">
              <div class="risk-score" :class="getRiskScoreClass(row.risk_score)">
                {{ row.risk_score }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button @click="handleViewApplication(row)" size="small">
                详情
              </el-button>
              <el-button @click="handleQuickApprove(row)" size="small" type="success">
                通过
              </el-button>
              <el-button @click="handleQuickReject(row)" size="small" type="danger">
                拒绝
              </el-button>
              <el-dropdown @command="(command) => handleApplicationAction(command, row)">
                <el-button size="small">
                  更多<ArrowDown class="w-3 h-3 ml-1" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="request_info">要求补充</el-dropdown-item>
                    <el-dropdown-item command="contact">联系申请人</el-dropdown-item>
                    <el-dropdown-item command="priority">设为优先</el-dropdown-item>
                    <el-dropdown-item command="assign">分配审核员</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="table-pagination">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="applicationDetailDialogVisible"
      title="申请详细信息"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="application-detail-content" v-if="selectedApplication">
        <el-tabs v-model="detailActiveTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="detail-section">
              <h3 class="detail-title">公司基本信息</h3>
              <div class="detail-grid">
                <div class="detail-item">
                  <label class="detail-label">公司名称:</label>
                  <span class="detail-value">{{ selectedApplication.company_name }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">统一社会信用代码:</label>
                  <span class="detail-value">{{ selectedApplication.business_license_no }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">公司类型:</label>
                  <el-tag :type="getCompanyTypeTagType(selectedApplication.company_type)">
                    {{ getCompanyTypeText(selectedApplication.company_type) }}
                  </el-tag>
                </div>
                <div class="detail-item">
                  <label class="detail-label">注册资本:</label>
                  <span class="detail-value">{{ selectedApplication.registered_capital }}万元</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">成立时间:</label>
                  <span class="detail-value">{{ selectedApplication.established_date }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">经营范围:</label>
                  <span class="detail-value">{{ selectedApplication.business_scope }}</span>
                </div>
              </div>
            </div>
            
            <div class="detail-section">
              <h3 class="detail-title">联系信息</h3>
              <div class="detail-grid">
                <div class="detail-item">
                  <label class="detail-label">联系人:</label>
                  <span class="detail-value">{{ selectedApplication.contact_person }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">职务:</label>
                  <span class="detail-value">{{ selectedApplication.contact_position }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">联系电话:</label>
                  <span class="detail-value">{{ selectedApplication.contact_phone }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">邮箱地址:</label>
                  <span class="detail-value">{{ selectedApplication.contact_email }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">办公地址:</label>
                  <span class="detail-value">{{ selectedApplication.office_address }}</span>
                </div>
                <div class="detail-item">
                  <label class="detail-label">邮政编码:</label>
                  <span class="detail-value">{{ selectedApplication.postal_code }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 资质证件 -->
          <el-tab-pane label="资质证件" name="documents">
            <div class="documents-section">
              <h3 class="detail-title">上传的证件资料</h3>
              <div class="documents-grid">
                <div class="document-item" v-for="doc in selectedApplication.documents" :key="doc.type">
                  <div class="document-header">
                    <div class="document-title">{{ getDocumentTitle(doc.type) }}</div>
                    <div class="document-status">
                      <el-tag :type="doc.verified ? 'success' : 'warning'" size="small">
                        {{ doc.verified ? '已验证' : '待验证' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="document-content">
                    <div class="document-preview" @click="handlePreviewDocument(doc)">
                      <img v-if="doc.thumbnail" :src="doc.thumbnail" alt="证件预览" />
                      <div v-else class="document-placeholder">
                        <Document class="w-12 h-12 text-gray-400" />
                        <p class="placeholder-text">点击预览</p>
                      </div>
                    </div>
                    <div class="document-info">
                      <div class="document-name">{{ doc.filename }}</div>
                      <div class="document-size">{{ doc.size }}</div>
                      <div class="document-upload-time">{{ doc.uploaded_at }}</div>
                    </div>
                  </div>
                  <div class="document-actions">
                    <el-button @click="handleDownloadDocument(doc)" size="small">
                      <Download class="w-3 h-3 mr-1" />
                      下载
                    </el-button>
                    <el-button @click="handleVerifyDocument(doc)" size="small" type="primary">
                      <Lock class="w-3 h-3 mr-1" />
                      验证
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 风险评估 -->
          <el-tab-pane label="风险评估" name="risk">
            <div class="risk-assessment-section">
              <h3 class="detail-title">智能风险评估</h3>
              
              <div class="risk-score-overview">
                <div class="risk-score-card">
                  <div class="score-circle" :class="getRiskScoreClass(selectedApplication.risk_score)">
                    <span class="score-number">{{ selectedApplication.risk_score }}</span>
                    <span class="score-label">综合评分</span>
                  </div>
                  <div class="score-description">
                    <p class="risk-level">{{ getRiskLevelText(selectedApplication.risk_score) }}</p>
                    <p class="risk-suggestion">{{ getRiskSuggestion(selectedApplication.risk_score) }}</p>
                  </div>
                </div>
              </div>
              
              <div class="risk-factors">
                <h4 class="factors-title">风险因子分析</h4>
                <div class="factors-list">
                  <div 
                    v-for="factor in selectedApplication.risk_factors" 
                    :key="factor.type"
                    class="factor-item"
                  >
                    <div class="factor-header">
                      <span class="factor-name">{{ factor.name }}</span>
                      <el-tag :type="getFactorTagType(factor.score)" size="small">
                        {{ factor.score }}分
                      </el-tag>
                    </div>
                    <div class="factor-description">{{ factor.description }}</div>
                    <div class="factor-progress">
                      <el-progress 
                        :percentage="factor.score" 
                        :stroke-width="6"
                        :show-text="false"
                        :color="getFactorColor(factor.score)"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="risk-recommendations">
                <h4 class="recommendations-title">审核建议</h4>
                <div class="recommendations-list">
                  <div 
                    v-for="(recommendation, index) in selectedApplication.recommendations" 
                    :key="index"
                    class="recommendation-item"
                  >
                    <div class="recommendation-icon">
                      <component :is="getRecommendationIcon(recommendation.type)" class="w-4 h-4" />
                    </div>
                    <div class="recommendation-content">
                      <div class="recommendation-title">{{ recommendation.title }}</div>
                      <div class="recommendation-description">{{ recommendation.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 审核历史 -->
          <el-tab-pane label="审核历史" name="history">
            <div class="review-history-section">
              <h3 class="detail-title">审核记录</h3>
              <div class="history-timeline">
                <div 
                  v-for="(record, index) in selectedApplication.review_history" 
                  :key="index"
                  class="timeline-item"
                >
                  <div class="timeline-marker" :class="getTimelineMarkerClass(record.action)">
                    <component :is="getTimelineIcon(record.action)" class="w-4 h-4" />
                  </div>
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <span class="timeline-action">{{ getActionText(record.action) }}</span>
                      <span class="timeline-time">{{ record.created_at }}</span>
                    </div>
                    <div class="timeline-details">
                      <div class="reviewer-info">审核员: {{ record.reviewer_name }}</div>
                      <div class="review-comment" v-if="record.comment">
                        备注: {{ record.comment }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="handleRequestMoreInfoFilled(selectedApplication)">
              <Message class="w-4 h-4 mr-2" />
              要求补充资料
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="applicationDetailDialogVisible = false">关闭</el-button>
            <el-button @click="handleRejectApplication(selectedApplication)" type="danger">
              拒绝申请
            </el-button>
            <el-button @click="handleApproveApplication(selectedApplication)" type="success">
              通过申请
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog
      v-model="batchReviewDialogVisible"
      title="批量审核"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-review-content">
        <div class="selected-applications">
          <h4 class="section-title">已选择 {{ selectedApplications.length }} 个申请</h4>
          <div class="applications-list">
            <div 
              v-for="app in selectedApplications" 
              :key="app.id"
              class="application-item"
            >
              <span class="app-name">{{ app.company_name }}</span>
              <el-tag :type="getCompanyTypeTagType(app.company_type)" size="small">
                {{ getCompanyTypeText(app.company_type) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="batch-actions">
          <h4 class="section-title">批量操作</h4>
          <el-radio-group v-model="batchAction">
            <el-radio label="approve">全部通过</el-radio>
            <el-radio label="reject">全部拒绝</el-radio>
            <el-radio label="request_info">要求补充资料</el-radio>
          </el-radio-group>
        </div>
        
        <div class="batch-comment">
          <h4 class="section-title">审核备注</h4>
          <el-input
            v-model="batchComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注（可选）"
          />
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchReviewDialogVisible = false">取消</el-button>
          <el-button @click="handleExecuteBatchReview" type="primary">
            执行批量审核
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 所有图标已全局注册，无需导入

// 页面状态
const loading = ref(false)
const autoReviewLoading = ref(false)

// 筛选条件
const filters = reactive({
  reviewStatus: '',
  applicationType: '',
  dateRange: null,
  priority: '',
  searchKeyword: ''
})

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 选中的申请
const selectedApplications = ref([])

// 统计数据
const reviewStats = reactive({
  pendingCount: 23,
  approvedToday: 15,
  rejectedToday: 3,
  averageTime: 12
})

// 申请数据
const applicationData = ref([
  {
    id: 1,
    company_name: '天航材料技术有限公司',
    company_type: 'supplier',
    contact_person: '李工程师',
    contact_phone: '138****8888',
    contact_position: '技术总监',
    contact_email: '<EMAIL>',
    business_license_no: '91110000MA01234567',
    registered_capital: 5000,
    established_date: '2020-05-15',
    business_scope: '航空材料研发、生产、销售',
    office_address: '北京市大兴区航空产业园区',
    postal_code: '102600',
    completeness: 95,
    submitted_at: '2025-07-23 09:30:00',
    status: 'pending',
    priority: 'normal',
    risk_score: 78,
    documents: [
      {
        type: 'business_license',
        filename: '营业执照.pdf',
        size: '2.5MB',
        uploaded_at: '2025-07-23 09:32:00',
        verified: false,
        thumbnail: null
      },
      {
        type: 'certification',
        filename: 'AS9100认证证书.pdf',
        size: '1.8MB',
        uploaded_at: '2025-07-23 09:35:00',
        verified: true,
        thumbnail: null
      }
    ],
    risk_factors: [
      {
        type: 'business_history',
        name: '经营历史',
        score: 85,
        description: '公司成立5年，经营状况良好'
      },
      {
        type: 'financial_status',
        name: '财务状况',
        score: 72,
        description: '注册资本充足，无重大财务风险'
      },
      {
        type: 'industry_reputation',
        name: '行业声誉',
        score: 80,
        description: '在航空材料行业有一定知名度'
      }
    ],
    recommendations: [
      {
        type: 'approve',
        title: '建议通过',
        description: '公司资质完整，风险评分良好，建议通过审核'
      }
    ],
    review_history: [
      {
        action: 'submitted',
        reviewer_name: '系统',
        created_at: '2025-07-23 09:30:00',
        comment: '申请已提交'
      },
      {
        action: 'document_verified',
        reviewer_name: '张审核员',
        created_at: '2025-07-23 10:15:00',
        comment: 'AS9100认证证书验证通过'
      }
    ]
  },
  {
    id: 2,
    company_name: '东部航空维修服务公司',
    company_type: 'maintenance',
    contact_person: '王经理',
    contact_phone: '139****9999',
    contact_position: '业务经理',
    contact_email: '<EMAIL>',
    business_license_no: '91320000MA01234568',
    registered_capital: 8000,
    established_date: '2018-03-20',
    business_scope: '航空器维修、航材贸易',
    office_address: '江苏省南京市航空港经济区',
    postal_code: '211100',
    completeness: 88,
    submitted_at: '2025-07-22 15:45:00',
    status: 'pending',
    priority: 'urgent',
    risk_score: 65,
    documents: [
      {
        type: 'business_license',
        filename: '营业执照.jpg',
        size: '3.2MB',
        uploaded_at: '2025-07-22 15:47:00',
        verified: true,
        thumbnail: null
      },
      {
        type: 'mro_license',
        filename: '维修许可证.pdf',
        size: '2.1MB',
        uploaded_at: '2025-07-22 15:50:00',
        verified: false,
        thumbnail: null
      }
    ],
    risk_factors: [
      {
        type: 'business_history',
        name: '经营历史',
        score: 90,
        description: '公司成立7年，维修经验丰富'
      },
      {
        type: 'qualification',
        name: '资质认证',
        score: 45,
        description: '部分维修资质待验证'
      }
    ],
    recommendations: [
      {
        type: 'caution',
        title: '谨慎审核',
        description: '维修资质需要进一步核实，建议要求补充相关证明'
      }
    ],
    review_history: [
      {
        action: 'submitted',
        reviewer_name: '系统',
        created_at: '2025-07-22 15:45:00',
        comment: '申请已提交'
      }
    ]
  }
])

// 对话框状态
const applicationDetailDialogVisible = ref(false)
const batchReviewDialogVisible = ref(false)
const selectedApplication = ref(null)
const detailActiveTab = ref('basic')

// 批量审核
const batchAction = ref('approve')
const batchComment = ref('')

// 计算属性
const filteredApplicationData = computed(() => {
  let data = applicationData.value
  
  if (filters.reviewStatus) {
    data = data.filter(app => app.status === filters.reviewStatus)
  }
  
  if (filters.applicationType) {
    data = data.filter(app => app.company_type === filters.applicationType)
  }
  
  if (filters.priority) {
    data = data.filter(app => app.priority === filters.priority)
  }
  
  if (filters.searchKeyword) {
    const keyword = filters.searchKeyword.toLowerCase()
    data = data.filter(app => 
      app.company_name.toLowerCase().includes(keyword) ||
      app.contact_person.toLowerCase().includes(keyword)
    )
  }
  
  return data
})

// 方法
const handleRefreshApplications = () => {
  loading.value = true
  ElMessage.info('正在刷新申请列表...')
  
  setTimeout(() => {
    loading.value = false
    ElMessage.success('申请列表已刷新')
  }, 1500)
}

const handleBatchReview = () => {
  if (selectedApplications.value.length === 0) {
    ElMessage.warning('请选择要审核的申请')
    return
  }
  batchReviewDialogVisible.value = true
}

const handleExportReviewData = () => {
  ElMessage.info('正在导出审核数据...')
  setTimeout(() => {
    ElMessage.success('数据导出完成')
  }, 2000)
}

const handleSearch = () => {
  pagination.currentPage = 1
  ElMessage.success('搜索完成')
}

const handleResetFilters = () => {
  filters.reviewStatus = ''
  filters.applicationType = ''
  filters.dateRange = null
  filters.priority = ''
  filters.searchKeyword = ''
  ElMessage.success('筛选条件已重置')
}

const handleSelectionChange = (selection) => {
  selectedApplications.value = selection
}

const handleAutoReview = async () => {
  autoReviewLoading.value = true
  ElMessage.info('启动智能审核...')
  
  try {
    // 模拟智能审核过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('智能审核完成，已自动处理低风险申请')
  } catch (error) {
    ElMessage.error('智能审核失败')
  } finally {
    autoReviewLoading.value = false
  }
}

const handleQuickAction = (command) => {
  switch (command) {
    case 'approve_all':
      ElMessage.info('批量通过选中申请')
      break
    case 'request_more_info':
      ElMessage.info('要求选中申请补充资料')
      break
    case 'priority_review':
      ElMessage.info('设置选中申请为优先审核')
      break
  }
}

const handleViewApplication = (application) => {
  selectedApplication.value = application
  applicationDetailDialogVisible.value = true
  detailActiveTab.value = 'basic'
}

const handleQuickApprove = async (application) => {
  try {
    await ElMessageBox.confirm(`确定要通过 "${application.company_name}" 的注册申请吗？`, '确认通过', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    application.status = 'approved'
    ElMessage.success('申请已通过')
  } catch {
    // 用户取消
  }
}

const handleQuickReject = async (application) => {
  try {
    await ElMessageBox.confirm(`确定要拒绝 "${application.company_name}" 的注册申请吗？`, '确认拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    application.status = 'rejected'
    ElMessage.success('申请已拒绝')
  } catch {
    // 用户取消
  }
}

const handleApplicationAction = (command, application) => {
  switch (command) {
    case 'request_info':
      handleRequestMoreInfoFilled(application)
      break
    case 'contact':
      ElMessage.info(`联系申请人: ${application.contact_person}`)
      break
    case 'priority':
      application.priority = 'urgent'
      ElMessage.success('已设为紧急处理')
      break
    case 'assign':
      ElMessage.info('分配审核员功能开发中')
      break
  }
}

const handleRequestMoreInfoFilled = (application) => {
  ElMessage.info(`要求 ${application.company_name} 补充资料`)
  application.status = 'incomplete'
}

const handleApproveApplication = (application) => {
  application.status = 'approved'
  ElMessage.success('申请已通过')
  applicationDetailDialogVisible.value = false
}

const handleRejectApplication = (application) => {
  application.status = 'rejected'
  ElMessage.success('申请已拒绝')
  applicationDetailDialogVisible.value = false
}

const handleExecuteBatchReview = () => {
  ElMessage.success(`批量${batchAction.value === 'approve' ? '通过' : '处理'}了 ${selectedApplications.value.length} 个申请`)
  selectedApplications.value.forEach(app => {
    if (batchAction.value === 'approve') {
      app.status = 'approved'
    } else if (batchAction.value === 'reject') {
      app.status = 'rejected'
    } else if (batchAction.value === 'request_info') {
      app.status = 'incomplete'
    }
  })
  batchReviewDialogVisible.value = false
  selectedApplications.value = []
}

const handleViewDocument = (application, docType) => {
  ElMessage.info(`查看 ${application.company_name} 的 ${getDocumentTitle(docType)}`)
}

const handlePreviewDocument = (doc) => {
  ElMessage.info(`预览文档: ${doc.filename}`)
}

const handleDownloadDocument = (doc) => {
  ElMessage.info(`下载文档: ${doc.filename}`)
}

const handleVerifyDocument = (doc) => {
  doc.verified = true
  ElMessage.success('证件验证通过')
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

// 工具方法
const getRowClassName = ({ row }) => {
  if (row.priority === 'urgent') return 'urgent-row'
  if (row.priority === 'rush') return 'rush-row'
  return ''
}

const getPriorityTagType = (priority) => {
  const types = {
    urgent: 'danger',
    rush: 'warning',
    normal: 'info'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    rush: '加急',
    normal: '普通'
  }
  return texts[priority] || priority
}

const getCompanyTypeTagType = (type) => {
  const types = {
    supplier: 'primary',
    distributor: 'success',
    airline: 'warning',
    maintenance: 'info'
  }
  return types[type] || 'info'
}

const getCompanyTypeText = (type) => {
  const texts = {
    supplier: '供应商',
    distributor: '分销商',
    airline: '航空公司',
    maintenance: '维修公司',
    logistics: '物流公司'
  }
  return texts[type] || type
}

const getCompletenessColor = (percentage) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

const getWaitingTime = (submitTime) => {
  const now = new Date()
  const submit = new Date(submitTime)
  const hours = Math.floor((now - submit) / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚提交'
  if (hours < 24) return `${hours}小时`
  return `${Math.floor(hours / 24)}天`
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    incomplete: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    incomplete: '需补充'
  }
  return texts[status] || status
}

const getRiskScoreClass = (score) => {
  if (score >= 80) return 'risk-low'
  if (score >= 60) return 'risk-medium'
  return 'risk-high'
}

const getRiskLevelText = (score) => {
  if (score >= 80) return '低风险'
  if (score >= 60) return '中等风险'
  return '高风险'
}

const getRiskSuggestion = (score) => {
  if (score >= 80) return '建议快速通过审核'
  if (score >= 60) return '建议仔细审核相关资质'
  return '建议要求补充更多证明材料'
}

const getFactorTagType = (score) => {
  if (score >= 80) return 'success'
  if (score >= 60) return 'warning'
  return 'danger'
}

const getFactorColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getRecommendationIcon = (type) => {
  const icons = {
    approve: CircleCheck,
    caution: Warning,
    reject: CircleClose,
    info: InfoFilled
  }
  return icons[type] || InfoFilled
}

const getTimelineMarkerClass = (action) => {
  const classes = {
    submitted: 'timeline-submitted',
    document_verified: 'timeline-verified',
    approved: 'timeline-approved',
    rejected: 'timeline-rejected'
  }
  return classes[action] || 'timeline-default'
}

const getTimelineIcon = (action) => {
  const icons = {
    submitted: Document,
    document_verified: Shield,
    approved: CircleCheck,
    rejected: CircleClose
  }
  return icons[action] || InfoFilled
}

const getActionText = (action) => {
  const texts = {
    submitted: '提交申请',
    document_verified: '文件验证',
    approved: '审核通过',
    rejected: '审核拒绝',
    request_info: '要求补充'
  }
  return texts[action] || action
}

const getDocumentTitle = (type) => {
  const titles = {
    business_license: '营业执照',
    certification: '资质证书',
    mro_license: '维修许可证',
    tax_certificate: '税务登记证'
  }
  return titles[type] || '相关证件'
}

// 生命周期
onMounted(() => {
  console.log('用户注册审核页面已加载')
  pagination.total = applicationData.value.length
})
</script>

<style scoped>
.user-registration-review {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-area {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 审核概览 */
.review-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

/* 申请表格区域 */
.applications-table-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 表格行样式 */
:deep(.urgent-row) {
  background-color: #fef2f2 !important;
}

:deep(.rush-row) {
  background-color: #fffbeb !important;
}

/* 表格内容样式 */
.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-weight: 500;
  color: #1f2937;
}

.company-type {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 500;
  color: #1f2937;
}

.contact-phone {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.completeness-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.completeness-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.submit-time {
  font-size: 13px;
  color: #1f2937;
}

.waiting-time {
  font-size: 11px;
  color: #ef4444;
  margin-top: 2px;
}

.risk-score {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
}

.risk-score.risk-low {
  background-color: #dcfce7;
  color: #166534;
}

.risk-score.risk-medium {
  background-color: #fef3c7;
  color: #92400e;
}

.risk-score.risk-high {
  background-color: #fee2e2;
  color: #991b1b;
}

/* 申请详情对话框 */
.application-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
}

/* 证件文档区域 */
.documents-section {
  margin-top: 16px;
}

.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.document-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #f9fafb;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.document-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.document-content {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.document-preview {
  width: 60px;
  height: 60px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: white;
}

.document-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.document-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.placeholder-text {
  font-size: 10px;
  color: #9ca3af;
  margin-top: 4px;
}

.document-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.document-name {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.document-size,
.document-upload-time {
  font-size: 11px;
  color: #6b7280;
}

.document-actions {
  display: flex;
  gap: 8px;
}

/* 风险评估区域 */
.risk-assessment-section {
  margin-top: 16px;
}

.risk-score-overview {
  margin-bottom: 24px;
}

.risk-score-card {
  display: flex;
  align-items: center;
  gap: 24px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 4px solid;
}

.score-circle.risk-low {
  background-color: #dcfce7;
  border-color: #22c55e;
}

.score-circle.risk-medium {
  background-color: #fef3c7;
  border-color: #eab308;
}

.score-circle.risk-high {
  background-color: #fee2e2;
  border-color: #ef4444;
}

.score-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.score-label {
  font-size: 12px;
  color: #6b7280;
}

.score-description {
  flex: 1;
}

.risk-level {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.risk-suggestion {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.risk-factors {
  margin-bottom: 24px;
}

.factors-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.factors-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.factor-item {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.factor-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.factor-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
}

.factor-progress {
  margin-top: 8px;
}

.risk-recommendations {
  margin-bottom: 16px;
}

.recommendations-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.recommendation-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.recommendation-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
}

/* 审核历史时间线 */
.review-history-section {
  margin-top: 16px;
}

.history-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.timeline-marker {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timeline-marker.timeline-submitted {
  background-color: #dbeafe;
  color: #3b82f6;
}

.timeline-marker.timeline-verified {
  background-color: #dcfce7;
  color: #22c55e;
}

.timeline-marker.timeline-approved {
  background-color: #dcfce7;
  color: #22c55e;
}

.timeline-marker.timeline-rejected {
  background-color: #fee2e2;
  color: #ef4444;
}

.timeline-content {
  flex: 1;
  padding-top: 4px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-action {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.timeline-time {
  font-size: 12px;
  color: #6b7280;
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reviewer-info,
.review-comment {
  font-size: 13px;
  color: #6b7280;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 12px;
}

/* 批量审核对话框 */
.batch-review-content {
  max-height: 60vh;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.selected-applications {
  margin-bottom: 24px;
}

.applications-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

.application-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.application-item:last-child {
  border-bottom: none;
}

.app-name {
  font-size: 14px;
  color: #1f2937;
}

.batch-actions {
  margin-bottom: 24px;
}

.batch-comment {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-registration-review {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .documents-grid {
    grid-template-columns: 1fr;
  }
  
  .risk-score-card {
    flex-direction: column;
    text-align: center;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>