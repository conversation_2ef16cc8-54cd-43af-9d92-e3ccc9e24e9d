<!--
客户服务工具和问题跟踪系统
版本: 1.0
创建时间: 2025-07-23

平台员工专用的客户服务管理和问题跟踪界面
特点：
1. 工单管理系统
2. 在线客服聊天
3. 问题分类追踪
4. 客户满意度调查
5. 服务质量统计
-->

<template>
  <div class="customer-service">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">客户服务中心</h1>
          <p class="page-subtitle">提供专业的客户服务支持，处理用户问题和反馈，提升用户满意度</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshTickets">
            <Refresh class="w-4 h-4 mr-2" />
            刷新工单
          </el-button>
          <el-button @click="handleOpenChat">
            <MessageCircle class="w-4 h-4 mr-2" />
            在线客服
          </el-button>
          <el-button @click="handleCreateTicket" type="primary">
            <Plus class="w-4 h-4 mr-2" />
            新建工单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 服务状态概览 -->
    <div class="service-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="overview-card pending">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ serviceStats.pendingTickets }}</div>
            <div class="card-label">待处理工单</div>
            <div class="card-trend">需要及时处理</div>
          </div>
        </div>
        
        <div class="overview-card active">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ serviceStats.activeChats }}</div>
            <div class="card-label">活跃会话</div>
            <div class="card-trend">在线客服中</div>
          </div>
        </div>
        
        <div class="overview-card satisfaction">
          <div class="card-icon bg-green-100 text-green-600">
            <Star class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ serviceStats.satisfaction }}%</div>
            <div class="card-label">客户满意度</div>
            <div class="card-trend positive">+{{ serviceStats.satisfactionChange }}% 本月</div>
          </div>
        </div>
        
        <div class="overview-card response">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Zap class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ serviceStats.avgResponseTime }}min</div>
            <div class="card-label">平均响应时间</div>
            <div class="card-trend neutral">服务目标: <5min</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="service-content">
      <el-tabs v-model="activeServiceTab" @tab-change="handleTabChange">
        <!-- 工单管理 -->
        <el-tab-pane label="工单管理" name="tickets">
          <div class="tickets-panel">
            <!-- 工单筛选 -->
            <div class="tickets-filter">
              <div class="filter-row">
                <div class="filter-group">
                  <label class="filter-label">工单状态</label>
                  <el-select v-model="ticketFilters.status" placeholder="选择状态" clearable>
                    <el-option label="全部状态" value="" />
                    <el-option label="待处理" value="pending" />
                    <el-option label="处理中" value="processing" />
                    <el-option label="待回复" value="waiting_reply" />
                    <el-option label="已解决" value="resolved" />
                    <el-option label="已关闭" value="closed" />
                  </el-select>
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">问题类型</label>
                  <el-select v-model="ticketFilters.category" placeholder="选择类型" clearable>
                    <el-option label="全部类型" value="" />
                    <el-option label="技术问题" value="technical" />
                    <el-option label="账户问题" value="account" />
                    <el-option label="订单问题" value="order" />
                    <el-option label="支付问题" value="payment" />
                    <el-option label="功能建议" value="suggestion" />
                    <el-option label="其他问题" value="other" />
                  </el-select>
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">优先级</label>
                  <el-select v-model="ticketFilters.priority" placeholder="选择优先级" clearable>
                    <el-option label="全部优先级" value="" />
                    <el-option label="紧急" value="urgent" />
                    <el-option label="高" value="high" />
                    <el-option label="中" value="medium" />
                    <el-option label="低" value="low" />
                  </el-select>
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">负责人</label>
                  <el-select v-model="ticketFilters.assignee" placeholder="选择负责人" clearable>
                    <el-option label="全部负责人" value="" />
                    <el-option label="我的工单" value="me" />
                    <el-option label="张客服" value="zhang" />
                    <el-option label="李客服" value="li" />
                    <el-option label="王客服" value="wang" />
                  </el-select>
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">搜索</label>
                  <el-input
                    v-model="ticketFilters.keyword"
                    placeholder="搜索工单标题或客户"
                    @keyup.enter="handleSearchTickets"
                    clearable
                  >
                    <template #suffix>
                      <Search class="w-4 h-4 text-gray-400" />
                    </template>
                  </el-input>
                </div>
                
                <div class="filter-actions">
                  <el-button @click="handleSearchTickets" type="primary">搜索</el-button>
                  <el-button @click="handleResetFilters">重置</el-button>
                </div>
              </div>
            </div>

            <!-- 工单列表 -->
            <div class="tickets-table">
              <div class="table-header">
                <div class="table-title">工单列表</div>
                <div class="table-actions">
                  <el-button @click="handleBatchAssign" :disabled="!selectedTickets.length">
                    <UserCheck class="w-4 h-4 mr-2" />
                    批量分配
                  </el-button>
                  <el-button @click="handleExportTickets">
                    <Download class="w-4 h-4 mr-2" />
                    导出工单
                  </el-button>
                </div>
              </div>
              
              <el-table 
                :data="ticketData" 
                @selection-change="handleTicketSelectionChange"
                row-key="id"
                stripe
                :row-class-name="getTicketRowClassName"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="priority" label="优先级" width="80">
                  <template #default="{ row }">
                    <el-tag 
                      :type="getPriorityTagType(row.priority)" 
                      size="small"
                      v-if="row.priority !== 'medium'"
                    >
                      {{ getPriorityText(row.priority) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ticket_no" label="工单号" width="120">
                  <template #default="{ row }">
                    <el-link @click="handleViewTicket(row)" type="primary">
                      {{ row.ticket_no }}
                    </el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" width="250">
                  <template #default="{ row }">
                    <div class="ticket-title">
                      <span class="title-text">{{ row.title }}</span>
                      <div class="ticket-meta">
                        <el-tag :type="getCategoryTagType(row.category)" size="small">
                          {{ getCategoryText(row.category) }}
                        </el-tag>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="customer" label="客户" width="150">
                  <template #default="{ row }">
                    <div class="customer-info">
                      <div class="customer-name">{{ row.customer.name }}</div>
                      <div class="customer-company">{{ row.customer.company }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="assignee" label="负责人" width="100">
                  <template #default="{ row }">
                    <div class="assignee-info">
                      <el-avatar :size="24" :src="row.assignee?.avatar">
                        {{ row.assignee?.name?.charAt(0) || '未' }}
                      </el-avatar>
                      <span class="assignee-name">{{ row.assignee?.name || '待分配' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" width="150">
                  <template #default="{ row }">
                    <div class="time-info">
                      <div class="create-time">{{ row.created_at }}</div>
                      <div class="elapsed-time">{{ getElapsedTime(row.created_at) }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="updated_at" label="最后更新" width="150" />
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <el-button @click="handleProcessTicket(row)" size="small" type="primary">
                      处理
                    </el-button>
                    <el-button @click="handleAssignTicket(row)" size="small">
                      分配
                    </el-button>
                    <el-dropdown @command="(command) => handleTicketAction(command, row)">
                      <el-button size="small">
                        更多<ChevronDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="close">关闭工单</el-dropdown-item>
                          <el-dropdown-item command="escalate">升级处理</el-dropdown-item>
                          <el-dropdown-item command="duplicate">标记重复</el-dropdown-item>
                          <el-dropdown-item command="priority">调整优先级</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="table-pagination">
                <el-pagination
                  v-model:current-page="ticketPagination.currentPage"
                  v-model:page-size="ticketPagination.pageSize"
                  :total="ticketPagination.total"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleTicketSizeChange"
                  @current-change="handleTicketCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 在线客服 -->
        <el-tab-pane label="在线客服" name="chat">
          <div class="chat-panel">
            <div class="chat-layout">
              <!-- 会话列表 -->
              <div class="chat-sidebar">
                <div class="sidebar-header">
                  <h3 class="sidebar-title">活跃会话 ({{ chatSessions.length }})</h3>
                  <el-button @click="handleRefreshChats" size="small">
                    <Refresh class="w-3 h-3" />
                  </el-button>
                </div>
                
                <div class="chat-sessions">
                  <div 
                    v-for="session in chatSessions" 
                    :key="session.id"
                    class="chat-session-item"
                    :class="{ active: selectedChatSession?.id === session.id }"
                    @click="handleSelectChatSession(session)"
                  >
                    <div class="session-avatar">
                      <el-avatar :size="36" :src="session.customer.avatar">
                        {{ session.customer.name.charAt(0) }}
                      </el-avatar>
                      <div class="session-status" :class="session.status"></div>
                    </div>
                    <div class="session-info">
                      <div class="session-name">{{ session.customer.name }}</div>
                      <div class="session-company">{{ session.customer.company }}</div>
                      <div class="session-last-message">{{ session.lastMessage }}</div>
                    </div>
                    <div class="session-meta">
                      <div class="session-time">{{ formatTime(session.lastMessageTime) }}</div>
                      <el-badge 
                        v-if="session.unreadCount > 0" 
                        :value="session.unreadCount" 
                        :max="99"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 聊天区域 -->
              <div class="chat-main">
                <div v-if="selectedChatSession" class="chat-container">
                  <!-- 聊天头部 -->
                  <div class="chat-header">
                    <div class="chat-customer-info">
                      <el-avatar :size="32" :src="selectedChatSession.customer.avatar">
                        {{ selectedChatSession.customer.name.charAt(0) }}
                      </el-avatar>
                      <div class="customer-details">
                        <div class="customer-name">{{ selectedChatSession.customer.name }}</div>
                        <div class="customer-status">
                          <div class="status-dot" :class="selectedChatSession.status"></div>
                          {{ getStatusText(selectedChatSession.status) }}
                        </div>
                      </div>
                    </div>
                    <div class="chat-actions">
                      <el-button @click="handleTransferChat" size="small">
                        <UserX class="w-3 h-3 mr-1" />
                        转接
                      </el-button>
                      <el-button @click="handleEndChat" size="small" type="danger">
                        <PhoneOff class="w-3 h-3 mr-1" />
                        结束会话
                      </el-button>
                    </div>
                  </div>

                  <!-- 消息列表 -->
                  <div class="chat-messages" ref="chatMessagesRef">
                    <div 
                      v-for="message in selectedChatSession.messages" 
                      :key="message.id"
                      class="message-item"
                      :class="{ 'message-sent': message.sender === 'staff', 'message-received': message.sender === 'customer' }"
                    >
                      <div class="message-avatar">
                        <el-avatar :size="28" :src="message.avatar">
                          {{ message.senderName.charAt(0) }}
                        </el-avatar>
                      </div>
                      <div class="message-content">
                        <div class="message-header">
                          <span class="message-sender">{{ message.senderName }}</span>
                          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                        </div>
                        <div class="message-body">
                          <div v-if="message.type === 'text'" class="message-text">
                            {{ message.content }}
                          </div>
                          <div v-else-if="message.type === 'image'" class="message-image">
                            <img :src="message.content" alt="图片" @click="handlePreviewImage(message.content)" />
                          </div>
                          <div v-else-if="message.type === 'file'" class="message-file">
                            <div class="file-info">
                              <Document class="w-4 h-4" />
                              <span class="file-name">{{ message.fileName }}</span>
                              <span class="file-size">{{ message.fileSize }}</span>
                            </div>
                            <el-button @click="handleDownloadFile(message)" size="small">
                              下载
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 消息输入区域 -->
                  <div class="chat-input">
                    <div class="input-toolbar">
                      <el-button @click="handleSendImage" size="small">
                        <Image class="w-3 h-3 mr-1" />
                        图片
                      </el-button>
                      <el-button @click="handleSendFile" size="small">
                        <Paperclip class="w-3 h-3 mr-1" />
                        文件
                      </el-button>
                      <el-button @click="handleQuickReply" size="small">
                        <Zap class="w-3 h-3 mr-1" />
                        快捷回复
                      </el-button>
                    </div>
                    <div class="input-area">
                      <el-input
                        v-model="chatMessage"
                        type="textarea"
                        :rows="3"
                        placeholder="输入消息..."
                        @keydown.ctrl.enter="handleSendMessage"
                      />
                      <div class="input-actions">
                        <span class="input-tip">Ctrl+Enter 发送</span>
                        <el-button @click="handleSendMessage" type="primary" :disabled="!chatMessage.trim()">
                          发送
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else class="chat-empty">
                  <div class="empty-content">
                    <MessageCircle class="w-16 h-16 text-gray-300" />
                    <p class="empty-text">选择一个会话开始聊天</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 客户反馈 -->
        <el-tab-pane label="客户反馈" name="feedback">
          <div class="feedback-panel">
            <!-- 反馈统计 -->
            <div class="feedback-stats">
              <div class="stats-header">
                <h3 class="stats-title">反馈统计</h3>
                <el-date-picker
                  v-model="feedbackDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </div>
              
              <div class="stats-grid">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div class="feedback-stat-card">
                    <div class="stat-icon bg-blue-100 text-blue-600">
                      <MessageSquare class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ feedbackStats.total }}</div>
                      <div class="stat-label">总反馈数</div>
                    </div>
                  </div>
                  
                  <div class="feedback-stat-card">
                    <div class="stat-icon bg-green-100 text-green-600">
                      <ThumbsUp class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ feedbackStats.positive }}</div>
                      <div class="stat-label">正面反馈</div>
                    </div>
                  </div>
                  
                  <div class="feedback-stat-card">
                    <div class="stat-icon bg-red-100 text-red-600">
                      <ThumbsDown class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ feedbackStats.negative }}</div>
                      <div class="stat-label">负面反馈</div>
                    </div>
                  </div>
                  
                  <div class="feedback-stat-card">
                    <div class="stat-icon bg-yellow-100 text-yellow-600">
                      <Star class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ feedbackStats.avgRating }}</div>
                      <div class="stat-label">平均评分</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 反馈列表 -->
            <div class="feedback-list">
              <div class="list-header">
                <h3 class="list-title">客户反馈列表</h3>
                <div class="list-actions">
                  <el-select v-model="feedbackFilter" placeholder="筛选反馈" size="small">
                    <el-option label="全部反馈" value="" />
                    <el-option label="正面反馈" value="positive" />
                    <el-option label="负面反馈" value="negative" />
                    <el-option label="待处理" value="pending" />
                  </el-select>
                  <el-button @click="handleExportFeedback" size="small">
                    <Download class="w-3 h-3 mr-1" />
                    导出
                  </el-button>
                </div>
              </div>
              
              <div class="feedback-items">
                <div 
                  v-for="feedback in feedbackData" 
                  :key="feedback.id"
                  class="feedback-item"
                  :class="feedback.sentiment"
                >
                  <div class="feedback-header">
                    <div class="feedback-customer">
                      <el-avatar :size="32" :src="feedback.customer.avatar">
                        {{ feedback.customer.name.charAt(0) }}
                      </el-avatar>
                      <div class="customer-info">
                        <div class="customer-name">{{ feedback.customer.name }}</div>
                        <div class="customer-company">{{ feedback.customer.company }}</div>
                      </div>
                    </div>
                    <div class="feedback-meta">
                      <div class="feedback-rating">
                        <el-rate 
                          v-model="feedback.rating" 
                          disabled 
                          size="small"
                          show-score
                        />
                      </div>
                      <div class="feedback-time">{{ feedback.createdAt }}</div>
                    </div>
                  </div>
                  
                  <div class="feedback-content">
                    <div class="feedback-text">{{ feedback.content }}</div>
                    <div v-if="feedback.images" class="feedback-images">
                      <img 
                        v-for="(image, index) in feedback.images" 
                        :key="index"
                        :src="image" 
                        alt="反馈图片"
                        class="feedback-image"
                        @click="handlePreviewImage(image)"
                      />
                    </div>
                  </div>
                  
                  <div class="feedback-actions">
                    <el-tag :type="getSentimentTagType(feedback.sentiment)" size="small">
                      {{ getSentimentText(feedback.sentiment) }}
                    </el-tag>
                    <div class="action-buttons">
                      <el-button @click="handleReplyFeedback(feedback)" size="small">
                        回复
                      </el-button>
                      <el-button @click="handleCreateTicketFromFeedback(feedback)" size="small" type="primary">
                        创建工单
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 服务报告 -->
        <el-tab-pane label="服务报告" name="reports">
          <div class="reports-panel">
            <!-- 报告概览 -->
            <div class="reports-overview">
              <div class="overview-header">
                <h3 class="overview-title">服务质量报告</h3>
                <el-button @click="handleGenerateReport" type="primary" size="small">
                  <Document class="w-3 h-3 mr-1" />
                  生成报告
                </el-button>
              </div>
              
              <div class="report-metrics">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="report-metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">工单处理效率</h4>
                      <TrendingUp class="w-5 h-5 text-blue-600" />
                    </div>
                    <div class="metric-chart">
                      <div class="chart-placeholder">
                        <div class="placeholder-content">
                          <BarChart class="w-12 h-12 text-blue-400" />
                          <p class="placeholder-text">工单处理时间图表</p>
                        </div>
                      </div>
                    </div>
                    <div class="metric-summary">
                      <div class="summary-item">
                        <span class="summary-label">平均处理时间:</span>
                        <span class="summary-value">2.5小时</span>
                      </div>
                      <div class="summary-item">
                        <span class="summary-label">本月处理量:</span>
                        <span class="summary-value">248单</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="report-metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">客户满意度趋势</h4>
                      <Star class="w-5 h-5 text-yellow-600" />
                    </div>
                    <div class="metric-chart">
                      <div class="chart-placeholder">
                        <div class="placeholder-content">
                          <TrendingUp class="w-12 h-12 text-yellow-400" />
                          <p class="placeholder-text">满意度趋势图表</p>
                        </div>
                      </div>
                    </div>
                    <div class="metric-summary">
                      <div class="summary-item">
                        <span class="summary-label">当前满意度:</span>
                        <span class="summary-value">4.6/5.0</span>
                      </div>
                      <div class="summary-item">
                        <span class="summary-label">较上月:</span>
                        <span class="summary-value positive">+0.3</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="report-metric-card">
                    <div class="metric-header">
                      <h4 class="metric-title">问题分类统计</h4>
                      <PieChart class="w-5 h-5 text-green-600" />
                    </div>
                    <div class="metric-chart">
                      <div class="chart-placeholder">
                        <div class="placeholder-content">
                          <PieChart class="w-12 h-12 text-green-400" />
                          <p class="placeholder-text">问题分类饼图</p>
                        </div>
                      </div>
                    </div>
                    <div class="metric-summary">
                      <div class="summary-item">
                        <span class="summary-label">技术问题:</span>
                        <span class="summary-value">35%</span>
                      </div>
                      <div class="summary-item">
                        <span class="summary-label">订单问题:</span>
                        <span class="summary-value">28%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 服务团队表现 -->
            <div class="team-performance">
              <div class="performance-header">
                <h3 class="performance-title">服务团队表现</h3>
              </div>
              
              <div class="performance-table">
                <el-table :data="teamPerformanceData" stripe>
                  <el-table-column prop="name" label="客服代表" width="120">
                    <template #default="{ row }">
                      <div class="staff-info">
                        <el-avatar :size="24" :src="row.avatar">
                          {{ row.name.charAt(0) }}
                        </el-avatar>
                        <span class="staff-name">{{ row.name }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ticketsHandled" label="处理工单" width="100" />
                  <el-table-column prop="avgResponseTime" label="平均响应时间" width="120" />
                  <el-table-column prop="customerRating" label="客户评分" width="100">
                    <template #default="{ row }">
                      <el-rate 
                        v-model="row.customerRating" 
                        disabled 
                        size="small"
                        show-score
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="resolutionRate" label="解决率" width="100">
                    <template #default="{ row }">
                      <span :class="getResolutionRateClass(row.resolutionRate)">
                        {{ row.resolutionRate }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="chatSessions" label="在线会话" width="100" />
                  <el-table-column prop="workHours" label="工作时长" width="100" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getStaffStatusTagType(row.status)" size="small">
                        {{ getStaffStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 工单详情对话框 -->
    <el-dialog
      v-model="ticketDetailDialogVisible"
      title="工单详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="ticket-detail-content" v-if="selectedTicket">
        <!-- 工单信息 -->
        <div class="ticket-info">
          <div class="info-header">
            <div class="ticket-basic">
              <h3 class="ticket-title">{{ selectedTicket.title }}</h3>
              <div class="ticket-meta">
                <el-tag :type="getPriorityTagType(selectedTicket.priority)" size="small">
                  {{ getPriorityText(selectedTicket.priority) }}
                </el-tag>
                <el-tag :type="getCategoryTagType(selectedTicket.category)" size="small">
                  {{ getCategoryText(selectedTicket.category) }}
                </el-tag>
                <el-tag :type="getStatusTagType(selectedTicket.status)">
                  {{ getStatusText(selectedTicket.status) }}
                </el-tag>
              </div>
            </div>
            <div class="ticket-actions">
              <el-button @click="handleAssignTicket(selectedTicket)" size="small">
                分配
              </el-button>
              <el-button @click="handleUpdateTicketStatus(selectedTicket)" size="small" type="primary">
                更新状态
              </el-button>
            </div>
          </div>
          
          <div class="info-details">
            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">工单号:</label>
                <span class="detail-value">{{ selectedTicket.ticket_no }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">创建时间:</label>
                <span class="detail-value">{{ selectedTicket.created_at }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">客户:</label>
                <span class="detail-value">{{ selectedTicket.customer.name }} ({{ selectedTicket.customer.company }})</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">负责人:</label>
                <span class="detail-value">{{ selectedTicket.assignee?.name || '待分配' }}</span>
              </div>
            </div>
          </div>
          
          <div class="ticket-description">
            <h4 class="description-title">问题描述</h4>
            <div class="description-content">{{ selectedTicket.description }}</div>
          </div>
        </div>

        <!-- 回复记录 -->
        <div class="ticket-replies">
          <h4 class="replies-title">处理记录</h4>
          <div class="replies-list">
            <div 
              v-for="reply in selectedTicket.replies" 
              :key="reply.id"
              class="reply-item"
            >
              <div class="reply-header">
                <div class="reply-author">
                  <el-avatar :size="24" :src="reply.author.avatar">
                    {{ reply.author.name.charAt(0) }}
                  </el-avatar>
                  <span class="author-name">{{ reply.author.name }}</span>
                  <span class="author-role">({{ reply.author.role === 'customer' ? '客户' : '客服' }})</span>
                </div>
                <div class="reply-time">{{ reply.created_at }}</div>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
            </div>
          </div>
        </div>

        <!-- 添加回复 -->
        <div class="add-reply">
          <h4 class="reply-title">添加回复</h4>
          <el-input
            v-model="ticketReply"
            type="textarea"
            :rows="4"
            placeholder="输入回复内容..."
          />
          <div class="reply-actions">
            <el-checkbox v-model="sendEmailNotification">发送邮件通知客户</el-checkbox>
            <div class="action-buttons">
              <el-button @click="handleAddReply" type="primary">
                添加回复
              </el-button>
              <el-button @click="handleResolveTicket" type="success">
                解决并关闭
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const activeServiceTab = ref('tickets')
const loading = ref(false)

// 服务统计数据
const serviceStats = reactive({
  pendingTickets: 23,
  activeChats: 8,
  satisfaction: 92,
  satisfactionChange: 3.5,
  avgResponseTime: 3.2
})

// 工单筛选
const ticketFilters = reactive({
  status: '',
  category: '',
  priority: '',
  assignee: '',
  keyword: ''
})

// 工单分页
const ticketPagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 选中的工单
const selectedTickets = ref([])

// 工单数据
const ticketData = ref([
  {
    id: 1,
    ticket_no: 'TK202507230001',
    title: '无法登录系统，提示密码错误',
    category: 'technical',
    priority: 'high',
    status: 'pending',
    customer: {
      name: '张工程师',
      company: '中航材料科技有限公司',
      email: '<EMAIL>'
    },
    assignee: null,
    created_at: '2025-07-23 09:30:00',
    updated_at: '2025-07-23 09:30:00',
    description: '用户反映无法登录系统，输入正确的用户名和密码后系统提示密码错误。用户确认密码无误，请协助排查问题。',
    replies: [
      {
        id: 1,
        author: {
          name: '张工程师',
          role: 'customer',
          avatar: null
        },
        content: '系统提示密码错误，但我确认密码是正确的。',
        created_at: '2025-07-23 09:30:00'
      }
    ]
  },
  {
    id: 2,
    ticket_no: 'TK202507230002',
    title: '订单支付失败，资金已扣除',
    category: 'payment',
    priority: 'urgent',
    status: 'processing',
    customer: {
      name: '李经理',
      company: '东方航空器材贸易公司',
      email: '<EMAIL>'
    },
    assignee: {
      name: '王客服',
      avatar: null
    },
    created_at: '2025-07-23 08:15:00',
    updated_at: '2025-07-23 10:20:00',
    description: '客户在支付订单时系统提示支付失败，但银行卡已扣除费用。订单号：ORD202507230015，金额：58万元。',
    replies: [
      {
        id: 1,
        author: {
          name: '李经理',
          role: 'customer'
        },
        content: '支付时提示失败，但钱已经扣了，请尽快处理。',
        created_at: '2025-07-23 08:15:00'
      },
      {
        id: 2,
        author: {
          name: '王客服',
          role: 'staff'
        },
        content: '您好，我已收到您的反馈，正在联系财务部门核实扣款情况，预计1小时内给您回复。',
        created_at: '2025-07-23 10:20:00'
      }
    ]
  }
])

// 聊天会话
const chatSessions = ref([
  {
    id: 1,
    customer: {
      name: '王采购',
      company: '南方航空公司',
      avatar: null
    },
    status: 'online',
    lastMessage: '请问这个航材什么时候能到货？',
    lastMessageTime: new Date(Date.now() - 5 * 60 * 1000),
    unreadCount: 2,
    messages: [
      {
        id: 1,
        sender: 'customer',
        senderName: '王采购',
        content: '你好，我想咨询一下涡轮叶片的库存情况',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        type: 'text'
      },
      {
        id: 2,
        sender: 'staff',
        senderName: '客服小张',
        content: '您好！我来帮您查询一下涡轮叶片的库存。请问您需要的是哪个型号？',
        timestamp: new Date(Date.now() - 28 * 60 * 1000),
        type: 'text'
      },
      {
        id: 3,
        sender: 'customer',
        senderName: '王采购',
        content: 'T2000-A型号，我们需要2片',
        timestamp: new Date(Date.now() - 25 * 60 * 1000),
        type: 'text'
      },
      {
        id: 4,
        sender: 'staff',
        senderName: '客服小张',
        content: '好的，T2000-A型号目前有库存，2片可以满足。预计3-5个工作日可以发货。',
        timestamp: new Date(Date.now() - 20 * 60 * 1000),
        type: 'text'
      },
      {
        id: 5,
        sender: 'customer',
        senderName: '王采购',
        content: '请问这个航材什么时候能到货？',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        type: 'text'
      }
    ]
  },
  {
    id: 2,
    customer: {
      name: '陈工程师',
      company: '海航维修工程',
      avatar: null
    },
    status: 'away',
    lastMessage: '收到，谢谢您的帮助',
    lastMessageTime: new Date(Date.now() - 15 * 60 * 1000),
    unreadCount: 0,
    messages: []
  }
])

// 选中的聊天会话
const selectedChatSession = ref(null)
const chatMessage = ref('')
const chatMessagesRef = ref(null)

// 客户反馈
const feedbackDateRange = ref([
  new Date(2025, 6, 1),
  new Date(2025, 6, 23)
])
const feedbackFilter = ref('')

const feedbackStats = reactive({
  total: 156,
  positive: 128,
  negative: 28,
  avgRating: 4.3
})

const feedbackData = ref([
  {
    id: 1,
    customer: {
      name: '刘总监',
      company: '华东航空公司',
      avatar: null
    },
    rating: 5,
    sentiment: 'positive',
    content: '平台的服务非常好，客服响应及时，航材质量也很可靠。希望能继续保持这个水平。',
    createdAt: '2025-07-23 14:20:00',
    images: null
  },
  {
    id: 2,
    customer: {
      name: '赵经理',
      company: '西南维修中心',
      avatar: null
    },
    rating: 2,
    sentiment: 'negative',
    content: '订单处理太慢了，等了3天才发货。而且客服态度一般，需要改进。',
    createdAt: '2025-07-23 11:45:00',
    images: null
  }
])

// 团队表现数据
const teamPerformanceData = ref([
  {
    id: 1,
    name: '张客服',
    avatar: null,
    ticketsHandled: 45,
    avgResponseTime: '2.3分钟',
    customerRating: 4.8,
    resolutionRate: 96,
    chatSessions: 28,
    workHours: '8小时',
    status: 'online'
  },
  {
    id: 2,
    name: '李客服',
    avatar: null,
    ticketsHandled: 38,
    avgResponseTime: '3.1分钟',
    customerRating: 4.6,
    resolutionRate: 92,
    chatSessions: 22,
    workHours: '8小时',
    status: 'online'
  },
  {
    id: 3,
    name: '王客服',
    avatar: null,
    ticketsHandled: 41,
    avgResponseTime: '2.8分钟',
    customerRating: 4.7,
    resolutionRate: 94,
    chatSessions: 25,
    workHours: '7.5小时',
    status: 'busy'
  }
])

// 对话框状态
const ticketDetailDialogVisible = ref(false)
const selectedTicket = ref(null)
const ticketReply = ref('')
const sendEmailNotification = ref(true)

// 方法
const handleRefreshTickets = () => {
  loading.value = true
  ElMessage.info('正在刷新工单列表...')
  
  setTimeout(() => {
    loading.value = false
    ElMessage.success('工单列表已刷新')
  }, 1500)
}

const handleOpenChat = () => {
  activeServiceTab.value = 'chat'
  ElMessage.info('切换到在线客服')
}

const handleCreateTicket = () => {
  ElMessage.info('创建新工单功能开发中')
}

const handleTabChange = (tabName) => {
  console.log(`切换到标签页: ${tabName}`)
}

const handleSearchTickets = () => {
  ticketPagination.currentPage = 1
  ElMessage.success('搜索完成')
}

const handleResetFilters = () => {
  Object.keys(ticketFilters).forEach(key => {
    ticketFilters[key] = ''
  })
  ElMessage.success('筛选条件已重置')
}

const handleTicketSelectionChange = (selection) => {
  selectedTickets.value = selection
}

const handleBatchAssign = () => {
  if (selectedTickets.value.length === 0) {
    ElMessage.warning('请选择要分配的工单')
    return
  }
  ElMessage.info(`批量分配 ${selectedTickets.value.length} 个工单`)
}

const handleExportTickets = () => {
  ElMessage.info('正在导出工单数据...')
  setTimeout(() => {
    ElMessage.success('工单数据导出完成')
  }, 2000)
}

const handleViewTicket = (ticket) => {
  selectedTicket.value = ticket
  ticketDetailDialogVisible.value = true
}

const handleProcessTicket = (ticket) => {
  ElMessage.info(`处理工单: ${ticket.ticket_no}`)
}

const handleAssignTicket = (ticket) => {
  ElMessage.info(`分配工单: ${ticket.ticket_no}`)
}

const handleTicketAction = (command, ticket) => {
  switch (command) {
    case 'close':
      ElMessage.info(`关闭工单: ${ticket.ticket_no}`)
      break
    case 'escalate':
      ElMessage.info(`升级处理: ${ticket.ticket_no}`)
      break
    case 'duplicate':
      ElMessage.info(`标记重复: ${ticket.ticket_no}`)
      break
    case 'priority':
      ElMessage.info(`调整优先级: ${ticket.ticket_no}`)
      break
  }
}

const handleTicketSizeChange = (size) => {
  ticketPagination.pageSize = size
  ticketPagination.currentPage = 1
}

const handleTicketCurrentChange = (page) => {
  ticketPagination.currentPage = page
}

// 聊天相关方法
const handleRefreshChats = () => {
  ElMessage.success('聊天会话已刷新')
}

const handleSelectChatSession = (session) => {
  selectedChatSession.value = session
  session.unreadCount = 0
  
  // 滚动到底部
  setTimeout(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
    }
  }, 100)
}

const handleSendMessage = () => {
  if (!chatMessage.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }
  
  if (!selectedChatSession.value) {
    ElMessage.warning('请选择一个会话')
    return
  }
  
  const newMessage = {
    id: Date.now(),
    sender: 'staff',
    senderName: '客服小张',
    content: chatMessage.value,
    timestamp: new Date(),
    type: 'text'
  }
  
  selectedChatSession.value.messages.push(newMessage)
  selectedChatSession.value.lastMessage = chatMessage.value
  selectedChatSession.value.lastMessageTime = new Date()
  
  chatMessage.value = ''
  
  // 滚动到底部
  setTimeout(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
    }
  }, 100)
}

const handleTransferChat = () => {
  ElMessage.info('转接会话功能开发中')
}

const handleEndChat = async () => {
  try {
    await ElMessageBox.confirm('确定要结束当前会话吗？', '确认结束', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    selectedChatSession.value = null
    ElMessage.success('会话已结束')
  } catch {
    // 用户取消
  }
}

const handleSendImage = () => {
  ElMessage.info('发送图片功能开发中')
}

const handleSendFile = () => {
  ElMessage.info('发送文件功能开发中')
}

const handleQuickReply = () => {
  ElMessage.info('快捷回复功能开发中')
}

const handlePreviewImage = (imageUrl) => {
  ElMessage.info(`预览图片: ${imageUrl}`)
}

const handleDownloadFile = (message) => {
  ElMessage.info(`下载文件: ${message.fileName}`)
}

// 反馈相关方法
const handleExportFeedback = () => {
  ElMessage.info('正在导出反馈数据...')
  setTimeout(() => {
    ElMessage.success('反馈数据导出完成')
  }, 2000)
}

const handleReplyFeedback = (feedback) => {
  ElMessage.info(`回复客户反馈: ${feedback.customer.name}`)
}

const handleCreateTicketFromFeedback = (feedback) => {
  ElMessage.info(`为客户 ${feedback.customer.name} 创建工单`)
}

// 报告相关方法
const handleGenerateReport = () => {
  ElMessage.info('正在生成服务质量报告...')
  setTimeout(() => {
    ElMessage.success('服务质量报告生成完成')
  }, 3000)
}

// 工单详情相关方法
const handleAddReply = () => {
  if (!ticketReply.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }
  
  const newReply = {
    id: Date.now(),
    author: {
      name: '客服小张',
      role: 'staff'
    },
    content: ticketReply.value,
    created_at: new Date().toLocaleString()
  }
  
  selectedTicket.value.replies.push(newReply)
  ticketReply.value = ''
  
  ElMessage.success('回复已添加')
  
  if (sendEmailNotification.value) {
    ElMessage.info('邮件通知已发送给客户')
  }
}

const handleResolveTicket = async () => {
  try {
    await ElMessageBox.confirm('确定要解决并关闭此工单吗？', '确认解决', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    selectedTicket.value.status = 'resolved'
    ticketDetailDialogVisible.value = false
    ElMessage.success('工单已解决并关闭')
  } catch {
    // 用户取消
  }
}

const handleUpdateTicketStatus = (ticket) => {
  ElMessage.info(`更新工单状态: ${ticket.ticket_no}`)
}

// 工具方法
const getTicketRowClassName = ({ row }) => {
  if (row.priority === 'urgent') return 'urgent-row'
  if (row.priority === 'high') return 'high-row'
  return ''
}

const getPriorityTagType = (priority) => {
  const types = {
    urgent: 'danger',
    high: 'warning',
    medium: 'info',
    low: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getCategoryTagType = (category) => {
  const types = {
    technical: 'primary',
    account: 'success',
    order: 'warning',
    payment: 'danger',
    suggestion: 'info',
    other: ''
  }
  return types[category] || 'info'
}

const getCategoryText = (category) => {
  const texts = {
    technical: '技术问题',
    account: '账户问题',
    order: '订单问题',
    payment: '支付问题',
    suggestion: '功能建议',
    other: '其他问题'
  }
  return texts[category] || category
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    waiting_reply: 'info',
    resolved: 'success',
    closed: ''
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    waiting_reply: '待回复',
    resolved: '已解决',
    closed: '已关闭',
    online: '在线',
    away: '离开',
    offline: '离线'
  }
  return texts[status] || status
}

const getElapsedTime = (createTime) => {
  const now = new Date()
  const created = new Date(createTime)
  const hours = Math.floor((now - created) / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚创建'
  if (hours < 24) return `${hours}小时前`
  return `${Math.floor(hours / 24)}天前`
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60 * 1000) return '刚刚'
  if (diff < 60 * 60 * 1000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / 3600000)}小时前`
  
  return time.toLocaleString()
}

const getSentimentTagType = (sentiment) => {
  const types = {
    positive: 'success',
    negative: 'danger',
    neutral: 'info'
  }
  return types[sentiment] || 'info'
}

const getSentimentText = (sentiment) => {
  const texts = {
    positive: '正面反馈',
    negative: '负面反馈',
    neutral: '中性反馈'
  }
  return texts[sentiment] || sentiment
}

const getResolutionRateClass = (rate) => {
  if (rate >= 95) return 'text-green-600 font-semibold'
  if (rate >= 90) return 'text-yellow-600 font-semibold'
  return 'text-red-600 font-semibold'
}

const getStaffStatusTagType = (status) => {
  const types = {
    online: 'success',
    busy: 'warning',
    away: 'info',
    offline: ''
  }
  return types[status] || 'info'
}

const getStaffStatusText = (status) => {
  const texts = {
    online: '在线',
    busy: '忙碌',
    away: '离开',
    offline: '离线'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  console.log('客户服务中心页面已加载')
  ticketPagination.total = ticketData.value.length
})
</script>

<style scoped>
.customer-service {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-area {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 服务概览 */
.service-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 12px;
  color: #9ca3af;
}

.card-trend.positive {
  color: #059669;
}

.card-trend.neutral {
  color: #6b7280;
}

/* 服务内容区域 */
.service-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

/* 工单筛选 */
.tickets-filter {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

/* 工单表格 */
.tickets-table {
  margin-top: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 工单行样式 */
:deep(.urgent-row) {
  background-color: #fef2f2 !important;
}

:deep(.high-row) {
  background-color: #fffbeb !important;
}

/* 工单内容样式 */
.ticket-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #1f2937;
}

.ticket-meta {
  display: flex;
  gap: 4px;
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-weight: 500;
  color: #1f2937;
}

.customer-company {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 12px;
  color: #6b7280;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.create-time {
  font-size: 13px;
  color: #1f2937;
}

.elapsed-time {
  font-size: 11px;
  color: #ef4444;
  margin-top: 2px;
}

/* 聊天面板 */
.chat-layout {
  display: flex;
  height: 600px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.chat-sidebar {
  width: 300px;
  border-right: 1px solid #e5e7eb;
  background: #fafbfc;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chat-sessions {
  height: calc(100% - 65px);
  overflow-y: auto;
}

.chat-session-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.chat-session-item:hover {
  background-color: #f3f4f6;
}

.chat-session-item.active {
  background-color: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.session-avatar {
  position: relative;
}

.session-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
}

.session-status.online {
  background-color: #10b981;
}

.session-status.away {
  background-color: #f59e0b;
}

.session-status.offline {
  background-color: #6b7280;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.session-company {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.session-last-message {
  font-size: 12px;
  color: #9ca3af;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.session-time {
  font-size: 11px;
  color: #9ca3af;
}

/* 聊天主区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.chat-customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-details {
  display: flex;
  flex-direction: column;
}

.customer-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.online {
  background-color: #10b981;
}

.status-dot.away {
  background-color: #f59e0b;
}

.status-dot.offline {
  background-color: #6b7280;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #fafbfc;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-item.message-sent {
  flex-direction: row-reverse;
}

.message-item.message-sent .message-content {
  align-items: flex-end;
}

.message-item.message-sent .message-body {
  background: #3b82f6;
  color: white;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.message-sender {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.message-time {
  font-size: 11px;
  color: #9ca3af;
}

.message-body {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px 12px;
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
}

.message-image img {
  max-width: 200px;
  border-radius: 4px;
  cursor: pointer;
}

.message-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-size: 14px;
  color: #1f2937;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
}

.chat-input {
  border-top: 1px solid #e5e7eb;
  background: white;
}

.input-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 8px;
}

.input-area {
  padding: 16px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-tip {
  font-size: 12px;
  color: #9ca3af;
}

.chat-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #6b7280;
  margin-top: 12px;
}

/* 反馈面板 */
.feedback-stats {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.feedback-stat-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #e5e7eb;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

/* 反馈列表 */
.feedback-list {
  margin-top: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.list-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.feedback-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feedback-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.feedback-item.positive {
  border-left: 4px solid #10b981;
}

.feedback-item.negative {
  border-left: 4px solid #ef4444;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.feedback-customer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.feedback-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.feedback-time {
  font-size: 12px;
  color: #9ca3af;
}

.feedback-content {
  margin-bottom: 16px;
}

.feedback-text {
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  margin-bottom: 12px;
}

.feedback-images {
  display: flex;
  gap: 8px;
}

.feedback-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #e5e7eb;
}

.feedback-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 报告面板 */
.reports-overview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.overview-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.report-metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.metric-chart {
  height: 120px;
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background: #fafbfc;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.placeholder-text {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
}

.metric-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 12px;
  color: #6b7280;
}

.summary-value {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
}

.summary-value.positive {
  color: #10b981;
}

/* 团队表现 */
.team-performance {
  margin-top: 24px;
}

.performance-header {
  margin-bottom: 16px;
}

.performance-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.staff-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.staff-name {
  font-size: 14px;
  color: #1f2937;
}

/* 工单详情对话框 */
.ticket-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.ticket-info {
  margin-bottom: 24px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.ticket-basic {
  flex: 1;
}

.ticket-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.ticket-meta {
  display: flex;
  gap: 8px;
}

.ticket-actions {
  display: flex;
  gap: 8px;
}

.info-details {
  background: #f9fafb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.detail-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
}

.ticket-description {
  margin-bottom: 24px;
}

.description-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.description-content {
  font-size: 14px;
  line-height: 1.5;
  color: #4b5563;
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
}

/* 回复记录 */
.ticket-replies {
  margin-bottom: 24px;
}

.replies-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.replies-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.reply-item {
  background: #f9fafb;
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid #e5e7eb;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.author-role {
  font-size: 12px;
  color: #6b7280;
}

.reply-time {
  font-size: 12px;
  color: #9ca3af;
}

.reply-content {
  font-size: 14px;
  line-height: 1.4;
  color: #4b5563;
}

/* 添加回复 */
.add-reply {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.reply-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.reply-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-service {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chat-layout {
    flex-direction: column;
    height: auto;
  }
  
  .chat-sidebar {
    width: 100%;
    height: 200px;
  }
  
  .report-metrics {
    grid-template-columns: 1fr;
  }
  
  .info-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .detail-row {
    grid-template-columns: 1fr;
  }
  
  .reply-actions {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>