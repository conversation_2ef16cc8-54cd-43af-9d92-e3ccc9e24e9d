<!--
平台数据统计和业务报表页面
版本: 1.0
创建时间: 2025-07-23

平台员工专用的数据统计分析和业务报表生成界面
特点：
1. 多维度数据统计分析
2. 可视化图表展示
3. 自定义报表生成
4. 数据导出功能
5. 实时数据监控
-->

<template>
  <div class="business-reports">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">业务数据报表</h1>
          <p class="page-subtitle">全面分析平台业务数据，生成专业的统计报表和决策支持</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshData">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleScheduleReport">
            <Calendar class="w-4 h-4 mr-2" />
            定时报表
          </el-button>
          <el-button @click="handleCustomReport" type="primary">
            <Plus class="w-4 h-4 mr-2" />
            自定义报表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="key-metrics">
      <div class="metrics-header">
        <h2 class="metrics-title">核心业务指标</h2>
        <el-date-picker
          v-model="metricsDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        />
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="metric-card primary">
          <div class="metric-icon">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="metric-content">
            <div class="metric-value">¥{{ formatCurrency(keyMetrics.totalRevenue) }}</div>
            <div class="metric-label">总交易额</div>
            <div class="metric-change positive">
              <ArrowUp class="w-3 h-3" />
              +{{ keyMetrics.revenueGrowth }}%
            </div>
          </div>
        </div>
        
        <div class="metric-card success">
          <div class="metric-icon">
            <Package class="w-8 h-8" />
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ keyMetrics.totalOrders.toLocaleString() }}</div>
            <div class="metric-label">订单总数</div>
            <div class="metric-change positive">
              <ArrowUp class="w-3 h-3" />
              +{{ keyMetrics.orderGrowth }}%
            </div>
          </div>
        </div>
        
        <div class="metric-card warning">
          <div class="metric-icon">
            <Users class="w-8 h-8" />
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ keyMetrics.activeUsers.toLocaleString() }}</div>
            <div class="metric-label">活跃用户</div>
            <div class="metric-change positive">
              <ArrowUp class="w-3 h-3" />
              +{{ keyMetrics.userGrowth }}%
            </div>
          </div>
        </div>
        
        <div class="metric-card info">
          <div class="metric-icon">
            <Building class="w-8 h-8" />
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ keyMetrics.activeCompanies }}</div>
            <div class="metric-label">活跃企业</div>
            <div class="metric-change neutral">
              <Minus class="w-3 h-3" />
              {{ keyMetrics.companyGrowth }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报表主体内容 -->
    <div class="reports-content">
      <el-tabs v-model="activeReportTab" @tab-change="handleTabChange">
        <!-- 业务概览报表 -->
        <el-tab-pane label="业务概览" name="overview">
          <div class="overview-report">
            <!-- 趋势图表 -->
            <div class="chart-section">
              <div class="section-header">
                <h3 class="section-title">业务趋势分析</h3>
                <div class="chart-controls">
                  <el-select v-model="trendPeriod" size="small">
                    <el-option label="最近7天" value="7d" />
                    <el-option label="最近30天" value="30d" />
                    <el-option label="最近90天" value="90d" />
                    <el-option label="最近1年" value="1y" />
                  </el-select>
                  <el-button @click="handleExportChart" size="small">
                    <Download class="w-3 h-3 mr-1" />
                    导出图表
                  </el-button>
                </div>
              </div>
              
              <div class="charts-grid">
                <div class="chart-container">
                  <div class="chart-header">
                    <h4 class="chart-title">交易额趋势</h4>
                    <el-tag type="success" size="small">{{ trendPeriod }}</el-tag>
                  </div>
                  <div class="chart-content">
                    <!-- 这里可以集成 ECharts 或其他图表库 -->
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <TrendingUp class="w-16 h-16 text-blue-400" />
                        <p class="placeholder-text">交易额趋势图</p>
                        <div class="chart-stats">
                          <span class="stat-item">峰值: ¥{{ formatCurrency(chartData.revenue.max) }}</span>
                          <span class="stat-item">均值: ¥{{ formatCurrency(chartData.revenue.avg) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="chart-container">
                  <div class="chart-header">
                    <h4 class="chart-title">订单量趋势</h4>
                    <el-tag type="primary" size="small">{{ trendPeriod }}</el-tag>
                  </div>
                  <div class="chart-content">
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <BarChart class="w-16 h-16 text-green-400" />
                        <p class="placeholder-text">订单量趋势图</p>
                        <div class="chart-stats">
                          <span class="stat-item">峰值: {{ chartData.orders.max }} 单</span>
                          <span class="stat-item">均值: {{ chartData.orders.avg }} 单</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分类统计 -->
            <div class="category-stats">
              <div class="section-header">
                <h3 class="section-title">分类统计分析</h3>
                <el-button @click="handleExportCategoryStats" size="small">
                  <Document class="w-3 h-3 mr-1" />
                  导出统计
                </el-button>
              </div>
              
              <div class="stats-grid">
                <div class="stats-card">
                  <div class="stats-header">
                    <h4 class="stats-title">按公司类型统计</h4>
                    <PieChart class="w-5 h-5 text-blue-600" />
                  </div>
                  <div class="stats-content">
                    <div class="stats-list">
                      <div 
                        v-for="item in companyTypeStats" 
                        :key="item.type"
                        class="stats-item"
                      >
                        <div class="stats-item-header">
                          <span class="stats-label">{{ getCompanyTypeText(item.type) }}</span>
                          <span class="stats-value">{{ item.count }}</span>
                        </div>
                        <div class="stats-progress">
                          <el-progress 
                            :percentage="item.percentage" 
                            :stroke-width="6"
                            :show-text="false"
                            :color="getCompanyTypeColor(item.type)"
                          />
                        </div>
                        <div class="stats-details">
                          <span class="stats-amount">¥{{ formatCurrency(item.revenue) }}</span>
                          <span class="stats-percent">{{ item.percentage }}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="stats-card">
                  <div class="stats-header">
                    <h4 class="stats-title">热门航材排行</h4>
                    <Trophy class="w-5 h-5 text-yellow-600" />
                  </div>
                  <div class="stats-content">
                    <div class="ranking-list">
                      <div 
                        v-for="(item, index) in topMaterials" 
                        :key="item.id"
                        class="ranking-item"
                      >
                        <div class="ranking-number" :class="getRankingClass(index)">
                          {{ index + 1 }}
                        </div>
                        <div class="ranking-content">
                          <div class="ranking-name">{{ item.name }}</div>
                          <div class="ranking-stats">
                            <span class="stat">{{ item.orderCount }} 单</span>
                            <span class="stat">¥{{ formatCurrency(item.revenue) }}</span>
                          </div>
                        </div>
                        <div class="ranking-trend">
                          <component 
                            :is="getTrendIcon(item.trend)" 
                            :class="getTrendClass(item.trend)"
                            class="w-4 h-4"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="stats-card">
                  <div class="stats-header">
                    <h4 class="stats-title">地区分布统计</h4>
                    <MapPin class="w-5 h-5 text-green-600" />
                  </div>
                  <div class="stats-content">
                    <div class="region-stats">
                      <div 
                        v-for="region in regionStats" 
                        :key="region.name"
                        class="region-item"
                      >
                        <div class="region-header">
                          <span class="region-name">{{ region.name }}</span>
                          <span class="region-count">{{ region.companies }}家</span>
                        </div>
                        <div class="region-progress">
                          <el-progress 
                            :percentage="region.percentage" 
                            :stroke-width="4"
                            :show-text="false"
                            color="#3b82f6"
                          />
                        </div>
                        <div class="region-details">
                          <span class="region-revenue">¥{{ formatCurrency(region.revenue) }}</span>
                          <span class="region-growth" :class="getGrowthClass(region.growth)">
                            {{ region.growth > 0 ? '+' : '' }}{{ region.growth }}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 财务报表 -->
        <el-tab-pane label="财务分析" name="financial">
          <div class="financial-report">
            <!-- 财务概览 -->
            <div class="financial-overview">
              <div class="section-header">
                <h3 class="section-title">财务数据概览</h3>
                <div class="financial-controls">
                  <el-date-picker
                    v-model="financialPeriod"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                  />
                  <el-button @click="handleGenerateFinancialReport" type="primary" size="small">
                    生成报告
                  </el-button>
                </div>
              </div>
              
              <div class="financial-cards">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="financial-card revenue">
                    <div class="card-header">
                      <h4 class="card-title">营业收入</h4>
                      <DollarSign class="w-6 h-6 text-green-600" />
                    </div>
                    <div class="card-content">
                      <div class="card-value">¥{{ formatCurrency(financialData.totalRevenue) }}</div>
                      <div class="card-comparison">
                        <span class="comparison-label">较上期</span>
                        <span class="comparison-value positive">+{{ financialData.revenueGrowth }}%</span>
                      </div>
                      <div class="card-details">
                        <div class="detail-item">
                          <span class="detail-label">平台服务费:</span>
                          <span class="detail-value">¥{{ formatCurrency(financialData.serviceFee) }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">交易手续费:</span>
                          <span class="detail-value">¥{{ formatCurrency(financialData.transactionFee) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="financial-card cost">
                    <div class="card-header">
                      <h4 class="card-title">运营成本</h4>
                      <TrendingDown class="w-6 h-6 text-red-600" />
                    </div>
                    <div class="card-content">
                      <div class="card-value">¥{{ formatCurrency(financialData.totalCost) }}</div>
                      <div class="card-comparison">
                        <span class="comparison-label">较上期</span>
                        <span class="comparison-value negative">+{{ financialData.costGrowth }}%</span>
                      </div>
                      <div class="card-details">
                        <div class="detail-item">
                          <span class="detail-label">技术投入:</span>
                          <span class="detail-value">¥{{ formatCurrency(financialData.techCost) }}</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">运营费用:</span>
                          <span class="detail-value">¥{{ formatCurrency(financialData.operationCost) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="financial-card profit">
                    <div class="card-header">
                      <h4 class="card-title">净利润</h4>
                      <Target class="w-6 h-6 text-blue-600" />
                    </div>
                    <div class="card-content">
                      <div class="card-value">¥{{ formatCurrency(financialData.netProfit) }}</div>
                      <div class="card-comparison">
                        <span class="comparison-label">较上期</span>
                        <span class="comparison-value positive">+{{ financialData.profitGrowth }}%</span>
                      </div>
                      <div class="card-details">
                        <div class="detail-item">
                          <span class="detail-label">利润率:</span>
                          <span class="detail-value">{{ financialData.profitMargin }}%</span>
                        </div>
                        <div class="detail-item">
                          <span class="detail-label">ROI:</span>
                          <span class="detail-value">{{ financialData.roi }}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 收入分析 -->
            <div class="revenue-analysis">
              <div class="section-header">
                <h3 class="section-title">收入结构分析</h3>
              </div>
              
              <div class="analysis-grid">
                <div class="analysis-chart">
                  <div class="chart-header">
                    <h4 class="chart-title">收入来源分布</h4>
                  </div>
                  <div class="chart-content">
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <PieChart class="w-16 h-16 text-purple-400" />
                        <p class="placeholder-text">收入来源饼图</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="analysis-table">
                  <div class="table-header">
                    <h4 class="table-title">收入明细</h4>
                  </div>
                  <div class="revenue-breakdown">
                    <div 
                      v-for="item in revenueBreakdown" 
                      :key="item.type"
                      class="breakdown-item"
                    >
                      <div class="breakdown-header">
                        <span class="breakdown-label">{{ item.label }}</span>
                        <span class="breakdown-amount">¥{{ formatCurrency(item.amount) }}</span>
                      </div>
                      <div class="breakdown-progress">
                        <el-progress 
                          :percentage="item.percentage" 
                          :stroke-width="6"
                          :show-text="false"
                          :color="item.color"
                        />
                      </div>
                      <div class="breakdown-details">
                        <span class="breakdown-percent">{{ item.percentage }}%</span>
                        <span class="breakdown-change" :class="getChangeClass(item.change)">
                          {{ item.change > 0 ? '+' : '' }}{{ item.change }}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 用户分析报表 -->
        <el-tab-pane label="用户分析" name="user">
          <div class="user-report">
            <!-- 用户概览 -->
            <div class="user-overview">
              <div class="section-header">
                <h3 class="section-title">用户数据分析</h3>
                <el-button @click="handleExportUserReport" size="small">
                  <Download class="w-3 h-3 mr-1" />
                  导出用户报告
                </el-button>
              </div>
              
              <div class="user-metrics">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div class="user-metric-card">
                    <div class="metric-icon">
                      <UserPlus class="w-6 h-6 text-blue-600" />
                    </div>
                    <div class="metric-data">
                      <div class="metric-number">{{ userMetrics.newUser }}</div>
                      <div class="metric-title">新增用户</div>
                    </div>
                  </div>
                  
                  <div class="user-metric-card">
                    <div class="metric-icon">
                      <Monitor class="w-6 h-6 text-green-600" />
                    </div>
                    <div class="metric-data">
                      <div class="metric-number">{{ userMetrics.activeUser }}</div>
                      <div class="metric-title">活跃用户</div>
                    </div>
                  </div>
                  
                  <div class="user-metric-card">
                    <div class="metric-icon">
                      <Clock class="w-6 h-6 text-yellow-600" />
                    </div>
                    <div class="metric-data">
                      <div class="metric-number">{{ userMetrics.avgSessionTime }}分</div>
                      <div class="metric-title">平均会话时长</div>
                    </div>
                  </div>
                  
                  <div class="user-metric-card">
                    <div class="metric-icon">
                      <Repeat class="w-6 h-6 text-purple-600" />
                    </div>
                    <div class="metric-data">
                      <div class="metric-number">{{ userMetrics.returnRate }}%</div>
                      <div class="metric-title">用户留存率</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 用户行为分析 -->
            <div class="user-behavior">
              <div class="section-header">
                <h3 class="section-title">用户行为分析</h3>
              </div>
              
              <div class="behavior-grid">
                <div class="behavior-chart">
                  <div class="chart-header">
                    <h4 class="chart-title">用户活跃时段分布</h4>
                  </div>
                  <div class="chart-content">
                    <div class="chart-placeholder">
                      <div class="placeholder-content">
                        <BarChart class="w-16 h-16 text-indigo-400" />
                        <p class="placeholder-text">活跃时段柱状图</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="behavior-stats">
                  <div class="stats-header">
                    <h4 class="stats-title">行为统计</h4>
                  </div>
                  <div class="behavior-list">
                    <div 
                      v-for="behavior in userBehaviors" 
                      :key="behavior.action"
                      class="behavior-item"
                    >
                      <div class="behavior-info">
                        <span class="behavior-name">{{ behavior.name }}</span>
                        <span class="behavior-count">{{ behavior.count.toLocaleString() }} 次</span>
                      </div>
                      <div class="behavior-progress">
                        <el-progress 
                          :percentage="behavior.percentage" 
                          :stroke-width="4"
                          :show-text="false"
                          color="#6366f1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 自定义报表 -->
        <el-tab-pane label="自定义报表" name="custom">
          <div class="custom-report">
            <div class="report-builder">
              <div class="section-header">
                <h3 class="section-title">报表构建器</h3>
                <el-button @click="handleSaveReportTemplate" type="primary" size="small">
                  保存模板
                </el-button>
              </div>
              
              <div class="builder-form">
                <div class="form-section">
                  <h4 class="form-title">报表基本信息</h4>
                  <div class="form-grid">
                    <div class="form-item">
                      <label class="form-label">报表名称</label>
                      <el-input v-model="customReport.name" placeholder="输入报表名称" />
                    </div>
                    <div class="form-item">
                      <label class="form-label">报表类型</label>
                      <el-select v-model="customReport.type" placeholder="选择报表类型">
                        <el-option label="业务分析报表" value="business" />
                        <el-option label="财务分析报表" value="financial" />
                        <el-option label="用户分析报表" value="user" />
                        <el-option label="运营分析报表" value="operation" />
                      </el-select>
                    </div>
                    <div class="form-item">
                      <label class="form-label">时间范围</label>
                      <el-date-picker
                        v-model="customReport.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
                
                <div class="form-section">
                  <h4 class="form-title">数据指标选择</h4>
                  <div class="metrics-selection">
                    <div class="metrics-categories">
                      <div 
                        v-for="category in metricsCategories" 
                        :key="category.name"
                        class="category-group"
                      >
                        <div class="category-header">
                          <h5 class="category-title">{{ category.label }}</h5>
                          <el-checkbox 
                            :indeterminate="isCategoryIndeterminate(category)"
                            v-model="category.checked"
                            @change="handleCategoryCheck(category)"
                          >
                            全选
                          </el-checkbox>
                        </div>
                        <div class="category-metrics">
                          <el-checkbox-group v-model="customReport.selectedMetrics">
                            <el-checkbox 
                              v-for="metric in category.metrics" 
                              :key="metric.key"
                              :label="metric.key"
                              class="metric-checkbox"
                            >
                              <span class="metric-name">{{ metric.name }}</span>
                              <span class="metric-description">{{ metric.description }}</span>
                            </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="form-section">
                  <h4 class="form-title">图表配置</h4>
                  <div class="chart-config">
                    <div class="config-grid">
                      <div class="config-item">
                        <label class="config-label">图表类型</label>
                        <el-select v-model="customReport.chartType" placeholder="选择图表类型">
                          <el-option label="线图" value="line" />
                          <el-option label="柱状图" value="bar" />
                          <el-option label="饼图" value="pie" />
                          <el-option label="面积图" value="area" />
                          <el-option label="散点图" value="scatter" />
                        </el-select>
                      </div>
                      <div class="config-item">
                        <label class="config-label">数据聚合</label>
                        <el-select v-model="customReport.aggregation" placeholder="选择聚合方式">
                          <el-option label="按天" value="daily" />
                          <el-option label="按周" value="weekly" />
                          <el-option label="按月" value="monthly" />
                          <el-option label="按季度" value="quarterly" />
                        </el-select>
                      </div>
                      <div class="config-item">
                        <label class="config-label">排序方式</label>
                        <el-select v-model="customReport.sortBy" placeholder="选择排序方式">
                          <el-option label="按时间升序" value="time_asc" />
                          <el-option label="按时间降序" value="time_desc" />
                          <el-option label="按数值升序" value="value_asc" />
                          <el-option label="按数值降序" value="value_desc" />
                        </el-select>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="form-actions">
                  <el-button @click="handlePreviewReport" size="large">
                    <Eye class="w-4 h-4 mr-2" />
                    预览报表
                  </el-button>
                  <el-button @click="handleGenerateReport" type="primary" size="large">
                    <Document class="w-4 h-4 mr-2" />
                    生成报表
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 报表模板 -->
            <div class="report-templates">
              <div class="section-header">
                <h3 class="section-title">报表模板</h3>
                <el-button @click="handleImportTemplate" size="small">
                  <Upload class="w-3 h-3 mr-1" />
                  导入模板
                </el-button>
              </div>
              
              <div class="templates-grid">
                <div 
                  v-for="template in reportTemplates" 
                  :key="template.id"
                  class="template-card"
                  @click="handleLoadTemplate(template)"
                >
                  <div class="template-header">
                    <h4 class="template-name">{{ template.name }}</h4>
                    <el-tag :type="getTemplateTagType(template.type)" size="small">
                      {{ getTemplateTypeText(template.type) }}
                    </el-tag>
                  </div>
                  <div class="template-description">{{ template.description }}</div>
                  <div class="template-meta">
                    <span class="template-metrics">{{ template.metricsCount }} 个指标</span>
                    <span class="template-usage">使用 {{ template.usageCount }} 次</span>
                  </div>
                  <div class="template-actions">
                    <el-button @click.stop="handleEditTemplate(template)" size="small" type="primary">
                      编辑
                    </el-button>
                    <el-button @click.stop="handleDeleteTemplate(template)" size="small" type="danger">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 报表预览对话框 -->
    <el-dialog
      v-model="reportPreviewDialogVisible"
      title="报表预览"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="report-preview-content">
        <div class="preview-header">
          <h3 class="preview-title">{{ customReport.name || '未命名报表' }}</h3>
          <div class="preview-meta">
            <span class="meta-item">类型: {{ getReportTypeText(customReport.type) }}</span>
            <span class="meta-item">时间范围: {{ formatDateRange(customReport.dateRange) }}</span>
            <span class="meta-item">指标数量: {{ customReport.selectedMetrics.length }}</span>
          </div>
        </div>
        
        <div class="preview-content">
          <div class="preview-chart">
            <div class="chart-placeholder">
              <div class="placeholder-content">
                <component :is="getChartIcon(customReport.chartType)" class="w-20 h-20 text-gray-400" />
                <p class="placeholder-text">{{ customReport.name || '报表' }}预览图表</p>
                <p class="placeholder-subtitle">{{ getChartTypeText(customReport.chartType) }}</p>
              </div>
            </div>
          </div>
          
          <div class="preview-metrics">
            <h4 class="metrics-title">选中的指标</h4>
            <div class="metrics-list">
              <el-tag 
                v-for="metricKey in customReport.selectedMetrics" 
                :key="metricKey"
                class="metric-tag"
              >
                {{ getMetricName(metricKey) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reportPreviewDialogVisible = false">关闭</el-button>
          <el-button @click="handleExportReport" type="success">
            <Download class="w-4 h-4 mr-2" />
            导出报表
          </el-button>
          <el-button @click="handleGenerateReport" type="primary">
            <Check class="w-4 h-4 mr-2" />
            确认生成
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 页面状态
const activeReportTab = ref('overview')
const loading = ref(false)

// 日期范围
const metricsDateRange = ref([
  new Date(2025, 6, 1), // 2025-07-01
  new Date(2025, 6, 23)  // 2025-07-23
])
const financialPeriod = ref([
  new Date(2025, 5, 1), // 2025-06-01
  new Date(2025, 6, 31)  // 2025-07-31
])

// 趋势期间
const trendPeriod = ref('30d')

// 核心指标数据
const keyMetrics = reactive({
  totalRevenue: 45680000,
  revenueGrowth: 15.8,
  totalOrders: 15620,
  orderGrowth: 12.3,
  activeUsers: 2840,
  userGrowth: 8.7,
  activeCompanies: 156,
  companyGrowth: 2.1
})

// 图表数据
const chartData = reactive({
  revenue: {
    max: 2800000,
    avg: 1520000
  },
  orders: {
    max: 680,
    avg: 420
  }
})

// 公司类型统计
const companyTypeStats = ref([
  {
    type: 'supplier',
    count: 45,
    percentage: 28.8,
    revenue: 15600000
  },
  {
    type: 'distributor',
    count: 38,
    percentage: 24.4,
    revenue: 18200000
  },
  {
    type: 'airline',
    count: 52,
    percentage: 33.3,
    revenue: 8900000
  },
  {
    type: 'maintenance',
    count: 21,
    percentage: 13.5,
    revenue: 2980000
  }
])

// 热门航材
const topMaterials = ref([
  {
    id: 1,
    name: '涡轮叶片 T2000-A',
    orderCount: 245,
    revenue: 8600000,
    trend: 'up'
  },
  {
    id: 2,
    name: '燃油泵组件 FP-8800',
    orderCount: 198,
    revenue: 6200000,
    trend: 'up'
  },
  {
    id: 3,
    name: '液压缸 HC-600',
    orderCount: 156,
    revenue: 4800000,
    trend: 'down'
  },
  {
    id: 4,
    name: '起落架部件 LG-450',
    orderCount: 134,
    revenue: 7200000,
    trend: 'neutral'
  },
  {
    id: 5,
    name: '航电设备 AV-200',
    orderCount: 112,
    revenue: 3400000,
    trend: 'up'
  }
])

// 地区统计
const regionStats = ref([
  {
    name: '华东地区',
    companies: 68,
    percentage: 43.6,
    revenue: 19800000,
    growth: 18.5
  },
  {
    name: '华北地区',
    companies: 42,
    percentage: 26.9,
    revenue: 12600000,
    growth: 12.8
  },
  {
    name: '华南地区',
    companies: 31,
    percentage: 19.9,
    revenue: 8900000,
    growth: 15.2
  },
  {
    name: '其他地区',
    companies: 15,
    percentage: 9.6,
    revenue: 4380000,
    growth: -2.1
  }
])

// 财务数据
const financialData = reactive({
  totalRevenue: 45680000,
  revenueGrowth: 15.8,
  totalCost: 32400000,
  costGrowth: 8.2,
  netProfit: 13280000,
  profitGrowth: 28.6,
  profitMargin: 29.1,
  roi: 24.8,
  serviceFee: 3680000,
  transactionFee: 2100000,
  techCost: 8900000,
  operationCost: 12800000
})

// 收入分解
const revenueBreakdown = ref([
  {
    type: 'transaction_fee',
    label: '交易手续费',
    amount: 28600000,
    percentage: 62.6,
    change: 18.5,
    color: '#3b82f6'
  },
  {
    type: 'service_fee',
    label: '平台服务费',
    amount: 12800000,
    percentage: 28.0,
    change: 12.3,
    color: '#10b981'
  },
  {
    type: 'premium_fee',
    label: '增值服务费',
    amount: 3280000,
    percentage: 7.2,
    change: 25.8,
    color: '#f59e0b'
  },
  {
    type: 'other_revenue',
    label: '其他收入',
    amount: 1000000,
    percentage: 2.2,
    change: -5.2,
    color: '#8b5cf6'
  }
])

// 用户指标
const userMetrics = reactive({
  newUsers: 485,
  activeUsers: 2840,
  avgSessionTime: 26,
  returnRate: 68.5
})

// 用户行为
const userBehaviors = ref([
  {
    action: 'search_materials',
    name: '搜索航材',
    count: 45680,
    percentage: 35.2
  },
  {
    action: 'view_details',
    name: '查看详情',
    count: 32450,
    percentage: 25.0
  },
  {
    action: 'create_order',
    name: '创建订单',
    count: 18920,
    percentage: 14.6
  },
  {
    action: 'contact_supplier',
    name: '联系供应商',
    count: 15680,
    percentage: 12.1
  },
  {
    action: 'share_materials',
    name: '发布共享',
    count: 12340,
    percentage: 9.5
  },
  {
    action: 'other_actions',
    name: '其他操作',
    count: 4830,
    percentage: 3.6
  }
])

// 自定义报表
const customReport = reactive({
  name: '',
  type: '',
  dateRange: null,
  selectedMetrics: [],
  chartType: 'line',
  aggregation: 'daily',
  sortBy: 'time_asc'
})

// 指标分类
const metricsCategories = ref([
  {
    name: 'business',
    label: '业务指标',
    checked: false,
    metrics: [
      { key: 'total_revenue', name: '总交易额', description: '平台所有交易的总金额' },
      { key: 'order_count', name: '订单数量', description: '平台订单总数' },
      { key: 'avg_order_value', name: '平均订单价值', description: '平均每个订单的金额' },
      { key: 'completion_rate', name: '订单完成率', description: '成功完成的订单占比' }
    ]
  },
  {
    name: 'user',
    label: '用户指标',
    checked: false,
    metrics: [
      { key: 'active_users', name: '活跃用户', description: '月活跃用户数' },
      { key: 'new_users', name: '新增用户', description: '新注册用户数' },
      { key: 'user_retention', name: '用户留存', description: '用户留存率' },
      { key: 'session_duration', name: '会话时长', description: '平均会话时长' }
    ]
  },
  {
    name: 'financial',
    label: '财务指标',
    checked: false,
    metrics: [
      { key: 'platform_revenue', name: '平台收入', description: '平台服务费和手续费收入' },
      { key: 'profit_margin', name: '利润率', description: '净利润率' },
      { key: 'operating_cost', name: '运营成本', description: '平台运营成本' },
      { key: 'roi', name: '投资回报率', description: 'ROI指标' }
    ]
  }
])

// 报表模板
const reportTemplates = ref([
  {
    id: 1,
    name: '月度业务报表',
    type: 'business',
    description: '包含订单量、交易额、用户活跃度等核心业务指标',
    metricsCount: 8,
    usageCount: 45
  },
  {
    id: 2,
    name: '财务分析报表',
    type: 'financial',
    description: '平台收入、成本、利润等财务数据分析',
    metricsCount: 6,
    usageCount: 23
  },
  {
    id: 3,
    name: '用户行为报表',
    type: 'user',
    description: '用户注册、活跃、留存等行为数据分析',
    metricsCount: 5,
    usageCount: 18
  }
])

// 对话框状态
const reportPreviewDialogVisible = ref(false)

// 计算属性
const isCategoryIndeterminate = (category) => {
  const selectedInCategory = category.metrics.filter(metric => 
    customReport.selectedMetrics.includes(metric.key)
  ).length
  return selectedInCategory > 0 && selectedInCategory < category.metrics.length
}

// 方法
const handleRefreshData = () => {
  loading.value = true
  ElMessage.info('正在刷新数据...')
  
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1500)
}

const handleScheduleReport = () => {
  ElMessage.info('定时报表功能开发中')
}

const handleCustomReport = () => {
  activeReportTab.value = 'custom'
  ElMessage.info('切换到自定义报表')
}

const handleDateRangeChange = () => {
  ElMessage.success('时间范围已更新')
}

const handleTabChange = (tabName) => {
  console.log(`切换到标签页: ${tabName}`)
}

const handleExportChart = () => {
  ElMessage.info('正在导出图表...')
  setTimeout(() => {
    ElMessage.success('图表导出完成')
  }, 2000)
}

const handleExportCategoryStats = () => {
  ElMessage.info('正在导出分类统计...')
  setTimeout(() => {
    ElMessage.success('统计数据导出完成')
  }, 2000)
}

const handleGenerateFinancialReport = () => {
  ElMessage.info('正在生成财务报告...')
  setTimeout(() => {
    ElMessage.success('财务报告生成完成')
  }, 3000)
}

const handleExportUserReport = () => {
  ElMessage.info('正在导出用户报告...')
  setTimeout(() => {
    ElMessage.success('用户报告导出完成')
  }, 2000)
}

const handleCategoryCheck = (category) => {
  if (category.checked) {
    // 选中该分类的所有指标
    category.metrics.forEach(metric => {
      if (!customReport.selectedMetrics.includes(metric.key)) {
        customReport.selectedMetrics.push(metric.key)
      }
    })
  } else {
    // 取消选中该分类的所有指标
    category.metrics.forEach(metric => {
      const index = customReport.selectedMetrics.indexOf(metric.key)
      if (index > -1) {
        customReport.selectedMetrics.splice(index, 1)
      }
    })
  }
}

const handlePreviewReport = () => {
  if (!customReport.name) {
    ElMessage.warning('请输入报表名称')
    return
  }
  if (customReport.selectedMetrics.length === 0) {
    ElMessage.warning('请至少选择一个指标')
    return
  }
  reportPreviewDialogVisible.value = true
}

const handleGenerateReport = () => {
  if (!customReport.name) {
    ElMessage.warning('请输入报表名称')
    return
  }
  if (customReport.selectedMetrics.length === 0) {
    ElMessage.warning('请至少选择一个指标')
    return
  }
  
  ElMessage.info('正在生成自定义报表...')
  setTimeout(() => {
    ElMessage.success('自定义报表生成完成')
    reportPreviewDialogVisible.value = false
  }, 3000)
}

const handleSaveReportTemplate = () => {
  if (!customReport.name) {
    ElMessage.warning('请输入报表名称')
    return
  }
  
  ElMessage.success('报表模板已保存')
}

const handleImportTemplate = () => {
  ElMessage.info('导入模板功能开发中')
}

const handleLoadTemplate = (template) => {
  customReport.name = template.name
  customReport.type = template.type
  ElMessage.success(`已加载模板: ${template.name}`)
}

const handleEditTemplate = (template) => {
  ElMessage.info(`编辑模板: ${template.name}`)
}

const handleDeleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = reportTemplates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      reportTemplates.value.splice(index, 1)
      ElMessage.success('模板已删除')
    }
  } catch {
    // 用户取消
  }
}

const handleExportReport = () => {
  ElMessage.info('正在导出报表...')
  setTimeout(() => {
    ElMessage.success('报表导出完成')
  }, 2000)
}

// 工具方法
const formatCurrency = (amount) => {
  if (!amount) return '0'
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toLocaleString()
}

const getCompanyTypeText = (type) => {
  const texts = {
    supplier: '供应商',
    distributor: '分销商',
    airline: '航空公司',
    maintenance: '维修公司'
  }
  return texts[type] || type
}

const getCompanyTypeColor = (type) => {
  const colors = {
    supplier: '#3b82f6',
    distributor: '#10b981',
    airline: '#f59e0b',
    maintenance: '#8b5cf6'
  }
  return colors[type] || '#6b7280'
}

const getRankingClass = (index) => {
  if (index === 0) return 'ranking-gold'
  if (index === 1) return 'ranking-silver'
  if (index === 2) return 'ranking-bronze'
  return 'ranking-normal'
}

const getTrendIcon = (trend) => {
  const icons = {
    up: TrendingUp,
    down: TrendingDown,
    neutral: Minus
  }
  return icons[trend] || Minus
}

const getTrendClass = (trend) => {
  const classes = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-400'
  }
  return classes[trend] || 'text-gray-400'
}

const getGrowthClass = (growth) => {
  if (growth > 0) return 'text-green-600'
  if (growth < 0) return 'text-red-600'
  return 'text-gray-600'
}

const getChangeClass = (change) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

const getTemplateTagType = (type) => {
  const types = {
    business: 'primary',
    financial: 'success',
    user: 'warning',
    operation: 'info'
  }
  return types[type] || 'info'
}

const getTemplateTypeText = (type) => {
  const texts = {
    business: '业务',
    financial: '财务',
    user: '用户',
    operation: '运营'
  }
  return texts[type] || type
}

const getReportTypeText = (type) => {
  const texts = {
    business: '业务分析报表',
    financial: '财务分析报表',
    user: '用户分析报表',
    operation: '运营分析报表'
  }
  return texts[type] || '自定义报表'
}

const getChartIcon = (chartType) => {
  const icons = {
    line: TrendingUp,
    bar: BarChart,
    pie: PieChart,
    area: Monitor,
    scatter: Target
  }
  return icons[chartType] || BarChart
}

const getChartTypeText = (chartType) => {
  const texts = {
    line: '线图',
    bar: '柱状图',
    pie: '饼图',
    area: '面积图',
    scatter: '散点图'
  }
  return texts[chartType] || '图表'
}

const formatDateRange = (dateRange) => {
  if (!dateRange || !Array.isArray(dateRange) || dateRange.length !== 2) {
    return '未设置'
  }
  const start = dateRange[0].toLocaleDateString()
  const end = dateRange[1].toLocaleDateString()
  return `${start} - ${end}`
}

const getMetricName = (metricKey) => {
  const allMetrics = metricsCategories.value.flatMap(cat => cat.metrics)
  const metric = allMetrics.find(m => m.key === metricKey)
  return metric ? metric.name : metricKey
}

// 生命周期
onMounted(() => {
  console.log('业务数据报表页面已加载')
})
</script>

<style scoped>
.business-reports {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-area {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 核心指标区域 */
.key-metrics {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.metrics-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.metric-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  position: relative;
  overflow: hidden;
}

.metric-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-card.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.metric-card.info {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: -50px;
  width: 100px;
  height: 100px;
  background: rgba(255,255,255,0.1);
  border-radius: 50%;
}

.metric-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  opacity: 0.3;
}

.metric-content {
  position: relative;
  z-index: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.neutral {
  color: #6b7280;
}

/* 报表内容区域 */
.reports-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

/* 图表区域 */
.chart-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.chart-content {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background: white;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.placeholder-text {
  font-size: 14px;
  color: #6b7280;
  margin: 8px 0;
}

.chart-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.stat-item {
  font-size: 12px;
  color: #9ca3af;
}

/* 分类统计区域 */
.category-stats {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.stats-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.stats-content {
  margin-top: 16px;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stats-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.stats-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.stats-value {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.stats-progress {
  margin-bottom: 8px;
}

.stats-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-amount {
  font-size: 12px;
  color: #6b7280;
}

.stats-percent {
  font-size: 12px;
  font-weight: 500;
  color: #3b82f6;
}

/* 排行榜 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.ranking-number.ranking-gold {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.ranking-number.ranking-silver {
  background: linear-gradient(135deg, #d1d5db, #9ca3af);
}

.ranking-number.ranking-bronze {
  background: linear-gradient(135deg, #cd7c2f, #a16207);
}

.ranking-number.ranking-normal {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.ranking-content {
  flex: 1;
}

.ranking-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.ranking-stats {
  display: flex;
  gap: 12px;
}

.stat {
  font-size: 12px;
  color: #6b7280;
}

.ranking-trend {
  flex-shrink: 0;
}

/* 地区统计 */
.region-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.region-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.region-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.region-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.region-count {
  font-size: 12px;
  color: #6b7280;
}

.region-progress {
  margin-bottom: 8px;
}

.region-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.region-revenue {
  font-size: 12px;
  color: #6b7280;
}

.region-growth {
  font-size: 12px;
  font-weight: 500;
}

/* 财务报表区域 */
.financial-overview {
  margin-bottom: 32px;
}

.financial-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.financial-cards {
  margin-top: 20px;
}

.financial-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.card-comparison {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comparison-label {
  font-size: 12px;
  color: #6b7280;
}

.comparison-value {
  font-size: 12px;
  font-weight: 500;
}

.comparison-value.positive {
  color: #10b981;
}

.comparison-value.negative {
  color: #ef4444;
}

.card-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
}

.detail-value {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
}

/* 收入分析 */
.revenue-analysis {
  margin-bottom: 32px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.analysis-chart,
.analysis-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.revenue-breakdown {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.breakdown-item {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.breakdown-label {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.breakdown-amount {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.breakdown-progress {
  margin-bottom: 8px;
}

.breakdown-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breakdown-percent {
  font-size: 12px;
  color: #6b7280;
}

.breakdown-change {
  font-size: 12px;
  font-weight: 500;
}

.breakdown-change.positive {
  color: #10b981;
}

.breakdown-change.negative {
  color: #ef4444;
}

.breakdown-change.neutral {
  color: #6b7280;
}

/* 用户报表区域 */
.user-overview {
  margin-bottom: 32px;
}

.user-metrics {
  margin-top: 20px;
}

.user-metric-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
}

.metric-data {
  flex: 1;
}

.metric-number {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.metric-title {
  font-size: 12px;
  color: #6b7280;
}

/* 用户行为分析 */
.user-behavior {
  margin-bottom: 32px;
}

.behavior-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.behavior-chart,
.behavior-stats {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
}

.behavior-list {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.behavior-item {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.behavior-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.behavior-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.behavior-count {
  font-size: 12px;
  color: #6b7280;
}

.behavior-progress {
  margin-top: 8px;
}

/* 自定义报表区域 */
.custom-report {
  padding: 20px 0;
}

.report-builder {
  margin-bottom: 32px;
}

.builder-form {
  background: #f9fafb;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.form-section {
  margin-bottom: 24px;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 指标选择区域 */
.metrics-selection {
  margin-top: 16px;
}

.metrics-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category-group {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.category-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.metric-checkbox {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #fafbfc;
}

.metric-name {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.metric-description {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

/* 图表配置 */
.chart-config {
  margin-top: 16px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 24px;
}

/* 报表模板区域 */
.report-templates {
  margin-bottom: 32px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.template-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.template-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.template-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.template-metrics,
.template-usage {
  font-size: 12px;
  color: #9ca3af;
}

.template-actions {
  display: flex;
  gap: 8px;
}

/* 报表预览对话框 */
.report-preview-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 24px;
}

.preview-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.preview-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.preview-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.preview-chart {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
  height: 300px;
}

.preview-metrics {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.metrics-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-tag {
  margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .business-reports {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .metrics-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .behavior-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-content {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>