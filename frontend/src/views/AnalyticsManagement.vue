<template>
  <div class="analytics-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800 flex items-center">
          <BarChart class="w-8 h-8 text-blue-500 mr-3" />
          数据分析
        </h1>
        <p class="text-gray-600 mt-2">业务数据分析与智能洞察</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showCustomReportDialog = true">
          <Document class="w-4 h-4 mr-2" />
          自定义报表
        </el-button>
        <el-button @click="exportData">
          <Download class="w-4 h-4 mr-2" />
          导出数据
        </el-button>
        <el-button @click="refreshData">
          <Refresh class="w-4 h-4 mr-2" />
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择器 -->
    <div class="time-selector mb-8">
      <div class="modern-card p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <span class="text-gray-700 font-medium">时间范围:</span>
          <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
            <el-radio-button value="today">今日</el-radio-button>
            <el-radio-button value="week">本周</el-radio-button>
            <el-radio-button value="month">本月</el-radio-button>
            <el-radio-button value="quarter">本季度</el-radio-button>
            <el-radio-button value="year">本年</el-radio-button>
            <el-radio-button value="custom">自定义</el-radio-button>
          </el-radio-group>
          
          <el-date-picker
            v-if="timeRange === 'custom'"
            v-model="customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleCustomDateChange"
          />
        </div>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="kpi-overview mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="modern-card p-6 text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Money class="w-6 h-6 text-blue-600" />
          </div>
          <div class="text-2xl font-bold text-gray-800">¥{{ formatNumber(kpiData.revenue) }}</div>
          <div class="text-gray-600">营业收入</div>
          <div class="text-sm mt-1" :class="kpiData.revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'">
            {{ kpiData.revenueGrowth >= 0 ? '↗' : '↘' }} {{ Math.abs(kpiData.revenueGrowth) }}%
          </div>
        </div>
        
        <div class="modern-card p-6 text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <ShoppingCart class="w-6 h-6 text-green-600" />
          </div>
          <div class="text-2xl font-bold text-gray-800">{{ kpiData.orderCount }}</div>
          <div class="text-gray-600">订单数量</div>
          <div class="text-sm mt-1" :class="kpiData.orderGrowth >= 0 ? 'text-green-500' : 'text-red-500'">
            {{ kpiData.orderGrowth >= 0 ? '↗' : '↘' }} {{ Math.abs(kpiData.orderGrowth) }}%
          </div>
        </div>
        
        <div class="modern-card p-6 text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Users class="w-6 h-6 text-purple-600" />
          </div>
          <div class="text-2xl font-bold text-gray-800">{{ kpiData.activeUser }}</div>
          <div class="text-gray-600">活跃用户</div>
          <div class="text-sm mt-1" :class="kpiData.userGrowth >= 0 ? 'text-green-500' : 'text-red-500'">
            {{ kpiData.userGrowth >= 0 ? '↗' : '↘' }} {{ Math.abs(kpiData.userGrowth) }}%
          </div>
        </div>
        
        <div class="modern-card p-6 text-center">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <TrendChart class="w-6 h-6 text-orange-600" />
          </div>
          <div class="text-2xl font-bold text-gray-800">{{ kpiData.conversionRate }}%</div>
          <div class="text-gray-600">转化率</div>
          <div class="text-sm mt-1" :class="kpiData.conversionGrowth >= 0 ? 'text-green-500' : 'text-red-500'">
            {{ kpiData.conversionGrowth >= 0 ? '↗' : '↘' }} {{ Math.abs(kpiData.conversionGrowth) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 分析标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 业务概览 -->
      <el-tab-pane label="业务概览" name="overview">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 收入趋势图 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">收入趋势</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <TrendChart class="w-8 h-8 mx-auto mb-2" />
                  <div>收入趋势图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 订单分析 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">订单分析</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <PieChart class="w-8 h-8 mx-auto mb-2" />
                  <div>订单分布图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 用户活跃度 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">用户活跃度</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <Connection class="w-8 h-8 mx-auto mb-2" />
                  <div>用户活跃度图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 热门产品 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">热门产品排行</h3>
            </div>
            <div class="p-6">
              <div class="space-y-3">
                <div v-for="(product, index) in topProducts" :key="index" class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-6 h-6 bg-blue-100 rounded text-blue-600 text-sm flex items-center justify-center mr-3">
                      {{ index + 1 }}
                    </div>
                    <div>
                      <div class="font-medium">{{ product.name }}</div>
                      <div class="text-sm text-gray-600">{{ product.category }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">{{ product.sales }}</div>
                    <div class="text-sm text-gray-600">销量</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 销售分析 -->
      <el-tab-pane label="销售分析" name="sales">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 销售漏斗 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">销售漏斗</h3>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div v-for="(stage, index) in salesFunnel" :key="index" class="funnel-stage">
                  <div class="flex justify-between items-center mb-2">
                    <span class="font-medium">{{ stage.name }}</span>
                    <span class="text-sm text-gray-600">{{ stage.count }}</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                      :style="{ width: stage.percentage + '%' }"
                    />
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ stage.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 区域销售 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">区域销售分布</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <Map class="w-8 h-8 mx-auto mb-2" />
                  <div>区域销售地图</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 库存分析 -->
      <el-tab-pane label="库存分析" name="inventory">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 库存周转率 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">库存周转率</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <RotateRefresh class="w-8 h-8 mx-auto mb-2" />
                  <div>库存周转率图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 库存预警 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">库存预警</h3>
            </div>
            <div class="p-6">
              <div class="space-y-3">
                <div v-for="(alert, index) in inventoryAlerts" :key="index" class="flex items-center justify-between p-3 rounded-lg" :class="getAlertBgClass(alert.level)">
                  <div>
                    <div class="font-medium">{{ alert.productName }}</div>
                    <div class="text-sm text-gray-600">{{ alert.location }}</div>
                  </div>
                  <div class="text-right">
                    <div :class="getAlertTextClass(alert.level)">{{ alert.currentStock }}</div>
                    <div class="text-sm text-gray-600">安全库存: {{ alert.safetyStock }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 财务分析 -->
      <el-tab-pane label="财务分析" name="financial">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 利润分析 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">利润分析</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <Money class="w-8 h-8 mx-auto mb-2" />
                  <div>利润分析图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 成本构成 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">成本构成</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <PieChart class="w-8 h-8 mx-auto mb-2" />
                  <div>成本构成图表</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 预测分析 -->
      <el-tab-pane label="预测分析" name="forecasting">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 需求预测 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">需求预测</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <TrendChart class="w-8 h-8 mx-auto mb-2" />
                  <div>需求预测图表</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 收入预测 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold">收入预测</h3>
            </div>
            <div class="p-6">
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <BarChart class="w-8 h-8 mx-auto mb-2" />
                  <div>收入预测图表</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 自定义报表弹窗 -->
    <CustomReportDialog 
      v-model="showCustomReportDialog" 
      @success="handleReportGenerated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  BarChart, Document, Download, Refresh, Money, ShoppingCart,
  User, TrendChart, PieChart, Connection, Map, Refresh as RotateRefresh
} from '@element-plus/icons-vue'
import { analyticsApi } from '@/api/analytics'
import CustomReportDialog from '@/components/CustomReportDialog.vue'

// 状态
const loading = ref(false)
const showCustomReportDialog = ref(false)
const timeRange = ref('month')
const customDateRange = ref(null)
const activeTab = ref('overview')

// KPI数据
const kpiData = ref({
  revenue: 12500000,
  revenueGrowth: 15.8,
  orderCount: 1247,
  orderGrowth: 23.5,
  activeUsers: 3456,
  userGrowth: 12.3,
  conversionRate: 18.9,
  conversionGrowth: 5.2
})

// 热门产品
const topProducts = ref([
  { name: 'CFM56发动机叶片', category: '发动机部件', sales: 156 },
  { name: 'A320主起落架', category: '起落架', sales: 89 },
  { name: '液压泵组件', category: '液压系统', sales: 67 },
  { name: '轮胎刹车组件', category: '轮胎刹车', sales: 45 },
  { name: '航电显示器', category: '航电设备', sales: 34 }
])

// 销售漏斗
const salesFunnel = ref([
  { name: '潜在客户', count: 1000, percentage: 100 },
  { name: '询价客户', count: 500, percentage: 50 },
  { name: '报价客户', count: 300, percentage: 30 },
  { name: '成交客户', count: 150, percentage: 15 }
])

// 库存预警
const inventoryAlerts = ref([
  { productName: 'CFM56叶片', location: '北京仓库', currentStock: 3, safetyStock: 10, level: 'danger' },
  { productName: '液压油滤芯', location: '上海仓库', currentStock: 8, safetyStock: 15, level: 'warning' },
  { productName: '密封圈组件', location: '广州仓库', currentStock: 12, safetyStock: 20, level: 'warning' }
])

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    // await loadAnalyticsData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTimeRangeChange = (value) => {
  if (value !== 'custom') {
    loadAnalyticsData()
  }
}

const handleCustomDateChange = (dates) => {
  if (dates && dates.length === 2) {
    loadAnalyticsData()
  }
}

const handleTabClick = (tab) => {
  loadTabData(tab.name)
}

const loadAnalyticsData = async () => {
  try {
    loading.value = true
    // 根据时间范围加载数据
    console.log('加载分析数据:', timeRange.value)
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadTabData = async (tabName) => {
  try {
    console.log('加载标签页数据:', tabName)
    // 根据标签页加载相应数据
  } catch (error) {
    ElMessage.error(`加载${tabName}数据失败`)
  }
}

const exportData = async () => {
  try {
    // await analyticsApi.exportAnalyticsData({ format: 'xlsx', time_range: timeRange.value })
    ElMessage.success('数据导出中，请稍后...')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

const handleReportGenerated = () => {
  ElMessage.success('自定义报表生成成功')
}

// 辅助函数
const formatNumber = (number) => {
  return new Intl.NumberFormat('zh-CN').format(number)
}

const getAlertBgClass = (level) => {
  const classes = {
    danger: 'bg-red-50 border border-red-200',
    warning: 'bg-orange-50 border border-orange-200',
    normal: 'bg-green-50 border border-green-200'
  }
  return classes[level] || classes.normal
}

const getAlertTextClass = (level) => {
  const classes = {
    danger: 'text-red-600 font-bold',
    warning: 'text-orange-600 font-bold',
    normal: 'text-green-600 font-bold'
  }
  return classes[level] || classes.normal
}

onMounted(() => {
  loadAnalyticsData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.funnel-stage {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.time-selector .modern-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-radio-button__inner) {
  border-radius: 6px;
  margin: 0 2px;
}

.kpi-overview .modern-card {
  border-left: 4px solid #3b82f6;
}
</style>