<template>
  <div class="logistics-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">物流跟踪</h1>
        <p class="text-gray-600 mt-2">管理订单物流状态和运输进度</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          创建跟踪
        </el-button>
        <el-button @click="batchQuery">
          <Search class="w-4 h-4 mr-2" />
          批量查询
        </el-button>
        <el-button @click="refreshLogistics">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 物流统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Van class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.totalShipments }}</div>
        <div class="text-gray-600">总运单数</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <CircleCheck class="w-6 h-6 text-green-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.onTimeRate }}%</div>
        <div class="text-gray-600">准时率</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Clock class="w-6 h-6 text-orange-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.avgDeliveryTime }}天</div>
        <div class="text-gray-600">平均配送时间</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Warning class="w-6 h-6 text-red-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.exceptionCount }}</div>
        <div class="text-gray-600">异常运单</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section mb-6">
      <div class="modern-card p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <el-input
            v-model="filters.search"
            placeholder="搜索运单号、订单号..."
            style="width: 200px"
            size="small"
            clearable
          >
            <template #prefix>
              <Search class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>
          
          <el-select v-model="filters.status" placeholder="运输状态" style="width: 150px" size="small">
            <el-option label="全部状态" value="" />
            <el-option label="待发货" value="pending" />
            <el-option label="运输中" value="in_transit" />
            <el-option label="派送中" value="delivering" />
            <el-option label="已签收" value="delivered" />
            <el-option label="异常" value="exception" />
          </el-select>
          
          <el-select v-model="filters.carrier" placeholder="承运商" style="width: 150px" size="small">
            <el-option label="全部承运商" value="" />
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="航空货运" value="AIR" />
          </el-select>
          
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            style="width: 240px"
          />
          
          <el-button @click="applyFilters" size="small">筛选</el-button>
          <el-button @click="resetFilters" size="small">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 物流列表 -->
    <div class="modern-card">
      <div class="logistics-list">
        <el-table :data="logisticsList" v-loading="loading" style="width: 100%">
          <el-table-column prop="tracking_number" label="运单号" width="160">
            <template #default="{ row }">
              <el-link type="primary" @click="viewTrackingDetail(row.id)">
                {{ row.tracking_number }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="order_id" label="关联订单" width="120">
            <template #default="{ row }">
              <el-link type="primary" @click="viewOrder(row.order_id)">
                {{ row.order_id }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="carrier" label="承运商" width="100">
            <template #default="{ row }">
              <div class="flex items-center">
                <img :src="getCarrierLogo(row.carrier)" :alt="row.carrier" class="w-6 h-6 mr-2" />
                <span>{{ getCarrierName(row.carrier) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="路线" min-width="180">
            <template #default="{ row }">
              <div class="route-info">
                <div class="flex items-center text-sm">
                  <span class="text-gray-600">{{ row.origin }}</span>
                  <ArrowRight class="w-4 h-4 mx-2 text-gray-400" />
                  <span class="text-gray-600">{{ row.destination }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="当前位置" width="150">
            <template #default="{ row }">
              <div class="location-info">
                <div class="text-sm font-medium">{{ row.current_location?.city }}</div>
                <div class="text-xs text-gray-500">{{ row.current_location?.facility }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="进度" width="120">
            <template #default="{ row }">
              <div class="progress-info">
                <el-progress 
                  :percentage="row.progress" 
                  :stroke-width="6"
                  :color="getProgressColor(row.progress)"
                />
                <div class="text-xs text-center mt-1">{{ row.progress }}%</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="预计送达" width="150">
            <template #default="{ row }">
              <div class="delivery-info">
                <div class="text-sm font-medium">{{ formatDate(row.estimated_delivery) }}</div>
                <div class="text-xs" :class="getDeliveryTimeClass(row.estimated_delivery)">
                  {{ getDeliveryTimeText(row.estimated_delivery) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex space-x-2">
                <el-button link type="primary" size="small" @click="viewTrackingDetail(row.id)">
                  跟踪详情
                </el-button>
                <el-button link size="small" @click="updateStatus(row.id)">
                  更新状态
                </el-button>
                <el-dropdown>
                  <el-button link size="small">
                    更多
                    <ArrowDown class="w-3 h-3 ml-1" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="generateWaybill(row.id)">
                        生成运单
                      </el-dropdown-item>
                      <el-dropdown-item @click="reportException(row.id)">
                        报告异常
                      </el-dropdown-item>
                      <el-dropdown-item @click="exportTracking(row.id)">
                        导出记录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 实时地图视图 -->
    <div class="modern-card mt-6" v-if="showMapView">
      <div class="map-header p-4 border-b">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">实时物流地图</h3>
          <el-button @click="toggleMapView" size="small">
            {{ showMapView ? '隐藏地图' : '显示地图' }}
          </el-button>
        </div>
      </div>
      <div class="map-container h-96 bg-gray-100 flex items-center justify-center">
        <div class="text-gray-500">
          <MapPin class="w-8 h-8 mx-auto mb-2" />
          <div>物流地图视图（需要集成地图API）</div>
        </div>
      </div>
    </div>

    <!-- 创建物流跟踪弹窗 -->
    <CreateLogisticsDialog 
      v-model="showCreateDialog" 
      @success="handleLogisticsCreated"
    />
    
    <!-- 物流详情弹窗 -->
    <LogisticsDetailDialog 
      v-model="showDetailDialog" 
      :tracking-id="selectedTrackingId"
    />
    
    <!-- 更新状态弹窗 -->
    <UpdateStatusDialog 
      v-model="showUpdateDialog" 
      :tracking-id="selectedTrackingId"
      @success="handleStatusUpdated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Search, Refresh, Van, CircleCheck, Clock, Warning,
  ArrowRight, ArrowDown, MapPin
} from '@element-plus/icons-vue'
import { logisticsApi } from '@/api/logistics'
import CreateLogisticsDialog from '@/components/CreateLogisticsDialog.vue'
import LogisticsDetailDialog from '@/components/LogisticsDetailDialog.vue'
import UpdateStatusDialog from '@/components/UpdateStatusDialog.vue'

// 状态
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showUpdateDialog = ref(false)
const showMapView = ref(false)
const selectedTrackingId = ref(null)

// 统计数据
const statistics = ref({
  totalShipments: 248,
  onTimeRate: 92,
  avgDeliveryTime: 5.2,
  exceptionCount: 8
})

// 筛选器
const filters = reactive({
  search: '',
  status: '',
  carrier: '',
  dateRange: null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 物流列表
const logisticsList = ref([])

// 模拟数据
const initializeMockData = () => {
  logisticsList.value = [
    {
      id: 'L001',
      tracking_number: 'SF1234567890',
      order_id: 'PO240001',
      carrier: 'SF',
      origin: '上海浦东机场',
      destination: '北京首都机场',
      status: 'in_transit',
      current_location: {
        city: '济南',
        facility: '济南转运中心'
      },
      progress: 65,
      estimated_delivery: '2025-01-15',
      created_at: '2025-01-13 10:00:00'
    },
    {
      id: 'L002',
      tracking_number: 'ZTO9876543210',
      order_id: 'SO240002',
      carrier: 'ZTO',
      origin: '广州白云区',
      destination: '深圳宝安区',
      status: 'delivering',
      current_location: {
        city: '深圳',
        facility: '宝安配送站'
      },
      progress: 90,
      estimated_delivery: '2025-01-14',
      created_at: '2025-01-12 14:30:00'
    },
    {
      id: 'L003',
      tracking_number: 'AIR2025001',
      order_id: 'AO240003',
      carrier: 'AIR',
      origin: '成都双流机场',
      destination: '西安咸阳机场',
      status: 'delivered',
      current_location: {
        city: '西安',
        facility: '已签收'
      },
      progress: 100,
      estimated_delivery: '2025-01-13',
      created_at: '2025-01-11 09:15:00'
    },
    {
      id: 'L004',
      tracking_number: 'YTO5678901234',
      order_id: 'PO240004',
      carrier: 'YTO',
      origin: '杭州萧山',
      destination: '南京禄口',
      status: 'exception',
      current_location: {
        city: '南京',
        facility: '派送异常-收件人电话无法接通'
      },
      progress: 85,
      estimated_delivery: '2025-01-14',
      created_at: '2025-01-12 16:45:00'
    }
  ]
  pagination.total = logisticsList.value.length
}

// 方法
const refreshLogistics = async () => {
  try {
    loading.value = true
    // await loadLogisticsList()
    initializeMockData()
    ElMessage.success('物流列表已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const applyFilters = async () => {
  try {
    loading.value = true
    pagination.page = 1
    // await loadLogisticsList()
    ElMessage.success('筛选完成')
  } catch (error) {
    ElMessage.error('筛选失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'dateRange' ? null : ''
  })
  applyFilters()
}

const batchQuery = () => {
  ElMessage.info('批量查询功能')
}

const viewTrackingDetail = (trackingId) => {
  selectedTrackingId.value = trackingId
  showDetailDialog.value = true
}

const viewOrder = (orderId) => {
  ElMessage.info(`查看订单: ${orderId}`)
}

const updateStatus = (trackingId) => {
  selectedTrackingId.value = trackingId
  showUpdateDialog.value = true
}

const generateWaybill = async (trackingId) => {
  try {
    // await logisticsApi.generateWaybill(trackingId)
    ElMessage.success('运单生成成功')
  } catch (error) {
    ElMessage.error('生成运单失败')
  }
}

const reportException = (trackingId) => {
  ElMessage.info(`报告物流异常: ${trackingId}`)
}

const exportTracking = (trackingId) => {
  ElMessage.info(`导出跟踪记录: ${trackingId}`)
}

const toggleMapView = () => {
  showMapView.value = !showMapView.value
}

const handleLogisticsCreated = () => {
  ElMessage.success('物流跟踪创建成功')
  refreshLogistics()
}

const handleStatusUpdated = () => {
  ElMessage.success('状态更新成功')
  refreshLogistics()
}

const handleSizeChange = (size) => {
  pagination.size = size
  // loadLogisticsList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  // loadLogisticsList()
}

// 辅助函数
const getCarrierLogo = (carrier) => {
  const logos = {
    SF: '/logos/sf.png',
    ZTO: '/logos/zto.png',
    YTO: '/logos/yto.png',
    AIR: '/logos/air.png'
  }
  return logos[carrier] || '/logos/default.png'
}

const getCarrierName = (carrier) => {
  const names = {
    SF: '顺丰',
    ZTO: '中通',
    YTO: '圆通',
    AIR: '航空货运'
  }
  return names[carrier] || carrier
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_transit: 'primary',
    delivering: 'primary',
    delivered: 'success',
    exception: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待发货',
    in_transit: '运输中',
    delivering: '派送中',
    delivered: '已签收',
    exception: '异常'
  }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 90) return '#67c23a'
  if (progress >= 60) return '#409eff'
  if (progress >= 30) return '#e6a23c'
  return '#f56c6c'
}

const getDeliveryTimeClass = (deliveryDate) => {
  const today = new Date()
  const delivery = new Date(deliveryDate)
  const diffDays = Math.ceil((delivery - today) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'text-red-500'
  if (diffDays <= 1) return 'text-orange-500'
  return 'text-green-500'
}

const getDeliveryTimeText = (deliveryDate) => {
  const today = new Date()
  const delivery = new Date(deliveryDate)
  const diffDays = Math.ceil((delivery - today) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return '已延期'
  if (diffDays === 0) return '今日到达'
  if (diffDays === 1) return '明日到达'
  return `${diffDays}天后到达`
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.logistics-list {
  padding: 20px;
}

.route-info {
  display: flex;
  align-items: center;
}

.location-info {
  max-width: 140px;
}

.progress-info {
  width: 100px;
}

.delivery-info {
  text-align: center;
}

.filters-section .modern-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.map-container {
  border-radius: 0 0 12px 12px;
}
</style>