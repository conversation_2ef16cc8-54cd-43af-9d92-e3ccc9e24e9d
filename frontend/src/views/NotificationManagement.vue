<template>
  <div class="notification-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">通知管理</h1>
        <p class="text-gray-600 mt-2">管理系统通知和消息推送</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showSendDialog = true">
          <Message class="w-4 h-4 mr-2" />
          发送通知
        </el-button>
        <el-button @click="markAllRead" :disabled="unreadCount === 0">
          <SuccessFilled class="w-4 h-4 mr-2" />
          全部已读
        </el-button>
        <el-button @click="refreshNotifications">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 通知统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Bell class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.totalNotifications }}</div>
        <div class="text-gray-600">总通知</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <InfoFilled class="w-6 h-6 text-orange-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.unreadNotifications }}</div>
        <div class="text-gray-600">未读通知</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <SuccessFilled class="w-6 h-6 text-green-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.readRate }}%</div>
        <div class="text-gray-600">阅读率</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <DataLine class="w-6 h-6 text-purple-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.todayNotifications }}</div>
        <div class="text-gray-600">今日通知</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section mb-6">
      <div class="modern-card p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <el-select v-model="filters.type" placeholder="通知类型" style="width: 150px" size="small">
            <el-option label="全部类型" value="" />
            <el-option label="系统通知" value="system" />
            <el-option label="订单通知" value="order" />
            <el-option label="审批通知" value="approval" />
            <el-option label="库存预警" value="inventory" />
            <el-option label="AOG紧急" value="aog" />
          </el-select>
          
          <el-select v-model="filters.priority" placeholder="优先级" style="width: 120px" size="small">
            <el-option label="全部优先级" value="" />
            <el-option label="紧急" value="urgent" />
            <el-option label="高" value="high" />
            <el-option label="普通" value="normal" />
            <el-option label="低" value="low" />
          </el-select>
          
          <el-select v-model="filters.read" placeholder="读取状态" style="width: 120px" size="small">
            <el-option label="全部状态" value="" />
            <el-option label="未读" value="false" />
            <el-option label="已读" value="true" />
          </el-select>
          
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            style="width: 240px"
          />
          
          <el-button @click="applyFilters" size="small">筛选</el-button>
          <el-button @click="resetFilters" size="small">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 通知列表 -->
    <div class="modern-card">
      <div class="notification-list">
        <el-table 
          :data="notifications" 
          v-loading="loading" 
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column width="60">
            <template #default="{ row }">
              <div class="notification-icon">
                <el-badge :is-dot="!row.read" :hidden="row.read">
                  <component 
                    :is="getNotificationIcon(row.type)" 
                    :class="getNotificationIconClass(row.type, row.priority)"
                    class="w-6 h-6"
                  />
                </el-badge>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="通知内容" min-width="300">
            <template #default="{ row }">
              <div class="notification-content" :class="{ 'font-bold': !row.read }">
                <div class="flex items-center mb-1">
                  <span class="notification-title">{{ row.title }}</span>
                  <el-tag 
                    :type="getPriorityColor(row.priority)" 
                    size="small" 
                    class="ml-2"
                  >
                    {{ getPriorityText(row.priority) }}
                  </el-tag>
                </div>
                <div class="notification-message text-gray-600 text-sm">
                  {{ row.content }}
                </div>
                <div v-if="row.metadata" class="notification-metadata text-xs text-gray-500 mt-1">
                  <span v-if="row.metadata.order_id">订单: {{ row.metadata.order_id }}</span>
                  <span v-if="row.metadata.workflow_id" class="ml-2">工作流: {{ row.metadata.workflow_id }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(row.type)" size="small">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="created_at" label="时间" width="150">
            <template #default="{ row }">
              <div class="text-sm">
                <div>{{ formatDate(row.created_at) }}</div>
                <div class="text-gray-500">{{ formatTime(row.created_at) }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.read ? 'success' : 'warning'" size="small">
                {{ row.read ? '已读' : '未读' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <div class="flex space-x-2">
                <el-button 
                  v-if="!row.read"
                  link 
                  type="primary" 
                  size="small" 
                  @click="markAsRead(row.id)"
                >
                  标记已读
                </el-button>
                <el-button 
                  link 
                  size="small" 
                  @click="viewNotification(row)"
                >
                  查看
                </el-button>
                <el-button 
                  link 
                  type="danger" 
                  size="small" 
                  @click="deleteNotification(row.id)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 批量操作 -->
        <div class="batch-actions mt-4 pt-4 border-t">
          <div class="flex justify-between items-center">
            <div>
              <el-button 
                @click="batchMarkAsRead" 
                :disabled="selectedNotifications.length === 0"
                size="small"
              >
                批量已读
              </el-button>
              <el-button 
                @click="batchDelete" 
                :disabled="selectedNotifications.length === 0"
                type="danger"
                size="small"
              >
                批量删除
              </el-button>
            </div>
            <div class="text-sm text-gray-500">
              已选择 {{ selectedNotifications.length }} 项
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 发送通知弹窗 -->
    <SendNotificationDialog 
      v-model="showSendDialog" 
      @success="handleNotificationSent"
    />
    
    <!-- 通知详情弹窗 -->
    <NotificationDetailDialog 
      v-model="showDetailDialog" 
      :notification="selectedNotification"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Message, SuccessFilled, Refresh, Bell, InfoFilled, DataLine,
  Notification, Document, ShoppingCart, Setting, Warning
} from '@element-plus/icons-vue'
import { notificationsApi } from '@/api/notifications'
import SendNotificationDialog from '@/components/SendNotificationDialog.vue'
import NotificationDetailDialog from '@/components/NotificationDetailDialog.vue'

// 状态
const loading = ref(false)
const showSendDialog = ref(false)
const showDetailDialog = ref(false)
const selectedNotification = ref(null)
const selectedNotifications = ref([])

// 统计数据
const statistics = ref({
  totalNotifications: 156,
  unreadNotifications: 23,
  readRate: 85,
  todayNotifications: 12
})

// 筛选器
const filters = reactive({
  type: '',
  priority: '',
  read: '',
  dateRange: null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 通知列表
const notifications = ref([])

// 计算属性
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 模拟数据
const initializeMockData = () => {
  notifications.value = [
    {
      id: 'N001',
      type: 'aog',
      priority: 'urgent',
      title: 'AOG紧急需求待处理',
      content: '发动机故障导致AOG，需要立即处理零件需求 CF6-80C2-B6F',
      read: false,
      created_at: '2025-01-13 15:30:00',
      metadata: {
        order_id: 'PO240001',
        workflow_id: 'WF-2025-001'
      }
    },
    {
      id: 'N002',
      type: 'approval',
      priority: 'high',
      title: '工作流审批提醒',
      content: '您有一个采购订单审批待处理，请及时审批',
      read: false,
      created_at: '2025-01-13 14:20:00',
      metadata: {
        workflow_id: 'WF-2025-002'
      }
    },
    {
      id: 'N003',
      type: 'inventory',
      priority: 'normal',
      title: '库存预警',
      content: '轮胎组件库存不足，当前库存：5件，安全库存：10件',
      read: true,
      created_at: '2025-01-13 10:15:00',
      metadata: {
        part_number: 'TIRE-001'
      }
    },
    {
      id: 'N004',
      type: 'order',
      priority: 'normal',
      title: '订单状态更新',
      content: '销售订单 SO240005 已完成发货，物流单号：SF1234567890',
      read: true,
      created_at: '2025-01-13 09:45:00',
      metadata: {
        order_id: 'SO240005',
        tracking_number: 'SF1234567890'
      }
    }
  ]
  pagination.total = notifications.value.length
}

// 方法
const refreshNotifications = async () => {
  try {
    loading.value = true
    // await loadNotifications()
    initializeMockData()
    ElMessage.success('通知列表已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const applyFilters = async () => {
  try {
    loading.value = true
    pagination.page = 1
    // await loadNotifications()
    ElMessage.success('筛选完成')
  } catch (error) {
    ElMessage.error('筛选失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'dateRange' ? null : ''
  })
  applyFilters()
}

const markAsRead = async (notificationId) => {
  try {
    // await notificationsApi.markNotificationAsRead(notificationId)
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const markAllRead = async () => {
  try {
    // await notificationsApi.markAllAsRead()
    notifications.value.forEach(n => n.read = true)
    ElMessage.success('所有通知已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNotification = async (notificationId) => {
  try {
    await ElMessageBox.confirm(
      '确定删除此通知吗？',
      '确认删除',
      { type: 'warning' }
    )
    
    // await notificationsApi.deleteNotification(notificationId)
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
    ElMessage.success('通知已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchMarkAsRead = async () => {
  if (selectedNotifications.value.length === 0) return
  
  try {
    const ids = selectedNotifications.value.map(n => n.id)
    // await notificationsApi.batchMarkAsRead(ids)
    selectedNotifications.value.forEach(n => n.read = true)
    ElMessage.success('批量标记已读成功')
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

const batchDelete = async () => {
  if (selectedNotifications.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定删除选中的 ${selectedNotifications.value.length} 条通知吗？`,
      '批量删除',
      { type: 'warning' }
    )
    
    const ids = selectedNotifications.value.map(n => n.id)
    ids.forEach(id => {
      const index = notifications.value.findIndex(n => n.id === id)
      if (index > -1) {
        notifications.value.splice(index, 1)
      }
    })
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const viewNotification = (notification) => {
  selectedNotification.value = notification
  showDetailDialog.value = true
  
  // 如果是未读通知，标记为已读
  if (!notification.read) {
    markAsRead(notification.id)
  }
}

const handleSelectionChange = (selection) => {
  selectedNotifications.value = selection
}

const handleNotificationSent = () => {
  ElMessage.success('通知发送成功')
  refreshNotifications()
}

const handleSizeChange = (size) => {
  pagination.size = size
  // loadNotifications()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  // loadNotifications()
}

// 辅助函数
const getNotificationIcon = (type) => {
  const icons = {
    system: Setting,
    order: ShoppingCart,
    approval: Document,
    inventory: Warning,
    aog: Notification
  }
  return icons[type] || Bell
}

const getNotificationIconClass = (type, priority) => {
  const baseClass = 'notification-icon'
  const priorityClass = {
    urgent: 'text-red-500',
    high: 'text-orange-500',
    normal: 'text-blue-500',
    low: 'text-gray-500'
  }
  return `${baseClass} ${priorityClass[priority] || priorityClass.normal}`
}

const getTypeColor = (type) => {
  const colors = {
    system: 'info',
    order: 'primary',
    approval: 'warning',
    inventory: 'danger',
    aog: 'danger'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    system: '系统',
    order: '订单',
    approval: '审批',
    inventory: '库存',
    aog: 'AOG'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const formatDate = (datetime) => {
  return new Date(datetime).toLocaleDateString('zh-CN')
}

const formatTime = (datetime) => {
  return new Date(datetime).toLocaleTimeString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.notification-list {
  padding: 20px;
}

.notification-content {
  transition: all 0.2s;
}

.notification-content:hover {
  background-color: rgba(64, 158, 255, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin: -8px;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-actions {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.filters-section .modern-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style>