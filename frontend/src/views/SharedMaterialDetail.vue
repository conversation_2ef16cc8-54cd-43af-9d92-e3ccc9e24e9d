<template>
  <div class="shared-material-detail">
    <!-- 公共导航栏 -->
    <PublicHeader />
    
    <div v-loading="loading" class="detail-container">
      <div v-if="!loading && materialDetail" class="content-wrapper">
        <!-- 注意：面包屑导航已在PublicHeader中实现，这里移除重复的面包屑 -->

      <!-- 主要内容区域 -->
      <div class="detail-content">
        <!-- 左侧：图片和基本信息 -->
        <div class="detail-left">
          <div class="image-section">
            <img :src="materialDetail.material.image_url" :alt="materialDetail.material.name" />
          </div>
          
          <div class="basic-info-card">
            <h3>基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">零件号：</span>
                <span class="value">{{ materialDetail.material.part_number }}</span>
              </div>
              <div class="info-item">
                <span class="label">制造商：</span>
                <span class="value">{{ materialDetail.material.manufacturer }}</span>
              </div>
              <div class="info-item">
                <span class="label">适用机型：</span>
                <span class="value">{{ materialDetail.material.aircraft_type }}</span>
              </div>
              <div class="info-item">
                <span class="label">型号：</span>
                <span class="value">{{ materialDetail.material.model }}</span>
              </div>
              <div class="info-item">
                <span class="label">分类：</span>
                <span class="value">{{ materialDetail.material.category }}</span>
              </div>
              <div class="info-item">
                <span class="label">状况代码：</span>
                <span class="value">{{ materialDetail.shared_material.condition_code }}</span>
              </div>
            </div>
          </div>

          <div class="specifications-card" v-if="materialDetail.material.specifications">
            <h3>技术规格</h3>
            <div class="specs-grid">
              <div
                v-for="(value, key) in materialDetail.material.specifications"
                :key="key"
                class="spec-item"
              >
                <span class="spec-label">{{ key }}：</span>
                <span class="spec-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：详细信息和操作 -->
        <div class="detail-right">
          <div class="title-section">
            <h1>{{ materialDetail.material.name }}</h1>
            <div class="badges">
              <el-tag v-if="materialDetail.shared_material.share_type === 'sale'" type="success">
                出售
              </el-tag>
              <el-tag v-else-if="materialDetail.shared_material.share_type === 'lease'" type="warning">
                租赁
              </el-tag>
              <el-tag v-else-if="materialDetail.shared_material.share_type === 'exchange'" type="info">
                交换
              </el-tag>
              <el-tag v-else type="primary">
                借用
              </el-tag>
              
              <el-tag v-if="materialDetail.shared_material.sharing_policy === 'immediate'" type="success">
                立即生效
              </el-tag>
              <el-tag v-else-if="materialDetail.shared_material.sharing_policy === 'approval'" type="warning">
                需要审批
              </el-tag>
              <el-tag v-else type="info">
                仅接受询价
              </el-tag>
            </div>
          </div>

          <div class="price-section">
            <div class="price-info">
              <span class="price">¥{{ formatPrice(materialDetail.shared_material.price) }}</span>
              <span class="unit">/ 件</span>
            </div>
            <div class="availability">
              <span class="available">可用：{{ materialDetail.shared_material.available_quantity }} 件</span>
              <span class="total">总计：{{ materialDetail.shared_material.share_quantity }} 件</span>
            </div>
          </div>

          <div class="quantity-section">
            <div class="quantity-info">
              <span>最小订购量：{{ materialDetail.shared_material.min_order_quantity }} 件</span>
              <span v-if="materialDetail.shared_material.expiry_date">
                有效期至：{{ formatDate(materialDetail.shared_material.expiry_date) }}
              </span>
            </div>
          </div>

          <div class="description-section">
            <h3>描述信息</h3>
            <p>{{ materialDetail.shared_material.description || '暂无描述' }}</p>
            <p v-if="materialDetail.material.description" class="material-desc">
              {{ materialDetail.material.description }}
            </p>
          </div>

          <div class="supplier-section">
            <h3>供应商信息</h3>
            <div class="supplier-card">
              <div class="supplier-info">
                <h4>{{ materialDetail.owner.company }}</h4>
                <p>联系人：{{ materialDetail.owner.name }}</p>
                <p v-if="materialDetail.owner.phone">电话：{{ materialDetail.owner.phone }}</p>
                <el-tag :type="getOwnerTypeColor(materialDetail.owner.user_type)" size="small">
                  {{ getOwnerTypeText(materialDetail.owner.user_type) }}
                </el-tag>
              </div>
              <div class="supplier-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ materialDetail.inquiry_count }}</span>
                  <span class="stat-label">询价次数</span>
                </div>
              </div>
            </div>
          </div>

          <div class="action-section">
            <div class="action-buttons">
              <el-button
                size="large"
                @click="showInquiryDialog"
              >
                <el-icon><ChatDotRound /></el-icon>
                询价咨询
              </el-button>
              <el-button
                type="primary"
                size="large"
                :disabled="!canOrder"
                @click="handleDirectOrder"
              >
                <el-icon><ShoppingCart /></el-icon>
                {{ getOrderButtonText() }}
              </el-button>
            </div>
            <div class="action-tips">
              <el-text size="small" type="info">
                {{ getActionTipText() }}
              </el-text>
            </div>
          </div>

          <div class="stats-section">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">{{ materialDetail.shared_material.view_count || 0 }}</div>
                <div class="stat-label">浏览次数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ materialDetail.inquiry_count || 0 }}</div>
                <div class="stat-label">询价次数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ materialDetail.shared_material.transaction_count || 0 }}</div>
                <div class="stat-label">成交次数</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部：询价记录 -->
      <div class="inquiry-section" v-if="materialDetail.recent_inquiries?.length > 0">
        <h3>最近询价</h3>
        <div class="inquiry-list">
          <div
            v-for="inquiry in materialDetail.recent_inquiries"
            :key="inquiry.id"
            class="inquiry-item"
          >
            <div class="inquiry-header">
              <span class="inquirer">{{ inquiry.user_name }}</span>
              <span class="company">{{ inquiry.company }}</span>
              <span class="time">{{ formatTime(inquiry.created_at) }}</span>
            </div>
            <div class="inquiry-content">
              <p>询价数量：{{ inquiry.quantity }} 件</p>
              <p>询价信息：{{ inquiry.message }}</p>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 询价对话框 -->
    <el-dialog
      v-model="inquiryDialogVisible"
      title="询价申请"
      width="500px"
      :before-close="handleInquiryDialogClose"
    >
      <SharedMaterialInquiryForm
        :material="materialDetail"
        @success="handleInquirySuccess"
        @cancel="inquiryDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入
import { getSharedMaterialDetail } from '@/api/sharedMaterials'
import SharedMaterialInquiryForm from '@/components/SharedMaterialInquiryForm.vue'
import PublicHeader from '@/components/PublicHeader.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const materialDetail = ref(null)
const inquiryDialogVisible = ref(false)

// 计算属性
const canOrder = computed(() => {
  if (!materialDetail.value) return false
  const material = materialDetail.value.shared_material
  return material.sharing_policy === 'immediate' && material.available_quantity > 0
})

// 生命周期
onMounted(() => {
  loadMaterialDetail()
})

// 方法定义
const loadMaterialDetail = async () => {
  try {
    loading.value = true
    const materialId = route.params.id
    
    const response = await getSharedMaterialDetail(materialId)
    if (response.success) {
      materialDetail.value = response.body
    } else {
      ElMessage.error(response.message || '获取共享件详情失败')
      router.push('/shared-materials')
    }
  } catch (error) {
    console.error('获取共享件详情失败:', error)
    ElMessage.error('获取共享件详情失败')
    router.push('/shared-materials')
  } finally {
    loading.value = false
  }
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN').format(price)
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatTime = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getOwnerTypeColor = (type) => {
  const colorMap = {
    airline: 'primary',
    supplier: 'success',
    maintenance: 'warning',
    admin: 'info'
  }
  return colorMap[type] || 'default'
}

const getOwnerTypeText = (type) => {
  const textMap = {
    airline: '航空公司',
    supplier: '供应商',
    maintenance: '维修企业',
    admin: '管理员'
  }
  return textMap[type] || '未知'
}

const getOrderButtonText = () => {
  if (!materialDetail.value) return '立即下单'
  const policy = materialDetail.value.shared_material.sharing_policy
  if (policy === 'immediate') return '立即下单'
  if (policy === 'approval') return '申请下单'
  return '联系供应商'
}

const getActionTipText = () => {
  if (!materialDetail.value) return ''
  const policy = materialDetail.value.shared_material.sharing_policy
  if (policy === 'immediate') return '支持立即下单，下单后等待供应商发货'
  if (policy === 'approval') return '需要供应商审批，审批通过后才能完成交易'
  return '仅支持询价咨询，请联系供应商洽谈具体事宜'
}

const showInquiryDialog = () => {
  inquiryDialogVisible.value = true
}

const handleInquiryDialogClose = (done) => {
  inquiryDialogVisible.value = false
  if (done) done()
}

const handleInquirySuccess = () => {
  inquiryDialogVisible.value = false
  ElMessage.success('询价申请提交成功')
  // 刷新页面数据
  loadMaterialDetail()
}

const handleDirectOrder = async () => {
  if (!canOrder.value) {
    ElMessage.warning('当前不支持直接下单')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要下单购买这个共享件吗？',
      '确认下单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该跳转到下单页面或者直接创建订单
    ElMessage.success('下单功能开发中，请先使用询价功能')
  } catch (error) {
    // 用户取消了操作
  }
}
</script>

<style scoped>
.shared-material-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.el-breadcrumb {
  margin-bottom: 24px;
}

.detail-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.detail-left {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-section img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
}

.basic-info-card,
.specifications-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.basic-info-card h3,
.specifications-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: grid;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  color: #909399;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.specs-grid {
  display: grid;
  gap: 8px;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.spec-label {
  color: #909399;
  min-width: 80px;
}

.spec-value {
  color: #303133;
  font-weight: 500;
}

.detail-right {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.title-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h1 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.price-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

.price {
  font-size: 28px;
  font-weight: 600;
  color: #e6a23c;
}

.unit {
  font-size: 14px;
  color: #909399;
}

.availability {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.available {
  color: #67c23a;
  font-weight: 500;
}

.total {
  color: #909399;
}

.quantity-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quantity-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #606266;
}

.description-section,
.supplier-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.description-section h3,
.supplier-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.description-section p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.material-desc {
  color: #909399;
  font-style: italic;
}

.supplier-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.supplier-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.supplier-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.supplier-stats {
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.action-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.action-buttons .el-button {
  flex: 1;
}

.action-tips {
  text-align: center;
}

.stats-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-card .stat-label {
  font-size: 14px;
  color: #606266;
}

.inquiry-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.inquiry-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.inquiry-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.inquiry-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.inquiry-header {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.inquirer {
  font-weight: 500;
  color: #303133;
}

.company {
  color: #606266;
}

.time {
  color: #909399;
  margin-left: auto;
}

.inquiry-content p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

@media (max-width: 968px) {
  .detail-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .supplier-card {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .shared-material-detail {
    padding: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .price-info {
    justify-content: center;
  }
  
  .availability {
    justify-content: center;
  }
  
  .quantity-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style>