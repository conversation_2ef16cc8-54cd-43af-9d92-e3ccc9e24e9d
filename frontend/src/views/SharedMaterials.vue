<template>
  <div class="shared-materials-page">
    <!-- 统一导航栏 -->
    <UnifiedHeader 
      :user-avatar="userAvatar"
      :unread-count="unreadCount"
      @quick-action="handleQuickAction"
      @toggle-notifications="handleToggleNotifications"
    />
    
    <!-- 页面内容 -->
    <div class="page-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">共享件市场</h1>
        <p class="page-description">发现和分享优质航材资源，构建高效的航材共享生态</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showPublishDialog">
          <template #icon><el-icon><Plus /></el-icon></template>
          发布共享件
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchForm.search"
          placeholder="搜索航材名称、零件号或制造商"
          class="search-input"
          @keyup.enter="searchMaterials"
          @input="handleSearchInput"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="searchMaterials">搜索</el-button>
          </template>
        </el-input>
        
        <!-- 搜索建议 -->
        <div v-if="suggestions.length > 0" class="search-suggestions">
          <div
            v-for="suggestion in suggestions"
            :key="suggestion.part_number"
            class="suggestion-item"
            @click="selectSuggestion(suggestion)"
          >
            <span class="suggestion-text">{{ suggestion.text }}</span>
            <span class="suggestion-meta">{{ suggestion.part_number }}</span>
          </div>
        </div>
      </div>

      <div class="filter-bar">
        <el-select v-model="searchForm.category" placeholder="选择分类" @change="searchMaterials">
          <el-option label="全部分类" value="" />
          <el-option
            v-for="category in categories"
            :key="category.name"
            :label="`${category.name} (${category.count})`"
            :value="category.name"
          />
        </el-select>

        <el-select v-model="searchForm.share_type" placeholder="共享类型" @change="searchMaterials">
          <el-option label="全部类型" value="" />
          <el-option label="出售" value="sale" />
          <el-option label="租赁" value="lease" />
          <el-option label="交换" value="exchange" />
          <el-option label="借用" value="loan" />
        </el-select>

        <div class="price-range">
          <el-input
            v-model.number="searchForm.price_min"
            placeholder="最低价格"
            type="number"
            @change="searchMaterials"
          />
          <span class="range-separator">-</span>
          <el-input
            v-model.number="searchForm.price_max"
            placeholder="最高价格"
            type="number"
            @change="searchMaterials"
          />
        </div>

        <el-select v-model="searchForm.sort_by" @change="searchMaterials">
          <el-option label="最新发布" value="created_at" />
          <el-option label="价格升序" value="price" />
          <el-option label="价格降序" value="price_desc" />
        </el-select>

        <el-button @click="resetFilters">重置筛选</el-button>
      </div>
    </div>

    <!-- 共享件列表 -->
    <div class="materials-grid" v-loading="loading">
      <div v-if="materials.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无共享件信息" />
      </div>

      <div
        v-for="material in materials"
        :key="material.id"
        class="material-card"
        @click="viewMaterialDetail(material.id)"
      >
        <div class="card-image">
          <img :src="material.material.image_url" :alt="material.material.name" />
          <div class="card-badges">
            <el-tag v-if="material.share_type === 'sale'" type="success" size="small">出售</el-tag>
            <el-tag v-else-if="material.share_type === 'lease'" type="warning" size="small">租赁</el-tag>
            <el-tag v-else-if="material.share_type === 'exchange'" type="info" size="small">交换</el-tag>
            <el-tag v-else type="primary" size="small">借用</el-tag>
          </div>
        </div>

        <div class="card-content">
          <h3 class="material-name">{{ material.material.name }}</h3>
          <p class="material-info">
            <span class="part-number">{{ material.material.part_number }}</span>
            <span class="manufacturer">{{ material.material.manufacturer }}</span>
          </p>
          <p class="material-description">{{ material.description }}</p>

          <div class="material-details">
            <div class="detail-row">
              <span class="label">分类：</span>
              <span>{{ material.material.category }}</span>
            </div>
            <div class="detail-row">
              <span class="label">适用机型：</span>
              <span>{{ material.material.aircraft_type }}</span>
            </div>
            <div class="detail-row">
              <span class="label">可用数量：</span>
              <span class="quantity">{{ material.available_quantity }} / {{ material.share_quantity }}</span>
            </div>
          </div>

          <div class="card-footer">
            <div class="price-info">
              <span class="price">¥{{ formatPrice(material.price) }}</span>
              <span class="unit">/ 件</span>
            </div>
            <div class="owner-info">
              <span class="owner-name">{{ material.owner.company }}</span>
              <el-tag :type="getOwnerTypeColor(material.owner.user_type)" size="small">
                {{ getOwnerTypeText(material.owner.user_type) }}
              </el-tag>
            </div>
          </div>

          <div class="card-actions">
            <el-button size="small" @click.stop="viewMaterialDetail(material.id)">
              查看详情
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click.stop="showInquiryDialog(material)"
            >
              立即询价
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 发布共享件对话框 -->
    <el-dialog
      v-model="publishDialogVisible"
      title="发布共享件"
      width="600px"
      :before-close="handlePublishDialogClose"
    >
      <SharedMaterialPublishForm
        @success="handlePublishSuccess"
        @cancel="publishDialogVisible = false"
      />
    </el-dialog>

    <!-- 询价对话框 -->
    <el-dialog
      v-model="inquiryDialogVisible"
      title="询价申请"
      width="500px"
      :before-close="handleInquiryDialogClose"
    >
      <SharedMaterialInquiryForm
        :material="selectedMaterial"
        @success="handleInquirySuccess"
        @cancel="inquiryDialogVisible = false"
      />
    </el-dialog>
    </div> <!-- 页面内容结束 -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import {
  getSharedMaterials,
  getMaterialCategories,
  getSearchSuggestions
} from '@/api/sharedMaterials'
import { useAuthStore } from '@/stores/auth'
import SharedMaterialPublishForm from '@/components/SharedMaterialPublishForm.vue'
import SharedMaterialInquiryForm from '@/components/SharedMaterialInquiryForm.vue'
import UnifiedHeader from '@/components/UnifiedHeader.vue'

const router = useRouter()
const authStore = useAuthStore()

// 统一导航相关数据
const userAvatar = ref('')
const unreadCount = ref(0)

// 响应式数据
const loading = ref(false)
const materials = ref([])
const categories = ref([])
const suggestions = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  search: '',
  category: '',
  share_type: '',
  price_min: null,
  price_max: null,
  sort_by: 'created_at'
})

// 对话框控制
const publishDialogVisible = ref(false)
const inquiryDialogVisible = ref(false)
const selectedMaterial = ref(null)

// 搜索建议防抖定时器
let suggestionTimer = null

// 计算属性
const sortOrder = computed(() => {
  if (searchForm.sort_by === 'price_desc') {
    return 'desc'
  }
  return 'asc'
})

const sortBy = computed(() => {
  if (searchForm.sort_by === 'price_desc') {
    return 'price'
  }
  return searchForm.sort_by
})

// 生命周期
onMounted(() => {
  loadCategories()
  loadMaterials()
})

// 监听搜索输入
watch(() => searchForm.search, (newValue) => {
  if (suggestionTimer) {
    clearTimeout(suggestionTimer)
  }
  
  if (newValue && newValue.length >= 2) {
    suggestionTimer = setTimeout(() => {
      loadSearchSuggestions(newValue)
    }, 300)
  } else {
    suggestions.value = []
  }
})

// 方法定义
const loadMaterials = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      search: searchForm.search,
      category: searchForm.category,
      share_type: searchForm.share_type,
      price_min: searchForm.price_min,
      price_max: searchForm.price_max,
      sort_by: sortBy.value,
      sort_order: sortOrder.value,
      status: 'approved'
    }

    const response = await getSharedMaterials(params)
    if (response.success) {
      materials.value = response.body.shared_materials
      total.value = response.body.total
    } else {
      ElMessage.error(response.message || '获取共享件列表失败')
    }
  } catch (error) {
    console.error('获取共享件列表失败:', error)
    ElMessage.error('获取共享件列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await getMaterialCategories()
    if (response.success) {
      categories.value = response.body.categories
    }
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const loadSearchSuggestions = async (query) => {
  try {
    const response = await getSearchSuggestions({ query })
    if (response.success) {
      suggestions.value = response.body.suggestions
    }
  } catch (error) {
    console.error('获取搜索建议失败:', error)
  }
}

const searchMaterials = () => {
  currentPage.value = 1
  suggestions.value = []
  loadMaterials()
}

const handleSearchInput = () => {
  suggestions.value = []
}

const selectSuggestion = (suggestion) => {
  searchForm.search = suggestion.text
  suggestions.value = []
  searchMaterials()
}

const resetFilters = () => {
  Object.assign(searchForm, {
    search: '',
    category: '',
    share_type: '',
    price_min: null,
    price_max: null,
    sort_by: 'created_at'
  })
  searchMaterials()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadMaterials()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMaterials()
}

const viewMaterialDetail = (materialId) => {
  router.push(`/shared-materials/${materialId}`)
}

const showPublishDialog = () => {
  publishDialogVisible.value = true
}

const showInquiryDialog = (material) => {
  selectedMaterial.value = material
  inquiryDialogVisible.value = true
}

const handlePublishDialogClose = (done) => {
  publishDialogVisible.value = false
  if (done) done()
}

const handleInquiryDialogClose = (done) => {
  inquiryDialogVisible.value = false
  selectedMaterial.value = null
  if (done) done()
}

const handlePublishSuccess = () => {
  publishDialogVisible.value = false
  ElMessage.success('共享件发布成功，等待审核')
  loadMaterials()
}

const handleInquirySuccess = () => {
  inquiryDialogVisible.value = false
  ElMessage.success('询价申请提交成功')
  selectedMaterial.value = null
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN').format(price)
}

const getOwnerTypeColor = (type) => {
  const colorMap = {
    airline: 'primary',
    supplier: 'success',
    maintenance: 'warning',
    admin: 'info'
  }
  return colorMap[type] || 'default'
}

const getOwnerTypeText = (type) => {
  const textMap = {
    airline: '航空公司',
    supplier: '供应商',
    maintenance: '维修企业',
    admin: '管理员'
  }
  return textMap[type] || '未知'
}

// 统一导航事件处理
const handleQuickAction = (action) => {
  if (action === 'publish-demand') {
    ElMessage.info('快速发布需求功能')
  }
}

const handleToggleNotifications = () => {
  ElMessage.info('通知功能')
}
</script>

<style scoped>
.shared-materials-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-bar {
  position: relative;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  max-width: 600px;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-width: 600px;
  background: white;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-item:hover {
  background-color: #f5f7fa;
}

.suggestion-text {
  font-size: 14px;
  color: #303133;
}

.suggestion-meta {
  font-size: 12px;
  color: #909399;
}

.filter-bar {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-range .el-input {
  width: 120px;
}

.range-separator {
  color: #909399;
}

.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.material-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.material-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-badges {
  position: absolute;
  top: 8px;
  right: 8px;
}

.card-content {
  padding: 16px;
}

.material-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-info {
  margin: 0 0 12px 0;
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.part-number {
  font-weight: 500;
}

.material-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.material-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
}

.detail-row .label {
  color: #909399;
  min-width: 80px;
}

.quantity {
  color: #67c23a;
  font-weight: 500;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
}

.unit {
  font-size: 12px;
  color: #909399;
}

.owner-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.owner-name {
  color: #606266;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-actions .el-button {
  flex: 1;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .shared-materials-page {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .materials-grid {
    grid-template-columns: 1fr;
  }
}
</style>