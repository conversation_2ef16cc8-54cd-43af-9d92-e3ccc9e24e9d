<template>
  <div class="min-h-screen">
    <!-- 统一导航栏 -->
    <UnifiedHeader 
      :user-avatar="userAvatar"
      :unread-count="unreadCount"
      @quick-action="handleQuickAction"
      @toggle-notifications="handleToggleNotifications"
    />

    <!-- 主要内容区域 -->
    <main>
      <!-- 英雄区域 -->
      <section class="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-20">
        <div class="max-w-7xl mx-auto px-6 text-center">
          <h1 class="title-font text-5xl md:text-6xl font-bold mb-6">
            智能航材共享
            <span class="bg-gradient-to-r from-blue-200 to-green-200 bg-clip-text text-transparent">保障平台</span>
          </h1>
          <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-4xl mx-auto">
            连接航空公司、航材公司、维修企业和物流企业，实现航材资源高效共享和配置
          </p>
          
          <!-- CTA按钮 - 仅对未登录用户显示 -->
          <div v-if="!isAuthenticated" class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
            <button @click="$router.push('/login')" class="btn-primary">
              立即开始
            </button>
            <button class="text-white font-medium border border-white border-opacity-30 px-8 py-4 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all">
              观看演示
            </button>
          </div>
          
          <!-- 平台统计数据 -->
          <div v-if="platformStats" class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto" :class="isAuthenticated ? 'mt-8 mb-12' : 'mb-12'">
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">{{ formatNumber(platformStats.total_users) }}</div>
              <div class="text-blue-200 text-sm">注册用户</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">{{ formatNumber(platformStats.total_materials) }}</div>
              <div class="text-blue-200 text-sm">在线航材</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">¥{{ formatAmount(platformStats.total_transaction_amount) }}</div>
              <div class="text-blue-200 text-sm">累计交易额</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-white mb-2">{{ platformStats.avg_response_time }}h</div>
              <div class="text-blue-200 text-sm">平均响应时间</div>
            </div>
          </div>

          <!-- 快速搜索区域 
          <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 max-w-2xl mx-auto">
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <el-input
                  v-model="searchQuery"
                  size="large"
                  placeholder="搜索航材件号、型号或描述"
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <el-button type="primary" size="large" @click="handleSearch">
                搜索航材
              </el-button>
            </div>
          </div>
          -->
        </div>
      </section>

      <!-- 热门航材推荐区域 -->
      <section id="hot-materials" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="title-font text-4xl font-bold text-gray-800 mb-4">热门航材推荐</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              基于搜索热度和交易频次，为您推荐最受欢迎的航材产品
            </p>
          </div>

          <div v-if="hotMaterials && hotMaterials.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div 
              v-for="material in hotMaterials" 
              :key="material.id" 
              class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer"
              @click="viewMaterialDetail(material)"
            >
              <div class="relative h-48 overflow-hidden">
                <img 
                  :src="material.image_url" 
                  :alt="material.name" 
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  @error="handleImageError"
                >
                <div class="absolute top-4 right-4">
                  <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    热度 {{ material.hot_score }}
                  </span>
                </div>
                <div class="absolute top-4 left-4">
                  <span :class="getAvailabilityClass(material.availability)">
                    {{ material.availability }}
                  </span>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-start justify-between mb-3">
                  <div>
                    <h3 class="font-bold text-lg text-gray-800 mb-1">{{ material.name }}</h3>
                    <p class="text-blue-600 font-medium">{{ material.part_number }}</p>
                  </div>
                </div>
                <div class="space-y-2 mb-4">
                  <div class="flex justify-between text-sm text-gray-600">
                    <span>制造商：</span>
                    <span class="font-medium">{{ material.manufacturer }}</span>
                  </div>
                  <div class="flex justify-between text-sm text-gray-600">
                    <span>类别：</span>
                    <span class="font-medium">{{ material.category }}</span>
                  </div>
                  <div class="flex justify-between text-sm text-gray-600">
                    <span>平均价格：</span>
                    <span class="font-medium text-green-600">¥{{ formatNumber(material.avg_price) }}</span>
                  </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <span>搜索 {{ material.search_count }} 次</span>
                  <span>成交 {{ material.order_count }} 单</span>
                </div>
                <el-button type="primary" class="w-full" @click.stop="inquireMaterial(material)">
                  立即询价
                </el-button>
              </div>
            </div>
          </div>

          <div class="text-center mt-12">
            <el-button size="large" @click="$router.push('/app/marketplace')">
              查看更多航材
            </el-button>
          </div>
        </div>
      </section>
      <!-- 解决方案展示区 -->
      <section id="solutions" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="title-font text-4xl font-bold text-gray-800 mb-4">核心解决方案</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              专为航空行业设计的全方位航材保障解决方案
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 周转件服务 -->
            <div class="modern-card overflow-hidden group">
              <div class="h-64 bg-gradient-to-br from-blue-500 to-blue-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center text-white">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <el-icon class="text-4xl"><Star /></el-icon>
                    </div>
                    <h3 class="subtitle-font text-2xl font-bold">周转件服务</h3>
                    <p class="text-blue-100 mt-2">灵活服务，降低库存成本</p>
                  </div>
                </div>
              </div>
              <div class="p-8">
                <h4 class="subtitle-font text-xl text-gray-800 mb-4">智能周转件管理</h4>
                <p class="text-gray-600 mb-6">
                  通过智能匹配算法，为航空公司提供高效的周转件服务，显著降低库存成本和资金占用
                </p>
                <ul class="space-y-3 text-sm text-gray-600 mb-8">
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    智能需求匹配，2小时内响应
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    全程质量追溯，适航证书齐全
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    灵活租期，按需付费模式
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    专业物流，全国快速配送
                  </li>
                </ul>
                <div class="flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    <span class="font-semibold text-blue-600">节省成本</span> 高达40%
                  </div>
                  <template v-if="isAuthenticated">
                    <button @click="$router.push('/app/inventory')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">立即使用</button>
                  </template>
                  <template v-else>
                    <button @click="scrollToSection('success-cases')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">查看案例</button>
                  </template>
                </div>
              </div>
            </div>

            <!-- 消耗件保障 -->
            <div class="modern-card overflow-hidden group">
              <div class="h-64 bg-gradient-to-br from-green-500 to-green-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center text-white">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <el-icon class="text-4xl"><Tools /></el-icon>
                    </div>
                    <h3 class="subtitle-font text-2xl font-bold">消耗件保障</h3>
                    <p class="text-green-100 mt-2">精准预测，高效保障</p>
                  </div>
                </div>
              </div>
              <div class="p-8">
                <h4 class="subtitle-font text-xl text-gray-800 mb-4">智能库存预测</h4>
                <p class="text-gray-600 mb-6">
                  基于大数据分析和机器学习，精准预测消耗件需求，确保关键时刻的航材供应
                </p>
                <ul class="space-y-3 text-sm text-gray-600 mb-8">
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    AI驱动需求预测，准确率95%+
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    自动补货机制，库存零断货
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    多级库存优化，成本降低30%
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    实时库存监控，透明化管理
                  </li>
                </ul>
                <div class="flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    <span class="font-semibold text-green-600">库存周转</span> 提升60%
                  </div>
                  <template v-if="isAuthenticated">
                    <button @click="$router.push('/app/analytics')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">查看分析</button>
                  </template>
                  <template v-else>
                    <button @click="scrollToSection('industry-news')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">了解详情</button>
                  </template>
                </div>
              </div>
            </div>
            <!-- AOG紧急响应 -->
            <div class="modern-card overflow-hidden group">
              <div class="h-64 bg-gradient-to-br from-red-500 to-red-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center text-white">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <el-icon class="text-4xl"><Warning /></el-icon>
                    </div>
                    <h3 class="subtitle-font text-2xl font-bold">AOG紧急响应</h3>
                    <p class="text-red-100 mt-2">24小时，极速响应</p>
                  </div>
                </div>
              </div>
              <div class="p-8">
                <h4 class="subtitle-font text-xl text-gray-800 mb-4">7×24小时应急保障</h4>
                <p class="text-gray-600 mb-6">
                  专业的AOG紧急响应团队，为航空公司提供24小时不间断的紧急航材保障服务
                </p>
                <ul class="space-y-3 text-sm text-gray-600 mb-8">
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    2小时内响应，快速定位资源
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    全球网络，就近调配航材
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    专业团队，经验丰富可靠
                  </li>
                  <li class="flex items-center">
                    <el-icon class="text-green-500 mr-3"><Check /></el-icon>
                    实时跟踪，透明化服务流程
                  </li>
                </ul>
                <div class="flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    <span class="font-semibold text-red-600">响应时间</span> ≤2小时
                  </div>
                  <template v-if="isAuthenticated">
                    <button @click="$router.push('/app/orders?type=aog')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">紧急需求</button>
                  </template>
                  <template v-else>
                    <button @click="scrollToSection('hot-materials')" class="btn-primary" style="color: white !important; font-size: 14px !important; background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important; padding: 8px 16px !important; border-radius: 8px !important;">查看服务</button>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- 行业资讯区域 -->
      <section id="industry-news" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="title-font text-4xl font-bold text-gray-800 mb-4">行业资讯动态</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              及时了解航空行业最新动态，把握市场发展趋势
            </p>
          </div>

          <div v-if="industryNews && industryNews.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div 
              v-for="news in industryNews" 
              :key="news.id" 
              class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer"
              @click="viewNewsDetail(news)"
            >
              <div class="relative h-48 overflow-hidden">
                <img 
                  :src="news.image_url" 
                  :alt="news.title" 
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  @error="handleImageError"
                >
                <div class="absolute top-4 right-4">
                  <span :class="getImportanceClass(news.importance)">
                    {{ getImportanceText(news.importance) }}
                  </span>
                </div>
              </div>
              <div class="p-6">
                <h3 class="font-bold text-lg text-gray-800 mb-3 line-clamp-2">{{ news.title }}</h3>
                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ news.summary }}</p>
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>{{ news.source }}</span>
                  <span>{{ news.publish_date }}</span>
                </div>
                <div class="flex items-center justify-between mt-3">
                  <span class="text-xs text-gray-500">阅读 {{ news.read_count }} 次</span>
                  <el-button type="text" size="small" @click.stop="viewNewsDetail(news)">
                    阅读全文
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="text-center mt-12">
            <el-button size="large" @click="viewMoreNews()">
              查看更多资讯
            </el-button>
          </div>
        </div>
      </section>

      <!-- 成功案例区域 -->
      <section id="success-cases" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="title-font text-4xl font-bold text-gray-800 mb-4">成功案例展示</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              真实的合作案例，见证平台专业实力和服务质量
            </p>
          </div>

          <div v-if="successCases && successCases.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div 
              v-for="case_ in successCases" 
              :key="case_.id" 
              class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
            >
              <div class="relative h-48 overflow-hidden">
                <img 
                  :src="case_.image_url" 
                  :alt="case_.title" 
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  @error="handleImageError"
                >
                <div class="absolute top-4 right-4">
                  <span :class="getCaseTypeClass(case_.case_type)">
                    {{ getCaseTypeText(case_.case_type) }}
                  </span>
                </div>
              </div>
              <div class="p-6">
                <h3 class="font-bold text-lg text-gray-800 mb-3">{{ case_.title }}</h3>
                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ case_.description }}</p>
                <div class="space-y-2 mb-4">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">客户：</span>
                    <span class="font-medium text-blue-600">{{ case_.client }}</span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">节省成本：</span>
                    <span class="font-medium text-green-600">¥{{ formatNumber(case_.save_cost) }}</span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">响应时间：</span>
                    <span class="font-medium">{{ case_.response_time }}</span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">成功率：</span>
                    <span class="font-medium text-green-600">{{ case_.success_rate }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white py-16">
      <div class="max-w-7xl mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                </svg>
              </div>
              <div>
                <h3 class="title-font text-lg">航材共享保障平台</h3>
                <p class="text-gray-400 text-sm">专业·高效·可靠</p>
              </div>
            </div>
            <p class="text-gray-400 text-sm leading-relaxed">
              致力于为航空行业提供最专业的航材共享服务，让航材流通更高效，让航空更安全。
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-4">平台服务</h4>
            <ul class="space-y-2 text-sm text-gray-400">
              <li><a href="#" class="hover:text-white transition-colors">航材交易</a></li>
              <li><a href="#" class="hover:text-white transition-colors">周转件服务</a></li>
              <li><a href="#" class="hover:text-white transition-colors">AOG保障</a></li>
              <li><a href="#" class="hover:text-white transition-colors">维修外包</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold mb-4">行业资源</h4>
            <ul class="space-y-2 text-sm text-gray-400">
              <li><a href="#" class="hover:text-white transition-colors">行业资讯</a></li>
              <li><a href="#" class="hover:text-white transition-colors">政策法规</a></li>
              <li><a href="#" class="hover:text-white transition-colors">技术标准</a></li>
              <li><a href="#" class="hover:text-white transition-colors">市场分析</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold mb-4">联系我们</h4>
            <ul class="space-y-2 text-sm text-gray-400">
              <li>服务热线：400-888-0000</li>
              <li>邮箱：<EMAIL></li>
              <li>地址：北京市朝阳区航空科技园</li>
            </ul>
          </div>
        </div>
        <div class="border-t border-gray-800 mt-12 pt-8 text-center text-sm text-gray-400">
          <p>© 2025 航材共享保障平台. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import { getPlatformStatistics, getHotMaterials, getIndustryNews, getSuccessCases } from '@/api/portal'
import { useAuthStore } from '@/stores/auth'
import UnifiedHeader from '@/components/UnifiedHeader.vue'

export default {
  name: 'Homepage',
  components: {
    UnifiedHeader
  },
  setup() {
    // 认证状态
    const authStore = useAuthStore()
    const isAuthenticated = computed(() => authStore.isAuthenticated)
    
    // 用户相关数据
    const userAvatar = ref('')
    const unreadCount = ref(0)
    // 响应式数据
    const searchQuery = ref('')
    const platformStats = ref(null)
    const hotMaterials = ref([])
    const industryNews = ref([])
    const successCases = ref([])
    const loading = ref(false)

    // 获取平台统计数据
    const loadPlatformStatistics = async () => {
      try {
        const response = await getPlatformStatistics()
        if (response.success) {
          platformStats.value = response.body
        }
      } catch (error) {
        console.error('获取平台统计数据失败:', error)
      }
    }

    // 获取热门航材
    const loadHotMaterials = async () => {
      try {
        const response = await getHotMaterials({ limit: 6 })
        if (response.success) {
          hotMaterials.value = response.body.materials
        }
      } catch (error) {
        console.error('获取热门航材失败:', error)
      }
    }

    // 获取行业资讯
    const loadIndustryNews = async () => {
      try {
        const response = await getIndustryNews({ page: 1, per_page: 6 })
        if (response.success) {
          industryNews.value = response.body.news
        }
      } catch (error) {
        console.error('获取行业资讯失败:', error)
      }
    }

    // 获取成功案例
    const loadSuccessCases = async () => {
      try {
        const response = await getSuccessCases({ limit: 6 })
        if (response.success) {
          successCases.value = response.body.cases
        }
      } catch (error) {
        console.error('获取成功案例失败:', error)
      }
    }

    // 处理搜索
    const handleSearch = () => {
      if (!searchQuery.value.trim()) {
        ElMessage.warning('请输入搜索关键词')
        return
      }
      // 跳转到共享件市场页面
      window.location.href = `/shared-materials?search=${encodeURIComponent(searchQuery.value)}`
    }

    // 查看航材详情
    const viewMaterialDetail = (material) => {
      // 跳转到共享件详情页面
      window.location.href = `/shared-materials/${material.id}`
    }

    // 询价航材
    const inquireMaterial = (material) => {
      // 跳转到共享件市场页面
      window.location.href = `/shared-materials/${material.id}`
    }

    // 查看新闻详情
    const viewNewsDetail = (news) => {
      console.log('查看新闻详情:', news)
      ElMessage.info('新闻详情页面开发中...')
    }

    // 查看更多资讯
    const viewMoreNews = () => {
      ElMessage.info('资讯中心页面开发中...')
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/placeholder.jpg'
    }

    // 工具函数
    const formatNumber = (number) => {
      if (number >= 10000) {
        return (number / 10000).toFixed(1) + '万'
      }
      return number?.toLocaleString() || '0'
    }

    const formatAmount = (amount) => {
      if (amount >= 100000000) {
        return (amount / 100000000).toFixed(1) + '亿'
      } else if (amount >= 10000) {
        return (amount / 10000).toFixed(1) + '万'
      }
      return amount?.toLocaleString() || '0'
    }

    const getAvailabilityClass = (availability) => {
      const classes = {
        '现货': 'bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        '预订': 'bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        '缺货': 'bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium'
      }
      return classes[availability] || 'bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-medium'
    }

    const getImportanceClass = (importance) => {
      const classes = {
        'high': 'bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'medium': 'bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'low': 'bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium'
      }
      return classes[importance] || 'bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-medium'
    }

    const getImportanceText = (importance) => {
      const texts = {
        'high': '重要',
        'medium': '一般',
        'low': '普通'
      }
      return texts[importance] || '普通'
    }

    const getCaseTypeClass = (caseType) => {
      const classes = {
        'aog': 'bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'cost_saving': 'bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'bulk_purchase': 'bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'maintenance': 'bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'inventory_sharing': 'bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium',
        'digital_transformation': 'bg-indigo-500 text-white px-2 py-1 rounded-full text-xs font-medium'
      }
      return classes[caseType] || 'bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-medium'
    }

    const getCaseTypeText = (caseType) => {
      const texts = {
        'aog': 'AOG保障',
        'cost_saving': '成本节约',
        'bulk_purchase': '批量采购',
        'maintenance': '维修服务',
        'inventory_sharing': '库存共享',
        'digital_transformation': '数字化转型'
      }
      return texts[caseType] || '案例'
    }

    // 事件处理方法
    const handleQuickAction = (action) => {
      if (action === 'publish-demand') {
        // 处理发布需求快速操作
        ElMessage.info('快速发布需求功能')
      }
    }

    const handleToggleNotifications = () => {
      // 处理通知切换
      ElMessage.info('通知功能')
    }

    // 页面内锚点跳转方法
    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }

    // 页面加载时获取数据
    onMounted(() => {
      loadPlatformStatistics()
      loadHotMaterials()
      loadIndustryNews()
      loadSuccessCases()
    })

    return {
      // 认证状态
      isAuthenticated,
      userAvatar,
      unreadCount,
      
      // 原有数据
      searchQuery,
      platformStats,
      hotMaterials,
      industryNews,
      successCases,
      loading,
      
      // 事件处理
      handleQuickAction,
      handleToggleNotifications,
      scrollToSection,
      
      // 原有方法
      handleSearch,
      viewMaterialDetail,
      inquireMaterial,
      viewNewsDetail,
      viewMoreNews,
      handleImageError,
      formatNumber,
      formatAmount,
      getAvailabilityClass,
      getImportanceClass,
      getImportanceText,
      getCaseTypeClass,
      getCaseTypeText
    }
  }
}
</script>

<style scoped>
/* 设计系统 */
:root {
  --primary-blue: #3B82F6;
  --secondary-blue: #1D4ED8;
  --accent-green: #10B981;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-800: #1F2937;
  --gray-900: #111827;
}

/* 字体样式 */
.title-font {
  font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.subtitle-font {
  font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  color: white;
  font-weight: 600;
  padding: 12px 32px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* 卡片样式 */
.modern-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* 搜索输入框样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title-font {
    font-size: 2.5rem;
  }
  
  .modern-card {
    margin-bottom: 2rem;
  }
  
  .btn-primary {
    padding: 10px 24px;
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 渐变文字效果 */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>