<template>
  <div class="orders">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">我的订单</h1>
        <p class="text-gray-600 mt-2">管理您的采购和销售订单</p>
      </div>
      <el-button type="primary" @click="createNewOrder">
        <Plus class="w-4 h-4 mr-2" />
        创建订单
      </el-button>
    </div>

    <!-- 订单统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Clock class="w-6 h-6 text-warning-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ stats.pending }}</div>
        <div class="text-gray-600">待处理</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Box class="w-6 h-6 text-primary-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ stats.processing }}</div>
        <div class="text-gray-600">处理中</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <SuccessFilled class="w-6 h-6 text-success-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ stats.completed }}</div>
        <div class="text-gray-600">已完成</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <DataAnalysis class="w-6 h-6 text-gray-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">¥{{ formatCurrency(stats.totalAmount) }}</div>
        <div class="text-gray-600">总金额</div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="modern-card p-6 mb-8">
      <div class="flex flex-col lg:flex-row gap-4">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索订单号、航材名称..."
          clearable
          style="max-width: 300px"
        >
          <template #prefix>
            <Search class="w-4 h-4 text-gray-400" />
          </template>
        </el-input>

        <el-select v-model="filterStatus" placeholder="订单状态" style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="待确认" value="pending" />
          <el-option label="已确认" value="confirmed" />
          <el-option label="处理中" value="processing" />
          <el-option label="已发货" value="shipped" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>

        <el-select v-model="filterType" placeholder="订单类型" style="width: 150px">
          <el-option label="全部类型" value="" />
          <el-option label="采购订单" value="purchase" />
          <el-option label="销售订单" value="sale" />
          <el-option label="周转件租赁" value="rental" />
          <el-option label="AOG紧急" value="aog" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />

        <el-button @click="handleSearch">搜索</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="modern-card">
      <el-table :data="orders" style="width: 100%" v-loading="loading">
        <el-table-column prop="order_number" label="订单号" width="160">
          <template #default="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row.id)">
              {{ row.order_number }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderTypeColor(row.type)" size="small">
              {{ getOrderTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="customer" label="客户/供应商" width="150">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <el-avatar :size="24" :src="row.customer.avatar">
                {{ row.customer.name.charAt(0) }}
              </el-avatar>
              <span class="text-sm">{{ row.customer.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="航材信息" min-width="200">
          <template #default="{ row }">
            <div>
              <div class="font-medium text-gray-800">{{ row.material_name }}</div>
              <div class="text-sm text-gray-500">零件号: {{ row.part_number }}</div>
              <div class="text-sm text-gray-500">数量: {{ row.quantity }} {{ row.unit }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="total_amount" label="金额" width="120" align="right">
          <template #default="{ row }">
            <span class="font-medium">¥{{ formatCurrency(row.total_amount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">{{ formatDate(row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="flex space-x-2">
              <el-button link type="primary" size="small" @click="viewOrderDetail(row.id)">
                查看
              </el-button>
              <el-button 
                v-if="canEdit(row.status)" 
                link 
                type="warning" 
                size="small" 
                @click="editOrder(row.id)"
              >
                编辑
              </el-button>
              <el-button 
                v-if="canCancel(row.status)" 
                link 
                type="danger" 
                size="small" 
                @click="cancelOrder(row.id)"
              >
                取消
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog v-model="showDetailDialog" :order-id="selectedOrderId" />
    
    <!-- 创建订单弹窗 -->
    <CreateOrderDialog v-model="showCreateDialog" @created="handleOrderCreated" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Clock, Box, SuccessFilled, DataAnalysis, Search 
} from '@element-plus/icons-vue'
import { ordersApi } from '@/api/orders'
import OrderDetailDialog from '@/components/OrderDetailDialog.vue'
import CreateOrderDialog from '@/components/CreateOrderDialog.vue'

// 状态
const loading = ref(false)
const searchKeyword = ref('')
const filterStatus = ref('')
const filterType = ref('')
const dateRange = ref([])
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const selectedOrderId = ref(null)

// 统计数据
const stats = ref({
  pending: 8,
  processing: 15,
  completed: 42,
  totalAmount: 1285000
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 订单列表
const orders = ref([])

// 模拟数据
const initializeMockData = () => {
  orders.value = [
    {
      id: 1,
      order_number: 'PO-2025-001',
      type: 'purchase',
      status: 'processing',
      customer: {
        name: '中航材北京公司',
        avatar: ''
      },
      material_name: '发动机高压压气机叶片',
      part_number: 'CFM56-7B-001',
      quantity: 2,
      unit: '件',
      unit_price: 35600,
      total_amount: 71200,
      created_at: '2025-01-10',
      estimated_delivery: '2025-01-20'
    },
    {
      id: 2,
      order_number: 'SO-2025-002',
      type: 'sale',
      status: 'shipped',
      customer: {
        name: '东方航空维修',
        avatar: ''
      },
      material_name: '主起落架减震支柱',
      part_number: 'A320-32-1001',
      quantity: 1,
      unit: '套',
      unit_price: 128000,
      total_amount: 128000,
      created_at: '2025-01-08',
      estimated_delivery: '2025-01-15'
    },
    {
      id: 3,
      order_number: 'AOG-2025-003',
      type: 'aog',
      status: 'confirmed',
      customer: {
        name: '南方航空维修',
        avatar: ''
      },
      material_name: '轮胎组件',
      part_number: 'MLG-TIRE-001',
      quantity: 4,
      unit: '件',
      unit_price: 8900,
      total_amount: 35600,
      created_at: '2025-01-12',
      estimated_delivery: '2025-01-13'
    }
  ]
  pagination.total = orders.value.length
}

// 方法
const handleSearch = () => {
  console.log('搜索订单:', { searchKeyword: searchKeyword.value, filterStatus: filterStatus.value })
}

const resetFilters = () => {
  searchKeyword.value = ''
  filterStatus.value = ''
  filterType.value = ''
  dateRange.value = []
}

const createNewOrder = () => {
  showCreateDialog.value = true
}

const viewOrderDetail = (orderId) => {
  selectedOrderId.value = orderId
  showDetailDialog.value = true
}

const editOrder = (orderId) => {
  ElMessage.info(`编辑订单 ${orderId}`)
}

const cancelOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
      type: 'warning'
    })
    ElMessage.success('订单已取消')
  } catch (error) {
    // 用户取消
  }
}

const handleOrderCreated = () => {
  ElMessage.success('订单创建成功')
  // 重新加载订单列表
}

const handleSizeChange = (size) => {
  pagination.size = size
}

const handleCurrentChange = (page) => {
  pagination.page = page
}

// 辅助函数
const getOrderTypeColor = (type) => {
  const colors = {
    purchase: '',
    sale: 'success',
    rental: 'warning',
    aog: 'danger'
  }
  return colors[type] || ''
}

const getOrderTypeText = (type) => {
  const texts = {
    purchase: '采购',
    sale: '销售',
    rental: '租赁',
    aog: 'AOG'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    confirmed: 'primary',
    processing: 'primary',
    shipped: 'success',
    completed: 'success',
    cancelled: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    processing: '处理中',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const canEdit = (status) => {
  return ['pending', 'confirmed'].includes(status)
}

const canCancel = (status) => {
  return ['pending', 'confirmed', 'processing'].includes(status)
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>