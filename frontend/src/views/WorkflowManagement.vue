<template>
  <div class="workflow-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">工作流管理</h1>
        <p class="text-gray-600 mt-2">管理审批流程和工作流状态</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showStartDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          启动工作流
        </el-button>
        <el-button @click="refreshWorkflows">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 工作流统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Document class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.totalWorkflows }}</div>
        <div class="text-gray-600">总工作流</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Clock class="w-6 h-6 text-orange-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.pendingWorkflows }}</div>
        <div class="text-gray-600">待处理</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <CircleCheck class="w-6 h-6 text-green-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.completedWorkflows }}</div>
        <div class="text-gray-600">已完成</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <DataLine class="w-6 h-6 text-purple-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.avgProcessTime }}小时</div>
        <div class="text-gray-600">平均处理时间</div>
      </div>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="待处理" name="pending">
        <WorkflowGrid 
          :workflows="pendingWorkflows" 
          :loading="loading"
          @process="handleProcessWorkflow"
          @view="handleViewWorkflow"
          @withdraw="handleWithdrawWorkflow"
        />
      </el-tab-pane>
      
      <el-tab-pane label="进行中" name="in_progress">
        <WorkflowGrid 
          :workflows="inProgressWorkflows" 
          :loading="loading"
          @view="handleViewWorkflow"
          @withdraw="handleWithdrawWorkflow"
        />
      </el-tab-pane>
      
      <el-tab-pane label="已完成" name="completed">
        <WorkflowGrid 
          :workflows="completedWorkflows" 
          :loading="loading"
          @view="handleViewWorkflow"
        />
      </el-tab-pane>
      
      <el-tab-pane label="我发起的" name="initiated">
        <WorkflowGrid 
          :workflows="initiatedWorkflows" 
          :loading="loading"
          @view="handleViewWorkflow"
          @withdraw="handleWithdrawWorkflow"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 启动工作流弹窗 -->
    <StartWorkflowDialog 
      v-model="showStartDialog" 
      @success="handleWorkflowStarted"
    />
    
    <!-- 工作流详情弹窗 -->
    <WorkflowDetailDialog 
      v-model="showDetailDialog" 
      :workflow-id="selectedWorkflowId"
    />
    
    <!-- 处理工作流弹窗 -->
    <ProcessWorkflowDialog 
      v-model="showProcessDialog" 
      :workflow-id="selectedWorkflowId"
      :step-id="selectedStepId"
      @success="handleWorkflowProcessed"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Refresh, Document, Clock, CircleCheck, DataLine 
} from '@element-plus/icons-vue'
import { workflowApi } from '@/api/workflow'
import WorkflowGrid from '@/components/WorkflowGrid.vue'
import StartWorkflowDialog from '@/components/StartWorkflowDialog.vue'
import WorkflowDetailDialog from '@/components/WorkflowDetailDialog.vue'
import ProcessWorkflowDialog from '@/components/ProcessWorkflowDialog.vue'

// 状态
const loading = ref(false)
const activeTab = ref('pending')
const showStartDialog = ref(false)
const showDetailDialog = ref(false)
const showProcessDialog = ref(false)
const selectedWorkflowId = ref(null)
const selectedStepId = ref(null)

// 统计数据
const statistics = ref({
  totalWorkflows: 45,
  pendingWorkflows: 12,
  completedWorkflows: 28,
  avgProcessTime: 18
})

// 工作流列表
const pendingWorkflows = ref([])
const inProgressWorkflows = ref([])
const completedWorkflows = ref([])
const initiatedWorkflows = ref([])

// 模拟数据
const initializeMockData = () => {
  const mockWorkflows = [
    {
      id: 'WF-2025-001',
      title: '采购订单审批 - PO240001',
      type: 'purchase_order',
      status: 'pending',
      current_step: '部门经理审批',
      initiator: '张三',
      created_at: '2025-01-13 09:00:00',
      priority: 'normal',
      object_type: '采购订单',
      object_id: 'PO240001'
    },
    {
      id: 'WF-2025-002',
      title: 'AOG需求审批 - DEM240002',
      type: 'aog_approval',
      status: 'in_progress',
      current_step: '财务审批',
      initiator: '李四',
      created_at: '2025-01-13 10:30:00',
      priority: 'urgent',
      object_type: 'AOG需求',
      object_id: 'DEM240002'
    },
    {
      id: 'WF-2025-003',
      title: '销售合同审批 - SO240003',
      type: 'sales_contract',
      status: 'completed',
      current_step: '已完成',
      initiator: '王五',
      created_at: '2025-01-12 14:20:00',
      completed_at: '2025-01-13 11:45:00',
      priority: 'high',
      object_type: '销售合同',
      object_id: 'SO240003'
    }
  ]

  pendingWorkflows.value = mockWorkflows.filter(w => w.status === 'pending')
  inProgressWorkflows.value = mockWorkflows.filter(w => w.status === 'in_progress')
  completedWorkflows.value = mockWorkflows.filter(w => w.status === 'completed')
  initiatedWorkflows.value = mockWorkflows
}

// 方法
const refreshWorkflows = async () => {
  try {
    loading.value = true
    // await loadWorkflowsByTab(activeTab.value)
    initializeMockData()
    ElMessage.success('工作流列表已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTabClick = (tab) => {
  loadWorkflowsByTab(tab.name)
}

const loadWorkflowsByTab = async (tabName) => {
  try {
    loading.value = true
    
    switch (tabName) {
      case 'pending':
        // const pendingData = await workflowApi.getPendingWorkflows({ status: 'pending' })
        // pendingWorkflows.value = pendingData.body.workflows
        break
      case 'in_progress':
        // const inProgressData = await workflowApi.getPendingWorkflows({ status: 'in_progress' })
        // inProgressWorkflows.value = inProgressData.body.workflows
        break
      case 'completed':
        // const completedData = await workflowApi.getPendingWorkflows({ status: 'completed' })
        // completedWorkflows.value = completedData.body.workflows
        break
      case 'initiated':
        // const initiatedData = await workflowApi.getWorkflowHistory({ initiator: 'current_user' })
        // initiatedWorkflows.value = initiatedData.body.workflows
        break
    }
  } catch (error) {
    ElMessage.error('加载工作流失败')
  } finally {
    loading.value = false
  }
}

const handleProcessWorkflow = (workflowId, stepId) => {
  selectedWorkflowId.value = workflowId
  selectedStepId.value = stepId
  showProcessDialog.value = true
}

const handleViewWorkflow = (workflowId) => {
  selectedWorkflowId.value = workflowId
  showDetailDialog.value = true
}

const handleWithdrawWorkflow = async (workflowId) => {
  try {
    await ElMessageBox.confirm(
      '确定要撤回此工作流吗？撤回后流程将终止。',
      '确认撤回',
      { type: 'warning' }
    )
    
    // await workflowApi.withdrawWorkflow(workflowId, { reason: '用户主动撤回' })
    ElMessage.success('工作流已撤回')
    refreshWorkflows()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败')
    }
  }
}

const handleWorkflowStarted = () => {
  ElMessage.success('工作流启动成功')
  refreshWorkflows()
}

const handleWorkflowProcessed = () => {
  ElMessage.success('工作流处理成功')
  refreshWorkflows()
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
</style>