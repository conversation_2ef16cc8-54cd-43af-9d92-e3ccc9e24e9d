<template>
  <div class="aog-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800 flex items-center">
          <Warning class="w-8 h-8 text-red-500 mr-3" />
          AOG紧急响应
        </h1>
        <p class="text-gray-600 mt-2">Aircraft on Ground - 24小时紧急响应服务</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="danger" @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          创建AOG请求
        </el-button>
        <el-button type="warning" @click="showEmergencyContact = true">
          <Phone class="w-4 h-4 mr-2" />
          紧急联系
        </el-button>
        <el-button @click="refreshAOG">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 紧急状态总览 -->
    <div class="emergency-overview mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="modern-card p-6 text-center border-l-4 border-red-500">
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Warning class="w-6 h-6 text-red-600" />
          </div>
          <div class="text-2xl font-bold text-red-600">{{ statistics.activeCases }}</div>
          <div class="text-gray-600">活跃案例</div>
          <div class="text-xs text-red-500 mt-1">需要立即关注</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-orange-500">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Clock class="w-6 h-6 text-orange-600" />
          </div>
          <div class="text-2xl font-bold text-orange-600">{{ statistics.avgResponseTime }}分钟</div>
          <div class="text-gray-600">平均响应时间</div>
          <div class="text-xs text-orange-500 mt-1">目标: &lt;120分钟</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-green-500">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <CircleCheck class="w-6 h-6 text-green-600" />
          </div>
          <div class="text-2xl font-bold text-green-600">{{ statistics.resolutionRate }}%</div>
          <div class="text-gray-600">解决率</div>
          <div class="text-xs text-green-500 mt-1">本月统计</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-blue-500">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Users class="w-6 h-6 text-blue-600" />
          </div>
          <div class="text-2xl font-bold text-blue-600">{{ statistics.availableTeams }}</div>
          <div class="text-gray-600">可用响应团队</div>
          <div class="text-xs text-blue-500 mt-1">24小时待命</div>
        </div>
      </div>
    </div>

    <!-- 实时监控面板 -->
    <div class="monitoring-panel mb-8">
      <div class="modern-card">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold flex items-center">
            <Monitor class="w-5 h-5 mr-2 text-blue-600" />
            实时监控面板
          </h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 响应时间图表 -->
            <div class="chart-container">
              <h4 class="font-medium mb-3">响应时间趋势</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500">
                  <BarChart class="w-8 h-8 mx-auto mb-2" />
                  <div>响应时间图表</div>
                </div>
              </div>
            </div>
            
            <!-- 案例分布 -->
            <div class="chart-container">
              <h4 class="font-medium mb-3">案例分布</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500">
                  <PieChart class="w-8 h-8 mx-auto mb-2" />
                  <div>案例分布图表</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section mb-6">
      <div class="modern-card p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <el-select v-model="filters.status" placeholder="案例状态" style="width: 150px" size="small">
            <el-option label="全部状态" value="" />
            <el-option label="新建" value="new" />
            <el-option label="响应中" value="responding" />
            <el-option label="处理中" value="in_progress" />
            <el-option label="等待零件" value="waiting_parts" />
            <el-option label="已解决" value="resolved" />
          </el-select>
          
          <el-select v-model="filters.priority" placeholder="优先级" style="width: 120px" size="small">
            <el-option label="全部优先级" value="" />
            <el-option label="紧急" value="critical" />
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
          </el-select>
          
          <el-select v-model="filters.aircraftType" placeholder="机型" style="width: 150px" size="small">
            <el-option label="全部机型" value="" />
            <el-option label="A320" value="A320" />
            <el-option label="A330" value="A330" />
            <el-option label="B737" value="B737" />
            <el-option label="B777" value="B777" />
          </el-select>
          
          <el-input
            v-model="filters.search"
            placeholder="搜索尾号、航班号..."
            style="width: 200px"
            size="small"
            clearable
          >
            <template #prefix>
              <Search class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>
          
          <el-button @click="applyFilters" size="small">筛选</el-button>
          <el-button @click="resetFilters" size="small">重置</el-button>
        </div>
      </div>
    </div>

    <!-- AOG案例列表 -->
    <div class="modern-card">
      <div class="aog-list">
        <el-table :data="aogCases" v-loading="loading" style="width: 100%">
          <el-table-column width="60">
            <template #default="{ row }">
              <div class="priority-indicator">
                <div 
                  :class="getPriorityIndicatorClass(row.priority)"
                  class="w-3 h-3 rounded-full"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="case_number" label="案例编号" width="140">
            <template #default="{ row }">
              <el-link type="primary" @click="viewAOGDetail(row.id)">
                {{ row.case_number }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column label="飞机信息" min-width="180">
            <template #default="{ row }">
              <div>
                <div class="font-medium">{{ row.aircraft.tail_number }}</div>
                <div class="text-sm text-gray-600">{{ row.aircraft.aircraft_type }}</div>
                <div class="text-xs text-gray-500">{{ row.aircraft.location }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="故障描述" min-width="200">
            <template #default="{ row }">
              <div class="fault-description">
                <div class="font-medium text-sm">{{ row.fault.title }}</div>
                <div class="text-xs text-gray-600 mt-1 line-clamp-2">
                  {{ row.fault.description }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityColor(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="响应团队" width="150">
            <template #default="{ row }">
              <div v-if="row.assigned_team">
                <div class="text-sm font-medium">{{ row.assigned_team.leader }}</div>
                <div class="text-xs text-gray-600">{{ row.assigned_team.name }}</div>
              </div>
              <div v-else class="text-gray-400 text-sm">未分配</div>
            </template>
          </el-table-column>

          <el-table-column label="时间信息" width="150">
            <template #default="{ row }">
              <div class="time-info text-xs">
                <div>创建: {{ formatTime(row.created_at) }}</div>
                <div v-if="row.response_time" class="text-blue-600">
                  响应: {{ formatTime(row.response_time) }}
                </div>
                <div v-if="row.estimated_resolution" class="text-orange-600">
                  预计: {{ formatTime(row.estimated_resolution) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex space-x-2">
                <el-button 
                  v-if="row.status === 'new'"
                  link 
                  type="danger" 
                  size="small" 
                  @click="respondToAOG(row.id)"
                >
                  立即响应
                </el-button>
                <el-button 
                  link 
                  type="primary" 
                  size="small" 
                  @click="viewAOGDetail(row.id)"
                >
                  查看详情
                </el-button>
                <el-dropdown>
                  <el-button link size="small">
                    更多
                    <ArrowDown class="w-3 h-3 ml-1" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="assignTeam(row.id)">
                        分配团队
                      </el-dropdown-item>
                      <el-dropdown-item @click="updateStatus(row.id)">
                        更新状态
                      </el-dropdown-item>
                      <el-dropdown-item @click="quickMatch(row.id)">
                        快速匹配
                      </el-dropdown-item>
                      <el-dropdown-item @click="emergencyPurchase(row.id)">
                        紧急采购
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 紧急联系面板 -->
    <div v-if="showEmergencyContact" class="emergency-contact-panel fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center text-red-600">
          <Phone class="w-5 h-5 mr-2" />
          AOG紧急联系方式
        </h3>
        <div class="space-y-3">
          <div class="contact-item p-3 bg-red-50 rounded-lg">
            <div class="font-medium">24小时热线</div>
            <div class="text-red-600 font-bold text-lg">400-AOG-HELP</div>
          </div>
          <div class="contact-item p-3 bg-blue-50 rounded-lg">
            <div class="font-medium">技术支持</div>
            <div class="text-blue-600"><EMAIL></div>
          </div>
          <div class="contact-item p-3 bg-green-50 rounded-lg">
            <div class="font-medium">紧急协调</div>
            <div class="text-green-600"><EMAIL></div>
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <el-button @click="showEmergencyContact = false">关闭</el-button>
        </div>
      </div>
    </div>

    <!-- 创建AOG弹窗 -->
    <CreateAOGDialog 
      v-model="showCreateDialog" 
      @success="handleAOGCreated"
    />
    
    <!-- AOG详情弹窗 -->
    <AOGDetailDialog 
      v-model="showDetailDialog" 
      :aog-id="selectedAOGId"
    />
    
    <!-- 快速响应弹窗 -->
    <QuickResponseDialog 
      v-model="showResponseDialog" 
      :aog-id="selectedAOGId"
      @success="handleResponseComplete"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Warning, Plus, Phone, Refresh, Clock, CircleCheck, User,
  Monitor, BarChart, PieChart, Search, ArrowDown
} from '@element-plus/icons-vue'
import { aogApi } from '@/api/aog'
import CreateAOGDialog from '@/components/CreateAOGDialog.vue'
import AOGDetailDialog from '@/components/AOGDetailDialog.vue'
import QuickResponseDialog from '@/components/QuickResponseDialog.vue'

// 状态
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showResponseDialog = ref(false)
const showEmergencyContact = ref(false)
const selectedAOGId = ref(null)

// 统计数据
const statistics = ref({
  activeCases: 3,
  avgResponseTime: 87,
  resolutionRate: 94,
  availableTeams: 5
})

// 筛选器
const filters = reactive({
  status: '',
  priority: '',
  aircraftType: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// AOG案例列表
const aogCases = ref([])

// 模拟数据
const initializeMockData = () => {
  aogCases.value = [
    {
      id: 'AOG-2025-001',
      case_number: 'AOG-2025-001',
      aircraft: {
        tail_number: 'B-1234',
        aircraft_type: 'A320-200',
        location: '北京首都机场T3'
      },
      fault: {
        title: '左发动机振动异常',
        description: '左发动机在巡航阶段出现异常振动，飞行员报告仪表显示振动超标，需要立即检查发动机叶片和轴承组件。'
      },
      priority: 'critical',
      status: 'responding',
      assigned_team: {
        name: '北京AOG响应团队',
        leader: '张工程师'
      },
      created_at: '2025-01-13 14:30:00',
      response_time: '2025-01-13 14:45:00',
      estimated_resolution: '2025-01-13 22:00:00'
    },
    {
      id: 'AOG-2025-002',
      case_number: 'AOG-2025-002',
      aircraft: {
        tail_number: 'B-5678',
        aircraft_type: 'B737-800',
        location: '上海浦东机场'
      },
      fault: {
        title: '主起落架收放异常',
        description: '主起落架在收起过程中卡死，液压系统压力正常，疑似机械故障。'
      },
      priority: 'high',
      status: 'in_progress',
      assigned_team: {
        name: '上海维修团队',
        leader: '李技师'
      },
      created_at: '2025-01-13 16:20:00',
      response_time: '2025-01-13 16:50:00',
      estimated_resolution: '2025-01-14 08:00:00'
    },
    {
      id: 'AOG-2025-003',
      case_number: 'AOG-2025-003',
      aircraft: {
        tail_number: 'B-9999',
        aircraft_type: 'A330-300',
        location: '广州白云机场'
      },
      fault: {
        title: '右发动机启动失败',
        description: '右发动机在启动过程中无法正常点火，疑似燃油喷嘴故障。'
      },
      priority: 'medium',
      status: 'new',
      assigned_team: null,
      created_at: '2025-01-13 18:45:00',
      response_time: null,
      estimated_resolution: null
    }
  ]
  pagination.total = aogCases.value.length
}

// 方法
const refreshAOG = async () => {
  try {
    loading.value = true
    // await loadAOGCases()
    initializeMockData()
    ElMessage.success('AOG案例列表已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const applyFilters = async () => {
  try {
    loading.value = true
    pagination.page = 1
    // await loadAOGCases()
    ElMessage.success('筛选完成')
  } catch (error) {
    ElMessage.error('筛选失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  applyFilters()
}

const viewAOGDetail = (aogId) => {
  selectedAOGId.value = aogId
  showDetailDialog.value = true
}

const respondToAOG = (aogId) => {
  selectedAOGId.value = aogId
  showResponseDialog.value = true
}

const assignTeam = (aogId) => {
  ElMessage.info(`分配响应团队: ${aogId}`)
}

const updateStatus = (aogId) => {
  ElMessage.info(`更新状态: ${aogId}`)
}

const quickMatch = async (aogId) => {
  try {
    // await aogApi.quickPartMatch(aogId)
    ElMessage.success('正在进行快速零件匹配...')
  } catch (error) {
    ElMessage.error('快速匹配失败')
  }
}

const emergencyPurchase = (aogId) => {
  ElMessage.info(`启动紧急采购: ${aogId}`)
}

const handleAOGCreated = () => {
  ElMessage.success('AOG请求创建成功')
  refreshAOG()
}

const handleResponseComplete = () => {
  ElMessage.success('响应操作完成')
  refreshAOG()
}

const handleSizeChange = (size) => {
  pagination.size = size
  // loadAOGCases()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  // loadAOGCases()
}

// 辅助函数
const getPriorityIndicatorClass = (priority) => {
  const classes = {
    critical: 'bg-red-500 animate-pulse',
    high: 'bg-orange-500',
    medium: 'bg-yellow-500'
  }
  return classes[priority] || 'bg-gray-400'
}

const getPriorityColor = (priority) => {
  const colors = {
    critical: 'danger',
    high: 'warning',
    medium: ''
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    critical: '紧急',
    high: '高',
    medium: '中'
  }
  return texts[priority] || priority
}

const getStatusColor = (status) => {
  const colors = {
    new: 'danger',
    responding: 'warning',
    in_progress: 'primary',
    waiting_parts: 'info',
    resolved: 'success'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    new: '新建',
    responding: '响应中',
    in_progress: '处理中',
    waiting_parts: '等待零件',
    resolved: '已解决'
  }
  return texts[status] || status
}

const formatTime = (datetime) => {
  return datetime ? new Date(datetime).toLocaleString('zh-CN') : ''
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.aog-list {
  padding: 20px;
}

.priority-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fault-description {
  max-width: 200px;
}

.time-info div {
  margin-bottom: 2px;
}

.emergency-contact-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.contact-item {
  transition: all 0.2s;
}

.contact-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.chart-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}
</style>