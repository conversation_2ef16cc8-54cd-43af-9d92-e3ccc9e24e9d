<template>
  <div class="inventory">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800">库存管理</h1>
        <p class="text-gray-600 mt-2">实时监控和管理航材库存</p>
      </div>
      <div class="flex space-x-3">
        <!-- 入库按钮 - 需要库存管理权限 -->
        <PermissionGate permission="manage_own_inventory">
          <el-button @click="showInboundDialog = true">
            <Plus class="w-4 h-4 mr-2" />
            入库
          </el-button>
        </PermissionGate>
        
        <!-- 出库按钮 - 需要库存管理权限 -->
        <PermissionGate permission="manage_own_inventory">
          <el-button @click="showOutboundDialog = true">
            <Minus class="w-4 h-4 mr-2" />
            出库
          </el-button>
        </PermissionGate>
        
        <!-- 调拨按钮 - 需要库存管理权限 -->
        <PermissionGate permission="manage_own_inventory">
          <el-button @click="showTransferDialog = true">
            <Switch class="w-4 h-4 mr-2" />
            调拨
          </el-button>
        </PermissionGate>
        
        <!-- 批量操作按钮 - 需要高级权限 -->
        <PermissionGate :permissionLevel="2">
          <el-button type="info" @click="showBatchOperationDialog = true">
            <Operation class="w-4 h-4 mr-2" />
            批量操作
          </el-button>
        </PermissionGate>
        
        <!-- 导出数据按钮 - 平台员工或管理员可见 -->
        <PermissionGate :userType="['platform_staff', 'admin']" :allowCrossCompany="true">
          <el-button plain @click="exportInventoryData">
            <Download class="w-4 h-4 mr-2" />
            导出数据
          </el-button>
        </PermissionGate>
      </div>
    </div>

    <!-- 库存统计 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Box class="w-6 h-6 text-primary-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.totalItems }}</div>
        <div class="text-gray-600">库存总数</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Warning class="w-6 h-6 text-warning-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.warningItems }}</div>
        <div class="text-gray-600">预警商品</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-error-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Close class="w-6 h-6 text-error-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.shortageItems }}</div>
        <div class="text-gray-600">缺货商品</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <Money class="w-6 h-6 text-success-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">¥{{ formatCurrency(statistics.totalValue) }}</div>
        <div class="text-gray-600">库存总值</div>
      </div>
      
      <div class="modern-card p-6 text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
          <DataLine class="w-6 h-6 text-blue-600" />
        </div>
        <div class="text-2xl font-bold text-gray-800">{{ statistics.turnoverRate }}%</div>
        <div class="text-gray-600">周转率</div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="modern-card p-6 mb-8">
      <div class="flex flex-col lg:flex-row gap-4">
        <el-input
          v-model="searchForm.search"
          placeholder="搜索航材名称、零件号..."
          clearable
          style="max-width: 300px"
        >
          <template #prefix>
            <Search class="w-4 h-4 text-gray-400" />
          </template>
        </el-input>

        <el-select v-model="searchForm.category" placeholder="航材类别" style="width: 150px">
          <el-option label="全部类别" value="" />
          <el-option label="发动机" value="engine" />
          <el-option label="起落架" value="landing_gear" />
          <el-option label="航电设备" value="avionics" />
          <el-option label="液压系统" value="hydraulic" />
        </el-select>

        <el-select v-model="searchForm.status" placeholder="库存状态" style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="正常" value="normal" />
          <el-option label="预警" value="warning" />
          <el-option label="缺货" value="shortage" />
        </el-select>

        <el-select v-model="searchForm.location" placeholder="仓库位置" style="width: 150px">
          <el-option label="全部位置" value="" />
          <el-option label="北京仓" value="beijing" />
          <el-option label="上海仓" value="shanghai" />
          <el-option label="广州仓" value="guangzhou" />
        </el-select>

        <el-button @click="handleSearch">搜索</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </div>

    <!-- 库存列表 -->
    <div class="modern-card">
      <el-table :data="inventory" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="航材信息" min-width="250">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <img 
                v-if="row.material_info.image" 
                :src="row.material_info.image" 
                :alt="row.material_info.name"
                class="w-12 h-12 object-cover rounded-lg"
              />
              <div v-else class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <Box class="w-6 h-6 text-gray-400" />
              </div>
              <div>
                <div class="font-medium text-gray-800">{{ row.material_info.name }}</div>
                <div class="text-sm text-gray-500">零件号: {{ row.material_info.part_number }}</div>
                <div class="text-sm text-gray-500">机型: {{ row.material_info.aircraft_type }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="current_stock" label="当前库存" width="120" align="center">
          <template #default="{ row }">
            <div class="font-medium" :class="getStockColor(row.current_stock, row.safety_stock)">
              {{ row.current_stock }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="safety_stock" label="安全库存" width="120" align="center">
          <template #default="{ row }">
            <span class="text-gray-600">{{ row.safety_stock }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="location" label="库位" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getLocationText(row.location) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="unit_price" label="单价" width="100" align="right">
          <template #default="{ row }">
            <span class="font-medium">¥{{ formatCurrency(row.unit_price) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="库存总值" width="120" align="right">
          <template #default="{ row }">
            <span class="font-medium text-primary-600">
              ¥{{ formatCurrency(row.current_stock * row.unit_price) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="last_updated" label="更新时间" width="120">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">{{ formatDate(row.last_updated) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex space-x-2">
              <!-- 查看详情 - 所有用户都可以查看自己的库存详情 -->
              <PermissionGate permission="view_own_data">
                <el-button link type="primary" size="small" @click="viewInventoryDetail(row.id)">
                  详情
                </el-button>
              </PermissionGate>
              
              <!-- 入库操作 - 需要库存管理权限 -->
              <PermissionGate permission="manage_own_inventory" :resourceOwnerId="row.supplier_id">
                <el-button link type="warning" size="small" @click="quickInbound(row.id)">
                  入库
                </el-button>
              </PermissionGate>
              
              <!-- 出库操作 - 需要库存管理权限和资源所有权 -->
              <PermissionGate permission="manage_own_inventory" :resourceOwnerId="row.supplier_id">
                <el-button link type="danger" size="small" @click="quickOutbound(row.id)">
                  出库
                </el-button>
              </PermissionGate>
              
              <!-- 更多操作下拉菜单 - 根据权限显示不同的选项 -->
              <el-dropdown>
                <PermissionGate :permission="['manage_own_inventory', 'view_own_data']" mode="any">
                  <el-button link size="small">
                    更多
                    <ArrowDown />
                  </el-button>
                </PermissionGate>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 调拨 - 需要库存管理权限 -->
                    <PermissionGate permission="manage_own_inventory" :resourceOwnerId="row.supplier_id">
                      <el-dropdown-item @click="transferItem(row.id)">调拨</el-dropdown-item>
                    </PermissionGate>
                    
                    <!-- 更新价格 - 需要库存管理权限 -->
                    <PermissionGate permission="manage_own_inventory" :resourceOwnerId="row.supplier_id">
                      <el-dropdown-item @click="updatePrice(row.id)">更新价格</el-dropdown-item>
                    </PermissionGate>
                    
                    <!-- 操作历史 - 所有相关用户都可以查看 -->
                    <PermissionGate permission="view_own_data">
                      <el-dropdown-item @click="viewHistory(row.id)">操作历史</el-dropdown-item>
                    </PermissionGate>
                    
                    <!-- 发布共享 - 需要共享权限 -->
                    <PermissionGate permission="publish_shared" :resourceOwnerId="row.supplier_id">
                      <el-dropdown-item @click="publishShared(row.id)">发布共享</el-dropdown-item>
                    </PermissionGate>
                    
                    <!-- 管理员功能 - 仅管理员或平台员工可见 -->
                    <PermissionGate :userType="['admin', 'platform_staff']">
                      <el-dropdown-item divided @click="adminEditItem(row.id)">管理员编辑</el-dropdown-item>
                    </PermissionGate>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 入库弹窗 -->
    <InboundDialog v-model="showInboundDialog" @success="handleInventoryUpdate" />
    
    <!-- 出库弹窗 -->
    <OutboundDialog v-model="showOutboundDialog" @success="handleInventoryUpdate" />
    
    <!-- 调拨弹窗 -->
    <TransferDialog v-model="showTransferDialog" @success="handleInventoryUpdate" />
    
    <!-- 库存详情弹窗 -->
    <InventoryDetailDialog v-model="showDetailDialog" :inventory-id="selectedInventoryId" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Minus, Switch, Box, Warning, Close, 
  Money, DataLine, Search, ArrowDown, Operation, Download
} from '@element-plus/icons-vue'
import { inventoryApi } from '@/api/inventory'
import PermissionGate from '@/components/PermissionGate.vue'
import InboundDialog from '@/components/InboundDialog.vue'
import OutboundDialog from '@/components/OutboundDialog.vue'
import TransferDialog from '@/components/TransferDialog.vue'
import InventoryDetailDialog from '@/components/InventoryDetailDialog.vue'

// 状态
const loading = ref(false)
const showInboundDialog = ref(false)
const showOutboundDialog = ref(false)
const showTransferDialog = ref(false)
const showDetailDialog = ref(false)
const selectedInventoryId = ref(null)

// 搜索表单
const searchForm = reactive({
  search: '',
  category: '',
  status: '',
  location: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const statistics = ref({
  totalItems: 1850,
  warningItems: 23,
  shortageItems: 8,
  totalValue: 15600000,
  turnoverRate: 78
})

// 库存列表
const inventory = ref([])

// 模拟数据
const initializeMockData = () => {
  inventory.value = [
    {
      id: 1,
      material_info: {
        name: '发动机高压压气机叶片',
        part_number: 'CFM56-7B-001',
        aircraft_type: 'B737',
        category: 'engine',
        image: '/images/发动机4.jpg'
      },
      current_stock: 15,
      safety_stock: 10,
      location: 'beijing',
      unit_price: 35600,
      status: 'normal',
      last_updated: '2025-01-12'
    },
    {
      id: 2,
      material_info: {
        name: '主起落架减震支柱',
        part_number: 'A320-32-1001',
        aircraft_type: 'A320',
        category: 'landing_gear',
        image: '/images/机轮2.jpg'
      },
      current_stock: 3,
      safety_stock: 5,
      location: 'shanghai',
      unit_price: 128000,
      status: 'warning',
      last_updated: '2025-01-11'
    },
    {
      id: 3,
      material_info: {
        name: '轮胎组件',
        part_number: 'MLG-TIRE-001',
        aircraft_type: 'B777',
        category: 'wheels_brakes',
        image: '/images/机轮.jpg'
      },
      current_stock: 0,
      safety_stock: 8,
      location: 'guangzhou',
      unit_price: 8900,
      status: 'shortage',
      last_updated: '2025-01-10'
    },
    {
      id: 4,
      material_info: {
        name: '航电显示器',
        part_number: 'EFIS-DU-001',
        aircraft_type: 'A330',
        category: 'avionics',
        image: '/images/航电1.jpg'
      },
      current_stock: 12,
      safety_stock: 6,
      location: 'beijing',
      unit_price: 45000,
      status: 'normal',
      last_updated: '2025-01-09'
    }
  ]
  pagination.total = inventory.value.length
}

// 方法
const handleSearch = () => {
  console.log('搜索库存:', searchForm)
}

const resetFilters = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
}

const viewInventoryDetail = (inventoryId) => {
  selectedInventoryId.value = inventoryId
  showDetailDialog.value = true
}

const quickInbound = (inventoryId) => {
  selectedInventoryId.value = inventoryId
  showInboundDialog.value = true
}

const quickOutbound = (inventoryId) => {
  selectedInventoryId.value = inventoryId
  showOutboundDialog.value = true
}

const transferItem = (inventoryId) => {
  selectedInventoryId.value = inventoryId
  showTransferDialog.value = true
}

const updatePrice = (inventoryId) => {
  ElMessage.info(`更新价格 ${inventoryId}`)
}

const viewHistory = (inventoryId) => {
  ElMessage.info(`查看操作历史 ${inventoryId}`)
}

// 新增权限控制相关方法
const publishShared = (inventoryId) => {
  ElMessage.info(`发布共享航材 ${inventoryId}`)
  // 这里可以跳转到共享发布页面或打开发布对话框
}

const adminEditItem = (inventoryId) => {
  ElMessage.warning(`管理员编辑功能 ${inventoryId}`)
  // 管理员特有的编辑功能
}

const exportInventoryData = () => {
  ElMessage.info('导出库存数据功能')
  // 导出当前筛选的库存数据
}

const showBatchOperationDialog = ref(false)

const handleInventoryUpdate = () => {
  ElMessage.success('操作成功')
  // 重新加载库存数据
}

const handleSizeChange = (size) => {
  pagination.size = size
}

const handleCurrentChange = (page) => {
  pagination.page = page
}

// 辅助函数
const getStockColor = (current, safety) => {
  if (current === 0) return 'text-red-600'
  if (current <= safety) return 'text-orange-600'
  return 'text-gray-800'
}

const getStatusColor = (status) => {
  const colors = {
    normal: 'success',
    warning: 'warning',
    shortage: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    normal: '正常',
    warning: '预警',
    shortage: '缺货'
  }
  return texts[status] || status
}

const getLocationText = (location) => {
  const texts = {
    beijing: '北京仓',
    shanghai: '上海仓',
    guangzhou: '广州仓'
  }
  return texts[location] || location
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>