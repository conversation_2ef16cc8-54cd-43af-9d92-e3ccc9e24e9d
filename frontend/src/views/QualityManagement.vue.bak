<template>
  <div class="quality-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800 flex items-center">
          <LockCheck class="w-8 h-8 text-green-500 mr-3" />
          质量管理
        </h1>
        <p class="text-gray-600 mt-2">航材质量控制、证书管理与合规检查</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showCreateCertDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新建证书
        </el-button>
        <el-button type="warning" @click="showComplianceCheck = true">
          <CheckCircle class="w-4 h-4 mr-2" />
          合规检查
        </el-button>
        <el-button @click="generateQualityReport">
          <Document class="w-4 h-4 mr-2" />
          质量报告
        </el-button>
        <el-button @click="refreshData">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 质量概览 -->
    <div class="quality-overview mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="modern-card p-6 text-center border-l-4 border-green-500">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <LockCheck class="w-6 h-6 text-green-600" />
          </div>
          <div class="text-2xl font-bold text-green-600">{{ statistics.validCertificates }}</div>
          <div class="text-gray-600">有效证书</div>
          <div class="text-xs text-green-500 mt-1">合规率 98.5%</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-orange-500">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Warning class="w-6 h-6 text-orange-600" />
          </div>
          <div class="text-2xl font-bold text-orange-600">{{ statistics.expiringSoon }}</div>
          <div class="text-gray-600">即将过期</div>
          <div class="text-xs text-orange-500 mt-1">30天内过期</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-blue-500">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Document class="w-6 h-6 text-blue-600" />
          </div>
          <div class="text-2xl font-bold text-blue-600">{{ statistics.testRecords }}</div>
          <div class="text-gray-600">测试记录</div>
          <div class="text-xs text-blue-500 mt-1">本月新增</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-purple-500">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Users class="w-6 h-6 text-purple-600" />
          </div>
          <div class="text-2xl font-bold text-purple-600">{{ statistics.supplierScore }}</div>
          <div class="text-gray-600">供应商评分</div>
          <div class="text-xs text-purple-500 mt-1">平均质量分</div>
        </div>
      </div>
    </div>

    <!-- 质量标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 证书管理 -->
      <el-tab-pane label="证书管理" name="certificates">
        <div class="certificates-section">
          <!-- 筛选器 -->
          <div class="filters mb-6">
            <div class="modern-card p-4">
              <div class="flex flex-wrap gap-4 items-center">
                <el-select v-model="certificateFilters.type" placeholder="证书类型" style="width: 150px" size="small">
                  <el-option label="全部类型" value="" />
                  <el-option label="8130-3适航标签" value="8130-3" />
                  <el-option label="CAAC适航证" value="caac" />
                  <el-option label="FAA适航证" value="faa" />
                  <el-option label="EASA适航证" value="easa" />
                </el-select>
                
                <el-select v-model="certificateFilters.status" placeholder="证书状态" style="width: 120px" size="small">
                  <el-option label="全部状态" value="" />
                  <el-option label="有效" value="valid" />
                  <el-option label="即将过期" value="expiring" />
                  <el-option label="已过期" value="expired" />
                </el-select>
                
                <el-input
                  v-model="certificateFilters.search"
                  placeholder="搜索零件号、证书号..."
                  style="width: 200px"
                  size="small"
                  clearable
                >
                  <template #prefix>
                    <Search class="w-4 h-4 text-gray-400" />
                  </template>
                </el-input>
                
                <el-button @click="applyCertificateFilters" size="small">筛选</el-button>
                <el-button @click="resetCertificateFilters" size="small">重置</el-button>
              </div>
            </div>
          </div>

          <!-- 证书列表 -->
          <div class="modern-card">
            <el-table :data="certificates" v-loading="loading" style="width: 100%">
              <el-table-column prop="certificate_number" label="证书编号" width="140">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewCertificateDetail(row.id)">
                    {{ row.certificate_number }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column prop="part_number" label="零件号" width="150" />

              <el-table-column prop="part_name" label="零件名称" min-width="180" />

              <el-table-column prop="certificate_type" label="证书类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCertificateTypeColor(row.certificate_type)" size="small">
                    {{ getCertificateTypeText(row.certificate_type) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="issuing_authority" label="颁发机构" width="120" />

              <el-table-column prop="expiry_date" label="有效期" width="120">
                <template #default="{ row }">
                  <div :class="getExpiryDateClass(row.expiry_date)">
                    {{ formatDate(row.expiry_date) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getCertificateStatusColor(row.status)" size="small">
                    {{ getCertificateStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <div class="flex space-x-2">
                    <el-button link type="primary" size="small" @click="viewCertificateDetail(row.id)">
                      查看
                    </el-button>
                    <el-button link type="success" size="small" @click="verifyCertificate(row.id)">
                      验证
                    </el-button>
                    <el-dropdown>
                      <el-button link size="small">
                        更多
                        <ArrowDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="downloadCertificate(row.id)">
                            下载证书
                          </el-dropdown-item>
                          <el-dropdown-item @click="renewCertificate(row.id)">
                            续期证书
                          </el-dropdown-item>
                          <el-dropdown-item @click="updateCertificateStatus(row.id)">
                            更新状态
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>

      <!-- 合规检查 -->
      <el-tab-pane label="合规检查" name="compliance">
        <div class="compliance-section">
          <!-- 检查清单 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">合规检查清单</h3>
              </div>
              <div class="p-6">
                <div class="space-y-4">
                  <div v-for="(item, index) in complianceChecklist" :key="index" class="checklist-item">
                    <div class="flex items-center justify-between p-3 border rounded-lg">
                      <div class="flex items-center">
                        <el-checkbox v-model="item.checked" @change="updateChecklistItem(item)" />
                        <div class="ml-3">
                          <div class="font-medium">{{ item.title }}</div>
                          <div class="text-sm text-gray-600">{{ item.description }}</div>
                        </div>
                      </div>
                      <el-tag :type="item.checked ? 'success' : 'warning'" size="small">
                        {{ item.checked ? '已完成' : '待检查' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 检查结果 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">检查结果</h3>
              </div>
              <div class="p-6">
                <div class="compliance-score text-center mb-6">
                  <div class="text-4xl font-bold text-green-600">{{ complianceScore }}%</div>
                  <div class="text-gray-600">合规评分</div>
                </div>
                
                <div class="space-y-3">
                  <div v-for="(result, index) in complianceResults" :key="index" class="result-item">
                    <div class="flex justify-between items-center p-3 rounded-lg" :class="getComplianceResultBg(result.status)">
                      <div>
                        <div class="font-medium">{{ result.category }}</div>
                        <div class="text-sm text-gray-600">{{ result.description }}</div>
                      </div>
                      <el-tag :type="getComplianceResultColor(result.status)" size="small">
                        {{ getComplianceResultText(result.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 测试记录 -->
      <el-tab-pane label="测试记录" name="testing">
        <div class="testing-section">
          <!-- 测试记录列表 -->
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">质量测试记录</h3>
                <el-button type="primary" size="small" @click="showCreateTestDialog = true">
                  <Plus class="w-4 h-4 mr-2" />
                  新建测试
                </el-button>
              </div>
            </div>
            <div class="p-6">
              <el-table :data="testRecords" style="width: 100%">
                <el-table-column prop="test_id" label="测试ID" width="120" />
                <el-table-column prop="part_number" label="零件号" width="150" />
                <el-table-column prop="test_type" label="测试类型" width="120" />
                <el-table-column prop="test_date" label="测试日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.test_date) }}
                  </template>
                </el-table-column>
                <el-table-column prop="tester" label="测试员" width="100" />
                <el-table-column prop="result" label="测试结果" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.result === 'pass' ? 'success' : 'danger'" size="small">
                      {{ row.result === 'pass' ? '通过' : '不通过' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="viewTestRecord(row.id)">
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 供应商评估 -->
      <el-tab-pane label="供应商评估" name="supplier">
        <div class="supplier-section">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 供应商质量排行 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">供应商质量排行</h3>
              </div>
              <div class="p-6">
                <div class="space-y-4">
                  <div v-for="(supplier, index) in supplierRanking" :key="index" class="supplier-item">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">
                          {{ index + 1 }}
                        </div>
                        <div>
                          <div class="font-medium">{{ supplier.name }}</div>
                          <div class="text-sm text-gray-600">{{ supplier.category }}</div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-bold" :class="getScoreColor(supplier.score)">
                          {{ supplier.score }}
                        </div>
                        <div class="text-sm text-gray-600">质量分</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 质量趋势 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">质量趋势分析</h3>
              </div>
              <div class="p-6">
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div class="text-gray-500 text-center">
                    <DataLine class="w-8 h-8 mx-auto mb-2" />
                    <div>质量趋势图表</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建证书弹窗 -->
    <CreateCertificateDialog 
      v-model="showCreateCertDialog" 
      @success="handleCertificateCreated"
    />
    
    <!-- 证书详情弹窗 -->
    <CertificateDetailDialog 
      v-model="showCertDetailDialog" 
      :certificate-id="selectedCertificateId"
    />
    
    <!-- 创建测试弹窗 -->
    <CreateTestDialog 
      v-model="showCreateTestDialog" 
      @success="handleTestCreated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  LockCheck, Plus, CheckCircle, Document, Refresh, Warning,
  User, Search, ArrowDown, DataLine
} from '@element-plus/icons-vue'
import { qualityApi } from '@/api/quality'
import CreateCertificateDialog from '@/components/CreateCertificateDialog.vue'
import CertificateDetailDialog from '@/components/CertificateDetailDialog.vue'
import CreateTestDialog from '@/components/CreateTestDialog.vue'

// 状态
const loading = ref(false)
const showCreateCertDialog = ref(false)
const showCertDetailDialog = ref(false)
const showCreateTestDialog = ref(false)
const showComplianceCheck = ref(false)
const selectedCertificateId = ref(null)
const activeTab = ref('certificates')

// 统计数据
const statistics = ref({
  validCertificates: 1248,
  expiringSoon: 23,
  testRecords: 156,
  supplierScore: 92.5
})

// 筛选器
const certificateFilters = reactive({
  type: '',
  status: '',
  search: ''
})

// 证书列表
const certificates = ref([])

// 测试记录
const testRecords = ref([])

// 合规检查清单
const complianceChecklist = ref([
  { id: 1, title: '适航证书验证', description: '验证零件适航证书的有效性', checked: true },
  { id: 2, title: '质量文件完整性', description: '检查质量相关文件是否完整', checked: true },
  { id: 3, title: '可追溯性检查', description: '验证零件来源的可追溯性', checked: false },
  { id: 4, title: '存储条件合规', description: '检查存储环境是否符合要求', checked: true },
  { id: 5, title: '包装标识规范', description: '验证包装标识是否符合规范', checked: false }
])

// 合规评分
const complianceScore = computed(() => {
  const checkedItems = complianceChecklist.value.filter(item => item.checked).length
  const totalItems = complianceChecklist.value.length
  return Math.round((checkedItems / totalItems) * 100)
})

// 合规结果
const complianceResults = ref([
  { category: '证书管理', description: '适航证书管理规范', status: 'pass' },
  { category: '质量控制', description: '质量控制流程合规', status: 'pass' },
  { category: '文档管理', description: '技术文档完整性', status: 'warning' },
  { category: '存储管理', description: '存储环境控制', status: 'pass' }
])

// 供应商排行
const supplierRanking = ref([
  { name: '东航技术', category: '发动机维修', score: 96.5 },
  { name: '南航机务', category: '航电维修', score: 94.2 },
  { name: '海航技术', category: '起落架维修', score: 92.8 },
  { name: '国航工程', category: '结构维修', score: 91.3 },
  { name: '厦航工程', category: '客舱维修', score: 89.7 }
])

// 模拟数据
const initializeMockData = () => {
  certificates.value = [
    {
      id: 'CERT-001',
      certificate_number: '8130-2025-001',
      part_number: 'CFM56-7B26-001',
      part_name: 'CFM56发动机高压压气机叶片',
      certificate_type: '8130-3',
      issuing_authority: 'CAAC',
      issue_date: '2025-01-01',
      expiry_date: '2025-12-31',
      status: 'valid'
    },
    {
      id: 'CERT-002',
      certificate_number: 'FAA-2025-002',
      part_number: 'A320-MLG-001',
      part_name: 'A320主起落架组件',
      certificate_type: 'faa',
      issuing_authority: 'FAA',
      issue_date: '2024-12-01',
      expiry_date: '2025-02-15',
      status: 'expiring'
    },
    {
      id: 'CERT-003',
      certificate_number: 'EASA-2024-003',
      part_number: 'HYD-PUMP-001',
      part_name: '液压泵组件',
      certificate_type: 'easa',
      issuing_authority: 'EASA',
      issue_date: '2024-01-01',
      expiry_date: '2025-01-01',
      status: 'expired'
    }
  ]

  testRecords.value = [
    {
      id: 'TEST-001',
      test_id: 'TEST-2025-001',
      part_number: 'CFM56-7B26-001',
      test_type: '材料检测',
      test_date: '2025-01-10',
      tester: '张工程师',
      result: 'pass'
    },
    {
      id: 'TEST-002',
      test_id: 'TEST-2025-002',
      part_number: 'A320-MLG-001',
      test_type: '功能测试',
      test_date: '2025-01-08',
      tester: '李技师',
      result: 'pass'
    },
    {
      id: 'TEST-003',
      test_id: 'TEST-2025-003',
      part_number: 'HYD-PUMP-001',
      test_type: '压力测试',
      test_date: '2025-01-05',
      tester: '王工程师',
      result: 'fail'
    }
  ]
}

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    // await loadQualityData()
    initializeMockData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTabClick = (tab) => {
  console.log('切换标签页:', tab.name)
}

const applyCertificateFilters = () => {
  console.log('应用证书筛选:', certificateFilters)
}

const resetCertificateFilters = () => {
  Object.keys(certificateFilters).forEach(key => {
    certificateFilters[key] = ''
  })
}

const viewCertificateDetail = (certificateId) => {
  selectedCertificateId.value = certificateId
  showCertDetailDialog.value = true
}

const verifyCertificate = async (certificateId) => {
  try {
    // await qualityApi.verifyCertificate(certificateId)
    ElMessage.success('证书验证通过')
  } catch (error) {
    ElMessage.error('证书验证失败')
  }
}

const downloadCertificate = (certificateId) => {
  ElMessage.info(`下载证书: ${certificateId}`)
}

const renewCertificate = (certificateId) => {
  ElMessage.info(`续期证书: ${certificateId}`)
}

const updateCertificateStatus = (certificateId) => {
  ElMessage.info(`更新证书状态: ${certificateId}`)
}

const updateChecklistItem = (item) => {
  console.log('更新检查项:', item)
}

const viewTestRecord = (testId) => {
  ElMessage.info(`查看测试记录: ${testId}`)
}

const generateQualityReport = async () => {
  try {
    // await qualityApi.generateQualityReport({ report_type: 'monthly', format: 'pdf' })
    ElMessage.success('质量报告生成中...')
  } catch (error) {
    ElMessage.error('报告生成失败')
  }
}

const handleCertificateCreated = () => {
  ElMessage.success('证书创建成功')
  refreshData()
}

const handleTestCreated = () => {
  ElMessage.success('测试记录创建成功')
  refreshData()
}

// 辅助函数
const getCertificateTypeColor = (type) => {
  const colors = {
    '8130-3': 'primary',
    'caac': 'success',
    'faa': 'warning',
    'easa': 'info'
  }
  return colors[type] || ''
}

const getCertificateTypeText = (type) => {
  const texts = {
    '8130-3': '8130-3',
    'caac': 'CAAC',
    'faa': 'FAA',
    'easa': 'EASA'
  }
  return texts[type] || type
}

const getCertificateStatusColor = (status) => {
  const colors = {
    valid: 'success',
    expiring: 'warning',
    expired: 'danger'
  }
  return colors[status] || ''
}

const getCertificateStatusText = (status) => {
  const texts = {
    valid: '有效',
    expiring: '即将过期',
    expired: '已过期'
  }
  return texts[status] || status
}

const getExpiryDateClass = (expiryDate) => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffDays = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'text-red-500 font-bold'
  if (diffDays <= 30) return 'text-orange-500 font-bold'
  return 'text-gray-700'
}

const getComplianceResultBg = (status) => {
  const classes = {
    pass: 'bg-green-50 border border-green-200',
    warning: 'bg-orange-50 border border-orange-200',
    fail: 'bg-red-50 border border-red-200'
  }
  return classes[status] || ''
}

const getComplianceResultColor = (status) => {
  const colors = {
    pass: 'success',
    warning: 'warning',
    fail: 'danger'
  }
  return colors[status] || ''
}

const getComplianceResultText = (status) => {
  const texts = {
    pass: '合规',
    warning: '警告',
    fail: '不合规'
  }
  return texts[status] || status
}

const getScoreColor = (score) => {
  if (score >= 95) return 'text-green-600'
  if (score >= 90) return 'text-blue-600'
  if (score >= 85) return 'text-orange-600'
  return 'text-red-600'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.checklist-item {
  transition: all 0.2s;
}

.checklist-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
}

.compliance-score {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.supplier-item {
  transition: transform 0.2s;
}

.supplier-item:hover {
  transform: translateX(4px);
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filters .modern-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style>