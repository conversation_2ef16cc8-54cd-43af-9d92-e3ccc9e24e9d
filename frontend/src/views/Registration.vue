<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 flex items-center justify-center p-6 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-10 rounded-full"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white bg-opacity-5 rounded-full"></div>
      <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white bg-opacity-5 rounded-full"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
    </div>

    <!-- 注册卡片 -->
    <div class="relative w-full max-w-4xl z-10">
      <!-- 返回登录按钮 -->
      <button @click="$router.push('/login')" class="absolute -top-16 left-0 flex items-center space-x-2 text-white hover:text-blue-200 transition-colors">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        <span>返回登录</span>
      </button>

      <div class="bg-white rounded-3xl shadow-2xl p-8">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
            </svg>
          </div>
          <h2 class="title-font text-2xl font-bold text-gray-800 mb-2">用户注册</h2>
          <p class="text-gray-600">中航材共享保障平台</p>
        </div>

        <!-- 注册步骤指示器 -->
        <div class="flex justify-center mb-8">
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold', 
                           currentStep >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-500']">
                1
              </div>
              <span class="ml-2 text-sm text-gray-600">用户类型</span>
            </div>
            <div class="w-8 h-1 bg-gray-200 rounded"></div>
            <div class="flex items-center">
              <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold',
                           currentStep >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-500']">
                2
              </div>
              <span class="ml-2 text-sm text-gray-600">选择角色</span>
            </div>
            <div class="w-8 h-1 bg-gray-200 rounded"></div>
            <div class="flex items-center">
              <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold',
                           currentStep >= 3 ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-500']">
                3
              </div>
              <span class="ml-2 text-sm text-gray-600">基本信息</span>
            </div>
          </div>
        </div>

        <!-- 步骤1: 用户类型选择 -->
        <div v-if="currentStep === 1" class="space-y-6">
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">请选择用户类型</h3>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 外部用户 -->
            <div 
              :class="['border-2 rounded-xl p-6 cursor-pointer transition-all duration-200',
                      registerForm.userCategory === 'external' 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-gray-200 hover:border-gray-300']"
              @click="selectUserCategory('external')"
            >
              <div class="flex items-center justify-center mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </div>
              </div>
              <h4 class="font-semibold text-gray-800 text-center mb-2">外部用户</h4>
              <p class="text-sm text-gray-600 text-center">供应商、分销商、航空公司等外部合作伙伴</p>
              <div class="mt-4 flex flex-wrap gap-1 justify-center">
                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">供应商</span>
                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">分销商</span>
                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">航空公司</span>
              </div>
            </div>

            <!-- 内部员工 -->
            <div 
              :class="['border-2 rounded-xl p-6 cursor-pointer transition-all duration-200',
                      registerForm.userCategory === 'internal' 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-gray-200 hover:border-gray-300']"
              @click="selectUserCategory('internal')"
            >
              <div class="flex items-center justify-center mb-4">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.98 1.98 0 0 0 18.07 7H16V6c0-.55-.45-1-1-1s-1 .45-1 1v1.5c0 .28.22.5.5.5h1.93l2.04 6.12V22h2z"/>
                  </svg>
                </div>
              </div>
              <h4 class="font-semibold text-gray-800 text-center mb-2">内部员工</h4>
              <p class="text-sm text-gray-600 text-center">平台内部工作人员，需要邀请码</p>
              <div class="mt-4 flex flex-wrap gap-1 justify-center">
                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">平台员工</span>
                <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">维修工程师</span>
                <span class="px-2 py-1 bg-cyan-100 text-cyan-800 text-xs rounded-full">物流专员</span>
                <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">管理员</span>
              </div>
            </div>
          </div>

          <!-- 邀请码输入（仅内部员工） -->
          <div v-if="registerForm.userCategory === 'internal'" class="mt-6">
            <el-form-item label="邀请码" prop="invitationCode" class="mb-4">
              <el-input
                v-model="registerForm.invitationCode"
                placeholder="请输入邀请码 (格式: STAFF-YYYY-XXXX-XXXX)"
                :prefix-icon="Ticket"
                clearable
                @blur="validateInvitationCode"
                @input="onInvitationCodeInput"
              >
                <template #suffix>
                  <div v-if="codeValidationStatus === 'validating'" class="flex items-center">
                    <el-icon class="is-loading text-gray-400">
                      <Loading />
                    </el-icon>
                  </div>
                  <div v-else-if="codeValidationStatus === 'valid'" class="flex items-center">
                    <el-icon class="text-green-500">
                      <Check />
                    </el-icon>
                  </div>
                  <div v-else-if="codeValidationStatus === 'invalid'" class="flex items-center">
                    <el-icon class="text-red-500">
                      <Close />
                    </el-icon>
                  </div>
                </template>
              </el-input>
              <div v-if="invitationInfo && codeValidationStatus === 'valid'" class="mt-2 p-3 bg-green-50 rounded-lg">
                <p class="text-sm text-green-800">
                  ✓ 邀请码有效，可注册角色：
                  <span v-for="role in invitationInfo.allowed_roles" :key="role" class="ml-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    {{ getRoleDisplayName(role) }}
                  </span>
                </p>
                <p class="text-xs text-green-600 mt-1">过期时间：{{ formatDateTime(invitationInfo.expires_at) }}</p>
              </div>
              <div v-if="codeValidationError" class="mt-2 p-3 bg-red-50 rounded-lg">
                <p class="text-sm text-red-800">{{ codeValidationError }}</p>
              </div>
            </el-form-item>
          </div>

          <!-- 下一步按钮 -->
          <div class="flex justify-end">
            <el-button 
              type="primary" 
              size="large"
              :disabled="!canGoToNextStep"
              @click="goToStep(2)"
            >
              下一步
            </el-button>
          </div>
        </div>

        <!-- 步骤2: 角色选择 -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择您的角色</h3>
            <p class="text-sm text-gray-600">根据您的业务类型选择合适的角色</p>
          </div>

          <RoleSelection 
            :roles="availableRoles"
            :selected-role="registerForm.selectedRole"
            @role-selected="onRoleSelected"
          />

          <!-- 按钮组 -->
          <div class="flex justify-between">
            <el-button size="large" @click="goToStep(1)">
              上一步
            </el-button>
            <el-button 
              type="primary" 
              size="large"
              :disabled="!registerForm.selectedRole"
              @click="goToStep(3)"
            >
              下一步
            </el-button>
          </div>
        </div>

        <!-- 步骤3: 基本信息 -->
        <div v-if="currentStep === 3" class="space-y-6">
          <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">填写基本信息</h3>
          </div>

          <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" label-width="80px">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 用户名 -->
              <el-form-item label="用户名" prop="username" required>
                <el-input
                  v-model="registerForm.username"
                  placeholder="请输入用户名"
                  :prefix-icon="User"
                  clearable
                />
              </el-form-item>

              <!-- 邮箱 -->
              <el-form-item label="邮箱" prop="email" required>
                <el-input
                  v-model="registerForm.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  :prefix-icon="Message"
                  clearable
                />
              </el-form-item>

              <!-- 公司名称 -->
              <el-form-item label="公司名称" prop="companyName" required>
                <el-input
                  v-model="registerForm.companyName"
                  placeholder="请输入公司名称"
                  :prefix-icon="OfficeBuilding"
                  clearable
                />
              </el-form-item>

              <!-- 联系电话 -->
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="registerForm.phone"
                  placeholder="请输入联系电话"
                  :prefix-icon="Phone"
                  clearable
                />
              </el-form-item>

              <!-- 密码 -->
              <el-form-item label="密码" prop="password" required>
                <el-input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="请输入密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                />
              </el-form-item>

              <!-- 确认密码 -->
              <el-form-item label="确认密码" prop="confirmPassword" required>
                <el-input
                  v-model="registerForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                />
              </el-form-item>
            </div>

            <!-- 服务条款 -->
            <el-form-item prop="agreeTerms" class="mt-6">
              <el-checkbox v-model="registerForm.agreeTerms">
                我已阅读并同意
                <a href="#" class="text-primary-600 hover:text-primary-800">《服务条款》</a>
                和
                <a href="#" class="text-primary-600 hover:text-primary-800">《隐私政策》</a>
              </el-checkbox>
            </el-form-item>
          </el-form>

          <!-- 按钮组 -->
          <div class="flex justify-between">
            <el-button size="large" @click="goToStep(2)">
              上一步
            </el-button>
            <el-button 
              type="primary" 
              size="large"
              :loading="registering"
              @click="handleRegister"
            >
              {{ registering ? '注册中...' : '完成注册' }}
            </el-button>
          </div>
        </div>

        <!-- 登录链接 -->
        <div class="text-center mt-6">
          <p class="text-sm text-gray-600">
            已有账户？
            <router-link to="/login" class="text-primary-600 hover:text-primary-800 font-semibold">立即登录</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { invitationsApi } from '@/api/invitations'
import { authApi } from '@/api/auth'
import roleConfigService from '@/services/RoleConfigService'
import RoleSelection from '@/components/RoleSelection.vue'

const router = useRouter()

// 响应式数据
const currentStep = ref(1)
const registering = ref(false)
const codeValidationStatus = ref(null) // null, 'validating', 'valid', 'invalid'
const codeValidationError = ref('')
const invitationInfo = ref(null)
const availableRoles = ref([])
const registerFormRef = ref(null)

// 表单数据
const registerForm = reactive({
  userCategory: '', // 'external' 或 'internal'
  invitationCode: '',
  selectedRole: null,
  username: '',
  email: '',
  companyName: '',
  phone: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  agreeTerms: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请先同意服务条款和隐私政策'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const canGoToNextStep = computed(() => {
  if (registerForm.userCategory === 'external') {
    return true
  }
  if (registerForm.userCategory === 'internal') {
    return codeValidationStatus.value === 'valid'
  }
  return false
})

// 方法
const selectUserCategory = (category) => {
  registerForm.userCategory = category
  // 重置相关状态
  registerForm.selectedRole = null
  if (category === 'external') {
    registerForm.invitationCode = ''
    codeValidationStatus.value = null
    codeValidationError.value = ''
    invitationInfo.value = null
  }
}

const onInvitationCodeInput = () => {
  // 重置验证状态
  codeValidationStatus.value = null
  codeValidationError.value = ''
  invitationInfo.value = null
}

const validateInvitationCode = async () => {
  if (!registerForm.invitationCode.trim()) {
    return
  }

  codeValidationStatus.value = 'validating'
  
  try {
    const response = await invitationsApi.validateInvitationCode(registerForm.invitationCode)
    
    if (response.success && response.body.valid) {
      codeValidationStatus.value = 'valid'
      invitationInfo.value = response.body
    } else {
      codeValidationStatus.value = 'invalid'
      codeValidationError.value = response.message || '邀请码无效'
    }
  } catch (error) {
    codeValidationStatus.value = 'invalid'
    codeValidationError.value = '验证邀请码时发生错误'
    console.error('邀请码验证失败:', error)
  }
}

const goToStep = async (step) => {
  if (step === 2) {
    // 加载可用角色
    await loadAvailableRoles()
  }
  currentStep.value = step
}

const loadAvailableRoles = async () => {
  try {
    let roles = []
    
    if (registerForm.userCategory === 'external') {
      // 外部用户：加载所有外部角色，使用公开API
      const externalRoles = await roleConfigService.getExternalRoles(true)
      roles = Object.entries(externalRoles).map(([code, config]) => ({
        code,
        ...config
      }))
    } else if (registerForm.userCategory === 'internal' && invitationInfo.value) {
      // 内部用户：根据邀请码允许的角色加载，使用公开API
      const allConfigs = await roleConfigService.getAllRoleConfigs(true)
      roles = invitationInfo.value.allowed_roles.map(roleCode => ({
        code: roleCode,
        ...allConfigs[roleCode]
      })).filter(role => role.name) // 过滤掉不存在的角色
    }
    
    availableRoles.value = roles
  } catch (error) {
    console.error('加载角色失败:', error)
    ElMessage.error('加载角色列表失败')
  }
}

const onRoleSelected = (role) => {
  registerForm.selectedRole = role
}

const handleRegister = async () => {
  // 验证表单
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
  } catch (error) {
    return
  }

  registering.value = true

  try {
    const registrationData = {
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      company_name: registerForm.companyName,
      phone: registerForm.phone,
      role_code: registerForm.selectedRole.code
    }

    let response
    if (registerForm.userCategory === 'internal') {
      // 内部用户使用邀请码注册
      registrationData.invitation_code = registerForm.invitationCode
      response = await invitationsApi.registerWithInvitation(registrationData)
    } else {
      // 外部用户普通注册
      response = await authApi.register(registrationData)
    }

    if (response.success) {
      ElMessage.success('注册成功！请使用新账户登录')
      router.push('/login')
    } else {
      ElMessage.error(response.message || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请稍后重试')
  } finally {
    registering.value = false
  }
}

const getRoleDisplayName = (roleCode) => {
  const roleMap = {
    platform_staff: '平台员工',
    maintenance_engineer: '维修工程师',
    logistics_specialist: '物流专员',
    admin: '系统管理员'
  }
  return roleMap[roleCode] || roleCode
}

const formatDateTime = (dateString) => {
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch (error) {
    return dateString
  }
}
</script>

<style scoped>
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(255,255,255,.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.title-font {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 角色卡片悬停效果 */
.role-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 步骤指示器动画 */
.step-indicator {
  transition: all 0.3s ease;
}

/* 表单动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>