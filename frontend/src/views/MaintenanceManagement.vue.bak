<template>
  <div class="maintenance-management">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-800 flex items-center">
          <Wrench class="w-8 h-8 text-blue-500 mr-3" />
          维修管理
        </h1>
        <p class="text-gray-600 mt-2">维修工单管理、进度跟踪与资源调度</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="showCreateWorkOrderDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          创建工单
        </el-button>
        <el-button type="warning" @click="showScheduleDialog = true">
          <Calendar class="w-4 h-4 mr-2" />
          维修计划
        </el-button>
        <el-button @click="generateMaintenanceReport">
          <Document class="w-4 h-4 mr-2" />
          维修报告
        </el-button>
        <el-button @click="refreshData">
          <Refresh class="w-4 h-4 mr-2" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 维修概览 -->
    <div class="maintenance-overview mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="modern-card p-6 text-center border-l-4 border-blue-500">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Document class="w-6 h-6 text-blue-600" />
          </div>
          <div class="text-2xl font-bold text-blue-600">{{ statistics.totalWorkOrders }}</div>
          <div class="text-gray-600">总工单数</div>
          <div class="text-xs text-blue-500 mt-1">本月新增 {{ statistics.newThisMonth }}</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-orange-500">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Clock class="w-6 h-6 text-orange-600" />
          </div>
          <div class="text-2xl font-bold text-orange-600">{{ statistics.inProgress }}</div>
          <div class="text-gray-600">进行中</div>
          <div class="text-xs text-orange-500 mt-1">需要关注</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-green-500">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <CheckCircle class="w-6 h-6 text-green-600" />
          </div>
          <div class="text-2xl font-bold text-green-600">{{ statistics.completionRate }}%</div>
          <div class="text-gray-600">完成率</div>
          <div class="text-xs text-green-500 mt-1">本月统计</div>
        </div>
        
        <div class="modern-card p-6 text-center border-l-4 border-purple-500">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <Users class="w-6 h-6 text-purple-600" />
          </div>
          <div class="text-2xl font-bold text-purple-600">{{ statistics.availableTechnicians }}</div>
          <div class="text-gray-600">可用技师</div>
          <div class="text-xs text-purple-500 mt-1">当前班次</div>
        </div>
      </div>
    </div>

    <!-- 快速状态面板 -->
    <div class="status-panel mb-8">
      <div class="modern-card">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold flex items-center">
            <Monitor class="w-5 h-5 mr-2 text-blue-600" />
            实时状态监控
          </h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 紧急工单 -->
            <div class="urgent-orders">
              <h4 class="font-medium mb-3 text-red-600">紧急工单</h4>
              <div class="space-y-2">
                <div v-for="(order, index) in urgentOrders" :key="index" class="urgent-item p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div class="flex justify-between items-center">
                    <div>
                      <div class="font-medium text-red-800">{{ order.workOrderNumber }}</div>
                      <div class="text-sm text-red-600">{{ order.aircraftTail }}</div>
                    </div>
                    <el-button type="danger" size="small" @click="handleUrgentOrder(order.id)">
                      处理
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 技师工作负载 -->
            <div class="technician-workload">
              <h4 class="font-medium mb-3 text-blue-600">技师工作负载</h4>
              <div class="space-y-3">
                <div v-for="(tech, index) in technicianWorkload" :key="index" class="workload-item">
                  <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium">{{ tech.name }}</span>
                    <span class="text-sm text-gray-600">{{ tech.workload }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="h-2 rounded-full transition-all duration-300" 
                      :class="getWorkloadColor(tech.workload)"
                      :style="{ width: tech.workload + '%' }"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 今日计划 -->
            <div class="daily-schedule">
              <h4 class="font-medium mb-3 text-green-600">今日维修计划</h4>
              <div class="space-y-2">
                <div v-for="(schedule, index) in dailySchedule" :key="index" class="schedule-item p-2 bg-green-50 border border-green-200 rounded">
                  <div class="text-sm font-medium">{{ schedule.time }}</div>
                  <div class="text-xs text-green-600">{{ schedule.task }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 维修标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 工单管理 -->
      <el-tab-pane label="工单管理" name="workorders">
        <div class="work-orders-section">
          <!-- 筛选器 -->
          <div class="filters mb-6">
            <div class="modern-card p-4">
              <div class="flex flex-wrap gap-4 items-center">
                <el-select v-model="workOrderFilters.status" placeholder="工单状态" style="width: 150px" size="small">
                  <el-option label="全部状态" value="" />
                  <el-option label="新建" value="new" />
                  <el-option label="进行中" value="in_progress" />
                  <el-option label="等待零件" value="waiting_parts" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
                
                <el-select v-model="workOrderFilters.priority" placeholder="优先级" style="width: 120px" size="small">
                  <el-option label="全部优先级" value="" />
                  <el-option label="AOG" value="aog" />
                  <el-option label="高" value="high" />
                  <el-option label="普通" value="normal" />
                  <el-option label="低" value="low" />
                </el-select>
                
                <el-select v-model="workOrderFilters.aircraftType" placeholder="机型" style="width: 150px" size="small">
                  <el-option label="全部机型" value="" />
                  <el-option label="A320" value="A320" />
                  <el-option label="A330" value="A330" />
                  <el-option label="B737" value="B737" />
                  <el-option label="B777" value="B777" />
                </el-select>
                
                <el-input
                  v-model="workOrderFilters.search"
                  placeholder="搜索工单号、尾号..."
                  style="width: 200px"
                  size="small"
                  clearable
                >
                  <template #prefix>
                    <Search class="w-4 h-4 text-gray-400" />
                  </template>
                </el-input>
                
                <el-button @click="applyWorkOrderFilters" size="small">筛选</el-button>
                <el-button @click="resetWorkOrderFilters" size="small">重置</el-button>
              </div>
            </div>
          </div>

          <!-- 工单列表 -->
          <div class="modern-card">
            <el-table :data="workOrders" v-loading="loading" style="width: 100%">
              <el-table-column width="60">
                <template #default="{ row }">
                  <div class="priority-indicator">
                    <div 
                      :class="getPriorityIndicatorClass(row.priority)"
                      class="w-3 h-3 rounded-full"
                    />
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="work_order_number" label="工单号" width="140">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewWorkOrderDetail(row.id)">
                    {{ row.work_order_number }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column label="飞机信息" min-width="160">
                <template #default="{ row }">
                  <div>
                    <div class="font-medium">{{ row.aircraft.tail_number }}</div>
                    <div class="text-sm text-gray-600">{{ row.aircraft.aircraft_type }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="故障描述" min-width="200">
                <template #default="{ row }">
                  <div class="fault-description">
                    <div class="text-sm font-medium">{{ row.fault_title }}</div>
                    <div class="text-xs text-gray-600 mt-1 line-clamp-2">
                      {{ row.fault_description }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="priority" label="优先级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getPriorityColor(row.priority)" size="small">
                    {{ getPriorityText(row.priority) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="status" label="状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusColor(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="技师" width="120">
                <template #default="{ row }">
                  <div v-if="row.assigned_technician">
                    <div class="text-sm font-medium">{{ row.assigned_technician.name }}</div>
                    <div class="text-xs text-gray-600">{{ row.assigned_technician.specialty }}</div>
                  </div>
                  <div v-else class="text-gray-400 text-sm">未分配</div>
                </template>
              </el-table-column>

              <el-table-column label="进度" width="100">
                <template #default="{ row }">
                  <div class="progress-info">
                    <el-progress 
                      :percentage="row.progress" 
                      :stroke-width="6"
                      :color="getProgressColor(row.progress)"
                    />
                    <div class="text-xs text-center mt-1">{{ row.progress }}%</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="预计完成" width="120">
                <template #default="{ row }">
                  <div class="text-sm" :class="getEstimatedCompletionClass(row.estimated_completion)">
                    {{ formatDate(row.estimated_completion) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <div class="flex space-x-2">
                    <el-button 
                      v-if="row.status === 'new'"
                      link 
                      type="primary" 
                      size="small" 
                      @click="assignTechnician(row.id)"
                    >
                      分配技师
                    </el-button>
                    <el-button 
                      link 
                      type="primary" 
                      size="small" 
                      @click="viewWorkOrderDetail(row.id)"
                    >
                      详情
                    </el-button>
                    <el-dropdown>
                      <el-button link size="small">
                        更多
                        <ArrowDown class="w-3 h-3 ml-1" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="updateWorkOrderStatus(row.id)">
                            更新状态
                          </el-dropdown-item>
                          <el-dropdown-item @click="recordLaborTime(row.id)">
                            记录工时
                          </el-dropdown-item>
                          <el-dropdown-item @click="requestParts(row.id)">
                            申请零件
                          </el-dropdown-item>
                          <el-dropdown-item @click="completeWorkOrder(row.id)">
                            完成工单
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>

      <!-- 技师管理 -->
      <el-tab-pane label="技师管理" name="technicians">
        <div class="technicians-section">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 技师列表 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">技师列表</h3>
              </div>
              <div class="p-6">
                <div class="space-y-4">
                  <div v-for="(technician, index) in technicians" :key="index" class="technician-item">
                    <div class="flex items-center justify-between p-4 border rounded-lg">
                      <div class="flex items-center">
                        <el-avatar :src="technician.avatar" size="medium" class="mr-4" />
                        <div>
                          <div class="font-medium">{{ technician.name }}</div>
                          <div class="text-sm text-gray-600">{{ technician.specialty }}</div>
                          <div class="text-xs text-gray-500">经验: {{ technician.experience }}年</div>
                        </div>
                      </div>
                      <div class="text-right">
                        <el-tag :type="technician.available ? 'success' : 'danger'" size="small">
                          {{ technician.available ? '可用' : '忙碌' }}
                        </el-tag>
                        <div class="text-xs text-gray-500 mt-1">
                          当前工单: {{ technician.current_orders }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 技能矩阵 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">技能矩阵</h3>
              </div>
              <div class="p-6">
                <div class="skills-matrix">
                  <div class="grid grid-cols-3 gap-4">
                    <div v-for="(skill, index) in skillCategories" :key="index" class="skill-category">
                      <div class="text-center p-4 border rounded-lg">
                        <div class="text-lg font-bold text-blue-600">{{ skill.count }}</div>
                        <div class="text-sm text-gray-600">{{ skill.name }}</div>
                        <div class="text-xs text-gray-500 mt-1">认证技师</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 维修计划 -->
      <el-tab-pane label="维修计划" name="schedule">
        <div class="schedule-section">
          <div class="modern-card">
            <div class="p-6 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">维修排程</h3>
                <el-button type="primary" size="small" @click="showScheduleDialog = true">
                  <Plus class="w-4 h-4 mr-2" />
                  新建计划
                </el-button>
              </div>
            </div>
            <div class="p-6">
              <div class="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-gray-500 text-center">
                  <Calendar class="w-8 h-8 mx-auto mb-2" />
                  <div>维修计划日历视图</div>
                  <div class="text-sm mt-2">显示未来7天的维修安排</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 统计分析 -->
      <el-tab-pane label="统计分析" name="analytics">
        <div class="analytics-section">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 维修效率 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">维修效率分析</h3>
              </div>
              <div class="p-6">
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div class="text-gray-500 text-center">
                    <TrendingUp class="w-8 h-8 mx-auto mb-2" />
                    <div>维修效率趋势图</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 成本分析 -->
            <div class="modern-card">
              <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold">维修成本分析</h3>
              </div>
              <div class="p-6">
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                  <div class="text-gray-500 text-center">
                    <PieChart class="w-8 h-8 mx-auto mb-2" />
                    <div>成本构成图表</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建工单弹窗 -->
    <CreateWorkOrderDialog 
      v-model="showCreateWorkOrderDialog" 
      @success="handleWorkOrderCreated"
    />
    
    <!-- 工单详情弹窗 -->
    <WorkOrderDetailDialog 
      v-model="showWorkOrderDetailDialog" 
      :work-order-id="selectedWorkOrderId"
    />
    
    <!-- 维修计划弹窗 -->
    <ScheduleMaintenanceDialog 
      v-model="showScheduleDialog" 
      @success="handleScheduleCreated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Wrench, Plus, Calendar, Document, Refresh, Clock, CheckCircle,
  User, Connection, Search, ArrowDown, DataLine, PieChart
} from '@element-plus/icons-vue'
import { maintenanceApi } from '@/api/maintenance'
import CreateWorkOrderDialog from '@/components/CreateWorkOrderDialog.vue'
import WorkOrderDetailDialog from '@/components/WorkOrderDetailDialog.vue'
import ScheduleMaintenanceDialog from '@/components/ScheduleMaintenanceDialog.vue'

// 状态
const loading = ref(false)
const showCreateWorkOrderDialog = ref(false)
const showWorkOrderDetailDialog = ref(false)
const showScheduleDialog = ref(false)
const selectedWorkOrderId = ref(null)
const activeTab = ref('workorders')

// 统计数据
const statistics = ref({
  totalWorkOrders: 456,
  newThisMonth: 89,
  inProgress: 23,
  completionRate: 94.5,
  availableTechnicians: 12
})

// 筛选器
const workOrderFilters = reactive({
  status: '',
  priority: '',
  aircraftType: '',
  search: ''
})

// 紧急工单
const urgentOrders = ref([
  { id: 'WO-001', workOrderNumber: 'MX-2025-001', aircraftTail: 'B-1234' },
  { id: 'WO-002', workOrderNumber: 'MX-2025-002', aircraftTail: 'B-5678' }
])

// 技师工作负载
const technicianWorkload = ref([
  { name: '张工程师', workload: 85 },
  { name: '李技师', workload: 70 },
  { name: '王师傅', workload: 95 },
  { name: '赵工', workload: 60 }
])

// 今日计划
const dailySchedule = ref([
  { time: '08:00', task: 'B-1234 发动机检查' },
  { time: '10:00', task: 'B-5678 起落架维修' },
  { time: '14:00', task: 'B-9999 定检项目' },
  { time: '16:00', task: 'B-1111 航电维修' }
])

// 工单列表
const workOrders = ref([])

// 技师列表
const technicians = ref([
  {
    name: '张工程师',
    specialty: '发动机维修',
    experience: 15,
    available: true,
    current_orders: 2,
    avatar: '/avatars/tech1.jpg'
  },
  {
    name: '李技师',
    specialty: '航电维修',
    experience: 12,
    available: false,
    current_orders: 3,
    avatar: '/avatars/tech2.jpg'
  },
  {
    name: '王师傅',
    specialty: '结构维修',
    experience: 18,
    available: true,
    current_orders: 1,
    avatar: '/avatars/tech3.jpg'
  }
])

// 技能分类
const skillCategories = ref([
  { name: '发动机', count: 8 },
  { name: '航电', count: 6 },
  { name: '结构', count: 5 },
  { name: '液压', count: 4 },
  { name: '起落架', count: 3 },
  { name: '客舱', count: 2 }
])

// 模拟数据
const initializeMockData = () => {
  workOrders.value = [
    {
      id: 'WO-001',
      work_order_number: 'MX-2025-001',
      aircraft: {
        tail_number: 'B-1234',
        aircraft_type: 'A320-200'
      },
      fault_title: '左发动机振动异常',
      fault_description: '巡航阶段左发动机出现异常振动，需要检查发动机叶片和轴承组件',
      priority: 'aog',
      status: 'in_progress',
      assigned_technician: {
        name: '张工程师',
        specialty: '发动机维修'
      },
      progress: 65,
      estimated_completion: '2025-01-14',
      created_at: '2025-01-13 08:00:00'
    },
    {
      id: 'WO-002',
      work_order_number: 'MX-2025-002',
      aircraft: {
        tail_number: 'B-5678',
        aircraft_type: 'B737-800'
      },
      fault_title: '主起落架收放异常',
      fault_description: '主起落架收放过程中动作缓慢，液压系统压力正常',
      priority: 'high',
      status: 'waiting_parts',
      assigned_technician: {
        name: '李技师',
        specialty: '起落架维修'
      },
      progress: 30,
      estimated_completion: '2025-01-15',
      created_at: '2025-01-12 14:30:00'
    },
    {
      id: 'WO-003',
      work_order_number: 'MX-2025-003',
      aircraft: {
        tail_number: 'B-9999',
        aircraft_type: 'A330-300'
      },
      fault_title: '航电系统故障',
      fault_description: '主显示器间歇性黑屏，疑似显示单元故障',
      priority: 'normal',
      status: 'new',
      assigned_technician: null,
      progress: 0,
      estimated_completion: '2025-01-16',
      created_at: '2025-01-13 16:20:00'
    }
  ]
}

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    // await loadMaintenanceData()
    initializeMockData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleTabClick = (tab) => {
  console.log('切换标签页:', tab.name)
}

const applyWorkOrderFilters = () => {
  console.log('应用工单筛选:', workOrderFilters)
}

const resetWorkOrderFilters = () => {
  Object.keys(workOrderFilters).forEach(key => {
    workOrderFilters[key] = ''
  })
}

const handleUrgentOrder = (orderId) => {
  ElMessage.info(`处理紧急工单: ${orderId}`)
}

const viewWorkOrderDetail = (workOrderId) => {
  selectedWorkOrderId.value = workOrderId
  showWorkOrderDetailDialog.value = true
}

const assignTechnician = (workOrderId) => {
  ElMessage.info(`分配技师: ${workOrderId}`)
}

const updateWorkOrderStatus = (workOrderId) => {
  ElMessage.info(`更新工单状态: ${workOrderId}`)
}

const recordLaborTime = (workOrderId) => {
  ElMessage.info(`记录工时: ${workOrderId}`)
}

const requestParts = (workOrderId) => {
  ElMessage.info(`申请零件: ${workOrderId}`)
}

const completeWorkOrder = (workOrderId) => {
  ElMessage.info(`完成工单: ${workOrderId}`)
}

const generateMaintenanceReport = async () => {
  try {
    ElMessage.success('维修报告生成中...')
  } catch (error) {
    ElMessage.error('报告生成失败')
  }
}

const handleWorkOrderCreated = () => {
  ElMessage.success('工单创建成功')
  refreshData()
}

const handleScheduleCreated = () => {
  ElMessage.success('维修计划创建成功')
  refreshData()
}

// 辅助函数
const getPriorityIndicatorClass = (priority) => {
  const classes = {
    aog: 'bg-red-500 animate-pulse',
    high: 'bg-orange-500',
    normal: 'bg-blue-500',
    low: 'bg-gray-400'
  }
  return classes[priority] || 'bg-gray-400'
}

const getPriorityColor = (priority) => {
  const colors = {
    aog: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    aog: 'AOG',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getStatusColor = (status) => {
  const colors = {
    new: 'info',
    in_progress: 'primary',
    waiting_parts: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    new: '新建',
    in_progress: '进行中',
    waiting_parts: '等待零件',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#409eff'
  if (progress >= 20) return '#e6a23c'
  return '#f56c6c'
}

const getWorkloadColor = (workload) => {
  if (workload >= 90) return 'bg-red-500'
  if (workload >= 70) return 'bg-orange-500'
  return 'bg-green-500'
}

const getEstimatedCompletionClass = (date) => {
  const today = new Date()
  const completion = new Date(date)
  const diffDays = Math.ceil((completion - today) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'text-red-500 font-bold'
  if (diffDays <= 1) return 'text-orange-500 font-bold'
  return 'text-gray-700'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(() => {
  initializeMockData()
})
</script>

<style scoped>
.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.priority-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fault-description {
  max-width: 200px;
}

.urgent-item {
  transition: all 0.2s;
}

.urgent-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.workload-item {
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 6px;
}

.schedule-item {
  transition: all 0.2s;
}

.schedule-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.technician-item {
  transition: all 0.2s;
}

.technician-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.progress-info {
  width: 80px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filters .modern-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style>