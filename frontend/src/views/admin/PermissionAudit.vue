<!--
权限审计日志页面
版本: 1.0
创建时间: 2025-07-23

权限变更历史追踪和审计的专门页面
特点：
1. 完整的权限变更历史记录
2. 多维度筛选和搜索
3. 权限变更对比展示
4. 操作时间线可视化
5. 异常权限变更告警
-->

<template>
  <div class="permission-audit">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">权限审计日志</h1>
          <p class="page-subtitle">追踪和审计所有权限变更操作，确保系统安全</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshLogs">
            <Refresh class="w-4 h-4 mr-2" />
            刷新日志
          </el-button>
          <el-button @click="handleExportLogs">
            <Download class="w-4 h-4 mr-2" />
            导出日志
          </el-button>
          <el-button type="primary" @click="handleGenerateReport">
            <Document class="w-4 h-4 mr-2" />
            生成报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 审计统计概览 -->
    <div class="audit-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <Document class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ auditStats.totalRecords }}</div>
            <div class="stat-label">总审计记录</div>
            <div class="stat-change positive">+{{ auditStats.todayRecords }} 今日新增</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-green-100 text-green-600">
            <CircleCheck class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ auditStats.successfulChanges }}</div>
            <div class="stat-label">成功变更</div>
            <div class="stat-change neutral">{{ auditStats.successRate }}% 成功率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-orange-100 text-orange-600">
            <Warning class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ auditStats.suspiciousActivities }}</div>
            <div class="stat-label">异常活动</div>
            <div class="stat-change warning">需要关注</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-purple-100 text-purple-600">
            <Avatar class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ auditStats.activeAdmins }}</div>
            <div class="stat-label">活跃管理员</div>
            <div class="stat-change info">本周操作</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="filter-section">
      <div class="filters-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">时间范围</label>
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              :shortcuts="timeRangeShortcuts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleTimeRangeChange"
            />
          </div>
          
          <div class="filter-group">
            <label class="filter-label">操作类型</label>
            <el-select v-model="selectedActionType" placeholder="选择操作类型" clearable @change="handleFilter">
              <el-option label="全部操作" value="" />
              <el-option label="权限授予" value="grant_permission" />
              <el-option label="权限撤销" value="revoke_permission" />
              <el-option label="角色变更" value="role_change" />
              <el-option label="用户创建" value="user_create" />
              <el-option label="用户禁用" value="user_disable" />
              <el-option label="批量操作" value="batch_operation" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">操作结果</label>
            <el-select v-model="selectedResult" placeholder="操作结果" clearable @change="handleFilter">
              <el-option label="全部结果" value="" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="部分成功" value="partial" />
            </el-select>
          </div>
        </div>
        
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">目标用户</label>
            <el-input
              v-model="targetAvatarFilter"
              placeholder="输入用户名搜索"
              clearable
              @input="handleFilter"
            >
              <template #prefix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">操作管理员</label>
            <el-select v-model="selectedAdmin" placeholder="选择管理员" clearable @change="handleFilter">
              <el-option label="全部管理员" value="" />
              <el-option 
                v-for="admin in adminList" 
                :key="admin.value"
                :label="admin.label"
                :value="admin.value"
              />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">高级筛选</label>
            <div class="advanced-filters">
              <el-checkbox v-model="showSuspiciousOnly" @change="handleFilter">仅显示异常活动</el-checkbox>
              <el-checkbox v-model="showFailedOnly" @change="handleFilter">仅显示失败操作</el-checkbox>
            </div>
          </div>
        </div>
        
        <div class="filter-actions">
          <el-button @click="handleResetFilters">重置筛选</el-button>
          <el-button @click="handleSaveFilter">保存筛选条件</el-button>
          <el-button type="primary" @click="handleApplyFilters">应用筛选</el-button>
        </div>
      </div>
    </div>

    <!-- 审计日志列表 -->
    <div class="audit-logs-section">
      <div class="logs-header">
        <div class="logs-title">
          <h3 class="text-lg font-semibold text-gray-800">审计记录列表</h3>
          <div class="logs-count">
            <el-tag>{{ filteredLogs.length }} 条记录</el-tag>
          </div>
        </div>
        
        <div class="view-options">
          <el-button-group>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''" 
              @click="setViewMode('table')"
            >
              <Grid class="w-4 h-4 mr-2" />
              表格视图
            </el-button>
            <el-button 
              :type="viewMode === 'timeline' ? 'primary' : ''" 
              @click="setViewMode('timeline')"
            >
              <Clock class="w-4 h-4 mr-2" />
              时间线视图
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-view">
        <el-table
          :data="paginatedLogs"
          v-loading="loadingLogs"
          stripe
          class="audit-table"
          @row-click="handleRowClick"
        >
          <el-table-column type="expand" width="30">
            <template #default="props">
              <div class="audit-detail-expansion">
                <div class="detail-sections">
                  <div class="detail-section">
                    <h5 class="detail-title">操作详情</h5>
                    <div class="detail-content">
                      <div class="detail-item">
                        <span class="detail-label">操作描述:</span>
                        <span class="detail-value">{{ props.row.description }}</span>
                      </div>
                      <div class="detail-item" v-if="props.row.ip_address">
                        <span class="detail-label">IP地址:</span>
                        <span class="detail-value">{{ props.row.ip_address }}</span>
                      </div>
                      <div class="detail-item" v-if="props.row.user_agent">
                        <span class="detail-label">用户代理:</span>
                        <span class="detail-value">{{ props.row.user_agent }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="detail-section" v-if="props.row.changes">
                    <h5 class="detail-title">权限变更对比</h5>
                    <div class="changes-comparison">
                      <div class="change-item" v-for="change in props.row.changes" :key="change.field">
                        <div class="change-field">{{ change.field }}</div>
                        <div class="change-values">
                          <div class="old-value">
                            <span class="value-label">原值:</span>
                            <el-tag size="small" type="info">{{ formatValue(change.oldValue) }}</el-tag>
                          </div>
                          <div class="arrow">→</div>
                          <div class="new-value">
                            <span class="value-label">新值:</span>
                            <el-tag size="small" type="success">{{ formatValue(change.newValue) }}</el-tag>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="操作时间" width="180" sortable>
            <template #default="scope">
              <div class="time-info">
                <div class="time-primary">{{ formatDate(scope.row.created_at) }}</div>
                <div class="time-secondary">{{ formatRelativeTime(scope.row.created_at) }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="action_type" label="操作类型" width="120">
            <template #default="scope">
              <el-tag 
                :type="getActionTypeTagType(scope.row.action_type)"
                size="small"
              >
                {{ getActionTypeDisplayName(scope.row.action_type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="admin_name" label="操作管理员" width="150">
            <template #default="scope">
              <div class="admin-info">
                <el-avatar :size="24" class="mr-2">
                  {{ scope.row.admin_name.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="admin-name">{{ scope.row.admin_name }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="target_user" label="目标用户" width="150">
            <template #default="scope">
              <div class="target-user-info" v-if="scope.row.target_user">
                <el-avatar :size="24" class="mr-2">
                  {{ scope.row.target_user.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="user-name">{{ scope.row.target_user }}</span>
              </div>
              <span v-else class="text-gray-400">系统操作</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="summary" label="操作摘要" min-width="200">
            <template #default="scope">
              <div class="operation-summary">
                <div class="summary-text">{{ scope.row.summary }}</div>
                <div class="summary-meta" v-if="scope.row.affected_permissions">
                  <el-tag 
                    v-for="permission in scope.row.affected_permissions.slice(0, 2)" 
                    :key="permission"
                    size="small"
                    class="permission-tag"
                  >
                    {{ getPermissionDisplayName(permission) }}
                  </el-tag>
                  <span v-if="scope.row.affected_permissions.length > 2" class="more-permissions">
                    +{{ scope.row.affected_permissions.length - 2 }} 更多
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="result" label="结果" width="100">
            <template #default="scope">
              <div class="result-indicator">
                <el-tag 
                  :type="getResultTagType(scope.row.result)"
                  size="small"
                >
                  <component :is="getResultIcon(scope.row.result)" class="w-3 h-3 mr-1" />
                  {{ getResultDisplayName(scope.row.result) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="risk_level" label="风险等级" width="100">
            <template #default="scope">
              <el-tag 
                :type="getRiskLevelTagType(scope.row.risk_level)"
                size="small"
                effect="dark"
              >
                {{ getRiskLevelDisplayName(scope.row.risk_level) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button 
                  size="small" 
                  text 
                  type="primary"
                  @click.stop="handleViewDetail(scope.row)"
                >
                  详情
                </el-button>
                <el-dropdown @command="(cmd) => handleLogAction(cmd, scope.row)">
                  <el-button size="small" text>
                    更多
                    <el-icon class="el-icon--right">
                      <arrow-down />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="rollback" v-if="canRollback(scope.row)">
                        回滚操作
                      </el-dropdown-item>
                      <el-dropdown-item command="export">导出记录</el-dropdown-item>
                      <el-dropdown-item command="flag" v-if="scope.row.risk_level === 'low'">
                        标记异常
                      </el-dropdown-item>
                      <el-dropdown-item command="unflag" v-if="scope.row.risk_level === 'high'">
                        取消标记
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="filteredLogs.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-else-if="viewMode === 'timeline'" class="timeline-view">
        <div class="timeline-container">
          <div 
            v-for="(dayLogs, date) in groupedByDate" 
            :key="date"
            class="timeline-day"
          >
            <div class="day-header">
              <h4 class="day-title">{{ formatDateTitle(date) }}</h4>
              <div class="day-stats">
                {{ dayLogs.length }} 条记录
              </div>
            </div>
            
            <div class="timeline-items">
              <div 
                v-for="log in dayLogs" 
                :key="log.id"
                class="timeline-item"
                :class="{ 'timeline-item-suspicious': log.risk_level === 'high' }"
              >
                <div class="timeline-dot" :class="getTimelineDotClass(log.result)"></div>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <div class="timeline-time">{{ formatTime(log.created_at) }}</div>
                    <div class="timeline-action">
                      <el-tag 
                        :type="getActionTypeTagType(log.action_type)"
                        size="small"
                      >
                        {{ getActionTypeDisplayName(log.action_type) }}
                      </el-tag>
                    </div>
                    <div class="timeline-result">
                      <component :is="getResultIcon(log.result)" 
                        :class="[
                          'w-4 h-4',
                          getResultIconClass(log.result)
                        ]" 
                      />
                    </div>
                  </div>
                  
                  <div class="timeline-summary">{{ log.summary }}</div>
                  
                  <div class="timeline-meta">
                    <div class="meta-item">
                      <Avatar class="w-3 h-3 mr-1" />
                      <span>{{ log.admin_name }}</span>
                    </div>
                    <div class="meta-item" v-if="log.target_user">
                      <ArrowRight class="w-3 h-3 mr-1" />
                      <span>{{ log.target_user }}</span>
                    </div>
                    <div class="meta-item" v-if="log.ip_address">
                      <Connection class="w-3 h-3 mr-1" />
                      <span>{{ log.ip_address }}</span>
                    </div>
                  </div>
                  
                  <div class="timeline-actions">
                    <el-button size="small" text @click="handleViewDetail(log)">
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审计记录详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`审计记录详情 - ${selectedLog?.action_type || ''}`"
      width="800px"
      @close="handleCloseDetail"
    >
      <div v-if="selectedLog" class="audit-detail-content">
        <div class="detail-header">
          <div class="detail-basic-info">
            <div class="info-item">
              <label class="info-label">操作时间:</label>
              <span class="info-value">{{ formatDate(selectedLog.created_at) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">操作管理员:</label>
              <span class="info-value">{{ selectedLog.admin_name }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">目标用户:</label>
              <span class="info-value">{{ selectedLog.target_user || '系统操作' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">操作结果:</label>
              <el-tag :type="getResultTagType(selectedLog.result)">
                {{ getResultDisplayName(selectedLog.result) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="detail-body">
          <el-tabs v-model="activeDetailTab">
            <el-tab-pane label="操作详情" name="details">
              <div class="operation-details">
                <div class="detail-section">
                  <h5 class="section-title">操作描述</h5>
                  <p class="section-content">{{ selectedLog.description }}</p>
                </div>
                
                <div class="detail-section" v-if="selectedLog.reason">
                  <h5 class="section-title">操作理由</h5>
                  <p class="section-content">{{ selectedLog.reason }}</p>
                </div>
                
                <div class="detail-section">
                  <h5 class="section-title">技术信息</h5>
                  <div class="tech-info">
                    <div class="tech-item">
                      <span class="tech-label">IP地址:</span>
                      <span class="tech-value">{{ selectedLog.ip_address || '未记录' }}</span>
                    </div>
                    <div class="tech-item">
                      <span class="tech-label">用户代理:</span>
                      <span class="tech-value">{{ selectedLog.user_agent || '未记录' }}</span>
                    </div>
                    <div class="tech-item">
                      <span class="tech-label">会话ID:</span>
                      <span class="tech-value">{{ selectedLog.session_id || '未记录' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="权限变更" name="changes" v-if="selectedLog.changes">
              <div class="permission-changes">
                <div 
                  v-for="change in selectedLog.changes" 
                  :key="change.field"
                  class="change-detail"
                >
                  <div class="change-header">
                    <h6 class="change-field">{{ change.field }}</h6>
                    <el-tag size="small" :type="getChangeTypeTag(change.type)">
                      {{ getChangeTypeDisplayName(change.type) }}
                    </el-tag>
                  </div>
                  <div class="change-comparison">
                    <div class="comparison-side">
                      <div class="side-label">变更前</div>
                      <div class="side-content old-content">
                        {{ formatChangeValue(change.oldValue) }}
                      </div>
                    </div>
                    <div class="comparison-arrow">
                      <ArrowRight class="w-5 h-5 text-gray-400" />
                    </div>
                    <div class="comparison-side">
                      <div class="side-label">变更后</div>
                      <div class="side-content new-content">
                        {{ formatChangeValue(change.newValue) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="风险分析" name="risk" v-if="selectedLog.risk_analysis">
              <div class="risk-analysis">
                <div class="risk-overview">
                  <div class="risk-level">
                    <label class="risk-label">风险等级:</label>
                    <el-tag 
                      :type="getRiskLevelTagType(selectedLog.risk_level)"
                      size="large"
                      effect="dark"
                    >
                      {{ getRiskLevelDisplayName(selectedLog.risk_level) }}
                    </el-tag>
                  </div>
                  <div class="risk-score">
                    <label class="risk-label">风险评分:</label>
                    <span class="score-value">{{ selectedLog.risk_analysis.score }}/100</span>
                  </div>
                </div>
                
                <div class="risk-factors">
                  <h6 class="factors-title">风险因素</h6>
                  <div class="factors-list">
                    <div 
                      v-for="factor in selectedLog.risk_analysis.factors" 
                      :key="factor.type"
                      class="risk-factor"
                    >
                      <div class="factor-name">{{ factor.name }}</div>
                      <div class="factor-description">{{ factor.description }}</div>
                      <div class="factor-score">权重: {{ factor.weight }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            v-if="canRollback(selectedLog)" 
            type="warning" 
            @click="handleRollbackOperation(selectedLog)"
          >
            回滚操作
          </el-button>
          <el-button type="primary" @click="handleExportDetail(selectedLog)">
            导出详情
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, Document, CircleCheck, Warning, Avatar,
  Search, Grid, Clock, ArrowRight, Connection, ArrowDown
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const viewMode = ref('table')
const loadingLogs = ref(false)
const showDetailDialog = ref(false)
const selectedLog = ref(null)
const activeDetailTab = ref('details')

// 筛选条件
const timeRange = ref([])
const selectedActionType = ref('')
const selectedResult = ref('')
const targetAvatarFilter = ref('')
const selectedAdmin = ref('')
const showSuspiciousOnly = ref(false)
const showFailedOnly = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 统计数据
const auditStats = ref({
  totalRecords: 1248,
  todayRecords: 23,
  successfulChanges: 1156,
  successRate: 92.6,
  suspiciousActivities: 8,
  activeAdmins: 5
})

// 管理员列表
const adminList = [
  { label: 'admin', value: 'admin' },
  { label: 'super_admin', value: 'super_admin' },
  { label: 'platform_manager', value: 'platform_manager' }
]

// 时间范围快捷选项
const timeRangeShortcuts = [
  {
    text: '最近1小时',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000)
      return [start, end]
    }
  },
  {
    text: '今天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      return [start, end]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

// 模拟审计日志数据
const auditLogs = ref([
  {
    id: 1,
    created_at: '2025-07-23T15:30:00',
    action_type: 'grant_permission',
    admin_name: 'admin',
    target_user: 'supplier_demo',
    summary: '为用户supplier_demo授予管理库存权限',
    description: '管理员admin为用户supplier_demo授予了manage_inventory权限，理由：业务需要扩展库存管理功能',
    result: 'success',
    risk_level: 'low',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    session_id: 'sess_123456',
    reason: '业务需要扩展库存管理功能',
    affected_permissions: ['manage_inventory'],
    changes: [
      {
        field: 'permissions',
        type: 'add',
        oldValue: ['view_own_data', 'publish_shared'],
        newValue: ['view_own_data', 'publish_shared', 'manage_inventory']
      }
    ]
  },
  {
    id: 2,
    created_at: '2025-07-23T14:15:00',
    action_type: 'role_change',
    admin_name: 'admin',
    target_user: 'distributor_test',
    summary: '将用户distributor_test的角色从supplier变更为distributor',
    description: '管理员admin批准了用户distributor_test的角色转换申请，从supplier变更为distributor',
    result: 'success',
    risk_level: 'medium',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    session_id: 'sess_123457',
    reason: '用户申请角色转换，业务需要',
    affected_permissions: ['publish_demand', 'trade_analysis', 'manage_purchase_orders'],
    changes: [
      {
        field: 'user_type',
        type: 'change',
        oldValue: 'supplier',
        newValue: 'distributor'
      },
      {
        field: 'permissions',
        type: 'add',
        oldValue: ['view_own_data', 'manage_inventory', 'publish_shared'],
        newValue: ['view_own_data', 'manage_inventory', 'publish_shared', 'publish_demand', 'trade_analysis']
      }
    ]
  },
  {
    id: 3,
    created_at: '2025-07-23T13:45:00',
    action_type: 'user_disable',
    admin_name: 'admin',
    target_user: 'suspicious_user',
    summary: '禁用可疑用户账号suspicious_user',
    description: '管理员admin禁用了用户suspicious_user的账号，原因：检测到异常登录行为',
    result: 'success',
    risk_level: 'high',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    session_id: 'sess_123458',
    reason: '检测到异常登录行为，暂时禁用账号',
    affected_permissions: [],
    changes: [
      {
        field: 'is_active',
        type: 'change',
        oldValue: true,
        newValue: false
      }
    ],
    risk_analysis: {
      score: 85,
      factors: [
        {
          type: 'suspicious_login',
          name: '异常登录行为',
          description: '检测到来自异地的登录尝试',
          weight: 40
        },
        {
          type: 'privilege_escalation',
          name: '权限提升尝试',
          description: '尝试访问超出权限范围的功能',
          weight: 30
        },
        {
          type: 'batch_operations',
          name: '批量操作异常',
          description: '短时间内进行大量操作',
          weight: 15
        }
      ]
    }
  },
  {
    id: 4,
    created_at: '2025-07-23T12:20:00',
    action_type: 'batch_operation',
    admin_name: 'admin',
    target_user: null,
    summary: '批量更新了15个用户的权限配置',
    description: '管理员admin执行了批量权限更新操作，涉及15个用户账号',
    result: 'partial',
    risk_level: 'medium',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    session_id: 'sess_123459',
    reason: '统一更新用户权限配置',
    affected_permissions: ['view_own_data', 'manage_inventory', 'publish_shared'],
    changes: [
      {
        field: 'batch_users',
        type: 'batch_update',
        oldValue: '15个用户的旧权限配置',
        newValue: '15个用户的新权限配置'
      }
    ]
  },
  {
    id: 5,
    created_at: '2025-07-23T11:10:00',
    action_type: 'revoke_permission',
    admin_name: 'admin',
    target_user: 'airline_demo',
    summary: '撤销用户airline_demo的系统配置权限',
    description: '管理员admin撤销了用户airline_demo的system_config权限',
    result: 'failed',
    risk_level: 'low',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    session_id: 'sess_123460',
    reason: '用户不再需要该权限',
    affected_permissions: ['system_config'],
    changes: [
      {
        field: 'permissions',
        type: 'remove',
        oldValue: ['view_own_data', 'publish_demand', 'system_config'],
        newValue: ['view_own_data', 'publish_demand']
      }
    ]
  }
])

// 计算属性
const filteredLogs = computed(() => {
  let filtered = [...auditLogs.value]
  
  // 时间范围筛选
  if (timeRange.value && timeRange.value.length === 2) {
    const [start, end] = timeRange.value
    filtered = filtered.filter(log => {
      const logTime = new Date(log.created_at)
      return logTime >= new Date(start) && logTime <= new Date(end)
    })
  }
  
  // 操作类型筛选
  if (selectedActionType.value) {
    filtered = filtered.filter(log => log.action_type === selectedActionType.value)
  }
  
  // 操作结果筛选
  if (selectedResult.value) {
    filtered = filtered.filter(log => log.result === selectedResult.value)
  }
  
  // 目标用户筛选
  if (targetAvatarFilter.value) {
    const keyword = targetAvatarFilter.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.target_user && log.target_user.toLowerCase().includes(keyword)
    )
  }
  
  // 管理员筛选
  if (selectedAdmin.value) {
    filtered = filtered.filter(log => log.admin_name === selectedAdmin.value)
  }
  
  // 仅显示异常活动
  if (showSuspiciousOnly.value) {
    filtered = filtered.filter(log => log.risk_level === 'high')
  }
  
  // 仅显示失败操作
  if (showFailedOnly.value) {
    filtered = filtered.filter(log => log.result === 'failed')
  }
  
  return filtered
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

const groupedByDate = computed(() => {
  const groups = {}
  filteredLogs.value.forEach(log => {
    const date = log.created_at.split('T')[0]
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(log)
  })
  return groups
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const formatRelativeTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}분钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const formatDateTitle = (dateString) => {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }
}

const formatTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

const getActionTypeDisplayName = (actionType) => {
  const actionMap = {
    'grant_permission': '权限授予',
    'revoke_permission': '权限撤销',
    'role_change': '角色变更',
    'user_create': '用户创建',
    'user_disable': '用户禁用',
    'batch_operation': '批量操作'
  }
  return actionMap[actionType] || actionType
}

const getActionTypeTagType = (actionType) => {
  const typeMap = {
    'grant_permission': 'success',
    'revoke_permission': 'warning',
    'role_change': 'primary',
    'user_create': 'info',
    'user_disable': 'danger',
    'batch_operation': 'warning'
  }
  return typeMap[actionType] || ''
}

const getResultDisplayName = (result) => {
  const resultMap = {
    'success': '成功',
    'failed': '失败',
    'partial': '部分成功'
  }
  return resultMap[result] || result
}

const getResultTagType = (result) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'partial': 'warning'
  }
  return typeMap[result] || ''
}

const getResultIcon = (result) => {
  const iconMap = {
    'success': 'CircleCheck',
    'failed': 'CircleClose',
    'partial': 'Warning'
  }
  return iconMap[result] || 'Circle'
}

const getResultIconClass = (result) => {
  const classMap = {
    'success': 'text-green-500',
    'failed': 'text-red-500',
    'partial': 'text-orange-500'
  }
  return classMap[result] || 'text-gray-400'
}

const getRiskLevelDisplayName = (riskLevel) => {
  const levelMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return levelMap[riskLevel] || riskLevel
}

const getRiskLevelTagType = (riskLevel) => {
  const typeMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return typeMap[riskLevel] || ''
}

const getTimelineDotClass = (result) => {
  const classMap = {
    'success': 'timeline-dot-success',
    'failed': 'timeline-dot-failed',
    'partial': 'timeline-dot-warning'
  }
  return classMap[result] || 'timeline-dot-default'
}

const getPermissionDisplayName = (permission) => {
  const permissionMap = {
    'manage_inventory': '管理库存',
    'publish_demand': '发布需求',
    'publish_shared': '发布共享',
    'system_config': '系统配置',
    'user_management': '用户管理',
    'trade_analysis': '贸易分析'
  }
  return permissionMap[permission] || permission
}

const formatValue = (value) => {
  if (Array.isArray(value)) {
    return value.join(', ')
  }
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  return value || '空'
}

const formatChangeValue = (value) => {
  if (Array.isArray(value)) {
    return value.map(v => getPermissionDisplayName(v)).join(', ')
  }
  if (typeof value === 'boolean') {
    return value ? '启用' : '禁用'
  }
  return value || '无'
}

const getChangeTypeDisplayName = (type) => {
  const typeMap = {
    'add': '新增',
    'remove': '移除',
    'change': '变更',
    'batch_update': '批量更新'
  }
  return typeMap[type] || type
}

const getChangeTypeTag = (type) => {
  const tagMap = {
    'add': 'success',
    'remove': 'danger',
    'change': 'warning',
    'batch_update': 'info'
  }
  return tagMap[type] || ''
}

const canRollback = (log) => {
  // 只有某些操作可以回滚，且必须是成功的操作
  const rollbackableActions = ['grant_permission', 'revoke_permission', 'role_change']
  return rollbackableActions.includes(log.action_type) && 
         log.result === 'success' && 
         userStore.user?.permission_level >= 4
}

// 事件处理方法
const setViewMode = (mode) => {
  viewMode.value = mode
}

const handleTimeRangeChange = (range) => {
  console.log('时间范围变更:', range)
  handleFilter()
}

const handleFilter = () => {
  currentPage.value = 1
  console.log('应用筛选条件')
}

const handleResetFilters = () => {
  timeRange.value = []
  selectedActionType.value = ''
  selectedResult.value = ''
  targetAvatarFilter.value = ''
  selectedAdmin.value = ''
  showSuspiciousOnly.value = false
  showFailedOnly.value = false
  handleFilter()
}

const handleSaveFilter = () => {
  ElMessage.success('筛选条件已保存')
}

const handleApplyFilters = () => {
  handleFilter()
  ElMessage.success('筛选条件已应用')
}

const handleRowClick = (row) => {
  handleViewDetail(row)
}

const handleViewDetail = (log) => {
  selectedLog.value = log
  activeDetailTab.value = 'details'
  showDetailDialog.value = true
}

const handleCloseDetail = () => {
  selectedLog.value = null
}

const handleLogAction = async (command, log) => {
  switch (command) {
    case 'rollback':
      await handleRollbackOperation(log)
      break
    case 'export':
      handleExportDetail(log)
      break
    case 'flag':
      log.risk_level = 'high'
      ElMessage.success('已标记为高风险操作')
      break
    case 'unflag':
      log.risk_level = 'low'
      ElMessage.success('已取消异常标记')
      break
  }
}

const handleRollbackOperation = async (log) => {
  try {
    await ElMessageBox.confirm(
      `确认要回滚操作"${log.summary}"吗？此操作将撤销该权限变更。`,
      '回滚确认',
      { type: 'warning' }
    )
    
    ElMessage.success('操作回滚成功')
    // 这里应该调用API执行回滚
  } catch {
    // 用户取消
  }
}

const handleExportDetail = (log) => {
  ElMessage.info('导出详情功能开发中...')
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const handleRefreshLogs = () => {
  loadingLogs.value = true
  setTimeout(() => {
    loadingLogs.value = false
    ElMessage.success('日志已刷新')
  }, 1000)
}

const handleExportLogs = () => {
  ElMessage.info('导出日志功能开发中...')
}

const handleGenerateReport = () => {
  ElMessage.info('生成报告功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('权限审计日志页面已加载')
})
</script>

<style scoped>
.permission-audit {
  @apply space-y-6;
}

.page-header {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-gray-600 mt-1;
}

.header-actions {
  @apply flex space-x-3;
}

.audit-overview {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-card {
  @apply bg-gray-50 rounded-lg p-6 flex items-center space-x-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.stat-value {
  @apply text-2xl font-bold text-gray-800;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-change {
  @apply text-xs mt-1 font-medium;
}

.stat-change.positive {
  @apply text-green-600;
}

.stat-change.negative {
  @apply text-red-600;
}

.stat-change.neutral {
  @apply text-gray-500;
}

.stat-change.warning {
  @apply text-orange-600;
}

.stat-change.info {
  @apply text-blue-600;
}

.filter-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.filters-container {
  @apply space-y-4;
}

.filter-row {
  @apply flex flex-wrap items-end gap-4;
}

.filter-group {
  @apply flex flex-col space-y-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700;
}

.advanced-filters {
  @apply flex items-center space-x-4;
}

.filter-actions {
  @apply flex items-center space-x-3 pt-4 border-t border-gray-200;
}

.audit-logs-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.logs-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.logs-title {
  @apply flex items-center space-x-3;
}

.logs-count {
  @apply flex items-center;
}

.view-options {
  @apply flex items-center;
}

.table-view {
  @apply p-6 space-y-4;
}

.audit-table {
  @apply w-full;
}

.time-info {
  @apply space-y-1;
}

.time-primary {
  @apply text-sm font-medium text-gray-800;
}

.time-secondary {
  @apply text-xs text-gray-500;
}

.admin-info,
.target-user-info {
  @apply flex items-center;
}

.admin-name,
.user-name {
  @apply text-sm font-medium text-gray-800;
}

.operation-summary {
  @apply space-y-2;
}

.summary-text {
  @apply text-sm text-gray-800;
}

.summary-meta {
  @apply flex flex-wrap gap-1;
}

.permission-tag {
  @apply text-xs;
}

.more-permissions {
  @apply text-xs text-gray-500;
}

.result-indicator {
  @apply flex items-center justify-center;
}

.action-buttons {
  @apply flex items-center space-x-2;
}

.pagination-container {
  @apply flex justify-center pt-4 border-t border-gray-200;
}

.timeline-view {
  @apply p-6;
}

.timeline-container {
  @apply space-y-8;
}

.timeline-day {
  @apply space-y-4;
}

.day-header {
  @apply flex items-center justify-between;
}

.day-title {
  @apply text-lg font-semibold text-gray-800;
}

.day-stats {
  @apply text-sm text-gray-500;
}

.timeline-items {
  @apply space-y-4;
}

.timeline-item {
  @apply relative flex items-start space-x-4 p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors;
}

.timeline-item-suspicious {
  @apply border-red-200 bg-red-50;
}

.timeline-dot {
  @apply w-3 h-3 rounded-full flex-shrink-0 mt-2;
}

.timeline-dot-success {
  @apply bg-green-500;
}

.timeline-dot-failed {
  @apply bg-red-500;
}

.timeline-dot-warning {
  @apply bg-orange-500;
}

.timeline-dot-default {
  @apply bg-gray-400;
}

.timeline-content {
  @apply flex-1 space-y-2;
}

.timeline-header {
  @apply flex items-center space-x-3;
}

.timeline-time {
  @apply text-sm font-medium text-gray-600;
}

.timeline-action {
  @apply flex items-center;
}

.timeline-result {
  @apply flex items-center;
}

.timeline-summary {
  @apply text-sm text-gray-800;
}

.timeline-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.meta-item {
  @apply flex items-center;
}

.timeline-actions {
  @apply flex items-center space-x-2;
}

.audit-detail-expansion {
  @apply p-6 bg-gray-50;
}

.detail-sections {
  @apply space-y-6;
}

.detail-section {
  @apply space-y-3;
}

.detail-title {
  @apply text-sm font-semibold text-gray-800;
}

.detail-content {
  @apply space-y-2;
}

.detail-item {
  @apply flex items-start space-x-2;
}

.detail-label {
  @apply text-sm font-medium text-gray-600 min-w-0 flex-shrink-0;
}

.detail-value {
  @apply text-sm text-gray-800;
}

.changes-comparison {
  @apply space-y-4;
}

.change-item {
  @apply bg-white rounded-lg p-4 border border-gray-200;
}

.change-field {
  @apply text-sm font-medium text-gray-800 mb-2;
}

.change-values {
  @apply flex items-center space-x-4;
}

.old-value,
.new-value {
  @apply flex items-center space-x-2;
}

.value-label {
  @apply text-xs text-gray-600;
}

.arrow {
  @apply text-gray-400;
}

.audit-detail-content {
  @apply space-y-6;
}

.detail-header {
  @apply border-b border-gray-200 pb-4;
}

.detail-basic-info {
  @apply grid grid-cols-2 gap-4;
}

.info-item {
  @apply flex items-center space-x-2;
}

.info-label {
  @apply text-sm font-medium text-gray-600;
}

.info-value {
  @apply text-sm text-gray-800;
}

.detail-body {
  @apply space-y-4;
}

.operation-details {
  @apply space-y-6;
}

.section-title {
  @apply text-base font-semibold text-gray-800 mb-3;
}

.section-content {
  @apply text-sm text-gray-700 leading-relaxed;
}

.tech-info {
  @apply space-y-2;
}

.tech-item {
  @apply flex items-center space-x-2;
}

.tech-label {
  @apply text-sm font-medium text-gray-600 min-w-0 flex-shrink-0;
}

.tech-value {
  @apply text-sm text-gray-800;
}

.permission-changes {
  @apply space-y-4;
}

.change-detail {
  @apply bg-gray-50 rounded-lg p-4 space-y-3;
}

.change-header {
  @apply flex items-center justify-between;
}

.change-field {
  @apply text-sm font-semibold text-gray-800;
}

.change-comparison {
  @apply flex items-center space-x-4;
}

.comparison-side {
  @apply flex-1;
}

.side-label {
  @apply text-xs font-medium text-gray-600 mb-2;
}

.side-content {
  @apply p-3 rounded-lg text-sm;
}

.old-content {
  @apply bg-red-50 border border-red-200 text-red-800;
}

.new-content {
  @apply bg-green-50 border border-green-200 text-green-800;
}

.comparison-arrow {
  @apply flex items-center justify-center flex-shrink-0;
}

.risk-analysis {
  @apply space-y-6;
}

.risk-overview {
  @apply flex items-center space-x-6;
}

.risk-level,
.risk-score {
  @apply flex items-center space-x-2;
}

.risk-label {
  @apply text-sm font-medium text-gray-700;
}

.score-value {
  @apply text-lg font-bold text-red-600;
}

.risk-factors {
  @apply space-y-4;
}

.factors-title {
  @apply text-base font-semibold text-gray-800;
}

.factors-list {
  @apply space-y-3;
}

.risk-factor {
  @apply bg-red-50 border border-red-200 rounded-lg p-4 space-y-2;
}

.factor-name {
  @apply text-sm font-semibold text-red-800;
}

.factor-description {
  @apply text-sm text-red-700;
}

.factor-score {
  @apply text-xs text-red-600;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>