<template>
  <div class="user-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-description">管理系统中的所有用户账户</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <template #icon><el-icon><Plus /></el-icon></template>
          新增用户
        </el-button>
        <el-button @click="exportUsers">
          <template #icon><el-icon><Download /></el-icon></template>
          导出用户
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_users }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.active_users }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon inactive">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.inactive_users }}</div>
              <div class="stat-label">非活跃用户</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon suspended">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.suspended_users }}</div>
              <div class="stat-label">暂停用户</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索用户名、邮箱、姓名或公司"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.user_type"
            placeholder="用户类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="type in userTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="暂停" value="suspended" />
            <el-option label="已删除" value="deleted" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.company_type"
            placeholder="公司类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="外部公司" value="external" />
            <el-option label="内部员工" value="internal" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedUsers.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedUsers.length }} 个用户
      </div>
      <div class="batch-buttons">
        <el-button size="small" @click="batchAction('activate')">批量激活</el-button>
        <el-button size="small" @click="batchAction('deactivate')">批量停用</el-button>
        <el-button size="small" type="warning" @click="batchAction('suspend')">批量暂停</el-button>
        <el-button size="small" type="danger" @click="batchAction('delete')">批量删除</el-button>
      </div>
    </div>

    <!-- 用户列表表格 -->
    <div class="user-table">
      <el-table
        v-loading="loading"
        :data="users"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="real_name" label="真实姓名" width="100" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="company_name" label="公司名称" width="150" />
        <el-table-column prop="user_type" label="用户类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getUserTypeTagType(row.user_type)">
              {{ getUserTypeLabel(row.user_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_online" label="在线状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_online ? 'success' : 'info'" size="small">
              {{ row.is_online ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="login_count" label="登录次数" width="100" />
        <el-table-column prop="last_login" label="最后登录" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUser(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
            <el-dropdown @command="handleUserAction($event, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                  <el-dropdown-item command="viewLogs">查看日志</el-dropdown-item>
                  <el-dropdown-item command="changeStatus" divided>修改状态</el-dropdown-item>
                  <el-dropdown-item command="delete" style="color: #f56c6c">删除用户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model="showDetailDialog"
      :user="selectedUser"
      @updated="loadUsers"
    />

    <!-- 用户编辑对话框 -->
    <UserEditDialog
      v-model="showEditDialog"
      :user="selectedUser"
      @updated="loadUsers"
    />

    <!-- 用户创建对话框 -->
    <UserCreateDialog
      v-model="showCreateDialog"
      @created="loadUsers"
    />

    <!-- 批量操作确认对话框 -->
    <BatchActionDialog
      v-model="showBatchDialog"
      :action="batchActionType"
      :users="selectedUsers"
      @confirmed="confirmBatchAction"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usersApi } from '@/api/users'
import UserDetailDialog from '@/components/UserDetailDialog.vue'
import UserEditDialog from '@/components/UserEditDialog.vue'
import UserCreateDialog from '@/components/UserCreateDialog.vue'
import BatchActionDialog from '@/components/BatchActionDialog.vue'

// 响应式数据
const loading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const statistics = ref({
  total_users: 0,
  active_users: 0,
  inactive_users: 0,
  suspended_users: 0
})
const userTypes = ref([])

// 搜索表单
const searchForm = reactive({
  search: '',
  user_type: '',
  status: '',
  company_type: '',
  sort_by: 'created_at',
  sort_order: 'desc'
})

// 分页
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0,
  pages: 0,
  has_prev: false,
  has_next: false
})

// 对话框状态
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showCreateDialog = ref(false)
const showBatchDialog = ref(false)
const selectedUser = ref(null)
const batchActionType = ref('')

// 初始化
onMounted(() => {
  loadUsers()
  loadStatistics()
  loadUserTypes()
})

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...searchForm
    }
    
    const response = await usersApi.getUserList(params)
    if (response.success) {
      users.value = response.body.users
      Object.assign(pagination, response.body.pagination)
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStatistics = async () => {
  try {
    const response = await usersApi.getUserStatistics()
    if (response.success) {
      statistics.value = response.body
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 加载用户类型
const loadUserTypes = async () => {
  try {
    const response = await usersApi.getUserTypes()
    if (response.success) {
      userTypes.value = response.body.user_types
    }
  } catch (error) {
    console.error('加载用户类型失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 选择变更处理
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.per_page = size
  pagination.page = 1
  loadUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

// 用户操作
const viewUser = (user) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

const editUser = (user) => {
  selectedUser.value = user
  showEditDialog.value = true
}

const handleUserAction = async (command, user) => {
  selectedUser.value = user
  
  switch (command) {
    case 'resetPassword':
      await resetPassword(user)
      break
    case 'viewLogs':
      viewUserLogs(user)
      break
    case 'changeStatus':
      changeUserStatus(user)
      break
    case 'delete':
      deleteUser(user)
      break
  }
}

// 重置密码
const resetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.username} 的密码吗？`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await usersApi.resetUserPassword(user.id)
    if (response.success) {
      ElMessage.success(`密码重置成功，新密码：${response.body.new_password}`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 批量操作
const batchAction = (action) => {
  batchActionType.value = action
  showBatchDialog.value = true
}

const confirmBatchAction = async (reason) => {
  try {
    const userIds = selectedUsers.value.map(user => user.id)
    const response = await usersApi.batchUpdateUsers(userIds, batchActionType.value, reason)
    
    if (response.success) {
      ElMessage.success(`批量${batchActionType.value}操作成功`)
      selectedUsers.value = []
      loadUsers()
      loadStatistics()
    }
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  }
}

// 导出用户
const exportUsers = async () => {
  try {
    const response = await usersApi.exportUsers('csv', searchForm)
    if (response.success) {
      ElMessage.success('用户列表导出成功')
      // 这里应该触发文件下载
      window.open(response.body.download_url)
    }
  } catch (error) {
    console.error('导出用户失败:', error)
    ElMessage.error('导出用户失败')
  }
}

// 工具方法
const getUserTypeLabel = (type) => {
  const typeObj = userTypes.value.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getUserTypeTagType = (type) => {
  const typeMap = {
    admin: 'danger',
    platform_staff: 'warning',
    supplier: 'success',
    distributor: 'primary',
    airline_buyer: 'info',
    maintenance_engineer: '',
    logistics_specialist: ''
  }
  return typeMap[type] || ''
}

const getStatusLabel = (status) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '暂停',
    deleted: '已删除'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    suspended: 'warning',
    deleted: 'danger'
  }
  return statusMap[status] || ''
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>
