<!--
管理员页面布局组件
版本: 1.0
创建时间: 2025-07-23

专为管理员功能设计的布局组件
特点：
1. 左侧导航菜单
2. 顶部面包屑导航
3. 权限控制显示
4. 响应式设计
-->

<template>
  <div class="admin-layout">
    <!-- 顶部导航栏 -->
    <div class="admin-header">
      <div class="header-content">
        <div class="breadcrumb-area">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/app/workspace' }">
              <House class="w-4 h-4" />
              工作台
            </el-breadcrumb-item>
            <el-breadcrumb-item>系统管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-actions">
          <el-badge :value="pendingCount" :max="99" class="mr-4">
            <el-button size="small" circle @click="handlePendingTasks">
              <Bell class="w-4 h-4" />
            </el-button>
          </el-badge>
          
          <el-dropdown @command="handleUserAction">
            <div class="user-info">
              <Avatar class="w-6 h-6 mr-2" />
              <span class="username">{{ userStore.user?.username }}</span>
              <span class="permission-badge">
                {{ getSuperAdminBadge() }}
              </span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="workspace">返回工作台</el-dropdown-item>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
      <!-- 左侧导航菜单 -->
      <div class="admin-sidebar">
        <div class="sidebar-header">
          <h2 class="sidebar-title">系统管理中心</h2>
          <p class="sidebar-subtitle">权限等级 {{ userStore.user?.permission_level }}</p>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :router="true"
          class="admin-menu"
          @select="handleMenuSelect"
        >
          <!-- 权限管理 -->
          <el-sub-menu index="permissions" v-if="canManagePermissions">
            <template #title>
              <User class="w-4 h-4 mr-2" />
              <span>权限管理</span>
            </template>
            <el-menu-item index="/app/admin/permissions">
              <CirclePlus class="w-3 h-3 mr-2" />
              权限管理中心
            </el-menu-item>
            <el-menu-item index="/app/admin/roles">
              <CirclePlus class="w-3 h-3 mr-2" />
              角色权限配置
            </el-menu-item>
            <el-menu-item index="/app/admin/audit">
              <CirclePlus class="w-3 h-3 mr-2" />
              权限审计日志
            </el-menu-item>
            <el-menu-item index="/app/admin/temp-permissions">
              <CirclePlus class="w-3 h-3 mr-2" />
              临时权限管理
            </el-menu-item>
          </el-sub-menu>

          <!-- 系统配置 -->
          <el-sub-menu index="config">
            <template #title>
              <Setting class="w-4 h-4 mr-2" />
              <span>系统配置</span>
            </template>
            <el-menu-item index="/app/admin/config/app">
              <CirclePlus class="w-3 h-3 mr-2" />
              应用配置
            </el-menu-item>
            <el-menu-item 
              index="/app/admin/config/database" 
              v-if="isSuperAdmin"
            >
              <CirclePlus class="w-3 h-3 mr-2" />
              数据库配置
            </el-menu-item>
            <el-menu-item index="/app/admin/config/api">
              <CirclePlus class="w-3 h-3 mr-2" />
              API管理
            </el-menu-item>
            <el-menu-item index="/app/admin/config/security">
              <CirclePlus class="w-3 h-3 mr-2" />
              安全配置
            </el-menu-item>
          </el-sub-menu>

          <!-- 系统监控 -->
          <el-menu-item index="/app/admin/monitoring">
            <Cpu class="w-4 h-4 mr-2" />
            系统监控面板
          </el-menu-item>

          <!-- 数据管理 -->
          <el-sub-menu index="data" v-if="isSuperAdmin">
            <template #title>
              <Coin class="w-4 h-4 mr-2" />
              <span>数据管理</span>
            </template>
            <el-menu-item index="/app/admin/data">
              <CirclePlus class="w-3 h-3 mr-2" />
              数据管理中心
            </el-menu-item>
            <el-menu-item index="/app/admin/data-backup">
              <CirclePlus class="w-3 h-3 mr-2" />
              数据备份
            </el-menu-item>
            <el-menu-item index="/app/admin/data-recovery">
              <CirclePlus class="w-3 h-3 mr-2" />
              数据恢复
            </el-menu-item>
            <el-menu-item index="/app/admin/data-cleanup">
              <CirclePlus class="w-3 h-3 mr-2" />
              数据清理
            </el-menu-item>
          </el-sub-menu>

          <!-- 安全监控 -->
          <el-sub-menu index="security">
            <template #title>
              <Lock class="w-4 h-4 mr-2" />
              <span>安全监控</span>
            </template>
            <el-menu-item index="/app/admin/security/reports">
              <CirclePlus class="w-3 h-3 mr-2" />
              安全报告
            </el-menu-item>
            <el-menu-item index="/app/admin/audit-logs">
              <CirclePlus class="w-3 h-3 mr-2" />
              操作审计
            </el-menu-item>
            <el-menu-item index="/app/admin/security/alerts">
              <CirclePlus class="w-3 h-3 mr-2" />
              安全告警
            </el-menu-item>
          </el-sub-menu>

          <!-- 系统监控 -->
          <el-menu-item index="/app/admin/monitoring">
            <Monitor class="w-4 h-4 mr-2" />
            <span>系统监控</span>
          </el-menu-item>

          <!-- 返回工作台 -->
          <el-menu-item index="/app/workspace" class="back-to-workspace">
            <ArrowLeft class="w-4 h-4 mr-2" />
            <span>返回工作台</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容区域 -->
      <div class="admin-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  House, Bell, Avatar, User, Setting, Box, Lock,
  Monitor, ArrowLeft, CirclePlus, Cpu, Coin
} from '@element-plus/icons-vue'

// Stores & Composables
const route = useRoute()
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const pendingCount = ref(5)
const activeMenu = ref('')

// 计算属性
const canManagePermissions = computed(() => {
  return userStore.user?.permission_level >= 3
})

const isSuperAdmin = computed(() => {
  return userStore.user?.permission_level >= 4
})

const currentPageTitle = computed(() => {
  const routeNames = {
    '/app/admin/permissions': '权限管理中心',
    '/app/admin/roles': '角色权限配置',
    '/app/admin/audit': '权限审计日志',
    '/app/admin/temp-permissions': '临时权限管理',
    '/app/admin/config/app': '应用配置',
    '/app/admin/config/database': '数据库配置',
    '/app/admin/config/api': 'API管理',
    '/app/admin/config/security': '安全配置',
    '/app/admin/data-recovery': '数据恢复',
    '/app/admin/security/reports': '安全报告',
    '/app/admin/audit-logs': '操作审计',
    '/app/admin/monitoring': '系统监控'
  }
  return routeNames[route.path] || '管理面板'
})

const getSuperAdminBadge = () => {
  if (userStore.user?.permission_level >= 4) {
    return '超级管理员'
  }
  return '管理员'
}

// 方法
const handleMenuSelect = (index) => {
  activeMenu.value = index
  console.log('选择菜单:', index)
}

const handlePendingTasks = () => {
  ElMessage.info('待处理任务功能开发中...')
}

const handleUserAction = (command) => {
  switch (command) {
    case 'workspace':
      router.push('/app/workspace')
      break
    case 'profile':
      ElMessage.info('个人设置功能开发中...')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

// 监听路由变化，更新激活菜单
watch(() => route.path, (newPath) => {
  activeMenu.value = newPath
}, { immediate: true })
</script>

<style scoped>
.admin-layout {
  @apply min-h-screen bg-gray-50;
}

.admin-header {
  @apply bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40;
}

.header-content {
  @apply max-w-full px-6 py-4 flex items-center justify-between;
}

.breadcrumb-area {
  @apply flex items-center;
}

.header-actions {
  @apply flex items-center space-x-4;
}

.user-info {
  @apply flex items-center cursor-pointer px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors;
}

.username {
  @apply text-gray-700 font-medium mr-2;
}

.permission-badge {
  @apply text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full;
}

.admin-main {
  @apply flex min-h-[calc(100vh-73px)];
}

.admin-sidebar {
  @apply w-64 bg-white shadow-sm border-r border-gray-200 flex-shrink-0;
}

.sidebar-header {
  @apply p-6 border-b border-gray-200;
}

.sidebar-title {
  @apply text-lg font-semibold text-gray-800;
}

.sidebar-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.admin-menu {
  @apply border-none;
}

.admin-menu :deep(.el-menu-item) {
  @apply text-gray-700 hover:bg-blue-50 hover:text-blue-600;
}

.admin-menu :deep(.el-menu-item.is-active) {
  @apply bg-blue-50 text-blue-600 border-r-2 border-blue-600;
}

.admin-menu :deep(.el-sub-menu__title) {
  @apply text-gray-700 hover:bg-blue-50 hover:text-blue-600;
}

.admin-menu :deep(.el-sub-menu .el-menu-item) {
  @apply text-gray-600 hover:bg-blue-50/50 hover:text-blue-600;
}

.back-to-workspace {
  @apply mt-4 border-t border-gray-200 pt-4;
}

.admin-content {
  @apply flex-1 p-6 overflow-auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    @apply absolute left-0 top-0 bottom-0 z-30 -translate-x-full transition-transform;
  }
  
  .admin-sidebar.mobile-open {
    @apply translate-x-0;
  }
  
  .admin-content {
    @apply w-full;
  }
}
</style>