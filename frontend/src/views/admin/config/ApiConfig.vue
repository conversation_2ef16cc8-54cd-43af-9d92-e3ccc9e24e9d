<!--
API接口配置管理
版本: 1.0
创建时间: 2025-07-23

系统管理员专用的API接口配置管理界面
特点：
1. API访问控制和限流配置
2. 第三方服务集成配置
3. API密钥和认证管理
4. 接口监控和日志配置
5. API版本和路由管理
-->

<template>
  <div class="api-config">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">API配置</h1>
          <p class="page-subtitle">管理API接口配置，控制访问权限，配置第三方服务集成</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleTestApi">
            <Zap class="w-4 h-4 mr-2" />
            接口测试
          </el-button>
          <el-button @click="handleGenerateKey">
            <Key class="w-4 h-4 mr-2" />
            生成密钥
          </el-button>
          <el-button @click="handleSaveConfig" type="primary">
            <Save class="w-4 h-4 mr-2" />
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- API状态概览 -->
    <div class="api-status-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="status-card api-health">
          <div class="card-icon bg-green-100 text-green-600">
            <Monitor class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ apiStatus.healthScore }}%</div>
            <div class="card-label">API健康度</div>
            <div class="card-detail">{{ apiStatus.activeEndpoints }} 个活跃接口</div>
          </div>
        </div>
        
        <div class="status-card request-rate">
          <div class="card-icon bg-blue-100 text-blue-600">
            <BarChart3 class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ apiStatus.requestsPerMinute }}</div>
            <div class="card-label">请求/分钟</div>
            <div class="card-detail">峰值: {{ apiStatus.peakRequests }}/分钟</div>
          </div>
        </div>
        
        <div class="status-card error-rate">
          <div class="card-icon bg-red-100 text-red-600">
            <Warning class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ apiStatus.errorRate }}%</div>
            <div class="card-label">错误率</div>
            <div class="card-detail">{{ apiStatus.totalErrors }} 次错误</div>
          </div>
        </div>
        
        <div class="status-card active-keys">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Key class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ apiStatus.activeKeys }}</div>
            <div class="card-label">活跃API密钥</div>
            <div class="card-detail">{{ apiStatus.totalKeys }} 个总密钥</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content">
      <el-tabs v-model="activeApiTab" @tab-change="handleTabChange">
        
        <!-- 访问控制 -->
        <el-tab-pane label="访问控制" name="access">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">全局访问设置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用API访问</label>
                  <el-switch v-model="apiConfig.access.enabled" />
                </div>
                <div class="config-item">
                  <label class="config-label">需要认证</label>
                  <el-switch v-model="apiConfig.access.requireAuth" />
                </div>
                <div class="config-item">
                  <label class="config-label">允许跨域</label>
                  <el-switch v-model="apiConfig.access.allowCors" />
                </div>
                <div class="config-item">
                  <label class="config-label">API版本</label>
                  <el-input v-model="apiConfig.access.version" placeholder="v1.0" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">限流配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用限流</label>
                  <el-switch v-model="apiConfig.rateLimit.enabled" />
                </div>
                <div class="config-item">
                  <label class="config-label">每分钟请求数</label>
                  <el-input-number v-model="apiConfig.rateLimit.requestsPerMinute" :min="1" :max="10000" />
                </div>
                <div class="config-item">
                  <label class="config-label">每小时请求数</label>
                  <el-input-number v-model="apiConfig.rateLimit.requestsPerHour" :min="1" :max="100000" />
                </div>
                <div class="config-item">
                  <label class="config-label">超限处理</label>
                  <el-select v-model="apiConfig.rateLimit.overLimitAction">
                    <el-option label="拒绝请求" value="reject" />
                    <el-option label="排队等待" value="queue" />
                    <el-option label="降级服务" value="degrade" />
                  </el-select>
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">IP白名单</h3>
              <div class="whitelist-config">
                <div class="whitelist-controls">
                  <el-switch v-model="apiConfig.access.useWhitelist" />
                  <span class="control-label">启用IP白名单</span>
                  <el-button @click="handleAddIp" size="small">添加IP</el-button>
                </div>
                <div v-if="apiConfig.access.useWhitelist" class="whitelist-items">
                  <div v-for="(ip, index) in apiConfig.access.whitelist" :key="index" class="whitelist-item">
                    <el-input v-model="apiConfig.access.whitelist[index]" placeholder="*********** 或 ***********/24" />
                    <el-button @click="handleRemoveIp(index)" size="small" type="danger">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 第三方集成 -->
        <el-tab-pane label="第三方集成" name="integrations">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">支付服务配置</h3>
              <div class="integration-cards">
                <div class="integration-card">
                  <div class="card-header">
                    <h4>支付宝</h4>
                    <el-switch v-model="apiConfig.integrations.alipay.enabled" />
                  </div>
                  <div v-if="apiConfig.integrations.alipay.enabled" class="card-content">
                    <div class="config-item">
                      <label class="config-label">App ID</label>
                      <el-input v-model="apiConfig.integrations.alipay.appId" placeholder="请输入支付宝App ID" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">私钥</label>
                      <el-input v-model="apiConfig.integrations.alipay.privateKey" type="password" placeholder="请输入应用私钥" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">公钥</label>
                      <el-input v-model="apiConfig.integrations.alipay.publicKey" type="password" placeholder="请输入支付宝公钥" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">环境</label>
                      <el-select v-model="apiConfig.integrations.alipay.environment">
                        <el-option label="沙箱环境" value="sandbox" />
                        <el-option label="生产环境" value="production" />
                      </el-select>
                    </div>
                  </div>
                </div>

                <div class="integration-card">
                  <div class="card-header">
                    <h4>微信支付</h4>
                    <el-switch v-model="apiConfig.integrations.wechatPay.enabled" />
                  </div>
                  <div v-if="apiConfig.integrations.wechatPay.enabled" class="card-content">
                    <div class="config-item">
                      <label class="config-label">商户号</label>
                      <el-input v-model="apiConfig.integrations.wechatPay.mchId" placeholder="请输入微信支付商户号" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">API密钥</label>
                      <el-input v-model="apiConfig.integrations.wechatPay.apiKey" type="password" placeholder="请输入API密钥" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">证书路径</label>
                      <el-input v-model="apiConfig.integrations.wechatPay.certPath" placeholder="请输入证书文件路径" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">短信服务配置</h3>
              <div class="integration-cards">
                <div class="integration-card">
                  <div class="card-header">
                    <h4>阿里云短信</h4>
                    <el-switch v-model="apiConfig.integrations.aliyunSms.enabled" />
                  </div>
                  <div v-if="apiConfig.integrations.aliyunSms.enabled" class="card-content">
                    <div class="config-item">
                      <label class="config-label">AccessKey ID</label>
                      <el-input v-model="apiConfig.integrations.aliyunSms.accessKeyId" placeholder="请输入AccessKey ID" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">AccessKey Secret</label>
                      <el-input v-model="apiConfig.integrations.aliyunSms.accessKeySecret" type="password" placeholder="请输入AccessKey Secret" />
                    </div>
                    <div class="config-item">
                      <label class="config-label">短信签名</label>
                      <el-input v-model="apiConfig.integrations.aliyunSms.signName" placeholder="航材共享平台" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- API密钥管理 -->
        <el-tab-pane label="密钥管理" name="keys">
          <div class="config-panel">
            <div class="keys-section">
              <div class="section-header">
                <h3 class="section-title">API密钥列表</h3>
                <el-button @click="handleCreateKey" type="primary">创建新密钥</el-button>
              </div>
              
              <div class="keys-table">
                <el-table :data="apiKeys" style="width: 100%">
                  <el-table-column prop="name" label="密钥名称" width="200" />
                  <el-table-column prop="key" label="API Key" width="300">
                    <template #default="scope">
                      <div class="key-display">
                        <span v-if="scope.row.showKey">{{ scope.row.key }}</span>
                        <span v-else>{{ maskApiKey(scope.row.key) }}</span>
                        <el-button @click="toggleKeyVisibility(scope.row)" size="small" text>
                          {{ scope.row.showKey ? '隐藏' : '显示' }}
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="permissions" label="权限范围" width="180">
                    <template #default="scope">
                      <el-tag v-for="perm in scope.row.permissions" :key="perm" size="small" style="margin-right: 4px;">
                        {{ perm }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastUsed" label="最后使用" width="180" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                        {{ scope.row.status === 'active' ? '活跃' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button size="small" @click="handleEditKey(scope.row)">编辑</el-button>
                      <el-button size="small" @click="handleCopyDocumentKey(scope.row)">复制</el-button>
                      <el-button size="small" type="danger" @click="handleDeleteKey(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 监控日志 -->
        <el-tab-pane label="监控日志" name="monitoring">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">日志配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用访问日志</label>
                  <el-switch v-model="apiConfig.logging.accessLog" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用错误日志</label>
                  <el-switch v-model="apiConfig.logging.errorLog" />
                </div>
                <div class="config-item">
                  <label class="config-label">日志级别</label>
                  <el-select v-model="apiConfig.logging.logLevel">
                    <el-option label="DEBUG" value="debug" />
                    <el-option label="INFO" value="info" />
                    <el-option label="WARN" value="warn" />
                    <el-option label="ERROR" value="error" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">日志保留天数</label>
                  <el-input-number v-model="apiConfig.logging.retentionDays" :min="1" :max="365" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">最近API调用记录</h3>
              <div class="api-logs">
                <el-table :data="apiLogs" style="width: 100%">
                  <el-table-column prop="timestamp" label="时间" width="180" />
                  <el-table-column prop="method" label="方法" width="80" />
                  <el-table-column prop="endpoint" label="接口" width="200" />
                  <el-table-column prop="ip" label="来源IP" width="120" />
                  <el-table-column prop="status" label="状态码" width="80">
                    <template #default="scope">
                      <el-tag :type="getStatusType(scope.row.status)">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="responseTime" label="响应时间" width="100" />
                  <el-table-column prop="userAgent" label="User Agent" />
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入

// 当前激活的API配置标签
const activeApiTab = ref('access')

// API状态
const apiStatus = reactive({
  healthScore: 98,
  activeEndpoints: 45,
  requestsPerMinute: 125,
  peakRequests: 580,
  errorRate: 0.2,
  totalErrors: 12,
  activeKeys: 8,
  totalKeys: 15
})

// API配置
const apiConfig = reactive({
  access: {
    enabled: true,
    requireAuth: true,
    allowCors: true,
    version: 'v1.0',
    useWhitelist: false,
    whitelist: ['***********/24']
  },
  rateLimit: {
    enabled: true,
    requestsPerMinute: 1000,
    requestsPerHour: 50000,
    overLimitAction: 'reject'
  },
  integrations: {
    alipay: {
      enabled: false,
      appId: '',
      privateKey: '',
      publicKey: '',
      environment: 'sandbox'
    },
    wechatPay: {
      enabled: false,
      mchId: '',
      apiKey: '',
      certPath: ''
    },
    aliyunSms: {
      enabled: true,
      accessKeyId: '',
      accessKeySecret: '',
      signName: '航材共享平台'
    }
  },
  logging: {
    accessLog: true,
    errorLog: true,
    logLevel: 'info',
    retentionDays: 30
  }
})

// API密钥列表
const apiKeys = ref([
  {
    id: 1,
    name: '移动端应用',
    key: 'ak_1234567890abcdef1234567890abcdef',
    permissions: ['read', 'write'],
    lastUsed: '2025-07-23 15:30:00',
    status: 'active',
    showKey: false
  },
  {
    id: 2,
    name: '第三方集成',
    key: 'ak_abcdef1234567890abcdef1234567890',
    permissions: ['read'],
    lastUsed: '2025-07-23 14:20:00',
    status: 'active',
    showKey: false
  },
  {
    id: 3,
    name: '测试密钥',
    key: 'ak_test1234567890abcdef1234567890ab',
    permissions: ['read'],
    lastUsed: '2025-07-22 10:15:00',
    status: 'disabled',
    showKey: false
  }
])

// API调用日志
const apiLogs = ref([
  {
    timestamp: '2025-07-23 21:15:30',
    method: 'GET',
    endpoint: '/api/v1/materials',
    ip: '***********00',
    status: 200,
    responseTime: '15ms',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
  },
  {
    timestamp: '2025-07-23 21:15:25',
    method: 'POST',
    endpoint: '/api/v1/orders',
    ip: '***********01',
    status: 201,
    responseTime: '32ms',
    userAgent: 'axios/0.21.1'
  },
  {
    timestamp: '2025-07-23 21:15:20',
    method: 'GET',
    endpoint: '/api/v1/users/profile',
    ip: '***********02',
    status: 401,
    responseTime: '5ms',
    userAgent: 'okhttp/4.9.0'
  }
])

// 组件挂载时加载数据
onMounted(() => {
  loadApiConfig()
})

// 加载API配置
function loadApiConfig() {
  console.log('加载API配置')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到API配置标签:', tabName)
}

// 测试API接口
function handleTestApi() {
  ElMessage.info('正在测试API接口...')
  setTimeout(() => {
    ElMessage.success('API接口测试成功')
  }, 2000)
}

// 生成新的API密钥
function handleGenerateKey() {
  const newKey = 'ak_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  ElMessage.success(`新密钥已生成: ${newKey}`)
}

// 保存配置
function handleSaveConfig() {
  console.log('保存API配置:', apiConfig)
  ElMessage.success('API配置已保存')
}

// 添加IP到白名单
function handleAddIp() {
  apiConfig.access.whitelist.push('')
}

// 从白名单删除IP
function handleRemoveIp(index) {
  apiConfig.access.whitelist.splice(index, 1)
}

// 创建新密钥
function handleCreateKey() {
  ElMessage.info('创建新API密钥功能')
}

// 编辑密钥
function handleEditKey(key) {
  ElMessage.info(`编辑密钥: ${key.name}`)
}

// 复制密钥
function handleCopyDocumentKey(key) {
  navigator.clipboard.writeText(key.key).then(() => {
    ElMessage.success('API密钥已复制到剪贴板')
  })
}

// 删除密钥
function handleDeleteKey(key) {
  ElMessageBox.confirm(`确定要删除密钥 ${key.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = apiKeys.value.findIndex(item => item.id === key.id)
    if (index !== -1) {
      apiKeys.value.splice(index, 1)
      ElMessage.success('API密钥已删除')
    }
  })
}

// 切换密钥显示状态
function toggleKeyVisibility(key) {
  key.showKey = !key.showKey
}

// 遮罩API密钥
function maskApiKey(key) {
  return key.substring(0, 8) + '****' + key.substring(key.length - 8)
}

// 获取状态码类型
function getStatusType(status) {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400 && status < 500) return 'warning'
  if (status >= 500) return 'danger'
  return 'info'
}
</script>

<style scoped>
.api-config {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.api-status-overview {
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

.config-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.config-panel {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.whitelist-config {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.whitelist-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.control-label {
  font-size: 14px;
  color: #374151;
}

.whitelist-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.whitelist-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.integration-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.integration-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.keys-section {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.keys-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.key-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-logs {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .api-config {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .config-grid, .integration-cards {
    grid-template-columns: 1fr;
  }
  
  .status-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>