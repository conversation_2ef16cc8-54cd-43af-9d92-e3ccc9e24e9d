<!--
应用程序配置管理
版本: 1.0
创建时间: 2025-07-23

系统管理员专用的应用配置管理界面
特点：
1. 系统参数配置和实时更新
2. 功能模块开关控制
3. 界面主题和语言设置
4. 邮件和通知配置
5. 业务流程参数配置
-->

<template>
  <div class="app-config">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">应用配置</h1>
          <p class="page-subtitle">管理系统全局配置参数，控制应用功能和界面设置</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleReloadConfig">
            <Refresh class="w-4 h-4 mr-2" />
            重新加载
          </el-button>
          <el-button @click="handleExportConfig">
            <Download class="w-4 h-4 mr-2" />
            导出配置
          </el-button>
          <el-button @click="handleSaveConfig" type="primary">
            <Save class="w-4 h-4 mr-2" />
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content">
      <el-tabs v-model="activeConfigTab" @tab-change="handleTabChange">
        
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">系统信息</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">系统名称</label>
                  <el-input v-model="config.basic.systemName" placeholder="航材共享保障平台" />
                </div>
                <div class="config-item">
                  <label class="config-label">系统版本</label>
                  <el-input v-model="config.basic.systemVersion" placeholder="v1.0.0" />
                </div>
                <div class="config-item">
                  <label class="config-label">公司名称</label>
                  <el-input v-model="config.basic.companyName" placeholder="航材科技有限公司" />
                </div>
                <div class="config-item">
                  <label class="config-label">联系电话</label>
                  <el-input v-model="config.basic.contactPhone" placeholder="+86-************" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">界面设置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">默认主题</label>
                  <el-select v-model="config.basic.defaultTheme">
                    <el-option label="浅色主题" value="light" />
                    <el-option label="深色主题" value="dark" />
                    <el-option label="自动切换" value="auto" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">默认语言</label>
                  <el-select v-model="config.basic.defaultLanguage">
                    <el-option label="简体中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">时区设置</label>
                  <el-select v-model="config.basic.timezone">
                    <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                    <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                    <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">页面大小</label>
                  <el-input-number v-model="config.basic.pageSize" :min="10" :max="100" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 功能配置 -->
        <el-tab-pane label="功能配置" name="features">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">核心功能模块</h3>
              <div class="feature-switches">
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>共享件交易</h4>
                    <p>启用航材共享件发布和交易功能</p>
                  </div>
                  <el-switch v-model="config.features.sharedTrading" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>在线支付</h4>
                    <p>启用在线支付和结算功能</p>
                  </div>
                  <el-switch v-model="config.features.onlinePayment" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>实时聊天</h4>
                    <p>启用用户间实时聊天功能</p>
                  </div>
                  <el-switch v-model="config.features.realTimeChat" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>移动端支持</h4>
                    <p>启用移动端访问和响应式界面</p>
                  </div>
                  <el-switch v-model="config.features.mobileSupport" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">高级功能</h3>
              <div class="feature-switches">
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>AI智能推荐</h4>
                    <p>启用基于AI的航材智能推荐</p>
                  </div>
                  <el-switch v-model="config.features.aiRecommendation" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>数据分析</h4>
                    <p>启用业务数据分析和报表</p>
                  </div>
                  <el-switch v-model="config.features.dataAnalytics" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>API开放接口</h4>
                    <p>启用第三方系统集成API</p>
                  </div>
                  <el-switch v-model="config.features.openApi" />
                </div>
                <div class="feature-item">
                  <div class="feature-info">
                    <h4>消息推送</h4>
                    <p>启用邮件和短信消息推送</p>
                  </div>
                  <el-switch v-model="config.features.messagePush" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 通知配置 -->
        <el-tab-pane label="通知配置" name="notifications">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">邮件配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">SMTP服务器</label>
                  <el-input v-model="config.notifications.smtp.server" placeholder="smtp.example.com" />
                </div>
                <div class="config-item">
                  <label class="config-label">端口</label>
                  <el-input-number v-model="config.notifications.smtp.port" :min="1" :max="65535" />
                </div>
                <div class="config-item">
                  <label class="config-label">用户名</label>
                  <el-input v-model="config.notifications.smtp.username" placeholder="<EMAIL>" />
                </div>
                <div class="config-item">
                  <label class="config-label">密码</label>
                  <el-input v-model="config.notifications.smtp.password" type="password" placeholder="••••••••" />
                </div>
                <div class="config-item">
                  <label class="config-label">发件人名称</label>
                  <el-input v-model="config.notifications.smtp.senderName" placeholder="航材共享平台" />
                </div>
                <div class="config-item">
                  <label class="config-label">使用SSL</label>
                  <el-switch v-model="config.notifications.smtp.ssl" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">短信配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">短信服务商</label>
                  <el-select v-model="config.notifications.sms.provider">
                    <el-option label="阿里云短信" value="aliyun" />
                    <el-option label="腾讯云短信" value="tencent" />
                    <el-option label="华为云短信" value="huawei" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">AccessKey ID</label>
                  <el-input v-model="config.notifications.sms.accessKeyId" placeholder="请输入AccessKey ID" />
                </div>
                <div class="config-item">
                  <label class="config-label">AccessKey Secret</label>
                  <el-input v-model="config.notifications.sms.accessKeySecret" type="password" placeholder="••••••••" />
                </div>
                <div class="config-item">
                  <label class="config-label">短信签名</label>
                  <el-input v-model="config.notifications.sms.signature" placeholder="航材共享平台" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 业务配置 -->
        <el-tab-pane label="业务配置" name="business">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">交易参数</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">平台手续费率 (%)</label>
                  <el-input-number v-model="config.business.platformFeeRate" :min="0" :max="10" :precision="2" />
                </div>
                <div class="config-item">
                  <label class="config-label">最小交易金额 (元)</label>
                  <el-input-number v-model="config.business.minTransactionAmount" :min="0" />
                </div>
                <div class="config-item">
                  <label class="config-label">最大交易金额 (元)</label>
                  <el-input-number v-model="config.business.maxTransactionAmount" :min="0" />
                </div>
                <div class="config-item">
                  <label class="config-label">订单超时时间 (小时)</label>
                  <el-input-number v-model="config.business.orderTimeout" :min="1" :max="72" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">审核参数</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">自动审核阈值 (元)</label>
                  <el-input-number v-model="config.business.autoApprovalThreshold" :min="0" />
                </div>
                <div class="config-item">
                  <label class="config-label">审核超时时间 (小时)</label>
                  <el-input-number v-model="config.business.approvalTimeout" :min="1" :max="168" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用多级审核</label>
                  <el-switch v-model="config.business.multiLevelApproval" />
                </div>
                <div class="config-item">
                  <label class="config-label">强制实名认证</label>
                  <el-switch v-model="config.business.forceRealNameAuth" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入

// 当前激活的配置标签
const activeConfigTab = ref('basic')

// 配置数据
const config = reactive({
  basic: {
    systemName: '航材共享保障平台',
    systemVersion: 'v1.0.0',
    companyName: '航材科技有限公司',
    contactPhone: '+86-************',
    defaultTheme: 'light',
    defaultLanguage: 'zh-CN',
    timezone: 'Asia/Shanghai',
    pageSize: 20
  },
  features: {
    sharedTrading: true,
    onlinePayment: true,
    realTimeChat: true,
    mobileSupport: true,
    aiRecommendation: false,
    dataAnalytics: true,
    openApi: false,
    messagePush: true
  },
  notifications: {
    smtp: {
      server: 'smtp.example.com',
      port: 587,
      username: '<EMAIL>',
      password: '',
      senderName: '航材共享平台',
      ssl: true
    },
    sms: {
      provider: 'aliyun',
      accessKeyId: '',
      accessKeySecret: '',
      signature: '航材共享平台'
    }
  },
  business: {
    platformFeeRate: 2.5,
    minTransactionAmount: 100,
    maxTransactionAmount: 1000000,
    orderTimeout: 24,
    autoApprovalThreshold: 10000,
    approvalTimeout: 48,
    multiLevelApproval: true,
    forceRealNameAuth: true
  }
})

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})

// 加载配置数据
function loadConfig() {
  // 模拟从后端加载配置数据
  console.log('加载应用配置数据')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到配置标签:', tabName)
}

// 重新加载配置
function handleReloadConfig() {
  loadConfig()
  ElMessage.success('配置已重新加载')
}

// 导出配置
function handleExportConfig() {
  const configData = JSON.stringify(config, null, 2)
  const blob = new Blob([configData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `app-config-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('配置文件已导出')
}

// 保存配置
function handleSaveConfig() {
  // 模拟保存配置到后端
  console.log('保存配置:', config)
  ElMessage.success('配置已保存')
}
</script>

<style scoped>
.app-config {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.config-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.config-panel {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.feature-switches {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.feature-info h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.feature-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .app-config {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
}
</style>