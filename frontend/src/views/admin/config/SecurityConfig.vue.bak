<!--
安全配置管理
版本: 1.0
创建时间: 2025-07-23

系统管理员专用的安全配置管理界面
特点：
1. 密码策略和登录安全配置
2. 会话管理和超时设置
3. 防护策略和入侵检测
4. 加密配置和证书管理
5. 审计日志和安全报告
-->

<template>
  <div class="security-config">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">安全配置</h1>
          <p class="page-subtitle">管理系统安全策略，配置防护机制，确保平台安全运行</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleSecurityScan">
            <Shield class="w-4 h-4 mr-2" />
            安全扫描
          </el-button>
          <el-button @click="handleExportLogs">
            <Download class="w-4 h-4 mr-2" />
            导出日志
          </el-button>
          <el-button @click="handleSaveConfig" type="primary">
            <Save class="w-4 h-4 mr-2" />
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 安全状态概览 -->
    <div class="security-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card security-level">
          <div class="card-icon bg-green-100 text-green-600">
            <Shield class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityStatus.level }}</div>
            <div class="card-label">安全等级</div>
            <div class="card-detail">{{ securityStatus.score }}/100 分</div>
          </div>
        </div>
        
        <div class="overview-card failed-logins">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Warning class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityStatus.failedLogins }}</div>
            <div class="card-label">今日失败登录</div>
            <div class="card-detail">阻止 {{ securityStatus.blockedIps }} 个IP</div>
          </div>
        </div>
        
        <div class="overview-card active_sessions">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityStatus.activeSessions }}</div>
            <div class="card-label">活跃会话数</div>
            <div class="card-detail">最大: {{ securityStatus.maxSessions }}</div>
          </div>
        </div>
        
        <div class="overview-card security-events">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Eye class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityStatus.securityEvents }}</div>
            <div class="card-label">安全事件</div>
            <div class="card-detail">本周新增 {{ securityStatus.newEvents }} 个</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content">
      <el-tabs v-model="activeSecurityTab" @tab-change="handleTabChange">
        
        <!-- 登录安全 -->
        <el-tab-pane label="登录安全" name="login">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">密码策略</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">最小密码长度</label>
                  <el-input-number v-model="securityConfig.password.minLength" :min="6" :max="32" />
                </div>
                <div class="config-item">
                  <label class="config-label">密码复杂度要求</label>
                  <div class="complexity-options">
                    <el-checkbox v-model="securityConfig.password.requireUppercase">大写字母</el-checkbox>
                    <el-checkbox v-model="securityConfig.password.requireLowercase">小写字母</el-checkbox>
                    <el-checkbox v-model="securityConfig.password.requireNumbers">数字</el-checkbox>
                    <el-checkbox v-model="securityConfig.password.requireSpecialChars">特殊字符</el-checkbox>
                  </div>
                </div>
                <div class="config-item">
                  <label class="config-label">密码有效期 (天)</label>
                  <el-input-number v-model="securityConfig.password.expiryDays" :min="0" :max="365" />
                </div>
                <div class="config-item">
                  <label class="config-label">密码历史记录</label>
                  <el-input-number v-model="securityConfig.password.historyCount" :min="0" :max="10" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">登录限制</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">最大登录尝试次数</label>
                  <el-input-number v-model="securityConfig.login.maxAttempts" :min="3" :max="10" />
                </div>
                <div class="config-item">
                  <label class="config-label">账户锁定时间 (分钟)</label>
                  <el-input-number v-model="securityConfig.login.lockoutDuration" :min="5" :max="1440" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用验证码</label>
                  <el-switch v-model="securityConfig.login.enableCaptcha" />
                </div>
                <div class="config-item">
                  <label class="config-label">验证码触发次数</label>
                  <el-input-number v-model="securityConfig.login.captchaThreshold" :min="1" :max="5" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用双因子认证</label>
                  <el-switch v-model="securityConfig.login.enable2FA" />
                </div>
                <div class="config-item">
                  <label class="config-label">强制双因子认证</label>
                  <el-switch v-model="securityConfig.login.force2FA" :disabled="!securityConfig.login.enable2FA" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">IP访问控制</h3>
              <div class="ip-control">
                <div class="control-header">
                  <el-radio-group v-model="securityConfig.ipControl.mode">
                    <el-radio value="disabled">不限制</el-radio>
                    <el-radio value="whitelist">白名单模式</el-radio>
                    <el-radio value="blacklist">黑名单模式</el-radio>
                  </el-radio-group>
                </div>
                
                <div v-if="securityConfig.ipControl.mode !== 'disabled'" class="ip-list-config">
                  <div class="list-header">
                    <h4>{{ securityConfig.ipControl.mode === 'whitelist' ? 'IP白名单' : 'IP黑名单' }}</h4>
                    <el-button @click="handleAddIp" size="small">添加IP</el-button>
                  </div>
                  <div class="ip-items">
                    <div v-for="(ip, index) in securityConfig.ipControl.ipList" :key="index" class="ip-item">
                      <el-input v-model="securityConfig.ipControl.ipList[index]" placeholder="*********** 或 ***********/24" />
                      <el-button @click="handleRemoveIp(index)" size="small" type="danger">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 会话管理 -->
        <el-tab-pane label="会话管理" name="session">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">会话设置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">会话超时时间 (分钟)</label>
                  <el-input-number v-model="securityConfig.session.timeout" :min="15" :max="1440" />
                </div>
                <div class="config-item">
                  <label class="config-label">最大并发会话数</label>
                  <el-input-number v-model="securityConfig.session.maxConcurrent" :min="1" :max="10" />
                </div>
                <div class="config-item">
                  <label class="config-label">记住我功能</label>
                  <el-switch v-model="securityConfig.session.rememberMe" />
                </div>
                <div class="config-item">
                  <label class="config-label">记住时长 (天)</label>
                  <el-input-number v-model="securityConfig.session.rememberDuration" :min="1" :max="30" :disabled="!securityConfig.session.rememberMe" />
                </div>
                <div class="config-item">
                  <label class="config-label">同一IP多设备登录</label>
                  <el-switch v-model="securityConfig.session.allowMultiDevice" />
                </div>
                <div class="config-item">
                  <label class="config-label">会话固定</label>
                  <el-switch v-model="securityConfig.session.fixation" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">当前活跃会话</h3>
              <div class="sessions-table">
                <el-table :data="activeSessions" style="width: 100%">
                  <el-table-column prop="userId" label="用户ID" width="100" />
                  <el-table-column prop="username" label="用户名" width="120" />
                  <el-table-column prop="ip" label="IP地址" width="140" />
                  <el-table-column prop="location" label="地理位置" width="120" />
                  <el-table-column prop="device" label="设备信息" />
                  <el-table-column prop="loginTime" label="登录时间" width="180" />
                  <el-table-column prop="lastMonitor" label="最后活动" width="180" />
                  <el-table-column label="操作" width="100">
                    <template #default="scope">
                      <el-button size="small" type="danger" @click="handleKillSession(scope.row)">踢出</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 防护策略 -->
        <el-tab-pane label="防护策略" name="protection">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">防攻击配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用SQL注入防护</label>
                  <el-switch v-model="securityConfig.protection.sqlInjection" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用XSS防护</label>
                  <el-switch v-model="securityConfig.protection.xssProtection" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用CSRF防护</label>
                  <el-switch v-model="securityConfig.protection.csrfProtection" />
                </div>
                <div class="config-item">
                  <label class="config-label">启用点击劫持防护</label>
                  <el-switch v-model="securityConfig.protection.clickjacking" />
                </div>
                <div class="config-item">
                  <label class="config-label">请求频率限制 (次/分钟)</label>
                  <el-input-number v-model="securityConfig.protection.rateLimit" :min="10" :max="1000" />
                </div>
                <div class="config-item">
                  <label class="config-label">文件上传安全检查</label>
                  <el-switch v-model="securityConfig.protection.fileUploadScan" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">入侵检测</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用入侵检测</label>
                  <el-switch v-model="securityConfig.intrusion.enabled" />
                </div>
                <div class="config-item">
                  <label class="config-label">异常登录检测</label>
                  <el-switch v-model="securityConfig.intrusion.abnormalLogin" />
                </div>
                <div class="config-item">
                  <label class="config-label">暴力破解检测</label>
                  <el-switch v-model="securityConfig.intrusion.bruteForce" />
                </div>
                <div class="config-item">
                  <label class="config-label">异常操作检测</label>
                  <el-switch v-model="securityConfig.intrusion.abnormalOperation" />
                </div>
                <div class="config-item">
                  <label class="config-label">检测敏感度</label>
                  <el-select v-model="securityConfig.intrusion.sensitivity">
                    <el-option label="低" value="low" />
                    <el-option label="中" value="medium" />
                    <el-option label="高" value="high" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">自动响应</label>
                  <el-switch v-model="securityConfig.intrusion.autoResponse" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 加密证书 -->
        <el-tab-pane label="加密证书" name="encryption">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">加密配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">数据传输加密</label>
                  <el-switch v-model="securityConfig.encryption.dataTransmission" />
                </div>
                <div class="config-item">
                  <label class="config-label">数据存储加密</label>
                  <el-switch v-model="securityConfig.encryption.dataStorage" />
                </div>
                <div class="config-item">
                  <label class="config-label">密码加密算法</label>
                  <el-select v-model="securityConfig.encryption.passwordAlgorithm">
                    <el-option label="bcrypt" value="bcrypt" />
                    <el-option label="scrypt" value="scrypt" />
                    <el-option label="PBKDF2" value="pbkdf2" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">会话Token加密</label>
                  <el-switch v-model="securityConfig.encryption.sessionToken" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">SSL/TLS证书</h3>
              <div class="certificate-config">
                <div class="cert-status">
                  <div class="status-item">
                    <span class="status-label">证书状态：</span>
                    <el-tag :type="certificateInfoFilled.status === 'valid' ? 'success' : 'danger'">
                      {{ certificateInfoFilled.status === 'valid' ? '有效' : '无效' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-label">颁发机构：</span>
                    <span>{{ certificateInfoFilled.issuer }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">有效期至：</span>
                    <span>{{ certificateInfoFilled.expiryDate }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">剩余天数：</span>
                    <span :class="{ 'text-red-500': certificateInfoFilled.daysLeft < 30 }">
                      {{ certificateInfoFilled.daysLeft }} 天
                    </span>
                  </div>
                </div>
                
                <div class="cert-actions">
                  <el-button @click="handleUploadCert">上传证书</el-button>
                  <el-button @click="handleRenewCert">续期证书</el-button>
                  <el-button @click="handleDownloadCert">下载证书</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 审计日志 -->
        <el-tab-pane label="审计日志" name="audit">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">日志配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用审计日志</label>
                  <el-switch v-model="securityConfig.audit.enabled" />
                </div>
                <div class="config-item">
                  <label class="config-label">记录登录日志</label>
                  <el-switch v-model="securityConfig.audit.loginLog" />
                </div>
                <div class="config-item">
                  <label class="config-label">记录操作日志</label>
                  <el-switch v-model="securityConfig.audit.operationLog" />
                </div>
                <div class="config-item">
                  <label class="config-label">记录数据变更</label>
                  <el-switch v-model="securityConfig.audit.dataChangeLog" />
                </div>
                <div class="config-item">
                  <label class="config-label">日志保留期 (天)</label>
                  <el-input-number v-model="securityConfig.audit.retentionDays" :min="30" :max="1095" />
                </div>
                <div class="config-item">
                  <label class="config-label">日志级别</label>
                  <el-select v-model="securityConfig.audit.logLevel">
                    <el-option label="所有操作" value="all" />
                    <el-option label="重要操作" value="important" />
                    <el-option label="敏感操作" value="sensitive" />
                  </el-select>
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">最近安全事件</h3>
              <div class="security-events-table">
                <el-table :data="securityEvents" style="width: 100%">
                  <el-table-column prop="timestamp" label="时间" width="180" />
                  <el-table-column prop="type" label="事件类型" width="120" />
                  <el-table-column prop="severity" label="严重程度" width="100">
                    <template #default="scope">
                      <el-tag :type="getSeverityType(scope.row.severity)">
                        {{ scope.row.severity }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="user" label="用户" width="120" />
                  <el-table-column prop="ip" label="IP地址" width="140" />
                  <el-table-column prop="description" label="事件描述" />
                  <el-table-column prop="status" label="处理状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'handled' ? 'success' : 'warning'">
                        {{ scope.row.status === 'handled' ? '已处理' : '待处理' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Shield, Warning, User, Eye, Download, Save } from 'lucide-vue-next'

// 当前激活的安全配置标签
const activeSecurityTab = ref('login')

// 安全状态
const securityStatus = reactive({
  level: '高',
  score: 85,
  failedLogins: 12,
  blockedIps: 3,
  activeSessions: 24,
  maxSessions: 100,
  securityEvents: 8,
  newEvents: 2
})

// 安全配置
const securityConfig = reactive({
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    expiryDays: 90,
    historyCount: 5
  },
  login: {
    maxAttempts: 5,
    lockoutDuration: 30,
    enableCaptcha: true,
    captchaThreshold: 3,
    enable2FA: false,
    force2FA: false
  },
  ipControl: {
    mode: 'disabled', // disabled, whitelist, blacklist
    ipList: ['***********/24']
  },
  session: {
    timeout: 120,
    maxConcurrent: 3,
    rememberMe: true,
    rememberDuration: 7,
    allowMultiDevice: true,
    fixation: true
  },
  protection: {
    sqlInjection: true,
    xssProtection: true,
    csrfProtection: true,
    clickjacking: true,
    rateLimit: 100,
    fileUploadScan: true
  },
  intrusion: {
    enabled: true,
    abnormalLogin: true,
    bruteForce: true,
    abnormalOperation: true,
    sensitivity: 'medium',
    autoResponse: false
  },
  encryption: {
    dataTransmission: true,
    dataStorage: true,
    passwordAlgorithm: 'bcrypt',
    sessionToken: true
  },
  audit: {
    enabled: true,
    loginLog: true,
    operationLog: true,
    dataChangeLog: true,
    retentionDays: 365,
    logLevel: 'important'
  }
})

// 证书信息
const certificateInfoFilled = reactive({
  status: 'valid',
  issuer: 'Let\'s Encrypt',
  expiryDate: '2025-12-31',
  daysLeft: 161
})

// 活跃会话数据
const activeSessions = ref([
  {
    userId: 1001,
    username: 'admin',
    ip: '*************',
    location: '北京',
    device: 'Chrome 91.0 / Windows 10',
    loginTime: '2025-07-23 20:30:00',
    lastMonitor: '2025-07-23 21:15:00'
  },
  {
    userId: 1002,
    username: 'airline_demo',
    ip: '*************',
    location: '上海',
    device: 'Safari 14.1 / macOS',
    loginTime: '2025-07-23 19:45:00',
    lastMonitor: '2025-07-23 21:10:00'
  },
  {
    userId: 1003,
    username: 'supplier_001',
    ip: '*************',
    location: '广州',
    device: 'Firefox 89.0 / Ubuntu',
    loginTime: '2025-07-23 18:20:00',
    lastMonitor: '2025-07-23 21:05:00'
  }
])

// 安全事件数据
const securityEvents = ref([
  {
    timestamp: '2025-07-23 21:00:00',
    type: '异常登录',
    severity: '中',
    user: 'unknown',
    ip: '*************',
    description: '来自异常地理位置的登录尝试',
    status: 'pending'
  },
  {
    timestamp: '2025-07-23 20:45:00',
    type: '暴力破解',
    severity: '高',
    user: 'attacker',
    ip: '***********99',
    description: '短时间内多次密码错误尝试',
    status: 'handled'
  },
  {
    timestamp: '2025-07-23 20:30:00',
    type: '权限提升',
    severity: '高',
    user: 'user123',
    ip: '***********05',
    description: '尝试访问未授权的管理员功能',
    status: 'handled'
  }
])

// 组件挂载时加载数据
onMounted(() => {
  loadSecurityConfig()
})

// 加载安全配置
function loadSecurityConfig() {
  console.log('加载安全配置')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到安全配置标签:', tabName)
}

// 安全扫描
function handleSecurityScan() {
  ElMessage.info('正在执行安全扫描...')
  setTimeout(() => {
    ElMessage.success('安全扫描完成，未发现高危漏洞')
  }, 3000)
}

// 导出审计日志
function handleExportLogs() {
  ElMessage.info('正在导出审计日志...')
  setTimeout(() => {
    ElMessage.success('审计日志已导出')
  }, 2000)
}

// 保存配置
function handleSaveConfig() {
  console.log('保存安全配置:', securityConfig)
  ElMessage.success('安全配置已保存')
}

// 添加IP
function handleAddIp() {
  securityConfig.ipControl.ipList.push('')
}

// 删除IP
function handleRemoveIp(index) {
  securityConfig.ipControl.ipList.splice(index, 1)
}

// 踢出会话
function handleKillSession(session) {
  ElMessageBox.confirm(`确定要踢出用户 ${session.username} 的会话吗？`, '确认踢出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = activeSessions.value.findIndex(item => item.userId === session.userId)
    if (index !== -1) {
      activeSessions.value.splice(index, 1)
      ElMessage.success('用户会话已被踢出')
    }
  })
}

// 上传证书
function handleUploadCert() {
  ElMessage.info('上传SSL证书功能')
}

// 续期证书
function handleRenewCert() {
  ElMessage.info('续期SSL证书功能')
}

// 下载证书
function handleDownloadCert() {
  ElMessage.info('下载SSL证书功能')
}

// 获取严重程度类型
function getSeverityType(severity) {
  switch (severity) {
    case '高': return 'danger'
    case '中': return 'warning'
    case '低': return 'info'
    default: return 'info'
  }
}
</script>

<style scoped>
.security-config {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.security-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

.config-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.config-panel {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.complexity-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.ip-control {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.control-header {
  margin-bottom: 16px;
}

.ip-list-config {
  margin-top: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.list-header h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0;
}

.ip-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ip-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.sessions-table, .security-events-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.certificate-config {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.cert-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.cert-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .security-config {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .complexity-options {
    flex-direction: column;
    gap: 8px;
  }
  
  .cert-actions {
    flex-direction: column;
  }
}
</style>