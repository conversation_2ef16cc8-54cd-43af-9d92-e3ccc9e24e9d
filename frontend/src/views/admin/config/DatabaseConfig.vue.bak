<!--
数据库配置管理
版本: 1.0
创建时间: 2025-07-23

超级管理员专用的数据库配置管理界面
特点：
1. 数据库连接配置和测试
2. 数据库性能监控
3. 备份和恢复策略配置
4. 数据清理和维护设置
5. 连接池和优化参数
-->

<template>
  <div class="database-config">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">数据库配置</h1>
          <p class="page-subtitle">管理数据库连接配置，监控性能，配置备份策略</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleTestConnection">
            <Zap class="w-4 h-4 mr-2" />
            测试连接
          </el-button>
          <el-button @click="handleBackupNow">
            <Database class="w-4 h-4 mr-2" />
            立即备份
          </el-button>
          <el-button @click="handleSaveConfig" type="primary">
            <Save class="w-4 h-4 mr-2" />
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据库状态概览 -->
    <div class="db-status-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="status-card connection-status">
          <div class="card-icon" :class="dbStatus.connectionStatus === 'connected' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'">
            <Database class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ dbStatus.connectionStatus === 'connected' ? '已连接' : '未连接' }}</div>
            <div class="card-label">连接状态</div>
            <div class="card-detail">响应时间: {{ dbStatus.responseTime }}ms</div>
          </div>
        </div>
        
        <div class="status-card database-size">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Coin class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ dbStatus.databaseSize }}</div>
            <div class="card-label">数据库大小</div>
            <div class="card-detail">表数量: {{ dbStatus.tableCount }}</div>
          </div>
        </div>
        
        <div class="status-card active-connections">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Monitor class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ dbStatus.activeConnections }}</div>
            <div class="card-label">活跃连接数</div>
            <div class="card-detail">最大: {{ dbStatus.maxConnections }}</div>
          </div>
        </div>
        
        <div class="status-card last-backup">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Archive class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ dbStatus.lastBackupTime }}</div>
            <div class="card-label">最后备份</div>
            <div class="card-detail">大小: {{ dbStatus.backupSize }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content">
      <el-tabs v-model="activeDbTab" @tab-change="handleTabChange">
        
        <!-- 连接配置 -->
        <el-tab-pane label="连接配置" name="connection">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">主数据库配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">数据库类型</label>
                  <el-select v-model="dbConfig.connection.type">
                    <el-option label="PostgreSQL" value="postgresql" />
                    <el-option label="MySQL" value="mysql" />
                    <el-option label="SQLite" value="sqlite" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">主机地址</label>
                  <el-input v-model="dbConfig.connection.host" placeholder="localhost" />
                </div>
                <div class="config-item">
                  <label class="config-label">端口</label>
                  <el-input-number v-model="dbConfig.connection.port" :min="1" :max="65535" />
                </div>
                <div class="config-item">
                  <label class="config-label">数据库名</label>
                  <el-input v-model="dbConfig.connection.database" placeholder="cassdemo_dev" />
                </div>
                <div class="config-item">
                  <label class="config-label">用户名</label>
                  <el-input v-model="dbConfig.connection.username" placeholder="数据库用户名" />
                </div>
                <div class="config-item">
                  <label class="config-label">密码</label>
                  <el-input v-model="dbConfig.connection.password" type="password" placeholder="••••••••" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">连接池配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">最小连接数</label>
                  <el-input-number v-model="dbConfig.pool.minConnections" :min="1" :max="50" />
                </div>
                <div class="config-item">
                  <label class="config-label">最大连接数</label>
                  <el-input-number v-model="dbConfig.pool.maxConnections" :min="1" :max="200" />
                </div>
                <div class="config-item">
                  <label class="config-label">连接超时 (秒)</label>
                  <el-input-number v-model="dbConfig.pool.connectionTimeout" :min="5" :max="300" />
                </div>
                <div class="config-item">
                  <label class="config-label">空闲超时 (秒)</label>
                  <el-input-number v-model="dbConfig.pool.idleTimeout" :min="60" :max="3600" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 性能监控 -->
        <el-tab-pane label="性能监控" name="performance">
          <div class="config-panel">
            <div class="performance-metrics">
              <div class="metrics-grid">
                <div class="metric-card">
                  <h4>查询性能</h4>
                  <div class="metric-chart">
                    <div class="chart-placeholder">查询响应时间图表</div>
                  </div>
                  <div class="metric-stats">
                    <div class="stat-item">
                      <span class="stat-label">平均响应时间</span>
                      <span class="stat-value">{{ performanceMetrics.avgResponseTime }}ms</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">慢查询数量</span>
                      <span class="stat-value">{{ performanceMetrics.slowQueries }}</span>
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <h4>连接使用率</h4>
                  <div class="metric-chart">
                    <div class="chart-placeholder">连接使用率图表</div>
                  </div>
                  <div class="metric-stats">
                    <div class="stat-item">
                      <span class="stat-label">当前使用率</span>
                      <span class="stat-value">{{ performanceMetrics.connectionUsage }}%</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">峰值使用率</span>
                      <span class="stat-value">{{ performanceMetrics.peakUsage }}%</span>
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <h4>存储使用情况</h4>
                  <div class="metric-chart">
                    <div class="chart-placeholder">存储使用趋势图</div>
                  </div>
                  <div class="metric-stats">
                    <div class="stat-item">
                      <span class="stat-label">已使用空间</span>
                      <span class="stat-value">{{ performanceMetrics.usedStorage }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">可用空间</span>
                      <span class="stat-value">{{ performanceMetrics.freeStorage }}</span>
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <h4>事务统计</h4>
                  <div class="metric-chart">
                    <div class="chart-placeholder">事务处理量图表</div>
                  </div>
                  <div class="metric-stats">
                    <div class="stat-item">
                      <span class="stat-label">每秒事务数</span>
                      <span class="stat-value">{{ performanceMetrics.transactionsPerSecond }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">锁等待时间</span>
                      <span class="stat-value">{{ performanceMetrics.lockWaitTime }}ms</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 备份配置 -->
        <el-tab-pane label="备份配置" name="backup">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">自动备份设置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">启用自动备份</label>
                  <el-switch v-model="dbConfig.backup.autoBackup" />
                </div>
                <div class="config-item">
                  <label class="config-label">备份频率</label>
                  <el-select v-model="dbConfig.backup.frequency">
                    <el-option label="每小时" value="hourly" />
                    <el-option label="每天" value="daily" />
                    <el-option label="每周" value="weekly" />
                    <el-option label="每月" value="monthly" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">备份时间</label>
                  <el-time-picker v-model="dbConfig.backup.backupTime" format="HH:mm" />
                </div>
                <div class="config-item">
                  <label class="config-label">保留份数</label>
                  <el-input-number v-model="dbConfig.backup.retentionCount" :min="1" :max="100" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">备份存储配置</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">存储类型</label>
                  <el-select v-model="dbConfig.backup.storageType">
                    <el-option label="本地存储" value="local" />
                    <el-option label="阿里云OSS" value="aliyun-oss" />
                    <el-option label="腾讯云COS" value="tencent-cos" />
                    <el-option label="AWS S3" value="aws-s3" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">存储路径</label>
                  <el-input v-model="dbConfig.backup.storagePath" placeholder="/data/backups" />
                </div>
                <div class="config-item">
                  <label class="config-label">压缩备份</label>
                  <el-switch v-model="dbConfig.backup.compression" />
                </div>
                <div class="config-item">
                  <label class="config-label">加密备份</label>
                  <el-switch v-model="dbConfig.backup.encryption" />
                </div>
              </div>
            </div>

            <!-- 备份历史 -->
            <div class="config-section">
              <h3 class="section-title">备份历史记录</h3>
              <div class="backup-history">
                <el-table :data="backupHistory" style="width: 100%">
                  <el-table-column prop="id" label="备份ID" width="120" />
                  <el-table-column prop="createTime" label="创建时间" width="180" />
                  <el-table-column prop="size" label="文件大小" width="120" />
                  <el-table-column prop="type" label="备份类型" width="120" />
                  <el-table-column prop="status" label="状态" width="120">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                        {{ scope.row.status === 'success' ? '成功' : '失败' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button size="small" @click="handleDownloadBackup(scope.row)">下载</el-button>
                      <el-button size="small" @click="handleRestoreBackup(scope.row)">恢复</el-button>
                      <el-button size="small" type="danger" @click="handleDeleteBackup(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 维护设置 -->
        <el-tab-pane label="维护设置" name="maintenance">
          <div class="config-panel">
            <div class="config-section">
              <h3 class="section-title">数据清理规则</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">清理日志数据 (天)</label>
                  <el-input-number v-model="dbConfig.maintenance.logRetentionDays" :min="1" :max="365" />
                </div>
                <div class="config-item">
                  <label class="config-label">清理临时文件 (天)</label>
                  <el-input-number v-model="dbConfig.maintenance.tempFileRetentionDays" :min="1" :max="30" />
                </div>
                <div class="config-item">
                  <label class="config-label">清理废弃数据 (天)</label>
                  <el-input-number v-model="dbConfig.maintenance.deletedDataRetentionDays" :min="7" :max="180" />
                </div>
                <div class="config-item">
                  <label class="config-label">自动清理</label>
                  <el-switch v-model="dbConfig.maintenance.autoCleanup" />
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">索引维护</h3>
              <div class="config-grid">
                <div class="config-item">
                  <label class="config-label">自动重建索引</label>
                  <el-switch v-model="dbConfig.maintenance.autoReindex" />
                </div>
                <div class="config-item">
                  <label class="config-label">重建频率</label>
                  <el-select v-model="dbConfig.maintenance.reindexFrequency">
                    <el-option label="每周" value="weekly" />
                    <el-option label="每月" value="monthly" />
                    <el-option label="每季度" value="quarterly" />
                  </el-select>
                </div>
                <div class="config-item">
                  <label class="config-label">维护时间</label>
                  <el-time-picker v-model="dbConfig.maintenance.maintenanceTime" format="HH:mm" />
                </div>
                <div class="config-item">
                  <label class="config-label">分析统计信息</label>
                  <el-switch v-model="dbConfig.maintenance.analyzeStats" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Zap, Database, Coin, Monitor, Archive, Save } from 'lucide-vue-next'

// 当前激活的数据库配置标签
const activeDbTab = ref('connection')

// 数据库状态
const dbStatus = reactive({
  connectionStatus: 'connected',
  responseTime: 15,
  databaseSize: '2.5GB',
  tableCount: 45,
  activeConnections: 12,
  maxConnections: 100,
  lastBackupTime: '2小时前',
  backupSize: '2.1GB'
})

// 数据库配置
const dbConfig = reactive({
  connection: {
    type: 'postgresql',
    host: 'localhost',
    port: 5432,
    database: 'cassdemo_dev',
    username: 'cassdemo_user',
    password: ''
  },
  pool: {
    minConnections: 5,
    maxConnections: 50,
    connectionTimeout: 30,
    idleTimeout: 600
  },
  backup: {
    autoBackup: true,
    frequency: 'daily',
    backupTime: new Date(2000, 0, 1, 2, 0),
    retentionCount: 30,
    storageType: 'local',
    storagePath: '/data/backups',
    compression: true,
    encryption: false
  },
  maintenance: {
    logRetentionDays: 90,
    tempFileRetentionDays: 7,
    deletedDataRetentionDays: 30,
    autoCleanup: true,
    autoReindex: true,
    reindexFrequency: 'weekly',
    maintenanceTime: new Date(2000, 0, 1, 3, 0),
    analyzeStats: true
  }
})

// 性能指标
const performanceMetrics = reactive({
  avgResponseTime: 15,
  slowQueries: 3,
  connectionUsage: 24,
  peakUsage: 85,
  usedStorage: '2.5GB',
  freeStorage: '47.5GB',
  transactionsPerSecond: 125,
  lockWaitTime: 2
})

// 备份历史记录
const backupHistory = ref([
  {
    id: 'backup_20250723_020000',
    createTime: '2025-07-23 02:00:00',
    size: '2.1GB',
    type: '完整备份',
    status: 'success'
  },
  {
    id: 'backup_20250722_020000',
    createTime: '2025-07-22 02:00:00',
    size: '2.0GB',
    type: '完整备份',
    status: 'success'
  },
  {
    id: 'backup_20250721_020000',
    createTime: '2025-07-21 02:00:00',
    size: '1.9GB',
    type: '完整备份',
    status: 'success'
  }
])

// 组件挂载时加载数据
onMounted(() => {
  loadDbConfig()
  loadPerformanceMetrics()
})

// 加载数据库配置
function loadDbConfig() {
  console.log('加载数据库配置')
}

// 加载性能指标
function loadPerformanceMetrics() {
  console.log('加载性能指标')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到数据库配置标签:', tabName)
}

// 测试数据库连接
function handleTestConnection() {
  ElMessage.info('正在测试数据库连接...')
  setTimeout(() => {
    ElMessage.success('数据库连接测试成功')
  }, 2000)
}

// 立即备份
function handleBackupNow() {
  ElMessageBox.confirm('确定要立即执行数据库备份吗？', '确认备份', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('备份任务已启动')
  })
}

// 保存配置
function handleSaveConfig() {
  console.log('保存数据库配置:', dbConfig)
  ElMessage.success('数据库配置已保存')
}

// 下载备份文件
function handleDownloadBackup(backup) {
  ElMessage.info(`正在下载备份文件: ${backup.id}`)
}

// 恢复备份
function handleRestoreBackup(backup) {
  ElMessageBox.confirm(`确定要恢复到备份 ${backup.id} 吗？此操作将覆盖当前数据！`, '确认恢复', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('备份恢复任务已启动')
  })
}

// 删除备份
function handleDeleteBackup(backup) {
  ElMessageBox.confirm(`确定要删除备份 ${backup.id} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = backupHistory.value.findIndex(item => item.id === backup.id)
    if (index !== -1) {
      backupHistory.value.splice(index, 1)
      ElMessage.success('备份文件已删除')
    }
  })
}
</script>

<style scoped>
.database-config {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.db-status-overview {
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

.config-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.config-panel {
  padding: 24px;
}

.config-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.performance-metrics {
  padding: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.metric-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.metric-card h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.chart-placeholder {
  height: 120px;
  background: #e5e7eb;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

.metric-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.backup-history {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .database-config {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .config-grid, .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .status-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>