<!--
安全报告管理
版本: 1.0
创建时间: 2025-07-23

系统管理员专用的安全报告生成和管理界面
特点：
1. 安全事件统计和分析报告
2. 威胁检测和风险评估报告
3. 合规性检查和审计报告
4. 安全趋势分析和预警
5. 报告定制和自动化生成
-->

<template>
  <div class="security-reports">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">安全报告</h1>
          <p class="page-subtitle">生成和管理系统安全报告，监控安全状态，评估风险趋势</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleGenerateReport">
            <Document class="w-4 h-4 mr-2" />
            生成报告
          </el-button>
          <el-button @click="handleScheduleReport">
            <Clock class="w-4 h-4 mr-2" />
            定时报告
          </el-button>
          <el-button @click="handleExportReports" type="primary">
            <Download class="w-4 h-4 mr-2" />
            批量导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 安全指标概览 -->
    <div class="security-metrics">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="metric-card threat-level">
          <div class="card-header">
            <div class="card-icon bg-red-100 text-red-600">
              <Shield class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span class="trend-value" :class="{ 'trend-up': securityMetrics.threatLevel.trend > 0, 'trend-down': securityMetrics.threatLevel.trend < 0 }">
                {{ securityMetrics.threatLevel.trend > 0 ? '+' : '' }}{{ securityMetrics.threatLevel.trend }}%
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityMetrics.threatLevel.value }}</div>
            <div class="card-label">威胁等级</div>
            <div class="card-subtitle">本周检测到 {{ securityMetrics.threatLevel.detected }} 次威胁</div>
          </div>
        </div>
        
        <div class="metric-card vulnerability-count">
          <div class="card-header">
            <div class="card-icon bg-orange-100 text-orange-600">
              <Warning class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span class="trend-value trend-down">-{{ securityMetrics.vulnerabilities.fixed }}%</span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityMetrics.vulnerabilities.total }}</div>
            <div class="card-label">安全漏洞</div>
            <div class="card-subtitle">高危: {{ securityMetrics.vulnerabilities.critical }}, 中危: {{ securityMetrics.vulnerabilities.medium }}</div>
          </div>
        </div>
        
        <div class="metric-card security-score">
          <div class="card-header">
            <div class="card-icon bg-green-100 text-green-600">
              <Award class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span class="trend-value trend-up">+{{ securityMetrics.securityScore.improvement }}</span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityMetrics.securityScore.score }}/100</div>
            <div class="card-label">安全评分</div>
            <div class="card-subtitle">{{ securityMetrics.securityScore.level }}</div>
          </div>
        </div>
        
        <div class="metric-card incident-count">
          <div class="card-header">
            <div class="card-icon bg-blue-100 text-blue-600">
              <Eye class="w-8 h-8" />
            </div>
            <div class="card-trend">
              <span class="trend-value" :class="{ 'trend-up': securityMetrics.incidents.trend > 0, 'trend-down': securityMetrics.incidents.trend <= 0 }">
                {{ securityMetrics.incidents.trend > 0 ? '+' : '' }}{{ securityMetrics.incidents.trend }}%
              </span>
            </div>
          </div>
          <div class="card-content">
            <div class="card-value">{{ securityMetrics.incidents.total }}</div>
            <div class="card-label">安全事件</div>
            <div class="card-subtitle">已处理: {{ securityMetrics.incidents.resolved }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeReportTab" @tab-change="handleTabChange">
        
        <!-- 报告生成 -->
        <el-tab-pane label="报告生成" name="generate">
          <div class="report-panel">
            <div class="report-generator">
              <div class="generator-section">
                <h3 class="section-title">报告配置</h3>
                <div class="report-config">
                  <div class="config-row">
                    <div class="config-item">
                      <label class="config-label">报告类型</label>
                      <el-select v-model="reportConfig.type" placeholder="选择报告类型">
                        <el-option label="安全事件报告" value="security-events" />
                        <el-option label="威胁分析报告" value="threat-analysis" />
                        <el-option label="漏洞评估报告" value="vulnerability-assessment" />
                        <el-option label="合规性检查报告" value="compliance-check" />
                        <el-option label="综合安全报告" value="comprehensive" />
                      </el-select>
                    </div>
                    
                    <div class="config-item">
                      <label class="config-label">时间范围</label>
                      <el-select v-model="reportConfig.timeRange" placeholder="选择时间范围">
                        <el-option label="最近7天" value="7d" />
                        <el-option label="最近30天" value="30d" />
                        <el-option label="最近3个月" value="3m" />
                        <el-option label="最近1年" value="1y" />
                        <el-option label="自定义" value="custom" />
                      </el-select>
                    </div>
                  </div>
                  
                  <div v-if="reportConfig.timeRange === 'custom'" class="config-row">
                    <div class="config-item full-width">
                      <label class="config-label">自定义时间范围</label>
                      <el-date-picker
                        v-model="reportConfig.customDateRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        format="YYYY-MM-DD HH:mm"
                      />
                    </div>
                  </div>
                  
                  <div class="config-row">
                    <div class="config-item">
                      <label class="config-label">包含内容</label>
                      <el-checkbox-group v-model="reportConfig.sections">
                        <el-checkbox value="summary">执行摘要</el-checkbox>
                        <el-checkbox value="charts">图表分析</el-checkbox>
                        <el-checkbox value="details">详细数据</el-checkbox>
                        <el-checkbox value="recommendations">安全建议</el-checkbox>
                      </el-checkbox-group>
                    </div>
                    
                    <div class="config-item">
                      <label class="config-label">输出格式</label>
                      <el-radio-group v-model="reportConfig.format">
                        <el-radio value="pdf">PDF</el-radio>
                        <el-radio value="excel">Excel</el-radio>
                        <el-radio value="html">HTML</el-radio>
                      </el-radio-group>
                    </div>
                  </div>
                  
                  <div class="config-row">
                    <div class="config-item">
                      <label class="config-label">报告级别</label>
                      <el-select v-model="reportConfig.level" placeholder="选择报告级别">
                        <el-option label="高级管理层" value="executive" />
                        <el-option label="技术团队" value="technical" />
                        <el-option label="合规审计" value="compliance" />
                      </el-select>
                    </div>
                    
                    <div class="config-item">
                      <label class="config-label">保密等级</label>
                      <el-select v-model="reportConfig.confidentiality" placeholder="选择保密等级">
                        <el-option label="公开" value="public" />
                        <el-option label="内部" value="internal" />
                        <el-option label="机密" value="confidential" />
                        <el-option label="绝密" value="secret" />
                      </el-select>
                    </div>
                  </div>
                </div>
                
                <div class="generator-actions">
                  <el-button @click="handlePreviewReport">预览报告</el-button>
                  <el-button @click="handleGenerateReport" type="primary" :loading="reportGenerating">
                    {{ reportGenerating ? '生成中...' : '生成报告' }}
                  </el-button>
                </div>
              </div>

              <!-- 报告预览 -->
              <div v-if="showPreview" class="report-preview">
                <h3 class="section-title">报告预览</h3>
                <div class="preview-content">
                  <div class="preview-header">
                    <h2>{{ getReportTitle(reportConfig.type) }}</h2>
                    <div class="preview-meta">
                      <span>生成时间: {{ new Date().toLocaleString() }}</span>
                      <span>报告级别: {{ getReportLevelName(reportConfig.level) }}</span>
                      <span>保密等级: {{ getConfidentialityName(reportConfig.confidentiality) }}</span>
                    </div>
                  </div>
                  
                  <div class="preview-sections">
                    <div v-if="reportConfig.sections.includes('summary')" class="preview-section">
                      <h3>执行摘要</h3>
                      <p>本报告分析了{{ getTimeRangeName(reportConfig.timeRange) }}的系统安全状况...</p>
                    </div>
                    
                    <div v-if="reportConfig.sections.includes('charts')" class="preview-section">
                      <h3>图表分析</h3>
                      <div class="chart-placeholder">
                        [安全事件趋势图]
                      </div>
                    </div>
                    
                    <div v-if="reportConfig.sections.includes('details')" class="preview-section">
                      <h3>详细数据</h3>
                      <div class="details-placeholder">
                        [详细数据表格]
                      </div>
                    </div>
                    
                    <div v-if="reportConfig.sections.includes('recommendations')" class="preview-section">
                      <h3>安全建议</h3>
                      <ul>
                        <li>建议加强密码策略配置</li>
                        <li>建议更新系统安全补丁</li>
                        <li>建议定期进行安全培训</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 报告历史 -->
        <el-tab-pane label="报告历史" name="history">
          <div class="report-panel">
            <div class="history-controls">
              <div class="control-section">
                <h3 class="section-title">报告历史记录</h3>
                <div class="history-filters">
                  <el-input 
                    v-model="historySearch" 
                    placeholder="搜索报告..." 
                    size="small"
                    style="width: 200px;"
                  >
                    <template #prefix>
                      <Search class="w-4 h-4" />
                    </template>
                  </el-input>
                  <el-select v-model="historyTypeFilter" placeholder="报告类型" size="small" style="width: 150px;">
                    <el-option label="全部类型" value="all" />
                    <el-option label="安全事件" value="security-events" />
                    <el-option label="威胁分析" value="threat-analysis" />
                    <el-option label="漏洞评估" value="vulnerability-assessment" />
                    <el-option label="合规检查" value="compliance-check" />
                  </el-select>
                  <el-date-picker
                    v-model="historyDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                  />
                </div>
              </div>
            </div>
            
            <div class="history-table">
              <el-table :data="filteredReports" style="width: 100%">
                <el-table-column prop="id" label="报告ID" width="150" />
                <el-table-column prop="title" label="报告标题" width="200" />
                <el-table-column prop="type" label="类型" width="120">
                  <template #default="scope">
                    <el-tag :type="getReportTypeColor(scope.row.type)" size="small">
                      {{ getReportTypeName(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="creator" label="创建人" width="100" />
                <el-table-column prop="createTime" label="创建时间" width="180" />
                <el-table-column prop="format" label="格式" width="80" />
                <el-table-column prop="size" label="大小" width="100" />
                <el-table-column prop="downloads" label="下载次数" width="100" />
                <el-table-column label="操作" width="200">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewReport(scope.row)">查看</el-button>
                    <el-button size="small" @click="handleDownloadReport(scope.row)">下载</el-button>
                    <el-button size="small" @click="handleShareReport(scope.row)">分享</el-button>
                    <el-button size="small" type="danger" @click="handleDeleteReport(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="table-pagination">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalReports"
                  layout="total, sizes, prev, pager, next, jumper"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 定时任务 -->
        <el-tab-pane label="定时任务" name="schedule">
          <div class="report-panel">
            <div class="schedule-controls">
              <div class="control-section">
                <h3 class="section-title">定时报告任务</h3>
                <el-button @click="handleCreateSchedule" type="primary">创建定时任务</el-button>
              </div>
            </div>
            
            <div class="schedule-list">
              <div class="schedule-cards">
                <div v-for="schedule in reportSchedules" :key="schedule.id" class="schedule-card">
                  <div class="card-header">
                    <div class="schedule-info">
                      <h4>{{ schedule.name }}</h4>
                      <p>{{ getReportTypeName(schedule.reportType) }}</p>
                    </div>
                    <div class="schedule-status">
                      <el-switch 
                        v-model="schedule.enabled" 
                        @change="handleToggleSchedule(schedule)"
                      />
                    </div>
                  </div>
                  
                  <div class="card-content">
                    <div class="schedule-details">
                      <div class="detail-item">
                        <span class="detail-label">执行频率:</span>
                        <span>{{ getFrequencyName(schedule.frequency) }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">下次执行:</span>
                        <span>{{ schedule.nextRun }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">接收人:</span>
                        <span>{{ schedule.recipients.join(', ') }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">最后执行:</span>
                        <span :class="{ 'text-green-500': schedule.lastStatus === 'success', 'text-red-500': schedule.lastStatus === 'failed' }">
                          {{ schedule.lastRun }} ({{ schedule.lastStatus === 'success' ? '成功' : '失败' }})
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="card-actions">
                    <el-button size="small" @click="handleEditSchedule(schedule)">编辑</el-button>
                    <el-button size="small" @click="handleRunScheduleNow(schedule)">立即执行</el-button>
                    <el-button size="small" @click="handleViewScheduleLogs(schedule)">日志</el-button>
                    <el-button size="small" type="danger" @click="handleDeleteSchedule(schedule)">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 模板管理 -->
        <el-tab-pane label="模板管理" name="templates">
          <div class="report-panel">
            <div class="template-controls">
              <div class="control-section">
                <h3 class="section-title">报告模板</h3>
                <el-button @click="handleCreateTemplate" type="primary">创建模板</el-button>
              </div>
            </div>
            
            <div class="template-list">
              <div class="template-grid">
                <div v-for="template in reportTemplates" :key="template.id" class="template-card">
                  <div class="template-preview">
                    <div class="preview-image">
                      <Document class="w-12 h-12 text-gray-400" />
                    </div>
                  </div>
                  
                  <div class="template-info">
                    <h4>{{ template.name }}</h4>
                    <p>{{ template.description }}</p>
                    <div class="template-meta">
                      <span class="meta-item">{{ getReportTypeName(template.type) }}</span>
                      <span class="meta-item">{{ template.usage }} 次使用</span>
                    </div>
                  </div>
                  
                  <div class="template-actions">
                    <el-button size="small" @click="handleUseTemplate(template)">使用</el-button>
                    <el-button size="small" @click="handleEditTemplate(template)">编辑</el-button>
                    <el-button size="small" @click="handleCopyTemplate(template)">复制</el-button>
                    <el-button size="small" type="danger" @click="handleDeleteTemplate(template)">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入

// 当前激活的报告标签
const activeReportTab = ref('generate')

// 安全指标数据
const securityMetrics = reactive({
  threatLevel: {
    value: '中',
    trend: -5,
    detected: 12
  },
  vulnerabilities: {
    total: 23,
    critical: 3,
    medium: 15,
    fixed: 12
  },
  securityScore: {
    score: 85,
    level: '良好',
    improvement: 8
  },
  incidents: {
    total: 45,
    resolved: 38,
    trend: -15
  }
})

// 报告配置
const reportConfig = reactive({
  type: 'comprehensive',
  timeRange: '30d',
  customDateRange: [],
  sections: ['summary', 'charts', 'details', 'recommendations'],
  format: 'pdf',
  level: 'technical',
  confidentiality: 'internal'
})

// 报告生成状态
const reportGenerating = ref(false)
const showPreview = ref(false)

// 历史记录筛选
const historySearch = ref('')
const historyTypeFilter = ref('all')
const historyDateRange = ref([])

// 分页设置
const currentPage = ref(1)
const pageSize = ref(20)
const totalReports = ref(150)

// 报告历史数据
const reportHistory = ref([
  {
    id: 'RPT_20250723_001',
    title: '7月安全事件综合报告',
    type: 'comprehensive',
    creator: 'admin',
    createTime: '2025-07-23 14:30:00',
    format: 'PDF',
    size: '2.5MB',
    downloads: 8
  },
  {
    id: 'RPT_20250722_001',
    title: '威胁分析月度报告',
    type: 'threat-analysis',
    creator: 'security_admin',
    createTime: '2025-07-22 10:15:00',
    format: 'Excel',
    size: '1.8MB',
    downloads: 12
  },
  {
    id: 'RPT_20250721_001',
    title: '漏洞评估报告',
    type: 'vulnerability-assessment',
    creator: 'admin',
    createTime: '2025-07-21 16:45:00',
    format: 'HTML',
    size: '3.2MB',
    downloads: 5
  }
])

// 定时任务数据
const reportSchedules = ref([
  {
    id: 'SCH_001',
    name: '每周安全事件报告',
    reportType: 'security-events',
    frequency: 'weekly',
    enabled: true,
    nextRun: '2025-07-28 09:00:00',
    lastRun: '2025-07-21 09:00:00',
    lastStatus: 'success',
    recipients: ['<EMAIL>', '<EMAIL>']
  },
  {
    id: 'SCH_002',
    name: '月度综合安全报告',
    reportType: 'comprehensive',
    frequency: 'monthly',
    enabled: true,
    nextRun: '2025-08-01 08:00:00',
    lastRun: '2025-07-01 08:00:00',
    lastStatus: 'success',
    recipients: ['<EMAIL>', '<EMAIL>']
  }
])

// 报告模板数据
const reportTemplates = ref([
  {
    id: 'TPL_001',
    name: '标准安全事件报告模板',
    type: 'security-events',
    description: '包含安全事件统计、分析和建议的标准模板',
    usage: 25
  },
  {
    id: 'TPL_002',
    name: '高管层威胁分析模板',
    type: 'threat-analysis',
    description: '面向高级管理层的威胁分析报告模板',
    usage: 18
  },
  {
    id: 'TPL_003',
    name: '合规性检查报告模板',
    type: 'compliance-check',
    description: '符合行业标准的合规性检查报告模板',
    usage: 12
  }
])

// 计算属性：过滤后的报告列表
const filteredReports = computed(() => {
  let filtered = reportHistory.value
  
  if (historySearch.value) {
    filtered = filtered.filter(report => 
      report.title.includes(historySearch.value) || 
      report.id.includes(historySearch.value)
    )
  }
  
  if (historyTypeFilter.value !== 'all') {
    filtered = filtered.filter(report => report.type === historyTypeFilter.value)
  }
  
  return filtered
})

// 组件挂载时加载数据
onMounted(() => {
  loadSecurityReports()
})

// 加载安全报告数据
function loadSecurityReports() {
  console.log('加载安全报告数据')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到安全报告标签:', tabName)
}

// 生成报告
function handleGenerateReport() {
  reportGenerating.value = true
  setTimeout(() => {
    reportGenerating.value = false
    ElMessage.success('报告生成成功')
  }, 3000)
}

// 预览报告
function handlePreviewReport() {
  showPreview.value = true
  ElMessage.info('正在生成报告预览...')
}

// 定时报告
function handleScheduleReport() {
  activeReportTab.value = 'schedule'
}

// 批量导出
function handleExportReports() {
  ElMessage.info('正在导出选中的报告...')
}

// 查看报告
function handleViewReport(report) {
  ElMessage.info(`查看报告: ${report.title}`)
}

// 下载报告
function handleDownloadReport(report) {
  ElMessage.info(`正在下载报告: ${report.title}`)
}

// 分享报告
function handleShareReport(report) {
  ElMessage.info(`分享报告: ${report.title}`)
}

// 删除报告
function handleDeleteReport(report) {
  ElMessageBox.confirm(`确定要删除报告 ${report.title} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = reportHistory.value.findIndex(item => item.id === report.id)
    if (index !== -1) {
      reportHistory.value.splice(index, 1)
      ElMessage.success('报告已删除')
    }
  })
}

// 创建定时任务
function handleCreateSchedule() {
  ElMessage.info('创建定时报告任务')
}

// 编辑定时任务
function handleEditSchedule(schedule) {
  ElMessage.info(`编辑定时任务: ${schedule.name}`)
}

// 切换定时任务状态
function handleToggleSchedule(schedule) {
  ElMessage.info(`${schedule.enabled ? '启用' : '禁用'}定时任务: ${schedule.name}`)
}

// 立即执行定时任务
function handleRunScheduleNow(schedule) {
  ElMessage.info(`正在执行定时任务: ${schedule.name}`)
}

// 查看定时任务日志
function handleViewScheduleLogs(schedule) {
  ElMessage.info(`查看定时任务日志: ${schedule.name}`)
}

// 删除定时任务
function handleDeleteSchedule(schedule) {
  ElMessageBox.confirm(`确定要删除定时任务 ${schedule.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = reportSchedules.value.findIndex(item => item.id === schedule.id)
    if (index !== -1) {
      reportSchedules.value.splice(index, 1)
      ElMessage.success('定时任务已删除')
    }
  })
}

// 创建模板
function handleCreateTemplate() {
  ElMessage.info('创建报告模板')
}

// 使用模板
function handleUseTemplate(template) {
  reportConfig.type = template.type
  activeReportTab.value = 'generate'
  ElMessage.success(`已应用模板: ${template.name}`)
}

// 编辑模板
function handleEditTemplate(template) {
  ElMessage.info(`编辑模板: ${template.name}`)
}

// 复制模板
function handleCopyTemplate(template) {
  ElMessage.info(`复制模板: ${template.name}`)
}

// 删除模板
function handleDeleteTemplate(template) {
  ElMessageBox.confirm(`确定要删除模板 ${template.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = reportTemplates.value.findIndex(item => item.id === template.id)
    if (index !== -1) {
      reportTemplates.value.splice(index, 1)
      ElMessage.success('模板已删除')
    }
  })
}

// 辅助函数
function getReportTitle(type) {
  const titles = {
    'security-events': '安全事件报告',
    'threat-analysis': '威胁分析报告',
    'vulnerability-assessment': '漏洞评估报告',
    'compliance-check': '合规性检查报告',
    'comprehensive': '综合安全报告'
  }
  return titles[type] || '安全报告'
}

function getReportTypeName(type) {
  const names = {
    'security-events': '安全事件',
    'threat-analysis': '威胁分析',
    'vulnerability-assessment': '漏洞评估',
    'compliance-check': '合规检查',
    'comprehensive': '综合报告'
  }
  return names[type] || type
}

function getReportTypeColor(type) {
  const colors = {
    'security-events': 'danger',
    'threat-analysis': 'warning',
    'vulnerability-assessment': 'info',
    'compliance-check': 'success',
    'comprehensive': 'primary'
  }
  return colors[type] || 'info'
}

function getReportLevelName(level) {
  const names = {
    'executive': '高级管理层',
    'technical': '技术团队',
    'compliance': '合规审计'
  }
  return names[level] || level
}

function getConfidentialityName(confidentiality) {
  const names = {
    'public': '公开',
    'internal': '内部',
    'confidential': '机密',
    'secret': '绝密'
  }
  return names[confidentiality] || confidentiality
}

function getTimeRangeName(timeRange) {
  const names = {
    '7d': '最近7天',
    '30d': '最近30天',
    '3m': '最近3个月',
    '1y': '最近1年',
    'custom': '自定义时间'
  }
  return names[timeRange] || timeRange
}

function getFrequencyName(frequency) {
  const names = {
    'daily': '每天',
    'weekly': '每周',
    'monthly': '每月',
    'quarterly': '每季度'
  }
  return names[frequency] || frequency
}
</script>

<style scoped>
.security-reports {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.security-metrics {
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-trend {
  font-size: 12px;
}

.trend-value {
  font-weight: 600;
}

.trend-up {
  color: #ef4444;
}

.trend-down {
  color: #10b981;
}

.card-content {
  margin-top: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-panel {
  padding: 24px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.report-generator {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-config {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item.full-width {
  grid-column: 1 / -1;
}

.config-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.generator-actions {
  display: flex;
  gap: 12px;
}

.report-preview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.preview-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-header {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.preview-header h2 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.preview-meta {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #6b7280;
}

.preview-section {
  margin-bottom: 24px;
}

.preview-section h3 {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.chart-placeholder, .details-placeholder {
  height: 200px;
  background: #e5e7eb;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

.history-controls {
  margin-bottom: 24px;
}

.history-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.history-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.schedule-controls {
  margin-bottom: 24px;
}

.schedule-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.schedule-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.schedule-info h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.schedule-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.detail-label {
  font-weight: 500;
  color: #374151;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.template-controls {
  margin-bottom: 24px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.template-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.template-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background: white;
  border-radius: 4px;
}

.template-info h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.template-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.template-meta {
  display: flex;
  gap: 12px;
}

.meta-item {
  font-size: 12px;
  color: #9ca3af;
}

.template-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .security-reports {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .config-row {
    grid-template-columns: 1fr;
  }
  
  .history-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .schedule-cards, .template-grid {
    grid-template-columns: 1fr;
  }
}
</style>