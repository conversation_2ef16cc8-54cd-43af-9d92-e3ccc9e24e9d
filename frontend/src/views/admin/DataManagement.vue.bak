<!--
数据管理页面
版本: 1.0
创建时间: 2025-07-23

数据备份、恢复和清理的专门页面
特点：
1. 数据库备份和恢复功能
2. 数据清理和优化工具
3. 数据导入导出管理
4. 数据完整性检查
5. 存储使用统计分析
-->

<template>
  <div class="data-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">数据管理中心</h1>
          <p class="page-subtitle">数据备份恢复、清理优化和完整性管理</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshStatus">
            <Refresh class="w-4 h-4 mr-2" />
            刷新状态
          </el-button>
          <el-button @click="handleDataIntegrityCheck">
            <CircleCheck class="w-4 h-4 mr-2" />
            完整性检查
          </el-button>
          <el-button type="primary" @click="handleCreateBackup">
            <Database class="w-4 h-4 mr-2" />
            立即备份
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据状态概览 -->
    <div class="data-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="status-card">
          <div class="status-icon bg-blue-100 text-blue-600">
            <Database class="w-8 h-8" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ dataStats.totalSize }}</div>
            <div class="status-label">数据库大小</div>
            <div class="status-detail">{{ dataStats.totalGrids }} 个表</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon bg-green-100 text-green-600">
            <Archive class="w-8 h-8" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ dataStats.backupCount }}</div>
            <div class="status-label">备份文件</div>
            <div class="status-detail">最新: {{ dataStats.lastBackup }}</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon bg-orange-100 text-orange-600">
            <Money class="w-8 h-8" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ dataStats.storageUsed }}</div>
            <div class="status-label">存储使用</div>
            <div class="status-detail">{{ dataStats.storagePercent }}% 使用率</div>
          </div>
        </div>
        
        <div class="status-card">
          <div class="status-icon bg-purple-100 text-purple-600">
            <Monitor class="w-8 h-8" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ dataStats.integrityScore }}%</div>
            <div class="status-label">完整性评分</div>
            <div class="status-detail">{{ dataStats.integrityStatus }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 数据备份 ---->
        <el-tab-pane label="数据备份" name="backup">
          <div class="backup-panel">
            <!-- 备份策略配置 -->
            <div class="backup-config">
              <div class="config-header">
                <h3 class="config-title">备份策略配置</h3>
                <el-button @click="handleSaveBackupConfig" type="primary" size="small">
                  保存配置
                </el-button>
              </div>
              
              <div class="config-form">
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">自动备份频率</label>
                    <el-select v-model="backupConfig.frequency" placeholder="选择备份频率">
                      <el-option label="每日备份" value="daily" />
                      <el-option label="每周备份" value="weekly" />
                      <el-option label="每月备份" value="monthly" />
                      <el-option label="手动备份" value="manual" />
                    </el-select>
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">备份时间</label>
                    <el-time-picker
                      v-model="backupConfig.time"
                      format="HH:mm"
                      placeholder="选择备份时间"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">保留份数</label>
                    <el-input-number
                      v-model="backupConfig.keepCount"
                      :min="1"
                      :max="100"
                      placeholder="保留备份份数"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">压缩备份</label>
                    <el-switch v-model="backupConfig.compress" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速备份操作 -->
            <div class="quick-backup">
              <div class="quick-header">
                <h3 class="quick-title">快速备份操作</h3>
                <div class="backup-progress" v-if="backupInProgress">
                  <el-progress
                    :percentage="backupProgress"
                    :stroke-width="8"
                    :show-text="true"
                  />
                  <span class="progress-text">正在备份数据库...</span>
                </div>
              </div>
              
              <div class="backup-types">
                <div class="backup-type-card" @click="handleFullBackup">
                  <div class="type-icon">
                    <Database class="w-12 h-12 text-blue-600" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">完整备份</h4>
                    <p class="type-description">备份整个数据库，包含所有表和数据</p>
                    <div class="type-stats">
                      <span class="stat-item">预计时间: {{ backupEstimates.full.time }}</span>
                      <span class="stat-item">文件大小: {{ backupEstimates.full.size }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="backup-type-card" @click="handleIncrementalBackup">
                  <div class="type-icon">
                    <Archive class="w-12 h-12 text-green-600" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">增量备份</h4>
                    <p class="type-description">仅备份自上次备份后的变更数据</p>
                    <div class="type-stats">
                      <span class="stat-item">预计时间: {{ backupEstimates.incremental.time }}</span>
                      <span class="stat-item">文件大小: {{ backupEstimates.incremental.size }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="backup-type-card" @click="handleCustomBackup">
                  <div class="type-icon">
                    <Settings class="w-12 h-12 text-orange-600" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">自定义备份</h4>
                    <p class="type-description">选择特定表或数据范围进行备份</p>
                    <div class="type-stats">
                      <span class="stat-item">自定义配置</span>
                      <span class="stat-item">灵活选择</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 备份历史列表 -->
            <div class="backup-history">
              <div class="history-header">
                <h3 class="history-title">备份历史</h3>
                <div class="history-actions">
                  <el-button @click="handleRefreshBackups" size="small">
                    <Refresh class="w-4 h-4 mr-1" />
                    刷新
                  </el-button>
                  <el-button @click="handleCleanupBackups" size="small">
                    <Trash2 class="w-4 h-4 mr-1" />
                    清理过期
                  </el-button>
                </div>
              </div>
              
              <div class="backup-list">
                <el-table :data="backupList" stripe>
                  <el-table-column prop="name" label="备份名称" width="200">
                    <template #default="{ row }">
                      <div class="backup-name">
                        <component :is="getBackupIcon(row.type)" class="w-4 h-4 mr-2" />
                        {{ row.name }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="备份类型" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getBackupTypeTagType(row.type)">
                        {{ getBackupTypeText(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="size" label="文件大小" width="120" />
                  <el-table-column prop="created_at" label="创建时间" width="180" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getStatusTagType(row.status)">
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                      <el-button @click="handleRestoreBackup(row)" size="small" type="primary">
                        恢复
                      </el-button>
                      <el-button @click="handleDownloadBackup(row)" size="small">
                        下载
                      </el-button>
                      <el-button @click="handleDeleteBackup(row)" size="small" type="danger">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据恢复 -->
        <el-tab-pane label="数据恢复" name="restore">
          <div class="restore-panel">
            <!-- 恢复方式选择 -->
            <div class="restore-options">
              <h3 class="options-title">选择恢复方式</h3>
              
              <div class="restore-types">
                <div class="restore-type-card" :class="{ active: restoreMode === 'file' }" @click="setRestoreMode('file')">
                  <div class="type-icon">
                    <Upload class="w-8 h-8" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">从备份文件恢复</h4>
                    <p class="type-description">从本地或历史备份文件恢复数据</p>
                  </div>
                </div>
                
                <div class="restore-type-card" :class="{ active: restoreMode === 'point' }" @click="setRestoreMode('point')">
                  <div class="type-icon">
                    <Clock class="w-8 h-8" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">时间点恢复</h4>
                    <p class="type-description">恢复到指定的时间点状态</p>
                  </div>
                </div>
                
                <div class="restore-type-card" :class="{ active: restoreMode === 'selective' }" @click="setRestoreMode('selective')">
                  <div class="type-icon">
                    <Filter class="w-8 h-8" />
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">选择性恢复</h4>
                    <p class="type-description">仅恢复特定表或数据范围</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 恢复配置 -->
            <div class="restore-config" v-if="restoreMode">
              <!-- 从文件恢复 -->
              <div v-if="restoreMode === 'file'" class="restore-from-file">
                <h4 class="config-subtitle">从备份文件恢复</h4>
                
                <div class="file-selection">
                  <div class="selection-tabs">
                    <el-tabs v-model="fileSourceTab">
                      <el-tab-pane label="历史备份" name="history">
                        <div class="backup-selection">
                          <el-table 
                            :data="backupList" 
                            @selection-change="handleBackupSelectionChange"
                            row-key="id"
                          >
                            <el-table-column type="selection" width="55" />
                            <el-table-column prop="name" label="备份名称" />
                            <el-table-column prop="created_at" label="创建时间" />
                            <el-table-column prop="size" label="文件大小" />
                            <el-table-column prop="type" label="类型" />
                          </el-table>
                        </div>
                      </el-tab-pane>
                      
                      <el-tab-pane label="上传文件" name="upload">
                        <div class="file-upload">
                          <el-upload
                            drag
                            :before-upload="handleBeforeUpload"
                            :on-success="handleUploadSuccess"
                            accept=".sql,.gz,.zip"
                            action="/api/admin/upload-backup"
                          >
                            <el-icon class="el-icon--upload"><Upload /></el-icon>
                            <div class="el-upload__text">
                              将备份文件拖拽到此处，或<em>点击上传</em>
                            </div>
                            <div class="el-upload__tip">
                              支持 .sql, .gz, .zip 格式，文件大小不超过 500MB
                            </div>
                          </el-upload>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>
                
                <div class="restore-options-config">
                  <div class="option-group">
                    <el-checkbox v-model="restoreOptions.dropExisting">
                      删除现有数据（危险操作）
                    </el-checkbox>
                    <el-checkbox v-model="restoreOptions.preserveUsers">
                      保留用户数据
                    </el-checkbox>
                    <el-checkbox v-model="restoreOptions.createBackupBeforeRestore">
                      恢复前自动备份
                    </el-checkbox>
                  </div>
                </div>
              </div>
              
              <!-- 时间点恢复 -->
              <div v-if="restoreMode === 'point'" class="restore-point-in-time">
                <h4 class="config-subtitle">时间点恢复</h4>
                
                <div class="time-selection">
                  <div class="time-picker-group">
                    <label class="time-label">恢复到此时间点：</label>
                    <el-date-picker
                      v-model="restoreTimePoint"
                      type="datetime"
                      placeholder="选择恢复时间点"
                      :disabled-date="disabledDate"
                      :disabled-hours="disabledHours"
                    />
                  </div>
                  
                  <div class="time-validation" v-if="restoreTimePoint">
                    <div class="validation-item">
                      <CircleCheck class="w-4 h-4 text-green-600" />
                      <span>时间点有效，可以进行恢复</span>
                    </div>
                    <div class="time-info">
                      <p>预计恢复时间: {{ estimatedRestoreTime }}</p>
                      <p>影响数据范围: {{ affectedDataRange }}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 选择性恢复 -->
              <div v-if="restoreMode === 'selective'" class="restore-selective">
                <h4 class="config-subtitle">选择性恢复</h4>
                
                <div class="table-selection">
                  <div class="selection-header">
                    <h5>选择要恢复的数据表：</h5>
                    <div class="selection-actions">
                      <el-button @click="selectAllGrids" size="small">全选</el-button>
                      <el-button @click="clearGridSelection" size="small">清空</el-button>
                    </div>
                  </div>
                  
                  <div class="table-list">
                    <el-checkbox-group v-model="selectedGrids">
                      <div class="table-category" v-for="category in tableCategories" :key="category.name">
                        <div class="category-header">
                          <h6 class="category-title">{{ category.label }}</h6>
                          <span class="category-count">({{ category.tables.length }} 个表)</span>
                        </div>
                        <div class="category-tables">
                          <el-checkbox 
                            v-for="table in category.tables" 
                            :key="table.name"
                            :label="table.name"
                            class="table-checkbox"
                          >
                            <div class="table-info">
                              <span class="table-name">{{ table.label }}</span>
                              <span class="table-count">{{ table.rowCount }} 条记录</span>
                            </div>
                          </el-checkbox>
                        </div>
                      </div>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>

              <!-- 恢复执行 -->
              <div class="restore-execution">
                <div class="execution-header">
                  <h4 class="execution-title">恢复执行</h4>
                  <div class="risk-warning" v-if="hasHighRisk">
                    <Warning class="w-4 h-4 text-red-600" />
                    <span class="warning-text">高风险操作，建议在维护时间窗口执行</span>
                  </div>
                </div>
                
                <div class="pre-restore-check">
                  <h5 class="check-title">恢复前检查：</h5>
                  <div class="check-list">
                    <div class="check-item" v-for="check in preRestoreChecks" :key="check.id">
                      <component :is="check.passed ? CircleCheck : Warning" 
                        :class="check.passed ? 'text-green-600' : 'text-red-600'" 
                        class="w-4 h-4" 
                      />
                      <span :class="check.passed ? 'text-green-800' : 'text-red-800'">
                        {{ check.message }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="execution-actions">
                  <el-button 
                    @click="handleStartRestore" 
                    type="primary" 
                    :disabled="!canStartRestore"
                    :loading="restoreInProgress"
                    size="large"
                  >
                    <Play class="w-4 h-4 mr-2" />
                    开始恢复
                  </el-button>
                  
                  <el-button @click="handleDryRun" :disabled="restoreInProgress">
                    <TestTube class="w-4 h-4 mr-2" />
                    模拟执行
                  </el-button>
                </div>
                
                <!-- 恢复进度 -->
                <div class="restore-progress" v-if="restoreInProgress">
                  <div class="progress-header">
                    <h5 class="progress-title">恢复进度</h5>
                    <div class="progress-actions">
                      <el-button @click="handlePauseRestore" size="small">
                        暂停
                      </el-button>
                      <el-button @click="handleCancelRestore" type="danger" size="small">
                        取消
                      </el-button>
                    </div>
                  </div>
                  
                  <div class="progress-content">
                    <el-progress
                      :percentage="restoreProgress.percentage"
                      :stroke-width="12"
                      :show-text="true"
                      :status="restoreProgress.status"
                    />
                    <div class="progress-details">
                      <p class="current-step">{{ restoreProgress.currentStep }}</p>
                      <p class="progress-stats">
                        已处理: {{ restoreProgress.processedItems }} / {{ restoreProgress.totalItems }}
                      </p>
                      <p class="estimated-time">预计剩余时间: {{ restoreProgress.estimatedTimeLeft }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据清理 -->
        <el-tab-pane label="数据清理" name="cleanup">
          <div class="cleanup-panel">
            <!-- 清理概览 -->
            <div class="cleanup-overview">
              <h3 class="overview-title">数据清理概览</h3>
              
              <div class="cleanup-stats">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="cleanup-stat-card">
                    <div class="stat-icon bg-red-100 text-red-600">
                      <Trash2 class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ cleanupStats.redundantData }}</div>
                      <div class="stat-label">冗余数据</div>
                      <div class="stat-detail">可清理 {{ cleanupStats.redundantSize }}</div>
                    </div>
                  </div>
                  
                  <div class="cleanup-stat-card">
                    <div class="stat-icon bg-orange-100 text-orange-600">
                      <Archive class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ cleanupStats.oldLogs }}</div>
                      <div class="stat-label">过期日志</div>
                      <div class="stat-detail">超过保留期限</div>
                    </div>
                  </div>
                  
                  <div class="cleanup-stat-card">
                    <div class="stat-icon bg-blue-100 text-blue-600">
                      <Money class="w-6 h-6" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ cleanupStats.tempFiles }}</div>
                      <div class="stat-label">临时文件</div>
                      <div class="stat-detail">可释放空间</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 清理规则配置 -->
            <div class="cleanup-rules">
              <div class="rules-header">
                <h3 class="rules-title">清理规则配置</h3>
                <el-button @click="handleSaveCleanupRules" type="primary" size="small">
                  保存规则
                </el-button>
              </div>
              
              <div class="rules-list">
                <div class="rule-category" v-for="category in cleanupRules" :key="category.name">
                  <div class="category-header">
                    <h4 class="category-title">{{ category.label }}</h4>
                    <el-switch v-model="category.enabled" />
                  </div>
                  
                  <div class="category-rules" v-if="category.enabled">
                    <div class="rule-item" v-for="rule in category.rules" :key="rule.id">
                      <div class="rule-info">
                        <div class="rule-name">{{ rule.name }}</div>
                        <div class="rule-description">{{ rule.description }}</div>
                      </div>
                      <div class="rule-config">
                        <div class="config-item" v-if="rule.type === 'time'">
                          <label class="config-label">保留天数:</label>
                          <el-input-number
                            v-model="rule.retentionDays"
                            :min="1"
                            :max="365"
                            size="small"
                          />
                        </div>
                        <div class="config-item" v-if="rule.type === 'size'">
                          <label class="config-label">最大大小 (MB):</label>
                          <el-input-number
                            v-model="rule.maxSize"
                            :min="1"
                            :max="10240"
                            size="small"
                          />
                        </div>
                        <div class="config-item" v-if="rule.type === 'count'">
                          <label class="config-label">最大数量:</label>
                          <el-input-number
                            v-model="rule.maxCount"
                            :min="1"
                            :max="100000"
                            size="small"
                          />
                        </div>
                        <div class="rule-actions">
                          <el-switch v-model="rule.enabled" size="small" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 清理执行 -->
            <div class="cleanup-execution">
              <div class="execution-header">
                <h3 class="execution-title">执行数据清理</h3>
                <div class="execution-warning">
                  <Warning class="w-4 h-4 text-yellow-600" />
                  <span>数据清理操作不可逆，建议先执行数据备份</span>
                </div>
              </div>
              
              <div class="cleanup-preview">
                <h4 class="preview-title">清理预览</h4>
                <div class="preview-list">
                  <div class="preview-item" v-for="item in cleanupPreview" :key="item.type">
                    <div class="item-icon">
                      <component :is="getCleanupIcon(item.type)" class="w-5 h-5" />
                    </div>
                    <div class="item-content">
                      <div class="item-title">{{ item.title }}</div>
                      <div class="item-stats">
                        将清理 {{ item.count }} 项，释放 {{ item.size }} 空间
                      </div>
                    </div>
                    <div class="item-actions">
                      <el-checkbox v-model="item.selected">执行</el-checkbox>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="execution-actions">
                <el-button 
                  @click="handleStartCleanup" 
                  type="primary" 
                  :disabled="!hasSelectedCleanup"
                  :loading="cleanupInProgress"
                >
                  <Play class="w-4 h-4 mr-2" />
                  开始清理
                </el-button>
                
                <el-button @click="handlePreviewCleanup">
                  <Eye class="w-4 h-4 mr-2" />
                  预览清理
                </el-button>
                
                <el-button @click="handleScheduleCleanup">
                  <Calendar class="w-4 h-4 mr-2" />
                  计划清理
                </el-button>
              </div>
              
              <!-- 清理进度 -->
              <div class="cleanup-progress" v-if="cleanupInProgress">
                <h4 class="progress-title">清理进度</h4>
                <el-progress
                  :percentage="cleanupProgressData.percentage"
                  :stroke-width="8"
                  :show-text="true"
                />
                <div class="progress-details">
                  <p>{{ cleanupProgressData.currentAction }}</p>
                  <p>已处理: {{ cleanupProgressData.processed }} / {{ cleanupProgressData.total }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据导入导出 -->
        <el-tab-pane label="导入导出" name="import-export">
          <div class="import-export-panel">
            <!-- 数据导出 -->
            <div class="export-section">
              <h3 class="section-title">数据导出</h3>
              
              <div class="export-options">
                <div class="export-formats">
                  <h4 class="options-title">选择导出格式：</h4>
                  <el-radio-group v-model="exportFormat">
                    <el-radio label="sql">SQL 脚本</el-radio>
                    <el-radio label="json">JSON 格式</el-radio>
                    <el-radio label="csv">CSV 文件</el-radio>
                    <el-radio label="excel">Excel 文件</el-radio>
                  </el-radio-group>
                </div>
                
                <div class="export-scope">
                  <h4 class="options-title">导出范围：</h4>
                  <el-radio-group v-model="exportScope">
                    <el-radio label="all">完整数据库</el-radio>
                    <el-radio label="tables">选择表</el-radio>
                    <el-radio label="custom">自定义查询</el-radio>
                  </el-radio-group>
                </div>
                
                <div class="table-selection" v-if="exportScope === 'tables'">
                  <h4 class="options-title">选择要导出的表：</h4>
                  <el-checkbox-group v-model="selectedExportGrids">
                    <div class="table-grid">
                      <el-checkbox 
                        v-for="table in availableGrids" 
                        :key="table.name"
                        :label="table.name"
                        class="table-checkbox"
                      >
                        {{ table.label }} ({{ table.rowCount }} 条)
                      </el-checkbox>
                    </div>
                  </el-checkbox-group>
                </div>
                
                <div class="custom-query" v-if="exportScope === 'custom'">
                  <h4 class="options-title">自定义查询：</h4>
                  <el-input
                    v-model="customQuery"
                    type="textarea"
                    :rows="6"
                    placeholder="输入 SQL 查询语句..."
                  />
                </div>
                
                <div class="export-actions">
                  <el-button @click="handlePreviewExport">
                    <Eye class="w-4 h-4 mr-2" />
                    预览数据
                  </el-button>
                  <el-button @click="handleStartExport" type="primary" :loading="exportInProgress">
                    <Download class="w-4 h-4 mr-2" />
                    开始导出
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 数据导入 -->
            <div class="import-section">
              <h3 class="section-title">数据导入</h3>
              
              <div class="import-options">
                <div class="import-method">
                  <h4 class="options-title">导入方式：</h4>
                  <el-radio-group v-model="importMethod">
                    <el-radio label="file">文件上传</el-radio>
                    <el-radio label="url">URL 导入</el-radio>
                    <el-radio label="api">API 同步</el-radio>
                  </el-radio-group>
                </div>
                
                <div class="file-import" v-if="importMethod === 'file'">
                  <el-upload
                    drag
                    :before-upload="handleBeforeImport"
                    :on-success="handleImportSuccess"
                    accept=".sql,.json,.csv,.xlsx"
                    action="/api/admin/import-data"
                  >
                    <el-icon class="el-icon--upload"><Upload /></el-icon>
                    <div class="el-upload__text">
                      将数据文件拖拽到此处，或<em>点击上传</em>
                    </div>
                    <div class="el-upload__tip">
                      支持 SQL, JSON, CSV, Excel 格式
                    </div>
                  </el-upload>
                </div>
                
                <div class="url-import" v-if="importMethod === 'url'">
                  <h4 class="options-title">数据源 URL：</h4>
                  <el-input
                    v-model="importUrl"
                    placeholder="输入数据源 URL..."
                  />
                </div>
                
                <div class="api-import" v-if="importMethod === 'api'">
                  <h4 class="options-title">API 配置：</h4>
                  <div class="api-config">
                    <el-input
                      v-model="apiConfig.endpoint"
                      placeholder="API 端点"
                    />
                    <el-input
                      v-model="apiConfig.token"
                      placeholder="访问令牌"
                      type="password"
                    />
                  </div>
                </div>
                
                <div class="import-settings">
                  <h4 class="options-title">导入设置：</h4>
                  <div class="settings-group">
                    <el-checkbox v-model="importSettings.skipDuplicates">
                      跳过重复数据
                    </el-checkbox>
                    <el-checkbox v-model="importSettings.validateData">
                      数据验证
                    </el-checkbox>
                    <el-checkbox v-model="importSettings.createBackup">
                      导入前备份
                    </el-checkbox>
                  </div>
                </div>
                
                <div class="import-actions">
                  <el-button @click="handleValidateImport">
                    <CircleCheck class="w-4 h-4 mr-2" />
                    验证数据
                  </el-button>
                  <el-button @click="handleStartImport" type="primary" :loading="importInProgress">
                    <Upload class="w-4 h-4 mr-2" />
                    开始导入
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 自定义备份对话框 -->
    <el-dialog
      v-model="customBackupDialogVisible"
      title="自定义备份配置"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="custom-backup-config">
        <div class="config-section">
          <h4 class="section-title">选择备份内容</h4>
          <el-checkbox-group v-model="customBackupGrids">
            <div class="table-categories">
              <div class="category" v-for="category in tableCategories" :key="category.name">
                <div class="category-header">
                  <el-checkbox 
                    :indeterminate="isCategoryIndeterminate(category)"
                    :value="isCategoryChecked(category)"
                    @change="handleCategoryCheck(category, $event)"
                  >
                    {{ category.label }}
                  </el-checkbox>
                </div>
                <div class="category-tables">
                  <el-checkbox 
                    v-for="table in category.tables" 
                    :key="table.name"
                    :label="table.name"
                  >
                    {{ table.label }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </div>
        
        <div class="config-section">
          <h4 class="section-title">备份选项</h4>
          <div class="backup-options">
            <el-checkbox v-model="customBackupOptions.includeStructure">
              包含表结构
            </el-checkbox>
            <el-checkbox v-model="customBackupOptions.includeData">
              包含数据
            </el-checkbox>
            <el-checkbox v-model="customBackupOptions.includeIndexes">
              包含索引
            </el-checkbox>
            <el-checkbox v-model="customBackupOptions.compress">
              压缩备份文件
            </el-checkbox>
          </div>
        </div>
        
        <div class="config-section">
          <h4 class="section-title">数据过滤</h4>
          <div class="data-filters">
            <div class="filter-item">
              <label class="filter-label">时间范围:</label>
              <el-date-picker
                v-model="customBackupFilters.dateRange"
                type="datetimerange"
                placeholder="选择时间范围"
              />
            </div>
            <div class="filter-item">
              <label class="filter-label">自定义条件:</label>
              <el-input
                v-model="customBackupFilters.customWhere"
                placeholder="WHERE 条件 (可选)"
              />
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="customBackupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleExecuteCustomBackup" :loading="backupInProgress">
            开始备份
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入

// 页面状态
const activeTab = ref('backup')
const loading = ref(false)

// 数据统计
const dataStats = reactive({
  totalSize: '2.4 GB',
  totalGrids: 45,
  backupCount: 12,
  lastBackup: '2 小时前',
  storageUsed: '8.2 GB',
  storagePercent: 68,
  integrityScore: 98,
  integrityStatus: '良好'
})

// 备份配置
const backupConfig = reactive({
  frequency: 'daily',
  time: new Date(),
  keepCount: 7,
  compress: true
})

// 备份状态
const backupInProgress = ref(false)
const backupProgress = ref(0)

// 备份预估
const backupEstimates = reactive({
  full: {
    time: '15-20 分钟',
    size: '~800 MB'
  },
  incremental: {
    time: '2-5 分钟',
    size: '~50 MB'
  }
})

// 备份历史
const backupList = ref([
  {
    id: 1,
    name: 'full_backup_20250723_140530',
    type: 'full',
    size: '786 MB',
    created_at: '2025-07-23 14:05:30',
    status: 'completed'
  },
  {
    id: 2,
    name: 'incremental_backup_20250723_120015',
    type: 'incremental',
    size: '52 MB',
    created_at: '2025-07-23 12:00:15',
    status: 'completed'
  },
  {
    id: 3,
    name: 'custom_backup_20250722_180245',
    type: 'custom',
    size: '234 MB',
    created_at: '2025-07-22 18:02:45',
    status: 'completed'
  }
])

// 恢复配置
const restoreMode = ref('')
const fileSourceTab = ref('history')
const restoreTimePoint = ref(null)
const restoreOptions = reactive({
  dropExisting: false,
  preserveUsers: true,
  createBackupBeforeRestore: true
})

// 恢复状态
const restoreInProgress = ref(false)
const restoreProgress = reactive({
  percentage: 0,
  status: 'normal',
  currentStep: '',
  processedItems: 0,
  totalItems: 0,
  estimatedTimeLeft: ''
})

// 表分类
const tableCategories = ref([
  {
    name: 'user',
    label: '用户数据',
    tables: [
      { name: 'users', label: '用户表', rowCount: 1250 },
      { name: 'user_profiles', label: '用户资料', rowCount: 1250 },
      { name: 'user_permissions', label: '用户权限', rowCount: 3200 }
    ]
  },
  {
    name: 'business',
    label: '业务数据',
    tables: [
      { name: 'aviation_materials', label: '航材表', rowCount: 15680 },
      { name: 'orders', label: '订单表', rowCount: 8920 },
      { name: 'suppliers', label: '供应商表', rowCount: 450 }
    ]
  },
  {
    name: 'system',
    label: '系统数据',
    tables: [
      { name: 'audit_logs', label: '审计日志', rowCount: 45230 },
      { name: 'system_configs', label: '系统配置', rowCount: 120 },
      { name: 'notifications', label: '通知表', rowCount: 8650 }
    ]
  }
])

const selectedGrids = ref([])

// 恢复前检查
const preRestoreChecks = ref([
  { id: 1, message: '数据库连接正常', passed: true },
  { id: 2, message: '备份文件完整性验证通过', passed: true },
  { id: 3, message: '磁盘空间充足', passed: true },
  { id: 4, message: '没有活跃的数据库连接', passed: false }
])

// 数据清理
const cleanupStats = reactive({
  redundantData: '245 MB',
  redundantSize: '245 MB',
  oldLogs: '15,420 条',
  tempFiles: '123 MB'
})

const cleanupRules = ref([
  {
    name: 'logs',
    label: '日志数据',
    enabled: true,
    rules: [
      {
        id: 'audit_logs',
        name: '审计日志清理',
        description: '清理超过指定天数的审计日志',
        type: 'time',
        retentionDays: 90,
        enabled: true
      },
      {
        id: 'error_logs',
        name: '错误日志清理',
        description: '清理超过指定天数的错误日志',
        type: 'time',
        retentionDays: 30,
        enabled: true
      }
    ]
  },
  {
    name: 'temp',
    label: '临时数据',
    enabled: true,
    rules: [
      {
        id: 'temp_files',
        name: '临时文件清理',
        description: '清理系统临时文件',
        type: 'time',
        retentionDays: 7,
        enabled: true
      },
      {
        id: 'cache_data',
        name: '缓存数据清理',
        description: '清理过期的缓存数据',
        type: 'size',
        maxSize: 500,
        enabled: true
      }
    ]
  }
])

const cleanupPreview = ref([
  {
    type: 'logs',
    title: '过期日志文件',
    count: '15,420',
    size: '245 MB',
    selected: true
  },
  {
    type: 'temp',
    title: '临时文件',
    count: '1,230',
    size: '123 MB',
    selected: true
  },
  {
    type: 'cache',
    title: '过期缓存',
    count: '5,680',
    size: '89 MB',
    selected: false
  }
])

const cleanupInProgress = ref(false)
const cleanupProgressData = reactive({
  percentage: 0,
  currentAction: '',
  processed: 0,
  total: 0
})

// 导入导出
const exportFormat = ref('sql')
const exportScope = ref('all')
const selectedExportGrids = ref([])
const customQuery = ref('')
const exportInProgress = ref(false)

const importMethod = ref('file')
const importUrl = ref('')
const apiConfig = reactive({
  endpoint: '',
  token: ''
})
const importSettings = reactive({
  skipDuplicates: true,
  validateData: true,
  createBackup: true
})
const importInProgress = ref(false)

// 可用表列表
const availableGrids = computed(() => {
  return tableCategories.value.flatMap(category => category.tables)
})

// 自定义备份对话框
const customBackupDialogVisible = ref(false)
const customBackupGrids = ref([])
const customBackupOptions = reactive({
  includeStructure: true,
  includeData: true,
  includeIndexes: true,
  compress: true
})
const customBackupFilters = reactive({
  dateRange: null,
  customWhere: ''
})

// 计算属性
const canStartRestore = computed(() => {
  return preRestoreChecks.value.every(check => check.passed) && !restoreInProgress.value
})

const hasHighRisk = computed(() => {
  return restoreOptions.dropExisting
})

const hasSelectedCleanup = computed(() => {
  return cleanupPreview.value.some(item => item.selected)
})

const estimatedRestoreTime = computed(() => {
  if (!restoreTimePoint.value) return ''
  return '约 10-15 分钟'
})

const affectedDataRange = computed(() => {
  if (!restoreTimePoint.value) return ''
  return '所有表数据'
})

// 方法
const handleRefreshStatus = () => {
  ElMessage.success('状态已刷新')
}

const handleDataIntegrityCheck = async () => {
  ElMessage.info('正在执行完整性检查...')
  // 模拟检查过程
  setTimeout(() => {
    dataStats.integrityScore = 98
    dataStats.integrityStatus = '良好'
    ElMessage.success('完整性检查完成')
  }, 2000)
}

const handleCreateBackup = () => {
  handleFullBackup()
}

const handleTabChange = (tabName) => {
  console.log(`切换到标签页: ${tabName}`)
}

// 备份相关方法
const handleSaveBackupConfig = () => {
  ElMessage.success('备份配置已保存')
}

const handleFullBackup = async () => {
  try {
    backupInProgress.value = true
    backupProgress.value = 0
    
    ElMessage.info('开始完整备份...')
    
    // 模拟备份进度
    const interval = setInterval(() => {
      backupProgress.value += 10
      if (backupProgress.value >= 100) {
        clearInterval(interval)
        backupInProgress.value = false
        
        // 添加新的备份记录
        const newBackup = {
          id: Date.now(),
          name: `full_backup_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`,
          type: 'full',
          size: '786 MB',
          created_at: new Date().toLocaleString(),
          status: 'completed'
        }
        backupList.value.unshift(newBackup)
        
        ElMessage.success('完整备份已完成')
      }
    }, 500)
  } catch (error) {
    backupInProgress.value = false
    ElMessage.error('备份失败: ' + error.message)
  }
}

const handleIncrementalBackup = async () => {
  try {
    backupInProgress.value = true
    backupProgress.value = 0
    
    ElMessage.info('开始增量备份...')
    
    // 模拟备份进度
    const interval = setInterval(() => {
      backupProgress.value += 20
      if (backupProgress.value >= 100) {
        clearInterval(interval)
        backupInProgress.value = false
        
        const newBackup = {
          id: Date.now(),
          name: `incremental_backup_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`,
          type: 'incremental',
          size: '52 MB',
          created_at: new Date().toLocaleString(),
          status: 'completed'
        }
        backupList.value.unshift(newBackup)
        
        ElMessage.success('增量备份已完成')
      }
    }, 300)
  } catch (error) {
    backupInProgress.value = false
    ElMessage.error('备份失败: ' + error.message)
  }
}

const handleCustomBackup = () => {
  customBackupDialogVisible.value = true
}

const handleExecuteCustomBackup = async () => {
  if (customBackupGrids.value.length === 0) {
    ElMessage.warning('请选择要备份的表')
    return
  }
  
  try {
    backupInProgress.value = true
    customBackupDialogVisible.value = false
    
    ElMessage.info('开始自定义备份...')
    
    // 模拟备份进度
    setTimeout(() => {
      backupInProgress.value = false
      
      const newBackup = {
        id: Date.now(),
        name: `custom_backup_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`,
        type: 'custom',
        size: '234 MB',
        created_at: new Date().toLocaleString(),
        status: 'completed'
      }
      backupList.value.unshift(newBackup)
      
      ElMessage.success('自定义备份已完成')
    }, 3000)
  } catch (error) {
    backupInProgress.value = false
    ElMessage.error('备份失败: ' + error.message)
  }
}

const handleRefreshBackups = () => {
  ElMessage.success('备份列表已刷新')
}

const handleCleanupBackups = async () => {
  try {
    await ElMessageBox.confirm('确定要清理过期的备份文件吗？', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('过期备份已清理')
  } catch {
    // 用户取消
  }
}

const handleRestoreBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(`确定要从备份 "${backup.name}" 恢复数据吗？`, '确认恢复', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.info('开始数据恢复...')
    // 这里实现恢复逻辑
  } catch {
    // 用户取消
  }
}

const handleDownloadBackup = (backup) => {
  ElMessage.info(`开始下载备份文件: ${backup.name}`)
  // 这里实现下载逻辑
}

const handleDeleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(`确定要删除备份 "${backup.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = backupList.value.findIndex(item => item.id === backup.id)
    if (index > -1) {
      backupList.value.splice(index, 1)
      ElMessage.success('备份已删除')
    }
  } catch {
    // 用户取消
  }
}

// 恢复相关方法
const setRestoreMode = (mode) => {
  restoreMode.value = mode
}

const handleBackupSelectionChange = (selection) => {
  console.log('选择的备份:', selection)
}

const handleBeforeUpload = (file) => {
  const isValidFormat = ['sql', 'gz', 'zip'].some(ext => file.name.endsWith(`.${ext}`))
  if (!isValidFormat) {
    ElMessage.error('只支持 .sql, .gz, .zip 格式的文件')
    return false
  }
  
  const isLt500M = file.size / 1024 / 1024 < 500
  if (!isLt500M) {
    ElMessage.error('文件大小不能超过 500MB')
    return false
  }
  
  return true
}

const handleUploadSuccess = () => {
  ElMessage.success('文件上传成功')
}

const selectAllGrids = () => {
  selectedGrids.value = availableGrids.value.map(table => table.name)
}

const clearGridSelection = () => {
  selectedGrids.value = []
}

const handleStartRestore = async () => {
  try {
    await ElMessageBox.confirm('确定要开始数据恢复吗？此操作可能需要较长时间。', '确认恢复', {
      confirmButtonText: '开始恢复',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    restoreInProgress.value = true
    restoreProgress.percentage = 0
    restoreProgress.currentStep = '准备恢复环境...'
    
    // 模拟恢复进度
    const steps = [
      '准备恢复环境...',
      '验证备份文件...',
      '创建临时表...',
      '导入数据...',
      '更新索引...',
      '验证数据完整性...',
      '清理临时文件...'
    ]
    
    let stepIndex = 0
    const interval = setInterval(() => {
      restoreProgress.percentage += 15
      
      if (stepIndex < steps.length - 1) {
        stepIndex++
        restoreProgress.currentStep = steps[stepIndex]
      }
      
      restoreProgress.processedItems = Math.floor(restoreProgress.percentage * 10)
      restoreProgress.totalItems = 1000
      restoreProgress.estimatedTimeLeft = `${Math.max(0, 10 - Math.floor(restoreProgress.percentage / 10))} 分钟`
      
      if (restoreProgress.percentage >= 100) {
        clearInterval(interval)
        restoreInProgress.value = false
        restoreProgress.currentStep = '恢复完成'
        ElMessage.success('数据恢复已完成')
      }
    }, 800)
  } catch {
    // 用户取消
  }
}

const handleDryRun = () => {
  ElMessage.info('开始模拟执行...')
  setTimeout(() => {
    ElMessage.success('模拟执行完成，没有发现问题')
  }, 2000)
}

const handlePauseRestore = () => {
  ElMessage.info('恢复已暂停')
}

const handleCancelRestore = async () => {
  try {
    await ElMessageBox.confirm('确定要取消恢复操作吗？', '确认取消', {
      confirmButtonText: '确定',
      cancelButtonText: '继续恢复',
      type: 'warning'
    })
    
    restoreInProgress.value = false
    ElMessage.info('恢复操作已取消')
  } catch {
    // 用户选择继续恢复
  }
}

const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

const disabledHours = () => {
  return []
}

// 清理相关方法
const handleSaveCleanupRules = () => {
  ElMessage.success('清理规则已保存')
}

const handlePreviewCleanup = () => {
  ElMessage.info('正在分析可清理的数据...')
  setTimeout(() => {
    ElMessage.success('清理预览已更新')
  }, 1500)
}

const handleStartCleanup = async () => {
  const selectedItems = cleanupPreview.value.filter(item => item.selected)
  
  try {
    await ElMessageBox.confirm(`确定要清理选中的 ${selectedItems.length} 项数据吗？此操作不可逆转。`, '确认清理', {
      confirmButtonText: '开始清理',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    cleanupInProgress.value = true
    cleanupProgressData.percentage = 0
    cleanupProgressData.currentAction = '开始清理过期日志...'
    
    // 模拟清理进度
    const interval = setInterval(() => {
      cleanupProgressData.percentage += 10
      cleanupProgressData.processed += 100
      cleanupProgressData.total = 1000
      
      if (cleanupProgressData.percentage >= 100) {
        clearInterval(interval)
        cleanupInProgress.value = false
        cleanupProgressData.currentAction = '清理完成'
        ElMessage.success('数据清理已完成')
      }
    }, 500)
  } catch {
    // 用户取消
  }
}

const handleScheduleCleanup = () => {
  ElMessage.info('计划清理功能正在开发中')
}

// 导入导出相关方法
const handlePreviewExport = () => {
  ElMessage.info('正在预览导出数据...')
}

const handleStartExport = async () => {
  exportInProgress.value = true
  
  try {
    ElMessage.info('开始导出数据...')
    
    // 模拟导出过程
    setTimeout(() => {
      exportInProgress.value = false
      ElMessage.success('数据导出完成')
    }, 3000)
  } catch (error) {
    exportInProgress.value = false
    ElMessage.error('导出失败: ' + error.message)
  }
}

const handleBeforeImport = (file) => {
  const validFormats = ['sql', 'json', 'csv', 'xlsx']
  const isValidFormat = validFormats.some(ext => file.name.endsWith(`.${ext}`))
  
  if (!isValidFormat) {
    ElMessage.error('文件格式不支持')
    return false
  }
  
  return true
}

const handleImportSuccess = () => {
  ElMessage.success('文件导入成功')
}

const handleValidateImport = () => {
  ElMessage.info('正在验证导入数据...')
}

const handleStartImport = async () => {
  importInProgress.value = true
  
  try {
    ElMessage.info('开始导入数据...')
    
    // 模拟导入过程
    setTimeout(() => {
      importInProgress.value = false
      ElMessage.success('数据导入完成')
    }, 4000)
  } catch (error) {
    importInProgress.value = false
    ElMessage.error('导入失败: ' + error.message)
  }
}

// 工具方法
const getBackupIcon = (type) => {
  const icons = {
    full: Database,
    incremental: Archive,
    custom: Settings
  }
  return icons[type] || Database
}

const getBackupTypeTagType = (type) => {
  const types = {
    full: 'primary',
    incremental: 'success',
    custom: 'warning'
  }
  return types[type] || 'info'
}

const getBackupTypeText = (type) => {
  const texts = {
    full: '完整',
    incremental: '增量',
    custom: '自定义'
  }
  return texts[type] || type
}

const getStatusTagType = (status) => {
  const types = {
    completed: 'success',
    running: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    running: '进行中',
    failed: '失败'
  }
  return texts[status] || status
}

const getCleanupIcon = (type) => {
  const icons = {
    logs: FileText,
    temp: Archive,
    cache: Money
  }
  return icons[type] || Trash2
}

// 自定义备份相关方法
const isCategoryIndeterminate = (category) => {
  const selectedCount = category.tables.filter(table => 
    customBackupGrids.value.includes(table.name)
  ).length
  return selectedCount > 0 && selectedCount < category.tables.length
}

const isCategoryChecked = (category) => {
  return category.tables.every(table => 
    customBackupGrids.value.includes(table.name)
  )
}

const handleCategoryCheck = (category, checked) => {
  if (checked) {
    // 选中该分类的所有表
    category.tables.forEach(table => {
      if (!customBackupGrids.value.includes(table.name)) {
        customBackupGrids.value.push(table.name)
      }
    })
  } else {
    // 取消选中该分类的所有表
    category.tables.forEach(table => {
      const index = customBackupGrids.value.indexOf(table.name)
      if (index > -1) {
        customBackupGrids.value.splice(index, 1)
      }
    })
  }
}

// 生命周期
onMounted(() => {
  console.log('数据管理页面已挂载')
})

onUnmounted(() => {
  // 清理定时器等资源
})
</script>

<style scoped>
.data-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-area {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 数据状态概览 */
.data-overview {
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

.status-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.status-detail {
  font-size: 12px;
  color: #9ca3af;
}

/* 主要内容区域 */
.main-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
}

/* 备份配置 */
.backup-config {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.config-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.config-form {
  margin-top: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 快速备份操作 */
.quick-backup {
  margin-bottom: 24px;
}

.quick-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quick-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.backup-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 300px;
}

.progress-text {
  font-size: 14px;
  color: #6b7280;
}

.backup-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.backup-type-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 16px;
}

.backup-type-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.type-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
}

.type-content {
  flex: 1;
}

.type-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.type-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.type-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 12px;
  color: #9ca3af;
}

/* 备份历史 */
.backup-history {
  margin-top: 24px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.history-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.backup-name {
  display: flex;
  align-items: center;
}

/* 恢复面板 */
.restore-panel {
  padding: 20px 0;
}

.restore-options {
  margin-bottom: 24px;
}

.options-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.restore-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.restore-type-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.restore-type-card:hover,
.restore-type-card.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.restore-config {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-top: 24px;
}

.config-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.restore-execution {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.execution-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.risk-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fef2f2;
  padding: 8px 12px;
  border-radius: 6px;
}

.warning-text {
  font-size: 14px;
  color: #dc2626;
}

.pre-restore-check {
  margin-bottom: 20px;
}

.check-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.check-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.check-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.execution-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.restore-progress {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.progress-actions {
  display: flex;
  gap: 8px;
}

.progress-content {
  margin-top: 16px;
}

.progress-details {
  margin-top: 12px;
}

.progress-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #6b7280;
}

/* 清理面板 */
.cleanup-overview {
  margin-bottom: 24px;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.cleanup-stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.cleanup-rules {
  margin-bottom: 24px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.rules-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.rule-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.category-header {
  background: #f9fafb;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.category-rules {
  padding: 16px;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-info {
  flex: 1;
}

.rule-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.rule-description {
  font-size: 12px;
  color: #6b7280;
}

.rule-config {
  display: flex;
  align-items: center;
  gap: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-label {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.cleanup-preview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.item-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 6px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.item-stats {
  font-size: 12px;
  color: #6b7280;
}

/* 导入导出面板 */
.import-export-panel {
  padding: 20px 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.export-section,
.import-section {
  margin-bottom: 32px;
}

.export-options,
.import-options {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.options-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.table-selection,
.custom-query {
  margin-top: 16px;
}

.table-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.table-checkbox {
  padding: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.export-actions,
.import-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

/* 自定义备份对话框 */
.custom-backup-config {
  max-height: 60vh;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  border-bottom: none;
  padding-bottom: 0;
}

.table-categories {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.category {
  border-bottom: 1px solid #f3f4f6;
}

.category:last-child {
  border-bottom: none;
}

.category-header {
  background: #f9fafb;
  padding: 12px 16px;
}

.category-tables {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.backup-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .backup-types,
  .restore-types {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .execution-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>