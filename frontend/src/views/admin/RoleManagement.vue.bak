<!--
角色权限配置页面
版本: 1.0
创建时间: 2025-07-23

角色权限矩阵配置的专门页面
特点：
1. 可视化权限矩阵配置
2. 拖拽式权限编辑
3. 权限模板管理
4. 实时权限预览
5. 权限继承关系
-->

<template>
  <div class="role-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">角色权限配置</h1>
          <p class="page-subtitle">配置系统角色权限矩阵，管理权限继承关系</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleImportRoles">
            <Upload class="w-4 h-4 mr-2" />
            导入配置
          </el-button>
          <el-button @click="handleExportRoles">
            <Download class="w-4 h-4 mr-2" />
            导出配置
          </el-button>
          <el-button type="primary" @click="handleSaveChanges" :loading="savingChanges">
            <Check class="w-4 h-4 mr-2" />
            保存变更
          </el-button>
        </div>
      </div>
    </div>

    <!-- 角色概览统计 -->
    <div class="roles-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="overview-card">
          <div class="card-icon bg-blue-100 text-blue-600">
            <User class="w-6 h-6" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ roleStats.totalRoles }}</div>
            <div class="card-label">系统角色</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-green-100 text-green-600">
            <Lock class="w-6 h-6" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ roleStats.totalPermissions }}</div>
            <div class="card-label">权限项目</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Edit class="w-6 h-6" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ roleStats.modifiedRoles }}</div>
            <div class="card-label">待保存变更</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Clock class="w-6 h-6" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ roleStats.lastModified }}</div>
            <div class="card-label">最后修改</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 权限矩阵配置 -->
        <el-tab-pane label="权限矩阵" name="matrix">
          <div class="permission-matrix-panel">
            <!-- 工具栏 -->
            <div class="matrix-toolbar">
              <div class="toolbar-left">
                <el-button-group>
                  <el-button 
                    :type="viewMode === 'detailed' ? 'primary' : ''" 
                    @click="setViewMode('detailed')"
                  >
                    详细视图
                  </el-button>
                  <el-button 
                    :type="viewMode === 'compact' ? 'primary' : ''" 
                    @click="setViewMode('compact')"
                  >
                    紧凑视图
                  </el-button>
                </el-button-group>
                
                <el-select v-model="selectedRoleGroup" placeholder="角色分组" class="ml-4">
                  <el-option label="全部角色" value="" />
                  <el-option label="业务角色" value="business" />
                  <el-option label="管理角色" value="management" />
                  <el-option label="服务角色" value="service" />
                </el-select>
              </div>
              
              <div class="toolbar-right">
                <el-button @click="handleExpandAll">全部展开</el-button>
                <el-button @click="handleCollapseAll">全部收起</el-button>
                <el-button @click="handleResetMatrix" type="warning">重置矩阵</el-button>
              </div>
            </div>

            <!-- 权限矩阵表格 -->
            <div class="matrix-container">
              <div class="matrix-table-wrapper">
                <table class="permission-matrix-table">
                  <thead>
                    <tr>
                      <th class="role-header">角色/权限</th>
                      <th 
                        v-for="permission in displayedPermissions" 
                        :key="permission.key"
                        class="permission-header"
                        :title="permission.description"
                      >
                        <div class="permission-header-content">
                          <span class="permission-name">{{ permission.name }}</span>
                          <el-tooltip :content="permission.description" placement="top">
                            <InfoFilled class="w-3 h-3 ml-1 text-gray-400" />
                          </el-tooltip>
                        </div>
                      </th>
                      <th class="actions-header">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr 
                      v-for="role in displayedRoles" 
                      :key="role.key"
                      :class="[
                        'role-row',
                        { 'role-modified': modifiedRoles.has(role.key) }
                      ]"
                    >
                      <td class="role-cell">
                        <div class="role-info">
                          <div class="role-icon-wrapper">
                            <div 
                              class="role-icon"
                              :class="getRoleIconClass(role.key)"
                            >
                              <component :is="getRoleIcon(role.key)" class="w-4 h-4" />
                            </div>
                          </div>
                          <div class="role-details">
                            <div class="role-name">{{ role.name }}</div>
                            <div class="role-desc">{{ role.description }}</div>
                            <div class="role-level">权限等级: {{ role.level }}</div>
                          </div>
                        </div>
                      </td>
                      
                      <td 
                        v-for="permission in displayedPermissions" 
                        :key="permission.key"
                        class="permission-cell"
                      >
                        <div class="permission-control">
                          <el-switch
                            v-model="rolePermissions[role.key][permission.key]"
                            :disabled="!canModifyPermission(role.key, permission.key)"
                            @change="handlePermissionChange(role.key, permission.key)"
                            size="small"
                          />
                          <div 
                            v-if="hasInheritedPermission(role.key, permission.key)"
                            class="inherited-indicator"
                            title="继承权限"
                          >
                            <Link class="w-3 h-3 text-blue-400" />
                          </div>
                        </div>
                      </td>
                      
                      <td class="actions-cell">
                        <div class="role-actions">
                          <el-button 
                            size="small" 
                            text 
                            @click="handleCopyRole(role.key)"
                            title="复制角色权限"
                          >
                            <CopyDocument class="w-4 h-4" />
                          </el-button>
                          <el-button 
                            size="small" 
                            text 
                            @click="handleResetRole(role.key)"
                            title="重置角色权限"
                          >
                            <RefreshLeft class="w-4 h-4" />
                          </el-button>
                          <el-button 
                            size="small" 
                            text 
                            @click="handleRoleDetail(role.key)"
                            title="角色详情"
                          >
                            <View class="w-4 h-4" />
                          </el-button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 权限统计信息 -->
            <div class="matrix-summary">
              <div class="summary-cards">
                <div class="summary-card">
                  <div class="summary-label">权限总数</div>
                  <div class="summary-value">{{ getTotalPermissions() }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-label">已启用</div>
                  <div class="summary-value text-green-600">{{ getEnabledPermissions() }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-label">已禁用</div>
                  <div class="summary-value text-red-600">{{ getDisabledPermissions() }}</div>
                </div>
                <div class="summary-card">
                  <div class="summary-label">继承权限</div>
                  <div class="summary-value text-blue-600">{{ getInheritedPermissions() }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限继承关系 -->
        <el-tab-pane label="继承关系" name="inheritance">
          <div class="inheritance-panel">
            <div class="inheritance-header">
              <h3 class="text-lg font-semibold text-gray-800">权限继承关系图</h3>
              <div class="inheritance-controls">
                <el-button @click="handleRefreshInheritance">
                  <Refresh class="w-4 h-4 mr-2" />
                  刷新关系图
                </el-button>
                <el-button @click="handleEditInheritance">
                  <Edit class="w-4 h-4 mr-2" />
                  编辑继承
                </el-button>
              </div>
            </div>
            
            <!-- 继承关系可视化 -->
            <div class="inheritance-visualization">
              <div class="inheritance-tree">
                <div 
                  v-for="level in inheritanceTree" 
                  :key="level.level"
                  class="inheritance-level"
                >
                  <div class="level-header">
                    <h4 class="level-title">权限等级 {{ level.level }}</h4>
                    <div class="level-description">{{ level.description }}</div>
                  </div>
                  
                  <div class="level-roles">
                    <div 
                      v-for="role in level.roles" 
                      :key="role.key"
                      class="inheritance-role-card"
                      @click="handleSelectInheritanceRole(role)"
                    >
                      <div class="role-header">
                        <div 
                          class="role-icon"
                          :class="getRoleIconClass(role.key)"
                        >
                          <component :is="getRoleIcon(role.key)" class="w-5 h-5" />
                        </div>
                        <div class="role-info">
                          <div class="role-name">{{ role.name }}</div>
                          <div class="role-count">{{ role.permissions.length }} 个权限</div>
                        </div>
                      </div>
                      
                      <div class="inheritance-connections" v-if="role.inheritsFrom.length > 0">
                        <div class="inheritance-arrow">↑</div>
                        <div class="inherited-roles">
                          继承自: {{ role.inheritsFrom.map(r => roleDefinitions[r]?.name).join(', ') }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 选中角色详情 -->
            <div v-if="selectedInheritanceRole" class="selected-role-details">
              <h4 class="details-title">{{ selectedInheritanceRole.name }} - 权限详情</h4>
              
              <div class="permission-breakdown">
                <div class="direct-permissions">
                  <h5 class="breakdown-title">直接权限</h5>
                  <div class="permission-tags">
                    <el-tag 
                      v-for="permission in selectedInheritanceRole.directPermissions" 
                      :key="permission"
                      size="small"
                      class="permission-tag"
                    >
                      {{ getPermissionName(permission) }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="inherited-permissions" v-if="selectedInheritanceRole.inheritedPermissions?.length > 0">
                  <h5 class="breakdown-title">继承权限</h5>
                  <div class="permission-tags">
                    <el-tag 
                      v-for="permission in selectedInheritanceRole.inheritedPermissions" 
                      :key="permission"
                      size="small"
                      type="info"
                      class="permission-tag"
                    >
                      {{ getPermissionName(permission) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限模板 -->
        <el-tab-pane label="权限模板" name="templates">
          <div class="templates-panel">
            <div class="templates-header">
              <h3 class="text-lg font-semibold text-gray-800">权限模板管理</h3>
              <el-button type="primary" @click="handleCreateTemplate">
                <Plus class="w-4 h-4 mr-2" />
                创建模板
              </el-button>
            </div>
            
            <div class="templates-grid">
              <div 
                v-for="template in permissionTemplates" 
                :key="template.id"
                class="template-card"
              >
                <div class="template-header">
                  <div class="template-info">
                    <h4 class="template-name">{{ template.name }}</h4>
                    <p class="template-description">{{ template.description }}</p>
                  </div>
                  <el-dropdown @command="(cmd) => handleTemplateAction(cmd, template)">
                    <el-button size="small" text>
                      <MoreFilled class="w-4 h-4" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit">编辑模板</el-dropdown-item>
                        <el-dropdown-item command="duplicate">复制模板</el-dropdown-item>
                        <el-dropdown-item command="export">导出模板</el-dropdown-item>
                        <el-dropdown-item divided command="delete">删除模板</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
                
                <div class="template-permissions">
                  <div class="permissions-preview">
                    <el-tag 
                      v-for="permission in template.permissions.slice(0, 4)" 
                      :key="permission"
                      size="small"
                      class="permission-tag"
                    >
                      {{ getPermissionName(permission) }}
                    </el-tag>
                    <span v-if="template.permissions.length > 4" class="more-permissions">
                      +{{ template.permissions.length - 4 }} 更多
                    </span>
                  </div>
                </div>
                
                <div class="template-actions">
                  <el-button size="small" @click="handleApplyTemplate(template)">
                    应用到角色
                  </el-button>
                  <el-button size="small" text @click="handlePreviewTemplate(template)">
                    详细预览
                  </el-button>
                </div>
                
                <div class="template-meta">
                  <div class="meta-item">
                    <Calendar class="w-3 h-3 mr-1" />
                    <span class="meta-text">{{ formatDate(template.created_at) }}</span>
                  </div>
                  <div class="meta-item">
                    <User class="w-3 h-3 mr-1" />
                    <span class="meta-text">{{ template.created_by }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 角色详情对话框 -->
    <el-dialog
      v-model="showRoleDetailDialog"
      :title="`${selectedRoleDetail?.name} - 角色详情`"
      width="800px"
    >
      <div v-if="selectedRoleDetail" class="role-detail-content">
        <div class="role-basic-info">
          <div class="flex items-center mb-4">
            <div 
              class="role-icon-large"
              :class="getRoleIconClass(selectedRoleDetail.key)"
            >
              <component :is="getRoleIcon(selectedRoleDetail.key)" class="w-8 h-8" />
            </div>
            <div class="ml-4">
              <h3 class="text-xl font-semibold">{{ selectedRoleDetail.name }}</h3>
              <p class="text-gray-600">{{ selectedRoleDetail.description }}</p>
              <div class="role-meta">
                <span class="meta-badge">权限等级: {{ selectedRoleDetail.level }}</span>
                <span class="meta-badge">用户数: {{ selectedRoleDetail.userCount || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="role-permissions-detail">
          <h4 class="detail-section-title">权限详情</h4>
          <div class="permissions-categories">
            <div 
              v-for="category in permissionCategories" 
              :key="category.key"
              class="permission-category"
            >
              <h5 class="category-title">{{ category.name }}</h5>
              <div class="category-permissions">
                <div 
                  v-for="permission in category.permissions" 
                  :key="permission.key"
                  class="permission-item"
                  :class="{ 
                    'permission-enabled': rolePermissions[selectedRoleDetail.key]?.[permission.key],
                    'permission-inherited': hasInheritedPermission(selectedRoleDetail.key, permission.key)
                  }"
                >
                  <div class="permission-status">
                    <Check v-if="rolePermissions[selectedRoleDetail.key]?.[permission.key]" class="w-4 h-4 text-green-500" />
                    <Close v-else class="w-4 h-4 text-gray-400" />
                  </div>
                  <div class="permission-info">
                    <div class="permission-name">{{ permission.name }}</div>
                    <div class="permission-desc">{{ permission.description }}</div>
                  </div>
                  <div v-if="hasInheritedPermission(selectedRoleDetail.key, permission.key)" class="inherited-badge">
                    继承
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload, Download, Check, User, Lock, Edit, Clock,
  InfoFilled, Link, RefreshLeft, View, Refresh, Plus,
  MoreFilled, Calendar, Avatar, CheckCircle, Close,
  CopyDocument
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const activeTab = ref('matrix')
const viewMode = ref('detailed')
const selectedRoleGroup = ref('')
const savingChanges = ref(false)
const modifiedRoles = ref(new Set())
const selectedInheritanceRole = ref(null)
const showRoleDetailDialog = ref(false)
const selectedRoleDetail = ref(null)

// 统计数据
const roleStats = ref({
  totalRoles: 7,
  totalPermissions: 24,
  modifiedRoles: 0,
  lastModified: '2小时前'
})

// 角色定义
const roleDefinitions = {
  supplier: {
    key: 'supplier',
    name: '供应商',
    description: '航材制造商，专注产品销售',
    level: 1,
    userCount: 15,
    inheritsFrom: [],
    group: 'business'
  },
  distributor: {
    key: 'distributor',
    name: '分销商',
    description: '航材贸易商，连接供需两端',
    level: 1,
    userCount: 8,
    inheritsFrom: ['supplier'],
    group: 'business'
  },
  airline: {
    key: 'airline',
    name: '航空公司',
    description: '航材最终使用方',
    level: 1,
    userCount: 12,
    inheritsFrom: [],
    group: 'business'
  },
  platform_staff: {
    key: 'platform_staff',
    name: '平台员工',
    description: '平台日常运营管理',
    level: 2,
    userCount: 6,
    inheritsFrom: [],
    group: 'management'
  },
  maintenance_engineer: {
    key: 'maintenance_engineer',
    name: '维修工程师',
    description: '航材维修技术支持',
    level: 1,
    userCount: 4,
    inheritsFrom: [],
    group: 'service'
  },
  logistics_specialist: {
    key: 'logistics_specialist',
    name: '物流专员',
    description: '航材物流配送管理',
    level: 1,
    userCount: 3,
    inheritsFrom: [],
    group: 'service'
  },
  admin: {
    key: 'admin',
    name: '系统管理员',
    description: '系统配置和用户管理',
    level: 3,
    userCount: 2,
    inheritsFrom: ['platform_staff'],
    group: 'management'
  }
}

// 权限定义
const permissionDefinitions = {
  view_own_data: { key: 'view_own_data', name: '查看自有数据', description: '查看本公司/个人的数据', category: 'basic' },
  manage_inventory: { key: 'manage_inventory', name: '管理库存', description: '管理航材库存信息', category: 'inventory' },
  publish_demand: { key: 'publish_demand', name: '发布需求', description: '发布航材采购需求', category: 'trading' },
  publish_shared: { key: 'publish_shared', name: '发布共享件', description: '发布可共享的航材', category: 'trading' },
  manage_orders: { key: 'manage_orders', name: '管理订单', description: '管理采购和销售订单', category: 'trading' },
  manage_sales_orders: { key: 'manage_sales_orders', name: '管理销售订单', description: '管理销售相关订单', category: 'trading' },
  manage_purchase_orders: { key: 'manage_purchase_orders', name: '管理采购订单', description: '管理采购相关订单', category: 'trading' },
  respond_to_inquiry: { key: 'respond_to_inquiry', name: '响应询价', description: '回复客户询价请求', category: 'trading' },
  trade_analysis: { key: 'trade_analysis', name: '贸易分析', description: '查看贸易数据分析', category: 'analytics' },
  maintenance_management: { key: 'maintenance_management', name: '维修管理', description: '管理维修工单和进度', category: 'maintenance' },
  logistics_tracking: { key: 'logistics_tracking', name: '物流跟踪', description: '跟踪物流配送状态', category: 'logistics' },
  user_management: { key: 'user_management', name: '用户管理', description: '管理系统用户', category: 'admin' },
  system_config: { key: 'system_config', name: '系统配置', description: '配置系统参数', category: 'admin' },
  data_analytics: { key: 'data_analytics', name: '数据分析', description: '查看系统数据分析', category: 'analytics' }
}

// 权限分类
const permissionCategories = [
  {
    key: 'basic',
    name: '基础权限',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'basic')
  },
  {
    key: 'inventory',
    name: '库存管理',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'inventory')
  },
  {
    key: 'trading',
    name: '交易管理',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'trading')
  },
  {
    key: 'maintenance',
    name: '维修管理',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'maintenance')
  },
  {
    key: 'logistics',
    name: '物流管理',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'logistics')
  },
  {
    key: 'analytics',
    name: '数据分析',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'analytics')
  },
  {
    key: 'admin',
    name: '系统管理',
    permissions: Object.values(permissionDefinitions).filter(p => p.category === 'admin')
  }
]

// 角色权限矩阵
const rolePermissions = reactive({
  supplier: {
    view_own_data: true,
    manage_inventory: true,
    publish_shared: true,
    manage_sales_orders: true,
    respond_to_inquiry: true
  },
  distributor: {
    view_own_data: true,
    manage_inventory: true,
    publish_demand: true,
    publish_shared: true,
    manage_orders: true,
    manage_sales_orders: true,
    manage_purchase_orders: true,
    respond_to_inquiry: true,
    trade_analysis: true
  },
  airline: {
    view_own_data: true,
    publish_demand: true,
    manage_purchase_orders: true
  },
  platform_staff: {
    view_own_data: true,
    user_management: true,
    data_analytics: true
  },
  maintenance_engineer: {
    view_own_data: true,
    maintenance_management: true
  },
  logistics_specialist: {
    view_own_data: true,
    logistics_tracking: true
  },
  admin: {
    view_own_data: true,
    user_management: true,
    system_config: true,
    data_analytics: true
  }
})

// 权限模板
const permissionTemplates = ref([
  {
    id: 1,
    name: '标准供应商模板',
    description: '适用于一般航材供应商的标准权限配置',
    permissions: ['view_own_data', 'manage_inventory', 'publish_shared', 'manage_sales_orders', 'respond_to_inquiry'],
    created_at: '2025-07-20T10:00:00',
    created_by: 'admin'
  },
  {
    id: 2,
    name: '高级分销商模板',
    description: '适用于大型分销商的全功能权限配置',
    permissions: ['view_own_data', 'manage_inventory', 'publish_demand', 'publish_shared', 'manage_orders', 'trade_analysis'],
    created_at: '2025-07-21T14:30:00',
    created_by: 'admin'
  },
  {
    id: 3,
    name: '平台管理模板',
    description: '适用于平台内部管理人员的权限配置',
    permissions: ['view_own_data', 'user_management', 'data_analytics', 'system_config'],
    created_at: '2025-07-22T09:15:00',
    created_by: 'super_admin'
  }
])

// 计算属性
const displayedRoles = computed(() => {
  const roles = Object.values(roleDefinitions)
  if (selectedRoleGroup.value) {
    return roles.filter(role => role.group === selectedRoleGroup.value)
  }
  return roles
})

const displayedPermissions = computed(() => {
  return Object.values(permissionDefinitions)
})

const inheritanceTree = computed(() => {
  const levels = [
    {
      level: 1,
      description: '业务用户 - 基础业务功能权限',
      roles: Object.values(roleDefinitions).filter(r => r.level === 1)
    },
    {
      level: 2,
      description: '平台员工 - 运营管理权限',
      roles: Object.values(roleDefinitions).filter(r => r.level === 2)
    },
    {
      level: 3,
      description: '系统管理员 - 系统配置权限',
      roles: Object.values(roleDefinitions).filter(r => r.level === 3)
    }
  ]
  
  // 为每个角色添加权限信息
  levels.forEach(level => {
    level.roles.forEach(role => {
      role.permissions = Object.keys(rolePermissions[role.key] || {}).filter(key => rolePermissions[role.key][key])
      role.directPermissions = role.permissions.filter(p => !hasInheritedPermission(role.key, p))
      role.inheritedPermissions = role.permissions.filter(p => hasInheritedPermission(role.key, p))
    })
  })
  
  return levels
})

// 方法
const getRoleIcon = (roleKey) => {
  const iconMap = {
    supplier: 'Factory',
    distributor: 'Truck',
    airline: 'Plane',
    platform_staff: 'User',
    maintenance_engineer: 'Wrench',
    logistics_specialist: 'Package',
    admin: 'Crown'
  }
  return iconMap[roleKey] || 'User'
}

const getRoleIconClass = (roleKey) => {
  const classMap = {
    supplier: 'bg-green-100 text-green-600',
    distributor: 'bg-purple-100 text-purple-600',
    airline: 'bg-blue-100 text-blue-600',
    platform_staff: 'bg-gray-100 text-gray-600',
    maintenance_engineer: 'bg-yellow-100 text-yellow-600',
    logistics_specialist: 'bg-orange-100 text-orange-600',
    admin: 'bg-red-100 text-red-600'
  }
  return classMap[roleKey] || 'bg-gray-100 text-gray-600'
}

const getPermissionName = (permissionKey) => {
  return permissionDefinitions[permissionKey]?.name || permissionKey
}

const canModifyPermission = (roleKey, permissionKey) => {
  const role = roleDefinitions[roleKey]
  const permission = permissionDefinitions[permissionKey]
  
  // 超级管理员可以修改所有权限
  if (userStore.user?.permission_level >= 4) return true
  
  // 管理员不能授予管理权限给比自己高级的角色
  if (userStore.user?.permission_level < role.level) return false
  
  // 管理员不能授予系统管理权限
  if (permission.category === 'admin' && userStore.user?.permission_level < 4) return false
  
  return true
}

const hasInheritedPermission = (roleKey, permissionKey) => {
  const role = roleDefinitions[roleKey]
  if (!role.inheritsFrom.length) return false
  
  // 检查是否从父角色继承了该权限
  return role.inheritsFrom.some(parentKey => {
    return rolePermissions[parentKey]?.[permissionKey] === true
  })
}

const getTotalPermissions = () => {
  let total = 0
  Object.values(rolePermissions).forEach(permissions => {
    total += Object.keys(permissions).length
  })
  return total
}

const getEnabledPermissions = () => {
  let enabled = 0
  Object.values(rolePermissions).forEach(permissions => {
    enabled += Object.values(permissions).filter(p => p === true).length
  })
  return enabled
}

const getDisabledPermissions = () => {
  return getTotalPermissions() - getEnabledPermissions()
}

const getInheritedPermissions = () => {
  let inherited = 0
  Object.keys(rolePermissions).forEach(roleKey => {
    Object.keys(rolePermissions[roleKey]).forEach(permissionKey => {
      if (hasInheritedPermission(roleKey, permissionKey)) {
        inherited++
      }
    })
  })
  return inherited
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 事件处理方法
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}

const setViewMode = (mode) => {
  viewMode.value = mode
}

const handleExpandAll = () => {
  ElMessage.info('展开所有权限分组')
}

const handleCollapseAll = () => {
  ElMessage.info('收起所有权限分组')
}

const handleResetMatrix = async () => {
  try {
    await ElMessageBox.confirm('确认要重置权限矩阵吗？这将撤销所有未保存的变更。', '重置确认', {
      type: 'warning'
    })
    
    // 重置权限矩阵
    modifiedRoles.value.clear()
    roleStats.value.modifiedRoles = 0
    ElMessage.success('权限矩阵已重置')
  } catch {
    // 用户取消
  }
}

const handlePermissionChange = (roleKey, permissionKey) => {
  modifiedRoles.value.add(roleKey)
  roleStats.value.modifiedRoles = modifiedRoles.value.size
  console.log(`权限变更: ${roleKey} - ${permissionKey}`)
}

const handleCopyRole = (roleKey) => {
  ElMessage.info(`复制角色 ${roleDefinitions[roleKey].name} 的权限配置`)
}

const handleResetRole = async (roleKey) => {
  try {
    await ElMessageBox.confirm(`确认要重置角色 ${roleDefinitions[roleKey].name} 的权限吗？`, '重置角色', {
      type: 'warning'
    })
    
    modifiedRoles.value.delete(roleKey)
    roleStats.value.modifiedRoles = modifiedRoles.value.size
    ElMessage.success('角色权限已重置')
  } catch {
    // 用户取消
  }
}

const handleRoleDetail = (roleKey) => {
  selectedRoleDetail.value = roleDefinitions[roleKey]
  showRoleDetailDialog.value = true
}

const handleSelectInheritanceRole = (role) => {
  selectedInheritanceRole.value = role
}

const handleRefreshInheritance = () => {
  ElMessage.success('继承关系图已刷新')
}

const handleEditInheritance = () => {
  ElMessage.info('编辑继承关系功能开发中...')
}

const handleSaveChanges = async () => {
  if (modifiedRoles.value.size === 0) {
    ElMessage.warning('没有权限变更需要保存')
    return
  }
  
  savingChanges.value = true
  try {
    // 这里应该调用API保存权限变更
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    modifiedRoles.value.clear()
    roleStats.value.modifiedRoles = 0
    roleStats.value.lastModified = '刚刚'
    ElMessage.success('权限变更已保存')
  } catch (error) {
    ElMessage.error('权限变更保存失败')
  } finally {
    savingChanges.value = false
  }
}

const handleImportRoles = () => {
  ElMessage.info('导入权限配置功能开发中...')
}

const handleExportRoles = () => {
  ElMessage.info('导出权限配置功能开发中...')
}

const handleCreateTemplate = () => {
  ElMessage.info('创建权限模板功能开发中...')
}

const handleTemplateAction = (command, template) => {
  ElMessage.info(`模板${command}功能开发中...`)
}

const handleApplyTemplate = (template) => {
  ElMessage.info(`应用模板 ${template.name} 功能开发中...`)
}

const handlePreviewTemplate = (template) => {
  ElMessage.info(`预览模板 ${template.name} 功能开发中...`)
}

// 组件挂载时初始化数据
onMounted(() => {
  console.log('角色权限管理页面已加载')
})
</script>

<style scoped>
.role-management {
  @apply space-y-6;
}

.page-header {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-gray-600 mt-1;
}

.header-actions {
  @apply flex space-x-3;
}

.roles-overview {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.overview-card {
  @apply bg-gray-50 rounded-lg p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.card-value {
  @apply text-2xl font-bold text-gray-800;
}

.card-label {
  @apply text-sm text-gray-600 mt-1;
}

.main-content {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.permission-matrix-panel {
  @apply p-6 space-y-6;
}

.matrix-toolbar {
  @apply flex items-center justify-between bg-gray-50 rounded-lg p-4;
}

.toolbar-left {
  @apply flex items-center;
}

.toolbar-right {
  @apply flex items-center space-x-3;
}

.matrix-container {
  @apply overflow-x-auto border border-gray-200 rounded-lg;
}

.matrix-table-wrapper {
  @apply min-w-full;
}

.permission-matrix-table {
  @apply w-full table-auto;
}

.permission-matrix-table th {
  @apply bg-gray-50 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky top-0;
}

.permission-matrix-table td {
  @apply px-4 py-3 whitespace-nowrap border-b border-gray-200;
}

.role-header {
  @apply w-48 min-w-0;
}

.permission-header {
  @apply w-32 text-center;
}

.permission-header-content {
  @apply flex items-center justify-center;
}

.permission-name {
  @apply text-xs;
}

.actions-header {
  @apply w-24 text-center;
}

.role-row {
  @apply hover:bg-gray-50 transition-colors;
}

.role-row.role-modified {
  @apply bg-yellow-50 border-l-4 border-yellow-400;
}

.role-cell {
  @apply align-top;
}

.role-info {
  @apply flex items-start space-x-3;
}

.role-icon-wrapper {
  @apply flex-shrink-0;
}

.role-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.role-details {
  @apply flex-1 min-w-0;
}

.role-name {
  @apply font-medium text-gray-800;
}

.role-desc {
  @apply text-xs text-gray-600 mt-1;
}

.role-level {
  @apply text-xs text-blue-600 mt-1;
}

.permission-cell {
  @apply text-center align-middle;
}

.permission-control {
  @apply flex items-center justify-center space-x-1;
}

.inherited-indicator {
  @apply flex items-center justify-center;
}

.actions-cell {
  @apply text-center align-middle;
}

.role-actions {
  @apply flex items-center justify-center space-x-1;
}

.matrix-summary {
  @apply bg-gray-50 rounded-lg p-4;
}

.summary-cards {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.summary-card {
  @apply text-center;
}

.summary-label {
  @apply text-sm text-gray-600;
}

.summary-value {
  @apply text-xl font-bold text-gray-800 mt-1;
}

.inheritance-panel {
  @apply p-6 space-y-6;
}

.inheritance-header {
  @apply flex items-center justify-between;
}

.inheritance-controls {
  @apply flex items-center space-x-3;
}

.inheritance-visualization {
  @apply border border-gray-200 rounded-lg p-6;
}

.inheritance-tree {
  @apply space-y-8;
}

.inheritance-level {
  @apply space-y-4;
}

.level-header {
  @apply text-center;
}

.level-title {
  @apply text-lg font-semibold text-gray-800;
}

.level-description {
  @apply text-sm text-gray-600;
}

.level-roles {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.inheritance-role-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow;
}

.role-header {
  @apply flex items-center space-x-3;
}

.role-icon-large {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.role-info {
  @apply flex-1;
}

.role-count {
  @apply text-xs text-gray-500;
}

.inheritance-connections {
  @apply mt-3 pt-3 border-t border-gray-200;
}

.inheritance-arrow {
  @apply text-center text-gray-400 text-lg;
}

.inherited-roles {
  @apply text-xs text-gray-600 text-center;
}

.selected-role-details {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6;
}

.details-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.permission-breakdown {
  @apply space-y-4;
}

.breakdown-title {
  @apply text-sm font-medium text-gray-700 mb-2;
}

.permission-tags {
  @apply flex flex-wrap gap-2;
}

.permission-tag {
  @apply text-xs;
}

.templates-panel {
  @apply p-6 space-y-6;
}

.templates-header {
  @apply flex items-center justify-between;
}

.templates-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.template-card {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-4;
}

.template-header {
  @apply flex items-start justify-between;
}

.template-info {
  @apply flex-1;
}

.template-name {
  @apply font-semibold text-gray-800;
}

.template-description {
  @apply text-sm text-gray-600 mt-1;
}

.template-permissions {
  @apply space-y-2;
}

.permissions-preview {
  @apply flex flex-wrap gap-2;
}

.more-permissions {
  @apply text-xs text-gray-500;
}

.template-actions {
  @apply flex items-center space-x-2;
}

.template-meta {
  @apply flex items-center justify-between text-xs text-gray-500;
}

.meta-item {
  @apply flex items-center;
}

.meta-text {
  @apply ml-1;
}

.meta-badge {
  @apply text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full mr-2;
}

.role-detail-content {
  @apply space-y-6;
}

.role-basic-info {
  @apply border-b border-gray-200 pb-4;
}

.role-meta {
  @apply flex items-center space-x-2 mt-2;
}

.role-permissions-detail {
  @apply space-y-4;
}

.detail-section-title {
  @apply text-lg font-semibold text-gray-800;
}

.permissions-categories {
  @apply space-y-6;
}

.permission-category {
  @apply space-y-3;
}

.category-title {
  @apply text-sm font-medium text-gray-700 border-b border-gray-200 pb-2;
}

.category-permissions {
  @apply space-y-2;
}

.permission-item {
  @apply flex items-center space-x-3 p-2 rounded-lg;
}

.permission-item.permission-enabled {
  @apply bg-green-50 border border-green-200;
}

.permission-item.permission-inherited {
  @apply bg-blue-50 border border-blue-200;
}

.permission-status {
  @apply flex-shrink-0;
}

.permission-info {
  @apply flex-1;
}

.permission-desc {
  @apply text-xs text-gray-600;
}

.inherited-badge {
  @apply text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full;
}
</style>