<!--
数据恢复管理
版本: 1.0
创建时间: 2025-07-23

超级管理员专用的数据恢复管理界面
特点：
1. 数据库备份和恢复操作
2. 数据迁移和同步功能
3. 系统配置备份恢复
4. 紧急数据恢复流程
5. 恢复操作审计和记录
-->

<template>
  <div class="data-recovery">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">数据恢复管理</h1>
          <p class="page-subtitle">管理系统数据备份和恢复，确保数据安全和业务连续性</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleCreateBackup">
            <Database class="w-4 h-4 mr-2" />
            创建备份
          </el-button>
          <el-button @click="handleRefreshData">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleEmergencyRestore" type="danger">
            <Warning class="w-4 h-4 mr-2" />
            紧急恢复
          </el-button>
        </div>
      </div>
    </div>

    <!-- 恢复状态概览 -->
    <div class="recovery-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card backup-status">
          <div class="card-icon bg-green-100 text-green-600">
            <Archive class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ recoveryStatus.totalBackups }}</div>
            <div class="card-label">备份总数</div>
            <div class="card-detail">最新: {{ recoveryStatus.latestBackup }}</div>
          </div>
        </div>
        
        <div class="overview-card backup-size">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Coin class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ recoveryStatus.totalSize }}</div>
            <div class="card-label">备份总大小</div>
            <div class="card-detail">平均: {{ recoveryStatus.avgSize }}</div>
          </div>
        </div>
        
        <div class="overview-card recovery_operations">
          <div class="card-icon bg-orange-100 text-orange-600">
            <RefreshLeft class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ recoveryStatus.recoveryOperations }}</div>
            <div class="card-label">恢复操作次数</div>
            <div class="card-detail">成功率: {{ recoveryStatus.successRate }}%</div>
          </div>
        </div>
        
        <div class="overview-card storage_health">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Monitor class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ recoveryStatus.storageHealth }}%</div>
            <div class="card-label">存储健康度</div>
            <div class="card-detail">{{ recoveryStatus.freeSpace }} 可用空间</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeRecoveryTab" @tab-change="handleTabChange">
        
        <!-- 备份管理 -->
        <el-tab-pane label="备份管理" name="backup">
          <div class="recovery-panel">
            <!-- 备份操作区域 -->
            <div class="backup-controls">
              <div class="control-section">
                <h3 class="section-title">备份操作</h3>
                <div class="backup-options">
                  <div class="backup-type">
                    <label class="option-label">备份类型</label>
                    <el-radio-group v-model="backupOptions.type">
                      <el-radio value="full">完整备份</el-radio>
                      <el-radio value="incremental">增量备份</el-radio>
                      <el-radio value="differential">差异备份</el-radio>
                    </el-radio-group>
                  </div>
                  
                  <div class="backup-target">
                    <label class="option-label">备份目标</label>
                    <el-checkbox-group v-model="backupOptions.targets">
                      <el-checkbox value="database">数据库</el-checkbox>
                      <el-checkbox value="files">文件系统</el-checkbox>
                      <el-checkbox value="config">配置文件</el-checkbox>
                      <el-checkbox value="logs">日志文件</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  
                  <div class="backup-compression">
                    <label class="option-label">压缩选项</label>
                    <el-switch v-model="backupOptions.compression" />
                    <span class="option-desc">启用压缩可节省存储空间</span>
                  </div>
                  
                  <div class="backup-encryption">
                    <label class="option-label">加密备份</label>
                    <el-switch v-model="backupOptions.encryption" />
                    <span class="option-desc">加密保护敏感数据</span>
                  </div>
                </div>
                
                <div class="backup-actions">
                  <el-button @click="handleStartBackup" type="primary" :loading="backupProgress.running">
                    {{ backupProgress.running ? '备份中...' : '开始备份' }}
                  </el-button>
                  <el-button @click="handleScheduleBackup">定时备份</el-button>
                  <el-button @click="handleCancelBackup" v-if="backupProgress.running">取消备份</el-button>
                </div>
                
                <!-- 备份进度 -->
                <div v-if="backupProgress.running" class="backup-progress">
                  <div class="progress-info">
                    <span>{{ backupProgress.currentTask }}</span>
                    <span>{{ backupProgress.percent }}%</span>
                  </div>
                  <el-progress :percentage="backupProgress.percent" :status="backupProgress.status" />
                  <div class="progress-details">
                    <span>已处理: {{ backupProgress.processed }}/{{ backupProgress.total }}</span>
                    <span>预计剩余: {{ backupProgress.timeRemaining }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 备份列表 -->
            <div class="backup-list">
              <div class="list-header">
                <h3 class="section-title">备份历史</h3>
                <div class="list-controls">
                  <el-input 
                    v-model="backupSearch" 
                    placeholder="搜索备份..." 
                    size="small"
                    style="width: 200px;"
                  >
                    <template #prefix>
                      <Search class="w-4 h-4" />
                    </template>
                  </el-input>
                  <el-select v-model="backupFilter" placeholder="筛选类型" size="small" style="width: 120px;">
                    <el-option label="全部" value="all" />
                    <el-option label="完整备份" value="full" />
                    <el-option label="增量备份" value="incremental" />
                    <el-option label="差异备份" value="differential" />
                  </el-select>
                </div>
              </div>
              
              <div class="backup-table">
                <el-table :data="filteredBackups" style="width: 100%">
                  <el-table-column prop="id" label="备份ID" width="150" />
                  <el-table-column prop="type" label="类型" width="100">
                    <template #default="scope">
                      <el-tag :type="getBackupTypeColor(scope.row.type)">
                        {{ getBackupTypeName(scope.row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" width="180" />
                  <el-table-column prop="size" label="大小" width="100" />
                  <el-table-column prop="duration" label="耗时" width="100" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                        {{ scope.row.status === 'success' ? '成功' : '失败' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="说明" />
                  <el-table-column label="操作" width="200">
                    <template #default="scope">
                      <el-button size="small" @click="handleRestoreFromBackup(scope.row)">恢复</el-button>
                      <el-button size="small" @click="handleDownloadBackup(scope.row)">下载</el-button>
                      <el-button size="small" @click="handleViewBackupDetails(scope.row)">详情</el-button>
                      <el-button size="small" type="danger" @click="handleDeleteBackup(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据恢复 -->
        <el-tab-pane label="数据恢复" name="restore">
          <div class="recovery-panel">
            <div class="restore-wizard">
              <div class="wizard-steps">
                <el-steps :active="restoreStep" finish-status="success">
                  <el-step title="选择备份" />
                  <el-step title="恢复选项" />
                  <el-step title="确认操作" />
                  <el-step title="执行恢复" />
                </el-steps>
              </div>

              <!-- 步骤1: 选择备份 -->
              <div v-if="restoreStep === 0" class="wizard-step">
                <h3 class="step-title">选择要恢复的备份</h3>
                <div class="backup-selection">
                  <el-table 
                    :data="backupList" 
                    @current-change="handleSelectBackup"
                    highlight-current-row
                    style="width: 100%"
                  >
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="id" label="备份ID" width="150" />
                    <el-table-column prop="type" label="类型" width="100" />
                    <el-table-column prop="createTime" label="创建时间" width="180" />
                    <el-table-column prop="size" label="大小" width="100" />
                    <el-table-column prop="description" label="说明" />
                  </el-table>
                </div>
                <div class="step-actions">
                  <el-button @click="restoreStep = 1" type="primary" :disabled="!selectedBackup">下一步</el-button>
                </div>
              </div>

              <!-- 步骤2: 恢复选项 -->
              <div v-if="restoreStep === 1" class="wizard-step">
                <h3 class="step-title">配置恢复选项</h3>
                <div class="restore-options">
                  <div class="option-group">
                    <label class="option-label">恢复目标</label>
                    <el-radio-group v-model="restoreOptions.target">
                      <el-radio value="current">当前系统</el-radio>
                      <el-radio value="new">新环境</el-radio>
                      <el-radio value="test">测试环境</el-radio>
                    </el-radio-group>
                  </div>
                  
                  <div class="option-group">
                    <label class="option-label">恢复范围</label>
                    <el-checkbox-group v-model="restoreOptions.scope">
                      <el-checkbox value="database">数据库</el-checkbox>
                      <el-checkbox value="files">文件系统</el-checkbox>
                      <el-checkbox value="config">配置文件</el-checkbox>
                    </el-checkbox-group>
                  </div>
                  
                  <div class="option-group">
                    <label class="option-label">覆盖策略</label>
                    <el-select v-model="restoreOptions.overwrite">
                      <el-option label="覆盖所有数据" value="all" />
                      <el-option label="仅覆盖不存在的数据" value="missing" />
                      <el-option label="备份现有数据后覆盖" value="backup" />
                    </el-select>
                  </div>
                  
                  <div class="option-group">
                    <label class="option-label">验证选项</label>
                    <el-switch v-model="restoreOptions.verify" />
                    <span class="option-desc">恢复后验证数据完整性</span>
                  </div>
                </div>
                <div class="step-actions">
                  <el-button @click="restoreStep = 0">上一步</el-button>
                  <el-button @click="restoreStep = 2" type="primary">下一步</el-button>
                </div>
              </div>

              <!-- 步骤3: 确认操作 -->
              <div v-if="restoreStep === 2" class="wizard-step">
                <h3 class="step-title">确认恢复操作</h3>
                <div class="restore-confirmation">
                  <el-alert
                    title="警告：数据恢复是一个不可逆的操作"
                    type="warning"
                    description="请仔细检查以下配置，确认无误后再执行恢复操作。建议在操作前创建当前数据的备份。"
                    show-icon
                    :closable="false"
                  />
                  
                  <div class="confirmation-details">
                    <div class="detail-item">
                      <label>选择的备份：</label>
                      <span>{{ selectedBackup?.id }} ({{ selectedBackup?.createTime }})</span>
                    </div>
                    <div class="detail-item">
                      <label>恢复目标：</label>
                      <span>{{ getTargetName(restoreOptions.target) }}</span>
                    </div>
                    <div class="detail-item">
                      <label>恢复范围：</label>
                      <span>{{ restoreOptions.scope.join(', ') }}</span>
                    </div>
                    <div class="detail-item">
                      <label>覆盖策略：</label>
                      <span>{{ getOverwriteName(restoreOptions.overwrite) }}</span>
                    </div>
                  </div>
                  
                  <div class="confirmation-checkbox">
                    <el-checkbox v-model="confirmRestore">
                      我已仔细阅读并理解上述警告，确认执行数据恢复操作
                    </el-checkbox>
                  </div>
                </div>
                <div class="step-actions">
                  <el-button @click="restoreStep = 1">上一步</el-button>
                  <el-button @click="restoreStep = 3" type="danger" :disabled="!confirmRestore">开始恢复</el-button>
                </div>
              </div>

              <!-- 步骤4: 执行恢复 -->
              <div v-if="restoreStep === 3" class="wizard-step">
                <h3 class="step-title">正在执行恢复操作</h3>
                <div class="restore-progress">
                  <div class="progress-info">
                    <span>{{ restoreProgress.currentTask }}</span>
                    <span>{{ restoreProgress.percent }}%</span>
                  </div>
                  <el-progress :percentage="restoreProgress.percent" :status="restoreProgress.status" />
                  <div class="progress-details">
                    <span>已处理: {{ restoreProgress.processed }}/{{ restoreProgress.total }}</span>
                    <span>预计剩余: {{ restoreProgress.timeRemaining }}</span>
                  </div>
                  
                  <div class="progress-logs">
                    <h4>执行日志</h4>
                    <div class="log-content">
                      <div v-for="(log, index) in restoreProgress.logs" :key="index" class="log-item">
                        <span class="log-time">{{ log.time }}</span>
                        <span class="log-message">{{ log.message }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-if="restoreProgress.completed" class="restore-result">
                  <el-result
                    :icon="restoreProgress.success ? 'success' : 'error'"
                    :title="restoreProgress.success ? '恢复成功' : '恢复失败'"
                    :sub-title="restoreProgress.message"
                  >
                    <template #extra>
                      <el-button @click="handleResetWizard">重新开始</el-button>
                      <el-button v-if="restoreProgress.success" type="primary" @click="handleViewResult">查看结果</el-button>
                    </template>
                  </el-result>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 恢复记录 -->
        <el-tab-pane label="恢复记录" name="history">
          <div class="recovery-panel">
            <div class="history-controls">
              <div class="control-section">
                <h3 class="section-title">恢复历史记录</h3>
                <div class="history-filters">
                  <el-date-picker
                    v-model="historyDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                  />
                  <el-select v-model="historyStatus" placeholder="状态筛选" size="small" style="width: 120px;">
                    <el-option label="全部" value="all" />
                    <el-option label="成功" value="success" />
                    <el-option label="失败" value="failed" />
                    <el-option label="进行中" value="running" />
                  </el-select>
                  <el-button @click="handleExportHistory" size="small">导出记录</el-button>
                </div>
              </div>
            </div>
            
            <div class="history-table">
              <el-table :data="recoveryHistory" style="width: 100%">
                <el-table-column prop="id" label="恢复ID" width="150" />
                <el-table-column prop="backupId" label="备份ID" width="150" />
                <el-table-column prop="operator" label="操作员" width="120" />
                <el-table-column prop="startTime" label="开始时间" width="180" />
                <el-table-column prop="endTime" label="结束时间" width="180" />
                <el-table-column prop="duration" label="耗时" width="100" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusColor(scope.row.status)">
                      {{ getStatusName(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="result" label="结果说明" />
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewDetails(scope.row)">详情</el-button>
                    <el-button size="small" @click="handleDownloadLog(scope.row)">日志</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Database, Refresh, Warning, Archive, Coin, RefreshLeft, Monitor, Search } from 'lucide-vue-next'

// 当前激活的数据恢复标签
const activeRecoveryTab = ref('backup')

// 恢复状态概览
const recoveryStatus = reactive({
  totalBackups: 45,
  latestBackup: '2小时前',
  totalSize: '125.6GB',
  avgSize: '2.8GB',
  recoveryOperations: 12,
  successRate: 95,
  storageHealth: 88,
  freeSpace: '1.2TB'
})

// 备份选项
const backupOptions = reactive({
  type: 'full',
  targets: ['database', 'config'],
  compression: true,
  encryption: false
})

// 备份进度
const backupProgress = reactive({
  running: false,
  percent: 0,
  status: '',
  currentTask: '',
  processed: 0,
  total: 0,
  timeRemaining: ''
})

// 恢复向导步骤
const restoreStep = ref(0)
const selectedBackup = ref(null)
const confirmRestore = ref(false)

// 恢复选项
const restoreOptions = reactive({
  target: 'current',
  scope: ['database'],
  overwrite: 'backup',
  verify: true
})

// 恢复进度
const restoreProgress = reactive({
  percent: 0,
  status: '',
  currentTask: '',
  processed: 0,
  total: 0,
  timeRemaining: '',
  completed: false,
  success: false,
  message: '',
  logs: []
})

// 备份搜索和筛选
const backupSearch = ref('')
const backupFilter = ref('all')

// 历史记录筛选
const historyDateRange = ref([])
const historyStatus = ref('all')

// 备份列表数据
const backupList = ref([
  {
    id: 'backup_20250723_020000',
    type: 'full',
    createTime: '2025-07-23 02:00:00',
    size: '2.5GB',
    duration: '25分钟',
    status: 'success',
    description: '每日自动完整备份'
  },
  {
    id: 'backup_20250722_140000',
    type: 'incremental',
    createTime: '2025-07-22 14:00:00',
    size: '156MB',
    duration: '3分钟',
    status: 'success',
    description: '增量备份 - 用户数据更新'
  },
  {
    id: 'backup_20250722_020000',
    type: 'full',
    createTime: '2025-07-22 02:00:00',
    size: '2.4GB',
    duration: '23分钟',
    status: 'success',
    description: '每日自动完整备份'
  },
  {
    id: 'backup_20250721_020000',
    type: 'full',
    createTime: '2025-07-21 02:00:00',
    size: '2.3GB',
    duration: '22分钟',
    status: 'failed',
    description: '备份失败 - 磁盘空间不足'
  }
])

// 恢复历史记录
const recoveryHistory = ref([
  {
    id: 'restore_001',
    backupId: 'backup_20250720_020000',
    operator: 'admin',
    startTime: '2025-07-22 10:30:00',
    endTime: '2025-07-22 10:45:00',
    duration: '15分钟',
    status: 'success',
    result: '成功恢复用户数据和配置文件'
  },
  {
    id: 'restore_002',
    backupId: 'backup_20250718_020000',
    operator: 'admin',
    startTime: '2025-07-20 16:20:00',
    endTime: '2025-07-20 16:25:00',
    duration: '5分钟',
    status: 'failed',
    result: '恢复失败 - 备份文件损坏'
  }
])

// 计算属性：过滤后的备份列表
const filteredBackups = computed(() => {
  let filtered = backupList.value
  
  if (backupSearch.value) {
    filtered = filtered.filter(backup => 
      backup.id.includes(backupSearch.value) || 
      backup.description.includes(backupSearch.value)
    )
  }
  
  if (backupFilter.value !== 'all') {
    filtered = filtered.filter(backup => backup.type === backupFilter.value)
  }
  
  return filtered
})

// 组件挂载时加载数据
onMounted(() => {
  loadRecoveryData()
})

// 加载恢复数据
function loadRecoveryData() {
  console.log('加载数据恢复信息')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到数据恢复标签:', tabName)
}

// 创建备份
function handleCreateBackup() {
  ElMessage.info('创建数据备份功能')
}

// 刷新数据
function handleRefreshData() {
  loadRecoveryData()
  ElMessage.success('数据已刷新')
}

// 紧急恢复
function handleEmergencyRestore() {
  ElMessageBox.confirm('紧急恢复将中断当前服务，确定要继续吗？', '紧急恢复确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('紧急恢复流程已启动')
  })
}

// 开始备份
function handleStartBackup() {
  backupProgress.running = true
  backupProgress.percent = 0
  backupProgress.currentTask = '正在初始化备份...'
  
  // 模拟备份进度
  const interval = setInterval(() => {
    backupProgress.percent += Math.random() * 10
    if (backupProgress.percent >= 100) {
      backupProgress.percent = 100
      backupProgress.running = false
      backupProgress.status = 'success'
      clearInterval(interval)
      ElMessage.success('备份创建成功')
    }
  }, 1000)
}

// 定时备份
function handleScheduleBackup() {
  ElMessage.info('定时备份配置功能')
}

// 取消备份
function handleCancelBackup() {
  backupProgress.running = false
  ElMessage.info('备份操作已取消')
}

// 从备份恢复
function handleRestoreFromBackup(backup) {
  selectedBackup.value = backup
  restoreStep.value = 1
  activeRecoveryTab.value = 'restore'
}

// 下载备份
function handleDownloadBackup(backup) {
  ElMessage.info(`正在下载备份: ${backup.id}`)
}

// 查看备份详情
function handleViewBackupDetails(backup) {
  ElMessage.info(`查看备份详情: ${backup.id}`)
}

// 删除备份
function handleDeleteBackup(backup) {
  ElMessageBox.confirm(`确定要删除备份 ${backup.id} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = backupList.value.findIndex(item => item.id === backup.id)
    if (index !== -1) {
      backupList.value.splice(index, 1)
      ElMessage.success('备份已删除')
    }
  })
}

// 选择备份
function handleSelectBackup(backup) {
  selectedBackup.value = backup
}

// 重置向导
function handleResetWizard() {
  restoreStep.value = 0
  selectedBackup.value = null
  confirmRestore.value = false
  restoreProgress.completed = false
}

// 查看恢复结果
function handleViewResult() {
  ElMessage.info('查看恢复结果详情')
}

// 导出历史记录
function handleExportHistory() {
  ElMessage.info('导出恢复历史记录')
}

// 查看详情
function handleViewDetails(record) {
  ElMessage.info(`查看恢复详情: ${record.id}`)
}

// 下载日志
function handleDownloadLog(record) {
  ElMessage.info(`下载恢复日志: ${record.id}`)
}

// 辅助函数
function getBackupTypeColor(type) {
  const colors = {
    full: 'success',
    incremental: 'warning',
    differential: 'info'
  }
  return colors[type] || 'info'
}

function getBackupTypeName(type) {
  const names = {
    full: '完整',
    incremental: '增量',
    differential: '差异'
  }
  return names[type] || type
}

function getTargetName(target) {
  const names = {
    current: '当前系统',
    new: '新环境',
    test: '测试环境'
  }
  return names[target] || target
}

function getOverwriteName(overwrite) {
  const names = {
    all: '覆盖所有数据',
    missing: '仅覆盖不存在的数据',
    backup: '备份现有数据后覆盖'
  }
  return names[overwrite] || overwrite
}

function getStatusColor(status) {
  const colors = {
    success: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return colors[status] || 'info'
}

function getStatusName(status) {
  const names = {
    success: '成功',
    failed: '失败',
    running: '进行中'
  }
  return names[status] || status
}
</script>

<style scoped>
.data-recovery {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.recovery-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recovery-panel {
  padding: 24px;
}

.section-title {
  font-size: 18px;
  color: #1f2937;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.backup-controls {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.backup-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.backup-type, .backup-target, .backup-compression, .backup-encryption {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.option-desc {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

.backup-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.backup-progress {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.backup-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.wizard-steps {
  margin-bottom: 32px;
}

.wizard-step {
  margin-bottom: 24px;
}

.step-title {
  font-size: 18px;
  color: #1f2937;
  margin-bottom: 16px;
}

.step-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
}

.restore-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.restore-confirmation {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.confirmation-details {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  width: 120px;
  flex-shrink: 0;
}

.restore-progress {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-logs {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.progress-logs h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.log-time {
  color: #6b7280;
  flex-shrink: 0;
}

.log-message {
  color: #374151;
}

.history-controls {
  margin-bottom: 24px;
}

.history-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.history-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .data-recovery {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .backup-options {
    grid-template-columns: 1fr;
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .list-controls, .history-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>