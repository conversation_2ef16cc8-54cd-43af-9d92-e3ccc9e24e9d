<template>
  <div class="role-management">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">角色管理</h1>
        <p class="text-gray-600 mt-1">管理系统角色和权限配置</p>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" @click="openCreateDialog">
          <el-icon><Plus /></el-icon>
          新增角色
        </el-button>
        <el-button @click="refreshRoles">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索角色名称或编码"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 分类筛选 -->
        <el-select
          v-model="filterCategory"
          placeholder="选择分类"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="外部用户" value="external" />
          <el-option label="内部员工" value="internal" />
        </el-select>

        <!-- 业务类型筛选 -->
        <el-select
          v-model="filterBusinessType"
          placeholder="选择业务类型"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="只销售" value="sell_only" />
          <el-option label="只采购" value="buy_only" />
          <el-option label="买卖双向" value="buy_and_sell" />
          <el-option label="服务支持" value="service" />
        </el-select>

        <!-- 状态筛选 -->
        <el-select
          v-model="filterStatus"
          placeholder="选择状态"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <el-table
        v-loading="loading"
        :data="filteredRoles"
        stripe
        @sort-change="handleSortChange"
      >
        <!-- 角色信息 -->
        <el-table-column prop="id" label="ID" width="80" sortable />
        
        <el-table-column label="角色信息" min-width="200">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <div 
                class="w-10 h-10 rounded-lg flex items-center justify-center"
                :style="{ 
                  backgroundColor: row.theme_color + '20' || '#f3f4f6',
                  color: row.theme_color || '#6b7280'
                }"
              >
                <el-icon :size="20">
                  <component :is="getIconComponent(row.icon_name)" />
                </el-icon>
              </div>
              <div>
                <div class="font-semibold text-gray-900">{{ row.display_name }}</div>
                <div class="text-sm text-gray-500">{{ row.role_code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <!-- 分类 -->
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.category === 'internal' ? 'warning' : 'info'"
              size="small"
            >
              {{ row.category === 'internal' ? '内部员工' : '外部用户' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 业务类型 -->
        <el-table-column prop="business_type" label="业务类型" width="120">
          <template #default="{ row }">
            <el-tag 
              :type="getBusinessTypeTagType(row.business_type)"
              size="small"
            >
              {{ getBusinessTypeText(row.business_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 排序 -->
        <el-table-column prop="sort_order" label="排序" width="80" sortable />

        <!-- 状态 -->
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
              :disabled="row.role_code === 'admin'"
            />
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex space-x-2">
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  text
                  @click="viewRoleDetail(row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="编辑角色" placement="top">
                <el-button
                  type="warning"
                  size="small"
                  text
                  @click="editRole(row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="权限配置" placement="top">
                <el-button
                  type="success"
                  size="small"
                  text
                  @click="managePermissions(row)"
                >
                  <el-icon><Key /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="删除角色" placement="top">
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="deleteRole(row)"
                  :disabled="row.role_code === 'admin' || !row.is_active"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-center py-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRoles"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="showRoleDialog"
      :title="isEditing ? '编辑角色' : '新增角色'"
      width="800px"
      :before-close="closeRoleDialog"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleRules"
        label-width="100px"
        @submit.prevent
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 基本信息 -->
          <el-form-item label="角色编码" prop="role_code" required>
            <el-input
              v-model="roleForm.role_code"
              placeholder="请输入角色编码"
              :disabled="isEditing"
            />
          </el-form-item>

          <el-form-item label="显示名称" prop="display_name" required>
            <el-input
              v-model="roleForm.display_name"
              placeholder="请输入显示名称"
            />
          </el-form-item>

          <el-form-item label="分类" prop="category" required>
            <el-select v-model="roleForm.category" placeholder="选择分类">
              <el-option label="外部用户" value="external" />
              <el-option label="内部员工" value="internal" />
            </el-select>
          </el-form-item>

          <el-form-item label="业务类型" prop="business_type" required>
            <el-select v-model="roleForm.business_type" placeholder="选择业务类型">
              <el-option label="只销售" value="sell_only" />
              <el-option label="只采购" value="buy_only" />
              <el-option label="买卖双向" value="buy_and_sell" />
              <el-option label="服务支持" value="service" />
            </el-select>
          </el-form-item>

          <el-form-item label="图标" prop="icon_name">
            <el-select v-model="roleForm.icon_name" placeholder="选择图标">
              <el-option 
                v-for="icon in availableIcons" 
                :key="icon.value" 
                :label="icon.label" 
                :value="icon.value"
              >
                <div class="flex items-center space-x-2">
                  <el-icon><component :is="icon.component" /></el-icon>
                  <span>{{ icon.label }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="主题色" prop="theme_color">
            <el-color-picker 
              v-model="roleForm.theme_color" 
              show-alpha 
              :predefine="predefineColors"
            />
          </el-form-item>

          <el-form-item label="排序顺序" prop="sort_order">
            <el-input-number
              v-model="roleForm.sort_order"
              :min="0"
              :max="999"
              controls-position="right"
            />
          </el-form-item>

          <el-form-item label="状态" prop="is_active">
            <el-switch v-model="roleForm.is_active" />
          </el-form-item>
        </div>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="closeRoleDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="saving"
            @click="saveRole"
          >
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限配置对话框 -->
    <PermissionManagement
      v-if="showPermissionDialog"
      v-model="showPermissionDialog"
      :role="selectedRole"
      @updated="handlePermissionUpdated"
    />

    <!-- 角色详情对话框 -->
    <RoleDetailDialog
      v-if="showDetailDialog"
      v-model="showDetailDialog"
      :role="selectedRole"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { rolesApi } from '@/api/roles'
import PermissionManagement from '@/components/PermissionManagement.vue'
import RoleDetailDialog from '@/components/RoleDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const roles = ref([])
const searchQuery = ref('')
const filterCategory = ref('')
const filterBusinessType = ref('')
const filterStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalRoles = ref(0)

// 对话框控制
const showRoleDialog = ref(false)
const showPermissionDialog = ref(false)
const showDetailDialog = ref(false)
const isEditing = ref(false)
const selectedRole = ref(null)
const roleFormRef = ref(null)

// 表单数据
const roleForm = reactive({
  role_code: '',
  display_name: '',
  description: '',
  category: 'external',
  business_type: 'service',
  icon_name: 'User',
  theme_color: '#6366F1',
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const roleRules = {
  role_code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9_]*$/, message: '角色编码只能包含小写字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  business_type: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ]
}

// 可用图标
const availableIcons = [
  { label: '用户', value: 'User', component: 'User' },
  { label: '办公楼', value: 'OfficeBuilding', component: 'OfficeBuilding' },
  { label: '货车', value: 'Truck', component: 'Truck' },
  { label: '飞机', value: 'WindPower', component: 'WindPower' },
  { label: '用户组', value: 'UserFilled', component: 'UserFilled' },
  { label: '工具', value: 'Tool', component: 'Tool' },
  { label: '设置', value: 'Setting', component: 'Setting' },
  { label: '服务', value: 'Service', component: 'Service' }
]

// 预定义颜色
const predefineColors = [
  '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B',
  '#EF4444', '#06B6D4', '#6366F1', '#EC4899'
]

// 计算属性
const filteredRoles = computed(() => {
  let filtered = roles.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(role => 
      role.display_name.toLowerCase().includes(query) ||
      role.role_code.toLowerCase().includes(query) ||
      (role.description && role.description.toLowerCase().includes(query))
    )
  }

  // 分类过滤
  if (filterCategory.value) {
    filtered = filtered.filter(role => role.category === filterCategory.value)
  }

  // 业务类型过滤
  if (filterBusinessType.value) {
    filtered = filtered.filter(role => role.business_type === filterBusinessType.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    const isActive = filterStatus.value === 'active'
    filtered = filtered.filter(role => role.is_active === isActive)
  }

  return filtered
})

// 方法
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await rolesApi.getAllRoles({ include_inactive: true })
    if (response.success) {
      roles.value = response.body.roles || []
      totalRoles.value = roles.value.length
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

const refreshRoles = () => {
  loadRoles()
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handleSortChange = ({ prop, order }) => {
  // 实现排序逻辑
  if (order) {
    roles.value.sort((a, b) => {
      const aVal = a[prop]
      const bVal = b[prop]
      if (order === 'ascending') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const handleStatusChange = async (role) => {
  try {
    await rolesApi.updateRole(role.id, { is_active: role.is_active })
    ElMessage.success(`角色${role.is_active ? '启用' : '禁用'}成功`)
  } catch (error) {
    // 恢复原状态
    role.is_active = !role.is_active
    ElMessage.error('状态更新失败')
  }
}

// 对话框操作
const openCreateDialog = () => {
  isEditing.value = false
  resetRoleForm()
  showRoleDialog.value = true
}

const editRole = (role) => {
  isEditing.value = true
  selectedRole.value = role
  Object.assign(roleForm, {
    role_code: role.role_code,
    display_name: role.display_name,
    description: role.description || '',
    category: role.category,
    business_type: role.business_type,
    icon_name: role.icon_name || 'User',
    theme_color: role.theme_color || '#6366F1',
    sort_order: role.sort_order || 0,
    is_active: role.is_active
  })
  showRoleDialog.value = true
}

const viewRoleDetail = (role) => {
  selectedRole.value = role
  showDetailDialog.value = true
}

const managePermissions = (role) => {
  selectedRole.value = role
  showPermissionDialog.value = true
}

const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色"${role.display_name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await rolesApi.deleteRole(role.id)
    ElMessage.success('删除成功')
    loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveRole = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true

  try {
    const roleData = { ...roleForm }
    
    if (isEditing.value) {
      await rolesApi.updateRole(selectedRole.value.id, roleData)
      ElMessage.success('角色更新成功')
    } else {
      await rolesApi.createRole(roleData)
      ElMessage.success('角色创建成功')
    }

    closeRoleDialog()
    loadRoles()
  } catch (error) {
    ElMessage.error(isEditing.value ? '更新失败' : '创建失败')
  } finally {
    saving.value = false
  }
}

const closeRoleDialog = () => {
  showRoleDialog.value = false
  resetRoleForm()
  selectedRole.value = null
}

const resetRoleForm = () => {
  Object.assign(roleForm, {
    role_code: '',
    display_name: '',
    description: '',
    category: 'external',
    business_type: 'service',
    icon_name: 'User',
    theme_color: '#6366F1',
    sort_order: 0,
    is_active: true
  })
  if (roleFormRef.value) {
    roleFormRef.value.clearValidate()
  }
}

const handlePermissionUpdated = () => {
  ElMessage.success('权限配置更新成功')
  loadRoles()
}

// 工具方法
const getIconComponent = (iconName) => {
  const iconMap = {
    Factory: 'OfficeBuilding',
    TruckDelivery: 'Truck',
    Airplane: 'WindPower',
    UserGroup: 'UserFilled',
    Wrench: 'Tool',
    Truck: 'Truck',
    Settings: 'Setting',
    User: 'User'
  }
  return iconMap[iconName] || 'User'
}

const getBusinessTypeTagType = (businessType) => {
  const typeMap = {
    sell_only: 'success',
    buy_only: 'primary',
    buy_and_sell: '',
    service: 'warning'
  }
  return typeMap[businessType] || ''
}

const getBusinessTypeText = (businessType) => {
  const textMap = {
    sell_only: '只销售',
    buy_only: '只采购',
    buy_and_sell: '买卖双向',
    service: '服务支持'
  }
  return textMap[businessType] || businessType
}

// 生命周期
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 表格自定义样式 */
:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* 对话框自定义样式 */
:deep(.el-dialog__header) {
  background-color: #fafafa;
  padding: 20px 24px 10px;
  margin: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__footer) {
  padding: 10px 24px 20px;
  margin: 0;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 工具提示样式 */
.el-tooltip__trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>