<!--
临时权限管理页面
版本: 1.0
创建时间: 2025-07-23

带过期时间的临时权限授权管理
特点：
1. 临时权限的创建和管理
2. 自动过期处理机制
3. 紧急权限授权功能
4. 临时权限使用监控
5. 权限到期提醒系统
-->

<template>
  <div class="temp-permissions">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">临时权限管理</h1>
          <p class="page-subtitle">管理带过期时间的临时权限授权，适用于紧急情况和短期需求</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshPermissions">
            <Refresh class="w-4 h-4 mr-2" />
            刷新列表
          </el-button>
          <el-button @click="handleBatchExpireCheck">
            <Clock class="w-4 h-4 mr-2" />
            检查过期
          </el-button>
          <el-button type="primary" @click="handleCreateTempPermission">
            <Plus class="w-4 h-4 mr-2" />
            创建临时权限
          </el-button>
        </div>
      </div>
    </div>

    <!-- 临时权限统计概览 -->
    <div class="temp-stats-overview">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div class="stat-card">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <Key class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ tempStats.totalActive }}</div>
            <div class="stat-label">活跃临时权限</div>
            <div class="stat-change positive">有效授权</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-orange-100 text-orange-600">
            <Clock class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ tempStats.expiringSoon }}</div>
            <div class="stat-label">即将过期</div>
            <div class="stat-change warning">24小时内</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-red-100 text-red-600">
            <Warning class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ tempStats.emergencyActive }}</div>
            <div class="stat-label">紧急权限</div>
            <div class="stat-change urgent">需要关注</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-green-100 text-green-600">
            <CheckCircle class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ tempStats.autoExpired }}</div>
            <div class="stat-label">今日自动过期</div>
            <div class="stat-change neutral">系统处理</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-purple-100 text-purple-600">
            <Users class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ tempStats.affectedUser }}</div>
            <div class="stat-label">涉及用户</div>
            <div class="stat-change info">当前活跃</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <div class="quick-actions-header">
        <h3 class="text-lg font-semibold text-gray-800">快速操作</h3>
      </div>
      
      <div class="quick-actions-grid">
        <div class="quick-action-card emergency" @click="handleEmergencyPermission">
          <div class="action-icon">
            <Zap class="w-8 h-8" />
          </div>
          <div class="action-content">
            <h4 class="action-title">紧急权限授权</h4>
            <p class="action-desc">为紧急情况快速授予临时权限</p>
          </div>
          <div class="action-badge emergency-badge">紧急</div>
        </div>
        
        <div class="quick-action-card" @click="handleBatchExtend">
          <div class="action-icon">
            <RefreshLeft class="w-8 h-8" />
          </div>
          <div class="action-content">
            <h4 class="action-title">批量延期</h4>
            <p class="action-desc">批量延长即将过期的临时权限</p>
          </div>
        </div>
        
        <div class="quick-action-card" @click="handleTemplatePermission">
          <div class="action-icon">
            <CopyDocument class="w-8 h-8" />
          </div>
          <div class="action-content">
            <h4 class="action-title">模板授权</h4>
            <p class="action-desc">使用预设模板快速创建临时权限</p>
          </div>
        </div>
        
        <div class="quick-action-card" @click="handleScheduledRevoke">
          <div class="action-icon">
            <Calendar class="w-8 h-8" />
          </div>
          <div class="action-content">
            <h4 class="action-title">定时撤销</h4>
            <p class="action-desc">设置权限的定时撤销计划</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">权限状态</label>
            <el-select v-model="statusFilter" placeholder="选择状态" clearable @change="handleFilter">
              <el-option label="全部状态" value="" />
              <el-option label="活跃中" value="active" />
              <el-option label="即将过期" value="expiring" />
              <el-option label="已过期" value="expired" />
              <el-option label="已撤销" value="revoked" />
              <el-option label="紧急权限" value="emergency" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">过期时间</label>
            <el-select v-model="expiryFilter" placeholder="过期时间范围" clearable @change="handleFilter">
              <el-option label="全部时间" value="" />
              <el-option label="1小时内" value="1hour" />
              <el-option label="24小时内" value="24hours" />
              <el-option label="7天内" value="7days" />
              <el-option label="30天内" value="30days" />
              <el-option label="已过期" value="overdue" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">授权管理员</label>
            <el-select v-model="adminFilter" placeholder="选择管理员" clearable @change="handleFilter">
              <el-option label="全部管理员" value="" />
              <el-option 
                v-for="admin in adminList" 
                :key="admin.value"
                :label="admin.label"
                :value="admin.value"
              />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">搜索用户</label>
            <el-input
              v-model="userSearchKeyword"
              placeholder="输入用户名搜索"
              clearable
              @input="handleFilter"
            >
              <template #prefix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
        </div>
        
        <div class="filter-actions">
          <el-button @click="handleResetFilters">重置筛选</el-button>
          <el-button type="primary" @click="handleApplyFilters">应用筛选</el-button>
        </div>
      </div>
    </div>

    <!-- 临时权限列表 -->
    <div class="temp-permissions-list">
      <div class="list-header">
        <div class="list-title">
          <h3 class="text-lg font-semibold text-gray-800">临时权限列表</h3>
          <div class="list-count">
            <el-tag>{{ filteredPermissions.length }} 条记录</el-tag>
          </div>
        </div>
        
        <div class="list-actions">
          <el-button-group>
            <el-button 
              :type="showExpiredPermissions ? '' : 'primary'" 
              @click="showExpiredPermissions = false"
            >
              活跃权限
            </el-button>
            <el-button 
              :type="showExpiredPermissions ? 'primary' : ''" 
              @click="showExpiredPermissions = true"
            >
              历史记录
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="permissions-table-container">
        <el-table
          :data="paginatedPermissions"
          v-loading="loadingPermissions"
          stripe
          class="temp-permissions-table"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column type="expand" width="30">
            <template #default="props">
              <div class="permission-detail-expansion">
                <div class="expansion-sections">
                  <div class="expansion-section">
                    <h5 class="expansion-title">授权详情</h5>
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="detail-label">授权理由:</span>
                        <span class="detail-value">{{ props.row.reason }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">申请来源:</span>
                        <span class="detail-value">{{ props.row.source || '管理员直接授权' }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label">使用情况:</span>
                        <span class="detail-value">
                          {{ props.row.usage_count || 0 }} 次使用 / 最后使用: {{ formatLastUsed(props.row.last_used) }}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="expansion-section" v-if="props.row.conditions">
                    <h5 class="expansion-title">使用条件</h5>
                    <div class="conditions-list">
                      <div 
                        v-for="condition in props.row.conditions" 
                        :key="condition.type"
                        class="condition-item"
                      >
                        <el-tag size="small" :type="getConditionTagType(condition.type)">
                          {{ getConditionDisplayName(condition.type) }}
                        </el-tag>
                        <span class="condition-desc">{{ condition.description }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="user.username" label="目标用户" width="150">
            <template #default="scope">
              <div class="user-info">
                <el-avatar :size="32" class="mr-3">
                  {{ scope.row.user.username.charAt(0).toUpperCase() }}
                </el-avatar>
                <div>
                  <div class="username">{{ scope.row.user.username }}</div>
                  <div class="user-role">{{ getRoleDisplayName(scope.row.user.user_type) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="permissions" label="临时权限" min-width="200">
            <template #default="scope">
              <div class="permissions-display">
                <div class="permissions-tags">
                  <el-tag 
                    v-for="permission in scope.row.permissions.slice(0, 3)" 
                    :key="permission"
                    size="small"
                    class="permission-tag"
                  >
                    {{ getPermissionDisplayName(permission) }}
                  </el-tag>
                  <span v-if="scope.row.permissions.length > 3" class="more-permissions">
                    +{{ scope.row.permissions.length - 3 }} 更多
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="授权时间" width="160" sortable>
            <template #default="scope">
              <div class="time-info">
                <div class="time-primary">{{ formatDateTime(scope.row.created_at) }}</div>
                <div class="time-secondary">{{ formatRelativeTime(scope.row.created_at) }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="expires_at" label="过期时间" width="160" sortable>
            <template #default="scope">
              <div class="expiry-info">
                <div class="expiry-time">{{ formatDateTime(scope.row.expires_at) }}</div>
                <div class="expiry-countdown" :class="getExpiryCountdownClass(scope.row.expires_at)">
                  {{ getExpiryCountdown(scope.row.expires_at) }}
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="duration" label="持续时间" width="120">
            <template #default="scope">
              <div class="duration-display">
                <el-tag 
                  size="small"
                  :type="getDurationTagType(scope.row.duration)"
                >
                  {{ formatDuration(scope.row.duration) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="granted_by" label="授权人" width="120">
            <template #default="scope">
              <div class="grantor-info">
                <el-avatar :size="24" class="mr-2">
                  {{ scope.row.granted_by.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="grantor-name">{{ scope.row.granted_by }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <div class="status-indicator">
                <el-tag 
                  :type="getStatusTagType(scope.row.status)"
                  size="small"
                  :effect="scope.row.is_emergency ? 'dark' : 'light'"
                >
                  <component :is="getStatusIcon(scope.row.status)" class="w-3 h-3 mr-1" />
                  {{ getStatusDisplayName(scope.row.status) }}
                </el-tag>
                <div v-if="scope.row.is_emergency" class="emergency-indicator">
                  <Zap class="w-3 h-3 text-red-500" />
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button 
                  v-if="scope.row.status === 'active'"
                  size="small" 
                  type="primary" 
                  text
                  @click="handleExtendPermission(scope.row)"
                >
                  延期
                </el-button>
                <el-button 
                  v-if="scope.row.status === 'active'"
                  size="small" 
                  type="warning" 
                  text
                  @click="handleRevokePermission(scope.row)"
                >
                  撤销
                </el-button>
                <el-dropdown @command="(cmd) => handlePermissionAction(cmd, scope.row)">
                  <el-button size="small" text>
                    更多
                    <el-icon class="el-icon--right">
                      <arrow-down />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="view-detail">查看详情</el-dropdown-item>
                      <el-dropdown-item command="usage-log">使用日志</el-dropdown-item>
                      <el-dropdown-item command="duplicate" v-if="scope.row.status === 'active'">
                        复制权限
                      </el-dropdown-item>
                      <el-dropdown-item divided command="export">导出记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 批量操作栏 -->
        <div class="batch-actions" v-if="selectedPermissions.length > 0">
          <div class="selected-info">
            已选择 {{ selectedPermissions.length }} 个临时权限
          </div>
          <div class="batch-operations">
            <el-button size="small" @click="handleBatchExtendSelected">批量延期</el-button>
            <el-button size="small" type="warning" @click="handleBatchRevokeSelected">批量撤销</el-button>
            <el-button size="small" @click="handleBatchExport">批量导出</el-button>
          </div>
        </div>
        
        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredPermissions.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建临时权限对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建临时权限"
      width="700px"
      @close="handleCloseCreateDialog"
    >
      <div class="create-permission-form">
        <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            
            <el-form-item label="目标用户" prop="targetUser" required>
              <el-select 
                v-model="createForm.targetUser" 
                placeholder="选择用户"
                filterable
                remote
                :remote-method="searchUsers"
                :loading="loadingUsers"
                style="width: 100%"
              >
                <el-option 
                  v-for="user in userOptions" 
                  :key="user.value"
                  :label="user.label"
                  :value="user.value"
                >
                  <div class="user-option">
                    <span class="user-name">{{ user.label }}</span>
                    <span class="user-role">{{ user.role }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="权限类型" required>
              <div class="permission-selector">
                <el-checkbox-group v-model="createForm.permissions">
                  <div class="permissions-grid">
                    <el-checkbox 
                      v-for="permission in availablePermissions" 
                      :key="permission.value"
                      :label="permission.value"
                      :disabled="!canGrantPermission(permission.value)"
                    >
                      <div class="permission-option">
                        <span class="permission-name">{{ permission.label }}</span>
                        <span class="permission-desc">{{ permission.description }}</span>
                      </div>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </el-form-item>
            
            <el-form-item label="授权理由" prop="reason" required>
              <el-input
                v-model="createForm.reason"
                type="textarea"
                :rows="3"
                placeholder="请详细说明临时权限的授权理由"
              />
            </el-form-item>
          </div>
          
          <div class="form-section">
            <h4 class="section-title">时间设置</h4>
            
            <el-form-item label="权限类型">
              <el-radio-group v-model="createForm.permissionType">
                <el-radio label="standard">标准临时权限</el-radio>
                <el-radio label="emergency">紧急权限</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="持续时间" v-if="createForm.permissionType === 'standard'">
              <el-select v-model="createForm.duration" placeholder="选择持续时间">
                <el-option label="1小时" :value="3600" />
                <el-option label="4小时" :value="14400" />
                <el-option label="8小时" :value="28800" />
                <el-option label="24小时" :value="86400" />
                <el-option label="3天" :value="259200" />
                <el-option label="7天" :value="604800" />
                <el-option label="30天" :value="2592000" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="自定义时间" v-if="createForm.duration === 'custom'">
              <el-date-picker
                v-model="createForm.customExpiry"
                type="datetime"
                placeholder="选择过期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            
            <el-form-item label="紧急权限时长" v-if="createForm.permissionType === 'emergency'">
              <el-select v-model="createForm.emergencyDuration" placeholder="紧急权限有效时长">
                <el-option label="30分钟" :value="1800" />
                <el-option label="1小时" :value="3600" />
                <el-option label="2小时" :value="7200" />
                <el-option label="4小时" :value="14400" />
              </el-select>
              <div class="emergency-warning">
                <Warning class="w-4 h-4 mr-2 text-orange-500" />
                <span class="text-orange-600 text-sm">紧急权限将被特别标记和监控</span>
              </div>
            </el-form-item>
          </div>
          
          <div class="form-section">
            <h4 class="section-title">使用条件（可选）</h4>
            
            <el-form-item label="IP限制">
              <el-input
                v-model="createForm.ipRestriction"
                placeholder="限制IP地址范围，如：***********/24"
              />
            </el-form-item>
            
            <el-form-item label="使用次数限制">
              <el-input-number
                v-model="createForm.usageLimit"
                :min="0"
                :max="1000"
                placeholder="限制使用次数，0表示不限制"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="通知设置">
              <el-checkbox-group v-model="createForm.notifications">
                <el-checkbox label="permission_used">权限使用时通知</el-checkbox>
                <el-checkbox label="expiry_warning">过期前提醒</el-checkbox>
                <el-checkbox label="auto_revoke">自动撤销时通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button @click="handlePreviewPermission">预览配置</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmCreate" 
            :loading="creatingPermission"
          >
            {{ createForm.permissionType === 'emergency' ? '立即授权' : '创建权限' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Clock, Plus, Key, Warning, CheckCircle, User,
  Zap, RefreshLeft, CopyDocument, Calendar, Search, ArrowDown
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const loadingPermissions = ref(false)
const loadingUsers = ref(false)
const creatingPermission = ref(false)
const showCreateDialog = ref(false)
const showExpiredPermissions = ref(false)

// 筛选条件
const statusFilter = ref('')
const expiryFilter = ref('')
const adminFilter = ref('')
const userSearchKeyword = ref('')

// 表格数据
const selectedPermissions = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 表单数据
const createFormRef = ref()
const createForm = reactive({
  targetUser: '',
  permissions: [],
  reason: '',
  permissionType: 'standard',
  duration: 86400, // 默认24小时
  customExpiry: '',
  emergencyDuration: 3600, // 默认1小时
  ipRestriction: '',
  usageLimit: 0,
  notifications: ['expiry_warning']
})

const createRules = {
  targetUser: [{ required: true, message: '请选择目标用户', trigger: 'change' }],
  reason: [{ required: true, message: '请填写授权理由', trigger: 'blur' }]
}

// 统计数据
const tempStats = ref({
  totalActive: 12,
  expiringSoon: 3,
  emergencyActive: 2,
  autoExpired: 8,
  affectedUsers: 9
})

// 管理员列表
const adminList = [
  { label: 'admin', value: 'admin' },
  { label: 'super_admin', value: 'super_admin' }
]

// 用户选项
const userOptions = ref([
  { value: 'supplier_demo', label: 'supplier_demo', role: '供应商' },
  { value: 'distributor_demo', label: 'distributor_demo', role: '分销商' },
  { value: 'airline_demo', label: 'airline_demo', role: '航空公司' }
])

// 可用权限列表
const availablePermissions = [
  { 
    value: 'manage_inventory', 
    label: '管理库存', 
    description: '管理航材库存信息'
  },
  { 
    value: 'publish_demand', 
    label: '发布需求', 
    description: '发布航材采购需求'
  },
  { 
    value: 'publish_shared', 
    label: '发布共享件', 
    description: '发布可共享的航材'
  },
  { 
    value: 'manage_orders', 
    label: '管理订单', 
    description: '管理采购和销售订单'
  },
  { 
    value: 'trade_analysis', 
    label: '贸易分析', 
    description: '查看贸易数据分析'
  },
  { 
    value: 'user_management', 
    label: '用户管理', 
    description: '管理系统用户（谨慎授权）'
  },
  { 
    value: 'system_config', 
    label: '系统配置', 
    description: '配置系统参数（高风险）'
  }
]

// 模拟临时权限数据
const tempPermissions = ref([
  {
    id: 1,
    user: { username: 'supplier_demo', user_type: 'supplier' },
    permissions: ['manage_inventory', 'publish_shared'],
    created_at: '2025-07-23T14:00:00',
    expires_at: '2025-07-24T14:00:00',
    duration: 86400,
    granted_by: 'admin',
    status: 'active',
    is_emergency: false,
    reason: '临时需要管理库存权限进行库存盘点',
    source: '用户申请',
    usage_count: 5,
    last_used: '2025-07-23T16:30:00',
    conditions: [
      { type: 'ip_restriction', description: '仅限办公室IP访问' }
    ]
  },
  {
    id: 2,
    user: { username: 'distributor_demo', user_type: 'distributor' },
    permissions: ['user_management'],
    created_at: '2025-07-23T10:30:00',
    expires_at: '2025-07-23T18:30:00',
    duration: 28800,
    granted_by: 'admin',
    status: 'expiring',
    is_emergency: true,
    reason: '紧急情况需要用户管理权限',
    source: '紧急授权',
    usage_count: 2,
    last_used: '2025-07-23T15:45:00',
    conditions: [
      { type: 'usage_limit', description: '限制使用10次' },
      { type: 'emergency', description: '紧急权限，特别监控' }
    ]
  },
  {
    id: 3,
    user: { username: 'airline_demo', user_type: 'airline' },
    permissions: ['trade_analysis', 'publish_shared'],
    created_at: '2025-07-22T16:00:00',
    expires_at: '2025-07-25T16:00:00',
    duration: 259200,
    granted_by: 'admin',
    status: 'active',
    is_emergency: false,
    reason: '临时开放贸易分析功能，用于月度报告',
    source: '管理员直接授权',
    usage_count: 12,
    last_used: '2025-07-23T11:20:00'
  },
  {
    id: 4,
    user: { username: 'test_user', user_type: 'supplier' },
    permissions: ['system_config'],
    created_at: '2025-07-21T09:00:00',
    expires_at: '2025-07-21T17:00:00',
    duration: 28800,
    granted_by: 'super_admin',
    status: 'expired',
    is_emergency: false,
    reason: '系统配置权限，用于测试新功能',
    source: '开发测试',
    usage_count: 3,
    last_used: '2025-07-21T15:30:00'
  }
])

// 计算属性
const filteredPermissions = computed(() => {
  let filtered = [...tempPermissions.value]
  
  // 根据显示模式筛选
  if (!showExpiredPermissions.value) {
    filtered = filtered.filter(p => p.status !== 'expired' && p.status !== 'revoked')
  }
  
  // 状态筛选
  if (statusFilter.value) {
    if (statusFilter.value === 'expiring') {
      filtered = filtered.filter(p => {
        const now = new Date()
        const expiry = new Date(p.expires_at)
        const hoursLeft = (expiry - now) / (1000 * 60 * 60)
        return p.status === 'active' && hoursLeft <= 24
      })
    } else if (statusFilter.value === 'emergency') {
      filtered = filtered.filter(p => p.is_emergency)
    } else {
      filtered = filtered.filter(p => p.status === statusFilter.value)
    }
  }
  
  // 过期时间筛选
  if (expiryFilter.value) {
    const now = new Date()
    filtered = filtered.filter(p => {
      const expiry = new Date(p.expires_at)
      const hoursLeft = (expiry - now) / (1000 * 60 * 60)
      
      switch (expiryFilter.value) {
        case '1hour':
          return hoursLeft <= 1 && hoursLeft > 0
        case '24hours':
          return hoursLeft <= 24 && hoursLeft > 0
        case '7days':
          return hoursLeft <= 168 && hoursLeft > 0
        case '30days':
          return hoursLeft <= 720 && hoursLeft > 0
        case 'overdue':
          return hoursLeft <= 0
        default:
          return true
      }
    })
  }
  
  // 管理员筛选
  if (adminFilter.value) {
    filtered = filtered.filter(p => p.granted_by === adminFilter.value)
  }
  
  // 用户搜索
  if (userSearchKeyword.value) {
    const keyword = userSearchKeyword.value.toLowerCase()
    filtered = filtered.filter(p => 
      p.user.username.toLowerCase().includes(keyword)
    )
  }
  
  return filtered
})

const paginatedPermissions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPermissions.value.slice(start, end)
})

// 方法
const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const formatRelativeTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const formatLastUsed = (dateString) => {
  if (!dateString) return '未使用'
  return formatRelativeTime(dateString)
}

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天`
  if (hours > 0) return `${hours}小时`
  return `${Math.floor(seconds / 60)}分钟`
}

const getExpiryCountdown = (expiryString) => {
  const expiry = new Date(expiryString)
  const now = new Date()
  const diff = expiry - now
  
  if (diff <= 0) return '已过期'
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
}

const getExpiryCountdownClass = (expiryString) => {
  const expiry = new Date(expiryString)
  const now = new Date()
  const hoursLeft = (expiry - now) / (1000 * 60 * 60)
  
  if (hoursLeft <= 0) return 'expired'
  if (hoursLeft <= 1) return 'critical'
  if (hoursLeft <= 24) return 'warning'
  return 'normal'
}

const getRoleDisplayName = (roleType) => {
  const roleMap = {
    'supplier': '供应商',
    'distributor': '分销商',
    'airline': '航空公司',
    'platform_staff': '平台员工',
    'admin': '管理员'
  }
  return roleMap[roleType] || roleType
}

const getPermissionDisplayName = (permission) => {
  const found = availablePermissions.find(p => p.value === permission)
  return found ? found.label : permission
}

const getStatusDisplayName = (status) => {
  const statusMap = {
    'active': '活跃',
    'expired': '已过期',
    'revoked': '已撤销',
    'expiring': '即将过期'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    'active': 'success',
    'expired': 'info',
    'revoked': 'warning',
    'expiring': 'danger'
  }
  return typeMap[status] || ''
}

const getStatusIcon = (status) => {
  const iconMap = {
    'active': 'CheckCircle',
    'expired': 'Clock',
    'revoked': 'CircleClose',
    'expiring': 'Warning'
  }
  return iconMap[status] || 'Circle'
}

const getDurationTagType = (duration) => {
  const hours = duration / 3600
  if (hours <= 4) return 'danger'
  if (hours <= 24) return 'warning'
  if (hours <= 168) return 'primary'
  return 'success'
}

const getConditionDisplayName = (type) => {
  const conditionMap = {
    'ip_restriction': 'IP限制',
    'usage_limit': '使用次数限制',
    'time_restriction': '时间限制',
    'emergency': '紧急权限'
  }
  return conditionMap[type] || type
}

const getConditionTagType = (type) => {
  const typeMap = {
    'ip_restriction': 'warning',
    'usage_limit': 'info',
    'time_restriction': 'primary',
    'emergency': 'danger'
  }
  return typeMap[type] || ''
}

const canGrantPermission = (permission) => {
  // 超级管理员可以授予所有权限
  if (userStore.user?.permission_level >= 4) return true
  
  // 管理员不能授予系统管理权限
  const restrictedPermissions = ['user_management', 'system_config']
  return !restrictedPermissions.includes(permission)
}

// 事件处理方法
const handleFilter = () => {
  currentPage.value = 1
}

const handleResetFilters = () => {
  statusFilter.value = ''
  expiryFilter.value = ''
  adminFilter.value = ''
  userSearchKeyword.value = ''
  handleFilter()
}

const handleApplyFilters = () => {
  handleFilter()
  ElMessage.success('筛选条件已应用')
}

const handleSelectionChange = (selection) => {
  selectedPermissions.value = selection
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const handleCreateTempPermission = () => {
  showCreateDialog.value = true
}

const handleCloseCreateDialog = () => {
  createFormRef.value?.resetFields()
  Object.assign(createForm, {
    targetUser: '',
    permissions: [],
    reason: '',
    permissionType: 'standard',
    duration: 86400,
    customExpiry: '',
    emergencyDuration: 3600,
    ipRestriction: '',
    usageLimit: 0,
    notifications: ['expiry_warning']
  })
}

const handleConfirmCreate = async () => {
  try {
    await createFormRef.value?.validate()
    
    if (createForm.permissions.length === 0) {
      ElMessage.warning('请至少选择一个权限')
      return
    }
    
    creatingPermission.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(
      createForm.permissionType === 'emergency' 
        ? '紧急权限已立即生效' 
        : '临时权限创建成功'
    )
    
    showCreateDialog.value = false
    handleRefreshPermissions()
  } catch (error) {
    console.error('创建临时权限失败:', error)
  } finally {
    creatingPermission.value = false
  }
}

const handlePreviewPermission = () => {
  ElMessage.info('权限预览功能开发中...')
}

const searchUsers = (query) => {
  // 模拟用户搜索
  console.log('搜索用户:', query)
}

const handleExtendPermission = async (permission) => {
  try {
    const { value: extension } = await ElMessageBox.prompt(
      `当前过期时间: ${formatDateTime(permission.expires_at)}\n请选择延长时间:`,
      '延长临时权限',
      {
        confirmButtonText: '确定延长',
        cancelButtonText: '取消',
        inputType: 'number',
        inputPlaceholder: '延长小时数',
        inputValidator: (value) => {
          const hours = parseInt(value)
          if (!hours || hours <= 0 || hours > 720) {
            return '请输入1-720之间的小时数'
          }
          return true
        }
      }
    )
    
    const hours = parseInt(extension)
    ElMessage.success(`权限已延长${hours}小时`)
    handleRefreshPermissions()
  } catch {
    // 用户取消
  }
}

const handleRevokePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确认要撤销用户 ${permission.user.username} 的临时权限吗？`,
      '撤销临时权限',
      {
        type: 'warning',
        confirmButtonText: '确认撤销',
        cancelButtonText: '取消'
      }
    )
    
    permission.status = 'revoked'
    ElMessage.success('临时权限已撤销')
  } catch {
    // 用户取消
  }
}

const handlePermissionAction = (command, permission) => {
  switch (command) {
    case 'view-detail':
      ElMessage.info('查看详情功能开发中...')
      break
    case 'usage-log':
      ElMessage.info('使用日志功能开发中...')
      break
    case 'duplicate':
      ElMessage.info('复制权限功能开发中...')
      break
    case 'export':
      ElMessage.info('导出记录功能开发中...')
      break
  }
}

const handleEmergencyPermission = () => {
  createForm.permissionType = 'emergency'
  showCreateDialog.value = true
}

const handleBatchExtend = () => {
  ElMessage.info('批量延期功能开发中...')
}

const handleTemplatePermission = () => {
  ElMessage.info('模板授权功能开发中...')
}

const handleScheduledRevoke = () => {
  ElMessage.info('定时撤销功能开发中...')
}

const handleBatchExpireCheck = () => {
  loadingPermissions.value = true
  setTimeout(() => {
    loadingPermissions.value = false
    ElMessage.success('已检查过期权限，发现3个即将过期')
  }, 1000)
}

const handleRefreshPermissions = () => {
  loadingPermissions.value = true
  setTimeout(() => {
    loadingPermissions.value = false
    ElMessage.success('权限列表已刷新')
  }, 1000)
}

const handleBatchExtendSelected = () => {
  ElMessage.info('批量延期选中权限功能开发中...')
}

const handleBatchRevokeSelected = () => {
  ElMessage.info('批量撤销选中权限功能开发中...')
}

const handleBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('临时权限管理页面已加载')
})
</script>

<style scoped>
.temp-permissions {
  @apply space-y-6;
}

.page-header {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-gray-600 mt-1;
}

.header-actions {
  @apply flex space-x-3;
}

.temp-stats-overview {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-card {
  @apply bg-gray-50 rounded-lg p-4 flex items-center space-x-4;
}

.stat-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0;
}

.stat-value {
  @apply text-xl font-bold text-gray-800;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-change {
  @apply text-xs mt-1 font-medium;
}

.stat-change.positive {
  @apply text-green-600;
}

.stat-change.warning {
  @apply text-orange-600;
}

.stat-change.urgent {
  @apply text-red-600;
}

.stat-change.neutral {
  @apply text-gray-500;
}

.stat-change.info {
  @apply text-blue-600;
}

.quick-actions-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.quick-actions-header {
  @apply mb-6;
}

.quick-actions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.quick-action-card {
  @apply relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border border-gray-200;
}

.quick-action-card.emergency {
  @apply from-red-50 to-red-100 border-red-200;
}

.action-icon {
  @apply w-12 h-12 bg-white rounded-lg flex items-center justify-center text-gray-600 mb-4;
}

.emergency .action-icon {
  @apply bg-red-100 text-red-600;
}

.action-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.action-desc {
  @apply text-sm text-gray-600;
}

.action-badge {
  @apply absolute top-3 right-3 text-xs px-2 py-1 rounded-full;
}

.emergency-badge {
  @apply bg-red-500 text-white;
}

.filter-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.filter-container {
  @apply space-y-4;
}

.filter-row {
  @apply flex flex-wrap items-end gap-4;
}

.filter-group {
  @apply flex flex-col space-y-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700;
}

.filter-actions {
  @apply flex items-center space-x-3 pt-4 border-t border-gray-200;
}

.temp-permissions-list {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.list-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.list-title {
  @apply flex items-center space-x-3;
}

.list-count {
  @apply flex items-center;
}

.list-actions {
  @apply flex items-center;
}

.permissions-table-container {
  @apply p-6 space-y-4;
}

.temp-permissions-table {
  @apply w-full;
}

.user-info {
  @apply flex items-center;
}

.username {
  @apply font-medium text-gray-800;
}

.user-role {
  @apply text-xs text-gray-500;
}

.permissions-display {
  @apply space-y-2;
}

.permissions-tags {
  @apply flex flex-wrap gap-1;
}

.permission-tag {
  @apply text-xs;
}

.more-permissions {
  @apply text-xs text-gray-500;
}

.time-info {
  @apply space-y-1;
}

.time-primary {
  @apply text-sm font-medium text-gray-800;
}

.time-secondary {
  @apply text-xs text-gray-500;
}

.expiry-info {
  @apply space-y-1;
}

.expiry-time {
  @apply text-sm font-medium text-gray-800;
}

.expiry-countdown {
  @apply text-xs font-medium;
}

.expiry-countdown.normal {
  @apply text-green-600;
}

.expiry-countdown.warning {
  @apply text-orange-600;
}

.expiry-countdown.critical {
  @apply text-red-600;
}

.expiry-countdown.expired {
  @apply text-gray-500;
}

.duration-display {
  @apply flex items-center justify-center;
}

.grantor-info {
  @apply flex items-center;
}

.grantor-name {
  @apply text-sm font-medium text-gray-800;
}

.status-indicator {
  @apply flex items-center space-x-1;
}

.emergency-indicator {
  @apply flex items-center;
}

.action-buttons {
  @apply flex items-center space-x-2;
}

.batch-actions {
  @apply flex items-center justify-between bg-primary-50 border border-primary-200 rounded-lg p-4 mt-4;
}

.selected-info {
  @apply text-primary-700 font-medium;
}

.batch-operations {
  @apply flex items-center space-x-3;
}

.pagination-container {
  @apply flex justify-center pt-4 border-t border-gray-200;
}

.permission-detail-expansion {
  @apply p-6 bg-gray-50;
}

.expansion-sections {
  @apply space-y-6;
}

.expansion-section {
  @apply space-y-3;
}

.expansion-title {
  @apply text-sm font-semibold text-gray-800;
}

.detail-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.detail-item {
  @apply flex items-start space-x-2;
}

.detail-label {
  @apply text-sm font-medium text-gray-600 min-w-0 flex-shrink-0;
}

.detail-value {
  @apply text-sm text-gray-800;
}

.conditions-list {
  @apply space-y-2;
}

.condition-item {
  @apply flex items-center space-x-3;
}

.condition-desc {
  @apply text-sm text-gray-700;
}

.create-permission-form {
  @apply space-y-6;
}

.form-section {
  @apply space-y-4;
}

.section-title {
  @apply text-base font-semibold text-gray-800 border-b border-gray-200 pb-2;
}

.user-option {
  @apply flex items-center justify-between;
}

.user-name {
  @apply font-medium;
}

.user-role {
  @apply text-xs text-gray-500;
}

.permission-selector {
  @apply border border-gray-200 rounded-lg p-4;
}

.permissions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.permission-option {
  @apply space-y-1;
}

.permission-name {
  @apply font-medium;
}

.permission-desc {
  @apply text-xs text-gray-500;
}

.emergency-warning {
  @apply flex items-center mt-2 p-2 bg-orange-50 border border-orange-200 rounded-lg;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>