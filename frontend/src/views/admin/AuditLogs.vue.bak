<!--
审计日志管理
版本: 1.0
创建时间: 2025-07-23

系统管理员专用的审计日志查看和管理界面
特点：
1. 系统操作日志记录和查询
2. 用户行为审计和追踪
3. 安全事件日志分析
4. 日志导出和归档管理
5. 实时日志监控和告警
-->

<template>
  <div class="audit-logs">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">审计日志</h1>
          <p class="page-subtitle">查看和管理系统审计日志，追踪用户行为和安全事件</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshLogs">
            <Refresh class="w-4 h-4 mr-2" />
            刷新日志
          </el-button>
          <el-button @click="handleExportLogs">
            <Download class="w-4 h-4 mr-2" />
            导出日志
          </el-button>
          <el-button @click="handleLogConfig" type="primary">
            <Settings class="w-4 h-4 mr-2" />
            日志配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 日志统计概览 -->
    <div class="logs-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card total-logs">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Document class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ logStats.totalLogs }}</div>
            <div class="card-label">总日志条数</div>
            <div class="card-detail">今日新增 {{ logStats.todayLogs }} 条</div>
          </div>
        </div>
        
        <div class="overview-card security-events">
          <div class="card-icon bg-red-100 text-red-600">
            <Shield class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ logStats.securityEvents }}</div>
            <div class="card-label">安全事件</div>
            <div class="card-detail">待处理 {{ logStats.pendingEvents }} 个</div>
          </div>
        </div>
        
        <div class="overview-card failed_logins">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Warning class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ logStats.failedLogins }}</div>
            <div class="card-label">登录失败</div>
            <div class="card-detail">今日失败 {{ logStats.todayFailures }} 次</div>
          </div>
        </div>
        
        <div class="overview-card active_users">
          <div class="card-icon bg-green-100 text-green-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ logStats.activeUser }}</div>
            <div class="card-label">活跃用户</div>
            <div class="card-detail">最高在线 {{ logStats.peakUser }} 人</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeLogTab" @tab-change="handleTabChange">
        
        <!-- 操作日志 -->
        <el-tab-pane label="操作日志" name="operations">
          <div class="logs-panel">
            <!-- 高级筛选器 -->
            <div class="advanced-filters">
              <div class="filter-section">
                <h3 class="filter-title">高级筛选</h3>
                <div class="filter-grid">
                  <div class="filter-item">
                    <label class="filter-label">时间范围</label>
                    <el-date-picker
                      v-model="operationFilters.dateRange"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="YYYY-MM-DD HH:mm"
                      size="small"
                    />
                  </div>
                  
                  <div class="filter-item">
                    <label class="filter-label">操作类型</label>
                    <el-select v-model="operationFilters.action" placeholder="选择操作类型" size="small" multiple>
                      <el-option label="用户登录" value="login" />
                      <el-option label="用户登出" value="logout" />
                      <el-option label="数据创建" value="create" />
                      <el-option label="数据更新" value="update" />
                      <el-option label="数据删除" value="delete" />
                      <el-option label="权限变更" value="permission" />
                      <el-option label="配置修改" value="config" />
                    </el-select>
                  </div>
                  
                  <div class="filter-item">
                    <label class="filter-label">用户名</label>
                    <el-input 
                      v-model="operationFilters.username" 
                      placeholder="输入用户名" 
                      size="small"
                      clearable
                    />
                  </div>
                  
                  <div class="filter-item">
                    <label class="filter-label">IP地址</label>
                    <el-input 
                      v-model="operationFilters.ip" 
                      placeholder="输入IP地址" 
                      size="small"
                      clearable
                    />
                  </div>
                  
                  <div class="filter-item">
                    <label class="filter-label">操作结果</label>
                    <el-select v-model="operationFilters.result" placeholder="选择结果" size="small">
                      <el-option label="全部" value="" />
                      <el-option label="成功" value="success" />
                      <el-option label="失败" value="failed" />
                      <el-option label="异常" value="error" />
                    </el-select>
                  </div>
                  
                  <div class="filter-item">
                    <label class="filter-label">关键字</label>
                    <el-input 
                      v-model="operationFilters.keyword" 
                      placeholder="搜索描述内容" 
                      size="small"
                      clearable
                    />
                  </div>
                </div>
                
                <div class="filter-actions">
                  <el-button @click="handleApplyFilters" type="primary" size="small">应用筛选</el-button>
                  <el-button @click="handleResetFilters" size="small">重置</el-button>
                  <el-button @click="handleSaveFilter" size="small">保存筛选条件</el-button>
                </div>
              </div>
            </div>

            <!-- 日志列表 -->
            <div class="logs-table">
              <el-table 
                :data="filteredOperationLogs" 
                v-loading="logsLoading"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="timestamp" label="时间" width="180" sortable />
                <el-table-column prop="username" label="用户" width="120" />
                <el-table-column prop="action" label="操作" width="120">
                  <template #default="scope">
                    <el-tag :type="getActionColor(scope.row.action)" size="small">
                      {{ getActionName(scope.row.action) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="resource" label="操作对象" width="150" />
                <el-table-column prop="ip" label="IP地址" width="120" />
                <el-table-column prop="userAgent" label="用户代理" width="200" show-overflow-tooltip />
                <el-table-column prop="result" label="结果" width="80">
                  <template #default="scope">
                    <el-tag :type="getResultColor(scope.row.result)" size="small">
                      {{ getResultName(scope.row.result) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewLogDetail(scope.row)">详情</el-button>
                    <el-button size="small" @click="handleTraceUser(scope.row)" v-if="scope.row.username">追踪</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="table-pagination">
                <el-pagination
                  v-model:current-page="operationPagination.currentPage"
                  v-model:page-size="operationPagination.pageSize"
                  :page-sizes="[20, 50, 100, 200]"
                  :total="operationPagination.total"
                  layout="total, sizes, prev, pager, next, jumper"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 安全日志 -->
        <el-tab-pane label="安全日志" name="security">
          <div class="logs-panel">
            <!-- 安全事件筛选 -->
            <div class="security-filters">
              <div class="filter-row">
                <el-select v-model="securityFilters.severity" placeholder="严重程度" size="small">
                  <el-option label="全部级别" value="" />
                  <el-option label="严重" value="critical" />
                  <el-option label="高危" value="high" />
                  <el-option label="中危" value="medium" />
                  <el-option label="低危" value="low" />
                </el-select>
                
                <el-select v-model="securityFilters.category" placeholder="事件类别" size="small">
                  <el-option label="全部类别" value="" />
                  <el-option label="入侵检测" value="intrusion" />
                  <el-option label="恶意软件" value="malware" />
                  <el-option label="数据泄露" value="data-breach" />
                  <el-option label="权限异常" value="privilege-abuse" />
                  <el-option label="网络攻击" value="network-attack" />
                </el-select>
                
                <el-select v-model="securityFilters.status" placeholder="处理状态" size="small">
                  <el-option label="全部状态" value="" />
                  <el-option label="未处理" value="unhandled" />
                  <el-option label="处理中" value="handling" />
                  <el-option label="已处理" value="handled" />
                  <el-option label="已忽略" value="ignored" />
                </el-select>
                
                <el-button @click="handleRefreshSecurityLogs" size="small">刷新</el-button>
              </div>
            </div>

            <!-- 安全事件列表 -->
            <div class="security-events-table">
              <el-table :data="securityLogs" style="width: 100%">
                <el-table-column prop="timestamp" label="发生时间" width="180" sortable />
                <el-table-column prop="severity" label="严重程度" width="100">
                  <template #default="scope">
                    <el-tag :type="getSeverityColor(scope.row.severity)" size="small">
                      {{ getSeverityName(scope.row.severity) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="category" label="事件类别" width="120">
                  <template #default="scope">
                    <el-tag :type="getCategoryColor(scope.row.category)" size="small">
                      {{ getCategoryName(scope.row.category) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="sourceIp" label="源IP" width="140" />
                <el-table-column prop="targetResource" label="目标资源" width="150" />
                <el-table-column prop="description" label="事件描述" min-width="200" show-overflow-tooltip />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusColor(scope.row.status)" size="small">
                      {{ getStatusName(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewSecurityDetail(scope.row)">详情</el-button>
                    <el-button size="small" @click="handleHandleSecurityEvent(scope.row)" v-if="scope.row.status === 'unhandled'">处理</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 登录日志 -->
        <el-tab-pane label="登录日志" name="login">
          <div class="logs-panel">
            <!-- 登录统计 -->
            <div class="login-stats">
              <div class="stats-cards">
                <div class="stat-card success-logins">
                  <div class="stat-icon bg-green-100 text-green-600">
                    <CircleCheck class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ loginStats.successLogins }}</div>
                    <div class="stat-label">今日成功登录</div>
                  </div>
                </div>
                
                <div class="stat-card failed-logins">
                  <div class="stat-icon bg-red-100 text-red-600">
                    <CircleClose class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ loginStats.failedLogins }}</div>
                    <div class="stat-label">今日失败登录</div>
                  </div>
                </div>
                
                <div class="stat-card unique-ips">
                  <div class="stat-icon bg-blue-100 text-blue-600">
                    <Connection class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ loginStats.uniqueIps }}</div>
                    <div class="stat-label">独立IP数</div>
                  </div>
                </div>
                
                <div class="stat-card suspicious-attempts">
                  <div class="stat-icon bg-orange-100 text-orange-600">
                    <Warning class="w-6 h-6" />
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ loginStats.suspiciousAttempts }}</div>
                    <div class="stat-label">可疑尝试</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 登录日志列表 -->
            <div class="login-table">
              <el-table :data="loginLogs" style="width: 100%">
                <el-table-column prop="timestamp" label="登录时间" width="180" sortable />
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="ip" label="IP地址" width="140" />
                <el-table-column prop="location" label="地理位置" width="120" />
                <el-table-column prop="device" label="设备信息" width="200" show-overflow-tooltip />
                <el-table-column prop="result" label="登录结果" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.result === 'success' ? 'success' : 'danger'" size="small">
                      {{ scope.row.result === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="failureReason" label="失败原因" width="150" show-overflow-tooltip />
                <el-table-column prop="sessionDuration" label="会话时长" width="120" />
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewLoginDetail(scope.row)">详情</el-button>
                    <el-button size="small" @click="handleBlockIp(scope.row)" v-if="scope.row.result === 'failed'" type="danger">封禁</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 系统日志 -->
        <el-tab-pane label="系统日志" name="system">
          <div class="logs-panel">
            <!-- 系统日志筛选 -->
            <div class="system-filters">
              <div class="filter-row">
                <el-select v-model="systemFilters.level" placeholder="日志级别" size="small">
                  <el-option label="全部级别" value="" />
                  <el-option label="ERROR" value="error" />
                  <el-option label="WARN" value="warn" />
                  <el-option label="INFO" value="info" />
                  <el-option label="DEBUG" value="debug" />
                </el-select>
                
                <el-select v-model="systemFilters.module" placeholder="系统模块" size="small">
                  <el-option label="全部模块" value="" />
                  <el-option label="认证模块" value="auth" />
                  <el-option label="数据库" value="database" />
                  <el-option label="缓存" value="cache" />
                  <el-option label="文件系统" value="filesystem" />
                  <el-option label="网络" value="network" />
                </el-select>
                
                <el-input 
                  v-model="systemFilters.message" 
                  placeholder="搜索日志消息" 
                  size="small"
                  clearable
                />
                
                <el-button @click="handleRefreshSystemLogs" size="small">刷新</el-button>
              </div>
            </div>

            <!-- 系统日志列表 -->
            <div class="system-logs-table">
              <el-table :data="systemLogs" style="width: 100%">
                <el-table-column prop="timestamp" label="时间" width="180" sortable />
                <el-table-column prop="level" label="级别" width="80">
                  <template #default="scope">
                    <el-tag :type="getLevelColor(scope.row.level)" size="small">
                      {{ scope.row.level }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="module" label="模块" width="120" />
                <el-table-column prop="message" label="消息" min-width="300" show-overflow-tooltip />
                <el-table-column prop="thread" label="线程" width="100" />
                <el-table-column prop="exception" label="异常" width="120" show-overflow-tooltip />
                <el-table-column label="操作" width="80">
                  <template #default="scope">
                    <el-button size="small" @click="handleViewSystemDetail(scope.row)">详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Download, Settings, Document, Shield, Warning, User, 
  CircleCheck, CircleClose, Connection 
} from 'lucide-vue-next'

// 当前激活的日志标签
const activeLogTab = ref('operations')

// 日志加载状态
const logsLoading = ref(false)

// 日志统计数据
const logStats = reactive({
  totalLogs: 125648,
  todayLogs: 2456,
  securityEvents: 28,
  pendingEvents: 5,
  failedLogins: 145,
  todayFailures: 23,
  activeUsers: 89,
  peakUsers: 156
})

// 操作日志筛选条件
const operationFilters = reactive({
  dateRange: [],
  action: [],
  username: '',
  ip: '',
  result: '',
  keyword: ''
})

// 操作日志分页
const operationPagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 5000
})

// 安全日志筛选条件
const securityFilters = reactive({
  severity: '',
  category: '',
  status: ''
})

// 系统日志筛选条件
const systemFilters = reactive({
  level: '',
  module: '',
  message: ''
})

// 登录统计数据
const loginStats = reactive({
  successLogins: 234,
  failedLogins: 23,
  uniqueIps: 89,
  suspiciousAttempts: 5
})

// 选中的日志条目
const selectedLogs = ref([])

// 操作日志数据
const operationLogs = ref([
  {
    id: 1,
    timestamp: '2025-07-23 21:15:30',
    username: 'admin',
    action: 'login',
    resource: '系统登录',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    result: 'success',
    description: '管理员成功登录系统'
  },
  {
    id: 2,
    timestamp: '2025-07-23 21:12:15',
    username: 'airline_demo',
    action: 'update',
    resource: '用户信息',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    result: 'success',
    description: '用户更新个人资料信息'
  },
  {
    id: 3,
    timestamp: '2025-07-23 21:10:45',
    username: 'supplier_001',
    action: 'create',
    resource: '航材数据',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    result: 'success',
    description: '供应商创建新的航材信息'
  },
  {
    id: 4,
    timestamp: '2025-07-23 21:08:20',
    username: 'unknown',
    action: 'login',
    resource: '系统登录',
    ip: '*************',
    userAgent: 'curl/7.68.0',
    result: 'failed',
    description: '未知用户登录失败 - 用户名不存在'
  }
])

// 安全日志数据
const securityLogs = ref([
  {
    id: 1,
    timestamp: '2025-07-23 21:00:00',
    severity: 'high',
    category: 'intrusion',
    sourceIp: '*************',
    targetResource: '登录接口',
    description: '检测到来自异常IP的多次登录尝试',
    status: 'unhandled'
  },
  {
    id: 2,
    timestamp: '2025-07-23 20:45:00',
    severity: 'critical',
    category: 'privilege-abuse',
    sourceIp: '*************',
    targetResource: '管理员接口',
    description: '普通用户尝试访问管理员功能',
    status: 'handled'
  },
  {
    id: 3,
    timestamp: '2025-07-23 20:30:00',
    severity: 'medium',
    category: 'data-breach',
    sourceIp: '*************',
    targetResource: '用户数据',
    description: '检测到异常的用户数据访问模式',
    status: 'handling'
  }
])

// 登录日志数据
const loginLogs = ref([
  {
    id: 1,
    timestamp: '2025-07-23 21:15:30',
    username: 'admin',
    ip: '*************',
    location: '北京',
    device: 'Chrome 91.0 / macOS',
    result: 'success',
    failureReason: '',
    sessionDuration: '45分钟'
  },
  {
    id: 2,
    timestamp: '2025-07-23 21:10:15',
    username: 'airline_demo',
    ip: '*************',
    location: '上海',
    device: 'Firefox 89.0 / Windows 10',
    result: 'success',
    failureReason: '',
    sessionDuration: '1小时12分钟'
  },
  {
    id: 3,
    timestamp: '2025-07-23 21:05:45',
    username: 'unknown_user',
    ip: '*************',
    location: '未知',
    device: 'curl/7.68.0',
    result: 'failed',
    failureReason: '用户名不存在',
    sessionDuration: ''
  }
])

// 系统日志数据
const systemLogs = ref([
  {
    id: 1,
    timestamp: '2025-07-23 21:15:30',
    level: 'ERROR',
    module: 'database',
    message: '数据库连接超时，正在重试连接',
    thread: 'main-001',
    exception: 'ConnectionTimeoutException'
  },
  {
    id: 2,
    timestamp: '2025-07-23 21:14:15',
    level: 'WARN',
    module: 'auth',
    message: '检测到异常的登录尝试',
    thread: 'auth-002',
    exception: ''
  },
  {
    id: 3,
    timestamp: '2025-07-23 21:13:45',
    level: 'INFO',
    module: 'cache',
    message: '缓存清理任务执行完成',
    thread: 'cache-001',
    exception: ''
  }
])

// 计算属性：过滤后的操作日志
const filteredOperationLogs = computed(() => {
  let filtered = operationLogs.value
  
  // 这里可以根据筛选条件进行过滤
  if (operationFilters.username) {
    filtered = filtered.filter(log => 
      log.username.includes(operationFilters.username)
    )
  }
  
  if (operationFilters.ip) {
    filtered = filtered.filter(log => 
      log.ip.includes(operationFilters.ip)
    )
  }
  
  if (operationFilters.result) {
    filtered = filtered.filter(log => 
      log.result === operationFilters.result
    )
  }
  
  return filtered
})

// 组件挂载时加载数据
onMounted(() => {
  loadAuditLogs()
})

// 加载审计日志数据
function loadAuditLogs() {
  console.log('加载审计日志数据')
}

// 标签切换处理
function handleTabChange(tabName) {
  console.log('切换到审计日志标签:', tabName)
}

// 刷新日志
function handleRefreshLogs() {
  logsLoading.value = true
  setTimeout(() => {
    logsLoading.value = false
    loadAuditLogs()
    ElMessage.success('日志已刷新')
  }, 1000)
}

// 导出日志
function handleExportLogs() {
  ElMessage.info('正在导出审计日志...')
}

// 日志配置
function handleLogConfig() {
  ElMessage.info('打开日志配置界面')
}

// 应用筛选
function handleApplyFilters() {
  console.log('应用筛选条件:', operationFilters)
  ElMessage.success('筛选条件已应用')
}

// 重置筛选
function handleResetFilters() {
  Object.keys(operationFilters).forEach(key => {
    if (Array.isArray(operationFilters[key])) {
      operationFilters[key] = []
    } else {
      operationFilters[key] = ''
    }
  })
  ElMessage.info('筛选条件已重置')
}

// 保存筛选条件
function handleSaveFilter() {
  ElMessage.info('筛选条件已保存')
}

// 选择变更处理
function handleSelectionChange(selection) {
  selectedLogs.value = selection
}

// 查看日志详情
function handleViewLogDetail(log) {
  ElMessage.info(`查看日志详情: ${log.id}`)
}

// 追踪用户
function handleTraceUser(log) {
  ElMessage.info(`追踪用户: ${log.username}`)
}

// 刷新安全日志
function handleRefreshSecurityLogs() {
  ElMessage.success('安全日志已刷新')
}

// 查看安全事件详情
function handleViewSecurityDetail(event) {
  ElMessage.info(`查看安全事件详情: ${event.id}`)
}

// 处理安全事件
function handleHandleSecurityEvent(event) {
  ElMessage.info(`处理安全事件: ${event.id}`)
}

// 查看登录详情
function handleViewLoginDetail(login) {
  ElMessage.info(`查看登录详情: ${login.id}`)
}

// 封禁IP
function handleBlockIp(login) {
  ElMessageBox.confirm(`确定要封禁IP地址 ${login.ip} 吗？`, '确认封禁', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`IP地址 ${login.ip} 已被封禁`)
  })
}

// 刷新系统日志
function handleRefreshSystemLogs() {
  ElMessage.success('系统日志已刷新')
}

// 查看系统日志详情
function handleViewSystemDetail(log) {
  ElMessage.info(`查看系统日志详情: ${log.id}`)
}

// 辅助函数
function getActionColor(action) {
  const colors = {
    login: 'success',
    logout: 'info',
    create: 'primary',
    update: 'warning',
    delete: 'danger',
    permission: 'warning',
    config: 'info'
  }
  return colors[action] || 'info'
}

function getActionName(action) {
  const names = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    permission: '权限变更',
    config: '配置修改'
  }
  return names[action] || action
}

function getResultColor(result) {
  const colors = {
    success: 'success',
    failed: 'danger',
    error: 'danger'
  }
  return colors[result] || 'info'
}

function getResultName(result) {
  const names = {
    success: '成功',
    failed: '失败',
    error: '异常'
  }
  return names[result] || result
}

function getSeverityColor(severity) {
  const colors = {
    critical: 'danger',
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return colors[severity] || 'info'
}

function getSeverityName(severity) {
  const names = {
    critical: '严重',
    high: '高危',
    medium: '中危',
    low: '低危'
  }
  return names[severity] || severity
}

function getCategoryColor(category) {
  const colors = {
    intrusion: 'danger',
    malware: 'danger',
    'data-breach': 'warning',
    'privilege-abuse': 'warning',
    'network-attack': 'danger'
  }
  return colors[category] || 'info'
}

function getCategoryName(category) {
  const names = {
    intrusion: '入侵检测',
    malware: '恶意软件',
    'data-breach': '数据泄露',
    'privilege-abuse': '权限异常',
    'network-attack': '网络攻击'
  }
  return names[category] || category
}

function getStatusColor(status) {
  const colors = {
    unhandled: 'danger',
    handling: 'warning',
    handled: 'success',
    ignored: 'info'
  }
  return colors[status] || 'info'
}

function getStatusName(status) {
  const names = {
    unhandled: '未处理',
    handling: '处理中',
    handled: '已处理',
    ignored: '已忽略'
  }
  return names[status] || status
}

function getLevelColor(level) {
  const colors = {
    ERROR: 'danger',
    WARN: 'warning',
    INFO: 'info',
    DEBUG: 'success'
  }
  return colors[level] || 'info'
}
</script>

<style scoped>
.audit-logs {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-area h1 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-area p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.logs-overview {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 2px;
}

.card-detail {
  font-size: 12px;
  color: #9ca3af;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logs-panel {
  padding: 24px;
}

.advanced-filters {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.filter-title {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.security-filters, .system-filters {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.logs-table, .security-events-table, .login-table, .system-logs-table {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.login-stats {
  margin-bottom: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

:deep(.el-tabs__header) {
  background: #f9fafb;
  margin: 0;
  padding: 0 24px;
}

:deep(.el-tabs__nav-wrap) {
  background: #f9fafb;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

@media (max-width: 768px) {
  .audit-logs {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
}
</style>