<!--
权限管理中心
版本: 1.0
创建时间: 2025-07-23

权限管理的核心功能页面
特点：
1. 用户角色权限矩阵配置
2. 角色转换申请审批
3. 权限模板管理
4. 批量权限操作
5. 实时权限生效
-->

<template>
  <div class="permission-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">权限管理中心</h1>
          <p class="page-subtitle">管理系统用户权限，配置角色权限矩阵</p>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="handleCreateRole">
            <Plus class="w-4 h-4 mr-2" />
            新建角色
          </el-button>
          <el-button @click="handleExportPermissions">
            <Download class="w-4 h-4 mr-2" />
            导出配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览区域 -->
    <div class="stats-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <User class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalUser }}</div>
            <div class="stat-label">总用户数</div>
            <div class="stat-change positive">+{{ stats.newUsersToday }} 今日新增</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-green-100 text-green-600">
            <Avatar class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.activeRoles }}</div>
            <div class="stat-label">活跃角色</div>
            <div class="stat-change neutral">{{ stats.totalRoles }} 总角色</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-orange-100 text-orange-600">
            <Clock class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pendingRequests }}</div>
            <div class="stat-label">待审批申请</div>
            <div class="stat-change warning">需要处理</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon bg-purple-100 text-purple-600">
            <Lock class="w-6 h-6" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.permissionUpdates }}</div>
            <div class="stat-label">今日权限变更</div>
            <div class="stat-change info">次操作</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 用户权限管理 -->
        <el-tab-pane label="用户权限管理" name="users">
          <div class="users-permission-panel">
            <!-- 搜索和筛选区域 -->
            <div class="search-filter-area">
              <div class="search-section">
                <el-input
                  v-model="userSearchKeyword"
                  placeholder="搜索用户名、邮箱或公司名称"
                  clearable
                  @input="handleUserSearch"
                >
                  <template #prefix>
                    <Search class="w-4 h-4 text-gray-400" />
                  </template>
                </el-input>
              </div>
              
              <div class="filter-section">
                <el-select v-model="selectedRole" placeholder="筛选角色" clearable @change="handleRoleFilter">
                  <el-option label="全部角色" value="" />
                  <el-option 
                    v-for="role in roleOptions" 
                    :key="role.value"
                    :label="role.label"
                    :value="role.value"
                  />
                </el-select>
                
                <el-select v-model="selectedPermissionLevel" placeholder="权限等级" clearable @change="handlePermissionFilter">
                  <el-option label="全部等级" value="" />
                  <el-option label="普通用户 (1级)" value="1" />
                  <el-option label="平台员工 (2级)" value="2" />
                  <el-option label="管理员 (3级)" value="3" />
                  <el-option label="超级管理员 (4级)" value="4" />
                </el-select>
                
                <el-button @click="handleResetFilters">重置筛选</el-button>
              </div>
            </div>

            <!-- 用户列表表格 -->
            <div class="users-table-container">
              <el-table
                :data="filteredUsers"
                v-loading="loadingUsers"
                stripe
                class="users-table"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                
                <el-table-column prop="username" label="用户名" width="150">
                  <template #default="scope">
                    <div class="user-info">
                      <el-avatar :size="32" class="mr-3">
                        {{ scope.row.username.charAt(0).toUpperCase() }}
                      </el-avatar>
                      <div>
                        <div class="username">{{ scope.row.username }}</div>
                        <div class="user-email">{{ scope.row.email || '未设置邮箱' }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="company_name" label="所属公司" width="180" />
                
                <el-table-column prop="user_type" label="用户角色" width="120">
                  <template #default="scope">
                    <el-tag 
                      :type="getRoleTagType(scope.row.user_type)"
                      size="small"
                    >
                      {{ getRoleDisplayName(scope.row.user_type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="permission_level" label="权限等级" width="120">
                  <template #default="scope">
                    <el-tag 
                      :type="getPermissionTagType(scope.row.permission_level)"
                      effect="dark"
                      size="small"
                    >
                      {{ getPermissionDisplayName(scope.row.permission_level) }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="last_login" label="最后登录" width="150">
                  <template #default="scope">
                    <div class="text-sm text-gray-600">
                      {{ formatDate(scope.row.last_login) }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="created_at" label="注册时间" width="150">
                  <template #default="scope">
                    <div class="text-sm text-gray-600">
                      {{ formatDate(scope.row.created_at) }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="is_active" label="状态" width="80">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.is_active"
                      @change="handleUserStatusChange(scope.row)"
                    />
                  </template>
                </el-table-column>
                
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="scope">
                    <div class="action-buttons">
                      <el-button 
                        size="small" 
                        type="primary" 
                        text
                        @click="handleEditPermissions(scope.row)"
                      >
                        编辑权限
                      </el-button>
                      <el-button 
                        size="small" 
                        type="warning" 
                        text
                        @click="handleViewAuditLog(scope.row)"
                      >
                        审计日志
                      </el-button>
                      <el-dropdown @command="(cmd) => handleUserAction(cmd, scope.row)">
                        <el-button size="small" text>
                          更多
                          <el-icon class="el-icon--right">
                            <arrow-down />
                          </el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="reset-password">重置密码</el-dropdown-item>
                            <el-dropdown-item command="send-notification">发送通知</el-dropdown-item>
                            <el-dropdown-item divided command="delete" v-if="scope.row.permission_level < userStore.user.permission_level">
                              删除用户
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              
              <!-- 批量操作栏 -->
              <div class="batch-actions" v-if="selectedUsers.length > 0">
                <div class="selected-info">
                  已选择 {{ selectedUsers.length }} 个用户
                </div>
                <div class="batch-operations">
                  <el-button size="small" @click="handleBatchPermissionChange">批量权限变更</el-button>
                  <el-button size="small" @click="handleBatchStatusChange">批量状态变更</el-button>
                  <el-button size="small" type="warning" @click="handleBatchNotification">批量通知</el-button>
                </div>
              </div>
              
              <!-- 分页器 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalUsers"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 角色转换申请 -->
        <el-tab-pane label="角色转换申请" name="role-requests">
          <div class="role-requests-panel">
            <div class="requests-header">
              <h3 class="text-lg font-semibold text-gray-800">待审批的角色转换申请</h3>
              <div class="requests-stats">
                <el-tag type="warning">{{ roleConversionRequests.length }} 个待处理申请</el-tag>
              </div>
            </div>
            
            <div class="requests-list">
              <el-table
                :data="roleConversionRequests"
                v-loading="loadingRequests"
                class="requests-table"
              >
                <el-table-column prop="user.username" label="申请用户" width="150">
                  <template #default="scope">
                    <div class="user-info">
                      <el-avatar :size="32" class="mr-3">
                        {{ scope.row.user.username.charAt(0).toUpperCase() }}
                      </el-avatar>
                      <div>
                        <div class="username">{{ scope.row.user.username }}</div>
                        <div class="user-email">{{ scope.row.user.company_name }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="fromRole" label="当前角色" width="120">
                  <template #default="scope">
                    <el-tag size="small" :type="getRoleTagType(scope.row.fromRole)">
                      {{ getRoleDisplayName(scope.row.fromRole) }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="toRole" label="申请角色" width="120">
                  <template #default="scope">
                    <el-tag size="small" :type="getRoleTagType(scope.row.toRole)" effect="dark">
                      {{ getRoleDisplayName(scope.row.toRole) }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="reason" label="申请理由" min-width="200">
                  <template #default="scope">
                    <div class="reason-text">
                      {{ scope.row.reason }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="created_at" label="申请时间" width="150">
                  <template #default="scope">
                    <div class="text-sm text-gray-600">
                      {{ formatDate(scope.row.created_at) }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="attachments" label="证明材料" width="120">
                  <template #default="scope">
                    <el-button 
                      v-if="scope.row.attachments && scope.row.attachments.length > 0"
                      size="small" 
                      text 
                      type="primary"
                      @click="handleViewAttachments(scope.row)"
                    >
                      查看材料 ({{ scope.row.attachments.length }})
                    </el-button>
                    <span v-else class="text-gray-400 text-sm">无</span>
                  </template>
                </el-table-column>
                
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="scope">
                    <div class="action-buttons">
                      <el-button 
                        size="small" 
                        type="success"
                        @click="handleApproveRequest(scope.row)"
                      >
                        批准
                      </el-button>
                      <el-button 
                        size="small" 
                        type="danger"
                        @click="handleRejectRequest(scope.row)"
                      >
                        拒绝
                      </el-button>
                      <el-button 
                        size="small" 
                        text
                        @click="handleRequestDetail(scope.row)"
                      >
                        详情
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 权限模板管理 -->
        <el-tab-pane label="权限模板" name="templates">
          <div class="templates-panel">
            <div class="templates-header">
              <h3 class="text-lg font-semibold text-gray-800">权限模板管理</h3>
              <el-button type="primary" @click="handleCreateTemplate">
                <Plus class="w-4 h-4 mr-2" />
                新建模板
              </el-button>
            </div>
            
            <div class="templates-grid">
              <div 
                v-for="template in permissionTemplates" 
                :key="template.id"
                class="template-card"
              >
                <div class="template-header">
                  <h4 class="template-name">{{ template.name }}</h4>
                  <el-dropdown @command="(cmd) => handleTemplateAction(cmd, template)">
                    <el-button size="small" text>
                      <MoreFilled class="w-4 h-4" />
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                        <el-dropdown-item command="export">导出</el-dropdown-item>
                        <el-dropdown-item divided command="delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
                
                <div class="template-description">
                  {{ template.description }}
                </div>
                
                <div class="template-permissions">
                  <div class="permissions-preview">
                    <el-tag 
                      v-for="permission in template.permissions.slice(0, 3)" 
                      :key="permission"
                      size="small"
                      class="permission-tag"
                    >
                      {{ getPermissionLabel(permission) }}
                    </el-tag>
                    <span v-if="template.permissions.length > 3" class="more-permissions">
                      +{{ template.permissions.length - 3 }} 更多
                    </span>
                  </div>
                </div>
                
                <div class="template-actions">
                  <el-button size="small" @click="handleApplyTemplate(template)">
                    应用模板
                  </el-button>
                  <el-button size="small" text @click="handlePreviewTemplate(template)">
                    预览
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 权限编辑对话框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="编辑用户权限"
      width="600px"
      @close="handleClosePermissionDialog"
    >
      <div v-if="editingUser" class="permission-editor">
        <div class="user-basic-info">
          <div class="flex items-center mb-4">
            <el-avatar :size="48" class="mr-4">
              {{ editingUser.username.charAt(0).toUpperCase() }}
            </el-avatar>
            <div>
              <h3 class="text-lg font-semibold">{{ editingUser.username }}</h3>
              <p class="text-gray-600">{{ editingUser.company_name }}</p>
            </div>
          </div>
        </div>
        
        <el-form :model="permissionForm" label-width="120px" class="permission-form">
          <el-form-item label="用户角色">
            <el-select v-model="permissionForm.user_type" placeholder="选择用户角色">
              <el-option 
                v-for="role in availableRoles" 
                :key="role.value"
                :label="role.label"
                :value="role.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="权限等级">
            <el-select v-model="permissionForm.permission_level" placeholder="选择权限等级">
              <el-option label="普通用户 (1级)" :value="1" />
              <el-option label="平台员工 (2级)" :value="2" />
              <el-option label="管理员 (3级)" :value="3" :disabled="userStore.user.permission_level < 4" />
              <el-option label="超级管理员 (4级)" :value="4" :disabled="userStore.user.permission_level < 4" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="具体权限">
            <div class="permissions-checkboxes">
              <el-checkbox-group v-model="permissionForm.permissions">
                <div class="permissions-grid">
                  <el-checkbox 
                    v-for="permission in availablePermissions" 
                    :key="permission.value"
                    :label="permission.value"
                    :disabled="!canGrantPermission(permission.value)"
                  >
                    {{ permission.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-form-item>
          
          <el-form-item label="生效时间">
            <el-radio-group v-model="permissionForm.effectiveType">
              <el-radio label="immediate">立即生效</el-radio>
              <el-radio label="scheduled">定时生效</el-radio>
            </el-radio-group>
            <el-date-picker
              v-if="permissionForm.effectiveType === 'scheduled'"
              v-model="permissionForm.effectiveTime"
              type="datetime"
              placeholder="选择生效时间"
              class="mt-2"
            />
          </el-form-item>
          
          <el-form-item label="变更理由">
            <el-input
              v-model="permissionForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入权限变更理由"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSavePermissions" :loading="savingPermissions">
            保存变更
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Download, User, Avatar, Clock, Lock, Search,
  MoreFilled, ArrowDown
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const activeTab = ref('users')
const stats = ref({
  totalUsers: 0,
  newUsersToday: 0,
  activeRoles: 0,
  totalRoles: 0,
  pendingRequests: 0,
  permissionUpdates: 0
})

// 用户管理相关
const userSearchKeyword = ref('')
const selectedRole = ref('')
const selectedPermissionLevel = ref('')
const loadingUsers = ref(false)
const users = ref([])
const filteredUsers = ref([])
const selectedUsers = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)

// 角色转换申请相关
const roleConversionRequests = ref([])
const loadingRequests = ref(false)

// 权限模板相关
const permissionTemplates = ref([])

// 权限编辑相关
const showPermissionDialog = ref(false)
const editingUser = ref(null)
const savingPermissions = ref(false)
const permissionForm = reactive({
  user_type: '',
  permission_level: 1,
  permissions: [],
  effectiveType: 'immediate',
  effectiveTime: null,
  reason: ''
})

// 选项数据
const roleOptions = [
  { label: '供应商', value: 'supplier' },
  { label: '分销商', value: 'distributor' },
  { label: '航空公司', value: 'airline' },
  { label: '平台员工', value: 'platform_staff' },
  { label: '维修工程师', value: 'maintenance_engineer' },
  { label: '物流专员', value: 'logistics_specialist' },
  { label: '管理员', value: 'admin' }
]

const availableRoles = computed(() => {
  // 管理员不能给用户分配比自己更高的角色
  return roleOptions.filter(role => {
    if (role.value === 'admin') {
      return userStore.user?.permission_level >= 4
    }
    return true
  })
})

const availablePermissions = [
  { label: '查看自有数据', value: 'view_own_data' },
  { label: '管理库存', value: 'manage_inventory' },
  { label: '发布需求', value: 'publish_demand' },
  { label: '发布共享件', value: 'publish_shared' },
  { label: '管理订单', value: 'manage_orders' },
  { label: '管理销售订单', value: 'manage_sales_orders' },
  { label: '管理采购订单', value: 'manage_purchase_orders' },
  { label: '响应询价', value: 'respond_to_inquiry' },
  { label: '贸易分析', value: 'trade_analysis' },
  { label: '维修管理', value: 'maintenance_management' },
  { label: '物流跟踪', value: 'logistics_tracking' },
  { label: '用户管理', value: 'user_management' },
  { label: '系统配置', value: 'system_config' },
  { label: '数据分析', value: 'data_analytics' }
]

// 计算属性
const canGrantPermission = (permission) => {
  // 超级管理员可以授予所有权限
  if (userStore.user?.permission_level >= 4) return true
  
  // 管理员不能授予系统管理权限
  const restrictedPermissions = ['user_management', 'system_config']
  return !restrictedPermissions.includes(permission)
}

// 方法
const getRoleDisplayName = (roleType) => {
  const roleMap = {
    'supplier': '供应商',
    'distributor': '分销商', 
    'airline': '航空公司',
    'airline_buyer': '航空公司',
    'platform_staff': '平台员工',
    'maintenance_engineer': '维修工程师',
    'logistics_specialist': '物流专员',
    'admin': '管理员'
  }
  return roleMap[roleType] || roleType
}

const getRoleTagType = (roleType) => {
  const typeMap = {
    'supplier': 'success',
    'distributor': 'primary',
    'airline': 'warning',
    'airline_buyer': 'warning',
    'platform_staff': 'info',
    'maintenance_engineer': '',
    'logistics_specialist': '',
    'admin': 'danger'
  }
  return typeMap[roleType] || ''
}

const getPermissionDisplayName = (level) => {
  const levelMap = {
    1: '普通用户',
    2: '平台员工',
    3: '管理员',
    4: '超级管理员'
  }
  return levelMap[level] || `${level}级`
}

const getPermissionTagType = (level) => {
  const typeMap = {
    1: '',
    2: 'info',
    3: 'warning',
    4: 'danger'
  }
  return typeMap[level] || ''
}

const getPermissionLabel = (permission) => {
  const found = availablePermissions.find(p => p.value === permission)
  return found ? found.label : permission
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 事件处理方法
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
  if (tabName === 'role-requests') {
    loadRoleConversionRequests()
  } else if (tabName === 'templates') {
    loadPermissionTemplates()
  }
}

const handleUserSearch = () => {
  filterUsers()
}

const handleRoleFilter = () => {
  filterUsers()
}

const handlePermissionFilter = () => {
  filterUsers()
}

const handleResetFilters = () => {
  userSearchKeyword.value = ''
  selectedRole.value = ''
  selectedPermissionLevel.value = ''
  filterUsers()
}

const filterUsers = () => {
  let filtered = [...users.value]
  
  // 搜索关键词筛选
  if (userSearchKeyword.value) {
    const keyword = userSearchKeyword.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(keyword) ||
      (user.email && user.email.toLowerCase().includes(keyword)) ||
      (user.company_name && user.company_name.toLowerCase().includes(keyword))
    )
  }
  
  // 角色筛选
  if (selectedRole.value) {
    filtered = filtered.filter(user => user.user_type === selectedRole.value)
  }
  
  // 权限等级筛选
  if (selectedPermissionLevel.value) {
    filtered = filtered.filter(user => user.permission_level === parseInt(selectedPermissionLevel.value))
  }
  
  filteredUsers.value = filtered
  totalUsers.value = filtered.length
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleUserStatusChange = async (user) => {
  try {
    // 这里应该调用API更新用户状态
    ElMessage.success(`用户 ${user.username} 状态已更新`)
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    user.is_active = !user.is_active
  }
}

const handleEditPermissions = (user) => {
  editingUser.value = user
  permissionForm.user_type = user.user_type
  permissionForm.permission_level = user.permission_level
  permissionForm.permissions = user.permissions || []
  permissionForm.effectiveType = 'immediate'
  permissionForm.effectiveTime = null
  permissionForm.reason = ''
  showPermissionDialog.value = true
}

const handleViewAuditLog = (user) => {
  router.push({
    name: 'PermissionAudit',
    query: { userId: user.id }
  })
}

const handleUserAction = async (command, user) => {
  switch (command) {
    case 'reset-password':
      try {
        await ElMessageBox.confirm(`确认要重置用户 ${user.username} 的密码吗？`, '重置密码', {
          type: 'warning'
        })
        ElMessage.success('密码重置指令已发送')
      } catch {
        // 用户取消
      }
      break
    case 'send-notification':
      ElMessage.info('发送通知功能开发中...')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(`确认要删除用户 ${user.username} 吗？此操作不可逆。`, '删除用户', {
          type: 'error'
        })
        ElMessage.success('用户删除成功')
        loadUsers()
      } catch {
        // 用户取消
      }
      break
  }
}

const handleSavePermissions = async () => {
  if (!permissionForm.reason.trim()) {
    ElMessage.warning('请填写权限变更理由')
    return
  }
  
  savingPermissions.value = true
  try {
    // 这里应该调用API保存权限变更
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('权限变更已保存')
    showPermissionDialog.value = false
    loadUsers()
  } catch (error) {
    ElMessage.error('权限变更保存失败')
  } finally {
    savingPermissions.value = false
  }
}

const handleClosePermissionDialog = () => {
  editingUser.value = null
  Object.assign(permissionForm, {
    user_type: '',
    permission_level: 1,
    permissions: [],
    effectiveType: 'immediate',
    effectiveTime: null,
    reason: ''
  })
}

const handleApproveRequest = async (request) => {
  try {
    await ElMessageBox.confirm(`确认批准 ${request.user.username} 的角色转换申请吗？`, '批准申请', {
      type: 'success'
    })
    
    // 这里应该调用API批准申请
    ElMessage.success('申请已批准')
    loadRoleConversionRequests()
  } catch {
    // 用户取消
  }
}

const handleRejectRequest = async (request) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入拒绝理由', '拒绝申请', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请说明拒绝的原因'
    })
    
    if (reason.trim()) {
      // 这里应该调用API拒绝申请
      ElMessage.success('申请已拒绝')
      loadRoleConversionRequests()
    }
  } catch {
    // 用户取消
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadUsers()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadUsers()
}

// 数据加载方法
const loadUsers = async () => {
  loadingUsers.value = true
  try {
    // 这里应该调用API加载用户数据
    // 模拟数据
    users.value = [
      {
        id: 1,
        username: 'supplier_demo',
        email: '<EMAIL>',
        company_name: '航材制造公司A',
        user_type: 'supplier',
        permission_level: 1,
        is_active: true,
        last_login: '2025-07-23T10:30:00',
        created_at: '2025-07-20T09:00:00',
        permissions: ['view_own_data', 'manage_inventory', 'publish_shared']
      },
      {
        id: 2,
        username: 'distributor_demo',
        email: '<EMAIL>',
        company_name: '航材贸易公司B',
        user_type: 'distributor',
        permission_level: 1,
        is_active: true,
        last_login: '2025-07-23T11:15:00',
        created_at: '2025-07-21T14:20:00',
        permissions: ['view_own_data', 'manage_inventory', 'publish_shared', 'publish_demand', 'trade_analysis']
      },
      {
        id: 3,
        username: 'airline_demo',
        email: '<EMAIL>',
        company_name: '东方航空公司',
        user_type: 'airline',
        permission_level: 1,
        is_active: true,
        last_login: '2025-07-23T08:45:00',
        created_at: '2025-07-19T16:30:00',
        permissions: ['view_own_data', 'publish_demand', 'manage_orders']
      },
      {
        id: 4,
        username: 'platform_staff',
        email: '<EMAIL>',
        company_name: '平台运营团队',
        user_type: 'platform_staff',
        permission_level: 2,
        is_active: true,
        last_login: '2025-07-23T12:00:00',
        created_at: '2025-07-15T10:00:00',
        permissions: ['view_own_data', 'user_management', 'data_analytics']
      }
    ]
    
    filterUsers()
    
    // 更新统计数据
    stats.value = {
      totalUsers: users.value.length,
      newUsersToday: 2,
      activeRoles: 5,
      totalRoles: 7,
      pendingRequests: 3,
      permissionUpdates: 8
    }
  } catch (error) {
    ElMessage.error('用户数据加载失败')
  } finally {
    loadingUsers.value = false
  }
}

const loadRoleConversionRequests = async () => {
  loadingRequests.value = true
  try {
    // 模拟角色转换申请数据
    roleConversionRequests.value = [
      {
        id: 1,
        user: {
          username: 'supplier_test',
          company_name: '测试航材公司'
        },
        fromRole: 'supplier',
        toRole: 'distributor',
        reason: '公司业务扩展，需要增加采购和贸易功能',
        created_at: '2025-07-22T15:30:00',
        attachments: [
          { name: '营业执照扩展.pdf', url: '/files/license.pdf' },
          { name: '贸易资质证明.pdf', url: '/files/trade_cert.pdf' }
        ]
      },
      {
        id: 2,
        user: {
          username: 'airline_test',
          company_name: '区域航空公司'
        },
        fromRole: 'airline',
        toRole: 'distributor',
        reason: '开展航材贸易业务，向其他航空公司销售闲置航材',
        created_at: '2025-07-23T09:15:00',
        attachments: []
      }
    ]
  } catch (error) {
    ElMessage.error('申请数据加载失败')
  } finally {
    loadingRequests.value = false
  }
}

const loadPermissionTemplates = async () => {
  try {
    // 模拟权限模板数据
    permissionTemplates.value = [
      {
        id: 1,
        name: '标准供应商权限',
        description: '适用于一般航材供应商的标准权限配置',
        permissions: ['view_own_data', 'manage_inventory', 'publish_shared', 'manage_sales_orders']
      },
      {
        id: 2,
        name: '高级分销商权限',
        description: '适用于大型分销商的全功能权限配置',
        permissions: ['view_own_data', 'manage_inventory', 'publish_shared', 'publish_demand', 'manage_orders', 'trade_analysis']
      },
      {
        id: 3,
        name: '平台运营权限',
        description: '适用于平台内部运营人员的权限配置',
        permissions: ['view_own_data', 'user_management', 'data_analytics', 'system_config']
      }
    ]
  } catch (error) {
    ElMessage.error('模板数据加载失败')
  }
}

// 其他处理方法
const handleCreateRole = () => {
  ElMessage.info('新建角色功能开发中...')
}

const handleExportPermissions = () => {
  ElMessage.info('导出权限配置功能开发中...')
}

const handleBatchPermissionChange = () => {
  ElMessage.info('批量权限变更功能开发中...')
}

const handleBatchStatusChange = () => {
  ElMessage.info('批量状态变更功能开发中...')
}

const handleBatchNotification = () => {
  ElMessage.info('批量通知功能开发中...')
}

const handleViewAttachments = (request) => {
  ElMessage.info('查看附件功能开发中...')
}

const handleRequestDetail = (request) => {
  ElMessage.info('申请详情功能开发中...')
}

const handleCreateTemplate = () => {
  ElMessage.info('新建模板功能开发中...')
}

const handleTemplateAction = (command, template) => {
  ElMessage.info(`模板${command}功能开发中...`)
}

const handleApplyTemplate = (template) => {
  ElMessage.info('应用模板功能开发中...')
}

const handlePreviewTemplate = (template) => {
  ElMessage.info('模板预览功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.permission-management {
  @apply space-y-6;
}

.page-header {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-gray-600 mt-1;
}

.header-actions {
  @apply flex space-x-3;
}

.stats-overview {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-card {
  @apply bg-gray-50 rounded-lg p-6 flex items-center space-x-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.stat-value {
  @apply text-2xl font-bold text-gray-800;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-change {
  @apply text-xs mt-1 font-medium;
}

.stat-change.positive {
  @apply text-green-600;
}

.stat-change.negative {
  @apply text-red-600;
}

.stat-change.neutral {
  @apply text-gray-500;
}

.stat-change.warning {
  @apply text-orange-600;
}

.stat-change.info {
  @apply text-blue-600;
}

.main-content {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.users-permission-panel {
  @apply p-6 space-y-6;
}

.search-filter-area {
  @apply flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0;
}

.search-section {
  @apply flex-1 max-w-md;
}

.filter-section {
  @apply flex items-center space-x-3;
}

.users-table-container {
  @apply space-y-4;
}

.user-info {
  @apply flex items-center;
}

.username {
  @apply font-medium text-gray-800;
}

.user-email {
  @apply text-xs text-gray-500;
}

.action-buttons {
  @apply flex items-center space-x-2;
}

.batch-actions {
  @apply flex items-center justify-between bg-primary-50 border border-primary-200 rounded-lg p-4 mt-4;
}

.selected-info {
  @apply text-primary-700 font-medium;
}

.batch-operations {
  @apply flex items-center space-x-3;
}

.pagination-container {
  @apply flex justify-center mt-6;
}

.role-requests-panel {
  @apply p-6 space-y-6;
}

.requests-header {
  @apply flex items-center justify-between;
}

.requests-stats {
  @apply flex items-center space-x-3;
}

.reason-text {
  @apply text-sm text-gray-700 leading-relaxed;
}

.templates-panel {
  @apply p-6 space-y-6;
}

.templates-header {
  @apply flex items-center justify-between;
}

.templates-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.template-card {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow;
}

.template-header {
  @apply flex items-center justify-between mb-3;
}

.template-name {
  @apply font-semibold text-gray-800;
}

.template-description {
  @apply text-sm text-gray-600 mb-4;
}

.permissions-preview {
  @apply flex flex-wrap gap-2 mb-4;
}

.permission-tag {
  @apply text-xs;
}

.more-permissions {
  @apply text-xs text-gray-500;
}

.template-actions {
  @apply flex items-center space-x-2;
}

.permission-editor {
  @apply space-y-6;
}

.user-basic-info {
  @apply border-b border-gray-200 pb-4;
}

.permission-form {
  @apply space-y-4;
}

.permissions-checkboxes {
  @apply border border-gray-200 rounded-lg p-4;
}

.permissions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>