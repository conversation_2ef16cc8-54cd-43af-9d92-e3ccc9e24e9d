<!--
系统监控面板
版本: 1.0
创建时间: 2025-07-23

系统性能监控和健康状态管理
特点：
1. 实时系统性能监控
2. 数据库状态监控
3. API接口监控
4. 用户活动监控
5. 系统告警管理
-->

<template>
  <div class="system-monitoring">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">系统监控面板</h1>
          <p class="page-subtitle">实时监控系统性能、数据库状态和用户活动</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshAll">
            <Refresh class="w-4 h-4 mr-2" />
            刷新监控
          </el-button>
          <el-button @click="handleExportReport">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
          <el-button type="primary" @click="handleSystemDiagnostic">
            <Monitor class="w-4 h-4 mr-2" />
            系统诊断
          </el-button>
        </div>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="system-overview">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="status-card system-health" :class="getHealthStatusClass(systemHealth.overall)">
          <div class="status-icon">
            <component :is="getHealthIcon(systemHealth.overall)" class="w-8 h-8" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ systemHealth.overall }}%</div>
            <div class="status-label">系统健康度</div>
            <div class="status-trend" :class="systemHealth.trend">
              {{ getHealthTrendText(systemHealth.trend) }}
            </div>
          </div>
        </div>
        
        <div class="status-card cpu-usage">
          <div class="status-icon">
            <Cpu class="w-8 h-8 text-blue-600" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ systemMetrics.cpu }}%</div>
            <div class="status-label">CPU 使用率</div>
            <div class="status-progress">
              <el-progress 
                :percentage="systemMetrics.cpu" 
                :show-text="false" 
                :stroke-width="4"
                :color="getUsageColor(systemMetrics.cpu)"
              />
            </div>
          </div>
        </div>
        
        <div class="status-card memory-usage">
          <div class="status-icon">
            <Coin class="w-8 h-8 text-green-600" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ systemMetrics.memory }}%</div>
            <div class="status-label">内存使用率</div>
            <div class="status-progress">
              <el-progress 
                :percentage="systemMetrics.memory" 
                :show-text="false" 
                :stroke-width="4"
                :color="getUsageColor(systemMetrics.memory)"
              />
            </div>
          </div>
        </div>
        
        <div class="status-card db-connections">
          <div class="status-icon">
            <Database class="w-8 h-8 text-purple-600" />
          </div>
          <div class="status-content">
            <div class="status-value">{{ systemMetrics.dbConnections }}/100</div>
            <div class="status-label">数据库连接</div>
            <div class="status-progress">
              <el-progress 
                :percentage="systemMetrics.dbConnections" 
                :show-text="false" 
                :stroke-width="4"
                :color="getUsageColor(systemMetrics.dbConnections)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控仪表板 -->
    <div class="monitoring-dashboard">
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-6">
        <!-- 左侧监控图表区域 -->
        <div class="xl:col-span-8 space-y-6">
          <!-- 性能监控图表 -->
          <div class="chart-panel">
            <div class="panel-header">
              <h3 class="panel-title">系统性能监控</h3>
              <div class="panel-controls">
                <el-select v-model="performanceTimeRange" size="small" @change="handleTimeRangeChange">
                  <el-option label="最近1小时" value="1h" />
                  <el-option label="最近6小时" value="6h" />
                  <el-option label="最近24小时" value="24h" />
                  <el-option label="最近7天" value="7d" />
                </el-select>
              </div>
            </div>
            
            <div class="chart-container">
              <div class="performance-charts">
                <div class="chart-item">
                  <div class="chart-title">CPU & 内存使用率</div>
                  <div class="chart-canvas">
                    <canvas ref="performanceChart" width="600" height="200"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- API监控面板 -->
          <div class="api-monitoring-panel">
            <div class="panel-header">
              <h3 class="panel-title">API接口监控</h3>
              <div class="panel-stats">
                <el-tag type="success">{{ apiStats.healthy }} 健康</el-tag>
                <el-tag type="warning">{{ apiStats.slow }} 缓慢</el-tag>
                <el-tag type="danger">{{ apiStats.error }} 异常</el-tag>
              </div>
            </div>
            
            <div class="api-list">
              <div class="api-table-container">
                <el-table :data="apiMonitoringData" size="small" class="api-table">
                  <el-table-column prop="endpoint" label="接口" width="200">
                    <template #default="scope">
                      <div class="endpoint-info">
                        <el-tag :type="getMethodTagType(scope.row.method)" size="small">
                          {{ scope.row.method }}
                        </el-tag>
                        <span class="endpoint-path">{{ scope.row.path }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="avgResponseTime" label="平均响应时间" width="120">
                    <template #default="scope">
                      <span :class="getResponseTimeClass(scope.row.avgResponseTime)">
                        {{ scope.row.avgResponseTime }}ms
                      </span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="requestCount" label="请求数" width="80">
                    <template #default="scope">
                      <span class="request-count">{{ scope.row.requestCount }}</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="errorRate" label="错误率" width="80">
                    <template #default="scope">
                      <span :class="getErrorRateClass(scope.row.errorRate)">
                        {{ scope.row.errorRate }}%
                      </span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="status" label="状态" width="80">
                    <template #default="scope">
                      <el-tag 
                        :type="getApiStatusTagType(scope.row.status)"
                        size="small"
                      >
                        {{ getApiStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="lastCheck" label="最后检查" width="120">
                    <template #default="scope">
                      <span class="last-check">{{ formatRelativeTime(scope.row.lastCheck) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>

          <!-- 数据库监控面板 -->
          <div class="database-monitoring-panel">
            <div class="panel-header">
              <h3 class="panel-title">数据库监控</h3>
              <div class="db-status" :class="getDatabaseStatusClass(databaseMetrics.status)">
                <Database class="w-4 h-4 mr-1" />
                {{ getDatabaseStatusText(databaseMetrics.status) }}
              </div>
            </div>
            
            <div class="db-metrics-grid">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="db-metric-card">
                  <div class="metric-header">
                    <div class="metric-title">查询性能</div>
                    <div class="metric-value">{{ databaseMetrics.avgQueryTime }}ms</div>
                  </div>
                  <div class="metric-chart">
                    <el-progress 
                      :percentage="getQueryPerformancePercentage(databaseMetrics.avgQueryTime)" 
                      :color="getQueryPerformanceColor(databaseMetrics.avgQueryTime)"
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </div>
                </div>
                
                <div class="db-metric-card">
                  <div class="metric-header">
                    <div class="metric-title">活跃连接</div>
                    <div class="metric-value">{{ databaseMetrics.activeConnections }}</div>
                  </div>
                  <div class="metric-chart">
                    <el-progress 
                      :percentage="(databaseMetrics.activeConnections / databaseMetrics.maxConnections) * 100" 
                      :color="getConnectionUsageColor(databaseMetrics.activeConnections, databaseMetrics.maxConnections)"
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </div>
                </div>
                
                <div class="db-metric-card">
                  <div class="metric-header">
                    <div class="metric-title">存储使用</div>
                    <div class="metric-value">{{ formatSize(databaseMetrics.storageUsed) }}</div>
                  </div>
                  <div class="metric-chart">
                    <el-progress 
                      :percentage="(databaseMetrics.storageUsed / databaseMetrics.storageTotal) * 100" 
                      :color="getStorageUsageColor(databaseMetrics.storageUsed, databaseMetrics.storageTotal)"
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="xl:col-span-4 space-y-6">
          <!-- 实时告警面板 -->
          <div class="alerts-panel">
            <div class="panel-header">
              <h3 class="panel-title">实时告警</h3>
              <div class="alert-count">
                <el-badge :value="activeAlerts.length" :max="99" class="alert-badge">
                  <Bell class="w-5 h-5 text-orange-500" />
                </el-badge>
              </div>
            </div>
            
            <div class="alerts-list">
              <div v-if="activeAlerts.length === 0" class="no-alerts">
                <CircleCheck class="w-12 h-12 text-green-500 mx-auto mb-2" />
                <p class="text-gray-500 text-sm">暂无活跃告警</p>
              </div>
              
              <div v-else class="alert-items">
                <div 
                  v-for="alert in activeAlerts" 
                  :key="alert.id"
                  class="alert-item"
                  :class="getAlertSeverityClass(alert.severity)"
                >
                  <div class="alert-header">
                    <div class="alert-severity">
                      <component :is="getAlertIcon(alert.severity)" class="w-4 h-4" />
                    </div>
                    <div class="alert-time">{{ formatRelativeTime(alert.timestamp) }}</div>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">{{ alert.title }}</div>
                    <div class="alert-description">{{ alert.description }}</div>
                  </div>
                  <div class="alert-actions">
                    <el-button size="small" text @click="handleDismissAlert(alert)">
                      忽略
                    </el-button>
                    <el-button size="small" text type="primary" @click="handleViewAlert(alert)">
                      查看
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统资源面板 -->
          <div class="resource-panel">
            <div class="panel-header">
              <h3 class="panel-title">系统资源</h3>
            </div>
            
            <div class="resource-items">
              <div class="resource-item">
                <div class="resource-header">
                  <div class="resource-label">磁盘空间</div>
                  <div class="resource-value">
                    {{ formatSize(systemResources.diskUsed) }} / {{ formatSize(systemResources.diskTotal) }}
                  </div>
                </div>
                <div class="resource-progress">
                  <el-progress 
                    :percentage="(systemResources.diskUsed / systemResources.diskTotal) * 100" 
                    :color="getDiskUsageColor(systemResources.diskUsed, systemResources.diskTotal)"
                    :show-text="false"
                    :stroke-width="8"
                  />
                </div>
              </div>
              
              <div class="resource-item">
                <div class="resource-header">
                  <div class="resource-label">网络带宽</div>
                  <div class="resource-value">
                    ↑{{ formatSpeed(systemResources.networkOut) }} ↓{{ formatSpeed(systemResources.networkIn) }}
                  </div>
                </div>
                <div class="network-indicators">
                  <div class="network-indicator">
                    <span class="indicator-label">上行</span>
                    <div class="indicator-bar">
                      <div 
                        class="indicator-fill upload" 
                        :style="{ width: getNetworkUsagePercentage(systemResources.networkOut) + '%' }"
                      ></div>
                    </div>
                  </div>
                  <div class="network-indicator">
                    <span class="indicator-label">下行</span>
                    <div class="indicator-bar">
                      <div 
                        class="indicator-fill download" 
                        :style="{ width: getNetworkUsagePercentage(systemResources.networkIn) + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="resource-item">
                <div class="resource-header">
                  <div class="resource-label">负载平均</div>
                  <div class="resource-value">{{ systemResources.loadAverage.join(', ') }}</div>
                </div>
                <div class="load-indicators">
                  <div class="load-item">
                    <span class="load-label">1分钟</span>
                    <span class="load-value" :class="getLoadClass(systemResources.loadAverage[0])">
                      {{ systemResources.loadAverage[0] }}
                    </span>
                  </div>
                  <div class="load-item">
                    <span class="load-label">5分钟</span>
                    <span class="load-value" :class="getLoadClass(systemResources.loadAverage[1])">
                      {{ systemResources.loadAverage[1] }}
                    </span>
                  </div>
                  <div class="load-item">
                    <span class="load-label">15分钟</span>
                    <span class="load-value" :class="getLoadClass(systemResources.loadAverage[2])">
                      {{ systemResources.loadAverage[2] }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户活动面板 -->
          <div class="user-activity-panel">
            <div class="panel-header">
              <h3 class="panel-title">用户活动</h3>
              <div class="activity-stats">
                <el-tag type="success">{{ userMonitor.online }} 在线</el-tag>
              </div>
            </div>
            
            <div class="activity-metrics">
              <div class="activity-metric">
                <div class="metric-label">活跃用户</div>
                <div class="metric-value">{{ userMonitor.activeUser }}</div>
              </div>
              <div class="activity-metric">
                <div class="metric-label">新注册</div>
                <div class="metric-value">{{ userMonitor.newRegistrations }}</div>
              </div>
              <div class="activity-metric">
                <div class="metric-label">总登录次数</div>
                <div class="metric-value">{{ userMonitor.totalLogins }}</div>
              </div>
            </div>
            
            <div class="recent-activities">
              <div class="activities-header">
                <h4 class="activities-title">最近活动</h4>
              </div>
              <div class="activities-list">
                <div 
                  v-for="activity in recentActivities" 
                  :key="activity.id"
                  class="activity-item"
                >
                  <div class="activity-user">
                    <el-avatar :size="24" class="activity-avatar">
                      {{ activity.username.charAt(0).toUpperCase() }}
                    </el-avatar>
                    <span class="activity-username">{{ activity.username }}</span>
                  </div>
                  <div class="activity-action">{{ activity.action }}</div>
                  <div class="activity-time">{{ formatRelativeTime(activity.timestamp) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  Refresh, Download, Monitor, Cpu, Coin, Coin as Database,
  Bell, CircleCheck, Warning, CircleCloseFilled
} from '@element-plus/icons-vue'

// Stores & Composables
const router = useRouter()
const userStore = useAuthStore()

// 响应式数据
const performanceTimeRange = ref('1h')
const performanceChart = ref(null)

// 系统健康状态
const systemHealth = ref({
  overall: 98,
  trend: 'stable' // up, down, stable
})

// 系统指标
const systemMetrics = ref({
  cpu: 35,
  memory: 68,
  dbConnections: 25
})

// API监控统计
const apiStats = ref({
  healthy: 18,
  slow: 3,
  error: 1
})

// API监控数据
const apiMonitoringData = ref([
  {
    method: 'GET',
    path: '/api/users',
    avgResponseTime: 120,
    requestCount: 2847,
    errorRate: 0.2,
    status: 'healthy',
    lastCheck: '2025-07-23T16:30:00'
  },
  {
    method: 'POST',
    path: '/api/auth/login',
    avgResponseTime: 89,
    requestCount: 1634,
    errorRate: 1.2,
    status: 'healthy',
    lastCheck: '2025-07-23T16:30:00'
  },
  {
    method: 'GET',
    path: '/api/materials',
    avgResponseTime: 245,
    requestCount: 5432,
    errorRate: 0.8,
    status: 'slow',
    lastCheck: '2025-07-23T16:29:45'
  },
  {
    method: 'POST',
    path: '/api/orders',
    avgResponseTime: 67,
    requestCount: 892,
    errorRate: 0.1,
    status: 'healthy',
    lastCheck: '2025-07-23T16:30:00'
  },
  {
    method: 'GET',
    path: '/api/analytics',
    avgResponseTime: 1820,
    requestCount: 234,
    errorRate: 5.6,
    status: 'error',
    lastCheck: '2025-07-23T16:25:00'
  }
])

// 数据库指标
const databaseMetrics = ref({
  status: 'healthy',
  avgQueryTime: 45,
  activeConnections: 12,
  maxConnections: 100,
  storageUsed: 2.4 * 1024 * 1024 * 1024, // 2.4GB
  storageTotal: 50 * 1024 * 1024 * 1024  // 50GB
})

// 活跃告警
const activeAlerts = ref([
  {
    id: 1,
    severity: 'warning',
    title: 'API响应时间过长',
    description: '/api/analytics 接口平均响应时间超过1.5秒',
    timestamp: '2025-07-23T16:25:00'
  },
  {
    id: 2,
    severity: 'info',
    title: '内存使用率较高',
    description: '当前内存使用率为68%，建议关注',
    timestamp: '2025-07-23T16:20:00'
  }
])

// 系统资源
const systemResources = ref({
  diskUsed: 32 * 1024 * 1024 * 1024,  // 32GB
  diskTotal: 100 * 1024 * 1024 * 1024, // 100GB
  networkOut: 1024 * 1024, // 1MB/s
  networkIn: 2048 * 1024,  // 2MB/s
  loadAverage: [0.8, 1.2, 1.5]
})

// 用户活动
const userMonitor = ref({
  online: 24,
  activeUsers: 156,
  newRegistrations: 8,
  totalLogins: 423
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    username: 'supplier_demo',
    action: '登录系统',
    timestamp: '2025-07-23T16:25:00'
  },
  {
    id: 2,
    username: 'distributor_demo',
    action: '发布了新的共享件',
    timestamp: '2025-07-23T16:22:00'
  },
  {
    id: 3,
    username: 'airline_demo',
    action: '创建了采购订单',
    timestamp: '2025-07-23T16:18:00'
  },
  {
    id: 4,
    username: 'platform_staff',
    action: '审核了用户申请',
    timestamp: '2025-07-23T16:15:00'
  }
])

// 定时器
let monitoringInterval = null

// 方法
const getHealthStatusClass = (percentage) => {
  if (percentage >= 95) return 'status-excellent'
  if (percentage >= 85) return 'status-good'
  if (percentage >= 70) return 'status-warning'
  return 'status-critical'
}

const getHealthIcon = (percentage) => {
  if (percentage >= 95) return 'CircleCheck'
  if (percentage >= 85) return 'CircleCheck'
  if (percentage >= 70) return 'Warning'
  return 'CircleClose'
}

const getHealthTrendText = (trend) => {
  const trendMap = {
    'up': '↗ 改善中',
    'down': '↘ 下降中',
    'stable': '→ 稳定'
  }
  return trendMap[trend] || '稳定'
}

const getUsageColor = (percentage) => {
  if (percentage < 60) return '#52c41a'
  if (percentage < 80) return '#faad14'
  return '#ff4d4f'
}

const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return typeMap[method] || ''
}

const getResponseTimeClass = (time) => {
  if (time < 100) return 'response-time-excellent'
  if (time < 300) return 'response-time-good'
  if (time < 1000) return 'response-time-warning'
  return 'response-time-critical'
}

const getErrorRateClass = (rate) => {
  if (rate < 1) return 'error-rate-excellent'
  if (rate < 3) return 'error-rate-good'
  if (rate < 5) return 'error-rate-warning'
  return 'error-rate-critical'
}

const getApiStatusTagType = (status) => {
  const typeMap = {
    'healthy': 'success',
    'slow': 'warning',
    'error': 'danger'
  }
  return typeMap[status] || ''
}

const getApiStatusText = (status) => {
  const textMap = {
    'healthy': '健康',
    'slow': '缓慢',
    'error': '异常'
  }
  return textMap[status] || status
}

const getDatabaseStatusClass = (status) => {
  const classMap = {
    'healthy': 'db-status-healthy',
    'warning': 'db-status-warning',
    'error': 'db-status-error'
  }
  return classMap[status] || ''
}

const getDatabaseStatusText = (status) => {
  const textMap = {
    'healthy': '运行正常',
    'warning': '性能警告',
    'error': '连接异常'
  }
  return textMap[status] || status
}

const getQueryPerformancePercentage = (time) => {
  // 将查询时间转换为百分比，100ms以下为100%，1000ms以上为0%
  const maxTime = 1000
  const minTime = 0
  return Math.max(0, Math.min(100, 100 - ((time - minTime) / (maxTime - minTime)) * 100))
}

const getQueryPerformanceColor = (time) => {
  if (time < 50) return '#52c41a'
  if (time < 200) return '#faad14'
  return '#ff4d4f'
}

const getConnectionUsageColor = (active, max) => {
  const percentage = (active / max) * 100
  if (percentage < 60) return '#52c41a'
  if (percentage < 80) return '#faad14'
  return '#ff4d4f'
}

const getStorageUsageColor = (used, total) => {
  const percentage = (used / total) * 100
  if (percentage < 70) return '#52c41a'
  if (percentage < 90) return '#faad14'
  return '#ff4d4f'
}

const getDiskUsageColor = (used, total) => {
  return getStorageUsageColor(used, total)
}

const getNetworkUsagePercentage = (speed) => {
  // 将网络速度转换为百分比，10MB/s为100%
  const maxSpeed = 10 * 1024 * 1024
  return Math.min(100, (speed / maxSpeed) * 100)
}

const getLoadClass = (load) => {
  if (load < 1) return 'load-excellent'
  if (load < 2) return 'load-good'
  if (load < 4) return 'load-warning'
  return 'load-critical'
}

const getAlertSeverityClass = (severity) => {
  const classMap = {
    'info': 'alert-info',
    'warning': 'alert-warning', 
    'error': 'alert-error',
    'critical': 'alert-critical'
  }
  return classMap[severity] || ''
}

const getAlertIcon = (severity) => {
  const iconMap = {
    'info': 'InfoFilledCircle',
    'warning': 'Warning',
    'error': 'CircleClose',
    'critical': 'Warning'
  }
  return iconMap[severity] || 'InfoFilledCircle'
}

const formatSize = (bytes) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond) => {
  return formatSize(bytesPerSecond) + '/s'
}

const formatRelativeTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${Math.floor(hours / 24)}天前`
}

// 事件处理方法
const handleRefreshAll = () => {
  ElMessage.success('监控数据已刷新')
  updateMonitoringData()
}

const handleExportReport = () => {
  ElMessage.info('导出监控报告功能开发中...')
}

const handleSystemDiagnostic = () => {
  ElMessage.info('系统诊断功能开发中...')
}

const handleTimeRangeChange = (range) => {
  console.log('时间范围变更:', range)
  // 这里应该重新加载图表数据
}

const handleDismissAlert = (alert) => {
  const index = activeAlerts.value.findIndex(a => a.id === alert.id)
  if (index !== -1) {
    activeAlerts.value.splice(index, 1)
    ElMessage.success('告警已忽略')
  }
}

const handleViewAlert = (alert) => {
  ElMessage.info(`查看告警详情: ${alert.title}`)
}

// 更新监控数据
const updateMonitoringData = () => {
  // 模拟数据更新
  systemMetrics.value.cpu = Math.floor(Math.random() * 30) + 20
  systemMetrics.value.memory = Math.floor(Math.random() * 40) + 50
  systemMetrics.value.dbConnections = Math.floor(Math.random() * 20) + 15
  
  // 更新系统健康度
  const avgUsage = (systemMetrics.value.cpu + systemMetrics.value.memory) / 2
  systemHealth.value.overall = Math.max(60, 100 - avgUsage)
  
  console.log('监控数据已更新')
}

// 初始化图表
const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  // 这里应该初始化Chart.js或其他图表库
  console.log('初始化性能监控图表')
}

// 组件挂载
onMounted(() => {
  initPerformanceChart()
  
  // 启动定时更新
  monitoringInterval = setInterval(updateMonitoringData, 30000) // 30秒更新一次
  
  console.log('系统监控面板已加载')
})

// 组件卸载
onUnmounted(() => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
  }
})
</script>

<style scoped>
.system-monitoring {
  @apply space-y-6;
}

.page-header {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-gray-600 mt-1;
}

.header-actions {
  @apply flex space-x-3;
}

.system-overview {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.status-card {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 flex items-center space-x-4 transition-all duration-200;
}

.status-card.status-excellent {
  @apply from-green-50 to-green-100 border-green-200;
}

.status-card.status-good {
  @apply from-blue-50 to-blue-100 border-blue-200;
}

.status-card.status-warning {
  @apply from-orange-50 to-orange-100 border-orange-200;
}

.status-card.status-critical {
  @apply from-red-50 to-red-100 border-red-200;
}

.status-icon {
  @apply w-16 h-16 rounded-lg bg-white flex items-center justify-center flex-shrink-0;
}

.status-value {
  @apply text-2xl font-bold text-gray-800;
}

.status-label {
  @apply text-sm text-gray-600 mt-1;
}

.status-trend {
  @apply text-xs font-medium mt-1;
}

.status-trend.up {
  @apply text-green-600;
}

.status-trend.down {
  @apply text-red-600;
}

.status-trend.stable {
  @apply text-gray-500;
}

.status-progress {
  @apply mt-2;
}

.monitoring-dashboard {
  @apply space-y-6;
}

.chart-panel,
.api-monitoring-panel,
.database-monitoring-panel,
.alerts-panel,
.resource-panel,
.user-activity-panel {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.panel-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.panel-title {
  @apply text-lg font-semibold text-gray-800;
}

.panel-controls {
  @apply flex items-center space-x-3;
}

.panel-stats {
  @apply flex items-center space-x-2;
}

.chart-container {
  @apply p-6;
}

.performance-charts {
  @apply space-y-6;
}

.chart-item {
  @apply space-y-4;
}

.chart-title {
  @apply text-base font-medium text-gray-700;
}

.chart-canvas {
  @apply h-48 flex items-center justify-center bg-gray-50 rounded-lg;
}

.api-list {
  @apply p-6;
}

.api-table-container {
  @apply overflow-x-auto;
}

.endpoint-info {
  @apply flex items-center space-x-2;
}

.endpoint-path {
  @apply text-sm font-mono text-gray-700;
}

.response-time-excellent {
  @apply text-green-600 font-medium;
}

.response-time-good {
  @apply text-blue-600 font-medium;
}

.response-time-warning {
  @apply text-orange-600 font-medium;
}

.response-time-critical {
  @apply text-red-600 font-medium;
}

.error-rate-excellent {
  @apply text-green-600;
}

.error-rate-good {
  @apply text-blue-600;
}

.error-rate-warning {
  @apply text-orange-600;
}

.error-rate-critical {
  @apply text-red-600;
}

.request-count {
  @apply text-gray-700 font-medium;
}

.last-check {
  @apply text-gray-500 text-sm;
}

.db-status {
  @apply flex items-center text-sm font-medium;
}

.db-status-healthy {
  @apply text-green-600;
}

.db-status-warning {
  @apply text-orange-600;
}

.db-status-error {
  @apply text-red-600;
}

.db-metrics-grid {
  @apply p-6;
}

.db-metric-card {
  @apply bg-gray-50 rounded-lg p-4 space-y-3;
}

.metric-header {
  @apply flex items-center justify-between;
}

.metric-title {
  @apply text-sm font-medium text-gray-700;
}

.metric-value {
  @apply text-lg font-bold text-gray-800;
}

.metric-chart {
  @apply space-y-2;
}

.alerts-panel .panel-header {
  @apply pb-4;
}

.alert-count {
  @apply flex items-center;
}

.alerts-list {
  @apply p-6 pt-2;
}

.no-alerts {
  @apply text-center py-8;
}

.alert-items {
  @apply space-y-3 max-h-96 overflow-y-auto;
}

.alert-item {
  @apply bg-gray-50 rounded-lg p-4 space-y-3;
}

.alert-item.alert-info {
  @apply bg-blue-50 border-l-4 border-blue-400;
}

.alert-item.alert-warning {
  @apply bg-orange-50 border-l-4 border-orange-400;
}

.alert-item.alert-error {
  @apply bg-red-50 border-l-4 border-red-400;
}

.alert-item.alert-critical {
  @apply bg-purple-50 border-l-4 border-purple-400;
}

.alert-header {
  @apply flex items-center justify-between;
}

.alert-severity {
  @apply flex items-center;
}

.alert-time {
  @apply text-xs text-gray-500;
}

.alert-title {
  @apply font-medium text-gray-800;
}

.alert-description {
  @apply text-sm text-gray-600;
}

.alert-actions {
  @apply flex items-center space-x-2;
}

.resource-panel .panel-header {
  @apply pb-4;
}

.resource-items {
  @apply p-6 pt-2 space-y-6;
}

.resource-item {
  @apply space-y-3;
}

.resource-header {
  @apply flex items-center justify-between;
}

.resource-label {
  @apply text-sm font-medium text-gray-700;
}

.resource-value {
  @apply text-sm font-medium text-gray-800;
}

.resource-progress {
  @apply space-y-2;
}

.network-indicators {
  @apply space-y-2;
}

.network-indicator {
  @apply flex items-center space-x-3;
}

.indicator-label {
  @apply text-xs text-gray-600 w-8;
}

.indicator-bar {
  @apply flex-1 h-2 bg-gray-200 rounded-full overflow-hidden;
}

.indicator-fill {
  @apply h-full transition-all duration-300;
}

.indicator-fill.upload {
  @apply bg-blue-500;
}

.indicator-fill.download {
  @apply bg-green-500;
}

.load-indicators {
  @apply flex items-center space-x-4;
}

.load-item {
  @apply flex flex-col items-center space-y-1;
}

.load-label {
  @apply text-xs text-gray-600;
}

.load-value {
  @apply text-sm font-medium;
}

.load-value.load-excellent {
  @apply text-green-600;
}

.load-value.load-good {
  @apply text-blue-600;
}

.load-value.load-warning {
  @apply text-orange-600;
}

.load-value.load-critical {
  @apply text-red-600;
}

.user-activity-panel .panel-header {
  @apply pb-4;
}

.activity-stats {
  @apply flex items-center space-x-2;
}

.activity-metrics {
  @apply grid grid-cols-3 gap-4 p-6 pb-4 border-b border-gray-200;
}

.activity-metric {
  @apply text-center space-y-1;
}

.metric-label {
  @apply text-xs text-gray-600;
}

.metric-value {
  @apply text-lg font-bold text-gray-800;
}

.recent-activities {
  @apply p-6 pt-4;
}

.activities-header {
  @apply mb-4;
}

.activities-title {
  @apply text-sm font-medium text-gray-700;
}

.activities-list {
  @apply space-y-3 max-h-64 overflow-y-auto;
}

.activity-item {
  @apply flex items-center space-x-3 py-2;
}

.activity-user {
  @apply flex items-center space-x-2 flex-1;
}

.activity-avatar {
  @apply flex-shrink-0;
}

.activity-username {
  @apply text-sm font-medium text-gray-800;
}

.activity-action {
  @apply text-sm text-gray-600;
}

.activity-time {
  @apply text-xs text-gray-500 flex-shrink-0;
}
</style>