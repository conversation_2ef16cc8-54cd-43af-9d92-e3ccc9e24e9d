<template>
  <div class="shared-material-review">
    <div class="page-header">
      <h1>共享件审核</h1>
      <p>管理和审核平台上的共享件发布申请</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.pending || 0 }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon approved">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.approved || 0 }}</div>
          <div class="stat-label">已通过</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rejected">
          <el-icon><Close /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.rejected || 0 }}</div>
          <div class="stat-label">已拒绝</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.total || 0 }}</div>
          <div class="stat-label">总计</div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-controls">
        <el-select v-model="filters.status" placeholder="审核状态" @change="loadMaterials">
          <el-option label="全部状态" value="" />
          <el-option label="待审核" value="pending_review" />
          <el-option label="已通过" value="approved" />
          <el-option label="已拒绝" value="rejected" />
        </el-select>

        <el-select v-model="filters.category" placeholder="分类" @change="loadMaterials">
          <el-option label="全部分类" value="" />
          <el-option label="发动机" value="发动机" />
          <el-option label="起落架" value="起落架" />
          <el-option label="航电" value="航电" />
          <el-option label="机体结构" value="机体结构" />
        </el-select>

        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadMaterials"
        />

        <el-button @click="resetFilters">重置筛选</el-button>
      </div>
    </div>

    <!-- 共享件列表 -->
    <div class="materials-table" v-loading="loading">
      <el-table :data="materials" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="航材信息" min-width="200">
          <template #default="{ row }">
            <div class="material-info">
              <img :src="row.material.image_url" :alt="row.material.name" class="material-image" />
              <div class="material-details">
                <h4>{{ row.material.name }}</h4>
                <p class="part-number">{{ row.material.part_number }}</p>
                <p class="category">{{ row.material.category }}</p>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="发布人" width="120">
          <template #default="{ row }">
            <div class="publisher-info">
              <div class="publisher-name">{{ row.owner.name }}</div>
              <div class="publisher-company">{{ row.owner.company }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="共享类型" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.share_type === 'sale'" type="success" size="small">出售</el-tag>
            <el-tag v-else-if="row.share_type === 'lease'" type="warning" size="small">租赁</el-tag>
            <el-tag v-else-if="row.share_type === 'exchange'" type="info" size="small">交换</el-tag>
            <el-tag v-else type="primary" size="small">借用</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="数量/价格" width="120">
          <template #default="{ row }">
            <div class="quantity-price">
              <div class="quantity">{{ row.share_quantity }} 件</div>
              <div class="price">¥{{ formatPrice(row.price) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="申请时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                @click="viewDetail(row)"
              >
                查看详情
              </el-button>
              <el-button 
                v-if="row.status === 'pending_review'"
                type="success" 
                size="small"
                @click="approveItem(row)"
              >
                通过
              </el-button>
              <el-button 
                v-if="row.status === 'pending_review'"
                type="danger" 
                size="small"
                @click="rejectItem(row)"
              >
                拒绝
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="共享件详情"
      width="800px"
      :before-close="handleDetailDialogClose"
    >
      <SharedMaterialDetailCard 
        v-if="selectedMaterial"
        :material="selectedMaterial"
        :show-actions="false"
      />
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      :title="approvalAction === 'approve' ? '审核通过' : '审核拒绝'"
      width="500px"
      :before-close="handleApprovalDialogClose"
    >
      <div class="approval-form">
        <div class="material-summary">
          <h4>{{ selectedMaterial?.material.name }}</h4>
          <p>发布人：{{ selectedMaterial?.owner.name }}</p>
          <p>共享类型：{{ getShareTypeText(selectedMaterial?.share_type) }}</p>
          <p>数量：{{ selectedMaterial?.share_quantity }} 件</p>
          <p>价格：¥{{ formatPrice(selectedMaterial?.price) }}</p>
        </div>
        
        <el-form :model="approvalForm" label-width="100px">
          <el-form-item label="审核意见">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>

        <div class="approval-actions">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button 
            :type="approvalAction === 'approve' ? 'success' : 'danger'"
            :loading="approvalLoading"
            @click="submitApproval"
          >
            {{ approvalAction === 'approve' ? '确认通过' : '确认拒绝' }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入
import { getSharedMaterials, approveSharedMaterial } from '@/api/sharedMaterials'
import SharedMaterialDetailCard from '@/components/SharedMaterialDetailCard.vue'

// 响应式数据
const loading = ref(false)
const materials = ref([])
const statistics = ref({})
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  category: '',
  dateRange: null
})

// 对话框控制
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const selectedMaterial = ref(null)
const approvalAction = ref('approve')
const approvalLoading = ref(false)

// 审批表单
const approvalForm = reactive({
  comment: ''
})

// 计算属性
const statusOptions = computed(() => [
  { label: '全部状态', value: '' },
  { label: '待审核', value: 'pending_review' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' }
])

// 生命周期
onMounted(() => {
  loadMaterials()
  loadStatistics()
})

// 方法定义
const loadMaterials = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      status: filters.status,
      category: filters.category,
      // 这里可以添加更多筛选条件
    }

    // 如果有日期范围，添加到参数中
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0].toISOString().split('T')[0]
      params.end_date = filters.dateRange[1].toISOString().split('T')[0]
    }

    const response = await getSharedMaterials(params)
    if (response.success) {
      materials.value = response.body.shared_materials
      total.value = response.body.total
    } else {
      ElMessage.error(response.message || '获取共享件列表失败')
    }
  } catch (error) {
    console.error('获取共享件列表失败:', error)
    ElMessage.error('获取共享件列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = () => {
  // 模拟统计数据
  statistics.value = {
    pending: 12,
    approved: 156,
    rejected: 8,
    total: 176
  }
}

const resetFilters = () => {
  filters.status = ''
  filters.category = ''
  filters.dateRange = null
  loadMaterials()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadMaterials()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMaterials()
}

const viewDetail = (material) => {
  selectedMaterial.value = material
  detailDialogVisible.value = true
}

const approveItem = (material) => {
  selectedMaterial.value = material
  approvalAction.value = 'approve'
  approvalForm.comment = ''
  approvalDialogVisible.value = true
}

const rejectItem = (material) => {
  selectedMaterial.value = material
  approvalAction.value = 'reject'
  approvalForm.comment = ''
  approvalDialogVisible.value = true
}

const handleDetailDialogClose = (done) => {
  detailDialogVisible.value = false
  selectedMaterial.value = null
  if (done) done()
}

const handleApprovalDialogClose = (done) => {
  approvalDialogVisible.value = false
  selectedMaterial.value = null
  approvalForm.comment = ''
  if (done) done()
}

const submitApproval = async () => {
  try {
    approvalLoading.value = true
    
    const data = {
      action: approvalAction.value,
      comment: approvalForm.comment
    }
    
    const response = await approveSharedMaterial(selectedMaterial.value.id, data)
    
    if (response.success) {
      ElMessage.success(approvalAction.value === 'approve' ? '审核通过成功' : '审核拒绝成功')
      approvalDialogVisible.value = false
      loadMaterials()
      loadStatistics()
    } else {
      ElMessage.error(response.message || '审核操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('审核操作失败')
  } finally {
    approvalLoading.value = false
  }
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN').format(price)
}

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const typeMap = {
    pending_review: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    pending_review: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知'
}

const getShareTypeText = (type) => {
  const textMap = {
    sale: '出售',
    lease: '租赁',
    exchange: '交换',
    loan: '借用'
  }
  return textMap[type] || '未知'
}
</script>

<style scoped>
.shared-material-review {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: #e6a23c;
}

.stat-icon.approved {
  background: #67c23a;
}

.stat-icon.rejected {
  background: #f56c6c;
}

.stat-icon.total {
  background: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.materials-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.material-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.material-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.material-details h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.material-details p {
  margin: 2px 0;
  font-size: 12px;
  color: #606266;
}

.publisher-info {
  text-align: center;
}

.publisher-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.publisher-company {
  font-size: 12px;
  color: #606266;
}

.quantity-price {
  text-align: center;
}

.quantity {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.price {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.approval-form {
  max-height: 60vh;
  overflow-y: auto;
}

.material-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.material-summary h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.material-summary p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.approval-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>