<!--
维修进度跟踪和状态更新系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的进度跟踪和状态管理界面
特点：
1. 实时进度更新和状态管理
2. 工作日志记录和时间追踪
3. 里程碑管理和关键节点
4. 图表化进度展示
5. 团队协作和任务分配
-->

<template>
  <div class="progress-tracking">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">维修进度跟踪</h1>
          <p class="page-subtitle">实时跟踪维修进度，管理工作任务，提升团队协作效率</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshProgress">
            <Refresh class="w-4 h-4 mr-2" />
            刷新进度
          </el-button>
          <el-button @click="handleExportReport">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
          <el-button @click="handleQuickUpdate" type="primary">
            <Edit class="w-4 h-4 mr-2" />
            快速更新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 进度概览仪表板 -->
    <div class="progress-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card active">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Monitor class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ progressStats.activeWorks }}</div>
            <div class="card-label">进行中工作</div>
            <div class="card-detail">平均进度: {{ progressStats.avgProgress }}%</div>
          </div>
        </div>
        
        <div class="overview-card milestone">
          <div class="card-icon bg-green-100 text-green-600">
            <Flag class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ progressStats.completedMilestones }}</div>
            <div class="card-label">已完成里程碑</div>
            <div class="card-detail">总计: {{ progressStats.totalMilestones }}</div>
          </div>
        </div>
        
        <div class="overview-card time">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ progressStats.totalHours }}h</div>
            <div class="card-label">今日工作时长</div>
            <div class="card-detail">本周: {{ progressStats.weeklyHours }}h</div>
          </div>
        </div>
        
        <div class="overview-card efficiency">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ progressStats.efficiency }}%</div>
            <div class="card-label">完成效率</div>
            <div class="card-detail">{{ progressStats.efficiencyTrend > 0 ? '+' : '' }}{{ progressStats.efficiencyTrend }}% 本周</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeProgressTab" @tab-change="handleTabChange">
        <!-- 我的工作 -->
        <el-tab-pane label="我的工作" name="my-work">
          <div class="my-work-panel">
            <!-- 工作列表 -->
            <div class="work-list">
              <div class="list-header">
                <h3 class="text-lg font-semibold text-gray-800">当前工作任务</h3>
                <div class="list-actions">
                  <el-select v-model="workFilter" size="small" placeholder="筛选状态">
                    <el-option label="全部任务" value="" />
                    <el-option label="进行中" value="in_progress" />
                    <el-option label="待开始" value="pending" />
                    <el-option label="已暂停" value="paused" />
                    <el-option label="待验收" value="review" />
                  </el-select>
                </div>
              </div>
              
              <div class="work-items">
                <div
                  v-for="work in filteredMyWork"
                  :key="work.id"
                  class="work-item"
                  @click="handleSelectWork(work)"
                  :class="{ 'selected': selectedWork?.id === work.id }"
                >
                  <div class="work-header">
                    <div class="work-info">
                      <h4 class="work-title">{{ work.title }}</h4>
                      <p class="work-order">工单: {{ work.orderNo }}</p>
                    </div>
                    
                    <div class="work-status">
                      <el-tag :type="getWorkStatusColor(work.status)" size="small">
                        {{ getWorkStatusLabel(work.status) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="work-progress">
                    <div class="progress-bar">
                      <el-progress
                        :percentage="work.progress"
                        :color="getProgressColor(work.progress)"
                        :stroke-width="6"
                        :show-text="false"
                      />
                    </div>
                    <span class="progress-text">{{ work.progress }}%</span>
                  </div>
                  
                  <div class="work-meta">
                    <div class="meta-item">
                      <Calendar class="w-3 h-3" />
                      <span>截止: {{ formatDate(work.deadline) }}</span>
                    </div>
                    <div class="meta-item">
                      <Clock class="w-3 h-3" />
                      <span>已用: {{ work.spentHours }}h/{{ work.estimatedHours }}h</span>
                    </div>
                  </div>
                  
                  <div class="work-actions">
                    <el-button
                      @click.stop="handleUpdateProgress(work)"
                      size="small"
                      type="primary"
                    >
                      更新进度
                    </el-button>
                    <el-button
                      @click.stop="handleLogWork(work)"
                      size="small"
                    >
                      记录工作
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 详细进度 -->
        <el-tab-pane label="详细进度" name="detailed">
          <div class="detailed-panel" v-if="selectedWork">
            <div class="detail-header">
              <div class="detail-info">
                <h3 class="detail-title">{{ selectedWork.title }}</h3>
                <p class="detail-subtitle">工单号: {{ selectedWork.orderNo }}</p>
              </div>
              
              <div class="detail-actions">
                <el-button @click="handleViewWorkOrder" size="small">
                  查看工单
                </el-button>
                <el-button @click="handleEditMilestones" size="small" type="primary">
                  编辑里程碑
                </el-button>
              </div>
            </div>
            
            <!-- 进度图表 -->
            <div class="progress-chart">
              <div class="chart-header">
                <h4 class="chart-title">进度趋势</h4>
                <div class="chart-controls">
                  <el-radio-group v-model="chartTimeRange" size="small">
                    <el-radio-button label="7d">最近7天</el-radio-button>
                    <el-radio-button label="30d">最近30天</el-radio-button>
                    <el-radio-button label="all">全部</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              
              <div class="chart-container">
                <div class="chart-placeholder">
                  <TrendingUp class="w-16 h-16 text-gray-400" />
                  <p class="text-gray-500">进度趋势图表</p>
                  <p class="text-sm text-gray-400">显示工作进度随时间的变化趋势</p>
                </div>
              </div>
            </div>
            
            <!-- 里程碑管理 -->
            <div class="milestones-section">
              <div class="section-header">
                <h4 class="section-title">里程碑管理</h4>
                <el-button @click="handleAddMilestone" size="small" type="primary">
                  <Plus class="w-3 h-3 mr-1" />
                  添加里程碑
                </el-button>
              </div>
              
              <div class="milestones-list">
                <div
                  v-for="milestone in selectedWork.milestones"
                  :key="milestone.id"
                  class="milestone-item"
                  :class="{ 'completed': milestone.completed }"
                >
                  <div class="milestone-marker">
                    <component
                      :is="milestone.completed ? 'CircleCheck' : 'Circle'"
                      :class="[
                        'w-4 h-4',
                        milestone.completed ? 'text-green-500' : 'text-gray-400'
                      ]"
                    />
                  </div>
                  
                  <div class="milestone-content">
                    <div class="milestone-header">
                      <h5 class="milestone-title">{{ milestone.title }}</h5>
                      <div class="milestone-meta">
                        <span class="milestone-progress">{{ milestone.progress }}%</span>
                        <span class="milestone-date">{{ formatDate(milestone.targetDate) }}</span>
                      </div>
                    </div>
                    
                    <p class="milestone-desc">{{ milestone.description }}</p>
                    
                    <div class="milestone-tasks" v-if="milestone.tasks.length > 0">
                      <div
                        v-for="task in milestone.tasks"
                        :key="task.id"
                        class="task-item"
                        :class="{ 'completed': task.completed }"
                      >
                        <el-checkbox
                          v-model="task.completed"
                          @change="handleTaskToggle(task, milestone)"
                        >
                          {{ task.title }}
                        </el-checkbox>
                        <span class="task-time" v-if="task.estimatedTime">
                          {{ task.estimatedTime }}h
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="milestone-actions">
                    <el-dropdown @command="handleMilestoneAction">
                      <el-button size="small" text>
                        <MoreVertical class="w-3 h-3" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="{action: 'edit', milestone}">
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'complete', milestone}">
                            标记完成
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'delete', milestone}">
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Time Tracking -->
            <div class="time-tracking">
              <div class="section-header">
                <h4 class="section-title">时间追踪</h4>
                <div class="time-controls">
                  <el-button
                    @click="handleStartTimer"
                    :disabled="timerRunning"
                    size="small"
                    type="success"
                  >
                    <Play class="w-3 h-3 mr-1" />
                    开始计时
                  </el-button>
                  <el-button
                    @click="handleStopTimer"
                    :disabled="!timerRunning"
                    size="small"
                    type="danger"
                  >
                    <Pause class="w-3 h-3 mr-1" />
                    停止计时
                  </el-button>
                </div>
              </div>
              
              <div class="timer-display">
                <div class="current-session">
                  <div class="session-time">{{ formatTime(currentSessionTime) }}</div>
                  <div class="session-label">当前工作时长</div>
                </div>
                
                <div class="today-total">
                  <div class="total-time">{{ formatTime(todayTotalTime) }}</div>
                  <div class="total-label">今日总计</div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="no-selection">
            <div class="no-selection-content">
              <Monitor class="w-16 h-16 text-gray-400" />
              <h3 class="text-lg font-medium text-gray-600 mb-2">选择工作任务</h3>
              <p class="text-gray-500">请从左侧选择一个工作任务来查看详细进度</p>
            </div>
          </div>
        </el-tab-pane>

        <!-- 工作日志 -->
        <el-tab-pane label="工作日志" name="logs">
          <div class="logs-panel">
            <!-- 日志筛选 -->
            <div class="logs-filter">
              <div class="filter-row">
                <div class="filter-group">
                  <label class="filter-label">时间范围</label>
                  <el-date-picker
                    v-model="logDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handleLogDateChange"
                  />
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">工作类型</label>
                  <el-select v-model="logTypeFilter" placeholder="选择类型" clearable>
                    <el-option label="全部类型" value="" />
                    <el-option label="正常工作" value="normal" />
                    <el-option label="加班工作" value="overtime" />
                    <el-option label="学习培训" value="training" />
                    <el-option label="会议讨论" value="meeting" />
                  </el-select>
                </div>
                
                <div class="filter-actions">
                  <el-button @click="handleLogSearch">筛选</el-button>
                  <el-button @click="handleResetLogFilter">重置</el-button>
                </div>
              </div>
            </div>
            
            <!-- 工作日志列表 -->
            <div class="logs-list">
              <div class="logs-timeline">
                <div
                  v-for="log in workLogs"
                  :key="log.id"
                  class="log-item"
                >
                  <div class="log-date">
                    <div class="date-marker"></div>
                    <span class="date-text">{{ formatLogDate(log.date) }}</span>
                  </div>
                  
                  <div class="log-content">
                    <div class="log-header">
                      <h5 class="log-title">{{ log.workTitle }}</h5>
                      <div class="log-meta">
                        <span class="log-duration">{{ log.duration }}h</span>
                        <span class="log-type">{{ getLogTypeLabel(log.type) }}</span>
                      </div>
                    </div>
                    
                    <p class="log-description">{{ log.description }}</p>
                    
                    <div class="log-achievements" v-if="log.achievements.length > 0">
                      <h6 class="achievements-title">完成内容:</h6>
                      <ul class="achievements-list">
                        <li
                          v-for="achievement in log.achievements"
                          :key="achievement"
                          class="achievement-item"
                        >
                          {{ achievement }}
                        </li>
                      </ul>
                    </div>
                    
                    <div class="log-issues" v-if="log.issues.length > 0">
                      <h6 class="issues-title">遇到问题:</h6>
                      <ul class="issues-list">
                        <li
                          v-for="issue in log.issues"
                          :key="issue"
                          class="issue-item"
                        >
                          {{ issue }}
                        </li>
                      </ul>
                    </div>
                    
                    <div class="log-actions">
                      <el-button @click="handleEditLog(log)" size="small">
                        编辑
                      </el-button>
                      <el-button @click="handleDeleteLog(log)" size="small" type="danger">
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 团队协作 -->
        <el-tab-pane label="团队协作" name="team">
          <div class="team-panel">
            <!-- 团队成员状态 -->
            <div class="team-status">
              <h3 class="text-lg font-semibold text-gray-800 mb-4">团队工作状态</h3>
              
              <div class="team-members">
                <div
                  v-for="member in teamMembers"
                  :key="member.id"
                  class="member-card"
                >
                  <div class="member-avatar">
                    <el-avatar :size="40" :src="member.avatar">
                      {{ member.name.charAt(0) }}
                    </el-avatar>
                    <div
                      class="status-indicator"
                      :class="member.status"
                    ></div>
                  </div>
                  
                  <div class="member-info">
                    <h4 class="member-name">{{ member.name }}</h4>
                    <p class="member-role">{{ member.role }}</p>
                    <div class="member-work">
                      <span class="current-work">{{ member.currentWork || '空闲' }}</span>
                    </div>
                  </div>
                  
                  <div class="member-stats">
                    <div class="stat-item">
                      <span class="stat-value">{{ member.activeWorks }}</span>
                      <span class="stat-label">进行中</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ member.todayHours }}h</span>
                      <span class="stat-label">今日工时</span>
                    </div>
                  </div>
                  
                  <div class="member-actions">
                    <el-button @click="handleChatWithMember(member)" size="small">
                      <MessageCircle class="w-3 h-3 mr-1" />
                      聊天
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 协作任务 -->
            <div class="collaboration-tasks">
              <div class="section-header">
                <h4 class="section-title">协作任务</h4>
                <el-button @click="handleCreateCollaboration" size="small" type="primary">
                  <Plus class="w-3 h-3 mr-1" />
                  发起协作
                </el-button>
              </div>
              
              <div class="collaboration-list">
                <div
                  v-for="collab in collaborations"
                  :key="collab.id"
                  class="collaboration-item"
                >
                  <div class="collab-header">
                    <h5 class="collab-title">{{ collab.title }}</h5>
                    <el-tag :type="getCollabStatusColor(collab.status)" size="small">
                      {{ getCollabStatusLabel(collab.status) }}
                    </el-tag>
                  </div>
                  
                  <p class="collab-description">{{ collab.description }}</p>
                  
                  <div class="collab-participants">
                    <div class="participants-label">参与成员:</div>
                    <div class="participants-list">
                      <el-avatar-group :max="4">
                        <el-avatar
                          v-for="participant in collab.participants"
                          :key="participant.id"
                          :size="24"
                          :src="participant.avatar"
                        >
                          {{ participant.name.charAt(0) }}
                        </el-avatar>
                      </el-avatar-group>
                    </div>
                  </div>
                  
                  <div class="collab-meta">
                    <span class="collab-deadline">截止: {{ formatDate(collab.deadline) }}</span>
                    <span class="collab-progress">进度: {{ collab.progress }}%</span>
                  </div>
                  
                  <div class="collab-actions">
                    <el-button @click="handleJoinCollaboration(collab)" size="small">
                      参与协作
                    </el-button>
                    <el-button @click="handleViewCollaboration(collab)" size="small" type="primary">
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 快速更新对话框 -->
    <el-dialog
      v-model="quickUpdateDialog.visible"
      title="快速进度更新"
      width="600px"
    >
      <div class="quick-update-form">
        <el-form :model="quickUpdateForm" label-width="100px">
          <el-form-item label="选择工作">
            <el-select v-model="quickUpdateForm.workId" placeholder="选择要更新的工作">
              <el-option
                v-for="work in myWorks"
                :key="work.id"
                :label="work.title"
                :value="work.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="完成进度">
            <el-slider
              v-model="quickUpdateForm.progress"
              :min="0"
              :max="100"
              :step="5"
              show-input
            />
          </el-form-item>
          
          <el-form-item label="工作时长">
            <el-input-number
              v-model="quickUpdateForm.hoursSpent"
              :min="0"
              :step="0.5"
              placeholder="小时"
            />
          </el-form-item>
          
          <el-form-item label="工作内容">
            <el-input
              v-model="quickUpdateForm.workDone"
              type="textarea"
              :rows="3"
              placeholder="描述今天完成的工作内容..."
            />
          </el-form-item>
          
          <el-form-item label="遇到问题">
            <el-input
              v-model="quickUpdateForm.issues"
              type="textarea"
              :rows="2"
              placeholder="记录遇到的问题或需要帮助的地方..."
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="quickUpdateDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitQuickUpdate" type="primary" :loading="quickUpdateDialog.loading">
          更新进度
        </el-button>
      </template>
    </el-dialog>

    <!-- 工作日志对话框 -->
    <el-dialog
      v-model="workLogDialog.visible"
      title="记录工作日志"
      width="700px"
    >
      <div class="work-log-form">
        <el-form :model="workLogForm" label-width="100px">
          <el-form-item label="工作日期">
            <el-date-picker
              v-model="workLogForm.date"
              type="date"
              placeholder="选择日期"
            />
          </el-form-item>
          
          <el-form-item label="工作时长">
            <el-input-number
              v-model="workLogForm.duration"
              :min="0.5"
              :step="0.5"
              placeholder="小时"
            />
          </el-form-item>
          
          <el-form-item label="工作类型">
            <el-select v-model="workLogForm.type" placeholder="选择类型">
              <el-option label="正常工作" value="normal" />
              <el-option label="加班工作" value="overtime" />
              <el-option label="学习培训" value="training" />
              <el-option label="会议讨论" value="meeting" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="工作描述">
            <el-input
              v-model="workLogForm.description"
              type="textarea"
              :rows="4"
              placeholder="详细描述今天的工作内容..."
            />
          </el-form-item>
          
          <el-form-item label="完成内容">
            <div class="achievements-input">
              <div
                v-for="(achievement, index) in workLogForm.achievements"
                :key="index"
                class="achievement-input-item"
              >
                <el-input
                  v-model="workLogForm.achievements[index]"
                  placeholder="完成的具体内容"
                />
                <el-button
                  @click="removeAchievement(index)"
                  :disabled="workLogForm.achievements.length <= 1"
                  size="small"
                  type="danger"
                  circle
                >
                  <Minus class="w-3 h-3" />
                </el-button>
              </div>
              <el-button @click="addAchievement" size="small" type="primary" plain>
                <Plus class="w-3 h-3 mr-1" />
                添加完成项
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="遇到问题">
            <div class="issues-input">
              <div
                v-for="(issue, index) in workLogForm.issues"
                :key="index"
                class="issue-input-item"
              >
                <el-input
                  v-model="workLogForm.issues[index]"
                  placeholder="遇到的问题或困难"
                />
                <el-button
                  @click="removeIssue(index)"
                  :disabled="workLogForm.issues.length <= 1"
                  size="small"
                  type="danger"
                  circle
                >
                  <Minus class="w-3 h-3" />
                </el-button>
              </div>
              <el-button @click="addIssue" size="small" type="warning" plain>
                <Plus class="w-3 h-3 mr-1" />
                添加问题
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="workLogDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitWorkLog" type="primary" :loading="workLogDialog.loading">
          保存日志
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Download, Edit, Monitor, Flag, Clock, TrendingUp,
  Calendar, Plus, CircleCheck, Circle, MoreVertical, Play, Pause,
  MessageCircle, Minus
} from '@element-plus/icons-vue'

// 响应式数据
const activeProgressTab = ref('my-work')
const workFilter = ref('')
const selectedWork = ref(null)
const chartTimeRange = ref('7d')
const timerRunning = ref(false)
const currentSessionTime = ref(0)
const todayTotalTime = ref(18000) // 5小时 = 18000秒
const logDateRange = ref([])
const logTypeFilter = ref('')

// 定时器
let timer = null

// 统计数据
const progressStats = ref({
  activeWorks: 6,
  avgProgress: 68,
  completedMilestones: 12,
  totalMilestones: 18,
  totalHours: 5.5,
  weeklyHours: 28.5,
  efficiency: 85,
  efficiencyTrend: 3
})

// 我的工作数据
const myWorks = ref([
  {
    id: 1,
    title: 'CFM56发动机燃油喷嘴更换',
    orderNo: 'WO-2024-001',
    status: 'in_progress',
    progress: 75,
    deadline: '2025-07-25 16:00:00',
    spentHours: 18.5,
    estimatedHours: 24,
    milestones: [
      {
        id: 1,
        title: '拆卸发动机外壳',
        description: '按照标准程序拆卸发动机外壳，记录拆卸过程',
        progress: 100,
        targetDate: '2025-07-23 12:00:00',
        completed: true,
        tasks: [
          { id: 1, title: '准备工具和设备', completed: true, estimatedTime: 1 },
          { id: 2, title: '拆卸外部管路', completed: true, estimatedTime: 2 },
          { id: 3, title: '拆卸外壳螺栓', completed: true, estimatedTime: 3 },
          { id: 4, title: '记录拆卸过程', completed: true, estimatedTime: 0.5 }
        ]
      },
      {
        id: 2,
        title: '检查燃油喷嘴状态',
        description: '详细检查所有燃油喷嘴的磨损情况和工作状态',
        progress: 80,
        targetDate: '2025-07-24 10:00:00',
        completed: false,
        tasks: [
          { id: 5, title: '拆卸燃油喷嘴', completed: true, estimatedTime: 2 },
          { id: 6, title: '清洁燃油喷嘴', completed: true, estimatedTime: 1.5 },
          { id: 7, title: '测量喷嘴参数', completed: false, estimatedTime: 3 },
          { id: 8, title: '记录检查结果', completed: false, estimatedTime: 1 }
        ]
      },
      {
        id: 3,
        title: '安装新的燃油喷嘴',
        description: '按规定扭矩安装新的燃油喷嘴，确保密封性',
        progress: 0,
        targetDate: '2025-07-25 14:00:00',
        completed: false,
        tasks: [
          { id: 9, title: '准备新燃油喷嘴', completed: false, estimatedTime: 0.5 },
          { id: 10, title: '安装燃油喷嘴', completed: false, estimatedTime: 2 },
          { id: 11, title: '调整扭矩', completed: false, estimatedTime: 1 },
          { id: 12, title: '检查密封性', completed: false, estimatedTime: 1.5 }
        ]
      }
    ]
  },
  {
    id: 2,
    title: 'APU定期检查维护',
    orderNo: 'WO-2024-002',
    status: 'pending',
    progress: 15,
    deadline: '2025-07-28 17:00:00',
    spentHours: 3,
    estimatedHours: 20,
    milestones: [
      {
        id: 4,
        title: '外观检查',
        description: '检查APU外观是否有损坏、腐蚀等问题',
        progress: 60,
        targetDate: '2025-07-24 16:00:00',
        completed: false,
        tasks: [
          { id: 13, title: '检查外壳', completed: true, estimatedTime: 1 },
          { id: 14, title: '检查管路', completed: false, estimatedTime: 1.5 },
          { id: 15, title: '检查电气连接', completed: false, estimatedTime: 1 }
        ]
      }
    ]
  },
  {
    id: 3,
    title: '起落架液压系统维修',
    orderNo: 'WO-2024-003',
    status: 'review',
    progress: 95,
    deadline: '2025-07-24 14:00:00',
    spentHours: 22,
    estimatedHours: 24,
    milestones: []
  }
])

// 工作日志数据
const workLogs = ref([
  {
    id: 1,
    date: '2025-07-23',
    workTitle: 'CFM56发动机燃油喷嘴更换',
    duration: 5.5,
    type: 'normal',
    description: '完成了发动机外壳的拆卸工作，开始进行燃油喷嘴的检查。按照维修手册的要求，逐一拆卸了所有燃油喷嘴，并进行了初步的清洁工作。',
    achievements: [
      '成功拆卸发动机外壳',
      '拆卸12个燃油喷嘴',
      '完成燃油喷嘴清洁工作',
      '记录了拆卸过程的详细步骤'
    ],
    issues: [
      '第7号燃油喷嘴拆卸较困难，螺纹有轻微损坏',
      '清洁过程中发现部分喷嘴有积碳现象'
    ]
  },
  {
    id: 2,
    date: '2025-07-22',
    workTitle: '起落架液压系统维修',
    duration: 6,
    type: 'normal',
    description: '完成了液压系统的最终测试和调试工作，所有参数都符合技术要求。进行了系统的功能性测试，确认维修质量。',
    achievements: [
      '完成液压系统压力测试',
      '调整了系统压力参数',
      '完成功能性测试',
      '编写了维修报告'
    ],
    issues: []
  }
])

// 团队成员数据
const teamMembers = ref([
  {
    id: 1,
    name: '张维修',
    role: '高级维修工程师',
    avatar: '',
    status: 'online',
    currentWork: 'V2500发动机检查',
    activeWorks: 2,
    todayHours: 6.5
  },
  {
    id: 2,
    name: '李技师',
    role: '维修技师',
    avatar: '',
    status: 'busy',
    currentWork: '液压泵维修',
    activeWorks: 3,
    todayHours: 5
  },
  {
    id: 3,
    name: '王工程师',
    role: '维修工程师',
    avatar: '',
    status: 'offline',
    currentWork: null,
    activeWorks: 1,
    todayHours: 0
  }
])

// 协作任务数据
const collaborations = ref([
  {
    id: 1,
    title: 'CFM56发动机大修项目',
    description: '多名工程师协作完成发动机的全面大修工作',
    status: 'active',
    progress: 65,
    deadline: '2025-08-15 17:00:00',
    participants: [
      { id: 1, name: '张维修', avatar: '' },
      { id: 2, name: '李技师', avatar: '' },
      { id: 4, name: '陈专家', avatar: '' }
    ]
  },
  {
    id: 2,
    title: 'APU系统升级改造',
    description: '升级APU控制系统，提升性能和可靠性',
    status: 'planning',
    progress: 15,
    deadline: '2025-09-30 17:00:00',
    participants: [
      { id: 3, name: '王工程师', avatar: '' },
      { id: 5, name: '赵专家', avatar: '' }
    ]
  }
])

// 对话框状态
const quickUpdateDialog = ref({
  visible: false,
  loading: false
})

const quickUpdateForm = ref({
  workId: '',
  progress: 0,
  hoursSpent: 0,
  workDone: '',
  issues: ''
})

const workLogDialog = ref({
  visible: false,
  loading: false
})

const workLogForm = ref({
  date: new Date(),
  duration: 0,
  type: 'normal',
  description: '',
  achievements: [''],
  issues: ['']
})

// 计算属性
const filteredMyWork = computed(() => {
  if (!workFilter.value) {
    return myWorks.value
  }
  return myWorks.value.filter(work => work.status === workFilter.value)
})

// 方法
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatLogDate = (dateStr) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const getWorkStatusLabel = (status) => {
  const labels = {
    pending: '待开始',
    in_progress: '进行中',
    paused: '已暂停',
    review: '待验收',
    completed: '已完成'
  }
  return labels[status] || status
}

const getWorkStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    paused: 'info',
    review: 'success',
    completed: 'success'
  }
  return colors[status] || ''
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getLogTypeLabel = (type) => {
  const labels = {
    normal: '正常工作',
    overtime: '加班工作',
    training: '学习培训',
    meeting: '会议讨论'
  }
  return labels[type] || type
}

const getCollabStatusLabel = (status) => {
  const labels = {
    planning: '规划中',
    active: '进行中',
    paused: '已暂停',
    completed: '已完成'
  }
  return labels[status] || status
}

const getCollabStatusColor = (status) => {
  const colors = {
    planning: 'info',
    active: 'primary',
    paused: 'warning',
    completed: 'success'
  }
  return colors[status] || ''
}

// 事件处理
const handleRefreshProgress = () => {
  ElMessage.success('进度数据已刷新')
}

const handleExportReport = () => {
  ElMessage.info('正在导出进度报告...')
}

const handleQuickUpdate = () => {
  quickUpdateDialog.value.visible = true
  if (selectedWork.value) {
    quickUpdateForm.value.workId = selectedWork.value.id
    quickUpdateForm.value.progress = selectedWork.value.progress
  }
}

const handleTabChange = (tabName) => {
  console.log('切换标签:', tabName)
}

const handleSelectWork = (work) => {
  selectedWork.value = work
}

const handleUpdateProgress = (work) => {
  quickUpdateDialog.value.visible = true
  quickUpdateForm.value.workId = work.id
  quickUpdateForm.value.progress = work.progress
}

const handleLogWork = (work) => {
  workLogDialog.value.visible = true
  workLogForm.value.workTitle = work.title
}

const handleViewWorkOrder = () => {
  ElMessage.info('查看关联工单')
}

const handleEditMilestones = () => {
  ElMessage.info('编辑里程碑')
}

const handleAddMilestone = () => {
  ElMessage.info('添加新里程碑')
}

const handleTaskToggle = (task, milestone) => {
  // 重新计算里程碑进度
  const completedTasks = milestone.tasks.filter(t => t.completed).length
  milestone.progress = Math.round((completedTasks / milestone.tasks.length) * 100)
  
  if (milestone.progress === 100) {
    milestone.completed = true
  }
  
  ElMessage.success(`任务"${task.title}"已${task.completed ? '完成' : '取消完成'}`)
}

const handleMilestoneAction = ({ action, milestone }) => {
  switch (action) {
    case 'edit':
      ElMessage.info(`编辑里程碑: ${milestone.title}`)
      break
    case 'complete':
      milestone.completed = true
      milestone.progress = 100
      ElMessage.success(`里程碑"${milestone.title}"已标记为完成`)
      break
    case 'delete':
      ElMessage.warning(`删除里程碑: ${milestone.title}`)
      break
  }
}

const handleStartTimer = () => {
  timerRunning.value = true
  timer = setInterval(() => {
    currentSessionTime.value++
    todayTotalTime.value++
  }, 1000)
  ElMessage.success('开始计时')
}

const handleStopTimer = () => {
  timerRunning.value = false
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  ElMessage.info('停止计时')
}

const handleLogDateChange = () => {
  // 筛选日志
}

const handleLogSearch = () => {
  ElMessage.info('筛选工作日志')
}

const handleResetLogFilter = () => {
  logDateRange.value = []
  logTypeFilter.value = ''
  ElMessage.info('重置筛选条件')
}

const handleEditLog = (log) => {
  ElMessage.info(`编辑日志: ${log.id}`)
}

const handleDeleteLog = (log) => {
  ElMessage.warning(`删除日志: ${log.id}`)
}

const handleChatWithMember = (member) => {
  ElMessage.info(`与 ${member.name} 开始聊天`)
}

const handleCreateCollaboration = () => {
  ElMessage.info('发起新的协作任务')
}

const handleJoinCollaboration = (collab) => {
  ElMessage.success(`已加入协作: ${collab.title}`)
}

const handleViewCollaboration = (collab) => {
  ElMessage.info(`查看协作详情: ${collab.title}`)
}

const handleSubmitQuickUpdate = () => {
  if (!quickUpdateForm.value.workId) {
    ElMessage.warning('请选择要更新的工作')
    return
  }
  
  quickUpdateDialog.value.loading = true
  
  setTimeout(() => {
    // 更新工作进度
    const work = myWorks.value.find(w => w.id === quickUpdateForm.value.workId)
    if (work) {
      work.progress = quickUpdateForm.value.progress
      work.spentHours += quickUpdateForm.value.hoursSpent
    }
    
    ElMessage.success('进度更新成功')
    quickUpdateDialog.value.visible = false
    quickUpdateDialog.value.loading = false
    
    // 重置表单
    quickUpdateForm.value = {
      workId: '',
      progress: 0,
      hoursSpent: 0,
      workDone: '',
      issues: ''
    }
  }, 1000)
}

const handleSubmitWorkLog = () => {
  if (!workLogForm.value.description) {
    ElMessage.warning('请填写工作描述')
    return
  }
  
  workLogDialog.value.loading = true
  
  setTimeout(() => {
    // 添加工作日志
    const newLog = {
      id: Date.now(),
      date: workLogForm.value.date.toISOString().split('T')[0],
      workTitle: workLogForm.value.workTitle || '工作记录',
      duration: workLogForm.value.duration,
      type: workLogForm.value.type,
      description: workLogForm.value.description,
      achievements: workLogForm.value.achievements.filter(a => a.trim()),
      issues: workLogForm.value.issues.filter(i => i.trim())
    }
    
    workLogs.value.unshift(newLog)
    
    ElMessage.success('工作日志保存成功')
    workLogDialog.value.visible = false
    workLogDialog.value.loading = false
    
    // 重置表单
    workLogForm.value = {
      date: new Date(),
      duration: 0,
      type: 'normal',
      description: '',
      achievements: [''],
      issues: ['']
    }
  }, 1000)
}

const addAchievement = () => {
  workLogForm.value.achievements.push('')
}

const removeAchievement = (index) => {
  workLogForm.value.achievements.splice(index, 1)
}

const addIssue = () => {
  workLogForm.value.issues.push('')
}

const removeIssue = (index) => {
  workLogForm.value.issues.splice(index, 1)
}

// 生命周期
onMounted(() => {
  // 选择第一个工作项目
  if (myWorks.value.length > 0) {
    selectedWork.value = myWorks.value[0]
  }
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.progress-tracking {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.progress-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-detail {
  @apply text-xs text-gray-500;
}

.main-content {
  @apply modern-card;
}

.my-work-panel, .detailed-panel, .logs-panel, .team-panel {
  @apply p-6;
}

.work-list {
  @apply space-y-4;
}

.list-header {
  @apply flex items-center justify-between mb-6;
}

.list-actions {
  @apply flex items-center space-x-2;
}

.work-items {
  @apply space-y-4;
}

.work-item {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer transition-all hover:shadow-md;
}

.work-item.selected {
  @apply border-primary-500 bg-primary-50;
}

.work-header {
  @apply flex items-start justify-between mb-3;
}

.work-info h4 {
  @apply font-semibold text-gray-900 mb-1;
}

.work-info p {
  @apply text-sm text-gray-600;
}

.work-progress {
  @apply flex items-center space-x-3 mb-3;
}

.progress-bar {
  @apply flex-1;
}

.progress-text {
  @apply text-sm font-medium text-gray-700;
}

.work-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500 mb-3;
}

.meta-item {
  @apply flex items-center space-x-1;
}

.work-actions {
  @apply flex items-center space-x-2;
}

.detail-header {
  @apply flex items-center justify-between mb-6;
}

.detail-info h3 {
  @apply text-xl font-semibold text-gray-900 mb-1;
}

.detail-info p {
  @apply text-gray-600;
}

.detail-actions {
  @apply flex items-center space-x-2;
}

.progress-chart {
  @apply modern-card p-6 mb-6;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800;
}

.chart-controls {
  @apply flex items-center space-x-2;
}

.chart-container {
  @apply h-64 flex items-center justify-center bg-gray-50 rounded-lg;
}

.chart-placeholder {
  @apply text-center;
}

.milestones-section, .time-tracking {
  @apply modern-card p-6 mb-6;
}

.section-header {
  @apply flex items-center justify-between mb-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-800;
}

.milestones-list {
  @apply space-y-4;
}

.milestone-item {
  @apply flex space-x-4 p-4 border border-gray-200 rounded-lg;
}

.milestone-item.completed {
  @apply bg-green-50 border-green-200;
}

.milestone-marker {
  @apply mt-1;
}

.milestone-content {
  @apply flex-1;
}

.milestone-header {
  @apply flex items-start justify-between mb-2;
}

.milestone-title {
  @apply font-semibold text-gray-900;
}

.milestone-meta {
  @apply text-sm text-gray-500 space-x-3;
}

.milestone-desc {
  @apply text-sm text-gray-600 mb-3;
}

.milestone-tasks {
  @apply space-y-2;
}

.task-item {
  @apply flex items-center justify-between p-2 bg-gray-50 rounded text-sm;
}

.task-item.completed {
  @apply bg-green-50 text-green-800;
}

.task-time {
  @apply text-xs text-gray-500;
}

.milestone-actions {
  @apply mt-1;
}

.time-controls {
  @apply flex items-center space-x-2;
}

.timer-display {
  @apply grid grid-cols-2 gap-6;
}

.current-session, .today-total {
  @apply text-center p-6 bg-gray-50 rounded-lg;
}

.session-time, .total-time {
  @apply text-3xl font-bold text-primary-600 mb-2;
}

.session-label, .total-label {
  @apply text-sm text-gray-600;
}

.no-selection {
  @apply flex items-center justify-center h-96;
}

.no-selection-content {
  @apply text-center;
}

.logs-filter {
  @apply modern-card p-4 mb-6;
}

.filter-row {
  @apply flex items-end space-x-4;
}

.filter-group {
  @apply flex-1;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.filter-actions {
  @apply flex items-center space-x-2;
}

.logs-list {
  @apply modern-card p-6;
}

.logs-timeline {
  @apply space-y-6;
}

.log-item {
  @apply flex space-x-4;
}

.log-date {
  @apply flex items-center space-x-2 w-32 flex-shrink-0;
}

.date-marker {
  @apply w-3 h-3 bg-blue-500 rounded-full;
}

.date-text {
  @apply text-sm font-medium text-gray-700;
}

.log-content {
  @apply flex-1 pb-4 border-b border-gray-200;
}

.log-header {
  @apply flex items-start justify-between mb-2;
}

.log-title {
  @apply font-semibold text-gray-900;
}

.log-meta {
  @apply text-sm text-gray-500 space-x-3;
}

.log-description {
  @apply text-sm text-gray-600 mb-4;
}

.log-achievements, .log-issues {
  @apply mb-4;
}

.achievements-title, .issues-title {
  @apply text-sm font-semibold text-gray-800 mb-2;
}

.achievements-list, .issues-list {
  @apply list-disc list-inside text-sm text-gray-600 space-y-1;
}

.log-actions {
  @apply flex items-center space-x-2;
}

.team-status {
  @apply mb-8;
}

.team-members {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.member-card {
  @apply modern-card p-6;
}

.member-avatar {
  @apply relative mb-4;
}

.status-indicator {
  @apply absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white;
}

.status-indicator.online {
  @apply bg-green-500;
}

.status-indicator.busy {
  @apply bg-orange-500;
}

.status-indicator.offline {
  @apply bg-gray-400;
}

.member-info {
  @apply text-center mb-4;
}

.member-name {
  @apply font-semibold text-gray-900 mb-1;
}

.member-role {
  @apply text-sm text-gray-600 mb-2;
}

.current-work {
  @apply text-sm text-blue-600;
}

.member-stats {
  @apply flex justify-center space-x-4 text-center mb-4;
}

.stat-item {
  @apply space-y-1;
}

.stat-value {
  @apply block text-lg font-bold text-gray-900;
}

.stat-label {
  @apply text-xs text-gray-500;
}

.member-actions {
  @apply text-center;
}

.collaboration-tasks {
  @apply space-y-4;
}

.collaboration-list {
  @apply space-y-4;
}

.collaboration-item {
  @apply modern-card p-6;
}

.collab-header {
  @apply flex items-start justify-between mb-3;
}

.collab-title {
  @apply font-semibold text-gray-900;
}

.collab-description {
  @apply text-sm text-gray-600 mb-4;
}

.collab-participants {
  @apply flex items-center space-x-3 mb-4;
}

.participants-label {
  @apply text-sm font-medium text-gray-700;
}

.participants-list {
  @apply flex items-center;
}

.collab-meta {
  @apply flex items-center space-x-4 text-sm text-gray-500 mb-4;
}

.collab-actions {
  @apply flex items-center space-x-2;
}

.quick-update-form, .work-log-form {
  @apply space-y-4;
}

.achievements-input, .issues-input {
  @apply space-y-2;
}

.achievement-input-item, .issue-input-item {
  @apply flex items-center space-x-2;
}
</style>