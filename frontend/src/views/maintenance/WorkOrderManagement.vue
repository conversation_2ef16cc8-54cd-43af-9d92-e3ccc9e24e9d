<!--
维修工单管理系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的完整工单管理界面
特点：
1. 工单全生命周期管理
2. 任务分配和进度跟踪
3. 技术文档集成
4. 质量控制和验收
5. 成本统计和分析
-->

<template>
  <div class="work-order-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">维修工单管理</h1>
          <p class="page-subtitle">全面管理维修工作单，跟踪任务进度，确保维修质量和效率</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshOrders">
            <Refresh class="w-4 h-4 mr-2" />
            刷新列表
          </el-button>
          <el-button @click="handleBatchAssign" :disabled="!selectedOrders.length">
            <Users class="w-4 h-4 mr-2" />
            批量分配
          </el-button>
          <el-button @click="handleCreateOrder" type="primary">
            <Plus class="w-4 h-4 mr-2" />
            创建工单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 工单状态概览 -->
    <div class="order-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card pending">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderStats.pendingCount }}</div>
            <div class="card-label">待处理工单</div>
            <div class="card-trend">紧急处理</div>
          </div>
        </div>
        
        <div class="overview-card progress">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Settings class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderStats.inProgressCount }}</div>
            <div class="card-label">进行中工单</div>
            <div class="card-trend">{{ orderStats.avgProgress }}% 平均进度</div>
          </div>
        </div>
        
        <div class="overview-card completed">
          <div class="card-icon bg-green-100 text-green-600">
            <CircleCheck class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderStats.completedToday }}</div>
            <div class="card-label">今日完成</div>
            <div class="card-trend positive">+{{ orderStats.completedGrowth }}% 本周</div>
          </div>
        </div>
        
        <div class="overview-card efficiency">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderStats.efficiency }}%</div>
            <div class="card-label">按时完成率</div>
            <div class="card-trend neutral">本月统计</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">工单状态</label>
            <el-select v-model="filters.status" placeholder="选择状态" clearable>
              <el-option label="全部状态" value="" />
              <el-option label="待分配" value="pending" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="待验收" value="inspection" />
              <el-option label="已完成" value="completed" />
              <el-option label="已暂停" value="paused" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">优先级</label>
            <el-select v-model="filters.priority" placeholder="选择优先级" clearable>
              <el-option label="全部优先级" value="" />
              <el-option label="紧急" value="urgent" />
              <el-option label="重要" value="high" />
              <el-option label="普通" value="normal" />
              <el-option label="低优先级" value="low" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">维修类型</label>
            <el-select v-model="filters.maintenanceType" placeholder="选择类型" clearable>
              <el-option label="全部类型" value="" />
              <el-option label="预防性维修" value="preventive" />
              <el-option label="修复性维修" value="corrective" />
              <el-option label="改进性维修" value="improvement" />
              <el-option label="紧急维修" value="emergency" />
            </el-select>
          </div>
          
          <div class="filter-group">
            <label class="filter-label">负责人</label>
            <el-select v-model="filters.assignee" placeholder="选择负责人" clearable filterable>
              <el-option label="全部人员" value="" />
              <el-option
                v-for="engineer in engineerList"
                :key="engineer.id"
                :label="engineer.name"
                :value="engineer.id"
              />
            </el-select>
          </div>
        </div>
        
        <div class="filter-row">
          <div class="filter-group flex-1">
            <label class="filter-label">搜索工单</label>
            <el-input
              v-model="filters.keyword"
              placeholder="搜索工单号、设备名称、故障描述..."
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
          
          <div class="filter-actions">
            <el-button @click="handleSearch" type="primary">搜索</el-button>
            <el-button @click="handleResetFilters">重置</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单列表 -->
    <div class="order-list">
      <el-table
        :data="filteredOrders"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        class="work-order-table"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="orderNo" label="工单号" width="120" sortable>
          <template #default="{ row }">
            <el-link @click="handleViewOrder(row)" type="primary">
              {{ row.orderNo }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="工单标题" min-width="200">
          <template #default="{ row }">
            <div class="order-title">
              <span class="title-text">{{ row.title }}</span>
              <div class="order-tags">
                <el-tag v-if="row.isUrgent" type="danger" size="small">紧急</el-tag>
                <el-tag v-if="row.isAog" type="warning" size="small">AOG</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="equipment" label="设备信息" width="150">
          <template #default="{ row }">
            <div class="equipment-info">
              <div class="equipment-name">{{ row.equipment.name }}</div>
              <div class="equipment-model">{{ row.equipment.model }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="maintenanceType" label="维修类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getMaintenanceTypeColor(row.maintenanceType)" size="small">
              {{ getMaintenanceTypeLabel(row.maintenanceType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="assignee" label="负责人" width="100">
          <template #default="{ row }">
            <div class="assignee-info">
              <el-avatar :size="24" :src="row.assignee?.avatar">
                {{ row.assignee?.name?.charAt(0) }}
              </el-avatar>
              <span class="assignee-name">{{ row.assignee?.name || '未分配' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <div class="progress-info">
              <el-progress
                :percentage="row.progress"
                :color="getProgressColor(row.progress)"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="deadline" label="截止时间" width="120" sortable>
          <template #default="{ row }">
            <div class="deadline-info" :class="{ 'overdue': isOverdue(row.deadline) }">
              <Calendar class="w-3 h-3 mr-1" />
              {{ formatDate(row.deadline) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="estimatedCost" label="预估成本" width="100" sortable>
          <template #default="{ row }">
            <span class="cost-amount">¥{{ formatCurrency(row.estimatedCost) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.status === 'pending'"
                @click="handleAssignOrder(row)"
                type="primary"
                size="small"
              >
                分配
              </el-button>
              
              <el-button
                v-if="row.status === 'in_progress'"
                @click="handleUpdateProgress(row)"
                type="success"
                size="small"
              >
                更新进度
              </el-button>
              
              <el-button
                v-if="row.status === 'inspection'"
                @click="handleInspection(row)"
                type="warning"
                size="small"
              >
                验收
              </el-button>
              
              <el-dropdown @command="handleOrderAction">
                <el-button size="small">
                  更多<MoreVertical class="w-3 h-3 ml-1" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'view', order: row}">
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'edit', order: row}">
                      编辑工单
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'clone', order: row}">
                      复制工单
                    </el-dropdown-item>
                    <el-dropdown-item v-if="row.status !== 'completed'" :command="{action: 'pause', order: row}">
                      暂停工单
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'history', order: row}">
                      查看历史
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 创建工单对话框 -->
    <el-dialog
      v-model="createOrderDialog.visible"
      title="创建维修工单"
      width="800px"
      @close="handleCloseCreateDialog"
    >
      <div class="create-order-form">
        <el-form
          ref="createOrderFormRef"
          :model="createOrderForm"
          :rules="createOrderRules"
          label-width="120px"
        >
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <el-form-item label="工单标题" prop="title">
              <el-input v-model="createOrderForm.title" placeholder="请输入工单标题" />
            </el-form-item>
            
            <el-form-item label="设备信息" prop="equipmentId">
              <el-select
                v-model="createOrderForm.equipmentId"
                placeholder="选择设备"
                filterable
                remote
                :remote-method="searchEquipment"
                style="width: 100%"
              >
                <el-option
                  v-for="equipment in equipmentOptions"
                  :key="equipment.id"
                  :label="`${equipment.name} - ${equipment.model}`"
                  :value="equipment.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="维修类型" prop="maintenanceType">
              <el-select v-model="createOrderForm.maintenanceType" placeholder="选择维修类型">
                <el-option label="预防性维修" value="preventive" />
                <el-option label="修复性维修" value="corrective" />
                <el-option label="改进性维修" value="improvement" />
                <el-option label="紧急维修" value="emergency" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="createOrderForm.priority" placeholder="选择优先级">
                <el-option label="紧急" value="urgent" />
                <el-option label="重要" value="high" />
                <el-option label="普通" value="normal" />
                <el-option label="低优先级" value="low" />
              </el-select>
            </el-form-item>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">任务详情</h3>
            
            <el-form-item label="故障描述" prop="faultDescription">
              <el-input
                v-model="createOrderForm.faultDescription"
                type="textarea"
                :rows="4"
                placeholder="详细描述设备故障情况..."
              />
            </el-form-item>
            
            <el-form-item label="维修要求" prop="requirements">
              <el-input
                v-model="createOrderForm.requirements"
                type="textarea"
                :rows="3"
                placeholder="描述维修要求和注意事项..."
              />
            </el-form-item>
            
            <el-form-item label="预计工时" prop="estimatedHours">
              <el-input-number
                v-model="createOrderForm.estimatedHours"
                :min="0.5"
                :step="0.5"
                placeholder="小时"
              />
            </el-form-item>
            
            <el-form-item label="预计成本" prop="estimatedCost">
              <el-input-number
                v-model="createOrderForm.estimatedCost"
                :min="0"
                :step="100"
                placeholder="元"
              />
            </el-form-item>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">分配和时间</h3>
            
            <el-form-item label="负责人">
              <el-select
                v-model="createOrderForm.assigneeId"
                placeholder="选择负责人（可稍后分配）"
                clearable
              >
                <el-option
                  v-for="engineer in engineerList"
                  :key="engineer.id"
                  :label="engineer.name"
                  :value="engineer.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="计划开始时间" prop="plannedStartTime">
              <el-date-picker
                v-model="createOrderForm.plannedStartTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="createOrderForm.deadline"
                type="datetime"
                placeholder="选择截止时间"
                style="width: 100%"
              />
            </el-form-item>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">附加选项</h3>
            
            <el-form-item>
              <el-checkbox v-model="createOrderForm.isUrgent">紧急工单</el-checkbox>
              <el-checkbox v-model="createOrderForm.isAog">AOG (Aircraft on Ground)</el-checkbox>
              <el-checkbox v-model="createOrderForm.requiresApproval">需要审批</el-checkbox>
            </el-form-item>
            
            <el-form-item label="备注">
              <el-input
                v-model="createOrderForm.notes"
                type="textarea"
                :rows="2"
                placeholder="其他备注信息..."
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createOrderDialog.visible = false">取消</el-button>
          <el-button @click="handleSaveAsDraft" type="info">保存草稿</el-button>
          <el-button @click="handleCreateOrderSubmit" type="primary" :loading="createOrderDialog.loading">
            创建工单
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工单分配对话框 -->
    <el-dialog
      v-model="assignDialog.visible"
      title="分配工单"
      width="600px"
    >
      <div class="assign-form">
        <div class="order-info">
          <h4>工单信息</h4>
          <p><strong>工单号：</strong>{{ assignDialog.order?.orderNo }}</p>
          <p><strong>标题：</strong>{{ assignDialog.order?.title }}</p>
          <p><strong>设备：</strong>{{ assignDialog.order?.equipment?.name }}</p>
        </div>
        
        <el-form :model="assignForm" label-width="100px">
          <el-form-item label="负责人" required>
            <el-select v-model="assignForm.assigneeId" placeholder="选择负责人" style="width: 100%">
              <el-option
                v-for="engineer in engineerList"
                :key="engineer.id"
                :label="engineer.name"
                :value="engineer.id"
              >
                <div class="engineer-option">
                  <span>{{ engineer.name }}</span>
                  <span class="engineer-workload">当前工单: {{ engineer.currentWorkload || 0 }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="assignForm.startTime"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="备注">
            <el-input
              v-model="assignForm.notes"
              type="textarea"
              :rows="3"
              placeholder="分配说明或特别要求..."
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="assignDialog.visible = false">取消</el-button>
        <el-button @click="handleAssignSubmit" type="primary" :loading="assignDialog.loading">
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, User, Clock, Settings, CircleCheck, TrendingUp,
  Search, Calendar, Warning, MoreVertical
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const selectedOrders = ref([])

// 工单统计数据
const orderStats = ref({
  pendingCount: 15,
  inProgressCount: 28,
  completedToday: 12,
  completedGrowth: 8,
  avgProgress: 65,
  efficiency: 92
})

// 筛选器
const filters = ref({
  status: '',
  priority: '',
  maintenanceType: '',
  assignee: '',
  keyword: ''
})

// 分页
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

// 工程师列表
const engineerList = ref([
  { id: 1, name: '张维修', currentWorkload: 3, avatar: '' },
  { id: 2, name: '李技师', currentWorkload: 5, avatar: '' },
  { id: 3, name: '王工程师', currentWorkload: 2, avatar: '' },
  { id: 4, name: '陈师傅', currentWorkload: 4, avatar: '' },
  { id: 5, name: '赵专家', currentWorkload: 1, avatar: '' }
])

// 设备选项
const equipmentOptions = ref([
  { id: 1, name: 'CFM56-7B发动机', model: 'CFM56-7B26' },
  { id: 2, name: 'V2500发动机', model: 'V2533-A5' },
  { id: 3, name: 'APU辅助动力装置', model: 'GTCP131-9A' },
  { id: 4, name: '起落架系统', model: 'A320-LG-001' },
  { id: 5, name: '液压泵', model: 'HYD-PUMP-3000' }
])

// 工单数据
const workOrders = ref([
  {
    id: 1,
    orderNo: 'WO-2024-001',
    title: 'CFM56发动机燃油喷嘴更换',
    equipment: { name: 'CFM56-7B发动机', model: 'CFM56-7B26' },
    maintenanceType: 'corrective',
    priority: 'urgent',
    assignee: { id: 1, name: '张维修', avatar: '' },
    status: 'in_progress',
    progress: 75,
    deadline: '2025-07-25 16:00:00',
    estimatedCost: 125000,
    isUrgent: true,
    isAog: true,
    createdAt: '2025-07-23 09:00:00'
  },
  {
    id: 2,
    orderNo: 'WO-2024-002',
    title: 'APU定期检查维护',
    equipment: { name: 'APU辅助动力装置', model: 'GTCP131-9A' },
    maintenanceType: 'preventive',
    priority: 'normal',
    assignee: { id: 2, name: '李技师', avatar: '' },
    status: 'pending',
    progress: 0,
    deadline: '2025-07-28 17:00:00',
    estimatedCost: 35000,
    isUrgent: false,
    isAog: false,
    createdAt: '2025-07-23 10:30:00'
  },
  {
    id: 3,
    orderNo: 'WO-2024-003',
    title: '起落架液压系统维修',
    equipment: { name: '起落架系统', model: 'A320-LG-001' },
    maintenanceType: 'corrective',
    priority: 'high',
    assignee: { id: 3, name: '王工程师', avatar: '' },
    status: 'inspection',
    progress: 95,
    deadline: '2025-07-24 14:00:00',
    estimatedCost: 89000,
    isUrgent: false,
    isAog: false,
    createdAt: '2025-07-22 14:15:00'
  },
  {
    id: 4,
    orderNo: 'WO-2024-004',
    title: 'V2500发动机性能测试',
    equipment: { name: 'V2500发动机', model: 'V2533-A5' },
    maintenanceType: 'preventive',
    priority: 'normal',
    assignee: null,
    status: 'pending',
    progress: 0,
    deadline: '2025-07-30 12:00:00',
    estimatedCost: 45000,
    isUrgent: false,
    isAog: false,
    createdAt: '2025-07-23 11:00:00'
  },
  {
    id: 5,
    orderNo: 'WO-2024-005',
    title: '液压泵故障诊断',
    equipment: { name: '液压泵', model: 'HYD-PUMP-3000' },
    maintenanceType: 'corrective',
    priority: 'high',
    assignee: { id: 4, name: '陈师傅', avatar: '' },
    status: 'completed',
    progress: 100,
    deadline: '2025-07-23 18:00:00',
    estimatedCost: 28000,
    isUrgent: false,
    isAog: false,
    createdAt: '2025-07-22 08:30:00'
  }
])

// 创建工单对话框
const createOrderDialog = ref({
  visible: false,
  loading: false
})

const createOrderForm = ref({
  title: '',
  equipmentId: '',
  maintenanceType: '',
  priority: 'normal',
  faultDescription: '',
  requirements: '',
  estimatedHours: null,
  estimatedCost: null,
  assigneeId: '',
  plannedStartTime: null,
  deadline: null,
  isUrgent: false,
  isAog: false,
  requiresApproval: false,
  notes: ''
})

const createOrderRules = {
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  equipmentId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  maintenanceType: [{ required: true, message: '请选择维修类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  faultDescription: [{ required: true, message: '请描述故障情况', trigger: 'blur' }],
  plannedStartTime: [{ required: true, message: '请选择计划开始时间', trigger: 'change' }],
  deadline: [{ required: true, message: '请选择截止时间', trigger: 'change' }]
}

const createOrderFormRef = ref()

// 分配对话框
const assignDialog = ref({
  visible: false,
  loading: false,
  order: null
})

const assignForm = ref({
  assigneeId: '',
  startTime: null,
  notes: ''
})

// 计算属性
const filteredOrders = computed(() => {
  let result = workOrders.value

  if (filters.value.status) {
    result = result.filter(order => order.status === filters.value.status)
  }

  if (filters.value.priority) {
    result = result.filter(order => order.priority === filters.value.priority)
  }

  if (filters.value.maintenanceType) {
    result = result.filter(order => order.maintenanceType === filters.value.maintenanceType)
  }

  if (filters.value.assignee) {
    result = result.filter(order => order.assignee?.id == filters.value.assignee)
  }

  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    result = result.filter(order =>
      order.orderNo.toLowerCase().includes(keyword) ||
      order.title.toLowerCase().includes(keyword) ||
      order.equipment.name.toLowerCase().includes(keyword)
    )
  }

  pagination.value.total = result.length
  return result
})

// 方法
const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const isOverdue = (deadline) => {
  if (!deadline) return false
  return new Date(deadline) < new Date()
}

const getMaintenanceTypeLabel = (type) => {
  const labels = {
    preventive: '预防性',
    corrective: '修复性',
    improvement: '改进性',
    emergency: '紧急'
  }
  return labels[type] || type
}

const getMaintenanceTypeColor = (type) => {
  const colors = {
    preventive: 'info',
    corrective: 'warning',
    improvement: 'success',
    emergency: 'danger'
  }
  return colors[type] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    urgent: '紧急',
    high: '重要',
    normal: '普通',
    low: '低'
  }
  return labels[priority] || priority
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: 'info',
    low: 'success'
  }
  return colors[priority] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '待分配',
    in_progress: '进行中',
    inspection: '待验收',
    completed: '已完成',
    paused: '已暂停'
  }
  return labels[status] || status
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    inspection: 'success',
    completed: 'success',
    paused: 'info'
  }
  return colors[status] || ''
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

// 事件处理
const handleRefreshOrders = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('列表已刷新')
  }, 1000)
}

const handleSearch = () => {
  pagination.value.current = 1
  // 搜索逻辑在computed中处理
}

const handleResetFilters = () => {
  filters.value = {
    status: '',
    priority: '',
    maintenanceType: '',
    assignee: '',
    keyword: ''
  }
  pagination.value.current = 1
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

const handleSortChange = ({ prop, order }) => {
  // 排序逻辑
  console.log('排序:', prop, order)
}

const handleSizeChange = (newSize) => {
  pagination.value.size = newSize
  pagination.value.current = 1
}

const handlePageChange = (newPage) => {
  pagination.value.current = newPage
}

const handleCreateOrder = () => {
  createOrderDialog.value.visible = true
}

const handleCloseCreateDialog = () => {
  createOrderForm.value = {
    title: '',
    equipmentId: '',
    maintenanceType: '',
    priority: 'normal',
    faultDescription: '',
    requirements: '',
    estimatedHours: null,
    estimatedCost: null,
    assigneeId: '',
    plannedStartTime: null,
    deadline: null,
    isUrgent: false,
    isAog: false,
    requiresApproval: false,
    notes: ''
  }
}

const handleCreateOrderSubmit = async () => {
  try {
    await createOrderFormRef.value.validate()
    createOrderDialog.value.loading = true
    
    // 模拟API调用
    setTimeout(() => {
      ElMessage.success('工单创建成功')
      createOrderDialog.value.visible = false
      createOrderDialog.value.loading = false
      handleCloseCreateDialog()
      handleRefreshOrders()
    }, 1500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleSaveAsDraft = () => {
  ElMessage.info('工单已保存为草稿')
  createOrderDialog.value.visible = false
  handleCloseCreateDialog()
}

const handleAssignOrder = (order) => {
  assignDialog.value.order = order
  assignDialog.value.visible = true
  assignForm.value = {
    assigneeId: '',
    startTime: null,
    notes: ''
  }
}

const handleAssignSubmit = () => {
  if (!assignForm.value.assigneeId) {
    ElMessage.warning('请选择负责人')
    return
  }
  
  assignDialog.value.loading = true
  
  setTimeout(() => {
    ElMessage.success('工单分配成功')
    assignDialog.value.visible = false
    assignDialog.value.loading = false
    handleRefreshOrders()
  }, 1000)
}

const handleBatchAssign = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要分配的工单')
    return
  }
  
  ElMessageBox.prompt('请选择负责人', '批量分配工单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    ElMessage.success(`已批量分配 ${selectedOrders.value.length} 个工单`)
    selectedOrders.value = []
    handleRefreshOrders()
  })
}

const handleViewOrder = (order) => {
  ElMessage.info(`查看工单详情: ${order.orderNo}`)
}

const handleUpdateProgress = (order) => {
  ElMessage.info(`更新工单进度: ${order.orderNo}`)
}

const handleInspection = (order) => {
  ElMessage.info(`验收工单: ${order.orderNo}`)
}

const handleOrderAction = ({ action, order }) => {
  switch (action) {
    case 'view':
      handleViewOrder(order)
      break
    case 'edit':
      ElMessage.info(`编辑工单: ${order.orderNo}`)
      break
    case 'clone':
      ElMessage.info(`复制工单: ${order.orderNo}`)
      break
    case 'pause':
      ElMessage.warning(`暂停工单: ${order.orderNo}`)
      break
    case 'history':
      ElMessage.info(`查看历史: ${order.orderNo}`)
      break
  }
}

const searchEquipment = (query) => {
  if (query) {
    // 模拟设备搜索
    console.log('搜索设备:', query)
  }
}

// 生命周期
onMounted(() => {
  handleRefreshOrders()
})

// 监听筛选器变化
watch(filters, () => {
  pagination.value.current = 1
}, { deep: true })
</script>

<style scoped>
.work-order-management {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.order-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-trend {
  @apply text-xs text-gray-500;
}

.card-trend.positive {
  @apply text-green-600;
}

.card-trend.neutral {
  @apply text-blue-600;
}

.filter-section {
  @apply modern-card p-6;
}

.filter-container {
  @apply space-y-4;
}

.filter-row {
  @apply flex items-end space-x-4;
}

.filter-group {
  @apply flex-1 min-w-0;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.filter-actions {
  @apply flex items-center space-x-2;
}

.order-list {
  @apply modern-card;
}

.work-order-table {
  @apply w-full;
}

.order-title {
  @apply space-y-1;
}

.title-text {
  @apply block text-sm font-medium text-gray-900;
}

.order-tags {
  @apply flex items-center space-x-1;
}

.equipment-info {
  @apply space-y-1;
}

.equipment-name {
  @apply text-sm font-medium text-gray-900;
}

.equipment-model {
  @apply text-xs text-gray-500;
}

.assignee-info {
  @apply flex items-center space-x-2;
}

.assignee-name {
  @apply text-sm text-gray-900;
}

.progress-info {
  @apply space-y-1;
}

.progress-text {
  @apply text-xs text-gray-600;
}

.deadline-info {
  @apply flex items-center text-xs text-gray-600;
}

.deadline-info.overdue {
  @apply text-red-600;
}

.cost-amount {
  @apply text-sm font-medium text-gray-900;
}

.action-buttons {
  @apply flex items-center space-x-1;
}

.pagination-container {
  @apply flex justify-center py-6;
}

.create-order-form {
  @apply max-h-96 overflow-y-auto;
}

.form-section {
  @apply mb-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200;
}

.assign-form {
  @apply space-y-4;
}

.order-info {
  @apply p-4 bg-gray-50 rounded-lg;
}

.order-info h4 {
  @apply font-semibold text-gray-900 mb-2;
}

.order-info p {
  @apply text-sm text-gray-600 mb-1;
}

.engineer-option {
  @apply flex items-center justify-between;
}

.engineer-workload {
  @apply text-xs text-gray-500;
}

.dialog-footer {
  @apply flex items-center justify-end space-x-3;
}
</style>