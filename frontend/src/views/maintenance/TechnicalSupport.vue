<!--
技术支持和备件申请系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的技术支持申请和备件需求管理界面
特点：
1. 技术问题咨询和支持申请
2. 备件需求申请和审批流程
3. 专家团队在线咨询
4. 技术资料搜索和下载
5. 经验分享和知识库
-->

<template>
  <div class="technical-support">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">技术支持中心</h1>
          <p class="page-subtitle">获取专业技术支持，快速申请所需备件，提升维修效率和质量</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshData">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleQuickConsult" type="primary">
            <MessageCircle class="w-4 h-4 mr-2" />
            快速咨询
          </el-button>
        </div>
      </div>
    </div>

    <!-- 服务状态概览 -->
    <div class="support-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card support">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Headphones class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ supportStats.activeRequests }}</div>
            <div class="card-label">活跃支持请求</div>
            <div class="card-detail">平均响应: 2小时</div>
          </div>
        </div>
        
        <div class="overview-card parts">
          <div class="card-icon bg-green-100 text-green-600">
            <Package class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ supportStats.pendingParts }}</div>
            <div class="card-label">待处理备件申请</div>
            <div class="card-detail">今日新增: {{ supportStats.newPartsToday }}</div>
          </div>
        </div>
        
        <div class="overview-card experts">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ supportStats.onlineExperts }}</div>
            <div class="card-label">在线专家</div>
            <div class="card-detail">可立即咨询</div>
          </div>
        </div>
        
        <div class="overview-card knowledge">
          <div class="card-icon bg-orange-100 text-orange-600">
            <BookOpen class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ supportStats.knowledgeBase }}</div>
            <div class="card-label">技术文档</div>
            <div class="card-detail">持续更新</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <el-tabs v-model="activeSupportTab" @tab-change="handleTabChange">
        <!-- 技术支持申请 -->
        <el-tab-pane label="技术支持" name="support">
          <div class="support-panel">
            <!-- 快速申请区域 -->
            <div class="quick-support">
              <div class="quick-header">
                <h3 class="text-lg font-semibold text-gray-800">快速申请技术支持</h3>
                <div class="support-types">
                  <el-button-group>
                    <el-button
                      v-for="type in supportTypes"
                      :key="type.key"
                      :type="selectedSupportType === type.key ? 'primary' : ''"
                      @click="selectSupportType(type.key)"
                      size="small"
                    >
                      <component :is="type.icon" class="w-4 h-4 mr-1" />
                      {{ type.label }}
                    </el-button>
                  </el-button-group>
                </div>
              </div>
              
              <div class="support-form">
                <el-form :model="supportForm" :rules="supportRules" ref="supportFormRef" label-width="120px">
                  <div class="form-row">
                    <el-form-item label="问题分类" prop="category" class="form-col">
                      <el-select v-model="supportForm.category" placeholder="选择问题分类">
                        <el-option label="故障诊断" value="diagnosis" />
                        <el-option label="维修指导" value="repair_guide" />
                        <el-option label="技术咨询" value="consultation" />
                        <el-option label="工艺改进" value="process_improvement" />
                        <el-option label="质量问题" value="quality_issue" />
                        <el-option label="其他技术问题" value="other" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="紧急程度" prop="urgency" class="form-col">
                      <el-select v-model="supportForm.urgency" placeholder="选择紧急程度">
                        <el-option label="紧急 (2小时内)" value="urgent" />
                        <el-option label="重要 (4小时内)" value="high" />
                        <el-option label="普通 (1天内)" value="normal" />
                        <el-option label="低优先级 (3天内)" value="low" />
                      </el-select>
                    </el-form-item>
                  </div>
                  
                  <el-form-item label="相关设备">
                    <el-select
                      v-model="supportForm.equipmentId"
                      placeholder="选择相关设备（可选）"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="equipment in equipmentOptions"
                        :key="equipment.id"
                        :label="`${equipment.name} - ${equipment.model}`"
                        :value="equipment.id"
                      />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="问题描述" prop="description">
                    <el-input
                      v-model="supportForm.description"
                      type="textarea"
                      :rows="6"
                      placeholder="请详细描述您遇到的技术问题，包括故障现象、环境条件、已尝试的解决方案等..."
                    />
                  </el-form-item>
                  
                  <el-form-item label="附件上传">
                    <el-upload
                      ref="uploadRef"
                      :file-list="uploadedFiles"
                      :auto-upload="false"
                      multiple
                      drag
                      accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                    >
                      <Upload class="w-16 h-16 text-gray-400" />
                      <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                      </div>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持 jpg/png/pdf/doc 文件，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </el-form-item>
                  
                  <el-form-item>
                    <div class="form-actions">
                      <el-button @click="handleSaveDraft">保存草稿</el-button>
                      <el-button @click="handleSubmitSupport" type="primary" :loading="supportForm.submitting">
                        提交申请
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <!-- 我的支持请求 -->
            <div class="my-support-requests">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">我的支持请求</h3>
                <el-button @click="handleViewAllRequests" size="small">查看全部</el-button>
              </div>
              
              <div class="request-list">
                <div
                  v-for="request in supportRequests.slice(0, 5)"
                  :key="request.id"
                  class="request-item"
                  @click="handleViewRequest(request)"
                >
                  <div class="request-header">
                    <div class="request-id">#{{ request.id }}</div>
                    <div class="request-status">
                      <el-tag :type="getRequestStatusColor(request.status)" size="small">
                        {{ getRequestStatusLabel(request.status) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="request-content">
                    <h4 class="request-title">{{ request.title }}</h4>
                    <p class="request-desc">{{ request.description.substring(0, 100) }}...</p>
                    
                    <div class="request-meta">
                      <span class="request-time">{{ formatDate(request.createdAt) }}</span>
                      <span class="request-expert" v-if="request.assignedExpert">
                        专家: {{ request.assignedExpert }}
                      </span>
                    </div>
                  </div>
                  
                  <div class="request-actions">
                    <el-button v-if="request.hasReply" type="primary" size="small">
                      查看回复
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 备件申请 -->
        <el-tab-pane label="备件申请" name="parts">
          <div class="parts-panel">
            <!-- 快速申请备件 -->
            <div class="quick-parts">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">申请备件</h3>
                <div class="parts-actions">
                  <el-button @click="handleImportFromWorkOrder" size="small">
                    从工单导入
                  </el-button>
                  <el-button @click="handleBatchApply" type="primary" size="small">
                    批量申请
                  </el-button>
                </div>
              </div>
              
              <div class="parts-form">
                <el-form :model="partsForm" :rules="partsRules" ref="partsFormRef" label-width="120px">
                  <div class="form-row">
                    <el-form-item label="关联工单" class="form-col">
                      <el-select
                        v-model="partsForm.workOrderId"
                        placeholder="选择关联工单（可选）"
                        filterable
                        clearable
                      >
                        <el-option
                          v-for="order in workOrderOptions"
                          :key="order.id"
                          :label="`${order.orderNo} - ${order.title}`"
                          :value="order.id"
                        />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="申请原因" prop="reason" class="form-col">
                      <el-select v-model="partsForm.reason" placeholder="选择申请原因">
                        <el-option label="故障更换" value="replacement" />
                        <el-option label="预防性维护" value="maintenance" />
                        <el-option label="改装升级" value="upgrade" />
                        <el-option label="库存补充" value="stock" />
                        <el-option label="紧急需求" value="emergency" />
                      </el-select>
                    </el-form-item>
                  </div>
                  
                  <el-form-item label="备件需求" prop="partsList">
                    <div class="parts-list">
                      <div
                        v-for="(part, index) in partsForm.partsList"
                        :key="index"
                        class="part-item"
                      >
                        <div class="part-row">
                          <el-input
                            v-model="part.partNumber"
                            placeholder="备件号"
                            class="part-number"
                            @blur="handlePartNumberChange(part, index)"
                          />
                          <el-input
                            v-model="part.description"
                            placeholder="备件描述"
                            class="part-desc"
                          />
                          <el-input-number
                            v-model="part.quantity"
                            :min="1"
                            :max="9999"
                            placeholder="数量"
                            class="part-quantity"
                          />
                          <el-select
                            v-model="part.urgency"
                            placeholder="紧急程度"
                            class="part-urgency"
                          >
                            <el-option label="紧急" value="urgent" />
                            <el-option label="普通" value="normal" />
                            <el-option label="备用" value="backup" />
                          </el-select>
                          <el-button
                            @click="handleRemovePart(index)"
                            :disabled="partsForm.partsList.length <= 1"
                            type="danger"
                            size="small"
                            circle
                          >
                            <Minus class="w-3 h-3" />
                          </el-button>
                        </div>
                        
                        <div class="part-details" v-if="part.details">
                          <div class="part-info">
                            <span class="info-item">库存: {{ part.details.stock || 0 }}</span>
                            <span class="info-item">单价: ¥{{ part.details.price || 0 }}</span>
                            <span class="info-item">供应商: {{ part.details.supplier || '未知' }}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div class="add-part">
                        <el-button @click="handleAddPart" type="primary" plain size="small">
                          <Plus class="w-3 h-3 mr-1" />
                          添加备件
                        </el-button>
                      </div>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="预期交付时间" prop="expectedDelivery">
                    <el-date-picker
                      v-model="partsForm.expectedDelivery"
                      type="datetime"
                      placeholder="选择预期交付时间"
                      :disabled-date="disablePastDates"
                    />
                  </el-form-item>
                  
                  <el-form-item label="特别说明">
                    <el-input
                      v-model="partsForm.notes"
                      type="textarea"
                      :rows="3"
                      placeholder="如有特殊要求或说明，请在此填写..."
                    />
                  </el-form-item>
                  
                  <el-form-item>
                    <div class="form-actions">
                      <el-button @click="handleSavePartsDraft">保存草稿</el-button>
                      <el-button @click="handleSubmitParts" type="primary" :loading="partsForm.submitting">
                        提交申请
                      </el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <!-- 备件申请历史 -->
            <div class="parts-history">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">备件申请历史</h3>
                <div class="history-filters">
                  <el-select v-model="partsHistoryFilter" size="small" placeholder="筛选状态">
                    <el-option label="全部状态" value="" />
                    <el-option label="待审批" value="pending" />
                    <el-option label="已批准" value="approved" />
                    <el-option label="采购中" value="purchasing" />
                    <el-option label="已发货" value="shipped" />
                    <el-option label="已完成" value="completed" />
                  </el-select>
                </div>
              </div>
              
              <div class="history-list">
                <el-table :data="filteredPartsHistory" stripe>
                  <el-table-column prop="applicationNo" label="申请单号" width="120">
                    <template #default="{ row }">
                      <el-link @click="handleViewPartsApplication(row)" type="primary">
                        {{ row.applicationNo }}
                      </el-link>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="reason" label="申请原因" width="100">
                    <template #default="{ row }">
                      <el-tag size="small">{{ getReasonLabel(row.reason) }}</el-tag>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="partsCount" label="备件数量" width="80" />
                  
                  <el-table-column prop="totalCost" label="总金额" width="100">
                    <template #default="{ row }">
                      ¥{{ formatCurrency(row.totalCost) }}
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getPartsStatusColor(row.status)" size="small">
                        {{ getPartsStatusLabel(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="expectedDelivery" label="预期交付" width="120">
                    <template #default="{ row }">
                      {{ formatDate(row.expectedDelivery) }}
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="createdAt" label="申请时间" width="120">
                    <template #default="{ row }">
                      {{ formatDate(row.createdAt) }}
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button @click="handleViewPartsApplication(row)" size="small">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 专家咨询 -->
        <el-tab-pane label="专家咨询" name="expert">
          <div class="expert-panel">
            <!-- 在线专家列表 -->
            <div class="online-experts">
              <h3 class="text-lg font-semibold text-gray-800 mb-4">在线专家</h3>
              
              <div class="experts-grid">
                <div
                  v-for="expert in onlineExperts"
                  :key="expert.id"
                  class="expert-card"
                  @click="handleStartConsultation(expert)"
                >
                  <div class="expert-avatar">
                    <el-avatar :size="60" :src="expert.avatar">
                      {{ expert.name.charAt(0) }}
                    </el-avatar>
                    <div class="online-indicator"></div>
                  </div>
                  
                  <div class="expert-info">
                    <h4 class="expert-name">{{ expert.name }}</h4>
                    <p class="expert-title">{{ expert.title }}</p>
                    <div class="expert-specialties">
                      <el-tag
                        v-for="specialty in expert.specialties"
                        :key="specialty"
                        size="small"
                        class="specialty-tag"
                      >
                        {{ specialty }}
                      </el-tag>
                    </div>
                    
                    <div class="expert-stats">
                      <div class="stat-item">
                        <Star class="w-3 h-3" />
                        <span>{{ expert.rating }}</span>
                      </div>
                      <div class="stat-item">
                        <Clock class="w-3 h-3" />
                        <span>{{ expert.avgResponseTime }}min</span>
                      </div>
                      <div class="stat-item">
                        <MessageCircle class="w-3 h-3" />
                        <span>{{ expert.consultations }}次</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="expert-actions">
                    <el-button type="primary" size="small" @click.stop="handleStartConsultation(expert)">
                      开始咨询
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 咨询历史 -->
            <div class="consultation-history">
              <h3 class="text-lg font-semibold text-gray-800 mb-4">咨询历史</h3>
              
              <div class="history-timeline">
                <div
                  v-for="consultation in consultationHistory"
                  :key="consultation.id"
                  class="timeline-item"
                >
                  <div class="timeline-marker"></div>
                  <div class="timeline-content">
                    <div class="consultation-header">
                      <h4 class="consultation-title">{{ consultation.title }}</h4>
                      <div class="consultation-meta">
                        <span class="consultation-expert">专家: {{ consultation.expert }}</span>
                        <span class="consultation-time">{{ formatDate(consultation.createdAt) }}</span>
                      </div>
                    </div>
                    
                    <p class="consultation-summary">{{ consultation.summary }}</p>
                    
                    <div class="consultation-actions">
                      <el-button @click="handleViewConsultation(consultation)" size="small">
                        查看详情
                      </el-button>
                      <el-button @click="handleContinueConsultation(consultation)" size="small" type="primary">
                        继续咨询
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 知识库 -->
        <el-tab-pane label="知识库" name="knowledge">
          <div class="knowledge-panel">
            <!-- 搜索区域 -->
            <div class="knowledge-search">
              <div class="search-header">
                <h3 class="text-lg font-semibold text-gray-800">技术文档搜索</h3>
                <div class="search-stats">
                  <span class="text-sm text-gray-600">共 {{ knowledgeStats.totalDocs }} 个文档</span>
                </div>
              </div>
              
              <div class="search-bar">
                <el-input
                  v-model="knowledgeSearchQuery"
                  placeholder="搜索技术文档、故障解决方案、维修手册..."
                  @keyup.enter="handleKnowledgeSearch"
                  size="large"
                >
                  <template #prefix>
                    <Search class="w-4 h-4 text-gray-400" />
                  </template>
                  <template #append>
                    <el-button @click="handleKnowledgeSearch" type="primary">搜索</el-button>
                  </template>
                </el-input>
              </div>
              
              <div class="search-filters">
                <el-button-group>
                  <el-button
                    v-for="category in knowledgeCategories"
                    :key="category.key"
                    :type="selectedKnowledgeCategory === category.key ? 'primary' : ''"
                    @click="selectKnowledgeCategory(category.key)"
                    size="small"
                  >
                    {{ category.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
            
            <!-- 热门文档 -->
            <div class="popular-docs">
              <h4 class="text-md font-semibold text-gray-800 mb-3">热门文档</h4>
              
              <div class="docs-grid">
                <div
                  v-for="doc in popularDocs"
                  :key="doc.id"
                  class="doc-card"
                  @click="handleViewDocument(doc)"
                >
                  <div class="doc-icon">
                    <component :is="getDocumentIcon(doc.type)" class="w-8 h-8" />
                  </div>
                  
                  <div class="doc-info">
                    <h5 class="doc-title">{{ doc.title }}</h5>
                    <p class="doc-summary">{{ doc.summary }}</p>
                    
                    <div class="doc-meta">
                      <span class="doc-type">{{ getDocumentTypeLabel(doc.type) }}</span>
                      <span class="doc-downloads">{{ doc.downloads }} 次下载</span>
                      <span class="doc-updated">{{ formatDate(doc.updatedAt) }}</span>
                    </div>
                  </div>
                  
                  <div class="doc-actions">
                    <el-button @click.stop="handleDownloadDocument(doc)" size="small">
                      <Download class="w-3 h-3 mr-1" />
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 最近更新 -->
            <div class="recent-updates">
              <h4 class="text-md font-semibold text-gray-800 mb-3">最近更新</h4>
              
              <div class="updates-list">
                <div
                  v-for="update in recentUpdates"
                  :key="update.id"
                  class="update-item"
                >
                  <div class="update-info">
                    <h5 class="update-title">{{ update.title }}</h5>
                    <p class="update-desc">{{ update.description }}</p>
                    <span class="update-time">{{ formatDate(update.updatedAt) }}</span>
                  </div>
                  
                  <div class="update-actions">
                    <el-button @click="handleViewDocument(update)" size="small" type="primary">
                      查看
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 快速咨询对话框 -->
    <el-dialog
      v-model="quickConsultDialog.visible"
      title="快速咨询"
      width="600px"
    >
      <div class="quick-consult-form">
        <el-form :model="quickConsultForm" label-width="100px">
          <el-form-item label="咨询类型">
            <el-select v-model="quickConsultForm.type" placeholder="选择咨询类型">
              <el-option label="故障诊断" value="diagnosis" />
              <el-option label="维修指导" value="repair" />
              <el-option label="技术咨询" value="technical" />
              <el-option label="经验分享" value="experience" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="问题描述">
            <el-input
              v-model="quickConsultForm.question"
              type="textarea"
              :rows="4"
              placeholder="请简要描述您的问题..."
            />
          </el-form-item>
          
          <el-form-item label="指定专家">
            <el-select v-model="quickConsultForm.expertId" placeholder="选择专家（可选）" clearable>
              <el-option
                v-for="expert in onlineExperts"
                :key="expert.id"
                :label="expert.name"
                :value="expert.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="quickConsultDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitQuickConsult" type="primary" :loading="quickConsultDialog.loading">
          发起咨询
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, MessageCircle, Headphones, Package, User, BookOpen,
  Upload, Plus, Minus, Search, Download, Star, Clock
} from '@element-plus/icons-vue'

// 响应式数据
const activeSupportTab = ref('support')
const selectedSupportType = ref('diagnosis')
const selectedKnowledgeCategory = ref('all')
const knowledgeSearchQuery = ref('')
const partsHistoryFilter = ref('')
const uploadedFiles = ref([])

// 统计数据
const supportStats = ref({
  activeRequests: 8,
  pendingParts: 12,
  newPartsToday: 3,
  onlineExperts: 15,
  knowledgeBase: 2847
})

const knowledgeStats = ref({
  totalDocs: 2847,
  totalDownloads: 156890,
  recentUpdates: 23
})

// 支持类型
const supportTypes = ref([
  { key: 'diagnosis', label: '故障诊断', icon: 'Search' },
  { key: 'repair', label: '维修指导', icon: 'Settings' },
  { key: 'consultation', label: '技术咨询', icon: 'MessageCircle' },
  { key: 'quality', label: '质量问题', icon: 'Warning' }
])

// 设备选项
const equipmentOptions = ref([
  { id: 1, name: 'CFM56-7B发动机', model: 'CFM56-7B26' },
  { id: 2, name: 'V2500发动机', model: 'V2533-A5' },
  { id: 3, name: 'APU辅助动力装置', model: 'GTCP131-9A' },
  { id: 4, name: '起落架系统', model: 'A320-LG-001' },
  { id: 5, name: '液压泵', model: 'HYD-PUMP-3000' }
])

// 工单选项
const workOrderOptions = ref([
  { id: 1, orderNo: 'WO-2024-001', title: 'CFM56发动机燃油喷嘴更换' },
  { id: 2, orderNo: 'WO-2024-002', title: 'APU定期检查维护' },
  { id: 3, orderNo: 'WO-2024-003', title: '起落架液压系统维修' }
])

// 支持申请表单
const supportForm = ref({
  category: '',
  urgency: 'normal',
  equipmentId: '',
  description: '',
  submitting: false
})

const supportRules = {
  category: [{ required: true, message: '请选择问题分类', trigger: 'change' }],
  urgency: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
  description: [{ required: true, message: '请描述技术问题', trigger: 'blur' }]
}

const supportFormRef = ref()

// 备件申请表单
const partsForm = ref({
  workOrderId: '',
  reason: '',
  partsList: [
    {
      partNumber: '',
      description: '',
      quantity: 1,
      urgency: 'normal',
      details: null
    }
  ],
  expectedDelivery: null,
  notes: '',
  submitting: false
})

const partsRules = {
  reason: [{ required: true, message: '请选择申请原因', trigger: 'change' }],
  partsList: [{ required: true, message: '请添加至少一个备件', trigger: 'blur' }],
  expectedDelivery: [{ required: true, message: '请选择预期交付时间', trigger: 'change' }]
}

const partsFormRef = ref()

// 快速咨询对话框
const quickConsultDialog = ref({
  visible: false,
  loading: false
})

const quickConsultForm = ref({
  type: '',
  question: '',
  expertId: ''
})

// 支持请求数据
const supportRequests = ref([
  {
    id: 'SR-2024-001',
    title: 'CFM56发动机高压压气机叶片损坏分析',
    description: '发现发动机高压压气机第3级叶片出现裂纹，需要专业技术指导确定维修方案...',
    status: 'in_progress',
    createdAt: '2025-07-23 09:30:00',
    assignedExpert: '张技术专家',
    hasReply: true
  },
  {
    id: 'SR-2024-002',
    title: 'APU启动异常故障诊断请求',
    description: 'APU在启动过程中出现EGT温度异常升高，怀疑是燃油系统问题...',
    status: 'pending',
    createdAt: '2025-07-23 14:15:00',
    assignedExpert: null,
    hasReply: false
  },
  {
    id: 'SR-2024-003',
    title: '液压系统压力不稳定技术咨询',
    description: '起落架液压系统在收放过程中压力波动较大，需要技术指导...',
    status: 'completed',
    createdAt: '2025-07-22 16:45:00',
    assignedExpert: '李工程师',
    hasReply: true
  }
])

// 备件申请历史
const partsApplications = ref([
  {
    id: 1,
    applicationNo: 'PA-2024-001',
    reason: 'replacement',
    partsCount: 3,
    totalCost: 125000,
    status: 'approved',
    expectedDelivery: '2025-07-28 10:00:00',
    createdAt: '2025-07-23 09:00:00'
  },
  {
    id: 2,
    applicationNo: 'PA-2024-002',
    reason: 'maintenance',
    partsCount: 5,
    totalCost: 68000,
    status: 'purchasing',
    expectedDelivery: '2025-07-30 14:00:00',
    createdAt: '2025-07-23 11:30:00'
  },
  {
    id: 3,
    applicationNo: 'PA-2024-003',
    reason: 'emergency',
    partsCount: 1,
    totalCost: 89000,
    status: 'completed',
    expectedDelivery: '2025-07-24 16:00:00',
    createdAt: '2025-07-22 08:15:00'
  }
])

// 在线专家
const onlineExperts = ref([
  {
    id: 1,
    name: '张技术专家',
    title: '发动机维修专家',
    avatar: '',
    specialties: ['发动机', '燃油系统', '性能分析'],
    rating: 4.9,
    avgResponseTime: 15,
    consultations: 234
  },
  {
    id: 2,
    name: '李工程师',
    title: '液压系统专家',
    avatar: '',
    specialties: ['液压系统', '起落架', '刹车系统'],
    rating: 4.8,
    avgResponseTime: 12,
    consultations: 189
  },
  {
    id: 3,
    name: '王高级工程师',
    title: '航电系统专家',
    avatar: '',
    specialties: ['航电系统', '导航设备', '通信系统'],
    rating: 4.9,
    avgResponseTime: 18,
    consultations: 167
  },
  {
    id: 4,
    name: '陈资深专家',
    title: '结构维修专家',
    avatar: '',
    specialties: ['机身结构', '复合材料', '腐蚀防护'],
    rating: 5.0,
    avgResponseTime: 25,
    consultations: 298
  }
])

// 咨询历史
const consultationHistory = ref([
  {
    id: 1,
    title: '发动机叶片检查标准咨询',
    expert: '张技术专家',
    summary: '关于CFM56发动机叶片裂纹检查的标准和方法讨论，获得了详细的检查指导...',
    createdAt: '2025-07-22 10:30:00'
  },
  {
    id: 2,
    title: '液压油品质分析',
    expert: '李工程师',
    summary: '针对液压油变色问题进行了深入分析，确定了更换标准和预防措施...',
    createdAt: '2025-07-21 15:45:00'
  }
])

// 知识库分类
const knowledgeCategories = ref([
  { key: 'all', label: '全部文档' },
  { key: 'manual', label: '维修手册' },
  { key: 'procedure', label: '操作程序' },
  { key: 'troubleshoot', label: '故障排除' },
  { key: 'technical', label: '技术通告' },
  { key: 'safety', label: '安全指南' }
])

// 热门文档
const popularDocs = ref([
  {
    id: 1,
    title: 'CFM56-7B发动机维修手册',
    summary: '包含发动机拆装、检查、修理的完整程序和技术要求',
    type: 'manual',
    downloads: 1245,
    updatedAt: '2025-07-20 14:30:00'
  },
  {
    id: 2,
    title: 'A320起落架系统故障排除指南',
    summary: '详细描述起落架常见故障现象及排除方法',
    type: 'troubleshoot',
    downloads: 987,
    updatedAt: '2025-07-19 09:15:00'
  },
  {
    id: 3,
    title: '液压系统安全操作程序',
    summary: '液压系统维修作业的安全要求和操作规范',
    type: 'safety',
    downloads: 756,
    updatedAt: '2025-07-18 16:20:00'
  }
])

// 最近更新
const recentUpdates = ref([
  {
    id: 1,
    title: 'V2500发动机叶片检查程序更新',
    description: '新增高周疲劳检查要求和检查间隔调整',
    updatedAt: '2025-07-23 10:00:00'
  },
  {
    id: 2,
    title: 'APU火警系统技术通告',
    description: '关于APU火警探测器灵敏度调整的最新要求',
    updatedAt: '2025-07-22 15:30:00'
  }
])

// 计算属性
const filteredPartsHistory = computed(() => {
  if (!partsHistoryFilter.value) {
    return partsApplications.value
  }
  return partsApplications.value.filter(app => app.status === partsHistoryFilter.value)
})

// 方法
const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const disablePastDates = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}

const getRequestStatusLabel = (status) => {
  const labels = {
    pending: '待处理',
    in_progress: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getRequestStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return colors[status] || ''
}

const getReasonLabel = (reason) => {
  const labels = {
    replacement: '故障更换',
    maintenance: '预防性维护',
    upgrade: '改装升级',
    stock: '库存补充',
    emergency: '紧急需求'
  }
  return labels[reason] || reason
}

const getPartsStatusLabel = (status) => {
  const labels = {
    pending: '待审批',
    approved: '已批准',
    purchasing: '采购中',
    shipped: '已发货',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

const getPartsStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    approved: 'success',
    purchasing: 'primary',
    shipped: 'info',
    completed: 'success',
    rejected: 'danger'
  }
  return colors[status] || ''
}

const getDocumentTypeLabel = (type) => {
  const labels = {
    manual: '维修手册',
    procedure: '操作程序',
    troubleshoot: '故障排除',
    technical: '技术通告',
    safety: '安全指南'
  }
  return labels[type] || type
}

const getDocumentIcon = (type) => {
  const icons = {
    manual: 'BookOpen',
    procedure: 'List',
    troubleshoot: 'Search',
    technical: 'Document',
    safety: 'Shield'
  }
  return icons[type] || 'Document'
}

// 事件处理
const handleRefreshData = () => {
  ElMessage.success('数据已刷新')
}

const handleTabChange = (tabName) => {
  console.log('切换标签:', tabName)
}

const selectSupportType = (type) => {
  selectedSupportType.value = type
}

const selectKnowledgeCategory = (category) => {
  selectedKnowledgeCategory.value = category
}

const handleSubmitSupport = async () => {
  try {
    await supportFormRef.value.validate()
    supportForm.value.submitting = true
    
    setTimeout(() => {
      ElMessage.success('技术支持申请已提交')
      supportForm.value.submitting = false
      // 重置表单
      supportForm.value = {
        category: '',
        urgency: 'normal',
        equipmentId: '',
        description: '',
        submitting: false
      }
    }, 1500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleSaveDraft = () => {
  ElMessage.info('支持申请已保存为草稿')
}

const handleViewRequest = (request) => {
  ElMessage.info(`查看支持请求: ${request.id}`)
}

const handleViewAllRequests = () => {
  ElMessage.info('查看所有支持请求')
}

const handleAddPart = () => {
  partsForm.value.partsList.push({
    partNumber: '',
    description: '',
    quantity: 1,
    urgency: 'normal',
    details: null
  })
}

const handleRemovePart = (index) => {
  partsForm.value.partsList.splice(index, 1)
}

const handlePartNumberChange = (part, index) => {
  if (part.partNumber) {
    // 模拟根据备件号获取详细信息
    setTimeout(() => {
      part.details = {
        stock: Math.floor(Math.random() * 100),
        price: Math.floor(Math.random() * 10000) + 1000,
        supplier: ['波音公司', '空客公司', '通用电气', '霍尼韦尔'][Math.floor(Math.random() * 4)]
      }
      part.description = `${part.partNumber} 对应的备件描述`
    }, 500)
  }
}

const handleSubmitParts = async () => {
  try {
    await partsFormRef.value.validate()
    partsForm.value.submitting = true
    
    setTimeout(() => {
      ElMessage.success('备件申请已提交')
      partsForm.value.submitting = false
      // 重置表单
      partsForm.value = {
        workOrderId: '',
        reason: '',
        partsList: [
          {
            partNumber: '',
            description: '',
            quantity: 1,
            urgency: 'normal',
            details: null
          }
        ],
        expectedDelivery: null,
        notes: '',
        submitting: false
      }
    }, 1500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleSavePartsDraft = () => {
  ElMessage.info('备件申请已保存为草稿')
}

const handleImportFromWorkOrder = () => {
  ElMessage.info('从工单导入备件需求')
}

const handleBatchApply = () => {
  ElMessage.info('批量申请备件')
}

const handleViewPartsApplication = (application) => {
  ElMessage.info(`查看备件申请: ${application.applicationNo}`)
}

const handleStartConsultation = (expert) => {
  ElMessage.info(`开始与 ${expert.name} 专家咨询`)
}

const handleViewConsultation = (consultation) => {
  ElMessage.info(`查看咨询详情: ${consultation.id}`)
}

const handleContinueConsultation = (consultation) => {
  ElMessage.info(`继续咨询: ${consultation.id}`)
}

const handleKnowledgeSearch = () => {
  ElMessage.info(`搜索: ${knowledgeSearchQuery.value}`)
}

const handleViewDocument = (doc) => {
  ElMessage.info(`查看文档: ${doc.title}`)
}

const handleDownloadDocument = (doc) => {
  ElMessage.success(`开始下载: ${doc.title}`)
}

const handleQuickConsult = () => {
  quickConsultDialog.value.visible = true
}

const handleSubmitQuickConsult = () => {
  if (!quickConsultForm.value.question) {
    ElMessage.warning('请描述您的问题')
    return
  }
  
  quickConsultDialog.value.loading = true
  
  setTimeout(() => {
    ElMessage.success('咨询请求已发送，专家将尽快回复')
    quickConsultDialog.value.visible = false
    quickConsultDialog.value.loading = false
    quickConsultForm.value = { type: '', question: '', expertId: '' }
  }, 1000)
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.technical-support {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.support-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-detail {
  @apply text-xs text-gray-500;
}

.main-content {
  @apply modern-card;
}

.support-panel, .parts-panel, .expert-panel, .knowledge-panel {
  @apply space-y-8 p-6;
}

.quick-support, .quick-parts {
  @apply modern-card p-6;
}

.quick-header {
  @apply flex items-center justify-between mb-6;
}

.support-types, .parts-actions {
  @apply flex items-center space-x-2;
}

.support-form, .parts-form {
  @apply space-y-4;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-col {
  @apply flex-1;
}

.form-actions {
  @apply flex items-center justify-end space-x-3;
}

.my-support-requests, .parts-history {
  @apply modern-card p-6;
}

.request-list {
  @apply space-y-4;
}

.request-item {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow;
}

.request-header {
  @apply flex items-center justify-between mb-2;
}

.request-id {
  @apply font-semibold text-primary-600;
}

.request-title {
  @apply font-medium text-gray-900 mb-2;
}

.request-desc {
  @apply text-sm text-gray-600 mb-3;
}

.request-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.request-actions {
  @apply mt-3;
}

.parts-list {
  @apply space-y-4;
}

.part-item {
  @apply p-4 border border-gray-200 rounded-lg;
}

.part-row {
  @apply grid grid-cols-12 gap-3 items-center;
}

.part-number {
  @apply col-span-3;
}

.part-desc {
  @apply col-span-4;
}

.part-quantity {
  @apply col-span-2;
}

.part-urgency {
  @apply col-span-2;
}

.part-details {
  @apply mt-3 p-3 bg-gray-50 rounded;
}

.part-info {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.info-item {
  @apply px-2 py-1 bg-white rounded text-xs;
}

.add-part {
  @apply p-4 border-2 border-dashed border-gray-300 rounded-lg text-center;
}

.history-filters {
  @apply flex items-center space-x-2;
}

.history-list {
  @apply space-y-4;
}

.online-experts {
  @apply space-y-4;
}

.experts-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.expert-card {
  @apply modern-card p-6 cursor-pointer hover:shadow-lg transition-shadow;
}

.expert-avatar {
  @apply relative mb-4 text-center;
}

.online-indicator {
  @apply absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white;
}

.expert-info {
  @apply text-center mb-4;
}

.expert-name {
  @apply font-semibold text-gray-900 mb-1;
}

.expert-title {
  @apply text-sm text-gray-600 mb-3;
}

.expert-specialties {
  @apply flex flex-wrap justify-center gap-1 mb-3;
}

.specialty-tag {
  @apply text-xs;
}

.expert-stats {
  @apply flex justify-center space-x-4 text-xs text-gray-500;
}

.stat-item {
  @apply flex items-center space-x-1;
}

.expert-actions {
  @apply text-center;
}

.consultation-history {
  @apply space-y-4;
}

.history-timeline {
  @apply space-y-6;
}

.timeline-item {
  @apply flex space-x-4;
}

.timeline-marker {
  @apply w-3 h-3 bg-primary-500 rounded-full mt-2 flex-shrink-0;
}

.timeline-content {
  @apply flex-1 pb-6;
}

.consultation-header {
  @apply flex items-start justify-between mb-2;
}

.consultation-title {
  @apply font-medium text-gray-900;
}

.consultation-meta {
  @apply text-sm text-gray-500 space-x-4;
}

.consultation-summary {
  @apply text-sm text-gray-600 mb-4;
}

.consultation-actions {
  @apply space-x-2;
}

.knowledge-search {
  @apply space-y-4;
}

.search-header {
  @apply flex items-center justify-between;
}

.search-bar {
  @apply max-w-2xl;
}

.search-filters {
  @apply flex items-center space-x-2;
}

.popular-docs, .recent-updates {
  @apply space-y-4;
}

.docs-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.doc-card {
  @apply modern-card p-6 cursor-pointer hover:shadow-lg transition-shadow;
}

.doc-icon {
  @apply w-16 h-16 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mb-4;
}

.doc-info {
  @apply mb-4;
}

.doc-title {
  @apply font-semibold text-gray-900 mb-2;
}

.doc-summary {
  @apply text-sm text-gray-600 mb-3;
}

.doc-meta {
  @apply flex flex-wrap gap-3 text-xs text-gray-500;
}

.doc-actions {
  @apply text-center;
}

.updates-list {
  @apply space-y-4;
}

.update-item {
  @apply flex items-start justify-between p-4 border border-gray-200 rounded-lg;
}

.update-info {
  @apply flex-1;
}

.update-title {
  @apply font-medium text-gray-900 mb-1;
}

.update-desc {
  @apply text-sm text-gray-600 mb-2;
}

.update-time {
  @apply text-xs text-gray-500;
}

.update-actions {
  @apply ml-4;
}

.quick-consult-form {
  @apply space-y-4;
}
</style>