<!--
维修成本统计和分析系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的成本控制和分析界面
特点：
1. 维修成本实时统计和分析
2. 成本预算管理和控制
3. 成本趋势分析和预测
4. 成本优化建议和方案
5. 财务报表和成本报告
-->

<template>
  <div class="cost-analysis">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">成本分析中心</h1>
          <p class="page-subtitle">精确分析维修成本，优化资源配置，提升成本效益和盈利能力</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshCosts">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleBudgetManagement">
            <Calculator class="w-4 h-4 mr-2" />
            预算管理
          </el-button>
          <el-button @click="handleExportReport" type="primary">
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 成本概览仪表板 -->
    <div class="cost-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card total-cost">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Money class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatCurrency(costStats.totalCost) }}</div>
            <div class="card-label">总维修成本</div>
            <div class="card-trend" :class="{ positive: costStats.costChange > 0, negative: costStats.costChange < 0 }">
              {{ costStats.costChange > 0 ? '+' : '' }}{{ costStats.costChange }}% 本月
            </div>
          </div>
        </div>
        
        <div class="overview-card labor-cost">
          <div class="card-icon bg-green-100 text-green-600">
            <Users class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatCurrency(costStats.laborCost) }}</div>
            <div class="card-label">人工成本</div>
            <div class="card-detail">{{ costStats.laborHours }}h 工时</div>
          </div>
        </div>
        
        <div class="overview-card material-cost">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Package class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatCurrency(costStats.materialCost) }}</div>
            <div class="card-label">材料成本</div>
            <div class="card-detail">{{ costStats.materialItems }} 项材料</div>
          </div>
        </div>
        
        <div class="overview-card efficiency">
          <div class="card-icon bg-purple-100 text-purple-600">
            <TrendingUp class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ costStats.efficiency }}%</div>
            <div class="card-label">成本效率</div>
            <div class="card-trend positive">+{{ costStats.efficiencyChange }}% 改善</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeCostTab" @tab-change="handleTabChange">
        <!-- 成本统计 -->
        <el-tab-pane label="成本统计" name="statistics">
          <div class="statistics-panel">
            <!-- 时间筛选器 -->
            <div class="time-filter">
              <div class="filter-header">
                <h3 class="text-lg font-semibold text-gray-800">成本统计分析</h3>
                <div class="filter-controls">
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handleDateRangeChange"
                    size="small"
                  />
                  <el-button-group>
                    <el-button
                      v-for="period in timePeriods"
                      :key="period.key"
                      :type="selectedPeriod === period.key ? 'primary' : ''"
                      @click="selectTimePeriod(period.key)"
                      size="small"
                    >
                      {{ period.label }}
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
            
            <!-- 成本构成分析 -->
            <div class="cost-breakdown">
              <div class="breakdown-header">
                <h4 class="breakdown-title">成本构成分析</h4>
                <div class="breakdown-total">
                  总成本: ¥{{ formatCurrency(getTotalCost()) }}
                </div>
              </div>
              
              <div class="breakdown-content">
                <div class="breakdown-chart">
                  <div class="chart-placeholder">
                    <PieChart class="w-24 h-24 text-gray-400" />
                    <p class="text-gray-500 mt-2">成本构成饼图</p>
                  </div>
                </div>
                
                <div class="breakdown-details">
                  <div
                    v-for="item in costBreakdown"
                    :key="item.category"
                    class="breakdown-item"
                  >
                    <div class="item-header">
                      <div class="item-color" :style="{ backgroundColor: item.color }"></div>
                      <span class="item-name">{{ item.name }}</span>
                      <span class="item-percentage">{{ item.percentage }}%</span>
                    </div>
                    
                    <div class="item-details">
                      <div class="item-amount">¥{{ formatCurrency(item.amount) }}</div>
                      <div class="item-change" :class="{ positive: item.change > 0, negative: item.change < 0 }">
                        {{ item.change > 0 ? '+' : '' }}{{ item.change }}%
                      </div>
                    </div>
                    
                    <div class="item-progress">
                      <el-progress
                        :percentage="item.percentage"
                        :color="item.color"
                        :stroke-width="4"
                        :show-text="false"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 项目成本排行 -->
            <div class="cost-ranking">
              <div class="ranking-header">
                <h4 class="ranking-title">项目成本排行</h4>
                <el-select v-model="rankingType" size="small" style="width: 120px">
                  <el-option label="总成本" value="total" />
                  <el-option label="人工成本" value="labor" />
                  <el-option label="材料成本" value="material" />
                  <el-option label="超预算" value="overbudget" />
                </el-select>
              </div>
              
              <div class="ranking-list">
                <div
                  v-for="(project, index) in costRanking"
                  :key="project.id"
                  class="ranking-item"
                  @click="handleViewProjectCost(project)"
                >
                  <div class="ranking-position">
                    <span class="position-number" :class="`rank-${index + 1}`">{{ index + 1 }}</span>
                  </div>
                  
                  <div class="project-info">
                    <h5 class="project-name">{{ project.name }}</h5>
                    <p class="project-order">工单: {{ project.orderNo }}</p>
                  </div>
                  
                  <div class="cost-info">
                    <div class="cost-amount">¥{{ formatCurrency(project.totalCost) }}</div>
                    <div class="cost-budget">
                      预算: ¥{{ formatCurrency(project.budget) }}
                    </div>
                  </div>
                  
                  <div class="cost-status">
                    <el-tag
                      :type="getCostStatusColor(project.status)"
                      size="small"
                    >
                      {{ getCostStatusLabel(project.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 成本趋势 -->
        <el-tab-pane label="成本趋势" name="trends">
          <div class="trends-panel">
            <!-- 趋势图表 -->
            <div class="trend-charts">
              <div class="chart-section">
                <div class="chart-header">
                  <h4 class="chart-title">成本趋势分析</h4>
                  <div class="chart-controls">
                    <el-radio-group v-model="trendChartType" size="small">
                      <el-radio-button label="line">线图</el-radio-button>
                      <el-radio-button label="bar">柱图</el-radio-button>
                      <el-radio-button label="area">面积图</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
                
                <div class="chart-container">
                  <div class="chart-placeholder">
                    <BarChart class="w-32 h-32 text-gray-400" />
                    <p class="text-gray-500 mt-4">成本趋势图表</p>
                    <p class="text-sm text-gray-400">显示各类成本随时间的变化趋势</p>
                  </div>
                </div>
              </div>
              
              <div class="trend-metrics">
                <div class="metrics-header">
                  <h4 class="metrics-title">趋势指标</h4>
                </div>
                
                <div class="metrics-list">
                  <div
                    v-for="metric in trendMetrics"
                    :key="metric.key"
                    class="metric-item"
                  >
                    <div class="metric-icon" :style="{ backgroundColor: metric.color }">
                      <component :is="metric.icon" class="w-4 h-4 text-white" />
                    </div>
                    
                    <div class="metric-content">
                      <div class="metric-name">{{ metric.name }}</div>
                      <div class="metric-value">{{ metric.value }}</div>
                      <div class="metric-trend" :class="{ positive: metric.trend > 0, negative: metric.trend < 0 }">
                        {{ metric.trend > 0 ? '+' : '' }}{{ metric.trend }}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 预测分析 -->
            <div class="forecast-analysis">
              <div class="forecast-header">
                <h4 class="forecast-title">成本预测分析</h4>
                <div class="forecast-controls">
                  <el-select v-model="forecastPeriod" size="small" style="width: 120px">
                    <el-option label="下个月" value="next_month" />
                    <el-option label="下季度" value="next_quarter" />
                    <el-option label="下半年" value="next_half_year" />
                  </el-select>
                </div>
              </div>
              
              <div class="forecast-content">
                <div class="forecast-chart">
                  <div class="chart-placeholder">
                    <TrendingUp class="w-20 h-20 text-gray-400" />
                    <p class="text-gray-500 mt-2">成本预测图表</p>
                  </div>
                </div>
                
                <div class="forecast-summary">
                  <div class="summary-item">
                    <span class="summary-label">预测总成本</span>
                    <span class="summary-value">¥{{ formatCurrency(forecastData.totalCost) }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">预计变化</span>
                    <span class="summary-value" :class="{ positive: forecastData.change > 0, negative: forecastData.change < 0 }">
                      {{ forecastData.change > 0 ? '+' : '' }}{{ forecastData.change }}%
                    </span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">置信度</span>
                    <span class="summary-value">{{ forecastData.confidence }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 预算管理 -->
        <el-tab-pane label="预算管理" name="budget">
          <div class="budget-panel">
            <!-- 预算概览 -->
            <div class="budget-overview">
              <div class="overview-header">
                <h3 class="text-lg font-semibold text-gray-800">预算执行情况</h3>
                <div class="overview-period">
                  <span class="period-label">统计周期:</span>
                  <el-select v-model="budgetPeriod" size="small" style="width: 120px">
                    <el-option label="本月" value="current_month" />
                    <el-option label="本季度" value="current_quarter" />
                    <el-option label="本年度" value="current_year" />
                  </el-select>
                </div>
              </div>
              
              <div class="budget-progress">
                <div
                  v-for="budget in budgetItems"
                  :key="budget.category"
                  class="budget-item"
                >
                  <div class="budget-header">
                    <span class="budget-name">{{ budget.name }}</span>
                    <span class="budget-ratio">{{ budget.usedRatio }}%</span>
                  </div>
                  
                  <div class="budget-bar">
                    <el-progress
                      :percentage="budget.usedRatio"
                      :color="getBudgetColor(budget.usedRatio)"
                      :stroke-width="8"
                    />
                  </div>
                  
                  <div class="budget-details">
                    <span class="budget-used">已用: ¥{{ formatCurrency(budget.used) }}</span>
                    <span class="budget-total">总预算: ¥{{ formatCurrency(budget.total) }}</span>
                    <span class="budget-remaining">剩余: ¥{{ formatCurrency(budget.remaining) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 预算预警 -->
            <div class="budget-alerts">
              <div class="alerts-header">
                <h4 class="alerts-title">预算预警</h4>
                <el-badge :value="budgetAlerts.length" :max="99" class="alerts-badge">
                  <Warning class="w-5 h-5 text-orange-500" />
                </el-badge>
              </div>
              
              <div class="alerts-list">
                <div
                  v-for="alert in budgetAlerts"
                  :key="alert.id"
                  class="alert-item"
                  :class="`alert-${alert.level}`"
                >
                  <div class="alert-icon">
                    <component :is="getAlertIcon(alert.level)" class="w-4 h-4" />
                  </div>
                  
                  <div class="alert-content">
                    <h5 class="alert-title">{{ alert.title }}</h5>
                    <p class="alert-message">{{ alert.message }}</p>
                    <div class="alert-meta">
                      <span class="alert-time">{{ formatDate(alert.createdAt) }}</span>
                      <span class="alert-category">{{ alert.category }}</span>
                    </div>
                  </div>
                  
                  <div class="alert-actions">
                    <el-button @click="handleViewAlert(alert)" size="small">
                      查看详情
                    </el-button>
                    <el-button @click="handleDismissAlert(alert)" size="small" type="danger">
                      忽略
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 预算调整 -->
            <div class="budget-adjustment">
              <div class="adjustment-header">
                <h4 class="adjustment-title">预算调整申请</h4>
                <el-button @click="handleCreateAdjustment" size="small" type="primary">
                  <Plus class="w-3 h-3 mr-1" />
                  申请调整
                </el-button>
              </div>
              
              <div class="adjustment-list">
                <el-table :data="budgetAdjustments" stripe>
                  <el-table-column prop="category" label="预算类别" width="120" />
                  <el-table-column prop="currentAmount" label="当前预算" width="120">
                    <template #default="{ row }">
                      ¥{{ formatCurrency(row.currentAmount) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="requestedAmount" label="申请金额" width="120">
                    <template #default="{ row }">
                      ¥{{ formatCurrency(row.requestedAmount) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="reason" label="调整原因" min-width="200" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getAdjustmentStatusColor(row.status)" size="small">
                        {{ getAdjustmentStatusLabel(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="submittedAt" label="申请时间" width="120">
                    <template #default="{ row }">
                      {{ formatDate(row.submittedAt) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button @click="handleViewAdjustment(row)" size="small">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 优化建议 -->
        <el-tab-pane label="优化建议" name="optimization">
          <div class="optimization-panel">
            <!-- 成本优化机会 -->
            <div class="optimization-opportunities">
              <div class="opportunities-header">
                <h3 class="text-lg font-semibold text-gray-800">成本优化机会</h3>
                <div class="opportunities-stats">
                  <span class="stats-item">
                    潜在节省: ¥{{ formatCurrency(optimizationStats.potentialSavings) }}
                  </span>
                  <span class="stats-item">
                    {{ optimizationOpportunities.length }} 个优化点
                  </span>
                </div>
              </div>
              
              <div class="opportunities-list">
                <div
                  v-for="opportunity in optimizationOpportunities"
                  :key="opportunity.id"
                  class="opportunity-item"
                >
                  <div class="opportunity-header">
                    <div class="opportunity-priority" :class="`priority-${opportunity.priority}`">
                      {{ getPriorityLabel(opportunity.priority) }}
                    </div>
                    <h4 class="opportunity-title">{{ opportunity.title }}</h4>
                    <div class="opportunity-savings">
                      预计节省: ¥{{ formatCurrency(opportunity.savings) }}
                    </div>
                  </div>
                  
                  <p class="opportunity-description">{{ opportunity.description }}</p>
                  
                  <div class="opportunity-details">
                    <div class="detail-item">
                      <span class="detail-label">影响范围:</span>
                      <span class="detail-value">{{ opportunity.scope }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">实施难度:</span>
                      <span class="detail-value">{{ getDifficultyLabel(opportunity.difficulty) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">预计周期:</span>
                      <span class="detail-value">{{ opportunity.timeline }}</span>
                    </div>
                  </div>
                  
                  <div class="opportunity-actions">
                    <el-button @click="handleViewOpportunity(opportunity)" size="small">
                      查看详情
                    </el-button>
                    <el-button @click="handleImplementOpportunity(opportunity)" size="small" type="primary">
                      开始实施
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 实施进度 -->
            <div class="implementation-progress">
              <div class="progress-header">
                <h4 class="progress-title">优化实施进度</h4>
              </div>
              
              <div class="progress-list">
                <div
                  v-for="implementation in implementations"
                  :key="implementation.id"
                  class="implementation-item"
                >
                  <div class="implementation-info">
                    <h5 class="implementation-title">{{ implementation.title }}</h5>
                    <div class="implementation-meta">
                      <span class="meta-item">开始时间: {{ formatDate(implementation.startDate) }}</span>
                      <span class="meta-item">负责人: {{ implementation.responsible }}</span>
                    </div>
                  </div>
                  
                  <div class="implementation-progress-bar">
                    <div class="progress-info">
                      <span class="progress-label">完成进度</span>
                      <span class="progress-percentage">{{ implementation.progress }}%</span>
                    </div>
                    <el-progress
                      :percentage="implementation.progress"
                      :color="getProgressColor(implementation.progress)"
                      :stroke-width="6"
                    />
                  </div>
                  
                  <div class="implementation-status">
                    <el-tag :type="getImplementationStatusColor(implementation.status)" size="small">
                      {{ getImplementationStatusLabel(implementation.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 预算调整对话框 -->
    <el-dialog
      v-model="adjustmentDialog.visible"
      title="预算调整申请"
      width="600px"
    >
      <div class="adjustment-form">
        <el-form :model="adjustmentForm" :rules="adjustmentRules" ref="adjustmentFormRef" label-width="120px">
          <el-form-item label="预算类别" prop="category">
            <el-select v-model="adjustmentForm.category" placeholder="选择预算类别">
              <el-option label="人工成本" value="labor" />
              <el-option label="材料成本" value="material" />
              <el-option label="设备成本" value="equipment" />
              <el-option label="其他费用" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="当前预算">
            <el-input
              :value="getCurrentBudget(adjustmentForm.category)"
              disabled
              placeholder="当前预算金额"
            />
          </el-form-item>
          
          <el-form-item label="申请金额" prop="requestedAmount">
            <el-input-number
              v-model="adjustmentForm.requestedAmount"
              :min="0"
              :step="1000"
              placeholder="申请调整金额"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="调整类型" prop="type">
            <el-radio-group v-model="adjustmentForm.type">
              <el-radio label="increase">增加预算</el-radio>
              <el-radio label="decrease">减少预算</el-radio>
              <el-radio label="transfer">预算转移</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="调整原因" prop="reason">
            <el-input
              v-model="adjustmentForm.reason"
              type="textarea"
              :rows="4"
              placeholder="详细说明预算调整的原因和必要性"
            />
          </el-form-item>
          
          <el-form-item label="支撑材料">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx"
            >
              <el-button size="small">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持PDF、Word、Excel文件，用于支撑预算调整申请
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="adjustmentDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitAdjustment" type="primary" :loading="adjustmentDialog.loading">
          提交申请
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Calculator, Download, Money, User, Package, TrendingUp,
  PieChart, BarChart, Warning, Plus
} from '@element-plus/icons-vue'

// 响应式数据
const activeCostTab = ref('statistics')
const dateRange = ref([])
const selectedPeriod = ref('month')
const rankingType = ref('total')
const trendChartType = ref('line')
const forecastPeriod = ref('next_month')
const budgetPeriod = ref('current_month')

// 统计数据
const costStats = ref({
  totalCost: 2856000,
  costChange: -3.2,
  laborCost: 1285000,
  laborHours: 2850,
  materialCost: 1571000,
  materialItems: 156,
  efficiency: 92.8,
  efficiencyChange: 4.5
})

// 时间周期选项
const timePeriods = ref([
  { key: 'week', label: '本周' },
  { key: 'month', label: '本月' },
  { key: 'quarter', label: '本季度' },
  { key: 'year', label: '本年度' }
])

// 成本构成数据
const costBreakdown = ref([
  {
    category: 'labor',
    name: '人工成本',
    amount: 1285000,
    percentage: 45,
    change: -2.1,
    color: '#3b82f6'
  },
  {
    category: 'material',
    name: '材料成本',
    amount: 1571000,
    percentage: 55,
    change: -4.2,
    color: '#10b981'
  },
  {
    category: 'equipment',
    name: '设备成本',
    amount: 285600,
    percentage: 10,
    change: 1.8,
    color: '#f59e0b'
  },
  {
    category: 'other',
    name: '其他费用',
    amount: 142800,
    percentage: 5,
    change: -1.5,
    color: '#ef4444'
  }
])

// 项目成本排行
const costRanking = ref([
  {
    id: 1,
    name: 'CFM56发动机大修项目',
    orderNo: 'WO-2024-001',
    totalCost: 856000,
    budget: 800000,
    status: 'over_budget'
  },
  {
    id: 2,
    name: 'A320起落架系统维修',
    orderNo: 'WO-2024-003',
    totalCost: 456000,
    budget: 500000,
    status: 'under_budget'
  },
  {
    id: 3,
    name: 'APU辅助动力装置检修',
    orderNo: 'WO-2024-002',
    totalCost: 285000,
    budget: 300000,
    status: 'on_budget'
  }
])

// 趋势指标
const trendMetrics = ref([
  {
    key: 'total_cost',
    name: '总成本',
    value: '¥285.6万',
    trend: -3.2,
    color: '#3b82f6',
    icon: 'Money'
  },
  {
    key: 'labor_cost',
    name: '人工成本',
    value: '¥128.5万',
    trend: -2.1,
    color: '#10b981',
    icon: 'Users'
  },
  {
    key: 'material_cost',
    name: '材料成本',
    value: '¥157.1万',
    trend: -4.2,
    color: '#f59e0b',
    icon: 'Package'
  },
  {
    key: 'efficiency',
    name: '成本效率',
    value: '92.8%',
    trend: 4.5,
    color: '#8b5cf6',
    icon: 'TrendingUp'
  }
])

// 预测数据
const forecastData = ref({
  totalCost: 2750000,
  change: -3.7,
  confidence: 85
})

// 预算项目
const budgetItems = ref([
  {
    category: 'labor',
    name: '人工成本',
    total: 1500000,
    used: 1285000,
    remaining: 215000,
    usedRatio: 86
  },
  {
    category: 'material',
    name: '材料成本',
    total: 1800000,
    used: 1571000,
    remaining: 229000,
    usedRatio: 87
  },
  {
    category: 'equipment',
    name: '设备成本',
    total: 400000,
    used: 285600,
    remaining: 114400,
    usedRatio: 71
  },
  {
    category: 'other',
    name: '其他费用',
    total: 200000,
    used: 142800,
    remaining: 57200,
    usedRatio: 71
  }
])

// 预算预警
const budgetAlerts = ref([
  {
    id: 1,
    level: 'warning',
    title: '材料成本预算即将超支',
    message: '当前材料成本已使用87%预算，预计本月底将超出预算5%',
    category: '材料成本',
    createdAt: '2025-07-23 14:30:00'
  },
  {
    id: 2,
    level: 'danger',
    title: 'CFM56项目预算已超支',
    message: 'CFM56发动机大修项目实际成本已超出预算7%，需要申请预算调整',
    category: 'CFM56项目',
    createdAt: '2025-07-23 09:15:00'
  }
])

// 预算调整申请
const budgetAdjustments = ref([
  {
    id: 1,
    category: '材料成本',
    currentAmount: 1800000,
    requestedAmount: 200000,
    reason: '由于关键航材价格上涨，需要增加材料预算',
    status: 'pending',
    submittedAt: '2025-07-23 10:00:00'
  },
  {
    id: 2,
    category: '人工成本',
    currentAmount: 1500000,
    requestedAmount: -50000,
    reason: '优化工作流程，减少人工成本预算',
    status: 'approved',
    submittedAt: '2025-07-22 15:30:00'
  }
])

// 优化统计
const optimizationStats = ref({
  potentialSavings: 425000
})

// 优化机会
const optimizationOpportunities = ref([
  {
    id: 1,
    priority: 'high',
    title: '优化材料采购流程',
    description: '通过集中采购和供应商谈判，预计可降低材料成本15-20%',
    savings: 235000,
    scope: '所有材料采购',
    difficulty: 'medium',
    timeline: '3-6个月'
  },
  {
    id: 2,
    priority: 'medium',
    title: '改进维修工艺流程',
    description: '通过标准化作业流程和工具优化，提升维修效率',
    savings: 128000,
    scope: '主要维修项目',
    difficulty: 'low',
    timeline: '2-3个月'
  },
  {
    id: 3,
    priority: 'low',
    title: '引入预测性维护',
    description: '使用数据分析预测设备故障，减少计划外维修成本',
    savings: 62000,
    scope: '关键设备',
    difficulty: 'high',
    timeline: '6-12个月'
  }
])

// 实施进度
const implementations = ref([
  {
    id: 1,
    title: '材料采购流程优化',
    startDate: '2025-07-01',
    responsible: '采购部门',
    progress: 65,
    status: 'in_progress'
  },
  {
    id: 2,
    title: '维修标准化作业',
    startDate: '2025-06-15',
    responsible: '维修部门',
    progress: 85,
    status: 'nearly_complete'
  }
])

// 对话框状态
const adjustmentDialog = ref({
  visible: false,
  loading: false
})

const adjustmentForm = ref({
  category: '',
  requestedAmount: 0,
  type: 'increase',
  reason: ''
})

const adjustmentRules = {
  category: [{ required: true, message: '请选择预算类别', trigger: 'change' }],
  requestedAmount: [{ required: true, message: '请输入申请金额', trigger: 'blur' }],
  type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  reason: [{ required: true, message: '请填写调整原因', trigger: 'blur' }]
}

const adjustmentFormRef = ref()

// 方法
const formatCurrency = (value) => {
  if (!value) return '0'
  return new Intl.NumberFormat('zh-CN').format(value)
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getTotalCost = () => {
  return costBreakdown.value.reduce((sum, item) => sum + item.amount, 0)
}

const getCostStatusLabel = (status) => {
  const labels = {
    under_budget: '预算内',
    on_budget: '接近预算',
    over_budget: '超预算'
  }
  return labels[status] || status
}

const getCostStatusColor = (status) => {
  const colors = {
    under_budget: 'success',
    on_budget: 'warning',
    over_budget: 'danger'
  }
  return colors[status] || ''
}

const getBudgetColor = (ratio) => {
  if (ratio < 70) return '#67c23a'
  if (ratio < 90) return '#e6a23c'
  return '#f56c6c'
}

const getAlertIcon = (level) => {
  const icons = {
    info: 'AlertCircle',
    warning: 'Warning',
    danger: 'AlertOctagon'
  }
  return icons[level] || 'Warning'
}

const getAdjustmentStatusLabel = (status) => {
  const labels = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

const getAdjustmentStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return colors[status] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return labels[priority] || priority
}

const getDifficultyLabel = (difficulty) => {
  const labels = {
    low: '容易',
    medium: '中等',
    high: '困难'
  }
  return labels[difficulty] || difficulty
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getImplementationStatusLabel = (status) => {
  const labels = {
    planning: '规划中',
    in_progress: '进行中',
    nearly_complete: '接近完成',
    completed: '已完成',
    paused: '已暂停'
  }
  return labels[status] || status
}

const getImplementationStatusColor = (status) => {
  const colors = {
    planning: 'info',
    in_progress: 'primary',
    nearly_complete: 'warning',
    completed: 'success',
    paused: 'danger'
  }
  return colors[status] || ''
}

const getCurrentBudget = (category) => {
  const budget = budgetItems.value.find(b => b.category === category)
  return budget ? `¥${formatCurrency(budget.total)}` : ''
}

// 事件处理
const handleRefreshCosts = () => {
  ElMessage.success('成本数据已刷新')
}

const handleBudgetManagement = () => {
  activeCostTab.value = 'budget'
}

const handleExportReport = () => {
  ElMessage.info('正在导出成本分析报告...')
}

const handleTabChange = (tabName) => {
  console.log('切换标签:', tabName)
}

const handleDateRangeChange = () => {
  ElMessage.info('更新时间范围')
}

const selectTimePeriod = (period) => {
  selectedPeriod.value = period
  ElMessage.info(`切换到${timePeriods.value.find(p => p.key === period)?.label}`)
}

const handleViewProjectCost = (project) => {
  ElMessage.info(`查看项目成本详情: ${project.name}`)
}

const handleViewAlert = (alert) => {
  ElMessage.info(`查看预警详情: ${alert.title}`)
}

const handleDismissAlert = (alert) => {
  const index = budgetAlerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    budgetAlerts.value.splice(index, 1)
    ElMessage.success('预警已忽略')
  }
}

const handleCreateAdjustment = () => {
  adjustmentDialog.value.visible = true
}

const handleViewAdjustment = (adjustment) => {
  ElMessage.info(`查看调整申请: ${adjustment.category}`)
}

const handleViewOpportunity = (opportunity) => {
  ElMessage.info(`查看优化机会详情: ${opportunity.title}`)
}

const handleImplementOpportunity = (opportunity) => {
  ElMessage.success(`开始实施优化方案: ${opportunity.title}`)
}

const handleSubmitAdjustment = async () => {
  try {
    await adjustmentFormRef.value.validate()
    adjustmentDialog.value.loading = true
    
    setTimeout(() => {
      ElMessage.success('预算调整申请已提交')
      adjustmentDialog.value.visible = false
      adjustmentDialog.value.loading = false
      
      // 重置表单
      adjustmentForm.value = {
        category: '',
        requestedAmount: 0,
        type: 'increase',
        reason: ''
      }
    }, 1000)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.cost-analysis {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.cost-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-trend {
  @apply text-xs;
}

.card-trend.positive {
  @apply text-green-600;
}

.card-trend.negative {
  @apply text-red-600;
}

.card-detail {
  @apply text-xs text-gray-500;
}

.main-content {
  @apply modern-card;
}

.statistics-panel, .trends-panel, .budget-panel, .optimization-panel {
  @apply p-6 space-y-8;
}

.time-filter {
  @apply modern-card p-4;
}

.filter-header {
  @apply flex items-center justify-between;
}

.filter-controls {
  @apply flex items-center space-x-4;
}

.cost-breakdown {
  @apply modern-card p-6;
}

.breakdown-header {
  @apply flex items-center justify-between mb-6;
}

.breakdown-title {
  @apply text-lg font-semibold text-gray-800;
}

.breakdown-total {
  @apply text-lg font-bold text-primary-600;
}

.breakdown-content {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8;
}

.chart-placeholder {
  @apply flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg;
}

.breakdown-details {
  @apply space-y-4;
}

.breakdown-item {
  @apply space-y-3;
}

.item-header {
  @apply flex items-center space-x-3;
}

.item-color {
  @apply w-4 h-4 rounded;
}

.item-name {
  @apply flex-1 font-medium text-gray-900;
}

.item-percentage {
  @apply text-sm text-gray-600;
}

.item-details {
  @apply flex items-center justify-between text-sm;
}

.item-amount {
  @apply font-medium text-gray-900;
}

.item-change {
  @apply text-xs;
}

.item-change.positive {
  @apply text-green-600;
}

.item-change.negative {
  @apply text-red-600;
}

.item-progress {
  @apply mt-2;
}

.cost-ranking {
  @apply modern-card p-6;
}

.ranking-header {
  @apply flex items-center justify-between mb-6;
}

.ranking-title {
  @apply text-lg font-semibold text-gray-800;
}

.ranking-list {
  @apply space-y-4;
}

.ranking-item {
  @apply flex items-center space-x-4 p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md;
}

.ranking-position {
  @apply flex-shrink-0;
}

.position-number {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm;
}

.position-number.rank-1 {
  @apply bg-yellow-500;
}

.position-number.rank-2 {
  @apply bg-gray-400;
}

.position-number.rank-3 {
  @apply bg-orange-500;
}

.project-info {
  @apply flex-1;
}

.project-name {
  @apply font-semibold text-gray-900 mb-1;
}

.project-order {
  @apply text-sm text-gray-600;
}

.cost-info {
  @apply text-right;
}

.cost-amount {
  @apply font-bold text-gray-900;
}

.cost-budget {
  @apply text-sm text-gray-600;
}

.cost-status {
  @apply flex-shrink-0;
}

.trend-charts {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.chart-section {
  @apply lg:col-span-2 modern-card p-6;
}

.chart-header {
  @apply flex items-center justify-between mb-6;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800;
}

.chart-controls {
  @apply flex items-center space-x-2;
}

.chart-container {
  @apply h-80 flex items-center justify-center bg-gray-50 rounded-lg;
}

.trend-metrics {
  @apply modern-card p-6;
}

.metrics-header {
  @apply mb-6;
}

.metrics-title {
  @apply text-lg font-semibold text-gray-800;
}

.metrics-list {
  @apply space-y-4;
}

.metric-item {
  @apply flex items-center space-x-3;
}

.metric-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
}

.metric-content {
  @apply flex-1;
}

.metric-name {
  @apply text-sm text-gray-600;
}

.metric-value {
  @apply font-semibold text-gray-900;
}

.metric-trend {
  @apply text-xs;
}

.metric-trend.positive {
  @apply text-green-600;
}

.metric-trend.negative {
  @apply text-red-600;
}

.forecast-analysis {
  @apply modern-card p-6;
}

.forecast-header {
  @apply flex items-center justify-between mb-6;
}

.forecast-title {
  @apply text-lg font-semibold text-gray-800;
}

.forecast-controls {
  @apply flex items-center space-x-2;
}

.forecast-content {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.forecast-chart {
  @apply h-48 flex items-center justify-center bg-gray-50 rounded-lg;
}

.forecast-summary {
  @apply space-y-4;
}

.summary-item {
  @apply flex items-center justify-between p-3 bg-gray-50 rounded-lg;
}

.summary-label {
  @apply text-sm text-gray-600;
}

.summary-value {
  @apply font-semibold text-gray-900;
}

.summary-value.positive {
  @apply text-green-600;
}

.summary-value.negative {
  @apply text-red-600;
}

.budget-overview {
  @apply modern-card p-6;
}

.overview-header {
  @apply flex items-center justify-between mb-6;
}

.overview-period {
  @apply flex items-center space-x-2;
}

.period-label {
  @apply text-sm text-gray-600;
}

.budget-progress {
  @apply space-y-6;
}

.budget-item {
  @apply space-y-3;
}

.budget-header {
  @apply flex items-center justify-between;
}

.budget-name {
  @apply font-medium text-gray-900;
}

.budget-ratio {
  @apply text-sm font-semibold text-gray-700;
}

.budget-bar {
  @apply w-full;
}

.budget-details {
  @apply flex items-center justify-between text-sm text-gray-600;
}

.budget-alerts {
  @apply modern-card p-6;
}

.alerts-header {
  @apply flex items-center justify-between mb-6;
}

.alerts-title {
  @apply text-lg font-semibold text-gray-800;
}

.alerts-badge {
  @apply flex items-center;
}

.alerts-list {
  @apply space-y-4;
}

.alert-item {
  @apply flex items-start space-x-4 p-4 rounded-lg border-l-4;
}

.alert-item.alert-warning {
  @apply border-orange-500 bg-orange-50;
}

.alert-item.alert-danger {
  @apply border-red-500 bg-red-50;
}

.alert-icon {
  @apply flex-shrink-0 mt-1;
}

.alert-content {
  @apply flex-1;
}

.alert-title {
  @apply font-semibold text-gray-900 mb-2;
}

.alert-message {
  @apply text-sm text-gray-700 mb-2;
}

.alert-meta {
  @apply flex items-center space-x-3 text-xs text-gray-500;
}

.alert-actions {
  @apply flex items-center space-x-2;
}

.budget-adjustment {
  @apply modern-card p-6;
}

.adjustment-header {
  @apply flex items-center justify-between mb-6;
}

.adjustment-title {
  @apply text-lg font-semibold text-gray-800;
}

.adjustment-list {
  @apply space-y-4;
}

.optimization-opportunities {
  @apply modern-card p-6;
}

.opportunities-header {
  @apply flex items-center justify-between mb-6;
}

.opportunities-stats {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.stats-item {
  @apply font-medium;
}

.opportunities-list {
  @apply space-y-6;
}

.opportunity-item {
  @apply p-6 border border-gray-200 rounded-lg;
}

.opportunity-header {
  @apply flex items-center justify-between mb-4;
}

.opportunity-priority {
  @apply px-2 py-1 rounded text-xs font-medium;
}

.opportunity-priority.priority-high {
  @apply bg-red-100 text-red-800;
}

.opportunity-priority.priority-medium {
  @apply bg-yellow-100 text-yellow-800;
}

.opportunity-priority.priority-low {
  @apply bg-green-100 text-green-800;
}

.opportunity-title {
  @apply flex-1 text-lg font-semibold text-gray-900 mx-4;
}

.opportunity-savings {
  @apply text-lg font-bold text-primary-600;
}

.opportunity-description {
  @apply text-gray-700 mb-4;
}

.opportunity-details {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4 mb-4;
}

.detail-item {
  @apply flex items-center space-x-2 text-sm;
}

.detail-label {
  @apply text-gray-600;
}

.detail-value {
  @apply font-medium text-gray-900;
}

.opportunity-actions {
  @apply flex items-center space-x-2;
}

.implementation-progress {
  @apply modern-card p-6;
}

.progress-header {
  @apply mb-6;
}

.progress-title {
  @apply text-lg font-semibold text-gray-800;
}

.progress-list {
  @apply space-y-4;
}

.implementation-item {
  @apply flex items-center space-x-4 p-4 border border-gray-200 rounded-lg;
}

.implementation-info {
  @apply flex-1;
}

.implementation-title {
  @apply font-semibold text-gray-900 mb-2;
}

.implementation-meta {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.meta-item {
  @apply text-xs;
}

.implementation-progress-bar {
  @apply flex-1 space-y-2;
}

.progress-info {
  @apply flex items-center justify-between;
}

.progress-label {
  @apply text-sm text-gray-600;
}

.progress-percentage {
  @apply text-sm font-medium text-gray-900;
}

.implementation-status {
  @apply flex-shrink-0;
}

.adjustment-form {
  @apply space-y-4;
}
</style>