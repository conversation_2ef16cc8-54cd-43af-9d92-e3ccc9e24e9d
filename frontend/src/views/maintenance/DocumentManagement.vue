<!--
技术文档管理和检索系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的技术文档管理和智能检索界面
特点：
1. 智能文档搜索和分类
2. 文档版本管理和更新通知
3. 个人文档收藏和标签管理
4. 文档协作和批注功能
5. 移动端适配和离线下载
-->

<template>
  <div class="document-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">技术文档中心</h1>
          <p class="page-subtitle">智能检索技术文档，管理维修资料，提升工作效率和专业能力</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshDocs">
            <Refresh class="w-4 h-4 mr-2" />
            刷新文档
          </el-button>
          <el-button @click="handleUploadDoc">
            <Upload class="w-4 h-4 mr-2" />
            上传文档
          </el-button>
          <el-button @click="handleOfflineSync" type="primary">
            <Download class="w-4 h-4 mr-2" />
            离线同步
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文档概览统计 -->
    <div class="docs-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card total">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Document class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ docStats.totalDocs }}</div>
            <div class="card-label">总文档数</div>
            <div class="card-detail">本月新增: {{ docStats.newDocsThisMonth }}</div>
          </div>
        </div>
        
        <div class="overview-card favorites">
          <div class="card-icon bg-red-100 text-red-600">
            <Heart class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ docStats.myFavorites }}</div>
            <div class="card-label">我的收藏</div>
            <div class="card-detail">分类管理</div>
          </div>
        </div>
        
        <div class="overview-card downloads">
          <div class="card-icon bg-green-100 text-green-600">
            <Download class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ docStats.offlineDocs }}</div>
            <div class="card-label">离线文档</div>
            <div class="card-detail">已同步本地</div>
          </div>
        </div>
        
        <div class="overview-card recent">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Clock class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ docStats.recentAccess }}</div>
            <div class="card-label">最近访问</div>
            <div class="card-detail">7天内</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <div class="search-bar">
          <el-input
            v-model="searchQuery"
            placeholder="搜索技术文档、维修手册、故障代码、操作程序..."
            @keyup.enter="handleDocSearch"
            size="large"
            class="search-input"
          >
            <template #prefix>
              <Search class="w-5 h-5 text-gray-400" />
            </template>
            <template #append>
              <el-button @click="handleDocSearch" type="primary">
                智能搜索
              </el-button>
            </template>
          </el-input>
        </div>
        
        <div class="search-suggestions" v-if="searchSuggestions.length > 0 && !searchExecuted">
          <div class="suggestions-header">
            <span class="suggestions-title">搜索建议</span>
          </div>
          <div class="suggestions-list">
            <div
              v-for="suggestion in searchSuggestions"
              :key="suggestion.id"
              class="suggestion-item"
              @click="handleSelectSuggestion(suggestion)"
            >
              <component :is="suggestion.icon" class="w-4 h-4 text-gray-500" />
              <span class="suggestion-text">{{ suggestion.text }}</span>
              <span class="suggestion-type">{{ suggestion.type }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-layout">
        <!-- 左侧分类导航 -->
        <div class="sidebar">
          <div class="sidebar-section">
            <h3 class="section-title">文档分类</h3>
            <div class="category-tree">
              <div
                v-for="category in documentCategories"
                :key="category.id"
                class="category-item"
                :class="{ active: selectedCategory === category.id }"
                @click="handleSelectCategory(category.id)"
              >
                <div class="category-header">
                  <component :is="category.icon" class="w-4 h-4" />
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-count">({{ category.count }})</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="sidebar-section">
            <h3 class="section-title">我的标签</h3>
            <div class="tags-list">
              <el-tag
                v-for="tag in myTags"
                :key="tag.id"
                :type="tag.color"
                size="small"
                class="tag-item"
                @click="handleSelectTag(tag)"
              >
                {{ tag.name }} ({{ tag.count }})
              </el-tag>
              <el-button
                @click="handleCreateTag"
                size="small"
                type="primary"
                plain
                class="create-tag-btn"
              >
                <Plus class="w-3 h-3 mr-1" />
                新建标签
              </el-button>
            </div>
          </div>
          
          <div class="sidebar-section">
            <h3 class="section-title">快速访问</h3>
            <div class="quick-access">
              <div class="access-item" @click="handleShowRecent">
                <Clock class="w-4 h-4" />
                <span>最近访问</span>
              </div>
              <div class="access-item" @click="handleShowFavorites">
                <Heart class="w-4 h-4" />
                <span>我的收藏</span>
              </div>
              <div class="access-item" @click="handleShowOffline">
                <Download class="w-4 h-4" />
                <span>离线文档</span>
              </div>
              <div class="access-item" @click="handleShowShared">
                <Share class="w-4 h-4" />
                <span>共享文档</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧文档列表 -->
        <div class="content-area">
          <!-- 筛选和排序 -->
          <div class="content-header">
            <div class="header-left">
              <h3 class="content-title">{{ getContentTitle() }}</h3>
              <span class="content-count">共 {{ filteredDocuments.length }} 个文档</span>
            </div>
            
            <div class="header-actions">
              <el-select v-model="sortBy" size="small" placeholder="排序方式" style="width: 140px">
                <el-option label="最新更新" value="updated" />
                <el-option label="创建时间" value="created" />
                <el-option label="文档名称" value="name" />
                <el-option label="访问次数" value="views" />
                <el-option label="文件大小" value="size" />
              </el-select>
              
              <el-button-group>
                <el-button
                  :type="viewMode === 'list' ? 'primary' : ''"
                  @click="viewMode = 'list'"
                  size="small"
                >
                  <List class="w-3 h-3" />
                </el-button>
                <el-button
                  :type="viewMode === 'grid' ? 'primary' : ''"
                  @click="viewMode = 'grid'"
                  size="small"
                >
                  <Grid class="w-3 h-3" />
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <!-- 文档列表视图 -->
          <div v-if="viewMode === 'list'" class="documents-list">
            <div
              v-for="doc in filteredDocuments"
              :key="doc.id"
              class="document-item"
              @click="handleViewDocument(doc)"
            >
              <div class="doc-icon">
                <component :is="getDocumentIcon(doc.type)" class="w-8 h-8" :class="getDocumentIconColor(doc.type)" />
              </div>
              
              <div class="doc-info">
                <div class="doc-header">
                  <h4 class="doc-title">{{ doc.title }}</h4>
                  <div class="doc-badges">
                    <el-tag v-if="doc.isNew" type="success" size="small">新</el-tag>
                    <el-tag v-if="doc.isUpdated" type="warning" size="small">更新</el-tag>
                    <el-tag v-if="doc.isOffline" type="info" size="small">离线</el-tag>
                  </div>
                </div>
                
                <p class="doc-description">{{ doc.description }}</p>
                
                <div class="doc-meta">
                  <span class="meta-item">
                    <Folder class="w-3 h-3" />
                    {{ doc.category }}
                  </span>
                  <span class="meta-item">
                    <Calendar class="w-3 h-3" />
                    {{ formatDate(doc.updatedAt) }}
                  </span>
                  <span class="meta-item">
                    <Document class="w-3 h-3" />
                    {{ formatFileSize(doc.size) }}
                  </span>
                  <span class="meta-item">
                    <Eye class="w-3 h-3" />
                    {{ doc.views }} 次
                  </span>
                </div>
                
                <div class="doc-tags" v-if="doc.tags.length > 0">
                  <el-tag
                    v-for="tag in doc.tags"
                    :key="tag"
                    size="small"
                    class="doc-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
              
              <div class="doc-actions">
                <el-button
                  @click.stop="handleToggleFavorite(doc)"
                  :type="doc.isFavorite ? 'danger' : ''"
                  size="small"
                  circle
                >
                  <Heart class="w-3 h-3" :class="{ 'text-red-500': doc.isFavorite }" />
                </el-button>
                
                <el-button
                  @click.stop="handleDownloadDoc(doc)"
                  size="small"
                  circle
                >
                  <Download class="w-3 h-3" />
                </el-button>
                
                <el-dropdown @command="handleDocAction">
                  <el-button size="small" circle>
                    <MoreVertical class="w-3 h-3" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'view', doc}">
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'share', doc}">
                        分享文档
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'annotate', doc}">
                        添加批注
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'version', doc}">
                        版本历史
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'offline', doc}">
                        {{ doc.isOffline ? '移除离线' : '添加离线' }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
          
          <!-- 文档网格视图 -->
          <div v-else class="documents-grid">
            <div
              v-for="doc in filteredDocuments"
              :key="doc.id"
              class="document-card"
              @click="handleViewDocument(doc)"
            >
              <div class="card-header">
                <div class="doc-icon-large">
                  <component :is="getDocumentIcon(doc.type)" class="w-12 h-12" :class="getDocumentIconColor(doc.type)" />
                </div>
                <div class="card-badges">
                  <el-tag v-if="doc.isNew" type="success" size="small">新</el-tag>
                  <el-tag v-if="doc.isUpdated" type="warning" size="small">更新</el-tag>
                </div>
              </div>
              
              <div class="card-content">
                <h4 class="card-title">{{ doc.title }}</h4>
                <p class="card-description">{{ doc.description.substring(0, 80) }}...</p>
                
                <div class="card-meta">
                  <span class="meta-text">{{ formatDate(doc.updatedAt) }}</span>
                  <span class="meta-text">{{ formatFileSize(doc.size) }}</span>
                </div>
              </div>
              
              <div class="card-footer">
                <div class="card-tags">
                  <el-tag
                    v-for="tag in doc.tags.slice(0, 2)"
                    :key="tag"
                    size="small"
                    class="card-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                
                <div class="card-actions">
                  <el-button
                    @click.stop="handleToggleFavorite(doc)"
                    :type="doc.isFavorite ? 'danger' : ''"
                    size="small"
                    circle
                  >
                    <Heart class="w-3 h-3" />
                  </el-button>
                  <el-button
                    @click.stop="handleDownloadDoc(doc)"
                    size="small"
                    circle
                  >
                    <Download class="w-3 h-3" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="pagination-container" v-if="totalPages > 1">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="totalDocuments"
              :page-sizes="[12, 24, 48, 96]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 文档查看对话框 -->
    <el-dialog
      v-model="documentDialog.visible"
      :title="documentDialog.document?.title"
      width="90%"
      top="5vh"
      class="document-viewer-dialog"
    >
      <div class="document-viewer" v-if="documentDialog.document">
        <div class="viewer-header">
          <div class="doc-info-detailed">
            <div class="doc-title-large">{{ documentDialog.document.title }}</div>
            <div class="doc-meta-detailed">
              <span class="meta-item">
                分类: {{ documentDialog.document.category }}
              </span>
              <span class="meta-item">
                大小: {{ formatFileSize(documentDialog.document.size) }}
              </span>
              <span class="meta-item">
                更新: {{ formatDate(documentDialog.document.updatedAt) }}
              </span>
              <span class="meta-item">
                版本: {{ documentDialog.document.version }}
              </span>
            </div>
          </div>
          
          <div class="viewer-actions">
            <el-button @click="handleAnnotateMode" size="small">
              <Edit class="w-3 h-3 mr-1" />
              批注模式
            </el-button>
            <el-button @click="handlePrintDoc" size="small">
              <Printer class="w-3 h-3 mr-1" />
              打印
            </el-button>
            <el-button @click="handleDownloadDoc(documentDialog.document)" size="small" type="primary">
              <Download class="w-3 h-3 mr-1" />
              下载
            </el-button>
          </div>
        </div>
        
        <div class="viewer-content">
          <div class="content-placeholder">
            <Document class="w-24 h-24 text-gray-400" />
            <h3 class="text-lg font-medium text-gray-600 mb-2">文档内容预览</h3>
            <p class="text-gray-500 mb-4">{{ documentDialog.document.description }}</p>
            <el-button @click="handleOpenInNewTab" type="primary">
              在新标签页中打开
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 上传文档对话框 -->
    <el-dialog
      v-model="uploadDialog.visible"
      title="上传技术文档"
      width="600px"
    >
      <div class="upload-form">
        <el-form :model="uploadForm" label-width="100px">
          <el-form-item label="文档标题">
            <el-input v-model="uploadForm.title" placeholder="请输入文档标题" />
          </el-form-item>
          
          <el-form-item label="文档分类">
            <el-select v-model="uploadForm.category" placeholder="选择分类">
              <el-option
                v-for="category in documentCategories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="文档描述">
            <el-input
              v-model="uploadForm.description"
              type="textarea"
              :rows="3"
              placeholder="请描述文档内容和用途"
            />
          </el-form-item>
          
          <el-form-item label="标签">
            <el-select
              v-model="uploadForm.tags"
              multiple
              filterable
              allow-create
              placeholder="选择或创建标签"
            >
              <el-option
                v-for="tag in allTags"
                :key="tag.name"
                :label="tag.name"
                :value="tag.name"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="文件上传">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :multiple="true"
              drag
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
            >
              <Upload class="w-16 h-16 text-gray-400" />
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持 PDF、Word、Excel、PowerPoint、文本文件，单个文件不超过50MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="uploadDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitUpload" type="primary" :loading="uploadDialog.loading">
          上传文档
        </el-button>
      </template>
    </el-dialog>

    <!-- 创建标签对话框 -->
    <el-dialog
      v-model="tagDialog.visible"
      title="创建新标签"
      width="400px"
    >
      <div class="tag-form">
        <el-form :model="tagForm" label-width="80px">
          <el-form-item label="标签名称">
            <el-input v-model="tagForm.name" placeholder="输入标签名称" />
          </el-form-item>
          
          <el-form-item label="标签颜色">
            <el-select v-model="tagForm.color" placeholder="选择颜色">
              <el-option label="默认" value="" />
              <el-option label="成功" value="success" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warning" />
              <el-option label="危险" value="danger" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="tagDialog.visible = false">取消</el-button>
        <el-button @click="handleCreateTagSubmit" type="primary">
          创建标签
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Upload, Download, Document, Heart, Clock, Search, Plus,
  List, Grid, Folder, Calendar, Eye, MoreVertical, Share, Edit,
  Printer
} from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const searchExecuted = ref(false)
const selectedCategory = ref('all')
const viewMode = ref('list')
const sortBy = ref('updated')
const currentPage = ref(1)
const pageSize = ref(24)

// 统计数据
const docStats = ref({
  totalDocs: 2847,
  newDocsThisMonth: 23,
  myFavorites: 156,
  offlineDocs: 45,
  recentAccess: 12
})

// 搜索建议
const searchSuggestions = ref([
  { id: 1, text: 'CFM56发动机维修手册', type: '手册', icon: 'Document' },
  { id: 2, text: 'A320起落架系统', type: '系统', icon: 'Search' },
  { id: 3, text: '液压系统故障代码', type: '故障', icon: 'Warning' },
  { id: 4, text: 'APU操作程序', type: '程序', icon: 'List' }
])

// 文档分类
const documentCategories = ref([
  { id: 'all', name: '全部文档', icon: 'Document', count: 2847 },
  { id: 'manual', name: '维修手册', icon: 'BookOpen', count: 856 },
  { id: 'procedure', name: '操作程序', icon: 'List', count: 423 },
  { id: 'troubleshoot', name: '故障排除', icon: 'Search', count: 267 },
  { id: 'technical', name: '技术通告', icon: 'Document', count: 389 },
  { id: 'safety', name: '安全指南', icon: 'Shield', count: 156 },
  { id: 'training', name: '培训资料', icon: 'GraduationCap', count: 234 },
  { id: 'drawing', name: '技术图纸', icon: 'Image', count: 522 }
])

// 我的标签
const myTags = ref([
  { id: 1, name: '常用', color: 'success', count: 25 },
  { id: 2, name: '重要', color: 'danger', count: 12 },
  { id: 3, name: '发动机', color: 'primary', count: 45 },
  { id: 4, name: '液压', color: 'warning', count: 18 },
  { id: 5, name: '电气', color: 'info', count: 22 }
])

// 所有标签
const allTags = ref([
  { name: '常用' }, { name: '重要' }, { name: '发动机' }, { name: '液压' },
  { name: '电气' }, { name: '航电' }, { name: '结构' }, { name: '燃油' },
  { name: '起落架' }, { name: 'APU' }, { name: '空调' }, { name: '防火' }
])

// 文档数据
const documents = ref([
  {
    id: 1,
    title: 'CFM56-7B发动机维修手册 - 第72章 发动机',
    description: '包含CFM56-7B系列发动机的完整维修程序、拆装步骤、检查标准和故障排除指南。涵盖发动机的所有主要部件和系统的维修要求。',
    category: '维修手册',
    type: 'pdf',
    size: 25600000, // 25.6MB
    version: 'Rev.5',
    updatedAt: '2025-07-20 14:30:00',
    views: 1245,
    tags: ['发动机', 'CFM56', '维修手册', '常用'],
    isFavorite: true,
    isNew: false,
    isUpdated: true,
    isOffline: true
  },
  {
    id: 2,
    title: 'A320起落架系统操作和维修程序',
    description: '详细描述A320飞机起落架系统的操作程序、维修要求、故障排除方法和检查标准。包含主起落架和前起落架的完整技术资料。',
    category: '操作程序',
    type: 'pdf',
    size: 18200000, // 18.2MB
    version: 'Rev.3',
    updatedAt: '2025-07-19 09:15:00',
    views: 987,
    tags: ['起落架', 'A320', '液压', '操作程序'],
    isFavorite: false,
    isNew: true,
    isUpdated: false,
    isOffline: false
  },
  {
    id: 3,
    title: '液压系统故障代码诊断指南',
    description: '全面的液压系统故障代码参考手册，包含故障代码含义、可能原因、诊断步骤和修复方法。适用于多种机型的液压系统维修。',
    category: '故障排除',
    type: 'pdf',
    size: 8900000, // 8.9MB
    version: 'Rev.2',
    updatedAt: '2025-07-18 16:20:00',
    views: 756,
    tags: ['液压', '故障代码', '诊断', '重要'],
    isFavorite: true,
    isNew: false,
    isUpdated: false,
    isOffline: true
  },
  {
    id: 4,
    title: 'APU辅助动力装置技术通告 - SB 49-001',
    description: '关于APU燃油控制单元软件升级的技术通告，包含升级步骤、注意事项和验证程序。影响序列号范围内的所有APU。',
    category: '技术通告',
    type: 'pdf',
    size: 3200000, // 3.2MB
    version: 'Rev.1',
    updatedAt: '2025-07-17 11:45:00',
    views: 423,
    tags: ['APU', '技术通告', '软件升级'],
    isFavorite: false,
    isNew: true,
    isUpdated: false,
    isOffline: false
  },
  {
    id: 5,
    title: '航空器维修安全操作规程',
    description: '航空器维修作业的安全操作规程和要求，包含人身安全、设备安全、环境安全等方面的详细规定和操作标准。',
    category: '安全指南',
    type: 'pdf',
    size: 12800000, // 12.8MB
    version: 'Rev.4',
    updatedAt: '2025-07-16 13:30:00',
    views: 1876,
    tags: ['安全', '操作规程', '重要', '常用'],
    isFavorite: true,
    isNew: false,
    isUpdated: false,
    isOffline: true
  },
  {
    id: 6,
    title: 'V2500发动机叶片检查培训资料',
    description: '针对V2500发动机叶片检查的专业培训资料，包含检查方法、标准、工具使用和缺陷判断标准。适用于维修工程师培训。',
    category: '培训资料',
    type: 'ppt',
    size: 45600000, // 45.6MB
    version: 'Rev.1',
    updatedAt: '2025-07-15 10:20:00',
    views: 345,
    tags: ['V2500', '叶片检查', '培训', '发动机'],
    isFavorite: false,
    isNew: false,
    isUpdated: true,
    isOffline: false
  }
])

// 对话框状态
const documentDialog = ref({
  visible: false,
  document: null
})

const uploadDialog = ref({
  visible: false,
  loading: false
})

const uploadForm = ref({
  title: '',
  category: '',
  description: '',
  tags: []
})

const tagDialog = ref({
  visible: false
})

const tagForm = ref({
  name: '',
  color: ''
})

// 计算属性
const filteredDocuments = computed(() => {
  let result = documents.value

  // 分类筛选
  if (selectedCategory.value && selectedCategory.value !== 'all') {
    const category = documentCategories.value.find(c => c.id === selectedCategory.value)
    if (category) {
      result = result.filter(doc => doc.category === category.name)
    }
  }

  // 搜索筛选
  if (searchQuery.value && searchExecuted.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(doc =>
      doc.title.toLowerCase().includes(query) ||
      doc.description.toLowerCase().includes(query) ||
      doc.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'updated':
        return new Date(b.updatedAt) - new Date(a.updatedAt)
      case 'created':
        return new Date(b.createdAt || b.updatedAt) - new Date(a.createdAt || a.updatedAt)
      case 'name':
        return a.title.localeCompare(b.title)
      case 'views':
        return b.views - a.views
      case 'size':
        return b.size - a.size
      default:
        return 0
    }
  })

  return result
})

const totalDocuments = computed(() => filteredDocuments.value.length)
const totalPages = computed(() => Math.ceil(totalDocuments.value / pageSize.value))

// 方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getDocumentIcon = (type) => {
  const icons = {
    pdf: 'Document',
    doc: 'Document',
    docx: 'Document',
    xls: 'Document',
    xlsx: 'Document',
    ppt: 'Document',
    pptx: 'Document',
    txt: 'Document'
  }
  return icons[type] || 'Document'
}

const getDocumentIconColor = (type) => {
  const colors = {
    pdf: 'text-red-500',
    doc: 'text-blue-500',
    docx: 'text-blue-500',
    xls: 'text-green-500',
    xlsx: 'text-green-500',
    ppt: 'text-orange-500',
    pptx: 'text-orange-500',
    txt: 'text-gray-500'
  }
  return colors[type] || 'text-gray-500'
}

const getContentTitle = () => {
  if (selectedCategory.value === 'all') {
    return '全部文档'
  }
  const category = documentCategories.value.find(c => c.id === selectedCategory.value)
  return category ? category.name : '文档列表'
}

// 事件处理
const handleRefreshDocs = () => {
  ElMessage.success('文档列表已刷新')
}

const handleUploadDoc = () => {
  uploadDialog.value.visible = true
}

const handleOfflineSync = () => {
  ElMessage.info('开始同步离线文档...')
}

const handleDocSearch = () => {
  searchExecuted.value = true
  ElMessage.info(`搜索: ${searchQuery.value}`)
}

const handleSelectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.text
  handleDocSearch()
}

const handleSelectCategory = (categoryId) => {
  selectedCategory.value = categoryId
  currentPage.value = 1
}

const handleSelectTag = (tag) => {
  searchQuery.value = tag.name
  searchExecuted.value = true
}

const handleCreateTag = () => {
  tagDialog.value.visible = true
}

const handleShowRecent = () => {
  ElMessage.info('显示最近访问的文档')
}

const handleShowFavorites = () => {
  ElMessage.info('显示收藏的文档')
}

const handleShowOffline = () => {
  ElMessage.info('显示离线文档')
}

const handleShowShared = () => {
  ElMessage.info('显示共享文档')
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handlePageChange = (newPage) => {
  currentPage.value = newPage
}

const handleViewDocument = (doc) => {
  documentDialog.value.document = doc
  documentDialog.value.visible = true
  
  // 增加访问次数
  doc.views++
}

const handleToggleFavorite = (doc) => {
  doc.isFavorite = !doc.isFavorite
  ElMessage.success(`已${doc.isFavorite ? '添加到' : '移除'}收藏`)
}

const handleDownloadDoc = (doc) => {
  ElMessage.success(`开始下载: ${doc.title}`)
}

const handleDocAction = ({ action, doc }) => {
  switch (action) {
    case 'view':
      handleViewDocument(doc)
      break
    case 'share':
      ElMessage.info(`分享文档: ${doc.title}`)
      break
    case 'annotate':
      ElMessage.info(`添加批注: ${doc.title}`)
      break
    case 'version':
      ElMessage.info(`查看版本历史: ${doc.title}`)
      break
    case 'offline':
      doc.isOffline = !doc.isOffline
      ElMessage.success(`${doc.isOffline ? '已添加到' : '已移除'}离线文档`)
      break
  }
}

const handleAnnotateMode = () => {
  ElMessage.info('进入批注模式')
}

const handlePrintDoc = () => {
  ElMessage.info('准备打印文档')
}

const handleOpenInNewTab = () => {
  ElMessage.info('在新标签页中打开文档')
}

const handleSubmitUpload = () => {
  if (!uploadForm.value.title) {
    ElMessage.warning('请输入文档标题')
    return
  }
  
  uploadDialog.value.loading = true
  
  setTimeout(() => {
    ElMessage.success('文档上传成功')
    uploadDialog.value.visible = false
    uploadDialog.value.loading = false
    
    // 重置表单
    uploadForm.value = {
      title: '',
      category: '',
      description: '',
      tags: []
    }
  }, 2000)
}

const handleCreateTagSubmit = () => {
  if (!tagForm.value.name) {
    ElMessage.warning('请输入标签名称')
    return
  }
  
  const newTag = {
    id: Date.now(),
    name: tagForm.value.name,
    color: tagForm.value.color || '',
    count: 0
  }
  
  myTags.value.push(newTag)
  allTags.value.push({ name: newTag.name })
  
  ElMessage.success('标签创建成功')
  tagDialog.value.visible = false
  
  // 重置表单
  tagForm.value = {
    name: '',
    color: ''
  }
}

// 监听搜索输入
watch(searchQuery, (newVal) => {
  if (!newVal) {
    searchExecuted.value = false
  }
})

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.document-management {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.docs-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-detail {
  @apply text-xs text-gray-500;
}

.search-section {
  @apply modern-card p-6;
}

.search-container {
  @apply max-w-4xl mx-auto;
}

.search-input {
  @apply w-full;
}

.search-suggestions {
  @apply mt-4 p-4 bg-gray-50 rounded-lg;
}

.suggestions-header {
  @apply mb-3;
}

.suggestions-title {
  @apply text-sm font-medium text-gray-700;
}

.suggestions-list {
  @apply space-y-2;
}

.suggestion-item {
  @apply flex items-center space-x-3 p-2 rounded-md cursor-pointer hover:bg-white;
}

.suggestion-text {
  @apply flex-1 text-sm text-gray-700;
}

.suggestion-type {
  @apply text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded;
}

.main-content {
  @apply modern-card;
}

.content-layout {
  @apply flex;
}

.sidebar {
  @apply w-64 p-6 border-r border-gray-200;
}

.sidebar-section {
  @apply mb-8;
}

.section-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.category-tree {
  @apply space-y-2;
}

.category-item {
  @apply p-2 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors;
}

.category-item.active {
  @apply bg-primary-100 text-primary-700;
}

.category-header {
  @apply flex items-center space-x-2;
}

.category-name {
  @apply flex-1 text-sm;
}

.category-count {
  @apply text-xs text-gray-500;
}

.tags-list {
  @apply flex flex-wrap gap-2;
}

.tag-item {
  @apply cursor-pointer;
}

.create-tag-btn {
  @apply text-xs;
}

.quick-access {
  @apply space-y-2;
}

.access-item {
  @apply flex items-center space-x-2 p-2 rounded-lg cursor-pointer hover:bg-gray-100 text-sm text-gray-700;
}

.content-area {
  @apply flex-1 p-6;
}

.content-header {
  @apply flex items-center justify-between mb-6;
}

.header-left {
  @apply flex items-center space-x-4;
}

.content-title {
  @apply text-xl font-semibold text-gray-900;
}

.content-count {
  @apply text-sm text-gray-500;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.documents-list {
  @apply space-y-4;
}

.document-item {
  @apply flex items-start space-x-4 p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow;
}

.doc-icon {
  @apply flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-50 rounded-lg;
}

.doc-info {
  @apply flex-1;
}

.doc-header {
  @apply flex items-start justify-between mb-2;
}

.doc-title {
  @apply font-semibold text-gray-900 text-base leading-tight;
}

.doc-badges {
  @apply flex items-center space-x-1;
}

.doc-description {
  @apply text-sm text-gray-600 mb-3 leading-relaxed;
}

.doc-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500 mb-2;
}

.meta-item {
  @apply flex items-center space-x-1;
}

.doc-tags {
  @apply flex flex-wrap gap-1;
}

.doc-tag {
  @apply text-xs;
}

.doc-actions {
  @apply flex-shrink-0 flex items-center space-x-1;
}

.documents-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.document-card {
  @apply modern-card p-4 cursor-pointer hover:shadow-lg transition-shadow;
}

.card-header {
  @apply flex items-start justify-between mb-4;
}

.doc-icon-large {
  @apply w-16 h-16 flex items-center justify-center bg-gray-50 rounded-lg;
}

.card-badges {
  @apply flex flex-col space-y-1;
}

.card-content {
  @apply mb-4;
}

.card-title {
  @apply font-semibold text-gray-900 text-sm leading-tight mb-2;
}

.card-description {
  @apply text-xs text-gray-600 leading-relaxed mb-3;
}

.card-meta {
  @apply flex items-center justify-between text-xs text-gray-500;
}

.card-footer {
  @apply flex items-center justify-between;
}

.card-tags {
  @apply flex space-x-1;
}

.card-tag {
  @apply text-xs;
}

.card-actions {
  @apply flex items-center space-x-1;
}

.pagination-container {
  @apply flex justify-center mt-8;
}

.document-viewer-dialog :deep(.el-dialog__body) {
  @apply p-0;
}

.document-viewer {
  @apply h-full;
}

.viewer-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.doc-info-detailed {
  @apply flex-1;
}

.doc-title-large {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.doc-meta-detailed {
  @apply flex items-center space-x-6 text-sm text-gray-600;
}

.viewer-actions {
  @apply flex items-center space-x-2;
}

.viewer-content {
  @apply p-8 min-h-96 flex items-center justify-center bg-gray-50;
}

.content-placeholder {
  @apply text-center;
}

.upload-form, .tag-form {
  @apply space-y-4;
}
</style>