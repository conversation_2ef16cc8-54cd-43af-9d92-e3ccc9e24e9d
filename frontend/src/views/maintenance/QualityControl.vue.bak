<!--
维修质量控制和报告系统
版本: 1.0
创建时间: 2025-07-23

维修工程师专用的质量控制管理和报告生成界面
特点：
1. 维修质量检查和验收流程
2. 质量问题跟踪和纠正措施
3. 质量报告生成和统计分析
4. 质量标准管理和合规检查
5. 质量审计和持续改进
-->

<template>
  <div class="quality-control">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-area">
          <h1 class="page-title">质量控制中心</h1>
          <p class="page-subtitle">确保维修质量标准，跟踪质量问题，生成专业质量报告</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="handleRefreshQuality">
            <Refresh class="w-4 h-4 mr-2" />
            刷新数据
          </el-button>
          <el-button @click="handleCreateInspection">
            <CheckCircle class="w-4 h-4 mr-2" />
            新建检查
          </el-button>
          <el-button @click="handleGenerateReport" type="primary">
            <Document class="w-4 h-4 mr-2" />
            生成报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 质量概览仪表板 -->
    <div class="quality-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card pass-rate">
          <div class="card-icon bg-green-100 text-green-600">
            <Shield class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ qualityStats.passRate }}%</div>
            <div class="card-label">质量合格率</div>
            <div class="card-trend positive">+{{ qualityStats.passRateChange }}% 本月</div>
          </div>
        </div>
        
        <div class="overview-card inspections">
          <div class="card-icon bg-blue-100 text-blue-600">
            <Search class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ qualityStats.totalInspections }}</div>
            <div class="card-label">质量检查数</div>
            <div class="card-detail">本月: {{ qualityStats.monthlyInspections }}</div>
          </div>
        </div>
        
        <div class="overview-card issues">
          <div class="card-icon bg-orange-100 text-orange-600">
            <Warning class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ qualityStats.activeIssues }}</div>
            <div class="card-label">待处理问题</div>
            <div class="card-detail">需要关注</div>
          </div>
        </div>
        
        <div class="overview-card audits">
          <div class="card-icon bg-purple-100 text-purple-600">
            <Award class="w-8 h-8" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ qualityStats.auditScore }}</div>
            <div class="card-label">审计评分</div>
            <div class="card-trend positive">优秀水平</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeQualityTab" @tab-change="handleTabChange">
        <!-- 质量检查 -->
        <el-tab-pane label="质量检查" name="inspections">
          <div class="inspections-panel">
            <!-- 检查任务列表 -->
            <div class="inspection-tasks">
              <div class="tasks-header">
                <h3 class="text-lg font-semibold text-gray-800">质量检查任务</h3>
                <div class="tasks-filters">
                  <el-select v-model="inspectionFilter" size="small" placeholder="筛选状态">
                    <el-option label="全部任务" value="" />
                    <el-option label="待检查" value="pending" />
                    <el-option label="检查中" value="in_progress" />
                    <el-option label="已完成" value="completed" />
                    <el-option label="不合格" value="failed" />
                  </el-select>
                </div>
              </div>
              
              <div class="tasks-list">
                <div
                  v-for="inspection in filteredInspections"
                  :key="inspection.id"
                  class="inspection-item"
                  @click="handleSelectInspection(inspection)"
                  :class="{ 'selected': selectedInspection?.id === inspection.id }"
                >
                  <div class="inspection-header">
                    <div class="inspection-info">
                      <h4 class="inspection-title">{{ inspection.title }}</h4>
                      <p class="inspection-order">工单: {{ inspection.workOrderNo }}</p>
                    </div>
                    
                    <div class="inspection-status">
                      <el-tag :type="getInspectionStatusColor(inspection.status)" size="small">
                        {{ getInspectionStatusLabel(inspection.status) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="inspection-details">
                    <div class="detail-item">
                      <Calendar class="w-3 h-3" />
                      <span>计划: {{ formatDate(inspection.scheduledDate) }}</span>
                    </div>
                    <div class="detail-item">
                      <User class="w-3 h-3" />
                      <span>检查员: {{ inspection.inspector }}</span>
                    </div>
                    <div class="detail-item">
                      <Clock class="w-3 h-3" />
                      <span>类型: {{ getInspectionTypeLabel(inspection.type) }}</span>
                    </div>
                  </div>
                  
                  <div class="inspection-progress" v-if="inspection.status === 'in_progress'">
                    <div class="progress-info">
                      <span class="progress-text">检查进度: {{ inspection.progress }}%</span>
                      <el-progress
                        :percentage="inspection.progress"
                        :stroke-width="4"
                        :show-text="false"
                        :color="getProgressColor(inspection.progress)"
                      />
                    </div>
                  </div>
                  
                  <div class="inspection-actions">
                    <el-button
                      v-if="inspection.status === 'pending'"
                      @click.stop="handleStartInspection(inspection)"
                      size="small"
                      type="primary"
                    >
                      开始检查
                    </el-button>
                    <el-button
                      v-if="inspection.status === 'in_progress'"
                      @click.stop="handleContinueInspection(inspection)"
                      size="small"
                      type="success"
                    >
                      继续检查
                    </el-button>
                    <el-button
                      @click.stop="handleViewInspectionReport(inspection)"
                      size="small"
                    >
                      查看报告
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 检查详情 -->
        <el-tab-pane label="检查详情" name="details">
          <div class="details-panel" v-if="selectedInspection">
            <div class="detail-header">
              <div class="detail-info">
                <h3 class="detail-title">{{ selectedInspection.title }}</h3>
                <p class="detail-subtitle">工单号: {{ selectedInspection.workOrderNo }}</p>
              </div>
              
              <div class="detail-actions">
                <el-button @click="handlePrintInspection" size="small">
                  <Printer class="w-3 h-3 mr-1" />
                  打印
                </el-button>
                <el-button @click="handleExportInspection" size="small" type="primary">
                  <Download class="w-3 h-3 mr-1" />
                  导出
                </el-button>
              </div>
            </div>
            
            <!-- 检查项目 -->
            <div class="inspection-checklist">
              <div class="checklist-header">
                <h4 class="checklist-title">检查项目清单</h4>
                <div class="checklist-progress">
                  <span class="progress-label">完成进度</span>
                  <div class="progress-bar">
                    <el-progress
                      :percentage="selectedInspection.progress"
                      :color="getProgressColor(selectedInspection.progress)"
                    />
                  </div>
                </div>
              </div>
              
              <div class="checklist-items">
                <div
                  v-for="item in selectedInspection.checklistItems"
                  :key="item.id"
                  class="checklist-item"
                  :class="{ 
                    'completed': item.status === 'pass' || item.status === 'fail',
                    'failed': item.status === 'fail'
                  }"
                >
                  <div class="item-header">
                    <div class="item-info">
                      <h5 class="item-title">{{ item.title }}</h5>
                      <p class="item-description">{{ item.description }}</p>
                    </div>
                    
                    <div class="item-status">
                      <el-radio-group
                        v-model="item.status"
                        @change="handleItemStatusChange(item)"
                        size="small"
                      >
                        <el-radio-button label="pending">待检</el-radio-button>
                        <el-radio-button label="pass">合格</el-radio-button>
                        <el-radio-button label="fail">不合格</el-radio-button>
                      </el-radio-group>
                    </div>
                  </div>
                  
                  <div class="item-details" v-if="item.status !== 'pending'">
                    <div class="details-grid">
                      <div class="detail-field">
                        <label class="field-label">检查结果</label>
                        <el-input
                          v-model="item.result"
                          placeholder="记录检查结果"
                          size="small"
                        />
                      </div>
                      
                      <div class="detail-field">
                        <label class="field-label">测量值</label>
                        <el-input
                          v-model="item.measurement"
                          placeholder="测量数值"
                          size="small"
                        />
                      </div>
                      
                      <div class="detail-field">
                        <label class="field-label">标准范围</label>
                        <span class="field-value">{{ item.standardRange }}</span>
                      </div>
                      
                      <div class="detail-field">
                        <label class="field-label">检查员</label>
                        <span class="field-value">{{ item.inspector || selectedInspection.inspector }}</span>
                      </div>
                    </div>
                    
                    <div class="item-notes" v-if="item.status === 'fail'">
                      <label class="notes-label">不合格说明</label>
                      <el-input
                        v-model="item.failureReason"
                        type="textarea"
                        :rows="2"
                        placeholder="描述不合格原因和建议处理措施"
                        size="small"
                      />
                    </div>
                    
                    <div class="item-photos">
                      <label class="photos-label">检查照片</label>
                      <el-upload
                        :file-list="item.photos || []"
                        :auto-upload="false"
                        list-type="picture-card"
                        accept="image/*"
                        multiple
                      >
                        <Plus class="w-4 h-4" />
                      </el-upload>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="checklist-actions">
                <el-button @click="handleSaveInspection">
                  保存检查结果
                </el-button>
                <el-button @click="handleSubmitInspection" type="primary">
                  提交检查报告
                </el-button>
              </div>
            </div>
            
            <!-- 检查总结 -->
            <div class="inspection-summary">
              <h4 class="summary-title">检查总结</h4>
              
              <div class="summary-content">
                <div class="summary-stats">
                  <div class="stat-item">
                    <span class="stat-label">检查项目</span>
                    <span class="stat-value">{{ selectedInspection.checklistItems.length }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">合格项目</span>
                    <span class="stat-value text-green-600">{{ getPassedItems(selectedInspection) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">不合格项目</span>
                    <span class="stat-value text-red-600">{{ getFailedItems(selectedInspection) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">合格率</span>
                    <span class="stat-value">{{ getInspectionPassRate(selectedInspection) }}%</span>
                  </div>
                </div>
                
                <div class="summary-form">
                  <el-form :model="inspectionSummary" label-width="100px">
                    <el-form-item label="总体评价">
                      <el-select v-model="inspectionSummary.overallResult" placeholder="选择总体评价">
                        <el-option label="完全合格" value="excellent" />
                        <el-option label="基本合格" value="good" />
                        <el-option label="部分合格" value="fair" />
                        <el-option label="不合格" value="fail" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="检查结论">
                      <el-input
                        v-model="inspectionSummary.conclusion"
                        type="textarea"
                        :rows="3"
                        placeholder="总结检查情况，描述主要发现和结论"
                      />
                    </el-form-item>
                    
                    <el-form-item label="建议措施">
                      <el-input
                        v-model="inspectionSummary.recommendations"
                        type="textarea"
                        :rows="3"
                        placeholder="提出改进建议和后续处理措施"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="no-selection">
            <div class="no-selection-content">
              <CheckCircle class="w-16 h-16 text-gray-400" />
              <h3 class="text-lg font-medium text-gray-600 mb-2">选择检查任务</h3>
              <p class="text-gray-500">请从左侧选择一个检查任务来查看详情</p>
            </div>
          </div>
        </el-tab-pane>

        <!-- 质量问题 -->
        <el-tab-pane label="质量问题" name="issues">
          <div class="issues-panel">
            <!-- 问题列表筛选 -->
            <div class="issues-filter">
              <div class="filter-row">
                <div class="filter-group">
                  <label class="filter-label">问题状态</label>
                  <el-select v-model="issueStatusFilter" placeholder="选择状态" clearable>
                    <el-option label="全部状态" value="" />
                    <el-option label="新发现" value="new" />
                    <el-option label="分析中" value="analyzing" />
                    <el-option label="处理中" value="resolving" />
                    <el-option label="已解决" value="resolved" />
                    <el-option label="已关闭" value="closed" />
                  </el-select>
                </div>
                
                <div class="filter-group">
                  <label class="filter-label">问题等级</label>
                  <el-select v-model="issueSeverityFilter" placeholder="选择等级" clearable>
                    <el-option label="全部等级" value="" />
                    <el-option label="严重" value="critical" />
                    <el-option label="重要" value="major" />
                    <el-option label="一般" value="minor" />
                    <el-option label="轻微" value="trivial" />
                  </el-select>
                </div>
                
                <div class="filter-actions">
                  <el-button @click="handleFilterIssues">筛选</el-button>
                  <el-button @click="handleResetIssueFilter">重置</el-button>
                  <el-button @click="handleCreateIssue" type="primary">
                    <Plus class="w-3 h-3 mr-1" />
                    创建问题
                  </el-button>
                </div>
              </div>
            </div>
            
            <!-- 质量问题列表 -->
            <div class="issues-list">
              <div
                v-for="issue in filteredQualityIssues"
                :key="issue.id"
                class="issue-item"
              >
                <div class="issue-header">
                  <div class="issue-info">
                    <h4 class="issue-title">{{ issue.title }}</h4>
                    <p class="issue-id">问题编号: {{ issue.issueNo }}</p>
                  </div>
                  
                  <div class="issue-badges">
                    <el-tag :type="getIssueSeverityColor(issue.severity)" size="small">
                      {{ getIssueSeverityLabel(issue.severity) }}
                    </el-tag>
                    <el-tag :type="getIssueStatusColor(issue.status)" size="small">
                      {{ getIssueStatusLabel(issue.status) }}
                    </el-tag>
                  </div>
                </div>
                
                <p class="issue-description">{{ issue.description }}</p>
                
                <div class="issue-meta">
                  <div class="meta-item">
                    <Calendar class="w-3 h-3" />
                    <span>发现时间: {{ formatDate(issue.discoveredAt) }}</span>
                  </div>
                  <div class="meta-item">
                    <User class="w-3 h-3" />
                    <span>发现人: {{ issue.discoveredBy }}</span>
                  </div>
                  <div class="meta-item">
                    <Tool class="w-3 h-3" />
                    <span>关联工单: {{ issue.workOrderNo }}</span>
                  </div>
                </div>
                
                <div class="issue-progress" v-if="issue.correctionPlan">
                  <div class="progress-header">
                    <span class="progress-label">纠正措施进度</span>
                    <span class="progress-percent">{{ issue.correctionProgress }}%</span>
                  </div>
                  <el-progress
                    :percentage="issue.correctionProgress"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getProgressColor(issue.correctionProgress)"
                  />
                </div>
                
                <div class="issue-actions">
                  <el-button @click="handleViewIssue(issue)" size="small">
                    查看详情
                  </el-button>
                  <el-button
                    v-if="issue.status === 'new'"
                    @click="handleAnalyzeIssue(issue)"
                    size="small"
                    type="warning"
                  >
                    开始分析
                  </el-button>
                  <el-button
                    v-if="issue.status === 'analyzing'"
                    @click="handleCreateCorrectionPlan(issue)"
                    size="small"
                    type="primary"
                  >
                    制定纠正措施
                  </el-button>
                  <el-button
                    v-if="issue.status === 'resolving'"
                    @click="handleUpdateProgress(issue)"
                    size="small"
                    type="success"
                  >
                    更新进度
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 质量报告 -->
        <el-tab-pane label="质量报告" name="reports">
          <div class="reports-panel">
            <!-- 报告生成器 -->
            <div class="report-generator">
              <div class="generator-header">
                <h3 class="text-lg font-semibold text-gray-800">质量报告生成</h3>
              </div>
              
              <div class="generator-form">
                <el-form :model="reportForm" label-width="120px">
                  <div class="form-row">
                    <el-form-item label="报告类型">
                      <el-select v-model="reportForm.type" placeholder="选择报告类型">
                        <el-option label="日检查报告" value="daily" />
                        <el-option label="周质量报告" value="weekly" />
                        <el-option label="月质量报告" value="monthly" />
                        <el-option label="质量问题报告" value="issues" />
                        <el-option label="审计报告" value="audit" />
                        <el-option label="自定义报告" value="custom" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="时间范围">
                      <el-date-picker
                        v-model="reportForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      />
                    </el-form-item>
                  </div>
                  
                  <el-form-item label="包含内容">
                    <el-checkbox-group v-model="reportForm.includeItems">
                      <el-checkbox label="quality_metrics">质量指标统计</el-checkbox>
                      <el-checkbox label="inspection_results">检查结果汇总</el-checkbox>
                      <el-checkbox label="quality_issues">质量问题分析</el-checkbox>
                      <el-checkbox label="trend_analysis">趋势分析图表</el-checkbox>
                      <el-checkbox label="recommendations">改进建议</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  
                  <el-form-item label="报告格式">
                    <el-radio-group v-model="reportForm.format">
                      <el-radio label="pdf">PDF格式</el-radio>
                      <el-radio label="excel">Excel格式</el-radio>
                      <el-radio label="word">Word格式</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button @click="handlePreviewReport">预览报告</el-button>
                    <el-button @click="handleGenerateReportSubmit" type="primary" :loading="reportForm.generating">
                      生成报告
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <!-- 历史报告 -->
            <div class="historical-reports">
              <div class="reports-header">
                <h4 class="text-md font-semibold text-gray-800">历史报告</h4>
              </div>
              
              <div class="reports-list">
                <div
                  v-for="report in historicalReports"
                  :key="report.id"
                  class="report-item"
                >
                  <div class="report-icon">
                    <Document class="w-8 h-8 text-blue-500" />
                  </div>
                  
                  <div class="report-info">
                    <h5 class="report-title">{{ report.title }}</h5>
                    <div class="report-meta">
                      <span class="meta-text">{{ report.type }}</span>
                      <span class="meta-text">{{ formatDate(report.generatedAt) }}</span>
                      <span class="meta-text">{{ formatFileSize(report.size) }}</span>
                    </div>
                  </div>
                  
                  <div class="report-actions">
                    <el-button @click="handleViewReport(report)" size="small">
                      查看
                    </el-button>
                    <el-button @click="handleDownloadReport(report)" size="small" type="primary">
                      <Download class="w-3 h-3 mr-1" />
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 创建检查对话框 -->
    <el-dialog
      v-model="inspectionDialog.visible"
      title="创建质量检查"
      width="700px"
    >
      <div class="inspection-form">
        <el-form :model="inspectionForm" :rules="inspectionRules" ref="inspectionFormRef" label-width="120px">
          <el-form-item label="检查标题" prop="title">
            <el-input v-model="inspectionForm.title" placeholder="请输入检查标题" />
          </el-form-item>
          
          <el-form-item label="关联工单" prop="workOrderNo">
            <el-select
              v-model="inspectionForm.workOrderNo"
              placeholder="选择关联工单"
              filterable
            >
              <el-option
                v-for="order in workOrderOptions"
                :key="order.orderNo"
                :label="`${order.orderNo} - ${order.title}`"
                :value="order.orderNo"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="检查类型" prop="type">
            <el-select v-model="inspectionForm.type" placeholder="选择检查类型">
              <el-option label="首检" value="first" />
              <el-option label="过程检查" value="process" />
              <el-option label="终检" value="final" />
              <el-option label="复检" value="re_inspection" />
              <el-option label="专项检查" value="special" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="检查员" prop="inspector">
            <el-select v-model="inspectionForm.inspector" placeholder="选择检查员">
              <el-option
                v-for="inspector in inspectorOptions"
                :key="inspector.id"
                :label="inspector.name"
                :value="inspector.name"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="计划时间" prop="scheduledDate">
            <el-date-picker
              v-model="inspectionForm.scheduledDate"
              type="datetime"
              placeholder="选择检查时间"
            />
          </el-form-item>
          
          <el-form-item label="检查要求">
            <el-input
              v-model="inspectionForm.requirements"
              type="textarea"
              :rows="3"
              placeholder="描述检查要求和注意事项"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="inspectionDialog.visible = false">取消</el-button>
        <el-button @click="handleSubmitInspectionForm" type="primary" :loading="inspectionDialog.loading">
          创建检查
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, CheckCircle, Document, Shield, Search, Warning, Award,
  Calendar, User, Clock, Printer, Download, Plus, Tool
} from '@element-plus/icons-vue'

// 响应式数据
const activeQualityTab = ref('inspections')
const inspectionFilter = ref('')
const selectedInspection = ref(null)
const issueStatusFilter = ref('')
const issueSeverityFilter = ref('')

// 统计数据
const qualityStats = ref({
  passRate: 96.5,
  passRateChange: 2.3,
  totalInspections: 234,
  monthlyInspections: 45,
  activeIssues: 8,
  auditScore: 98.2
})

// 检查任务数据
const inspectionTasks = ref([
  {
    id: 1,
    title: 'CFM56发动机燃油喷嘴更换质量检查',
    workOrderNo: 'WO-2024-001',
    type: 'final',
    status: 'pending',
    progress: 0,
    scheduledDate: '2025-07-25 09:00:00',
    inspector: '质检员李四',
    checklistItems: [
      {
        id: 1,
        title: '燃油喷嘴安装位置检查',
        description: '检查所有燃油喷嘴是否按照技术要求正确安装到位',
        status: 'pending',
        standardRange: '按图纸要求',
        result: '',
        measurement: '',
        failureReason: '',
        photos: []
      },
      {
        id: 2,
        title: '安装扭矩检查',
        description: '使用扭矩扳手检查所有连接螺栓的扭矩值',
        status: 'pending',
        standardRange: '25-30 N·m',
        result: '',
        measurement: '',
        failureReason: '',
        photos: []
      },
      {
        id: 3,
        title: '密封性检查',
        description: '进行燃油系统密封性测试，检查是否有泄漏',
        status: 'pending',
        standardRange: '无泄漏',
        result: '',
        measurement: '',
        failureReason: '',
        photos: []
      }
    ]
  },
  {
    id: 2,
    title: 'APU辅助动力装置定期检查',
    workOrderNo: 'WO-2024-002',
    type: 'process',
    status: 'in_progress',
    progress: 65,
    scheduledDate: '2025-07-24 14:00:00',
    inspector: '质检员张三',
    checklistItems: [
      {
        id: 4,
        title: 'APU外观检查',
        description: '检查APU外观是否有损坏、腐蚀等问题',
        status: 'pass',
        standardRange: '无明显损坏',
        result: '外观良好，无损坏',
        measurement: '通过',
        failureReason: '',
        photos: []
      },
      {
        id: 5,
        title: '电气连接检查',
        description: '检查所有电气连接是否牢固可靠',
        status: 'pass',
        standardRange: '连接牢固',
        result: '所有连接正常',
        measurement: '通过',
        failureReason: '',
        photos: []
      }
    ]
  }
])

// 质量问题数据
const qualityIssues = ref([
  {
    id: 1,
    issueNo: 'QI-2024-001',
    title: '燃油喷嘴安装扭矩不符合要求',
    description: '在CFM56发动机维修过程中发现部分燃油喷嘴的安装扭矩超出标准范围，可能影响密封性能。',
    severity: 'major',
    status: 'resolving',
    discoveredAt: '2025-07-22 15:30:00',
    discoveredBy: '质检员李四',
    workOrderNo: 'WO-2024-001',
    correctionPlan: '重新调整扭矩值并验证',
    correctionProgress: 75
  },
  {
    id: 2,
    issueNo: 'QI-2024-002',
    title: 'APU管路连接存在轻微渗漏',
    description: '在APU定期检查中发现燃油管路连接处有轻微渗漏现象，需要及时处理。',
    severity: 'minor',
    status: 'analyzing',
    discoveredAt: '2025-07-23 10:15:00',
    discoveredBy: '质检员张三',
    workOrderNo: 'WO-2024-002',
    correctionPlan: null,
    correctionProgress: 0
  }
])

// 历史报告数据
const historicalReports = ref([
  {
    id: 1,
    title: '2025年7月质量月报',
    type: '月质量报告',
    generatedAt: '2025-07-23 09:00:00',
    size: 2048000,
    format: 'pdf'
  },
  {
    id: 2,
    title: '维修质量问题分析报告',
    type: '质量问题报告',
    generatedAt: '2025-07-20 16:30:00',
    size: 1536000,
    format: 'word'
  }
])

// 表单数据
const inspectionSummary = ref({
  overallResult: '',
  conclusion: '',
  recommendations: ''
})

const reportForm = ref({
  type: '',
  dateRange: [],
  includeItems: ['quality_metrics', 'inspection_results'],
  format: 'pdf',
  generating: false
})

const inspectionDialog = ref({
  visible: false,
  loading: false
})

const inspectionForm = ref({
  title: '',
  workOrderNo: '',
  type: '',
  inspector: '',
  scheduledDate: null,
  requirements: ''
})

const inspectionRules = {
  title: [{ required: true, message: '请输入检查标题', trigger: 'blur' }],
  workOrderNo: [{ required: true, message: '请选择关联工单', trigger: 'change' }],
  type: [{ required: true, message: '请选择检查类型', trigger: 'change' }],
  inspector: [{ required: true, message: '请选择检查员', trigger: 'change' }],
  scheduledDate: [{ required: true, message: '请选择检查时间', trigger: 'change' }]
}

const inspectionFormRef = ref()

// 选项数据
const workOrderOptions = ref([
  { orderNo: 'WO-2024-001', title: 'CFM56发动机燃油喷嘴更换' },
  { orderNo: 'WO-2024-002', title: 'APU辅助动力装置定期检查' },
  { orderNo: 'WO-2024-003', title: '起落架液压系统维修' }
])

const inspectorOptions = ref([
  { id: 1, name: '质检员张三' },
  { id: 2, name: '质检员李四' },
  { id: 3, name: '质检员王五' }
])

// 计算属性
const filteredInspections = computed(() => {
  if (!inspectionFilter.value) {
    return inspectionTasks.value
  }
  return inspectionTasks.value.filter(inspection => inspection.status === inspectionFilter.value)
})

const filteredQualityIssues = computed(() => {
  let result = qualityIssues.value

  if (issueStatusFilter.value) {
    result = result.filter(issue => issue.status === issueStatusFilter.value)
  }

  if (issueSeverityFilter.value) {
    result = result.filter(issue => issue.severity === issueSeverityFilter.value)
  }

  return result
})

// 方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getInspectionStatusLabel = (status) => {
  const labels = {
    pending: '待检查',
    in_progress: '检查中',
    completed: '已完成',
    failed: '不合格'
  }
  return labels[status] || status
}

const getInspectionStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return colors[status] || ''
}

const getInspectionTypeLabel = (type) => {
  const labels = {
    first: '首检',
    process: '过程检查',
    final: '终检',
    re_inspection: '复检',
    special: '专项检查'
  }
  return labels[type] || type
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getIssueSeverityLabel = (severity) => {
  const labels = {
    critical: '严重',
    major: '重要',
    minor: '一般',
    trivial: '轻微'
  }
  return labels[severity] || severity
}

const getIssueSeverityColor = (severity) => {
  const colors = {
    critical: 'danger',
    major: 'warning',
    minor: 'info',
    trivial: 'success'
  }
  return colors[severity] || ''
}

const getIssueStatusLabel = (status) => {
  const labels = {
    new: '新发现',
    analyzing: '分析中',
    resolving: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return labels[status] || status
}

const getIssueStatusColor = (status) => {
  const colors = {
    new: 'danger',
    analyzing: 'warning',
    resolving: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return colors[status] || ''
}

const getPassedItems = (inspection) => {
  return inspection.checklistItems.filter(item => item.status === 'pass').length
}

const getFailedItems = (inspection) => {
  return inspection.checklistItems.filter(item => item.status === 'fail').length
}

const getInspectionPassRate = (inspection) => {
  const totalItems = inspection.checklistItems.length
  const passedItems = getPassedItems(inspection)
  return totalItems > 0 ? Math.round((passedItems / totalItems) * 100) : 0
}

// 事件处理
const handleRefreshQuality = () => {
  ElMessage.success('质量数据已刷新')
}

const handleCreateInspection = () => {
  inspectionDialog.value.visible = true
}

const handleGenerateReport = () => {
  activeQualityTab.value = 'reports'
}

const handleTabChange = (tabName) => {
  console.log('切换标签:', tabName)
}

const handleSelectInspection = (inspection) => {
  selectedInspection.value = inspection
}

const handleStartInspection = (inspection) => {
  inspection.status = 'in_progress'
  ElMessage.success(`开始检查: ${inspection.title}`)
}

const handleContinueInspection = (inspection) => {
  selectedInspection.value = inspection
  activeQualityTab.value = 'details'
}

const handleViewInspectionReport = (inspection) => {
  ElMessage.info(`查看检查报告: ${inspection.title}`)
}

const handleItemStatusChange = (item) => {
  // 重新计算检查进度
  if (selectedInspection.value) {
    const totalItems = selectedInspection.value.checklistItems.length
    const completedItems = selectedInspection.value.checklistItems.filter(
      i => i.status === 'pass' || i.status === 'fail'
    ).length
    selectedInspection.value.progress = Math.round((completedItems / totalItems) * 100)
  }
  
  ElMessage.success(`检查项目状态已更新: ${item.title}`)
}

const handleSaveInspection = () => {
  ElMessage.success('检查结果已保存')
}

const handleSubmitInspection = () => {
  if (selectedInspection.value) {
    selectedInspection.value.status = 'completed'
    ElMessage.success('检查报告已提交')
  }
}

const handlePrintInspection = () => {
  ElMessage.info('准备打印检查报告')
}

const handleExportInspection = () => {
  ElMessage.success('检查报告导出成功')
}

const handleFilterIssues = () => {
  ElMessage.info('筛选质量问题')
}

const handleResetIssueFilter = () => {
  issueStatusFilter.value = ''
  issueSeverityFilter.value = ''
  ElMessage.info('重置筛选条件')
}

const handleCreateIssue = () => {
  ElMessage.info('创建新的质量问题')
}

const handleViewIssue = (issue) => {
  ElMessage.info(`查看问题详情: ${issue.issueNo}`)
}

const handleAnalyzeIssue = (issue) => {
  issue.status = 'analyzing'
  ElMessage.success(`开始分析问题: ${issue.issueNo}`)
}

const handleCreateCorrectionPlan = (issue) => {
  ElMessage.info(`制定纠正措施: ${issue.issueNo}`)
}

const handleUpdateProgress = (issue) => {
  ElMessage.info(`更新处理进度: ${issue.issueNo}`)
}

const handlePreviewReport = () => {
  ElMessage.info('预览质量报告')
}

const handleGenerateReportSubmit = () => {
  if (!reportForm.value.type) {
    ElMessage.warning('请选择报告类型')
    return
  }
  
  reportForm.value.generating = true
  
  setTimeout(() => {
    ElMessage.success('质量报告生成成功')
    reportForm.value.generating = false
  }, 2000)
}

const handleViewReport = (report) => {
  ElMessage.info(`查看报告: ${report.title}`)
}

const handleDownloadReport = (report) => {
  ElMessage.success(`开始下载: ${report.title}`)
}

const handleSubmitInspectionForm = async () => {
  try {
    await inspectionFormRef.value.validate()
    inspectionDialog.value.loading = true
    
    setTimeout(() => {
      ElMessage.success('质量检查创建成功')
      inspectionDialog.value.visible = false
      inspectionDialog.value.loading = false
      
      // 重置表单
      inspectionForm.value = {
        title: '',
        workOrderNo: '',
        type: '',
        inspector: '',
        scheduledDate: null,
        requirements: ''
      }
    }, 1000)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 选择第一个检查任务
  if (inspectionTasks.value.length > 0) {
    selectedInspection.value = inspectionTasks.value[0]
  }
})
</script>

<style scoped>
.quality-control {
  @apply space-y-8;
}

.page-header {
  @apply modern-card p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.title-area h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.title-area p {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.quality-overview {
  @apply space-y-6;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.card-icon {
  @apply w-16 h-16 rounded-lg flex items-center justify-center;
}

.card-content {
  @apply flex-1;
}

.card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.card-label {
  @apply text-sm text-gray-600 mb-1;
}

.card-trend {
  @apply text-xs;
}

.card-trend.positive {
  @apply text-green-600;
}

.card-detail {
  @apply text-xs text-gray-500;
}

.main-content {
  @apply modern-card;
}

.inspections-panel, .details-panel, .issues-panel, .reports-panel {
  @apply p-6;
}

.inspection-tasks {
  @apply space-y-4;
}

.tasks-header {
  @apply flex items-center justify-between mb-6;
}

.tasks-filters {
  @apply flex items-center space-x-2;
}

.tasks-list {
  @apply space-y-4;
}

.inspection-item {
  @apply p-4 border border-gray-200 rounded-lg cursor-pointer transition-all hover:shadow-md;
}

.inspection-item.selected {
  @apply border-primary-500 bg-primary-50;
}

.inspection-header {
  @apply flex items-start justify-between mb-3;
}

.inspection-info h4 {
  @apply font-semibold text-gray-900 mb-1;
}

.inspection-info p {
  @apply text-sm text-gray-600;
}

.inspection-details {
  @apply flex items-center space-x-4 text-xs text-gray-500 mb-3;
}

.detail-item {
  @apply flex items-center space-x-1;
}

.inspection-progress {
  @apply mb-3;
}

.progress-info {
  @apply space-y-1;
}

.progress-text {
  @apply text-sm text-gray-700;
}

.inspection-actions {
  @apply flex items-center space-x-2;
}

.detail-header {
  @apply flex items-center justify-between mb-6;
}

.detail-info h3 {
  @apply text-xl font-semibold text-gray-900 mb-1;
}

.detail-info p {
  @apply text-gray-600;
}

.detail-actions {
  @apply flex items-center space-x-2;
}

.inspection-checklist {
  @apply modern-card p-6 mb-6;
}

.checklist-header {
  @apply flex items-center justify-between mb-6;
}

.checklist-title {
  @apply text-lg font-semibold text-gray-800;
}

.checklist-progress {
  @apply flex items-center space-x-3;
}

.progress-label {
  @apply text-sm text-gray-600;
}

.progress-bar {
  @apply w-48;
}

.checklist-items {
  @apply space-y-6;
}

.checklist-item {
  @apply p-4 border border-gray-200 rounded-lg;
}

.checklist-item.completed {
  @apply bg-green-50 border-green-200;
}

.checklist-item.failed {
  @apply bg-red-50 border-red-200;
}

.item-header {
  @apply flex items-start justify-between mb-4;
}

.item-info h5 {
  @apply font-semibold text-gray-900 mb-2;
}

.item-info p {
  @apply text-sm text-gray-600;
}

.item-details {
  @apply space-y-4;
}

.details-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.detail-field {
  @apply space-y-1;
}

.field-label {
  @apply text-sm font-medium text-gray-700;
}

.field-value {
  @apply text-sm text-gray-900;
}

.item-notes {
  @apply space-y-2;
}

.notes-label {
  @apply block text-sm font-medium text-gray-700;
}

.item-photos {
  @apply space-y-2;
}

.photos-label {
  @apply block text-sm font-medium text-gray-700;
}

.checklist-actions {
  @apply flex items-center justify-end space-x-3 mt-6;
}

.inspection-summary {
  @apply modern-card p-6;
}

.summary-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.summary-content {
  @apply space-y-6;
}

.summary-stats {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.stat-item {
  @apply text-center p-4 bg-gray-50 rounded-lg;
}

.stat-label {
  @apply block text-sm text-gray-600 mb-1;
}

.stat-value {
  @apply text-xl font-bold text-gray-900;
}

.summary-form {
  @apply space-y-4;
}

.no-selection {
  @apply flex items-center justify-center h-96;
}

.no-selection-content {
  @apply text-center;
}

.issues-filter {
  @apply modern-card p-4 mb-6;
}

.filter-row {
  @apply flex items-end space-x-4;
}

.filter-group {
  @apply flex-1;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.filter-actions {
  @apply flex items-center space-x-2;
}

.issues-list {
  @apply space-y-4;
}

.issue-item {
  @apply modern-card p-6;
}

.issue-header {
  @apply flex items-start justify-between mb-3;
}

.issue-info h4 {
  @apply font-semibold text-gray-900 mb-1;
}

.issue-info p {
  @apply text-sm text-gray-600;
}

.issue-badges {
  @apply flex items-center space-x-2;
}

.issue-description {
  @apply text-sm text-gray-700 mb-4;
}

.issue-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500 mb-4;
}

.meta-item {
  @apply flex items-center space-x-1;
}

.issue-progress {
  @apply mb-4;
}

.progress-header {
  @apply flex items-center justify-between mb-2;
}

.progress-label {
  @apply text-sm text-gray-700;
}

.progress-percent {
  @apply text-sm font-medium text-gray-900;
}

.issue-actions {
  @apply flex items-center space-x-2;
}

.report-generator {
  @apply modern-card p-6 mb-6;
}

.generator-header {
  @apply mb-6;
}

.generator-form {
  @apply space-y-4;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.historical-reports {
  @apply modern-card p-6;
}

.reports-header {
  @apply mb-4;
}

.reports-list {
  @apply space-y-4;
}

.report-item {
  @apply flex items-center space-x-4 p-4 border border-gray-200 rounded-lg;
}

.report-icon {
  @apply flex-shrink-0;
}

.report-info {
  @apply flex-1;
}

.report-title {
  @apply font-semibold text-gray-900 mb-1;
}

.report-meta {
  @apply flex items-center space-x-3 text-sm text-gray-500;
}

.meta-text {
  @apply text-xs;
}

.report-actions {
  @apply flex items-center space-x-2;
}

.inspection-form {
  @apply space-y-4;
}
</style>