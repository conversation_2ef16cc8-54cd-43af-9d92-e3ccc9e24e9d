import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Homepage',
    component: () => import('@/views/Homepage.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/shared-materials',
    name: 'SharedMaterials',
    component: () => import('@/views/SharedMaterials.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/shared-materials/:id',
    name: 'SharedMaterialDetail',
    component: () => import('@/views/SharedMaterialDetail.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Registration',
    component: () => import('@/views/Registration.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/app',
    name: 'MainApp',
    component: () => import('@/views/MainApp.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'workspace',
        name: 'Workspace',
        component: () => import('@/views/Workspace.vue')
      },
      {
        path: 'marketplace',
        name: 'Marketplace', 
        component: () => import('@/views/Marketplace.vue')
      },
      {
        path: 'orders',
        name: 'Orders',
        component: () => import('@/views/Orders.vue')
      },
      {
        path: 'inventory',
        name: 'Inventory',
        component: () => import('@/views/Inventory.vue')
      },
      {
        path: 'maintenance',
        name: 'Maintenance',
        component: () => import('@/views/Maintenance.vue')
      },
      {
        path: 'quality',
        name: 'Quality',
        component: () => import('@/views/Quality.vue')
      },
      {
        path: 'analytics',
        name: 'Analytics',
        component: () => import('@/views/Analytics.vue')
      },
      {
        path: 'notifications',
        name: 'Notifications',
        component: () => import('@/views/NotificationManagement.vue')
      },
      {
        path: 'demands',
        name: 'DemandManagement',
        component: () => import('@/views/DemandManagement.vue')
      },
      // 管理员专用路由
      {
        path: 'admin',
        name: 'AdminPanel',
        component: () => import('@/views/admin/AdminLayout.vue'),
        meta: { requiresPermission: 3 }, // 需要管理员权限
        children: [
          {
            path: 'permissions',
            name: 'PermissionManagement', 
            component: () => import('@/views/admin/PermissionManagement.vue')
          },
          {
            path: 'roles',
            name: 'RoleManagement',
            component: () => import('@/views/admin/RoleManagement.vue')
          },
          {
            path: 'audit',
            name: 'PermissionAudit',
            component: () => import('@/views/admin/PermissionAudit.vue')
          },
          {
            path: 'temp-permissions',
            name: 'TempPermissions',
            component: () => import('@/views/admin/TempPermissions.vue')
          },
          {
            path: 'monitoring',
            name: 'SystemMonitoring',
            component: () => import('@/views/admin/SystemMonitoring.vue')
          },
          {
            path: 'data',
            name: 'DataManagement',
            component: () => import('@/views/admin/DataManagement.vue'),
            meta: { requiresPermission: 4 } // 仅超级管理员
          },
          {
            path: 'config/app',
            name: 'AppConfig',
            component: () => import('@/views/admin/config/AppConfig.vue')
          },
          {
            path: 'config/database',
            name: 'DatabaseConfig',
            component: () => import('@/views/admin/config/DatabaseConfig.vue'),
            meta: { requiresPermission: 4 } // 仅超级管理员
          },
          {
            path: 'config/api',
            name: 'ApiConfig',
            component: () => import('@/views/admin/config/ApiConfig.vue')
          },
          {
            path: 'config/security',
            name: 'SecurityConfig',
            component: () => import('@/views/admin/config/SecurityConfig.vue')
          },
          {
            path: 'data-recovery',
            name: 'DataRecovery',
            component: () => import('@/views/admin/DataRecovery.vue'),
            meta: { requiresPermission: 4 }
          },
          {
            path: 'security/reports',
            name: 'SecurityReports',
            component: () => import('@/views/admin/SecurityReports.vue')
          },
          {
            path: 'audit-logs',
            name: 'AuditLogs',
            component: () => import('@/views/admin/AuditLogs.vue')
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 调试信息
  console.log('🛡️ 路由守卫检查:', {
    to: to.path,
    requiresAuth: to.meta.requiresAuth,
    requiresPermission: to.meta.requiresPermission,
    isAuthenticated: authStore.isAuthenticated,
    hasToken: !!authStore.token,
    user: authStore.user?.username,
    permissionLevel: authStore.user?.permission_level
  })
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    console.log('❌ 路由守卫拦截: 需要认证但用户未登录，跳转到登录页')
    next('/login')
    return
  }
  
  // 检查权限等级
  if (to.meta.requiresPermission && authStore.user?.permission_level < to.meta.requiresPermission) {
    console.log('❌ 路由守卫拦截: 权限不足，需要权限等级', to.meta.requiresPermission)
    next('/app/workspace') // 跳转到工作台
    return
  }
  
  console.log('✅ 路由守卫通过')
  next()
})

export default router