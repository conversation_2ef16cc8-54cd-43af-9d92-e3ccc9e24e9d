import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'

import App from './App.vue'
import router from './router'

// 导入图标统一管理
import { iconMap } from './icons'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 全局注册图标组件
Object.keys(iconMap).forEach(key => {
  app.component(key, iconMap[key])
})

app.mount('#app')