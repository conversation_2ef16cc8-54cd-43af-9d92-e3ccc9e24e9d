@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* 现代化卡片样式 */
  .modern-card {
    @apply bg-white border border-gray-200 rounded-xl shadow-lg transition-all duration-300 ease-in-out;
  }
  
  .modern-card:hover {
    @apply shadow-xl transform -translate-y-1 border-blue-200;
  }
  
  /* 其他通用组件样式 */
  .section-title {
    @apply text-xl font-semibold text-gray-800 mb-4;
  }
  
  .action-card {
    @apply bg-white border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-gray-300;
  }
  
  .action-icon {
    @apply w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center mb-3;
  }
  
  .action-title {
    @apply font-semibold text-gray-800 text-sm mb-1;
  }
  
  .action-desc {
    @apply text-gray-600 text-xs;
  }
  
  .quick-actions-grid {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4;
  }
  
  .workspace-content {
    @apply space-y-6 p-6;
  }
  
  .welcome-section {
    @apply rounded-2xl p-8 mb-8;
  }
  
  .quick-actions-section,
  .dashboard-section,
  .recent-activities-section,
  .role-specific-section,
  .aog-section {
    @apply space-y-4;
  }
  
  .admin-card {
    @apply modern-card p-6 text-center;
  }
}

/* 基础变量 */
:root {
  --primary-color: #3B82F6;
  --primary-dark: #1D4ED8;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  --text-dark: #1F2937;
  --text-gray: #6B7280;
  --bg-gray: #F9FAFB;
  --border-gray: #E5E7EB;
}

/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-gray);
  color: var(--text-dark);
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

/* 现代化按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-gray);
  transition: all 0.2s ease-in-out;
}

.card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}


.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-gray);
}

.card-body {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-dark);
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-gray);
}

.table th {
  background: var(--bg-gray);
  font-weight: 600;
  color: var(--text-dark);
}

.table tbody tr:hover {
  background: var(--bg-gray);
}

/* 状态标签 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.badge-primary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-gray);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 布局样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* 文字样式 */
.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-gray-500 {
  color: var(--text-gray);
}

.text-gray-900 {
  color: var(--text-dark);
}

/* 间距 */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.m-4 {
  margin: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--border-gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-gray);
}