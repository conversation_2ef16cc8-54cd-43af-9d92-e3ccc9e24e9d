# Lucide Vue Next 到 Element Plus 图标映射表

## 基础操作图标
```
search -> Search
plus -> Plus
trash -> Delete
trash-2 -> Delete
edit -> Edit
edit-2 -> Edit
edit-3 -> Edit
refresh-cw -> Refresh
refresh-ccw -> Refresh
download -> Download
upload -> Upload
x -> Close
check -> Check
timer -> Timer
clock -> Clock
chevron-down -> ArrowDown
arrow-down -> ArrowDown
chevron-up -> ArrowUp
arrow-up -> ArrowUp
chevron-left -> ArrowLeft
arrow-left -> ArrowLeft
chevron-right -> ArrowRight
arrow-right -> ArrowRight
```

## 状态指示图标
```
alert-triangle -> Warning
triangle-alert -> Warning
info -> InfoFilled
alert-circle -> InfoFilled
check-circle -> SuccessFilled
check-circle-2 -> CircleCheck
x-circle -> CircleClose
circle-x -> CircleClose
```

## 功能图标
```
star -> Star
wrench -> Tools
tool -> Tools
settings -> Setting
gear -> Setting
cog -> Setting
file -> Document
file-text -> Document
folder -> Folder
folder-open -> FolderOpened
files -> Files
image -> Picture
picture -> Picture
```

## 导航图标
```
home -> House
house -> House
shopping-cart -> ShoppingCart
cart -> ShoppingCart
user -> User
user-check -> UserFilled
menu -> Menu
more-horizontal -> More
more-vertical -> More
```

## 业务相关图标
```
package -> Goods
box -> Box
truck -> Van
map-pin -> Position
monitor -> Monitor
bar-chart -> DataAnalysis
trending-up -> TrendCharts
pie-chart -> PieChart
```

## 通信图标
```
message-circle -> Message
mail -> Message
bell -> Bell
phone -> Phone
message-square -> ChatDotRound
```

## 其他常用图标
```
eye -> View
eye-off -> Hide
lock -> Lock
unlock -> Unlock
key -> Key
user-circle -> Avatar
calendar -> Calendar
stopwatch -> Stopwatch
```

## 管理和配置图标
```
link -> Connection
activity -> Operation
dollar-sign -> Coin
coins -> Coin
check-square -> SemiSelect
check-circle -> Finished
x-square -> Failed
loader -> Loading
trending-up -> Promotion
headphones -> Service
settings -> SetUp
```

## 数据分析图标
```
layout-dashboard -> DataBoard
grid-3x3 -> Grid
list -> List
layers -> Management
server -> Platform
bar-chart-2 -> Histogram
```

## 方向指示图标
```
trending-up -> Rank
corner-up-right -> TopRight
corner-down-left -> BottomLeft
corner-up-left -> TopLeft
corner-down-right -> BottomRight
```

## 状态指示图标
```
check-square -> Select
alert-triangle -> WarnTriangleFilled
plus-circle -> CirclePlus
minus -> Remove
x -> CloseBold
minus -> Minus
```

## 扩展映射（航空相关）
```
plane -> Van
navigation -> Position
compass -> Position
line-chart -> TrendCharts
bar-chart-3 -> Histogram
analytics -> DataAnalysis
alert -> Warning
help-circle -> InfoFilled
shield -> Lock
shield-check -> Finished
filter -> Setting
sort -> Rank
flag -> Star
video -> Monitor
camera -> Picture
user-plus -> CirclePlus
users -> UserFilled
heart -> Star
bookmark -> Star
tag -> Star
wifi -> Connection
database -> DataBoard
hard-drive -> Monitor
calendar-days -> Calendar
calendar-clock -> Timer
shopping-bag -> ShoppingCart
credit-card -> Coin
clipboard -> Document
book -> Document
notebook -> Files
```

## 使用说明

1. **安装映射工具**：引入 `iconMapping.js` 文件
2. **使用映射函数**：
   ```javascript
   import { getElementIcon } from '@/utils/iconMapping'
   
   // 从Lucide图标名获取Element Plus图标名
   const elementIconName = getElementIcon('search') // 返回 'Search'
   ```
3. **批量转换**：
   ```javascript
   import { convertLucideToElement } from '@/utils/iconMapping'
   
   const lucideIcons = ['search', 'plus', 'edit']
   const elementIcons = convertLucideToElement(lucideIcons)
   // 返回 ['Search', 'Plus', 'Edit']
   ```

## 注意事项

1. 如果没有对应的Element Plus图标，会使用最相近的替代方案
2. 部分Lucide图标可能映射到同一个Element Plus图标
3. 建议优先使用Element Plus原生图标以保持UI一致性
4. 映射表支持双向查询（Lucide ↔ Element Plus）