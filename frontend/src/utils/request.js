import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data
  },
  async (error) => {
    const authStore = useAuthStore()

    if (error.response?.status === 401) {
      // 检查是否是刷新令牌请求本身失败
      if (error.config?.url?.includes('/auth/refresh')) {
        // 刷新令牌请求失败，直接清理登录状态并跳转
        await authStore.logout()
        window.location.href = '/login'
        return Promise.reject(error)
      }

      // 检查是否是需求匹配相关的API
      const isDemandMatchAPI = error.config?.url?.includes('/demands/') && 
                              (error.config?.url?.includes('/match') || error.config?.url?.includes('/rematch'))
      
      if (isDemandMatchAPI) {
        // 对于需求匹配API，不强制登出，让组件自己处理
        return Promise.reject(error)
      }

      // 检查是否已经在刷新过程中，避免无限循环
      if (error.config?._retry) {
        // 已经重试过了，直接清理登录状态并跳转
        await authStore.logout()
        window.location.href = '/login'
        return Promise.reject(error)
      }

      // 检查是否有refresh token
      if (!authStore.refreshToken) {
        await authStore.logout()
        window.location.href = '/login'
        return Promise.reject(error)
      }

      // 标记这个请求已经重试过
      error.config._retry = true

      try {
        // Token过期，尝试刷新
        const refreshSuccess = await authStore.refreshTokens()
        if (refreshSuccess) {
          // 更新请求头中的token
          error.config.headers.Authorization = `Bearer ${authStore.token}`
          // 重试原请求
          return request(error.config)
        } else {
          // 刷新失败，清理登录状态并跳转
          await authStore.logout()
          window.location.href = '/login'
          return Promise.reject(error)
        }
      } catch (refreshError) {
        await authStore.logout()
        window.location.href = '/login'
        return Promise.reject(error)
      }
    }

    // 显示错误消息（排除401错误，因为已经在上面处理了）
    if (error.response?.status !== 401) {
      const message = error.response?.data?.message || '请求失败'
      ElMessage.error(message)
    }
    
    return Promise.reject(error)
  }
)

// 封装常用的HTTP方法
export const get = (url, params) => {
  return request({
    method: 'get',
    url,
    params
  })
}

export const post = (url, data) => {
  return request({
    method: 'post',
    url,
    data
  })
}

export const put = (url, data) => {
  return request({
    method: 'put',
    url,
    data
  })
}

export const del = (url, params) => {
  return request({
    method: 'delete',
    url,
    params
  })
}

export default request