/**
 * Lucide Vue Next 图标到 Element Plus 图标映射表
 * 用于在项目中统一图标使用，从lucide图标名映射到Element Plus图标名
 */

export const lucideToElementMapping = {
  // ============ 基础操作图标 ============
  'search' -> 'Search',
  'plus' -> 'Plus',
  'trash' -> 'Delete',
  'trash-2' -> 'Delete',
  'edit' -> 'Edit',
  'edit-2' -> 'Edit',
  'edit-3' -> 'Edit',
  'refresh-cw' -> 'Refresh',
  'refresh-ccw' -> 'Refresh',
  'rotate-cw' -> 'Refresh',
  'download' -> 'Download',
  'upload' -> 'Upload',
  'x' -> 'Close',
  'check' -> 'Check',
  'timer' -> 'Timer',
  'clock' -> 'Clock',
  'chevron-down' -> 'ArrowDown',
  'arrow-down' -> 'ArrowDown',
  'chevron-up' -> 'ArrowUp',
  'arrow-up' -> 'ArrowUp',
  'chevron-left' -> 'ArrowLeft',
  'arrow-left' -> 'ArrowLeft',
  'chevron-right' -> 'ArrowRight',
  'arrow-right' -> 'ArrowRight',

  // ============ 状态指示图标 ============
  'alert-triangle' -> 'Warning',
  'triangle-alert' -> 'Warning',
  'info' -> 'InfoFilled',
  'alert-circle' -> 'InfoFilled',
  'check-circle' -> 'SuccessFilled',
  'check-circle-2' -> 'CircleCheck',
  'x-circle' -> 'CircleClose',
  'circle-x' -> 'CircleClose',

  // ============ 功能图标 ============
  'star' -> 'Star',
  'wrench' -> 'Tools',
  'tool' -> 'Tools',
  'settings' -> 'Setting',
  'gear' -> 'Setting',
  'cog' -> 'Setting',
  'file' -> 'Document',
  'file-text' -> 'Document',
  'folder' -> 'Folder',
  'folder-open' -> 'FolderOpened',
  'files' -> 'Files',
  'image' -> 'Picture',
  'picture' -> 'Picture',

  // ============ 导航图标 ============
  'home' -> 'House',
  'house' -> 'House',
  'shopping-cart' -> 'ShoppingCart',
  'cart' -> 'ShoppingCart',
  'user' -> 'User',
  'user-check' -> 'UserFilled',
  'menu' -> 'Menu',
  'more-horizontal' -> 'More',
  'more-vertical' -> 'More',

  // ============ 业务相关图标 ============
  'package' -> 'Goods',
  'box' -> 'Box',
  'truck' -> 'Van',
  'map-pin' -> 'Position',
  'monitor' -> 'Monitor',
  'bar-chart' -> 'DataAnalysis',
  'trending-up' -> 'TrendCharts',
  'pie-chart' -> 'PieChart',

  // ============ 通信图标 ============
  'message-circle' -> 'Message',
  'mail' -> 'Message',
  'bell' -> 'Bell',
  'phone' -> 'Phone',
  'message-square' -> 'ChatDotRound',

  // ============ 其他常用图标 ============
  'eye' -> 'View',
  'eye-off' -> 'Hide',
  'lock' -> 'Lock',
  'unlock' -> 'Unlock',
  'key' -> 'Key',
  'user-circle' -> 'Avatar',
  'calendar' -> 'Calendar',
  'stopwatch' -> 'Stopwatch',
  'clock-3' -> 'Clock',

  // ============ 管理和配置图标 ============
  'link' -> 'Connection',
  'activity' -> 'Operation',
  'dollar-sign' -> 'Coin',
  'coins' -> 'Coin',
  'check-square' -> 'SemiSelect',
  'check-circle' -> 'Finished',
  'x-square' -> 'Failed',
  'loader' -> 'Loading',
  'trending-up' -> 'Promotion',
  'headphones' -> 'Service',
  'settings' -> 'SetUp',

  // ============ 数据分析图标 ============
  'layout-dashboard' -> 'DataBoard',
  'grid-3x3' -> 'Grid',
  'list' -> 'List',
  'layers' -> 'Management',
  'server' -> 'Platform',
  'bar-chart-2' -> 'Histogram',

  // ============ 方向指示图标 ============
  'trending-up' -> 'Rank',
  'corner-up-right' -> 'TopRight',
  'corner-down-left' -> 'BottomLeft',
  'corner-up-left' -> 'TopLeft',
  'corner-down-right' -> 'BottomRight',

  // ============ 状态指示图标 ============
  'check-square' -> 'Select',
  'alert-triangle' -> 'WarnTriangleFilled',
  'plus-circle' -> 'CirclePlus',
  'minus' -> 'Remove',
  'x' -> 'CloseBold',
  'minus' -> 'Minus',

  // ============ 扩展映射 ============
  // 航空相关
  'plane' -> 'Van', // 飞机图标映射到运输车
  'navigation' -> 'Position',
  'compass' -> 'Position',
  
  // 数据分析
  'line-chart' -> 'TrendCharts',
  'bar-chart-3' -> 'Histogram',
  'analytics' -> 'DataAnalysis',
  
  // 状态
  'alert' -> 'Warning',
  'help-circle' -> 'InfoFilled',
  'shield' -> 'Lock',
  'shield-check' -> 'Finished',
  
  // 操作
  'filter' -> 'Setting',
  'sort' -> 'Rank',
  'flag' -> 'Star',
  
  // 媒体
  'video' -> 'Monitor',
  'camera' -> 'Picture',
  
  // 用户相关
  'user-plus' -> 'CirclePlus',
  'users' -> 'UserFilled',
  
  // 通用
  'heart' -> 'Star',
  'bookmark' -> 'Star',
  'tag' -> 'Star',
  
  // 系统
  'wifi' -> 'Connection',
  'database' -> 'DataBoard',
  'hard-drive' -> 'Monitor',
  
  // 时间
  'calendar-days' -> 'Calendar',
  'calendar-clock' -> 'Timer',
  
  // 购物
  'shopping-bag' -> 'ShoppingCart',
  'credit-card' -> 'Coin',
  
  // 文档
  'clipboard' -> 'Document',
  'book' -> 'Document',
  'notebook' -> 'Files'
}

/**
 * 反向映射：Element Plus图标名到Lucide图标名
 */
export const elementToLucideMapping = {
  // 基于上面的映射生成反向映射（取第一个匹配项）
  'Search' -> 'search',
  'Plus' -> 'plus',
  'Delete' -> 'trash',
  'Edit' -> 'edit',
  'Refresh' -> 'refresh-cw',
  'Download' -> 'download',
  'Upload' -> 'upload',
  'Close' -> 'x',
  'Check' -> 'check',
  'Timer' -> 'timer',
  'Clock' -> 'clock',
  'ArrowDown' -> 'chevron-down',
  'ArrowUp' -> 'chevron-up',
  'ArrowLeft' -> 'chevron-left',
  'ArrowRight' -> 'chevron-right',
  'Warning' -> 'alert-triangle',
  'InfoFilled' -> 'info',
  'SuccessFilled' -> 'check-circle',
  'CircleCheck' -> 'check-circle-2',
  'CircleClose' -> 'x-circle',
  'Star' -> 'star',
  'Tools' -> 'wrench',
  'Setting' -> 'settings',
  'Document' -> 'file',
  'Folder' -> 'folder',
  'FolderOpened' -> 'folder-open',
  'Files' -> 'files',
  'Picture' -> 'image',
  'House' -> 'home',
  'ShoppingCart' -> 'shopping-cart',
  'User' -> 'user',
  'UserFilled' -> 'user-check',
  'Menu' -> 'menu',
  'More' -> 'more-horizontal',
  'Goods' -> 'package',
  'Box' -> 'box',
  'Van' -> 'truck',
  'Position' -> 'map-pin',
  'Monitor' -> 'monitor',
  'DataAnalysis' -> 'bar-chart',
  'TrendCharts' -> 'trending-up',
  'PieChart' -> 'pie-chart',
  'Message' -> 'message-circle',
  'Bell' -> 'bell',
  'Phone' -> 'phone',
  'ChatDotRound' -> 'message-square',
  'View' -> 'eye',
  'Hide' -> 'eye-off',
  'Lock' -> 'lock',
  'Unlock' -> 'unlock',
  'Key' -> 'key',
  'Avatar' -> 'user-circle',
  'Calendar' -> 'calendar',
  'Stopwatch' -> 'stopwatch',
  'Connection' -> 'link',
  'Operation' -> 'activity',
  'Coin' -> 'dollar-sign',
  'SemiSelect' -> 'check-square',
  'Finished' -> 'check-circle',
  'Failed' -> 'x-square',
  'Loading' -> 'loader',
  'Promotion' -> 'trending-up',
  'Service' -> 'headphones',
  'SetUp' -> 'settings',
  'DataBoard' -> 'layout-dashboard',
  'Grid' -> 'grid-3x3',
  'List' -> 'list',
  'Management' -> 'layers',
  'Platform' -> 'server',
  'Histogram' -> 'bar-chart-2',
  'Rank' -> 'trending-up',
  'TopRight' -> 'corner-up-right',
  'BottomLeft' -> 'corner-down-left',
  'TopLeft' -> 'corner-up-left',
  'BottomRight' -> 'corner-down-right',
  'Select' -> 'check-square',
  'WarnTriangleFilled' -> 'alert-triangle',
  'CirclePlus' -> 'plus-circle',
  'Remove' -> 'minus',
  'CloseBold' -> 'x',
  'Minus' -> 'minus'
}

/**
 * 获取Element Plus图标名称（从Lucide图标名）
 * @param {string} lucideIconName - Lucide图标名称
 * @returns {string} Element Plus图标名称，如果没有映射则返回原名称
 */
export function getElementIcon(lucideIconName) {
  return lucideToElementMapping[lucideIconName] || lucideIconName
}

/**
 * 获取Lucide图标名称（从Element Plus图标名）
 * @param {string} elementIconName - Element Plus图标名称
 * @returns {string} Lucide图标名称，如果没有映射则返回原名称
 */
export function getLucideIcon(elementIconName) {
  return elementToLucideMapping[elementIconName] || elementIconName
}

/**
 * 批量转换Lucide图标名称为Element Plus图标名称
 * @param {string[]} lucideIcons - Lucide图标名称数组
 * @returns {string[]} Element Plus图标名称数组
 */
export function convertLucideToElement(lucideIcons) {
  return lucideIcons.map(icon => getElementIcon(icon))
}

/**
 * 检查是否支持该图标
 * @param {string} iconName - 图标名称
 * @param {'lucide'|'element'} type - 图标类型
 * @returns {boolean} 是否支持该图标
 */
export function isIconSupported(iconName, type = 'lucide') {
  if (type === 'lucide') {
    return iconName in lucideToElementMapping
  } else {
    return iconName in elementToLucideMapping
  }
}

/**
 * 获取所有支持的图标映射
 * @returns {Object} 包含所有映射关系的对象
 */
export function getAllMappings() {
  return {
    lucideToElement: lucideToElementMapping,
    elementToLucide: elementToLucideMapping
  }
}

export default {
  lucideToElementMapping,
  elementToLucideMapping,
  getElementIcon,
  getLucideIcon,
  convertLucideToElement,
  isIconSupported,
  getAllMappings
}