/**
 * 图标统一管理文件
 * 统一导入和导出项目中使用的所有Element Plus图标
 * 便于全局注册和按需使用
 */

// 从 @element-plus/icons-vue 导入所有需要的图标
import {
  // 基础操作图标
  Search,
  Plus,
  Delete,
  Edit,
  Refresh,
  Download,
  Upload,
  Close,
  Check,
  Timer,
  Clock,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  
  // 状态图标
  Warning,
  InfoFilled,
  SuccessFilled,
  CircleCheck,
  CircleClose,
  
  // 功能图标
  Star,
  Tools,
  Setting,
  Document,
  Folder,
  FolderOpened,
  Files,
  Picture,
  
  // 导航图标
  House,
  ShoppingCart,
  User,
  UserFilled,
  Menu,
  More,
  
  // 业务相关图标
  Goods,
  Box,
  Van,
  Position,
  Monitor,
  DataAnalysis,
  TrendCharts,
  PieChart,
  
  // 通信图标
  Message,
  Bell,
  Phone,
  ChatDotRound,
  
  // 其他常用图标
  View,
  Hide,
  Lock,
  Unlock,
  Key,
  Avatar,
  Calendar,
  Stopwatch,
  
  // 管理和配置图标
  Connection,
  Operation,
  Coin,
  SemiSelect,
  Finished,
  Failed,
  Loading,
  Promotion,
  Service,
  SetUp,
  
  // 数据分析图标
  DataBoard,
  Grid,
  List,
  Management,
  Platform,
  Histogram,
  
  // 业务操作图标
  Rank,
  TopRight,
  Bottom,
  Top,
  Right,
  BottomLeft,
  TopLeft,
  BottomRight,
  
  // 状态指示图标
  Select,
  WarnTriangleFilled,
  CirclePlus,
  Remove,
  CloseBold,
  Minus
} from '@element-plus/icons-vue'

// 导出所有图标
export {
  // 基础操作图标
  Search,
  Plus,
  Delete,
  Edit,
  Refresh,
  Download,
  Upload,
  Close,
  Check,
  Timer,
  Clock,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  
  // 状态图标
  Warning,
  InfoFilled,
  SuccessFilled,
  CircleCheck,
  CircleClose,
  
  // 功能图标
  Star,
  Tools,
  Setting,
  Document,
  Folder,
  FolderOpened,
  Files,
  Picture,
  
  // 导航图标
  House,
  ShoppingCart,
  User,
  UserFilled,
  Menu,
  More,
  
  // 业务相关图标
  Goods,
  Box,
  Van,
  Position,
  Monitor,
  DataAnalysis,
  TrendCharts,
  PieChart,
  
  // 通信图标
  Message,
  Bell,
  Phone,
  ChatDotRound,
  
  // 其他常用图标
  View,
  Hide,
  Lock,
  Unlock,
  Key,
  Avatar,
  Calendar,
  Stopwatch,
  
  // 管理和配置图标
  Connection,
  Operation,
  Coin,
  SemiSelect,
  Finished,
  Failed,
  Loading,
  Promotion,
  Service,
  SetUp,
  
  // 数据分析图标
  DataBoard,
  Grid,
  List,
  Management,
  Platform,
  Histogram,
  
  // 业务操作图标
  Rank,
  TopRight,
  Bottom,
  Top,
  Right,
  BottomLeft,
  TopLeft,
  BottomRight,
  
  // 状态指示图标
  Select,
  WarnTriangleFilled,
  CirclePlus,
  Remove,
  CloseBold,
  Minus
}

// 导出图标映射对象，便于动态使用
export const iconMap = {
  // 基础操作图标
  Search,
  Plus,
  Delete,
  Edit,
  Refresh,
  Download,
  Upload,
  Close,
  Check,
  Timer,
  Clock,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  
  // 状态图标
  Warning,
  InfoFilled,
  SuccessFilled,
  CircleCheck,
  CircleClose,
  
  // 功能图标
  Star,
  Tools,
  Setting,
  Document,
  Folder,
  FolderOpened,
  Files,
  Picture,
  
  // 导航图标
  House,
  ShoppingCart,
  User,
  UserFilled,
  Menu,
  More,
  
  // 业务相关图标
  Goods,
  Box,
  Van,
  Position,
  Monitor,
  DataAnalysis,
  TrendCharts,
  PieChart,
  
  // 通信图标
  Message,
  Bell,
  Phone,
  ChatDotRound,
  
  // 其他常用图标
  View,
  Hide,
  Lock,
  Unlock,
  Key,
  Avatar,
  Calendar,
  Stopwatch,
  
  // 管理和配置图标
  Connection,
  Operation,
  Coin,
  SemiSelect,
  Finished,
  Failed,
  Loading,
  Promotion,
  Service,
  SetUp,
  
  // 数据分析图标
  DataBoard,
  Grid,
  List,
  Management,
  Platform,
  Histogram,
  
  // 业务操作图标
  Rank,
  TopRight,
  Bottom,
  Top,
  Right,
  BottomLeft,
  TopLeft,
  BottomRight,
  
  // 状态指示图标
  Select,
  WarnTriangleFilled,
  CirclePlus,
  Remove,
  CloseBold,
  Minus
}

// 导出图标名称列表，便于验证
export const iconNames = Object.keys(iconMap)

// 导出默认图标集合，包含项目中最常用的图标
export const commonIcons = [
  'Search',
  'Plus',
  'Delete',
  'Edit',
  'Check',
  'Close',
  'Star',
  'Tools',
  'Warning',
  'House',
  'ShoppingCart',
  'Document',
  'Timer',
  'Clock',
  'ArrowDown'
]