import { rolesApi } from '@/api/roles'
import { permissionsApi } from '@/api/permissions'

/**
 * 动态角色配置服务
 * 负责从后端加载角色配置并构建前端使用的配置对象
 */
class RoleConfigService {
  constructor() {
    this.roleConfigs = {}
    this.isLoaded = false
  }

  /**
   * 加载角色配置
   * 从后端API获取角色信息并构建配置对象
   */
  async loadRoleConfigs(usePublicApi = false) {
    let response
    
    if (usePublicApi) {
      // 用于注册页面，使用公开API
      response = await rolesApi.getPublicRoles()
    } else {
      // 用于管理页面，使用需要认证的API
      response = await rolesApi.getAllRoles({ include_inactive: false })
    }
    
    if (response.success && response.body.roles) {
      this.roleConfigs = this.buildRoleConfigs(response.body.roles)
      this.isLoaded = true
      return this.roleConfigs
    } else {
      throw new Error('Failed to load role configurations from server')
    }
  }

  /**
   * 构建角色配置对象
   * 将后端角色数据转换为前端使用的配置格式
   */
  buildRoleConfigs(roles) {
    const configs = {}
    
    roles.forEach(role => {
      configs[role.role_code] = {
        name: role.display_name,
        description: role.description,
        category: role.category,
        businessType: role.business_type,
        theme: {
          primaryColor: role.theme_color || this.getDefaultThemeColor(role.role_code),
          gradientFrom: role.theme_color || this.getDefaultThemeColor(role.role_code),
          gradientTo: this.getDarkerColor(role.theme_color || this.getDefaultThemeColor(role.role_code))
        },
        icon: role.icon_name || this.getDefaultIcon(role.role_code),
        navigation: this.buildNavigation(role),
        quickActions: this.buildQuickActions(role),
        dashboard: this.buildDashboard(role),
        isActive: role.is_active,
        sortOrder: role.sort_order || 0
      }
    })
    
    return configs
  }

  /**
   * 获取默认配置
   * 当API加载失败时使用的默认配置
   */
  getDefaultConfigs() {
    return {
      supplier: {
        name: '供应商',
        description: '航材制造商，专注生产和销售',
        category: 'external',
        businessType: 'sell_only',
        theme: {
          primaryColor: '#10B981',
          gradientFrom: '#10B981',
          gradientTo: '#047857'
        },
        icon: 'Factory',
        navigation: this.getDefaultNavigation('supplier'),
        quickActions: this.getDefaultQuickActions('supplier'),
        dashboard: this.getDefaultDashboard('supplier')
      },
      distributor: {
        name: '分销商',
        description: '航材贸易商，连接供需两端',
        category: 'external',
        businessType: 'buy_and_sell',
        theme: {
          primaryColor: '#3B82F6',
          gradientFrom: '#3B82F6',
          gradientTo: '#1D4ED8'
        },
        icon: 'TruckDelivery',
        navigation: this.getDefaultNavigation('distributor'),
        quickActions: this.getDefaultQuickActions('distributor'),
        dashboard: this.getDefaultDashboard('distributor')
      },
      airline_buyer: {
        name: '航空公司',
        description: '航材需求方，专注采购和使用',
        category: 'external',
        businessType: 'buy_only',
        theme: {
          primaryColor: '#8B5CF6',
          gradientFrom: '#8B5CF6',
          gradientTo: '#5B21B6'
        },
        icon: 'Airplane',
        navigation: this.getDefaultNavigation('airline_buyer'),
        quickActions: this.getDefaultQuickActions('airline_buyer'),
        dashboard: this.getDefaultDashboard('airline_buyer')
      },
      platform_staff: {
        name: '平台员工',
        description: '平台运营管理人员',
        category: 'internal',
        businessType: 'service',
        theme: {
          primaryColor: '#F59E0B',
          gradientFrom: '#F59E0B',
          gradientTo: '#D97706'
        },
        icon: 'UserGroup',
        navigation: this.getDefaultNavigation('platform_staff'),
        quickActions: this.getDefaultQuickActions('platform_staff'),
        dashboard: this.getDefaultDashboard('platform_staff')
      },
      maintenance_engineer: {
        name: '维修工程师',
        description: '提供技术支持和维修服务',
        category: 'internal',
        businessType: 'service',
        theme: {
          primaryColor: '#EF4444',
          gradientFrom: '#EF4444',
          gradientTo: '#DC2626'
        },
        icon: 'Wrench',
        navigation: this.getDefaultNavigation('maintenance_engineer'),
        quickActions: this.getDefaultQuickActions('maintenance_engineer'),
        dashboard: this.getDefaultDashboard('maintenance_engineer')
      },
      logistics_specialist: {
        name: '物流专员',
        description: '负责配送跟踪和物流管理',
        category: 'internal',
        businessType: 'service',
        theme: {
          primaryColor: '#06B6D4',
          gradientFrom: '#06B6D4',
          gradientTo: '#0891B2'
        },
        icon: 'Truck',
        navigation: this.getDefaultNavigation('logistics_specialist'),
        quickActions: this.getDefaultQuickActions('logistics_specialist'),
        dashboard: this.getDefaultDashboard('logistics_specialist')
      },
      admin: {
        name: '系统管理员',
        description: '系统配置和用户管理',
        category: 'internal',
        businessType: 'service',
        theme: {
          primaryColor: '#6366F1',
          gradientFrom: '#6366F1',
          gradientTo: '#4F46E5'
        },
        icon: 'Settings',
        navigation: this.getDefaultNavigation('admin'),
        quickActions: this.getDefaultQuickActions('admin'),
        dashboard: this.getDefaultDashboard('admin')
      }
    }
  }

  /**
   * 获取角色配置
   * 返回指定角色的配置信息
   */
  async getRoleConfig(roleCode, usePublicApi = false) {
    if (!this.isLoaded) {
      await this.loadRoleConfigs(usePublicApi)
    }
    const config = this.roleConfigs[roleCode]
    if (!config) {
      throw new Error(`Role configuration not found for: ${roleCode}`)
    }
    return config
  }

  /**
   * 获取所有角色配置
   */
  async getAllRoleConfigs(usePublicApi = false) {
    if (!this.isLoaded) {
      await this.loadRoleConfigs(usePublicApi)
    }
    return this.roleConfigs
  }

  /**
   * 获取外部用户角色
   */
  async getExternalRoles(usePublicApi = false) {
    const configs = await this.getAllRoleConfigs(usePublicApi)
    const externalRoles = Object.entries(configs)
      .filter(([, config]) => config.category === 'external')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
    
    if (Object.keys(externalRoles).length === 0) {
      throw new Error('No external roles found in configuration')
    }
    return externalRoles
  }

  /**
   * 获取内部员工角色
   */
  async getInternalRoles(usePublicApi = false) {
    const configs = await this.getAllRoleConfigs(usePublicApi)
    const internalRoles = Object.entries(configs)
      .filter(([, config]) => config.category === 'internal')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
    
    if (Object.keys(internalRoles).length === 0) {
      throw new Error('No internal roles found in configuration')
    }
    return internalRoles
  }

  /**
   * 获取默认主题颜色
   */
  getDefaultThemeColor(roleCode) {
    const colorMap = {
      supplier: '#10B981',
      distributor: '#3B82F6',
      airline_buyer: '#8B5CF6',
      platform_staff: '#F59E0B',
      maintenance_engineer: '#EF4444',
      logistics_specialist: '#06B6D4',
      admin: '#6366F1'
    }
    return colorMap[roleCode] || '#6366F1'
  }

  /**
   * 获取默认图标
   */
  getDefaultIcon(roleCode) {
    const iconMap = {
      supplier: 'Factory',
      distributor: 'TruckDelivery',
      airline_buyer: 'Airplane',
      platform_staff: 'UserGroup',
      maintenance_engineer: 'Wrench',
      logistics_specialist: 'Truck',
      admin: 'Settings'
    }
    return iconMap[roleCode] || 'User'
  }

  /**
   * 获取更深的颜色
   */
  getDarkerColor(color) {
    // 简单的颜色加深算法
    const hex = color.replace('#', '')
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 40)
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 40)
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 40)
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  /**
   * 构建导航配置
   */
  buildNavigation(role) {
    // 根据角色权限构建导航
    const baseNav = [
      { name: '工作台', path: '/workspace', icon: 'Dashboard' }
    ]
    
    // 根据业务类型添加特定导航
    if (role.business_type === 'sell_only' || role.business_type === 'buy_and_sell') {
      baseNav.push({ name: '库存管理', path: '/inventory', icon: 'Package' })
      baseNav.push({ name: '共享管理', path: '/shared-materials', icon: 'Share' })
    }
    
    if (role.business_type === 'buy_only' || role.business_type === 'buy_and_sell') {
      baseNav.push({ name: '需求管理', path: '/demands', icon: 'Search' })
    }
    
    baseNav.push({ name: '订单管理', path: '/orders', icon: 'Document' })
    
    if (role.category === 'internal') {
      baseNav.push({ name: '客户服务', path: '/customer-service', icon: 'Support' })
    }
    
    return baseNav
  }

  /**
   * 构建快捷操作
   */
  buildQuickActions(role) {
    const actions = []
    
    if (role.business_type === 'sell_only' || role.business_type === 'buy_and_sell') {
      actions.push({ name: '发布共享', action: 'publish-shared', icon: 'Plus' })
    }
    
    if (role.business_type === 'buy_only' || role.business_type === 'buy_and_sell') {
      actions.push({ name: '发布需求', action: 'publish-demand', icon: 'Search' })
    }
    
    actions.push({ name: '创建订单', action: 'create-order', icon: 'Document' })
    
    return actions
  }

  /**
   * 构建仪表板配置
   */
  buildDashboard(role) {
    const widgets = ['overview', 'recent-activities']
    
    if (role.business_type === 'sell_only' || role.business_type === 'buy_and_sell') {
      widgets.push('inventory-status', 'sales-analytics')
    }
    
    if (role.business_type === 'buy_only' || role.business_type === 'buy_and_sell') {
      widgets.push('demand-status', 'procurement-analytics')
    }
    
    if (role.category === 'internal') {
      widgets.push('system-status', 'user-analytics')
    }
    
    return { widgets }
  }

  // 默认配置方法（简化实现）
  getDefaultNavigation(roleCode) { return this.buildNavigation({ business_type: 'service', category: 'external' }) }
  getDefaultQuickActions(roleCode) { return this.buildQuickActions({ business_type: 'service' }) }
  getDefaultDashboard(roleCode) { return this.buildDashboard({ business_type: 'service', category: 'external' }) }
}

// 创建单例实例
const roleConfigService = new RoleConfigService()

export default roleConfigService