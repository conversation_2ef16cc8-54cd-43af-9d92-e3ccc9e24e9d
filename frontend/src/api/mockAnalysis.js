/**
 * Mock数据使用分析工具
 * 分析当前Mock数据的使用情况，识别可优化的部分
 * 
 * 功能：
 * - 统计Mock数据大小和分布
 * - 识别重复或冗余数据
 * - 分析API调用模式
 * - 生成优化建议
 */

import { environment } from '@/config/environment.js'

/**
 * Mock数据分析器
 */
export class MockAnalyzer {
  constructor() {
    this.analysisResults = {
      totalSize: 0,
      totalApis: 0,
      dataDistribution: {},
      redundantData: [],
      optimizationSuggestions: [],
      riskAssessment: []
    }
  }
  
  /**
   * 分析Mock管理器
   */
  async analyzeMockManager(mockManager) {
    if (!mockManager) {
      console.warn('[MockAnalyzer] Mock管理器不可用')
      return this.analysisResults
    }
    
    console.log('[MockAnalyzer] 开始分析Mock数据使用情况...')
    
    // 1. 基础统计
    this.analyzeBasicStats(mockManager)
    
    // 2. 数据分布分析
    this.analyzeDataDistribution(mockManager)
    
    // 3. 冗余数据检测
    this.analyzeRedundantData(mockManager)
    
    // 4. 性能影响分析
    this.analyzePerformanceImpact(mockManager)
    
    // 5. 生成优化建议
    this.generateOptimizationSuggestions()
    
    console.log('[MockAnalyzer] 分析完成:', this.analysisResults)
    return this.analysisResults
  }
  
  /**
   * 基础统计分析
   */
  analyzeBasicStats(mockManager) {
    const mockData = mockManager.mockData
    
    this.analysisResults.totalApis = mockData.size
    
    let totalSize = 0
    for (const [apiName, mockItem] of mockData) {
      const dataSize = this.calculateDataSize(mockItem.data)
      totalSize += dataSize
      
      this.analysisResults.dataDistribution[apiName] = {
        size: dataSize,
        enabled: mockItem.enabled,
        description: mockItem.description,
        lastUsed: mockItem.lastUsed || 'never',
        usageCount: mockItem.usageCount || 0
      }
    }
    
    this.analysisResults.totalSize = totalSize
  }
  
  /**
   * 数据分布分析
   */
  analyzeDataDistribution(mockManager) {
    const distribution = this.analysisResults.dataDistribution
    
    // 按大小排序
    const sortedBySize = Object.entries(distribution)
      .sort((a, b) => b[1].size - a[1].size)
      .slice(0, 10) // 前10个最大的API
    
    // 找出大数据量API
    const largeDataApis = sortedBySize.filter(([, data]) => data.size > 10000) // 超过10KB
    
    if (largeDataApis.length > 0) {
      this.analysisResults.optimizationSuggestions.push({
        type: 'large-data',
        priority: 'high',
        title: '大数据量Mock API检测',
        description: `发现${largeDataApis.length}个数据量较大的Mock API`,
        apis: largeDataApis.map(([name]) => name),
        action: '考虑简化这些API的Mock数据或使用懒加载'
      })
    }
  }
  
  /**
   * 冗余数据检测
   */
  analyzeRedundantData(mockManager) {
    const distribution = this.analysisResults.dataDistribution
    
    // 检测未使用的API
    const unusedApis = Object.entries(distribution)
      .filter(([, data]) => data.usageCount === 0 || data.lastUsed === 'never')
      .map(([name]) => name)
    
    if (unusedApis.length > 0) {
      this.analysisResults.redundantData.push({
        type: 'unused-apis',
        count: unusedApis.length,
        apis: unusedApis,
        impact: 'low',
        suggestion: '可以移除这些未使用的Mock API以减少包大小'
      })
    }
    
    // 检测禁用的API
    const disabledApis = Object.entries(distribution)
      .filter(([, data]) => !data.enabled)
      .map(([name]) => name)
    
    if (disabledApis.length > 0) {
      this.analysisResults.redundantData.push({
        type: 'disabled-apis',
        count: disabledApis.length,
        apis: disabledApis,
        impact: 'low',
        suggestion: '可以移除这些已禁用的Mock API'
      })
    }
    
    // 检测重复结构的数据
    this.detectDuplicateStructures(mockManager)
  }
  
  /**
   * 检测重复结构的数据
   */
  detectDuplicateStructures(mockManager) {
    const structures = new Map()
    
    for (const [apiName, mockItem] of mockManager.mockData) {
      const structure = this.getDataStructure(mockItem.data)
      const structureKey = JSON.stringify(structure)
      
      if (!structures.has(structureKey)) {
        structures.set(structureKey, [])
      }
      structures.get(structureKey).push(apiName)
    }
    
    // 找出有相同结构的API
    const duplicateStructures = Array.from(structures.entries())
      .filter(([, apis]) => apis.length > 1)
      .map(([structure, apis]) => ({
        structure: JSON.parse(structure),
        apis,
        count: apis.length
      }))
    
    if (duplicateStructures.length > 0) {
      this.analysisResults.redundantData.push({
        type: 'duplicate-structures',
        count: duplicateStructures.length,
        structures: duplicateStructures,
        impact: 'medium',
        suggestion: '考虑提取公共数据结构，避免重复定义'
      })
    }
  }
  
  /**
   * 性能影响分析
   */
  analyzePerformanceImpact(mockManager) {
    const totalSize = this.analysisResults.totalSize
    const totalApis = this.analysisResults.totalApis
    
    // 评估内存使用
    if (totalSize > 100000) { // 超过100KB
      this.analysisResults.riskAssessment.push({
        type: 'memory-usage',
        level: 'high',
        description: `Mock数据总大小${Math.round(totalSize / 1024)}KB，可能影响应用启动速度`,
        recommendation: '考虑实施数据懒加载或分片加载'
      })
    }
    
    // 评估API数量
    if (totalApis > 20) {
      this.analysisResults.riskAssessment.push({
        type: 'api-count',
        level: 'medium',
        description: `Mock API数量${totalApis}个，管理复杂度较高`,
        recommendation: '考虑按功能模块分组管理Mock数据'
      })
    }
    
    // 评估动态数据使用
    const dynamicDataSize = this.calculateDataSize(mockManager.dynamicData)
    if (dynamicDataSize > 10000) { // 超过10KB
      this.analysisResults.riskAssessment.push({
        type: 'dynamic-data',
        level: 'medium',
        description: `动态数据大小${Math.round(dynamicDataSize / 1024)}KB，localStorage使用量较高`,
        recommendation: '考虑实施数据清理策略或限制存储量'
      })
    }
  }
  
  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions() {
    const suggestions = this.analysisResults.optimizationSuggestions
    
    // PostgreSQL迁移后的建议
    suggestions.push({
      type: 'postgresql-migration',
      priority: 'high',
      title: 'PostgreSQL迁移优化',
      description: '数据库已迁移到PostgreSQL，可以移除大部分静态Mock数据',
      actions: [
        '保留用户动态数据管理功能',
        '移除重复的静态用户、航材、订单数据',
        '保留API容错机制的备用数据',
        '实现智能数据源选择策略'
      ]
    })
    
    // 环境优化建议
    suggestions.push({
      type: 'environment-optimization',
      priority: 'medium',
      title: '环境配置优化',
      description: '根据不同环境采用不同的Mock策略',
      actions: [
        '开发环境：保持完整Mock功能',
        '演示环境：仅保留容错备用数据',
        '生产环境：完全禁用Mock代码',
        '实施构建时代码排除'
      ]
    })
    
    // 性能优化建议  
    if (this.analysisResults.totalSize > 50000) {
      suggestions.push({
        type: 'performance-optimization',
        priority: 'high',
        title: '性能优化',
        description: 'Mock数据体积较大，需要性能优化',
        actions: [
          '实施Mock数据懒加载',
          '移除未使用的API Mock',
          '压缩大数据量的Mock数据',
          '实施数据分片加载'
        ]
      })
    }
  }
  
  /**
   * 计算数据大小（字节）
   */
  calculateDataSize(data) {
    try {
      return JSON.stringify(data).length
    } catch (error) {
      console.warn('[MockAnalyzer] 计算数据大小失败:', error)
      return 0
    }
  }
  
  /**
   * 获取数据结构（不包含具体值）
   */
  getDataStructure(obj, depth = 0) {
    if (depth > 3) return 'deep-object' // 防止深度过深
    
    if (Array.isArray(obj)) {
      return ['array', obj.length > 0 ? this.getDataStructure(obj[0], depth + 1) : null]
    } else if (obj && typeof obj === 'object') {
      const structure = {}
      for (const key in obj) {
        structure[key] = this.getDataStructure(obj[key], depth + 1)
      }
      return structure
    } else {
      return typeof obj
    }
  }
  
  /**
   * 生成优化报告
   */
  generateReport() {
    const report = {
      summary: {
        totalSize: `${Math.round(this.analysisResults.totalSize / 1024)}KB`,
        totalApis: this.analysisResults.totalApis,
        redundantItems: this.analysisResults.redundantData.length,
        highPriorityIssues: this.analysisResults.riskAssessment.filter(r => r.level === 'high').length
      },
      recommendations: this.analysisResults.optimizationSuggestions,
      risks: this.analysisResults.riskAssessment,
      redundancy: this.analysisResults.redundantData
    }
    
    console.log('[MockAnalyzer] 优化报告:', report)
    return report
  }
}

/**
 * 执行Mock数据分析
 */
export async function analyzeMockData(mockManager) {
  const analyzer = new MockAnalyzer()
  const results = await analyzer.analyzeMockManager(mockManager)
  return analyzer.generateReport()
}

// 开发环境下自动执行分析
if (environment.isDevelopment) {
  // 延迟执行分析，确保Mock管理器已初始化
  setTimeout(async () => {
    try {
      const { mockController } = await import('@/config/environment.js')
      const mockManager = await mockController.getMockManager()
      
      if (mockManager) {
        const report = await analyzeMockData(mockManager)
        console.group('[MockAnalyzer] PostgreSQL迁移后Mock数据分析报告')
        console.log('📊 概况:', report.summary)
        console.log('🎯 优化建议:', report.recommendations)
        console.log('⚠️  风险评估:', report.risks)
        console.log('🗑️  冗余数据:', report.redundancy)
        console.groupEnd()
      }
    } catch (error) {
      console.warn('[MockAnalyzer] 自动分析失败:', error)
    }
  }, 2000)
}

export default MockAnalyzer