import request from '@/utils/request'

/**
 * 获取用户列表
 * 功能描述：获取系统用户列表，支持搜索、筛选、分页
 * 入参：{ page, per_page, search, user_type, status, company_type, sort_by, sort_order }
 * 返回参数：{ users, pagination }
 * url地址：/users/
 * 请求方式：GET
 */
export async function getUserList(params = {}) {
  return await request({
    url: '/users/',
    method: 'get',
    params
  })
}

/**
 * 获取用户详细信息
 * 功能描述：获取指定用户的详细信息
 * 入参：{ user_id }
 * 返回参数：{ user }
 * url地址：/users/{user_id}
 * 请求方式：GET
 */
export async function getUserDetail(userId) {
  return await request({
    url: `/users/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户信息
 * 功能描述：更新指定用户的信息
 * 入参：{ user_id, user_data }
 * 返回参数：{ user, updated_fields }
 * url地址：/users/{user_id}
 * 请求方式：PUT
 */
export async function updateUser(userId, userData) {
  return await request({
    url: `/users/${userId}`,
    method: 'put',
    data: userData
  })
}

/**
 * 更新用户状态
 * 功能描述：更新指定用户的状态
 * 入参：{ user_id, status, reason }
 * 返回参数：{ user_id, old_status, new_status, reason }
 * url地址：/users/{user_id}/status
 * 请求方式：PUT
 */
export async function updateUserStatus(userId, status, reason = '') {
  return await request({
    url: `/users/${userId}/status`,
    method: 'put',
    data: { status, reason }
  })
}

/**
 * 批量更新用户
 * 功能描述：批量更新多个用户的状态
 * 入参：{ user_ids, action, reason }
 * 返回参数：{ updated_count, updated_users, action, reason }
 * url地址：/users/batch-update
 * 请求方式：POST
 */
export async function batchUpdateUsers(userIds, action, reason = '') {
  return await request({
    url: '/users/batch-update',
    method: 'post',
    data: { user_ids: userIds, action, reason }
  })
}

/**
 * 获取用户统计信息
 * 功能描述：获取用户相关的统计数据
 * 入参：无
 * 返回参数：{ total_users, active_users, inactive_users, suspended_users, user_type_stats, company_type_stats, recent_users }
 * url地址：/users/statistics
 * 请求方式：GET
 */
export async function getUserStatistics() {
  return await request({
    url: '/users/statistics',
    method: 'get'
  })
}

/**
 * 重置用户密码
 * 功能描述：重置指定用户的密码
 * 入参：{ user_id, new_password?, force_change? }
 * 返回参数：{ user_id, username, new_password, force_change }
 * url地址：/users/{user_id}/reset-password
 * 请求方式：POST
 */
export async function resetUserPassword(userId, newPassword = null, forceChange = true) {
  return await request({
    url: `/users/${userId}/reset-password`,
    method: 'post',
    data: { new_password: newPassword, force_change: forceChange }
  })
}

/**
 * 获取用户活动日志
 * 功能描述：获取指定用户的活动日志
 * 入参：{ user_id, page?, per_page?, action_type?, start_date?, end_date? }
 * 返回参数：{ user_id, username, activity_logs, pagination }
 * url地址：/users/{user_id}/activity-logs
 * 请求方式：GET
 */
export async function getUserActivityLogs(userId, params = {}) {
  return await request({
    url: `/users/${userId}/activity-logs`,
    method: 'get',
    params
  })
}

/**
 * 导出用户列表
 * 功能描述：导出用户列表到文件
 * 入参：{ format, filters, fields }
 * 返回参数：{ export_format, total_records, fields, download_url, data }
 * url地址：/users/export
 * 请求方式：POST
 */
export async function exportUsers(format = 'csv', filters = {}, fields = []) {
  return await request({
    url: '/users/export',
    method: 'post',
    data: { format, filters, fields }
  })
}

/**
 * 获取用户类型选项
 * 功能描述：获取系统支持的用户类型列表
 * 入参：无
 * 返回参数：{ user_types }
 * url地址：/users/user-types
 * 请求方式：GET
 */
export async function getUserTypes() {
  return await request({
    url: '/users/user-types',
    method: 'get'
  })
}

export const usersApi = {
  getUserList,
  getUserDetail,
  updateUser,
  updateUserStatus,
  batchUpdateUsers,
  getUserStatistics,
  resetUserPassword,
  getUserActivityLogs,
  exportUsers,
  getUserTypes
}

export default usersApi
