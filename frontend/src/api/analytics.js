import request from '@/utils/request'

/**
 * 获取业务概览数据
 * 功能描述：获取平台核心业务指标和概览数据
 * 入参：{ time_range: string, user_type: string, region: string }
 * 返回参数：{ total_revenue: number, active_users: number, order_volume: number, growth_rate: number, key_metrics: object }
 * url地址：/analytics/overview
 * 请求方式：GET
 */
export function getBusinessOverview(params = {}) {
  return request({
    url: '/analytics/overview',
    method: 'get',
    params
  })
}

/**
 * 获取销售分析数据
 * 功能描述：获取销售相关的分析数据和趋势
 * 入参：{ time_range: string, product_category: string, region: string, aggregation: string }
 * 返回参数：{ sales_trend: array, top_products: array, regional_performance: object, forecast: array }
 * url地址：/analytics/sales
 * 请求方式：GET
 */
export function getSalesAnalytics(params = {}) {
  return request({
    url: '/analytics/sales',
    method: 'get',
    params
  })
}

/**
 * 获取库存分析数据
 * 功能描述：获取库存相关的分析数据和预警信息
 * 入参：{ time_range: string, category: string, warehouse: string, threshold: number }
 * 返回参数：{ inventory_turnover: object, stock_levels: array, reorder_alerts: array, optimization_suggestions: array }
 * url地址：/analytics/inventory
 * 请求方式：GET
 */
export function getInventoryAnalytics(params = {}) {
  return request({
    url: '/analytics/inventory',
    method: 'get',
    params
  })
}

/**
 * 获取用户行为分析
 * 功能描述：获取用户行为模式和活跃度分析
 * 入参：{ time_range: string, user_segment: string, behavior_type: string }
 * 返回参数：{ user_engagement: object, popular_features: array, user_journey: array, retention_rate: number }
 * url地址：/analytics/user-behavior
 * 请求方式：GET
 */
export function getUserBehaviorAnalytics(params = {}) {
  return request({
    url: '/analytics/user-behavior',
    method: 'get',
    params
  })
}

/**
 * 获取供应链分析
 * 功能描述：获取供应链效率和性能分析数据
 * 入参：{ time_range: string, supplier_category: string, performance_metric: string }
 * 返回参数：{ supplier_performance: array, delivery_metrics: object, cost_analysis: object, risk_assessment: array }
 * url地址：/analytics/supply-chain
 * 请求方式：GET
 */
export function getSupplyChainAnalytics(params = {}) {
  return request({
    url: '/analytics/supply-chain',
    method: 'get',
    params
  })
}

/**
 * 获取财务分析数据
 * 功能描述：获取财务相关的分析数据和报表
 * 入参：{ time_range: string, currency: string, business_unit: string, metric_type: string }
 * 返回参数：{ revenue_analysis: object, cost_breakdown: object, profit_margins: array, budget_variance: object }
 * url地址：/analytics/financial
 * 请求方式：GET
 */
export function getFinancialAnalytics(params = {}) {
  return request({
    url: '/analytics/financial',
    method: 'get',
    params
  })
}

/**
 * 生成自定义报表
 * 功能描述：根据用户配置生成自定义分析报表
 * 入参：{ report_config: object, data_sources: array, filters: object, format: string }
 * 返回参数：{ report_id: string, report_url: string, download_links: object, generation_status: string }
 * url地址：/analytics/custom-report
 * 请求方式：POST
 */
export function generateCustomReport(reportConfig) {
  return request({
    url: '/analytics/custom-report',
    method: 'post',
    data: reportConfig
  })
}

/**
 * 获取预测分析
 * 功能描述：基于历史数据进行趋势预测和分析
 * 入参：{ model_type: string, prediction_period: number, data_granularity: string, confidence_level: number }
 * 返回参数：{ predictions: array, confidence_intervals: array, model_accuracy: object, recommendations: array }
 * url地址：/analytics/forecasting
 * 请求方式：GET
 */
export function getForecastingAnalytics(params = {}) {
  return request({
    url: '/analytics/forecasting',
    method: 'get',
    params
  })
}

/**
 * 获取竞争分析
 * 功能描述：获取市场竞争态势和基准分析
 * 入参：{ market_segment: string, competitor_set: array, benchmark_metrics: array }
 * 返回参数：{ market_position: object, competitive_gaps: array, benchmark_results: object, market_trends: array }
 * url地址：/analytics/competitive
 * 请求方式：GET
 */
export function getCompetitiveAnalytics(params = {}) {
  return request({
    url: '/analytics/competitive',
    method: 'get',
    params
  })
}

/**
 * 保存分析看板配置
 * 功能描述：保存用户自定义的分析看板配置
 * 入参：{ dashboard_name: string, widgets: array, layout: object, filters: object, auto_refresh: boolean }
 * 返回参数：{ dashboard_id: string, message: string, saved_config: object }
 * url地址：/analytics/dashboard/save
 * 请求方式：POST
 */
export function saveDashboardConfig(configData) {
  return request({
    url: '/analytics/dashboard/save',
    method: 'post',
    data: configData
  })
}

/**
 * 获取分析看板配置
 * 功能描述：获取用户保存的分析看板配置
 * 入参：{ dashboard_id: string, user_id: string }
 * 返回参数：{ dashboard_config: object, widgets: array, last_updated: string }
 * url地址：/analytics/dashboard/{id}
 * 请求方式：GET
 */
export function getDashboardConfig(dashboardId) {
  return request({
    url: `/analytics/dashboard/${dashboardId}`,
    method: 'get'
  })
}

/**
 * 导出分析数据
 * 功能描述：导出分析数据到各种格式文件
 * 入参：{ data_type: string, format: string, date_range: object, filters: object, include_charts: boolean }
 * 返回参数：{ export_id: string, download_url: string, file_size: number, expiry_time: string }
 * url地址：/analytics/export
 * 请求方式：POST
 */
export function exportAnalyticsData(exportConfig) {
  return request({
    url: '/analytics/export',
    method: 'post',
    data: exportConfig
  })
}

export const analyticsApi = {
  getBusinessOverview,
  getSalesAnalytics,
  getInventoryAnalytics,
  getUserBehaviorAnalytics,
  getSupplyChainAnalytics,
  getFinancialAnalytics,
  generateCustomReport,
  getForecastingAnalytics,
  getCompetitiveAnalytics,
  saveDashboardConfig,
  getDashboardConfig,
  exportAnalyticsData
}