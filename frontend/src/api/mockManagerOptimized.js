/**
 * 优化后的Mock数据管理器
 * PostgreSQL迁移后的精简版本，保留核心功能，移除冗余数据
 * 
 * 保留功能：
 * - 动态数据管理（用户操作数据持久化）
 * - API容错机制（备用数据）
 * - 开发调试支持
 * - 智能环境控制
 * 
 * 移除功能：
 * - 大量静态Mock数据
 * - 重复的用户/航材/订单数据
 * - 过时的API模拟
 */

import { environment } from '@/config/environment.js'

class OptimizedMockManager {
  constructor() {
    // 智能环境检测
    const mockConfig = environment.getMockConfig()
    this.enabled = mockConfig.enabled
    this.fullDataMode = mockConfig.fullData
    this.fallbackMode = mockConfig.fallbackOnly
    
    // 核心配置
    this.config = {
      ...mockConfig,
      delay: mockConfig.apiDelay ? { min: 200, max: 800 } : { min: 0, max: 0 },
      errorSimulation: mockConfig.errorSimulation
    }
    
    // 动态数据存储（保留原有功能）
    this.dynamicData = {
      demands: this.loadDynamicData('demands') || [],
      sharedMaterials: this.loadDynamicData('sharedMaterials') || [],
      orders: this.loadDynamicData('orders') || [],
      notifications: this.loadDynamicData('notifications') || []
    }
    
    // 最小备用数据（仅用于API失败时的容错）
    this.fallbackData = this.initFallbackData()
    
    // 完整开发数据（仅在开发环境的完整模式下使用）
    this.devData = this.fullDataMode ? this.initDevData() : null
    
    this.logInit()
  }
  
  /**
   * 初始化最小备用数据
   * 仅包含API失败时必需的基础数据
   */
  initFallbackData() {
    return {
      // 登录备用数据
      login: {
        error: 0,
        message: '登录成功',
        body: {
          access_token: 'fallback-token-' + Date.now(),
          user: {
            id: 1,
            username: 'fallback_user',
            email: '<EMAIL>',
            user_type: 'demo',
            company_name: '演示用户',
            real_name: '备用用户'
          }
        },
        success: true
      },
      
      // 空列表备用数据
      emptyList: {
        error: 0,
        message: '数据获取成功',
        body: {
          data: [],
          pagination: {
            current_page: 1,
            per_page: 10,
            total: 0,
            total_pages: 0
          }
        },
        success: true
      },
      
      // 基础统计备用数据
      basicStats: {
        error: 0,
        message: '统计数据获取成功',
        body: {
          total: 0,
          today: 0,
          week: 0,
          month: 0
        },
        success: true
      }
    }
  }
  
  /**
   * 初始化开发环境数据
   * 仅在开发环境且fullDataMode=true时加载
   */
  initDevData() {
    return {
      // 简化的演示数据
      sampleMaterials: [
        {
          id: 1,
          name: 'CFM56发动机部件',
          part_number: 'CFM56-7B-001',
          category: 'engine',
          price: 125000,
          condition: 'new',
          location: '北京'
        }
      ],
      
      sampleDemands: [
        {
          id: 1,
          demand_number: 'DM' + Date.now(),
          title: 'AOG紧急需求',
          type: 'aog',
          priority: 'aog',
          status: 'published',
          requester: '演示用户',
          created_at: new Date().toISOString()
        }
      ],
      
      sampleOrders: [
        {
          id: 1,
          order_number: 'PO' + Date.now(),
          status: 'processing',
          total_amount: 125000,
          created_at: new Date().toISOString()
        }
      ]
    }
  }
  
  /**
   * 动态数据管理 - 保留原有功能
   */
  async addDynamicData(type, data) {
    if (!this.enabled) return false
    
    const newData = {
      ...data,
      id: Date.now(),
      created_at: new Date().toISOString(),
      _dynamic: true
    }
    
    this.dynamicData[type].unshift(newData)
    this.saveDynamicData(type, this.dynamicData[type])
    
    if (this.config.errorSimulation) {
      console.log(`[MockManager] 添加动态${type}数据:`, newData)
    }
    
    return newData
  }
  
  /**
   * 获取需求列表 - 优化版本
   */
  async getDemandsList(params = {}) {
    if (!this.enabled) return null
    
    await this.simulateDelay()
    
    // 合并动态数据和备用数据
    let demands = [...this.dynamicData.demands]
    
    // 仅在完整模式下添加演示数据
    if (this.fullDataMode && this.devData) {
      demands = [...demands, ...this.devData.sampleDemands]
    }
    
    // 如果没有数据且需要备用数据，返回空列表
    if (demands.length === 0 && this.fallbackMode) {
      return this.fallbackData.emptyList
    }
    
    return {
      error: 0,
      message: '获取需求列表成功',
      body: {
        demands: demands.slice(0, params.limit || 10),
        pagination: {
          current_page: params.page || 1,
          per_page: params.limit || 10,
          total: demands.length,
          total_pages: Math.ceil(demands.length / (params.limit || 10))
        }
      },
      success: true
    }
  }
  
  /**
   * 获取共享航材列表 - 优化版本
   */
  async getSharedMaterialsList(params = {}) {
    if (!this.enabled) return null
    
    await this.simulateDelay()
    
    let materials = [...this.dynamicData.sharedMaterials]
    
    if (this.fullDataMode && this.devData) {
      materials = [...materials, ...this.devData.sampleMaterials]
    }
    
    if (materials.length === 0 && this.fallbackMode) {
      return this.fallbackData.emptyList
    }
    
    return {
      error: 0,
      message: '获取共享航材列表成功',
      body: {
        materials: materials.slice(0, params.limit || 10),
        pagination: {
          current_page: params.page || 1,
          per_page: params.limit || 10,
          total: materials.length,
          total_pages: Math.ceil(materials.length / (params.limit || 10))
        }
      },
      success: true
    }
  }
  
  /**
   * 获取订单列表 - 优化版本
   */
  async getOrdersList(params = {}) {
    if (!this.enabled) return null
    
    await this.simulateDelay()
    
    let orders = [...this.dynamicData.orders]
    
    if (this.fullDataMode && this.devData) {
      orders = [...orders, ...this.devData.sampleOrders]
    }
    
    if (orders.length === 0 && this.fallbackMode) {
      return this.fallbackData.emptyList
    }
    
    return {
      error: 0,
      message: '获取订单列表成功',
      body: {
        orders: orders.slice(0, params.limit || 10),
        statistics: {
          pending: orders.filter(o => o.status === 'pending').length,
          processing: orders.filter(o => o.status === 'processing').length,
          completed: orders.filter(o => o.status === 'completed').length,
          total_amount: orders.reduce((sum, o) => sum + (o.total_amount || 0), 0)
        }
      },
      success: true
    }
  }
  
  /**
   * 通用备用数据获取
   */
  async getFallbackData(apiName) {
    if (!this.enabled || !this.fallbackMode) return null
    
    await this.simulateDelay()
    
    // 返回对应的备用数据或空列表
    switch (apiName) {
      case 'login':
        return this.fallbackData.login
      case 'stats':
      case 'statistics':
        return this.fallbackData.basicStats
      default:
        return this.fallbackData.emptyList
    }
  }
  
  /**
   * localStorage数据管理 - 保留原有功能
   */
  loadDynamicData(type) {
    try {
      const data = localStorage.getItem(`mockData_${type}`)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn(`[MockManager] 加载${type}动态数据失败:`, error)
      return null
    }
  }
  
  saveDynamicData(type, data) {
    try {
      localStorage.setItem(`mockData_${type}`, JSON.stringify(data))
    } catch (error) {
      console.warn(`[MockManager] 保存${type}动态数据失败:`, error)
    }
  }
  
  clearDynamicData(type) {
    if (type) {
      this.dynamicData[type] = []
      localStorage.removeItem(`mockData_${type}`)
    } else {
      // 清空所有动态数据
      Object.keys(this.dynamicData).forEach(key => {
        this.dynamicData[key] = []
        localStorage.removeItem(`mockData_${key}`)
      })
    }
  }
  
  /**
   * 网络延迟模拟 - 保留但优化
   */
  async simulateDelay() {
    if (!this.config.delay || (this.config.delay.min === 0 && this.config.delay.max === 0)) {
      return
    }
    
    const delay = this.config.delay.min + 
      Math.random() * (this.config.delay.max - this.config.delay.min)
    
    await new Promise(resolve => setTimeout(resolve, delay))
  }
  
  /**
   * 配置更新
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    this.enabled = this.config.enabled
    this.fullDataMode = this.config.fullData
    this.fallbackMode = this.config.fallbackOnly
    
    // 根据新配置重新初始化数据
    if (this.fullDataMode && !this.devData) {
      this.devData = this.initDevData()
    } else if (!this.fullDataMode && this.devData) {
      this.devData = null
    }
  }
  
  /**
   * 获取统计信息
   */
  getStats() {
    return {
      enabled: this.enabled,
      mode: this.fullDataMode ? 'full' : this.fallbackMode ? 'fallback' : 'disabled',
      dynamicData: Object.keys(this.dynamicData).reduce((stats, key) => {
        stats[key] = this.dynamicData[key].length
        return stats
      }, {}),
      config: this.config
    }
  }
  
  /**
   * 日志输出
   */
  logInit() {
    if (!environment.getLogConfig().mockOperations) return
    
    const mode = this.fullDataMode ? '完整开发模式' : 
                 this.fallbackMode ? '容错备用模式' : '禁用模式'
    
    console.log(`[MockManager] 优化版已${this.enabled ? '启用' : '禁用'} - ${mode}`)
    
    if (this.enabled) {
      console.log('[MockManager] 动态数据:', Object.keys(this.dynamicData).reduce((stats, key) => {
        stats[key] = this.dynamicData[key].length
        return stats
      }, {}))
    }
  }
}

export { OptimizedMockManager as MockManager }
export default OptimizedMockManager