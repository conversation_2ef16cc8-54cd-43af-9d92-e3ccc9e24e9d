import request from '@/utils/request'

/**
 * 创建AOG紧急请求
 * 功能描述：创建飞机停场(Aircraft on Ground)紧急响应请求
 * 入参：{ aircraft_info: object, fault_info: object, contact_info: object, required_parts: array, priority: string }
 * 返回参数：{ aog_id: string, case_number: string, response_team: object, estimated_response_time: number }
 * url地址：/aog/emergency-request
 * 请求方式：POST
 */
export function createAOGRequest(aogData) {
  return request({
    url: '/aog/emergency-request',
    method: 'post',
    data: aogData
  })
}

/**
 * 获取AOG请求详情
 * 功能描述：获取指定AOG请求的详细信息和处理状态
 * 入参：{ aog_id: string }
 * 返回参数：{ aog_request: object, status_timeline: array, response_team: object, progress: number }
 * url地址：/aog/{id}
 * 请求方式：GET
 */
export function getAOGDetails(aogId) {
  return request({
    url: `/aog/${aogId}`,
    method: 'get'
  })
}

/**
 * 更新AOG处理状态
 * 功能描述：更新AOG请求的处理进度和状态信息
 * 入参：{ aog_id: string, status: string, progress: number, notes: string, estimated_completion: string }
 * 返回参数：{ message: string, updated_status: object, next_actions: array }
 * url地址：/aog/{id}/update-status
 * 请求方式：PATCH
 */
export function updateAOGStatus(aogId, statusData) {
  return request({
    url: `/aog/${aogId}/update-status`,
    method: 'patch',
    data: statusData
  })
}

/**
 * 获取AOG列表
 * 功能描述：获取AOG请求列表，支持筛选和分页
 * 入参：{ status: string, priority: string, aircraft_type: string, page: number, size: number }
 * 返回参数：{ aog_requests: array, total: number, statistics: object }
 * url地址：/aog/requests
 * 请求方式：GET
 */
export function getAOGList(params = {}) {
  return request({
    url: '/aog/requests',
    method: 'get',
    params
  })
}

/**
 * 分配AOG响应团队
 * 功能描述：为AOG请求分配专业响应团队和资源
 * 入参：{ aog_id: string, team_members: array, priority_level: string, resources: array }
 * 返回参数：{ message: string, assigned_team: object, response_plan: object }
 * url地址：/aog/{id}/assign-team
 * 请求方式：POST
 */
export function assignAOGTeam(aogId, teamData) {
  return request({
    url: `/aog/${aogId}/assign-team`,
    method: 'post',
    data: teamData
  })
}

/**
 * 快速零件匹配
 * 功能描述：为AOG请求快速匹配可用零件和供应商
 * 入参：{ aog_id: string, part_requirements: array, location: string, max_distance: number }
 * 返回参数：{ available_parts: array, suppliers: array, delivery_options: array }
 * url地址：/aog/{id}/quick-match
 * 请求方式：GET
 */
export function quickPartMatch(aogId, params = {}) {
  return request({
    url: `/aog/${aogId}/quick-match`,
    method: 'get',
    params
  })
}

/**
 * 启动紧急采购流程
 * 功能描述：为AOG请求启动紧急采购和快速审批流程
 * 入参：{ aog_id: string, supplier_id: string, parts: array, delivery_method: string, approval_override: boolean }
 * 返回参数：{ purchase_order_id: string, approval_status: string, estimated_delivery: string }
 * url地址：/aog/{id}/emergency-purchase
 * 请求方式：POST
 */
export function startEmergencyPurchase(aogId, purchaseData) {
  return request({
    url: `/aog/${aogId}/emergency-purchase`,
    method: 'post',
    data: purchaseData
  })
}

/**
 * 获取AOG响应统计
 * 功能描述：获取AOG响应相关的统计数据和KPI指标
 * 入参：{ time_range: string, aircraft_type: string, region: string }
 * 返回参数：{ total_cases: number, avg_response_time: number, resolution_rate: number, stats_by_type: object }
 * url地址：/aog/statistics
 * 请求方式：GET
 */
export function getAOGStatistics(params = {}) {
  return request({
    url: '/aog/statistics',
    method: 'get',
    params
  })
}

/**
 * 创建AOG通知
 * 功能描述：向相关方发送AOG紧急通知
 * 入参：{ aog_id: string, notification_type: string, recipients: array, urgency: string, message: string }
 * 返回参数：{ message: string, notification_id: string, delivery_status: object }
 * url地址：/aog/{id}/notify
 * 请求方式：POST
 */
export function sendAOGNotification(aogId, notificationData) {
  return request({
    url: `/aog/${aogId}/notify`,
    method: 'post',
    data: notificationData
  })
}

/**
 * 关闭AOG案例
 * 功能描述：完成AOG处理并关闭案例
 * 入参：{ aog_id: string, resolution_summary: string, lessons_learned: string, feedback: object }
 * 返回参数：{ message: string, case_summary: object, performance_metrics: object }
 * url地址：/aog/{id}/close
 * 请求方式：POST
 */
export function closeAOGCase(aogId, closureData) {
  return request({
    url: `/aog/${aogId}/close`,
    method: 'post',
    data: closureData
  })
}

export const aogApi = {
  createAOGRequest,
  getAOGDetails,
  updateAOGStatus,
  getAOGList,
  assignAOGTeam,
  quickPartMatch,
  startEmergencyPurchase,
  getAOGStatistics,
  sendAOGNotification,
  closeAOGCase
}