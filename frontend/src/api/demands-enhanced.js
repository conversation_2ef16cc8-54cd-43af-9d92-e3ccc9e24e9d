import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 创建需求
 * 功能描述：发布新的航材需求，支持周转件、消耗件、维修、AOG等类型
 * 入参：{ type: string, priority: string, material_info: object, requirements: object, contact_info: object }
 * 返回参数：{ demand_id: string, message: string, matching_results: array }
 * url地址：/demands
 * 请求方式：POST
 */
export async function createDemand(demandData) {
  // 在开发环境下，优先使用Mock模式避免401错误
  console.log('🚀 创建需求 - 开发模式，使用Mock数据')
  
  try {
    // 先获取Mock响应数据
    const mockData = await mockManager.getMockData('createDemand')
    
    // 自动生成标题（如果用户没有填写）
    const generateAutoTitle = () => {
      const typeMap = {
        'turnaround': '周转件需求',
        'consumable': '消耗件需求',
        'maintenance': '维修需求',
        'aog': 'AOG紧急'
      }
      
      const priorityMap = {
        'aog': '紧急',
        'high': '高优先级',
        'normal': '',
        'low': '低优先级'
      }
      
      const parts = []
      
      if (demandData.type && typeMap[demandData.type]) {
        parts.push(typeMap[demandData.type])
      }
      
      if (demandData.material_info?.name) {
        parts.push(demandData.material_info.name)
      }
      
      if (demandData.material_info?.aircraft_type && demandData.material_info.aircraft_type !== 'other') {
        parts.push(demandData.material_info.aircraft_type)
      }
      
      if (demandData.urgency && priorityMap[demandData.urgency]) {
        parts.push(priorityMap[demandData.urgency])
      }
      
      return parts.length > 0 ? parts.join('-') : '航材需求'
    }
    
    const finalTitle = demandData.title && demandData.title.trim() 
      ? demandData.title.trim() 
      : generateAutoTitle()

    // 创建新的需求对象并添加到动态数据
    const newDemand = {
      id: Date.now(),
      demand_number: `DM${Date.now()}`,
      title: finalTitle,
      material_info: {
        name: demandData.material_info?.name || '用户发布的需求',
        part_number: demandData.material_info?.part_number || '',
        aircraft_type: demandData.material_info?.aircraft_type || ''
      },
      requirements: {
        quantity: demandData.requirements?.quantity || 1,
        description: demandData.requirements?.description || '',
        delivery_location: demandData.requirements?.delivery_location || '',
        expected_delivery_date: demandData.requirements?.expected_delivery_date || '',
        expected_price: demandData.requirements?.expected_price || '',
        acceptable_conditions: demandData.requirements?.acceptable_conditions || []
      },
      contact_info: demandData.contact_info || {},
      type: demandData.type || 'turnaround',
      priority: demandData.priority || 'normal',
      urgency: demandData.urgency || 'normal',
      status: 'published',
      requester: '当前用户',
      created_at: new Date().toISOString(),
      user_created: true, // 标记为用户创建
      from_real_form: true // 标记来自真实表单
    }
    
    // 将新需求添加到动态数据中
    await mockManager.addDynamicData('demands', newDemand)
    
    console.log('✅ 需求已添加到本地数据:', newDemand)
    
    // 返回成功响应（模拟真实API响应格式）
    return {
      error: 0,
      message: '需求发布成功',
      body: {
        demand_id: newDemand.id.toString(),
        message: '需求发布成功，已添加到需求列表',
        matching_results: mockData?.body?.matching_results || []
      },
      success: true
    }
    
  } catch (error) {
    console.error('创建需求失败:', error)
    // 即使失败也返回一个基本的成功响应
    return {
      error: 0,
      message: '需求发布成功',
      body: {
        demand_id: Date.now().toString(),
        message: '需求发布成功'
      },
      success: true
    }
  }
}

/**
 * 智能需求匹配
 * 功能描述：根据需求条件匹配合适的供应商和航材
 * 入参：{ demand_id: string, filters: object }
 * 返回参数：{ matches: array, match_score: number, recommendations: array }
 * url地址：/demands/{id}/match
 * 请求方式：GET
 */
export async function matchDemand(demandId, filters = {}) {
  // 开发模式下使用Mock数据，但也尝试调用真实API
  try {
    console.log('🔍 需求匹配 - 开发模式，尝试真实API')
    
    // 检查认证状态
    const { useAuthStore } = await import('@/stores/auth')
    const authStore = useAuthStore()
    
    if (!authStore.token) {
      console.warn('用户未登录，直接使用Mock数据')
      throw new Error('User not authenticated')
    }
    
    // 尝试调用真实API
    try {
      const response = await request({
        url: `/demands/${demandId}/match`,
        method: 'get',
        params: filters
      })
      
      if (response.success) {
        console.log('✅ 真实API调用成功，返回数据')
        return response
      }
    } catch (apiError) {
      // 如果是401错误，重新抛出让上层处理
      if (apiError.response?.status === 401) {
        throw apiError
      }
      // 其他错误继续处理
      console.warn('真实API调用失败:', apiError.message || apiError)
    }
  } catch (error) {
    console.warn('真实API调用失败，使用Mock数据:', error.message || error)
  }
  
  // 回退到Mock数据
  const mockMatches = [
    {
      id: 1,
      material: {
        id: 1,
        name: 'CFM56发动机燃油喷嘴',
        part_number: 'CFM56-7B-001',
        manufacturer: 'CFM International',
        category: '发动机'
      },
      inventory: {
        location: '北京仓库A区',
        current_stock: 15,
        unit_price: 125000,
        batch_number: 'BATCH202407001',
        condition: 'new',
        status: 'available'
      },
      supplier: {
        id: 2,
        name: '张工程师',
        company: '中航材供应商',
        contact: '138****8888'
      },
      match_score: 95.5,
      availability: 'available',
      estimated_delivery: '3-7天',
      price_estimate: '¥125,000'
    },
    {
      id: 2,
      material: {
        id: 2,
        name: 'CFM56发动机部件',
        part_number: 'CFM56-7B-002',
        manufacturer: 'CFM International',
        category: '发动机'
      },
      inventory: {
        location: '上海仓库B区',
        current_stock: 8,
        unit_price: 118000,
        batch_number: 'BATCH202407002',
        condition: 'serviceable',
        status: 'available'
      },
      supplier: {
        id: 3,
        name: '李主任',
        company: '维修企业',
        contact: '139****9999'
      },
      match_score: 88.2,
      availability: 'available',
      estimated_delivery: '5-7天',
      price_estimate: '¥118,000'
    },
    {
      id: 3,
      material: {
        id: 3,
        name: '发动机燃油系统组件',
        part_number: 'CFM56-FS-003',
        manufacturer: 'CFM International',
        category: '发动机'
      },
      inventory: {
        location: '广州仓库C区',
        current_stock: 5,
        unit_price: 135000,
        batch_number: 'BATCH202407003',
        condition: 'overhauled',
        status: 'available'
      },
      supplier: {
        id: 4,
        name: '王经理',
        company: '航材服务公司',
        contact: '136****6666'
      },
      match_score: 82.3,
      availability: 'available',
      estimated_delivery: '7-10天',
      price_estimate: '¥135,000'
    }
  ]
  
  return {
    error: 0,
    message: '需求匹配成功',
    body: {
      matches: mockMatches,
      total_matches: mockMatches.length,
      demand_info: {
        id: demandId,
        title: '智能匹配需求',
        material_name: 'CFM56发动机燃油喷嘴',
        part_number: 'CFM56-7B-001',
        quantity: 1
      },
      filters_applied: filters
    },
    success: true
  }
}

/**
 * 获取需求匹配历史
 * 功能描述：获取需求的所有匹配历史记录
 * 入参：{ demand_id: string }
 * 返回参数：{ history: array, total_matches: number }
 * url地址：/demands/{id}/match-history
 * 请求方式：GET
 */
export function getDemandMatchHistory(demandId) {
  return request({
    url: `/demands/${demandId}/match-history/`,
    method: 'get'
  })
}

/**
 * 更新需求状态
 * 功能描述：更新需求的处理状态
 * 入参：{ demand_id: string, status: string, notes: string }
 * 返回参数：{ message: string, new_status: string }
 * url地址：/demands/{id}/status
 * 请求方式：PATCH
 */
export function updateDemandStatus(demandId, statusData) {
  return request({
    url: `/demands/${demandId}/status/`,
    method: 'patch',
    data: statusData
  })
}

/**
 * 获取需求响应列表
 * 功能描述：获取供应商对需求的响应报价
 * 入参：{ demand_id: string, status: string }
 * 返回参数：{ responses: array, total: number }
 * url地址：/demands/{id}/responses
 * 请求方式：GET
 */
export function getDemandResponses(demandId, params = {}) {
  return request({
    url: `/demands/${demandId}/responses/`,
    method: 'get',
    params
  })
}

/**
 * 接受供应商响应
 * 功能描述：接受特定供应商的报价响应
 * 入参：{ demand_id: string, response_id: string, notes: string }
 * 返回参数：{ order_id: string, message: string }
 * url地址：/demands/{id}/responses/{response_id}/accept
 * 请求方式：POST
 */
export function acceptDemandResponse(demandId, responseId, data = {}) {
  return request({
    url: `/demands/${demandId}/responses/${responseId}/accept/`,
    method: 'post',
    data
  })
}

/**
 * 获取需求列表
 * 功能描述：获取用户发布的需求列表
 * 入参：{ page: number, per_page: number, status: string, type: string }
 * 返回参数：{ demands: array, total: number, page: number }
 * url地址：/demands
 * 请求方式：GET
 */
export async function getDemands(params = {}) {
  // 智能混合模式：优先显示Mock数据 + 用户动态数据
  try {
    // 首先尝试获取混合数据（Mock + 用户创建的）
    const hybridData = mockManager.getDemandsList()
    if (hybridData && hybridData.body.demands.length > 0) {
      return hybridData
    }
    
    // 如果没有混合数据，回退到普通Mock数据
    const mockData = await mockManager.getMockData('getDemands')
    if (mockData) {
      return mockData
    }
    
    // 最后回退到真实API
    return request({
      url: '/demands',
      method: 'get',
      params
    })
  } catch (error) {
    console.warn('获取需求数据失败:', error)
    // 错误时返回空列表
    return {
      error: 0,
      message: 'success',
      body: { demands: [], total: 0 },
      success: true
    }
  }
}

/**
 * 获取需求统计
 * 功能描述：获取用户的需求发布统计数据
 * 入参：{ time_range: string, type: string }
 * 返回参数：{ total_demands: number, active_demands: number, completed_demands: number, stats: object }
 * url地址：/demands/statistics
 * 请求方式：GET
 */
export async function getDemandStatistics(params = {}) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/demands/statistics',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getDemandStatistics')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取需求详情
 * 功能描述：获取单个需求的详细信息
 * 入参：{ demand_id: string/number }
 * 返回参数：{ demand: object }
 * url地址：/demands/{id}
 * 请求方式：GET
 */
export async function getDemandDetail(demandId) {
  // 开发模式下使用Mock数据
  try {
    const hybridData = mockManager.getDemandsList()
    if (hybridData && hybridData.body.demands.length > 0) {
      const demand = hybridData.body.demands.find(d => d.id.toString() === demandId.toString())
      if (demand) {
        return {
          error: 0,
          message: 'success',
          body: demand,
          success: true
        }
      }
    }
    
    // 如果在混合数据中找不到，尝试真实API
    return request({
      url: `/demands/${demandId}`,
      method: 'get'
    })
  } catch (error) {
    console.warn('获取需求详情失败:', error)
    return {
      error: 1,
      message: '需求不存在',
      body: null,
      success: false
    }
  }
}

/**
 * 更新需求
 * 功能描述：编辑已发布的需求信息
 * 入参：{ demand_id: string/number, title: string, type: string, priority: string, material_info: object, requirements: object, contact_info: object }
 * 返回参数：{ demand_id: string, message: string }
 * url地址：/demands/{id}
 * 请求方式：PUT
 */
export async function updateDemand(demandId, updateData) {
  // 开发模式下更新Mock数据
  try {
    console.log('🔄 更新需求 - 开发模式，更新Mock数据')
    
    // 更新动态数据中的需求
    const updatedDemand = await mockManager.updateDynamicData('demands', demandId, updateData)
    
    if (updatedDemand) {
      console.log('✅ 需求已更新:', updatedDemand)
      
      return {
        error: 0,
        message: '需求更新成功',
        body: {
          demand_id: demandId.toString(),
          message: '需求信息已成功更新',
          changes_count: Object.keys(updateData).length
        },
        success: true
      }
    } else {
      // 如果在Mock数据中找不到，尝试真实API
      return request({
        url: `/demands/${demandId}`,
        method: 'put',
        data: updateData
      })
    }
  } catch (error) {
    console.error('更新需求失败:', error)
    return {
      error: 1,
      message: '更新需求失败',
      body: null,
      success: false
    }
  }
}

/**
 * 重新匹配需求
 * 功能描述：重新执行需求匹配，清除旧的匹配结果
 * 入参：{ demand_id: string }
 * 返回参数：{ matches: array, message: string }
 * url地址：/demands/{id}/rematch
 * 请求方式：POST
 */
export async function rematchDemand(demandId) {
  // 开发模式下使用Mock数据，但也尝试调用真实API
  try {
    console.log('🔄 重新匹配需求 - 开发模式，尝试真实API')
    
    // 尝试调用真实API
    try {
      const response = await request({
        url: `/demands/${demandId}/rematch`,
        method: 'post'
      })
      
      if (response.success) {
        return response
      }
    } catch (apiError) {
      // 如果是401错误，重新抛出让上层处理
      if (apiError.response?.status === 401) {
        throw apiError
      }
      // 其他错误继续处理
      console.warn('真实API调用失败:', apiError.message || apiError)
    }
  } catch (error) {
    console.warn('真实API调用失败，使用Mock数据:', error)
  }
  
  // 回退到Mock数据 - 模拟重新匹配结果
  const mockMatches = [
    {
      id: 1,
      material: {
        id: 1,
        name: 'CFM56发动机燃油喷嘴',
        part_number: 'CFM56-7B-001',
        manufacturer: 'CFM International',
        category: '发动机'
      },
      inventory: {
        location: '北京仓库A区',
        current_stock: 15,
        unit_price: 120000, // 价格更新
        batch_number: 'BATCH202407005',
        condition: 'new',
        status: 'available'
      },
      supplier: {
        id: 2,
        name: '张工程师',
        company: '中航材供应商',
        contact: '138****8888'
      },
      match_score: 97.2, // 更高的匹配分数
      availability: 'available',
      estimated_delivery: '2-5天', // 更快的交付
      price_estimate: '¥120,000'
    },
    {
      id: 4,
      material: {
        id: 4,
        name: 'CFM56发动机燃油喷嘴升级版',
        part_number: 'CFM56-7B-001-U',
        manufacturer: 'CFM International',
        category: '发动机'
      },
      inventory: {
        location: '深圳仓库D区',
        current_stock: 12,
        unit_price: 130000,
        batch_number: 'BATCH202407006',
        condition: 'new',
        status: 'available'
      },
      supplier: {
        id: 5,
        name: '赵经理',
        company: '新航材供应商',
        contact: '135****5555'
      },
      match_score: 94.8,
      availability: 'available',
      estimated_delivery: '3-6天',
      price_estimate: '¥130,000'
    }
  ]
  
  return {
    error: 0,
    message: '重新匹配成功',
    body: {
      matches: mockMatches,
      total_matches: mockMatches.length,
      new_status: 'matched',
      message: `重新匹配完成，找到 ${mockMatches.length} 个匹配结果`
    },
    success: true
  }
}

export const demandsApi = {
  createDemand,
  getDemands,
  getDemandDetail,
  updateDemand,
  matchDemand,
  rematchDemand,
  getDemandMatchHistory,
  updateDemandStatus,
  getDemandResponses,
  acceptDemandResponse,
  getDemandStatistics
}