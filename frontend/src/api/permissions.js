import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 获取所有权限
 * 功能描述：获取系统中所有可用的权限列表
 * 入参：{ category: string }
 * 返回参数：{ permissions: Array<Permission> }
 * url地址：/api/v1/permissions/
 * 请求方式：GET
 */
export async function getAllPermissions(params = {}) {
  const response = await request({
    url: '/api/v1/permissions/',
    method: 'get',
    params
  })
  return response
}

/**
 * 获取权限分类
 * 功能描述：获取权限的分类列表
 * 入参：无
 * 返回参数：{ categories: Array<string> }
 * url地址：/api/v1/permissions/categories
 * 请求方式：GET
 */
export async function getPermissionCategories() {
  const response = await request({
    url: '/api/v1/permissions/categories',
    method: 'get'
  })
  return response
}

/**
 * 获取当前用户权限
 * 功能描述：获取当前登录用户的所有权限
 * 入参：无
 * 返回参数：{ permissions: Array<Permission> }
 * url地址：/api/v1/permissions/my-permissions
 * 请求方式：GET
 */
export async function getMyPermissions() {
  const response = await request({
    url: '/api/v1/permissions/my-permissions',
    method: 'get'
  })
  return response
}

export const permissionsApi = {
  getAllPermissions,
  getPermissionCategories,
  getMyPermissions
}