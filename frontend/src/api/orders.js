import request from '@/utils/request'

/**
 * 获取订单列表
 * 功能描述：获取用户的订单列表，支持筛选和分页
 * 入参：{ status: string, type: string, date_from: string, date_to: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, order_number, material_info, supplier_info, status, total_amount, created_at }] }
 * url地址：/orders
 * 请求方式：GET
 */
export function getOrders(params) {
  return request({
    url: '/orders/',
    method: 'get',
    params
  })
}

/**
 * 获取订单详情
 * 功能描述：获取指定订单的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, order_number, buyer_info, supplier_info, material_info, quantity, unit_price, total_amount, status, delivery_info, created_at, updated_at }
 * url地址：/orders/{id}
 * 请求方式：GET
 */
export function getOrderDetail(id) {
  return request({
    url: `/orders/${id}/`,
    method: 'get'
  })
}

/**
 * 创建订单
 * 功能描述：创建新的采购订单
 * 入参：{ material_id: number, supplier_id: number, quantity: number, delivery_address: string, delivery_date: string, special_requirements: string }
 * 返回参数：{ order_id, order_number, message }
 * url地址：/orders
 * 请求方式：POST
 */
export function createOrder(orderData) {
  return request({
    url: '/orders/',
    method: 'post',
    data: orderData
  })
}

/**
 * 更新订单状态
 * 功能描述：更新订单的状态信息
 * 入参：{ id: number, status: string, reason: string, notes: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/status
 * 请求方式：PUT
 */
export function updateOrderStatus(id, statusData) {
  return request({
    url: `/orders/${id}/status/`,
    method: 'put',
    data: statusData
  })
}

/**
 * 取消订单
 * 功能描述：取消指定的订单
 * 入参：{ id: number, reason: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/cancel
 * 请求方式：POST
 */
export function cancelOrder(id, reason) {
  return request({
    url: `/orders/${id}/cancel/`,
    method: 'post',
    data: { reason }
  })
}

/**
 * 获取订单统计
 * 功能描述：获取用户的订单统计数据
 * 入参：无
 * 返回参数：{ pending_count, processing_count, shipping_count, completed_count, total_amount }
 * url地址：/orders/statistics
 * 请求方式：GET
 */
export function getOrderStatistics() {
  return request({
    url: '/orders/statistics/',
    method: 'get'
  })
}

/**
 * 审批订单
 * 功能描述：审批订单
 * 入参：{ id: number, action: string, approval_level: number, comments: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/approve
 * 请求方式：POST
 */
export function approveOrder(id, approvalData) {
  return request({
    url: `/orders/${id}/approve/`,
    method: 'post',
    data: approvalData
  })
}

/**
 * 获取订单状态历史
 * 功能描述：获取订单的状态变更历史
 * 入参：{ id: number }
 * 返回参数：状态历史列表
 * url地址：/orders/{id}/history
 * 请求方式：GET
 */
export function getOrderHistory(id) {
  return request({
    url: `/orders/${id}/history/`,
    method: 'get'
  })
}

export const ordersApi = {
  getOrders,
  getOrderDetail,
  createOrder,
  updateOrderStatus,
  cancelOrder,
  getOrderStatistics,
  approveOrder,
  getOrderHistory
}