/**
 * 航材共享保障平台 - 门户首页API
 * 功能描述：门户首页专业功能接口，包括行业动态、热门航材推荐、平台统计等
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-18
 */

import { get, post } from '@/utils/request'
import mockManager from './mockManager'

/**
 * 获取平台统计数据
 * 功能描述：获取平台核心统计数据，用于首页展示
 * 入参：无
 * 返回参数：{
 *   total_users: 1500,
 *   total_materials: 50000,
 *   total_orders: 8000,
 *   total_transaction_amount: 500000000,
 *   monthly_growth: 15.5,
 *   active_suppliers: 200,
 *   completed_orders: 7500,
 *   avg_response_time: 2.5
 * }
 * url地址：/api/portal/statistics
 * 请求方式：GET
 */
export async function getPlatformStatistics() {
  try {
    // 优先使用真实API
    return await get('/portal/statistics')
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getPlatformStatistics')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取热门航材推荐
 * 功能描述：基于搜索频率和交易量获取热门航材
 * 入参：{ limit: 10 }
 * 返回参数：{
 *   materials: [
 *     {
 *       id: 1,
 *       part_number: "CFM56-001",
 *       name: "发动机燃油喷嘴",
 *       category: "发动机",
 *       manufacturer: "CFM International",
 *       search_count: 1250,
 *       order_count: 85,
 *       avg_price: 125000,
 *       availability: "现货",
 *       image_url: "/images/发动机燃油喷嘴.jpg",
 *       hot_score: 95.5
 *     }
 *   ]
 * }
 * url地址：/api/portal/hot-materials
 * 请求方式：GET
 */
export async function getHotMaterials(params) {
  try {
    // 优先使用真实API
    return await get('/portal/hot-materials', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getHotMaterials')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取行业动态新闻
 * 功能描述：获取航空行业最新新闻和动态
 * 入参：{ page: 1, per_page: 10, category: "industry" }
 * 返回参数：{
 *   news: [
 *     {
 *       id: 1,
 *       title: "中国商飞C919获得欧洲航空安全局认证",
 *       summary: "中国商飞C919客机正式获得欧洲航空安全局（EASA）的型号认证，标志着中国民用航空制造业的重要里程碑。",
 *       category: "industry",
 *       source: "航空工业周刊",
 *       publish_date: "2025-07-18",
 *       image_url: "/images/飞机6.jpg",
 *       read_count: 1250,
 *       importance: "high"
 *     }
 *   ],
 *   total: 50,
 *   page: 1,
 *   per_page: 10
 * }
 * url地址：/api/portal/industry-news
 * 请求方式：GET
 */
export async function getIndustryNews(params) {
  try {
    // 优先使用真实API
    return await get('/portal/industry-news', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getIndustryNews')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取成功案例
 * 功能描述：获取平台促成的典型交易案例
 * 入参：{ limit: 6 }
 * 返回参数：{
 *   cases: [
 *     {
 *       id: 1,
 *       title: "东方航空紧急AOG航材保障",
 *       description: "在48小时内为东方航空提供紧急航材，确保航班正常运行。",
 *       client: "东方航空",
 *       material_type: "发动机部件",
 *       save_cost: 1200000,
 *       response_time: "48小时",
 *       success_rate: "100%",
 *       image_url: "/images/发动机2.jpg",
 *       case_type: "aog"
 *     }
 *   ]
 * }
 * url地址：/api/portal/success-cases
 * 请求方式：GET
 */
export async function getSuccessCases(params) {
  try {
    // 优先使用真实API
    return await get('/portal/success-cases', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getSuccessCases')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取市场分析数据
 * 功能描述：获取航材市场趋势分析数据
 * 入参：{ period: "monthly" }
 * 返回参数：{
 *   analysis: {
 *     market_size: 58500000000,
 *     growth_rate: 12.5,
 *     hot_categories: ["发动机", "航电", "起落架"],
 *     regional_data: [],
 *     price_trends: [],
 *     demand_forecast: []
 *   }
 * }
 * url地址：/api/portal/market-analysis
 * 请求方式：GET
 */
export async function getMarketAnalysis(params) {
  try {
    // 优先使用真实API
    return await get('/portal/market-analysis', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMarketAnalysis')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}