import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 获取材料列表
 * 功能描述：获取平台中的材料列表，支持分页和筛选
 * 入参：{ page: number, per_page: number, search: string, category: string, manufacturer: string }
 * 返回参数：{ materials: [], total: number, page: number, per_page: number, pages: number }
 * url地址：/api/materials
 * 请求方式：GET
 */
export async function getMaterials(params) {
  try {
    // 优先使用真实API
    return await request({
      url: '/api/materials',
      method: 'get',
      params
    })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMaterials')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 航材搜索
 * 功能描述：根据关键词和筛选条件搜索航材
 * 入参：{ q: string, category: string, oem: string, condition: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, part_number, part_name, manufacturer, category, price_range, images, status }] }
 * url地址：/materials/search
 * 请求方式：GET
 */
export async function searchMaterials(params) {
  try {
    // 优先使用真实API
    return await request({
      url: '/materials/search',
      method: 'get',
      params
    })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMaterials')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取航材详情
 * 功能描述：获取指定航材的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, part_number, part_name, manufacturer, category, description, specifications, images }
 * url地址：/materials/{id}
 * 请求方式：GET
 */
export function getMaterialDetail(id) {
  return request({
    url: `/materials/${id}`,
    method: 'get'
  })
}

/**
 * 获取航材分类
 * 功能描述：获取所有航材分类列表
 * 入参：无
 * 返回参数：[{ id, name, parent_id, children }]
 * url地址：/materials/categories
 * 请求方式：GET
 */
export function getMaterialCategories() {
  return request({
    url: '/materials/categories',
    method: 'get'
  })
}

/**
 * 获取制造商列表
 * 功能描述：获取所有制造商列表
 * 入参：无
 * 返回参数：[{ id, name, country }]
 * url地址：/materials/manufacturers
 * 请求方式：GET
 */
export function getManufacturers() {
  return request({
    url: '/materials/manufacturers',
    method: 'get'
  })
}

/**
 * 创建询价
 * 功能描述：对航材创建询价请求
 * 入参：{ material_id: number, quantity: number, target_price: number, requirements: string }
 * 返回参数：{ inquiry_id, message }
 * url地址：/inquiries
 * 请求方式：POST
 */
export function createInquiry(inquiryData) {
  return request({
    url: '/inquiries',
    method: 'post',
    data: inquiryData
  })
}

export const materialsApi = {
  getMaterials,
  searchMaterials,
  getMaterialDetail,
  getMaterialCategories,
  getManufacturers,
  createInquiry
}