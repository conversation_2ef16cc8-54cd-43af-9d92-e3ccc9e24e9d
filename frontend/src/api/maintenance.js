import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 获取维修工单列表
 * 功能描述：获取维修工单列表，支持筛选和分页
 * 入参：{ status: string, priority: string, aircraft_type: string, technician: string, page: number, size: number }
 * 返回参数：{ work_orders: array, total: number, statistics: object, pending_count: number }
 * url地址：/maintenance/work-orders
 * 请求方式：GET
 */
export async function getWorkOrders(params = {}) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/maintenance/work-orders',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkOrders')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 创建维修工单
 * 功能描述：创建新的维修工单
 * 入参：{ aircraft_info: object, fault_description: string, priority: string, required_parts: array, estimated_hours: number }
 * 返回参数：{ work_order_id: string, order_number: string, assigned_technician: object, estimated_completion: string }
 * url地址：/maintenance/work-orders
 * 请求方式：POST
 */
export function createWorkOrder(workOrderData) {
  return request({
    url: '/maintenance/work-orders',
    method: 'post',
    data: workOrderData
  })
}

/**
 * 获取工单详情
 * 功能描述：获取指定维修工单的详细信息
 * 入参：{ work_order_id: string }
 * 返回参数：{ work_order: object, progress_history: array, parts_used: array, labor_records: array }
 * url地址：/maintenance/work-orders/{id}
 * 请求方式：GET
 */
export async function getWorkOrderDetails(workOrderId) {
  // 检查是否有Mock数据
  const mockData = await mockManager.getMockData('getWorkOrderDetails')
  if (mockData) {
    return mockData
  }
  
  return request({
    url: `/maintenance/work-orders/${workOrderId}`,
    method: 'get'
  })
}

/**
 * 更新工单状态
 * 功能描述：更新维修工单的状态和进度
 * 入参：{ work_order_id: string, status: string, progress: number, notes: string, technician: string }
 * 返回参数：{ message: string, updated_status: object, next_milestone: string }
 * url地址：/maintenance/work-orders/{id}/status
 * 请求方式：POST
 */
export function updateWorkOrderStatus(workOrderId, statusData) {
  return request({
    url: `/maintenance/work-orders/${workOrderId}/status`,
    method: 'post',
    data: {
      action: 'update_status',
      ...statusData
    }
  })
}

/**
 * 分配维修技师
 * 功能描述：为维修工单分配技术人员
 * 入参：{ work_order_id: string, technician_id: string, skill_requirements: array, priority_adjustment: string }
 * 返回参数：{ message: string, assigned_technician: object, estimated_start_time: string }
 * url地址：/maintenance/work-orders/{id}/assign
 * 请求方式：POST
 */
export function assignTechnician(workOrderId, assignmentData) {
  return request({
    url: `/maintenance/work-orders/${workOrderId}/assign`,
    method: 'post',
    data: assignmentData
  })
}

/**
 * 记录工时
 * 功能描述：记录维修工单的工时和进度
 * 入参：{ work_order_id: string, technician_id: string, start_time: string, end_time: string, work_description: string }
 * 返回参数：{ labor_record_id: string, total_hours: number, remaining_hours: number }
 * url地址：/maintenance/work-orders/{id}/labor
 * 请求方式：POST
 */
export function recordLaborTime(workOrderId, laborData) {
  return request({
    url: `/maintenance/work-orders/${workOrderId}/labor`,
    method: 'post',
    data: laborData
  })
}

/**
 * 请求零件
 * 功能描述：为维修工单申请所需零件
 * 入参：{ work_order_id: string, parts_request: array, urgency: string, justification: string }
 * 返回参数：{ request_id: string, approved_parts: array, pending_approval: array, estimated_delivery: string }
 * url地址：/maintenance/work-orders/{id}/parts-request
 * 请求方式：POST
 */
export function requestParts(workOrderId, partsData) {
  return request({
    url: `/maintenance/work-orders/${workOrderId}/parts-request`,
    method: 'post',
    data: partsData
  })
}

/**
 * 获取维修计划
 * 功能描述：获取维修计划和排程信息
 * 入参：{ time_range: string, aircraft_id: string, maintenance_type: string, facility: string }
 * 返回参数：{ scheduled_maintenance: array, resource_allocation: object, capacity_utilization: number }
 * url地址：/maintenance/schedule
 * 请求方式：GET
 */
export async function getMaintenanceSchedule(params = {}) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/maintenance/schedule',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMaintenanceSchedule')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 创建维修计划
 * 功能描述：创建新的维修计划
 * 入参：{ aircraft_id: string, maintenance_type: string, scheduled_date: string, estimated_duration: number, required_resources: array }
 * 返回参数：{ schedule_id: string, confirmation_status: string, resource_conflicts: array }
 * url地址：/maintenance/schedule
 * 请求方式：POST
 */
export function createMaintenanceSchedule(scheduleData) {
  return request({
    url: '/maintenance/schedule',
    method: 'post',
    data: scheduleData
  })
}

/**
 * 获取技师信息
 * 功能描述：获取维修技师信息和技能清单
 * 入参：{ skill_category: string, certification_level: string, availability: boolean }
 * 返回参数：{ technicians: array, skill_matrix: object, availability_schedule: array }
 * url地址：/maintenance/technicians
 * 请求方式：GET
 */
export async function getTechnicians(params = {}) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/maintenance/technicians',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getTechnicians')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 更新技师技能
 * 功能描述：更新维修技师的技能认证信息
 * 入参：{ technician_id: string, skills: array, certifications: array, training_records: array }
 * 返回参数：{ message: string, updated_skills: object, certification_status: array }
 * url地址：/maintenance/technicians/{id}/skills
 * 请求方式：POST
 */
export function updateTechnicianSkills(technicianId, skillsData) {
  return request({
    url: `/maintenance/technicians/${technicianId}/skills`,
    method: 'post',
    data: {
      action: 'update_skills',
      ...skillsData
    }
  })
}

/**
 * 获取维修统计
 * 功能描述：获取维修相关的统计数据和KPI
 * 入参：{ time_range: string, aircraft_type: string, maintenance_type: string }
 * 返回参数：{ completion_rate: number, avg_repair_time: number, cost_analysis: object, efficiency_trends: array }
 * url地址：/maintenance/statistics
 * 请求方式：GET
 */
export async function getMaintenanceStatistics(params = {}) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/maintenance/statistics',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMaintenanceStatistics')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 完成维修工单
 * 功能描述：完成维修工单并记录结果
 * 入参：{ work_order_id: string, completion_notes: string, parts_consumed: array, final_inspection: object, test_results: object }
 * 返回参数：{ completion_status: string, quality_report: object, aircraft_release_status: string }
 * url地址：/maintenance/work-orders/{id}/complete
 * 请求方式：POST
 */
export function completeWorkOrder(workOrderId, completionData) {
  return request({
    url: `/maintenance/work-orders/${workOrderId}/complete`,
    method: 'post',
    data: completionData
  })
}

export const maintenanceApi = {
  getWorkOrders,
  createWorkOrder,
  getWorkOrderDetails,
  updateWorkOrderStatus,
  assignTechnician,
  recordLaborTime,
  requestParts,
  getMaintenanceSchedule,
  createMaintenanceSchedule,
  getTechnicians,
  updateTechnicianSkills,
  getMaintenanceStatistics,
  completeWorkOrder
}