import request from '@/utils/request'

/**
 * 创建物流跟踪
 * 功能描述：为订单创建物流跟踪记录
 * 入参：{ order_id: string, carrier: string, tracking_number: string, origin: string, destination: string, estimated_delivery: string }
 * 返回参数：{ tracking_id: string, message: string, initial_status: object }
 * url地址：/logistics/create
 * 请求方式：POST
 */
export function createLogisticsTracking(trackingData) {
  return request({
    url: '/logistics/create',
    method: 'post',
    data: trackingData
  })
}

/**
 * 获取物流跟踪详情
 * 功能描述：获取指定物流跟踪的详细信息和状态历史
 * 入参：{ tracking_id: string }
 * 返回参数：{ tracking: object, status_history: array, current_location: object, estimated_delivery: string }
 * url地址：/logistics/{id}
 * 请求方式：GET
 */
export function getLogisticsDetails(trackingId) {
  return request({
    url: `/logistics/${trackingId}`,
    method: 'get'
  })
}

/**
 * 更新物流状态
 * 功能描述：更新物流跟踪的状态信息
 * 入参：{ tracking_id: string, status: string, location: string, timestamp: string, notes: string, proof_images: array }
 * 返回参数：{ message: string, updated_status: object, next_milestone: string }
 * url地址：/logistics/{id}/update-status
 * 请求方式：POST
 */
export function updateLogisticsStatus(trackingId, statusData) {
  return request({
    url: `/logistics/${trackingId}/update-status`,
    method: 'post',
    data: statusData
  })
}

/**
 * 批量查询物流状态
 * 功能描述：批量查询多个订单的物流状态
 * 入参：{ order_ids: array, tracking_numbers: array }
 * 返回参数：{ logistics: array, summary: object }
 * url地址：/logistics/batch-query
 * 请求方式：POST
 */
export function batchQueryLogistics(queryData) {
  return request({
    url: '/logistics/batch-query',
    method: 'post',
    data: queryData
  })
}

/**
 * 获取订单物流信息
 * 功能描述：根据订单ID获取关联的物流跟踪信息
 * 入参：{ order_id: string }
 * 返回参数：{ logistics: array, active_tracking: object, delivery_status: string }
 * url地址：/logistics/by-order/{order_id}
 * 请求方式：GET
 */
export function getLogisticsByOrder(orderId) {
  return request({
    url: `/logistics/by-order/${orderId}`,
    method: 'get'
  })
}

/**
 * 获取承运商列表
 * 功能描述：获取系统支持的承运商信息
 * 入参：{ active_only: boolean, region: string }
 * 返回参数：{ carriers: array, supported_services: object }
 * url地址：/logistics/carriers
 * 请求方式：GET
 */
export function getCarriers(params = {}) {
  return request({
    url: '/logistics/carriers',
    method: 'get',
    params
  })
}

/**
 * 预计送达时间
 * 功能描述：根据起点终点和承运商预估送达时间
 * 入参：{ origin: string, destination: string, carrier: string, service_type: string, weight: number }
 * 返回参数：{ estimated_delivery: string, transit_time: number, service_options: array }
 * url地址：/logistics/estimate-delivery
 * 请求方式：POST
 */
export function estimateDeliveryTime(estimateData) {
  return request({
    url: '/logistics/estimate-delivery',
    method: 'post',
    data: estimateData
  })
}

/**
 * 生成运单
 * 功能描述：为物流跟踪生成电子运单
 * 入参：{ tracking_id: string, template: string }
 * 返回参数：{ waybill_url: string, waybill_number: string, qr_code: string }
 * url地址：/logistics/{id}/generate-waybill
 * 请求方式：POST
 */
export function generateWaybill(trackingId, templateData = {}) {
  return request({
    url: `/logistics/${trackingId}/generate-waybill`,
    method: 'post',
    data: templateData
  })
}

/**
 * 获取物流统计
 * 功能描述：获取物流运输统计数据
 * 入参：{ time_range: string, carrier: string, status: string }
 * 返回参数：{ total_shipments: number, on_time_rate: number, avg_delivery_time: number, stats_by_carrier: object }
 * url地址：/logistics/statistics
 * 请求方式：GET
 */
export function getLogisticsStatistics(params = {}) {
  return request({
    url: '/logistics/statistics',
    method: 'get',
    params
  })
}

/**
 * 物流异常处理
 * 功能描述：报告和处理物流运输异常
 * 入参：{ tracking_id: string, exception_type: string, description: string, resolution: string, images: array }
 * 返回参数：{ exception_id: string, message: string, resolution_steps: array }
 * url地址：/logistics/{id}/report-exception
 * 请求方式：POST
 */
export function reportLogisticsException(trackingId, exceptionData) {
  return request({
    url: `/logistics/${trackingId}/report-exception`,
    method: 'post',
    data: exceptionData
  })
}

export const logisticsApi = {
  createLogisticsTracking,
  getLogisticsDetails,
  updateLogisticsStatus,
  batchQueryLogistics,
  getLogisticsByOrder,
  getCarriers,
  estimateDeliveryTime,
  generateWaybill,
  getLogisticsStatistics,
  reportLogisticsException
}