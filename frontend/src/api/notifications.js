import request from '@/utils/request'

/**
 * 获取通知列表
 * 功能描述：获取用户的通知消息列表，支持分类和筛选
 * 入参：{ type: string, read: boolean, priority: string, page: number, size: number }
 * 返回参数：{ notifications: array, total: number, unread_count: number }
 * url地址：/notifications
 * 请求方式：GET
 */
export function getNotifications(params = {}) {
  return request({
    url: '/notifications/',
    method: 'get',
    params
  })
}

/**
 * 标记通知已读
 * 功能描述：将指定通知标记为已读状态
 * 入参：{ notification_id: string }
 * 返回参数：{ message: string, updated: boolean }
 * url地址：/notifications/{id}/read
 * 请求方式：POST
 */
export function markNotificationAsRead(notificationId) {
  return request({
    url: `/notifications/${notificationId}/read/`,
    method: 'post',
    data: { action: 'mark_as_read' }
  })
}

/**
 * 批量标记已读
 * 功能描述：批量将多个通知标记为已读
 * 入参：{ notification_ids: array }
 * 返回参数：{ message: string, updated_count: number }
 * url地址：/notifications/batch-read
 * 请求方式：POST
 */
export function batchMarkAsRead(notificationIds) {
  return request({
    url: '/notifications/batch-read/',
    method: 'post',
    data: { 
      action: 'batch_mark_as_read',
      notification_ids: notificationIds 
    }
  })
}

/**
 * 全部标记已读
 * 功能描述：将用户所有未读通知标记为已读
 * 入参：无
 * 返回参数：{ message: string, updated_count: number }
 * url地址：/notifications/mark-all-read
 * 请求方式：POST
 */
export function markAllAsRead() {
  return request({
    url: '/notifications/mark-all-read/',
    method: 'post',
    data: { action: 'mark_all_as_read' }
  })
}

/**
 * 发送通知
 * 功能描述：系统内部发送通知给指定用户
 * 入参：{ recipient_ids: array, type: string, title: string, content: string, priority: string, metadata: object }
 * 返回参数：{ message: string, notification_id: string, sent_count: number }
 * url地址：/notifications/send
 * 请求方式：POST
 */
export function sendNotification(notificationData) {
  return request({
    url: '/notifications/send/',
    method: 'post',
    data: notificationData
  })
}

/**
 * 删除通知
 * 功能描述：删除指定的通知消息
 * 入参：{ notification_id: string }
 * 返回参数：{ message: string, deleted: boolean }
 * url地址：/notifications/{id}
 * 请求方式：POST
 */
export function deleteNotification(notificationId) {
  return request({
    url: `/notifications/${notificationId}/`,
    method: 'post',
    data: { action: 'delete' }
  })
}

/**
 * 获取通知设置
 * 功能描述：获取用户的通知偏好设置
 * 入参：无
 * 返回参数：{ settings: object, channels: array }
 * url地址：/notifications/settings
 * 请求方式：GET
 */
export function getNotificationSettings() {
  return request({
    url: '/notifications/settings/',
    method: 'get'
  })
}

/**
 * 更新通知设置
 * 功能描述：更新用户的通知偏好设置
 * 入参：{ email_enabled: boolean, sms_enabled: boolean, push_enabled: boolean, notification_types: array }
 * 返回参数：{ message: string, settings: object }
 * url地址：/notifications/settings
 * 请求方式：POST
 */
export function updateNotificationSettings(settings) {
  return request({
    url: '/notifications/settings/',
    method: 'post',
    data: {
      action: 'update_settings',
      ...settings
    }
  })
}

/**
 * 获取通知统计
 * 功能描述：获取通知发送和阅读统计数据
 * 入参：{ time_range: string, type: string }
 * 返回参数：{ total_sent: number, total_read: number, read_rate: number, stats_by_type: object }
 * url地址：/notifications/statistics
 * 请求方式：GET
 */
export function getNotificationStatistics(params = {}) {
  return request({
    url: '/notifications/statistics/',
    method: 'get',
    params
  })
}

/**
 * 获取实时通知
 * 功能描述：获取最新的实时通知（用于轮询或WebSocket）
 * 入参：{ last_check: string }
 * 返回参数：{ notifications: array, has_new: boolean, last_updated: string }
 * url地址：/notifications/realtime
 * 请求方式：GET
 */
export function getRealtimeNotifications(params = {}) {
  return request({
    url: '/notifications/realtime/',
    method: 'get',
    params
  })
}

export const notificationsApi = {
  getNotifications,
  markNotificationAsRead,
  batchMarkAsRead,
  markAllAsRead,
  sendNotification,
  deleteNotification,
  getNotificationSettings,
  updateNotificationSettings,
  getNotificationStatistics,
  getRealtimeNotifications
}