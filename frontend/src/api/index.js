/**
 * API统一导出文件 - 优化版本
 * 提供统一的API接口导入入口，支持新旧版本切换
 * 
 * 使用方式：
 * import { getDemands, getSharedMaterials } from '@/api'
 * 或
 * import * as api from '@/api'
 * 
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { environment } from '@/config/environment.js'

// 导入优化版API（默认）
export * from './demandsOptimized.js'
export * from './sharedMaterialsOptimized.js'
export * from './ordersOptimized.js'
export * from './inventoryOptimized.js'
export * from './materialsOptimized.js'
export * from './notificationsOptimized.js'

// 导入其他未优化的API
export * from './auth.js'
export * from './aog.js'
export * from './maintenance.js'
export * from './logistics.js'
export * from './workflow.js'
export * from './analytics.js'
export * from './quality.js'
export * from './portal.js'

// 数据提供器
export { dataProvider, mockController } from '@/config/environment.js'

// 兼容性API集合（保持向后兼容）
import { demandsApi } from './demandsOptimized.js'
import { ordersApi } from './ordersOptimized.js'
import { inventoryApi } from './inventoryOptimized.js'
import { materialsApi } from './materialsOptimized.js'
import { notificationsApi } from './notificationsOptimized.js'

export const api = {
  demands: demandsApi,
  orders: ordersApi,
  inventory: inventoryApi,
  materials: materialsApi,
  notifications: notificationsApi
}

// 开发环境下的调试工具
if (environment.isDevelopment) {
  window.api = api
  console.log('[API] 优化版API已加载，可通过 window.api 访问')
}

export default api