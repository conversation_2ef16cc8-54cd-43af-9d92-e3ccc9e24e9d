/**
 * 通知管理API - 优化版本
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 * 
 * 功能描述：通知查询、标记已读、设置管理、实时通知等功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 获取通知列表
 * 功能描述：获取用户的通知消息列表，支持分类和筛选
 * 入参：{ type: string, read: boolean, priority: string, page: number, size: number }
 * 返回参数：{ notifications: array, total: number, unread_count: number }
 * url地址：/notifications
 * 请求方式：GET
 */
export async function getNotifications(params = {}) {
  try {
    return await dataProvider.getData('/notifications/', params, {
      useCache: true,
      cacheTtl: 1 * 60 * 1000, // 1分钟缓存（通知数据更新频繁）
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取通知列表失败:', error)
    throw error
  }
}

/**
 * 标记通知已读
 * 功能描述：将指定通知标记为已读状态
 * 入参：{ notification_id: string }
 * 返回参数：{ message: string, updated: boolean }
 * url地址：/notifications/{id}/read
 * 请求方式：POST
 */
export async function markNotificationAsRead(notificationId) {
  try {
    const response = await request({
      url: `/notifications/${notificationId}/read/`,
      method: 'post',
      data: { action: 'mark_as_read' }
    })
    
    // 清除通知相关缓存
    dataProvider.clearCache()
    
    // 更新动态数据
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      const notifications = mockManager.dynamicData.notifications.map(notification => {
        if (notification.id === parseInt(notificationId)) {
          return { ...notification, is_read: true, read_at: new Date().toISOString() }
        }
        return notification
      })
      mockManager.dynamicData.notifications = notifications
      mockManager.saveDynamicData('notifications', notifications)
    }
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 标记通知已读失败:', error)
    throw error
  }
}

/**
 * 批量标记已读
 * 功能描述：批量将多个通知标记为已读
 * 入参：{ notification_ids: array }
 * 返回参数：{ message: string, updated_count: number }
 * url地址：/notifications/batch-read
 * 请求方式：POST
 */
export async function batchMarkAsRead(notificationIds) {
  try {
    const response = await request({
      url: '/notifications/batch-read/',
      method: 'post',
      data: { 
        action: 'batch_mark_as_read',
        notification_ids: notificationIds 
      }
    })
    
    // 清除通知相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 批量标记已读失败:', error)
    throw error
  }
}

/**
 * 全部标记已读
 * 功能描述：将用户所有未读通知标记为已读
 * 入参：无
 * 返回参数：{ message: string, updated_count: number }
 * url地址：/notifications/mark-all-read
 * 请求方式：POST
 */
export async function markAllAsRead() {
  try {
    const response = await request({
      url: '/notifications/mark-all-read/',
      method: 'post',
      data: { action: 'mark_all_as_read' }
    })
    
    // 清除通知相关缓存
    dataProvider.clearCache()
    
    // 更新动态数据
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      const notifications = mockManager.dynamicData.notifications.map(notification => ({
        ...notification, 
        is_read: true, 
        read_at: new Date().toISOString()
      }))
      mockManager.dynamicData.notifications = notifications
      mockManager.saveDynamicData('notifications', notifications)
    }
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 全部标记已读失败:', error)
    throw error
  }
}

/**
 * 发送通知
 * 功能描述：系统内部发送通知给指定用户
 * 入参：{ recipient_ids: array, type: string, title: string, content: string, priority: string, metadata: object }
 * 返回参数：{ message: string, notification_id: string, sent_count: number }
 * url地址：/notifications/send
 * 请求方式：POST
 */
export async function sendNotification(notificationData) {
  try {
    const response = await request({
      url: '/notifications/send/',
      method: 'post',
      data: notificationData
    })
    
    // 清除通知相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 发送通知失败:', error)
    throw error
  }
}

/**
 * 删除通知
 * 功能描述：删除指定的通知消息
 * 入参：{ notification_id: string }
 * 返回参数：{ message: string, deleted: boolean }
 * url地址：/notifications/{id}
 * 请求方式：POST
 */
export async function deleteNotification(notificationId) {
  try {
    const response = await request({
      url: `/notifications/${notificationId}/`,
      method: 'post',
      data: { action: 'delete' }
    })
    
    // 清除缓存并更新动态数据
    dataProvider.clearCache()
    
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      // 从动态数据中移除
      const notifications = mockManager.dynamicData.notifications.filter(
        n => n.id !== parseInt(notificationId)
      )
      mockManager.dynamicData.notifications = notifications
      mockManager.saveDynamicData('notifications', notifications)
    }
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 删除通知失败:', error)
    throw error
  }
}

/**
 * 获取通知设置
 * 功能描述：获取用户的通知偏好设置
 * 入参：无
 * 返回参数：{ settings: object, channels: array }
 * url地址：/notifications/settings
 * 请求方式：GET
 */
export async function getNotificationSettings() {
  try {
    return await dataProvider.getData('/notifications/settings/', {}, {
      useCache: true,
      cacheTtl: 15 * 60 * 1000, // 15分钟缓存（设置数据相对稳定）
      cacheFirst: true, // 优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取通知设置失败:', error)
    throw error
  }
}

/**
 * 更新通知设置
 * 功能描述：更新用户的通知偏好设置
 * 入参：{ email_enabled: boolean, sms_enabled: boolean, push_enabled: boolean, notification_types: array }
 * 返回参数：{ message: string, settings: object }
 * url地址：/notifications/settings
 * 请求方式：POST
 */
export async function updateNotificationSettings(settings) {
  try {
    const response = await request({
      url: '/notifications/settings/',
      method: 'post',
      data: {
        action: 'update_settings',
        ...settings
      }
    })
    
    // 清除设置相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 更新通知设置失败:', error)
    throw error
  }
}

/**
 * 获取通知统计
 * 功能描述：获取通知发送和阅读统计数据
 * 入参：{ time_range: string, type: string }
 * 返回参数：{ total_sent: number, total_read: number, read_rate: number, stats_by_type: object }
 * url地址：/notifications/statistics
 * 请求方式：GET
 */
export async function getNotificationStatistics(params = {}) {
  try {
    return await dataProvider.getData('/notifications/statistics/', params, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取通知统计失败:', error)
    throw error
  }
}

/**
 * 获取实时通知
 * 功能描述：获取最新的实时通知（用于轮询或WebSocket）
 * 入参：{ last_check: string }
 * 返回参数：{ notifications: array, has_new: boolean, last_updated: string }
 * url地址：/notifications/realtime
 * 请求方式：GET
 */
export async function getRealtimeNotifications(params = {}) {
  try {
    // 实时通知不使用缓存，确保数据的实时性
    return await dataProvider.getData('/notifications/realtime/', params, {
      useCache: false,
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取实时通知失败:', error)
    throw error
  }
}

/**
 * 获取未读通知数量
 * 功能描述：获取用户的未读通知数量
 * 入参：无
 * 返回参数：{ unread_count: number }
 * url地址：/notifications/unread-count
 * 请求方式：GET
 */
export async function getUnreadCount() {
  try {
    return await dataProvider.getData('/notifications/unread-count/', {}, {
      useCache: true,
      cacheTtl: 30 * 1000, // 30秒缓存（频繁查询但变化不大）
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取未读通知数量失败:', error)
    throw error
  }
}

/**
 * 订阅通知
 * 功能描述：订阅特定类型的通知
 * 入参：{ type: string, target_id: number }
 * 返回参数：{ success: true, message: "订阅成功" }
 * url地址：/notifications/subscribe
 * 请求方式：POST
 */
export async function subscribeNotification(subscriptionData) {
  try {
    const response = await request({
      url: '/notifications/subscribe/',
      method: 'post',
      data: subscriptionData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 订阅通知失败:', error)
    throw error
  }
}

/**
 * 取消订阅通知
 * 功能描述：取消订阅特定类型的通知
 * 入参：{ type: string, target_id: number }
 * 返回参数：{ success: true, message: "取消订阅成功" }
 * url地址：/notifications/unsubscribe
 * 请求方式：POST
 */
export async function unsubscribeNotification(subscriptionData) {
  try {
    const response = await request({
      url: '/notifications/unsubscribe/',
      method: 'post',
      data: subscriptionData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[NotificationsAPI] 取消订阅通知失败:', error)
    throw error
  }
}

/**
 * 获取通知模板
 * 功能描述：获取可用的通知模板列表
 * 入参：{ category: string }
 * 返回参数：{ templates: [...] }
 * url地址：/notifications/templates
 * 请求方式：GET
 */
export async function getNotificationTemplates(params = {}) {
  try {
    return await dataProvider.getData('/notifications/templates/', params, {
      useCache: true,
      cacheTtl: 60 * 60 * 1000, // 1小时缓存（模板数据变化很少）
      cacheFirst: true, // 优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[NotificationsAPI] 获取通知模板失败:', error)
    throw error
  }
}

// 导出API集合（保持向后兼容）
export const notificationsApi = {
  getNotifications,
  markNotificationAsRead,
  batchMarkAsRead,
  markAllAsRead,
  sendNotification,
  deleteNotification,
  getNotificationSettings,
  updateNotificationSettings,
  getNotificationStatistics,
  getRealtimeNotifications,
  getUnreadCount,
  subscribeNotification,
  unsubscribeNotification,
  getNotificationTemplates
}