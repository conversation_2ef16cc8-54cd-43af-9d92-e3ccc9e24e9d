import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 获取所有角色配置
 * 功能描述：获取系统中所有可用的角色配置信息
 * 入参：{ include_inactive: boolean }
 * 返回参数：{ roles: Array<Role> }
 * url地址：/api/v1/roles/
 * 请求方式：GET
 */
export async function getAllRoles(params = {}) {
  const response = await request({
    url: '/roles/',
    method: 'get',
    params
  })
  return response
}

/**
 * 获取公开角色配置（用于注册）
 * 功能描述：获取用于注册的公开角色列表，无需认证
 * 入参：无
 * 返回参数：{ roles: Array<Role> }
 * url地址：/api/v1/roles/public
 * 请求方式：GET
 */
export async function getPublicRoles() {
  const response = await request({
    url: '/roles/public',
    method: 'get'
  })
  return response
}

/**
 * 获取角色详情
 * 功能描述：获取指定角色的详细信息
 * 入参：{ roleId: number }
 * 返回参数：{ role: Role }
 * url地址：/api/v1/roles/{id}
 * 请求方式：GET
 */
export async function getRoleDetail(roleId) {
  const response = await request({
    url: `/roles/${roleId}`,
    method: 'get'
  })
  return response
}

/**
 * 创建新角色
 * 功能描述：创建一个新的系统角色
 * 入参：{ role_code, role_name, display_name, description, category, business_type, icon_name, theme_color }
 * 返回参数：{ role: Role }
 * url地址：/api/v1/roles/
 * 请求方式：POST
 */
export function createRole(roleData) {
  return request({
    url: '/roles/',
    method: 'post',
    data: roleData
  })
}

/**
 * 更新角色信息
 * 功能描述：更新指定角色的信息
 * 入参：{ roleId: number, roleData: object }
 * 返回参数：{ role: Role }
 * url地址：/api/v1/roles/{id}
 * 请求方式：PUT
 */
export function updateRole(roleId, roleData) {
  return request({
    url: `/roles/${roleId}`,
    method: 'put',
    data: roleData
  })
}

/**
 * 删除角色
 * 功能描述：删除指定的角色
 * 入参：{ roleId: number }
 * 返回参数：{ message: string }
 * url地址：/api/v1/roles/{id}
 * 请求方式：DELETE
 */
export function deleteRole(roleId) {
  return request({
    url: `/roles/${roleId}`,
    method: 'delete'
  })
}

/**
 * 获取角色权限
 * 功能描述：获取指定角色的权限列表
 * 入参：{ roleId: number }
 * 返回参数：{ permissions: Array<Permission> }
 * url地址：/api/v1/roles/{id}/permissions
 * 请求方式：GET
 */
export function getRolePermissions(roleId) {
  return request({
    url: `/roles/${roleId}/permissions`,
    method: 'get'
  })
}

/**
 * 更新角色权限
 * 功能描述：更新指定角色的权限配置
 * 入参：{ roleId: number, permissions: Array<number> }
 * 返回参数：{ message: string }
 * url地址：/api/v1/roles/{id}/permissions
 * 请求方式：PUT
 */
export function updateRolePermissions(roleId, permissions) {
  return request({
    url: `/roles/${roleId}/permissions`,
    method: 'put',
    data: { permissions }
  })
}

export const rolesApi = {
  getAllRoles,
  getPublicRoles,
  getRoleDetail,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  updateRolePermissions
}