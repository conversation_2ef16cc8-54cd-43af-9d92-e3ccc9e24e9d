/**
 * 智能数据提供器
 * 实现数据获取的优先级策略：真实API > 缓存 > Mock备用
 * 
 * 功能：
 * - API优先级管理
 * - 自动容错和降级
 * - 缓存管理
 * - Mock数据备用
 */

import { environment, mockController } from '@/config/environment.js'
import request from './request.js'

/**
 * 简单的内存缓存管理器
 */
class CacheManager {
  constructor() {
    this.cache = new Map()
    this.ttlMap = new Map() // TTL管理
    this.defaultTtl = 5 * 60 * 1000 // 5分钟默认缓存
  }
  
  /**
   * 生成缓存键
   */
  generateKey(endpoint, params = {}) {
    const paramsStr = JSON.stringify(params)
    return `${endpoint}:${paramsStr}`
  }
  
  /**
   * 设置缓存
   */
  set(endpoint, params, data, ttl = this.defaultTtl) {
    const key = this.generateKey(endpoint, params)
    const expireTime = Date.now() + ttl
    
    this.cache.set(key, data)
    this.ttlMap.set(key, expireTime)
  }
  
  /**
   * 获取缓存
   */
  get(endpoint, params) {
    const key = this.generateKey(endpoint, params)
    const expireTime = this.ttlMap.get(key)
    
    // 检查缓存是否过期
    if (!expireTime || Date.now() > expireTime) {
      this.cache.delete(key)
      this.ttlMap.delete(key)
      return null
    }
    
    return this.cache.get(key)
  }
  
  /**
   * 清除过期缓存
   */
  clearExpired() {
    const now = Date.now()
    for (const [key, expireTime] of this.ttlMap) {
      if (now > expireTime) {
        this.cache.delete(key)
        this.ttlMap.delete(key)
      }
    }
  }
  
  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
    this.ttlMap.clear()
  }
}

/**
 * 智能数据提供器类
 */
export class DataProvider {
  constructor() {
    this.cache = new CacheManager()
    this.config = environment.getApiConfig()
    this.logConfig = environment.getLogConfig()
    this.requestStats = {
      total: 0,
      success: 0,
      cached: 0,
      fallback: 0,
      failed: 0
    }
    
    // 定期清理缓存
    setInterval(() => this.cache.clearExpired(), 60000) // 每分钟清理一次
  }
  
  /**
   * 主要的数据获取方法
   * 实现优先级策略：API > 缓存 > Mock备用
   */
  async getData(endpoint, params = {}, options = {}) {
    const {
      useCache = true,
      cacheFirst = false,
      cacheTtl,
      enableFallback = this.config.enableFallback,
      retries = this.config.retries
    } = options
    
    this.requestStats.total++
    
    try {
      // 策略1：缓存优先模式（适用于静态数据）
      if (cacheFirst && useCache) {
        const cached = this.cache.get(endpoint, params)
        if (cached) {
          this.requestStats.cached++
          this.logRequest('cache', endpoint, params)
          return this.wrapResponse(cached, 'cache')
        }
      }
      
      // 策略2：尝试真实API
      const apiResponse = await this.callRealApi(endpoint, params, retries)
      
      // 缓存成功的API响应
      if (useCache && apiResponse.success) {
        this.cache.set(endpoint, params, apiResponse, cacheTtl)
      }
      
      this.requestStats.success++
      this.logRequest('api', endpoint, params, apiResponse)
      return apiResponse
      
    } catch (apiError) {
      this.logError('API调用失败', endpoint, apiError)
      
      // 策略3：尝试缓存降级
      if (useCache && !cacheFirst) {
        const cached = this.cache.get(endpoint, params)
        if (cached) {
          this.requestStats.cached++
          this.logRequest('cache-fallback', endpoint, params)
          console.warn(`[DataProvider] API失败，使用缓存数据: ${endpoint}`)
          return this.wrapResponse(cached, 'cache-fallback')
        }
      }
      
      // 策略4：Mock数据降级
      if (enableFallback) {
        try {
          const mockResponse = await this.callMockFallback(endpoint, params)
          this.requestStats.fallback++
          this.logRequest('mock-fallback', endpoint, params, mockResponse)
          console.warn(`[DataProvider] API失败，使用Mock备用数据: ${endpoint}`)
          return mockResponse
        } catch (mockError) {
          this.logError('Mock降级也失败', endpoint, mockError)
        }
      }
      
      // 所有策略都失败
      this.requestStats.failed++
      throw new Error(`数据获取失败: ${endpoint} - ${apiError.message}`)
    }
  }
  
  /**
   * 调用真实API
   */
  async callRealApi(endpoint, params, retries = 1) {
    let lastError
    
    for (let i = 0; i <= retries; i++) {
      try {
        if (i > 0) {
          // 重试延迟
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * i))
        }
        
        const response = await request.get(endpoint, { params })
        return response
        
      } catch (error) {
        lastError = error
        if (i === retries) {
          throw error
        }
        this.logError(`API调用失败，重试 ${i + 1}/${retries}`, endpoint, error)
      }
    }
    
    throw lastError
  }
  
  /**
   * 调用Mock备用数据
   */
  async callMockFallback(endpoint, params) {
    const mockManager = await mockController.getMockManager()
    
    if (!mockManager) {
      throw new Error('Mock管理器不可用')
    }
    
    // 根据endpoint映射到相应的Mock方法
    const mockResponse = await this.mapToMockMethod(mockManager, endpoint, params)
    return this.wrapResponse(mockResponse, 'mock')
  }
  
  /**
   * 将API端点映射到Mock方法
   */
  async mapToMockMethod(mockManager, endpoint, params) {
    // API端点到Mock方法的映射
    const endpointMap = {
      '/demands': () => mockManager.getDemandsList(params),
      '/shared-materials': () => mockManager.getSharedMaterialsList(params),
      '/orders': () => mockManager.getOrdersList(params),
      '/inventory': () => mockManager.getInventoryList(params),
      '/materials': () => mockManager.getMaterialsList(params),
      '/users': () => mockManager.getUsersList(params),
      '/notifications': () => mockManager.getNotificationsList(params),
      '/aog-cases': () => mockManager.getAogCasesList(params),
    }
    
    const mockMethod = endpointMap[endpoint]
    if (!mockMethod) {
      throw new Error(`没有对应的Mock方法: ${endpoint}`)
    }
    
    return await mockMethod()
  }
  
  /**
   * 包装响应数据
   */
  wrapResponse(data, source) {
    return {
      ...data,
      _meta: {
        source,
        timestamp: Date.now(),
        cached: source.includes('cache'),
        mock: source.includes('mock')
      }
    }
  }
  
  /**
   * 日志记录
   */
  logRequest(type, endpoint, params, response) {
    if (!this.logConfig.apiCalls) return
    
    const logData = {
      type,
      endpoint,
      params: Object.keys(params).length > 0 ? params : undefined,
      success: response?.success,
      timestamp: new Date().toISOString()
    }
    
    console.log(`[DataProvider] ${type.toUpperCase()}:`, logData)
  }
  
  /**
   * 错误日志记录
   */
  logError(message, endpoint, error) {
    console.error(`[DataProvider] ${message}:`, {
      endpoint,
      error: error.message,
      stack: this.logConfig.level === 'debug' ? error.stack : undefined
    })
  }
  
  /**
   * 获取统计信息
   */
  getStats() {
    const total = this.requestStats.total
    if (total === 0) {
      return { ...this.requestStats, rates: {} }
    }
    
    return {
      ...this.requestStats,
      rates: {
        success: ((this.requestStats.success / total) * 100).toFixed(1) + '%',
        cached: ((this.requestStats.cached / total) * 100).toFixed(1) + '%',
        fallback: ((this.requestStats.fallback / total) * 100).toFixed(1) + '%',
        failed: ((this.requestStats.failed / total) * 100).toFixed(1) + '%'
      }
    }
  }
  
  /**
   * 重置统计
   */
  resetStats() {
    this.requestStats = {
      total: 0,
      success: 0,
      cached: 0,
      fallback: 0,
      failed: 0
    }
  }
  
  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear()
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }
}

// 全局数据提供器实例
export const dataProvider = new DataProvider()

// 开发环境下暴露到window对象，便于调试
if (environment.isDevelopment) {
  window.dataProvider = dataProvider
  window.mockController = mockController
}

export default dataProvider