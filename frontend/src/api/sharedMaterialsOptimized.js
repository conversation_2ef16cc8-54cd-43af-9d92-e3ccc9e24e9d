/**
 * 航材共享保障平台 - 共享件管理API（优化版）
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 * 
 * 功能描述：共享件发布、审核、搜索、交易等核心业务功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 发布共享件
 * 功能描述：用户发布可共享的航材到平台
 * 入参：{
 *   material_id: 1,
 *   share_type: "sale",
 *   share_quantity: 5,
 *   reserved_quantity: 2,
 *   price: 125000,
 *   description: "CFM56发动机燃油喷嘴，状态良好",
 *   sharing_policy: "immediate",
 *   expiry_date: "2025-12-31",
 *   min_order_quantity: 1
 * }
 * 返回参数：{
 *   shared_material_id: 1,
 *   status: "pending_review",
 *   workflow_instance_id: 1,
 *   message: "共享件发布成功，等待审核"
 * }
 * url地址：/api/shared-materials/publish
 * 请求方式：POST
 */
export async function publishSharedMaterial(data) {
  try {
    // 直接调用真实API，不使用缓存
    const response = await request({
      url: '/api/shared-materials/publish',
      method: 'post',
      data
    })
    
    // 成功后添加到动态数据
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      await mockManager.addDynamicData('sharedMaterials', {
        ...data,
        id: response.body?.shared_material_id || Date.now(),
        status: response.body?.status || 'pending_review',
        created_at: new Date().toISOString(),
        inquiry_count: 0,
        view_count: 0
      })
    }
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 发布共享件失败:', error)
    throw error
  }
}

/**
 * 获取共享件列表
 * 功能描述：获取可共享的航材列表，支持搜索和筛选
 * 入参：{
 *   page: 1,
 *   per_page: 20,
 *   search: "CFM56",
 *   category: "发动机",
 *   share_type: "sale",
 *   status: "approved",
 *   price_min: 10000,
 *   price_max: 500000,
 *   sort_by: "created_at",
 *   sort_order: "desc"
 * }
 * 返回参数：{
 *   shared_materials: [...],
 *   total: 100,
 *   page: 1,
 *   per_page: 20,
 *   pages: 5
 * }
 * url地址：/api/shared-materials/list
 * 请求方式：GET
 */
export async function getSharedMaterials(params = {}) {
  try {
    return await dataProvider.getData('/api/shared-materials/list', params, {
      useCache: true,
      cacheTtl: 2 * 60 * 1000, // 2分钟缓存
      cacheFirst: false, // 不使用缓存优先（确保数据新鲜）
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取共享件列表失败:', error)
    throw error
  }
}

/**
 * 获取共享件详情
 * 功能描述：获取指定共享件的详细信息
 * 入参：shared_material_id (路径参数)
 * 返回参数：{
 *   shared_material: {...},
 *   material: {...},
 *   owner: {...},
 *   inquiry_count: 15,
 *   recent_inquiries: [...]
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>
 * 请求方式：GET
 */
export async function getSharedMaterialDetail(sharedMaterialId) {
  try {
    return await dataProvider.getData(`/api/shared-materials/${sharedMaterialId}`, {}, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取共享件详情失败:', error)
    throw error
  }
}

/**
 * 获取我发布的共享件
 * 功能描述：获取当前用户发布的共享件列表
 * 入参：{
 *   page: 1,
 *   per_page: 20,
 *   status: "all"
 * }
 * 返回参数：{
 *   shared_materials: [...],
 *   statistics: {
 *     total: 50,
 *     approved: 30,
 *     pending: 15,
 *     rejected: 5
 *   }
 * }
 * url地址：/api/shared-materials/my-published
 * 请求方式：GET
 */
export async function getMyPublishedMaterials(params = {}) {
  try {
    return await dataProvider.getData('/api/shared-materials/my-published', params, {
      useCache: true,
      cacheTtl: 1 * 60 * 1000, // 1分钟缓存（个人数据更新频繁）
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取我的共享件失败:', error)
    throw error
  }
}

/**
 * 审核共享件
 * 功能描述：管理员审核共享件发布申请
 * 入参：{
 *   action: "approve",
 *   comment: "审核通过"
 * }
 * 返回参数：{
 *   message: "审核操作成功",
 *   new_status: "approved"
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>/approve
 * 请求方式：POST
 */
export async function approveSharedMaterial(sharedMaterialId, data) {
  try {
    const response = await request({
      url: `/api/shared-materials/${sharedMaterialId}/approve`,
      method: 'post',
      data
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 审核共享件失败:', error)
    throw error
  }
}

/**
 * 创建询价
 * 功能描述：用户对共享件进行询价
 * 入参：{
 *   quantity: 2,
 *   target_price: 120000,
 *   message: "请问是否可以优惠？",
 *   urgency: "normal"
 * }
 * 返回参数：{
 *   inquiry_id: 1,
 *   message: "询价创建成功"
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>/inquiry
 * 请求方式：POST
 */
export async function createInquiry(sharedMaterialId, data) {
  try {
    const response = await request({
      url: `/api/shared-materials/${sharedMaterialId}/inquiry`,
      method: 'post',
      data
    })
    
    // 清除相关缓存（询价会影响询价数统计）
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 创建询价失败:', error)
    throw error
  }
}

/**
 * 获取航材分类
 * 功能描述：获取所有航材分类列表
 * 入参：无
 * 返回参数：{
 *   categories: [
 *     {
 *       name: "发动机",
 *       count: 150,
 *       subcategories: ["燃油系统", "点火系统", "控制系统"]
 *     }
 *   ]
 * }
 * url地址：/api/shared-materials/categories
 * 请求方式：GET
 */
export async function getMaterialCategories() {
  try {
    return await dataProvider.getData('/api/shared-materials/categories', {}, {
      useCache: true,
      cacheTtl: 30 * 60 * 1000, // 30分钟缓存（分类数据相对稳定）
      cacheFirst: true, // 优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取航材分类失败:', error)
    throw error
  }
}

/**
 * 获取搜索建议
 * 功能描述：根据用户输入获取搜索建议
 * 入参：{ query: "CFM" }
 * 返回参数：{
 *   suggestions: [
 *     {
 *       type: "material",
 *       text: "CFM56发动机燃油喷嘴",
 *       part_number: "CFM56-001"
 *     }
 *   ]
 * }
 * url地址：/api/shared-materials/search-suggestions
 * 请求方式：GET
 */
export async function getSearchSuggestions(params = {}) {
  try {
    return await dataProvider.getData('/api/shared-materials/search-suggestions', params, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取搜索建议失败:', error)
    throw error
  }
}

/**
 * 更新共享件状态
 * 功能描述：更新共享件状态（上架/下架）
 * 入参：{
 *   status: "online" | "offline",
 *   reason: "暂时缺货"
 * }
 * 返回参数：{ success: true, message: "状态更新成功" }
 * url地址：/api/shared-materials/<int:shared_material_id>/status
 * 请求方式：PUT
 */
export async function updateSharedMaterialStatus(sharedMaterialId, data) {
  try {
    const response = await request({
      url: `/api/shared-materials/${sharedMaterialId}/status`,
      method: 'put',
      data
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 更新共享件状态失败:', error)
    throw error
  }
}

/**
 * 删除共享件
 * 功能描述：删除共享件（软删除）
 * 入参：shared_material_id (路径参数)
 * 返回参数：{ success: true, message: "删除成功" }
 * url地址：/api/shared-materials/<int:shared_material_id>
 * 请求方式：DELETE
 */
export async function deleteSharedMaterial(sharedMaterialId) {
  try {
    const response = await request({
      url: `/api/shared-materials/${sharedMaterialId}`,
      method: 'delete'
    })
    
    // 清除缓存并更新动态数据
    dataProvider.clearCache()
    
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      // 从动态数据中移除
      const materials = mockManager.dynamicData.sharedMaterials.filter(
        m => m.id !== parseInt(sharedMaterialId)
      )
      mockManager.dynamicData.sharedMaterials = materials
      mockManager.saveDynamicData('sharedMaterials', materials)
    }
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 删除共享件失败:', error)
    throw error
  }
}

/**
 * 获取共享件统计
 * 功能描述：获取共享件相关统计数据
 * 入参：{ time_range: "week" | "month" | "year" }
 * 返回参数：{
 *   total_published: 150,
 *   total_inquiries: 450,
 *   total_transactions: 89,
 *   trend_data: [...]
 * }
 * url地址：/api/shared-materials/statistics
 * 请求方式：GET
 */
export async function getSharedMaterialStatistics(params = {}) {
  try {
    return await dataProvider.getData('/api/shared-materials/statistics', params, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取共享件统计失败:', error)
    throw error
  }
}

/**
 * 批量操作共享件
 * 功能描述：批量更新共享件状态
 * 入参：{
 *   ids: [1, 2, 3],
 *   action: "online" | "offline" | "delete",
 *   reason: "批量下架维护"
 * }
 * 返回参数：{
 *   success_count: 3,
 *   failed_count: 0,
 *   results: [...]
 * }
 * url地址：/api/shared-materials/batch-operation
 * 请求方式：POST
 */
export async function batchOperateSharedMaterials(data) {
  try {
    const response = await request({
      url: '/api/shared-materials/batch-operation',
      method: 'post',
      data
    })
    
    // 清除所有相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[SharedMaterialsAPI] 批量操作失败:', error)
    throw error
  }
}

/**
 * 获取热门共享件
 * 功能描述：获取热门/推荐的共享件列表
 * 入参：{
 *   limit: 10,
 *   category: "发动机"
 * }
 * 返回参数：{
 *   hot_materials: [...],
 *   recommended_materials: [...]
 * }
 * url地址：/api/shared-materials/hot
 * 请求方式：GET
 */
export async function getHotSharedMaterials(params = {}) {
  try {
    return await dataProvider.getData('/api/shared-materials/hot', params, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      cacheFirst: true, // 热门数据可以优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[SharedMaterialsAPI] 获取热门共享件失败:', error)
    throw error
  }
}