/**
 * 需求管理API - 优化版本
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 创建需求
 * 功能描述：发布新的航材需求
 * 入参：{ type, material_name, part_number, category, aircraft_type, quantity, acceptable_conditions, expected_price, delivery_location, expected_delivery_date, urgency, description, contact_person, contact_phone, contact_email, attachments }
 * 返回参数：{ demand_id, message }
 * url地址：/demands
 * 请求方式：POST
 */
export async function createDemand(demandData) {
  try {
    // 直接调用真实API，不使用缓存
    const response = await request({
      url: '/demands/',
      method: 'post',
      data: demandData
    })
    
    // 成功后添加到动态数据
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      await mockManager.addDynamicData('demands', {
        ...demandData,
        id: response.body?.demand_id || Date.now(),
        demand_number: `DM${Date.now()}`,
        status: 'published',
        created_at: new Date().toISOString(),
        match_count: 0,
        response_count: 0
      })
    }
    
    return response
  } catch (error) {
    console.error('[DemandsAPI] 创建需求失败:', error)
    throw error
  }
}

/**
 * 获取需求列表
 * 功能描述：获取需求列表，支持智能数据源选择
 * 入参：{ status, type, page, size }
 * 返回参数：{ total, page, size, pages, items }
 * url地址：/demands
 * 请求方式：GET
 */
export async function getDemands(params = {}) {
  try {
    return await dataProvider.getData('/demands', params, {
      useCache: true,
      cacheTtl: 2 * 60 * 1000, // 2分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[DemandsAPI] 获取需求列表失败:', error)
    throw error
  }
}

/**
 * 获取需求详情
 * 功能描述：获取单个需求的详细信息
 * 入参：{ demand_id }
 * 返回参数：需求详细信息对象
 * url地址：/demands/{demand_id}
 * 请求方式：GET
 */
export async function getDemandDetail(demandId) {
  try {
    return await dataProvider.getData(`/demands/${demandId}`, {}, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[DemandsAPI] 获取需求详情失败:', error)
    throw error
  }
}

/**
 * 更新需求状态
 * 功能描述：更新需求状态
 * 入参：{ demand_id, status, notes }
 * 返回参数：{ success, message }
 * url地址：/demands/{demand_id}/status
 * 请求方式：PUT
 */
export async function updateDemandStatus(demandId, statusData) {
  try {
    const response = await request({
      url: `/demands/${demandId}/status/`,
      method: 'put',
      data: statusData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[DemandsAPI] 更新需求状态失败:', error)
    throw error
  }
}

/**
 * 删除需求
 * 功能描述：删除需求
 * 入参：{ demand_id }
 * 返回参数：{ success, message }
 * url地址：/demands/{demand_id}
 * 请求方式：DELETE
 */
export async function deleteDemand(demandId) {
  try {
    const response = await request({
      url: `/demands/${demandId}/`,
      method: 'delete'
    })
    
    // 清除缓存并更新动态数据
    dataProvider.clearCache()
    
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      // 从动态数据中移除
      const demands = mockManager.dynamicData.demands.filter(d => d.id !== parseInt(demandId))
      mockManager.dynamicData.demands = demands
      mockManager.saveDynamicData('demands', demands)
    }
    
    return response
  } catch (error) {
    console.error('[DemandsAPI] 删除需求失败:', error)
    throw error
  }
}

/**
 * 响应需求
 * 功能描述：供应商响应需求
 * 入参：{ demand_id, response_data }
 * 返回参数：{ success, message }
 * url地址：/demands/{demand_id}/responses
 * 请求方式：POST
 */
export async function respondToDemand(demandId, responseData) {
  try {
    const response = await request({
      url: `/demands/${demandId}/responses/`,
      method: 'post',
      data: responseData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[DemandsAPI] 响应需求失败:', error)
    throw error
  }
}

/**
 * 获取需求响应列表
 * 功能描述：获取需求的响应列表
 * 入参：{ demand_id, page, size }
 * 返回参数：响应列表
 * url地址：/demands/{demand_id}/responses
 * 请求方式：GET
 */
export async function getDemandResponses(demandId, params = {}) {
  try {
    return await dataProvider.getData(`/demands/${demandId}/responses`, params, {
      useCache: true,
      cacheTtl: 3 * 60 * 1000, // 3分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[DemandsAPI] 获取需求响应失败:', error)
    throw error
  }
}

/**
 * 搜索需求
 * 功能描述：根据关键字搜索需求
 * 入参：{ keyword, filters, page, size }
 * 返回参数：搜索结果
 * url地址：/demands/search
 * 请求方式：GET
 */
export async function searchDemands(params = {}) {
  try {
    return await dataProvider.getData('/demands/search', params, {
      useCache: false, // 搜索不使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[DemandsAPI] 搜索需求失败:', error)
    throw error
  }
}

/**
 * 获取需求统计
 * 功能描述：获取需求相关统计数据
 * 入参：{ time_range, group_by }
 * 返回参数：统计数据
 * url地址：/demands/statistics
 * 请求方式：GET
 */
export async function getDemandStatistics(params = {}) {
  try {
    return await dataProvider.getData('/demands/statistics', params, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[DemandsAPI] 获取需求统计失败:', error)
    throw error
  }
}

/**
 * 保存需求草稿
 * 功能描述：保存需求草稿到localStorage
 * 入参：草稿数据
 * 返回参数：{ success, message }
 */
export async function saveDraft(draftData) {
  try {
    // 保存到localStorage
    const drafts = JSON.parse(localStorage.getItem('demand_drafts') || '[]')
    const draftId = Date.now()
    const draft = {
      id: draftId,
      ...draftData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    drafts.unshift(draft)
    // 最多保留10个草稿
    if (drafts.length > 10) {
      drafts.splice(10)
    }
    
    localStorage.setItem('demand_drafts', JSON.stringify(drafts))
    
    return {
      error: 0,
      message: '草稿保存成功',
      body: { draft_id: draftId },
      success: true
    }
  } catch (error) {
    console.error('[DemandsAPI] 保存草稿失败:', error)
    throw error
  }
}

/**
 * 获取草稿列表
 * 功能描述：获取保存的草稿列表
 * 返回参数：草稿列表
 */
export function getDrafts() {
  try {
    const drafts = JSON.parse(localStorage.getItem('demand_drafts') || '[]')
    return {
      error: 0,
      message: '获取草稿成功',
      body: { drafts },
      success: true
    }
  } catch (error) {
    console.error('[DemandsAPI] 获取草稿失败:', error)
    return {
      error: 1,
      message: '获取草稿失败',
      body: { drafts: [] },
      success: false
    }
  }
}

/**
 * 删除草稿
 * 功能描述：删除指定草稿
 * 入参：{ draft_id }
 * 返回参数：{ success, message }
 */
export function deleteDraft(draftId) {
  try {
    const drafts = JSON.parse(localStorage.getItem('demand_drafts') || '[]')
    const filteredDrafts = drafts.filter(draft => draft.id !== draftId)
    localStorage.setItem('demand_drafts', JSON.stringify(filteredDrafts))
    
    return {
      error: 0,
      message: '草稿删除成功',
      success: true
    }
  } catch (error) {
    console.error('[DemandsAPI] 删除草稿失败:', error)
    return {
      error: 1,
      message: '删除草稿失败',
      success: false
    }
  }
}