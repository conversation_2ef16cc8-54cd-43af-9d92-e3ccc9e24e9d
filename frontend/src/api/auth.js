import request from '@/utils/request'
import axios from 'axios'
import mockManager from '@/api/mockManager'

/**
 * 用户登录
 * 功能描述：用户身份验证，返回访问令牌
 * 入参：{ username: string, password: string, user_type: string }
 * 返回参数：{ access_token, refresh_token, expires_in, user }
 * url地址：/auth/login
 * 请求方式：POST
 */
export async function login(credentials) {
  try {
    // 优先尝试真实API登录
    const response = await request({
      url: '/auth/login/',
      method: 'post',
      data: credentials
    })

    return response

  } catch (error) {
    // 真实API失败时才使用Mock数据
    const mockData = await mockManager.getMockData('login')
    if (mockData) {
      return mockData
    }

    // 如果Mock数据也没有，重新抛出原始错误
    throw error
  }
}

/**
 * 刷新Token
 * 功能描述：使用refresh_token获取新的access_token
 * 入参：{ refresh_token: string }
 * 返回参数：{ access_token, expires_in }
 * url地址：/auth/refresh
 * 请求方式：POST
 */
export function refreshToken(refreshToken) {
  // 直接使用axios避免触发拦截器的无限循环
  return axios.post('/api/v1/auth/refresh/', {}, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${refreshToken}`
    }
  }).then(response => response.data)
}

/**
 * 退出登录
 * 功能描述：注销用户会话，使token失效
 * 入参：无
 * 返回参数：{ message: string }
 * url地址：/auth/logout
 * 请求方式：POST
 */
export function logout() {
  return request({
    url: '/auth/logout/',
    method: 'post'
  })
}

/**
 * 用户注册
 * 功能描述：新用户注册账户
 * 入参：{ username, email, phone, password, role_code, company_name }
 * 返回参数：{ user_id, message }
 * url地址：/auth/register/
 * 请求方式：POST
 */
export async function register(userData) {
  const response = await request({
    url: '/auth/register/',
    method: 'post',
    data: userData
  })
  return response
}

/**
 * 获取用户信息
 * 功能描述：获取当前用户详细信息
 * 入参：无
 * 返回参数：{ user_id, username, email, user_type, status }
 * url地址：/users/profile
 * 请求方式：GET
 */
export function getUserProfile() {
  return request({
    url: '/auth/profile/',
    method: 'get'
  })
}

export const authApi = {
  login,
  refreshToken,
  logout,
  register,
  getUserProfile
}