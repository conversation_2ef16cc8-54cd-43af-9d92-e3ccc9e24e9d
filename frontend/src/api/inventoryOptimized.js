/**
 * 库存管理API - 优化版本
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 * 
 * 功能描述：库存查询、入库、出库、调拨、统计等功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 获取库存列表
 * 功能描述：获取库存商品列表，支持筛选和搜索
 * 入参：{ search: string, category: string, status: string, location: string, aircraft_type: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, material_info, current_stock, safety_stock, location, status, unit_price, last_updated }] }
 * url地址：/inventory
 * 请求方式：GET
 */
export async function getInventory(params = {}) {
  try {
    return await dataProvider.getData('/inventory/', params, {
      useCache: true,
      cacheTtl: 3 * 60 * 1000, // 3分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存列表失败:', error)
    throw error
  }
}

/**
 * 获取库存详情
 * 功能描述：获取指定库存项的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, material_info, current_stock, safety_stock, location, status, unit_price, supplier_info, last_updated, history }
 * url地址：/inventory/{id}
 * 请求方式：GET
 */
export async function getInventoryDetail(id) {
  try {
    return await dataProvider.getData(`/inventory/${id}/`, {}, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存详情失败:', error)
    throw error
  }
}

/**
 * 入库操作
 * 功能描述：记录库存商品的入库操作
 * 入参：{ material_id: number, quantity: number, location: string, unit_price: number, supplier_id: number, notes: string }
 * 返回参数：{ transaction_id, new_stock, message }
 * url地址：/inventory/inbound
 * 请求方式：POST
 */
export async function inboundInventory(inboundData) {
  try {
    const response = await request({
      url: '/inventory/inbound/',
      method: 'post',
      data: inboundData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 入库操作失败:', error)
    throw error
  }
}

/**
 * 出库操作
 * 功能描述：记录库存商品的出库操作
 * 入参：{ inventory_id: number, quantity: number, reason: string, recipient: string, notes: string }
 * 返回参数：{ transaction_id, remaining_stock, message }
 * url地址：/inventory/outbound
 * 请求方式：POST
 */
export async function outboundInventory(outboundData) {
  try {
    const response = await request({
      url: '/inventory/outbound/',
      method: 'post',
      data: outboundData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 出库操作失败:', error)
    throw error
  }
}

/**
 * 库存调拨
 * 功能描述：在不同位置间调拨库存
 * 入参：{ inventory_id: number, quantity: number, from_location: string, to_location: string, notes: string }
 * 返回参数：{ transaction_id, message }
 * url地址：/inventory/transfer
 * 请求方式：POST
 */
export async function transferInventory(transferData) {
  try {
    const response = await request({
      url: '/inventory/transfer/',
      method: 'post',
      data: transferData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 库存调拨失败:', error)
    throw error
  }
}

/**
 * 获取库存统计
 * 功能描述：获取库存概览统计数据
 * 入参：无
 * 返回参数：{ total_items, warning_items, shortage_items, total_value, turnover_rate }
 * url地址：/inventory/statistics
 * 请求方式：GET
 */
export async function getInventoryStatistics() {
  try {
    return await dataProvider.getData('/inventory/statistics/', {}, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      cacheFirst: true, // 统计数据可以优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存统计失败:', error)
    throw error
  }
}

/**
 * 获取库存预警
 * 功能描述：获取库存预警列表
 * 入参：{ type: string }
 * 返回参数：[{ id, material_info, current_stock, safety_stock, warning_type, location }]
 * url地址：/inventory/warnings
 * 请求方式：GET
 */
export async function getInventoryWarnings(type) {
  try {
    return await dataProvider.getData('/inventory/warnings/', { type }, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存预警失败:', error)
    throw error
  }
}

/**
 * 批量更新库存
 * 功能描述：批量更新库存信息
 * 入参：{ items: [{ id: number, quantity: number, unit_price: number }] }
 * 返回参数：{ success_count, failed_count, results: [...] }
 * url地址：/inventory/batch-update
 * 请求方式：POST
 */
export async function batchUpdateInventory(batchData) {
  try {
    const response = await request({
      url: '/inventory/batch-update/',
      method: 'post',
      data: batchData
    })
    
    // 清除所有相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 批量更新库存失败:', error)
    throw error
  }
}

/**
 * 库存盘点
 * 功能描述：创建库存盘点任务
 * 入参：{ location: string, items: [...], notes: string }
 * 返回参数：{ stocktake_id, message }
 * url地址：/inventory/stocktake
 * 请求方式：POST
 */
export async function createStocktake(stocktakeData) {
  try {
    const response = await request({
      url: '/inventory/stocktake/',
      method: 'post',
      data: stocktakeData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 创建库存盘点失败:', error)
    throw error
  }
}

/**
 * 获取库存事务历史
 * 功能描述：获取库存变动历史记录
 * 入参：{ inventory_id: number, page: number, size: number }
 * 返回参数：{ transactions: [...], pagination: {...} }
 * url地址：/inventory/{id}/transactions
 * 请求方式：GET
 */
export async function getInventoryTransactions(inventoryId, params = {}) {
  try {
    return await dataProvider.getData(`/inventory/${inventoryId}/transactions/`, params, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存事务历史失败:', error)
    throw error
  }
}

/**
 * 设置库存预警阈值
 * 功能描述：设置库存项的预警阈值
 * 入参：{ inventory_id: number, safety_stock: number, reorder_point: number }
 * 返回参数：{ success: true, message: "设置成功" }
 * url地址：/inventory/{id}/threshold
 * 请求方式：PUT
 */
export async function setInventoryThreshold(inventoryId, thresholdData) {
  try {
    const response = await request({
      url: `/inventory/${inventoryId}/threshold/`,
      method: 'put',
      data: thresholdData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 设置库存预警阈值失败:', error)
    throw error
  }
}

/**
 * 获取库存价值分析
 * 功能描述：获取库存价值分析报告
 * 入参：{ time_range: "week" | "month" | "quarter" }
 * 返回参数：{ total_value, trend_data: [...], abc_analysis: {...} }
 * url地址：/inventory/value-analysis
 * 请求方式：GET
 */
export async function getInventoryValueAnalysis(params = {}) {
  try {
    return await dataProvider.getData('/inventory/value-analysis/', params, {
      useCache: true,
      cacheTtl: 30 * 60 * 1000, // 30分钟缓存
      cacheFirst: true, // 分析数据可以优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存价值分析失败:', error)
    throw error
  }
}

/**
 * 导出库存报表
 * 功能描述：导出库存数据为Excel或PDF
 * 入参：{ format: "excel" | "pdf", filters: {...} }
 * 返回参数：文件下载链接
 * url地址：/inventory/export
 * 请求方式：POST
 */
export async function exportInventory(exportData) {
  try {
    const response = await request({
      url: '/inventory/export/',
      method: 'post',
      data: exportData,
      responseType: 'blob'
    })
    
    return response
  } catch (error) {
    console.error('[InventoryAPI] 导出库存报表失败:', error)
    throw error
  }
}

/**
 * 获取库存周转率
 * 功能描述：获取库存周转率分析
 * 入参：{ period: "month" | "quarter" | "year" }
 * 返回参数：{ turnover_rate, slow_moving_items: [...] }
 * url地址：/inventory/turnover
 * 请求方式：GET
 */
export async function getInventoryTurnover(params = {}) {
  try {
    return await dataProvider.getData('/inventory/turnover/', params, {
      useCache: true,
      cacheTtl: 30 * 60 * 1000, // 30分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[InventoryAPI] 获取库存周转率失败:', error)
    throw error
  }
}

// 导出API集合（保持向后兼容）
export const inventoryApi = {
  getInventory,
  getInventoryDetail,
  inboundInventory,
  outboundInventory,
  transferInventory,
  getInventoryStatistics,
  getInventoryWarnings,
  batchUpdateInventory,
  createStocktake,
  getInventoryTransactions,
  setInventoryThreshold,
  getInventoryValueAnalysis,
  exportInventory,
  getInventoryTurnover
}