import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 验证邀请码
 * 功能描述：验证邀请码的有效性和允许的角色
 * 入参：{ code: string }
 * 返回参数：{ valid: boolean, allowed_roles: Array<string>, expires_at: string }
 * url地址：/api/v1/auth/validate-invitation
 * 请求方式：POST
 */
export async function validateInvitationCode(code) {
  const response = await request({
    url: '/auth/validate-invitation',
    method: 'post',
    data: { code }
  })
  return response
}

/**
 * 使用邀请码注册
 * 功能描述：使用邀请码注册新用户
 * 入参：{ code: string, username: string, email: string, password: string, role_code: string, company_name?: string }
 * 返回参数：{ user: User, message: string }
 * url地址：/api/v1/auth/register-with-invitation
 * 请求方式：POST
 */
export async function registerWithInvitation(registrationData) {
  const response = await request({
    url: '/auth/register-with-invitation',
    method: 'post',
    data: registrationData
  })
  return response
}

/**
 * 生成邀请码（管理员功能）
 * 功能描述：生成新的邀请码
 * 入参：{ type: string, allowed_roles: Array<string>, validity: number, max_uses: number, ip_whitelist?: Array<string> }
 * 返回参数：{ code: string, expires_at: string, allowed_roles: Array<string> }
 * url地址：/api/v1/invitation-codes/
 * 请求方式：POST
 */
export function generateInvitationCode(codeData) {
  return request({
    url: '/invitation-codes/',
    method: 'post',
    data: codeData
  })
}

/**
 * 获取邀请码列表（管理员功能）
 * 功能描述：获取系统中的邀请码列表
 * 入参：{ status?: string, type?: string, page?: number, limit?: number }
 * 返回参数：{ codes: Array<InvitationCode>, total: number }
 * url地址：/api/v1/invitation-codes/
 * 请求方式：GET
 */
export function getInvitationCodes(params = {}) {
  return request({
    url: '/invitation-codes/',
    method: 'get',
    params
  })
}

export const invitationsApi = {
  validateInvitationCode,
  registerWithInvitation,
  generateInvitationCode,
  getInvitationCodes
}