/**
 * 航材共享保障平台 - 工作流管理API
 * 功能描述：工作流实例管理、任务处理、流程监控相关接口
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-18
 */

import { get, post } from '@/utils/request'
import mockManager from './mockManager'

/**
 * 获取工作流定义列表
 * 功能描述：获取所有可用的工作流定义
 * 入参：无
 * 返回参数：{
 *   workflows: [
 *     {
 *       key: "simple_approval",
 *       name: "简单审批流程", 
 *       description: "一级审批，适用于简单业务场景"
 *     }
 *   ]
 * }
 * url地址：/api/workflow/definitions
 * 请求方式：GET
 */
export async function getWorkflowDefinitions() {
  try {
    // 优先使用真实API
    return await get('/api/workflow/definitions')
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowDefinitions')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 启动工作流实例
 * 功能描述：根据工作流定义启动新的工作流实例
 * 入参：{
 *   workflow_key: "simple_approval",
 *   business_key: "ORDER_001", 
 *   variables: {
 *     title: "订单审批",
 *     amount: 10000,
 *     description: "紧急航材采购订单"
 *   }
 * }
 * 返回参数：{
 *   instance_id: "123",
 *   status: "RUNNING",
 *   message: "工作流启动成功"
 * }
 * url地址：/api/workflow/start
 * 请求方式：POST
 */
export async function startWorkflow(params) {
  try {
    // 优先使用真实API
    return await post('/api/workflow/start', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('startWorkflow')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取工作流实例详情
 * 功能描述：获取指定工作流实例的详细信息
 * 入参：{ instance_id: string }
 * 返回参数：{
 *   id: "123",
 *   workflow_key: "simple_approval",
 *   business_key: "ORDER_001",
 *   status: "RUNNING",
 *   progress: 50.0,
 *   current_tasks: [],
 *   variables: {},
 *   created_at: "2025-07-18T10:00:00",
 *   completed_at: null
 * }
 * url地址：/api/workflow/instances/{instance_id}
 * 请求方式：GET
 */
export async function getWorkflowInstance(instance_id) {
  try {
    // 优先使用真实API
    return await get(`/api/workflow/instances/${instance_id}`)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowInstance')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取工作流实例的当前任务
 * 功能描述：获取指定工作流实例的待处理任务列表
 * 入参：{ instance_id: string }
 * 返回参数：{
 *   tasks: [
 *     {
 *       id: "task_001",
 *       name: "审批",
 *       description: "请审批此申请",
 *       assignee: "admin",
 *       status: "READY",
 *       created_at: "2025-07-18T10:00:00",
 *       due_date: null,
 *       form_data: {}
 *     }
 *   ]
 * }
 * url地址：/api/workflow/instances/{instance_id}/tasks
 * 请求方式：GET
 */
export async function getWorkflowTasks(instance_id) {
  try {
    // 优先使用真实API
    return await get(`/api/workflow/instances/${instance_id}/tasks`)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowTasks')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 完成工作流任务
 * 功能描述：完成指定的工作流任务
 * 入参：{
 *   task_id: "task_001",
 *   instance_id: "123",
 *   variables: {
 *     approved: true,
 *     comment: "审批通过",
 *     next_assignee: "manager"
 *   }
 * }
 * 返回参数：{
 *   success: true,
 *   message: "任务完成成功"
 * }
 * url地址：/api/workflow/tasks/{task_id}/complete
 * 请求方式：POST
 */
export async function completeTask(task_id, params) {
  try {
    // 优先使用真实API
    return await post(`/api/workflow/tasks/${task_id}/complete`, params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('completeTask')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取工作流实例列表
 * 功能描述：获取工作流实例列表，支持分页和筛选
 * 入参：{
 *   page: 1,
 *   per_page: 10,
 *   status: "RUNNING",
 *   workflow_key: "simple_approval",
 *   business_key: "ORDER_001"
 * }
 * 返回参数：{
 *   instances: [],
 *   total: 100,
 *   page: 1,
 *   per_page: 10,
 *   pages: 10
 * }
 * url地址：/api/workflow/instances
 * 请求方式：GET
 */
export async function getWorkflowInstances(params) {
  try {
    // 优先使用真实API
    return await get('/api/workflow/instances', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowInstances')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取我的待办任务
 * 功能描述：获取当前用户的待办任务列表
 * 入参：{
 *   page: 1,
 *   per_page: 10,
 *   status: "READY"
 * }
 * 返回参数：{
 *   tasks: [
 *     {
 *       id: "task_001",
 *       instance_id: "123",
 *       task_name: "审批",
 *       workflow_key: "simple_approval",
 *       business_key: "ORDER_001",
 *       status: "READY",
 *       created_at: "2025-07-18T10:00:00",
 *       assignee: "admin"
 *     }
 *   ],
 *   total: 10,
 *   page: 1,
 *   per_page: 10
 * }
 * url地址：/api/workflow/my-tasks
 * 请求方式：GET
 */
export async function getMyTasks(params) {
  try {
    // 优先使用真实API
    return await get('/api/workflow/my-tasks', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMyTasks')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取工作流实例历史记录
 * 功能描述：获取指定工作流实例的所有历史任务和操作记录
 * 入参：{ instance_id: string }
 * 返回参数：{
 *   history: [
 *     {
 *       task_name: "提交申请",
 *       assignee: "user1",
 *       status: "COMPLETED",
 *       completed_at: "2025-07-18T10:00:00",
 *       comment: "申请提交",
 *       variables: {}
 *     }
 *   ]
 * }
 * url地址：/api/workflow/instances/{instance_id}/history
 * 请求方式：GET
 */
export async function getWorkflowHistory(instance_id) {
  try {
    // 优先使用真实API
    return await get(`/api/workflow/instances/${instance_id}/history`)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowHistory')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取工作流统计信息
 * 功能描述：获取工作流实例的统计数据
 * 入参：{
 *   start_date: "2025-01-01",
 *   end_date: "2025-12-31",
 *   workflow_key: "simple_approval"
 * }
 * 返回参数：{
 *   total_instances: 100,
 *   running_instances: 20,
 *   completed_instances: 80,
 *   average_completion_time: 2.5,
 *   workflow_distribution: {
 *     simple_approval: 60,
 *     multi_level_approval: 30,
 *     parallel_approval: 10
 *   }
 * }
 * url地址：/api/workflow/statistics
 * 请求方式：GET
 */
export async function getWorkflowStatistics(params) {
  try {
    // 优先使用真实API
    return await get('/api/workflow/statistics', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getWorkflowStatistics')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}