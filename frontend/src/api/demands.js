import request from '@/utils/request'
import mockManager from '@/api/mockManager'

/**
 * 创建需求
 * 功能描述：发布新的航材需求
 * 入参：{ type: string, material_name: string, part_number: string, category: string, aircraft_type: string, quantity: number, acceptable_conditions: array, expected_price: string, delivery_location: string, expected_delivery_date: string, urgency: string, description: string, contact_person: string, contact_phone: string, contact_email: string, attachments: array }
 * 返回参数：{ demand_id, message }
 * url地址：/demands
 * 请求方式：POST
 */
export async function createDemand(demandData) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/demands/',
    method: 'post',
    data: demandData
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('createDemand')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 保存需求草稿
 * 功能描述：保存需求草稿
 * 入参：{ 同创建需求参数 }
 * 返回参数：{ draft_id, message }
 * url地址：/demands/draft
 * 请求方式：POST
 */
export function saveDraft(draftData) {
  return request({
    url: '/demands/draft/',
    method: 'post',
    data: draftData
  })
}

/**
 * 获取需求列表
 * 功能描述：获取用户发布的需求列表
 * 入参：{ status: string, type: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, material_name, part_number, quantity, status, urgency, created_at, responses_count }] }
 * url地址：/demands
 * 请求方式：GET
 */
export async function getDemands(params) {
  // 检查是否有Mock数据
  try {
    // 优先使用真实API
    return await request({
    url: '/demands/',
    method: 'get',
    params
  })
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getDemands')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取需求详情
 * 功能描述：获取指定需求的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, type, material_info, requirements, contact_info, responses, status, created_at }
 * url地址：/demands/{id}
 * 请求方式：GET
 */
export function getDemandDetail(id) {
  return request({
    url: `/demands/${id}`,
    method: 'get'
  })
}

export const demandsApi = {
  createDemand,
  saveDraft,
  getDemands,
  getDemandDetail
}