/**
 * 航材管理API - 优化版本
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 * 
 * 功能描述：航材查询、搜索、分类、制造商等功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 获取材料列表
 * 功能描述：获取平台中的材料列表，支持分页和筛选
 * 入参：{ page: number, per_page: number, search: string, category: string, manufacturer: string }
 * 返回参数：{ materials: [], total: number, page: number, per_page: number, pages: number }
 * url地址：/api/materials
 * 请求方式：GET
 */
export async function getMaterials(params = {}) {
  try {
    return await dataProvider.getData('/api/materials', params, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取材料列表失败:', error)
    throw error
  }
}

/**
 * 航材搜索
 * 功能描述：根据关键词和筛选条件搜索航材
 * 入参：{ q: string, category: string, oem: string, condition: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, part_number, part_name, manufacturer, category, price_range, images, status }] }
 * url地址：/materials/search
 * 请求方式：GET
 */
export async function searchMaterials(params = {}) {
  try {
    return await dataProvider.getData('/materials/search', params, {
      useCache: true,
      cacheTtl: 2 * 60 * 1000, // 2分钟缓存（搜索结果缓存时间短一些）
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 航材搜索失败:', error)
    throw error
  }
}

/**
 * 获取航材详情
 * 功能描述：获取指定航材的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, part_number, part_name, manufacturer, category, description, specifications, images }
 * url地址：/materials/{id}
 * 请求方式：GET
 */
export async function getMaterialDetail(id) {
  try {
    return await dataProvider.getData(`/materials/${id}`, {}, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存（详情页缓存时间长一些）
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取航材详情失败:', error)
    throw error
  }
}

/**
 * 获取航材分类
 * 功能描述：获取所有航材分类列表
 * 入参：无
 * 返回参数：[{ id, name, parent_id, children }]
 * url地址：/materials/categories
 * 请求方式：GET
 */
export async function getMaterialCategories() {
  try {
    return await dataProvider.getData('/materials/categories', {}, {
      useCache: true,
      cacheTtl: 30 * 60 * 1000, // 30分钟缓存（分类数据相对稳定）
      cacheFirst: true, // 优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取航材分类失败:', error)
    throw error
  }
}

/**
 * 获取制造商列表
 * 功能描述：获取所有制造商列表
 * 入参：无
 * 返回参数：[{ id, name, country }]
 * url地址：/materials/manufacturers
 * 请求方式：GET
 */
export async function getManufacturers() {
  try {
    return await dataProvider.getData('/materials/manufacturers', {}, {
      useCache: true,
      cacheTtl: 60 * 60 * 1000, // 1小时缓存（制造商数据变化很少）
      cacheFirst: true, // 优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取制造商列表失败:', error)
    throw error
  }
}

/**
 * 创建询价
 * 功能描述：对航材创建询价请求
 * 入参：{ material_id: number, quantity: number, target_price: number, requirements: string }
 * 返回参数：{ inquiry_id, message }
 * url地址：/inquiries
 * 请求方式：POST
 */
export async function createInquiry(inquiryData) {
  try {
    const response = await request({
      url: '/inquiries',
      method: 'post',
      data: inquiryData
    })
    
    // 清除相关缓存（新询价可能影响材料的询价统计）
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[MaterialsAPI] 创建询价失败:', error)
    throw error
  }
}

/**
 * 获取热门航材
 * 功能描述：获取热门搜索或交易的航材
 * 入参：{ type: "search" | "trade", limit: number }
 * 返回参数：{ materials: [...], updated_at: string }
 * url地址：/materials/hot
 * 请求方式：GET
 */
export async function getHotMaterials(params = {}) {
  try {
    return await dataProvider.getData('/materials/hot', params, {
      useCache: true,
      cacheTtl: 15 * 60 * 1000, // 15分钟缓存
      cacheFirst: true, // 热门数据可以优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取热门航材失败:', error)
    throw error
  }
}

/**
 * 获取推荐航材
 * 功能描述：基于用户行为推荐相关航材
 * 入参：{ user_id: number, material_id: number, limit: number }
 * 返回参数：{ materials: [...], reason: string }
 * url地址：/materials/recommend
 * 请求方式：GET
 */
export async function getRecommendedMaterials(params = {}) {
  try {
    return await dataProvider.getData('/materials/recommend', params, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取推荐航材失败:', error)
    throw error
  }
}

/**
 * 获取航材价格历史
 * 功能描述：获取航材的历史价格走势
 * 入参：{ material_id: number, period: "month" | "quarter" | "year" }
 * 返回参数：{ price_history: [...], trend: "up" | "down" | "stable" }
 * url地址：/materials/{id}/price-history
 * 请求方式：GET
 */
export async function getMaterialPriceHistory(materialId, params = {}) {
  try {
    return await dataProvider.getData(`/materials/${materialId}/price-history`, params, {
      useCache: true,
      cacheTtl: 30 * 60 * 1000, // 30分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取航材价格历史失败:', error)
    throw error
  }
}

/**
 * 获取航材供应商
 * 功能描述：获取指定航材的供应商列表
 * 入参：{ material_id: number }
 * 返回参数：{ suppliers: [...], total: number }
 * url地址：/materials/{id}/suppliers
 * 请求方式：GET
 */
export async function getMaterialSuppliers(materialId) {
  try {
    return await dataProvider.getData(`/materials/${materialId}/suppliers`, {}, {
      useCache: true,
      cacheTtl: 15 * 60 * 1000, // 15分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取航材供应商失败:', error)
    throw error
  }
}

/**
 * 获取相似航材
 * 功能描述：获取与指定航材相似的航材列表
 * 入参：{ material_id: number, limit: number }
 * 返回参数：{ materials: [...], similarity_score: number }
 * url地址：/materials/{id}/similar
 * 请求方式：GET
 */
export async function getSimilarMaterials(materialId, params = {}) {
  try {
    return await dataProvider.getData(`/materials/${materialId}/similar`, params, {
      useCache: true,
      cacheTtl: 20 * 60 * 1000, // 20分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取相似航材失败:', error)
    throw error
  }
}

/**
 * 获取航材统计
 * 功能描述：获取航材相关统计数据
 * 入参：{ category: string, period: string }
 * 返回参数：{ total_count, new_count, inquiry_count, trade_count }
 * url地址：/materials/statistics
 * 请求方式：GET
 */
export async function getMaterialStatistics(params = {}) {
  try {
    return await dataProvider.getData('/materials/statistics', params, {
      useCache: true,
      cacheTtl: 15 * 60 * 1000, // 15分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取航材统计失败:', error)
    throw error
  }
}

/**
 * 搜索建议
 * 功能描述：获取搜索关键词建议
 * 入参：{ query: string, limit: number }
 * 返回参数：{ suggestions: [...] }
 * url地址：/materials/search-suggestions
 * 请求方式：GET
 */
export async function getMaterialSearchSuggestions(params = {}) {
  try {
    return await dataProvider.getData('/materials/search-suggestions', params, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 获取搜索建议失败:', error)
    throw error
  }
}

/**
 * 航材比较
 * 功能描述：比较多个航材的规格和价格
 * 入参：{ material_ids: [1, 2, 3] }
 * 返回参数：{ comparison: {...}, materials: [...] }
 * url地址：/materials/compare
 * 请求方式：POST
 */
export async function compareMaterials(materialIds) {
  try {
    return await dataProvider.getData('/materials/compare', { material_ids: materialIds }, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[MaterialsAPI] 航材比较失败:', error)
    throw error
  }
}

// 导出API集合（保持向后兼容）
export const materialsApi = {
  getMaterials,
  searchMaterials,
  getMaterialDetail,
  getMaterialCategories,
  getManufacturers,
  createInquiry,
  getHotMaterials,
  getRecommendedMaterials,
  getMaterialPriceHistory,
  getMaterialSuppliers,
  getSimilarMaterials,
  getMaterialStatistics,
  getMaterialSearchSuggestions,
  compareMaterials
}