/**
 * 航材共享保障平台 - 共享件管理API
 * 功能描述：共享件发布、审核、搜索、交易等核心业务功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-18
 */

import { get, post } from '@/utils/request'
import mockManager from './mockManager'

/**
 * 发布共享件
 * 功能描述：用户发布可共享的航材到平台
 * 入参：{
 *   material_id: 1,
 *   share_type: "sale",
 *   share_quantity: 5,
 *   reserved_quantity: 2,
 *   price: 125000,
 *   description: "CFM56发动机燃油喷嘴，状态良好",
 *   sharing_policy: "immediate",
 *   expiry_date: "2025-12-31",
 *   min_order_quantity: 1
 * }
 * 返回参数：{
 *   shared_material_id: 1,
 *   status: "pending_review",
 *   workflow_instance_id: 1,
 *   message: "共享件发布成功，等待审核"
 * }
 * url地址：/api/shared-materials/publish
 * 请求方式：POST
 */
export async function publishSharedMaterial(data) {
  try {
    // 优先使用真实API
    return await post('/api/shared-materials/publish', data)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('publishSharedMaterial')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取共享件列表
 * 功能描述：获取可共享的航材列表，支持搜索和筛选
 * 入参：{
 *   page: 1,
 *   per_page: 20,
 *   search: "CFM56",
 *   category: "发动机",
 *   share_type: "sale",
 *   status: "approved",
 *   price_min: 10000,
 *   price_max: 500000,
 *   sort_by: "created_at",
 *   sort_order: "desc"
 * }
 * 返回参数：{
 *   shared_materials: [...],
 *   total: 100,
 *   page: 1,
 *   per_page: 20,
 *   pages: 5
 * }
 * url地址：/api/shared-materials/list
 * 请求方式：GET
 */
export async function getSharedMaterials(params) {
  try {
    // 优先使用真实API
    return await get('/api/shared-materials/list', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getSharedMaterials')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取共享件详情
 * 功能描述：获取指定共享件的详细信息
 * 入参：shared_material_id (路径参数)
 * 返回参数：{
 *   shared_material: {...},
 *   material: {...},
 *   owner: {...},
 *   inquiry_count: 15,
 *   recent_inquiries: [...]
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>
 * 请求方式：GET
 */
export async function getSharedMaterialDetail(sharedMaterialId) {
  try {
    // 优先使用真实API
    return await get(`/api/shared-materials/${sharedMaterialId}`)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getSharedMaterialDetail')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取我发布的共享件
 * 功能描述：获取当前用户发布的共享件列表
 * 入参：{
 *   page: 1,
 *   per_page: 20,
 *   status: "all"
 * }
 * 返回参数：{
 *   shared_materials: [...],
 *   statistics: {
 *     total: 50,
 *     approved: 30,
 *     pending: 15,
 *     rejected: 5
 *   }
 * }
 * url地址：/api/shared-materials/my-published
 * 请求方式：GET
 */
export async function getMyPublishedMaterials(params) {
  try {
    // 优先使用真实API
    return await get('/api/shared-materials/my-published', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMyPublishedMaterials')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 审核共享件
 * 功能描述：管理员审核共享件发布申请
 * 入参：{
 *   action: "approve",
 *   comment: "审核通过"
 * }
 * 返回参数：{
 *   message: "审核操作成功",
 *   new_status: "approved"
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>/approve
 * 请求方式：POST
 */
export async function approveSharedMaterial(sharedMaterialId, data) {
  try {
    // 优先使用真实API
    return await post(`/api/shared-materials/${sharedMaterialId}/approve`, data)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('approveSharedMaterial')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 创建询价
 * 功能描述：用户对共享件进行询价
 * 入参：{
 *   quantity: 2,
 *   target_price: 120000,
 *   message: "请问是否可以优惠？",
 *   urgency: "normal"
 * }
 * 返回参数：{
 *   inquiry_id: 1,
 *   message: "询价创建成功"
 * }
 * url地址：/api/shared-materials/<int:shared_material_id>/inquiry
 * 请求方式：POST
 */
export async function createInquiry(sharedMaterialId, data) {
  try {
    // 优先使用真实API
    return await post(`/api/shared-materials/${sharedMaterialId}/inquiry`, data)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('createInquiry')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取航材分类
 * 功能描述：获取所有航材分类列表
 * 入参：无
 * 返回参数：{
 *   categories: [
 *     {
 *       name: "发动机",
 *       count: 150,
 *       subcategories: ["燃油系统", "点火系统", "控制系统"]
 *     }
 *   ]
 * }
 * url地址：/api/shared-materials/categories
 * 请求方式：GET
 */
export async function getMaterialCategories() {
  try {
    // 优先使用真实API
    return await get('/api/shared-materials/categories')
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getMaterialCategories')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}

/**
 * 获取搜索建议
 * 功能描述：根据用户输入获取搜索建议
 * 入参：{ query: "CFM" }
 * 返回参数：{
 *   suggestions: [
 *     {
 *       type: "material",
 *       text: "CFM56发动机燃油喷嘴",
 *       part_number: "CFM56-001"
 *     }
 *   ]
 * }
 * url地址：/api/shared-materials/search-suggestions
 * 请求方式：GET
 */
export async function getSearchSuggestions(params) {
  try {
    // 优先使用真实API
    return await get('/api/shared-materials/search-suggestions', params)
  } catch (error) {
    // 真实API失败时才考虑Mock数据（仅作为fallback）
    const mockData = await mockManager.getMockData('getSearchSuggestions')
    if (mockData) {
      console.warn('[API] 真实API失败，使用Mock数据作为fallback:', error.message)
      return mockData
    }
    throw error
  }
}