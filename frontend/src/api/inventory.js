import request from '@/utils/request'

/**
 * 获取库存列表
 * 功能描述：获取库存商品列表，支持筛选和搜索
 * 入参：{ search: string, category: string, status: string, location: string, aircraft_type: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, material_info, current_stock, safety_stock, location, status, unit_price, last_updated }] }
 * url地址：/inventory
 * 请求方式：GET
 */
export function getInventory(params) {
  return request({
    url: '/inventory/',
    method: 'get',
    params
  })
}

/**
 * 获取库存详情
 * 功能描述：获取指定库存项的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, material_info, current_stock, safety_stock, location, status, unit_price, supplier_info, last_updated, history }
 * url地址：/inventory/{id}
 * 请求方式：GET
 */
export function getInventoryDetail(id) {
  return request({
    url: `/inventory/${id}/`,
    method: 'get'
  })
}

/**
 * 入库操作
 * 功能描述：记录库存商品的入库操作
 * 入参：{ material_id: number, quantity: number, location: string, unit_price: number, supplier_id: number, notes: string }
 * 返回参数：{ transaction_id, new_stock, message }
 * url地址：/inventory/inbound
 * 请求方式：POST
 */
export function inboundInventory(inboundData) {
  return request({
    url: '/inventory/inbound/',
    method: 'post',
    data: inboundData
  })
}

/**
 * 出库操作
 * 功能描述：记录库存商品的出库操作
 * 入参：{ inventory_id: number, quantity: number, reason: string, recipient: string, notes: string }
 * 返回参数：{ transaction_id, remaining_stock, message }
 * url地址：/inventory/outbound
 * 请求方式：POST
 */
export function outboundInventory(outboundData) {
  return request({
    url: '/inventory/outbound/',
    method: 'post',
    data: outboundData
  })
}

/**
 * 库存调拨
 * 功能描述：在不同位置间调拨库存
 * 入参：{ inventory_id: number, quantity: number, from_location: string, to_location: string, notes: string }
 * 返回参数：{ transaction_id, message }
 * url地址：/inventory/transfer
 * 请求方式：POST
 */
export function transferInventory(transferData) {
  return request({
    url: '/inventory/transfer/',
    method: 'post',
    data: transferData
  })
}

/**
 * 获取库存统计
 * 功能描述：获取库存概览统计数据
 * 入参：无
 * 返回参数：{ total_items, warning_items, shortage_items, total_value, turnover_rate }
 * url地址：/inventory/statistics
 * 请求方式：GET
 */
export function getInventoryStatistics() {
  return request({
    url: '/inventory/statistics/',
    method: 'get'
  })
}

/**
 * 获取库存预警
 * 功能描述：获取库存预警列表
 * 入参：{ type: string }
 * 返回参数：[{ id, material_info, current_stock, safety_stock, warning_type, location }]
 * url地址：/inventory/warnings
 * 请求方式：GET
 */
export function getInventoryWarnings(type) {
  return request({
    url: '/inventory/warnings/',
    method: 'get',
    params: { type }
  })
}

export const inventoryApi = {
  getInventory,
  getInventoryDetail,
  inboundInventory,
  outboundInventory,
  transferInventory,
  getInventoryStatistics,
  getInventoryWarnings
}