/**
 * Mock数据管理器
 * 功能描述：统一管理开发环境下的Mock数据，提供Mock数据注册、获取、启用/禁用功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-17
 * 版本：v1.0
 */

class MockManager {
  constructor() {
    // 判断是否启用Mock数据（仅在开发环境启用）
    this.enabled = import.meta.env.MODE === 'development' || import.meta.env.VITE_MOCK_ENABLED === 'true'
    
    // Mock数据存储
    this.mockData = new Map()
    
    // 动态数据存储（用于保存用户操作产生的数据）
    this.dynamicData = {
      demands: this.loadDynamicData('demands') || [],
      sharedMaterials: this.loadDynamicData('sharedMaterials') || [],
      orders: this.loadDynamicData('orders') || []
    }
    
    // 延迟配置（模拟网络延迟）
    this.delay = {
      min: 200,  // 最小延迟（毫秒）
      max: 800   // 最大延迟（毫秒）
    }
    
    // 初始化Mock数据
    this.initMockData()
    
    console.log(`[MockManager] Mock数据管理器已${this.enabled ? '启用' : '禁用'}`)
    console.log(`[MockManager] 已加载动态数据: demands=${this.dynamicData.demands.length}条`)
  }

  /**
   * 注册Mock数据
   * @param {string} apiName - API名称
   * @param {any} mockData - Mock数据
   * @param {object} options - 配置选项
   */
  register(apiName, mockData, options = {}) {
    if (!apiName) {
      throw new Error('API名称不能为空')
    }

    this.mockData.set(apiName, {
      data: mockData,
      enabled: options.enabled !== false,
      delay: options.delay || this.getRandomDelay(),
      description: options.description || '',
      createdAt: new Date().toISOString()
    })

    console.log(`[MockManager] 注册Mock数据: ${apiName}`)
  }

  /**
   * 获取Mock数据
   * @param {string} apiName - API名称
   * @returns {Promise<any>} Mock数据
   */
  async getMockData(apiName) {
    if (!this.enabled) {
      return null
    }

    const mockItem = this.mockData.get(apiName)
    if (!mockItem || !mockItem.enabled) {
      return null
    }

    // 模拟网络延迟
    await this.simulateDelay(mockItem.delay)

    console.log(`[MockManager] 返回Mock数据: ${apiName}`)
    return mockItem.data
  }

  /**
   * 检查是否有Mock数据
   * @param {string} apiName - API名称
   * @returns {boolean}
   */
  hasMockData(apiName) {
    return this.enabled && this.mockData.has(apiName) && this.mockData.get(apiName).enabled
  }

  /**
   * 启用/禁用Mock数据
   * @param {string} apiName - API名称
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(apiName, enabled) {
    const mockItem = this.mockData.get(apiName)
    if (mockItem) {
      mockItem.enabled = enabled
      console.log(`[MockManager] ${enabled ? '启用' : '禁用'} Mock数据: ${apiName}`)
    }
  }

  /**
   * 全局启用/禁用Mock功能
   * @param {boolean} enabled - 是否启用
   */
  setGlobalEnabled(enabled) {
    this.enabled = enabled
    console.log(`[MockManager] Mock功能已${enabled ? '启用' : '禁用'}`)
  }

  /**
   * 添加动态数据
   * @param {string} type - 数据类型 (demands, orders, sharedMaterials)
   * @param {object} data - 要添加的数据
   */
  async addDynamicData(type, data) {
    if (!this.dynamicData[type]) {
      this.dynamicData[type] = []
    }
    
    // 添加数据到数组开头（最新的在前面）
    this.dynamicData[type].unshift(data)
    
    // 保存到localStorage
    this.saveDynamicData(type, this.dynamicData[type])
    
    console.log(`[MockManager] 添加动态数据到 ${type}:`, data)
  }

  /**
   * 更新动态数据
   * @param {string} type - 数据类型 (demands, orders, sharedMaterials)
   * @param {string|number} id - 要更新的数据ID
   * @param {object} updateData - 更新的数据
   * @returns {object|null} 更新后的数据对象，如果找不到则返回null
   */
  async updateDynamicData(type, id, updateData) {
    if (!this.dynamicData[type]) {
      this.dynamicData[type] = []
    }
    
    // 找到要更新的数据项
    const index = this.dynamicData[type].findIndex(item => 
      item.id.toString() === id.toString()
    )
    
    if (index !== -1) {
      // 更新数据，合并新的数据到现有数据中
      const existingData = this.dynamicData[type][index]
      
      // 深度合并更新数据
      if (updateData.material_info && existingData.material_info) {
        Object.assign(existingData.material_info, updateData.material_info)
      }
      if (updateData.requirements && existingData.requirements) {
        Object.assign(existingData.requirements, updateData.requirements)
      }
      if (updateData.contact_info && existingData.contact_info) {
        Object.assign(existingData.contact_info, updateData.contact_info)
      }
      
      // 更新其他字段
      Object.assign(existingData, {
        ...updateData,
        updated_at: new Date().toISOString() // 更新时间戳
      })
      
      // 保存到localStorage
      this.saveDynamicData(type, this.dynamicData[type])
      
      console.log(`[MockManager] 更新动态数据 ${type}[${id}]:`, existingData)
      return existingData
    }
    
    console.warn(`[MockManager] 找不到要更新的数据 ${type}[${id}]`)
    return null
  }

  /**
   * 模拟网络延迟
   * @param {number} delay - 延迟时间（毫秒）
   */
  simulateDelay(delay) {
    return new Promise(resolve => {
      setTimeout(resolve, delay)
    })
  }

  /**
   * 加载动态数据
   * @param {string} key - 数据键名
   * @returns {any} 数据
   */
  loadDynamicData(key) {
    try {
      const data = localStorage.getItem(`mockData_${key}`)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error(`[MockManager] 加载动态数据失败: ${key}`, error)
      return null
    }
  }

  /**
   * 保存动态数据
   * @param {string} key - 数据键名
   * @param {any} data - 要保存的数据
   */
  saveDynamicData(key, data) {
    try {
      localStorage.setItem(`mockData_${key}`, JSON.stringify(data))
      console.log(`[MockManager] 保存动态数据: ${key}`)
    } catch (error) {
      console.error(`[MockManager] 保存动态数据失败: ${key}`, error)
    }
  }

  /**
   * 添加需求数据
   * @param {object} demandData - 需求数据
   * @returns {object} 创建结果
   */
  addDemand(demandData) {
    const demand = {
      id: 'DMD' + Date.now(),
      demand_id: 'DMD' + Date.now(),
      ...demandData,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    this.dynamicData.demands.unshift(demand)
    this.saveDynamicData('demands', this.dynamicData.demands)
    
    console.log(`[MockManager] 添加新需求: ${demand.demand_id}`)
    return demand
  }

  /**
   * 获取需求列表
   * @returns {array} 需求列表
   */
  getDemandsList() {
    // 合并静态Mock数据和动态数据
    const staticMockData = this.mockData.get('getDemands')?.data?.body?.demands || []
    const combinedDemands = [...this.dynamicData.demands, ...staticMockData]
    
    return {
      error: 0,
      message: 'success',
      body: {
        demands: combinedDemands,
        total: combinedDemands.length
      },
      success: true
    }
  }

  /**
   * 获取随机延迟时间
   * @returns {number} 延迟时间（毫秒）
   */
  getRandomDelay() {
    return Math.floor(Math.random() * (this.delay.max - this.delay.min + 1)) + this.delay.min
  }

  /**
   * 获取所有Mock数据列表
   * @returns {Array} Mock数据列表
   */
  getAllMockData() {
    const mockList = []
    this.mockData.forEach((value, key) => {
      mockList.push({
        apiName: key,
        description: value.description,
        enabled: value.enabled,
        delay: value.delay,
        createdAt: value.createdAt
      })
    })
    return mockList
  }

  /**
   * 清空所有Mock数据
   */
  clearAllMockData() {
    this.mockData.clear()
    console.log('[MockManager] 已清空所有Mock数据')
  }

  /**
   * 检查是否启用Mock功能
   * @returns {boolean} 是否启用Mock
   */
  isMockEnabled() {
    return this.enabled
  }

  /**
   * 初始化Mock数据
   * 注册所有API接口的Mock数据
   */
  initMockData() {
    // 用户认证相关Mock数据 - 禁用登录Mock，强制使用真实API
    this.register('login', {
      error: 0,
      message: '登录成功',
      body: {
        access_token: 'mock-jwt-access-token-' + Date.now(),
        refresh_token: 'mock-jwt-refresh-token-' + Date.now(),
        token_type: 'Bearer',
        expires_in: 86400,
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          user_type: 'admin',
          company_name: '航材共享保障平台',
          real_name: '系统管理员',
          phone: '138****8888'
        }
      },
      success: true
    }, { 
      description: '用户登录接口', 
      enabled: false  // 🚫 禁用登录Mock，强制使用真实API
    })

    // 航材市场相关Mock数据
    this.register('getMaterials', {
      error: 0,
      message: '获取航材列表成功',
      body: {
        materials: [
          {
            id: 1,
            name: 'CFM56发动机燃油喷嘴',
            part_number: 'CFM56-7B-001',
            category: 'engine',
            manufacturer: 'CFM国际',
            aircraft_type: 'A320',
            price: 125000,
            condition: 'new',
            location: '北京',
            image: '/images/发动机燃油喷嘴.jpg',
            supplier: {
              name: '中航材供应商',
              rating: 4.8
            },
            description: '高性能燃油喷嘴，适用于A320系列飞机',
            stock: 3
          },
          {
            id: 2,
            name: 'B737主起落架机轮',
            part_number: 'WHL-B737-001',
            category: 'landing_gear',
            manufacturer: '霍尼韦尔',
            aircraft_type: 'B737',
            price: 89000,
            condition: 'serviceable',
            location: '上海',
            image: '/images/机轮.jpg',
            supplier: {
              name: '维修企业',
              rating: 4.6
            },
            description: '原厂机轮，经过严格质量检测',
            stock: 5
          }
        ],
        pagination: {
          current_page: 1,
          per_page: 10,
          total: 2,
          total_pages: 1
        }
      },
      success: true
    }, { description: '获取航材列表接口' })

    // 订单管理相关Mock数据
    this.register('getOrders', {
      error: 0,
      message: '获取订单列表成功',
      body: {
        orders: [
          {
            id: 1,
            order_number: 'PO2024071701',
            buyer_name: '中国国际航空',
            supplier_name: '中航材供应商',
            status: 'processing',
            total_amount: 125000,
            created_at: '2024-07-17T10:00:00Z',
            items: [
              {
                material_name: 'CFM56发动机燃油喷嘴',
                quantity: 1,
                unit_price: 125000
              }
            ]
          }
        ],
        statistics: {
          pending: 8,
          processing: 15,
          completed: 42,
          total_amount: 1285000
        }
      },
      success: true
    }, { description: '获取订单列表接口' })

    // 库存管理相关Mock数据
    this.register('getInventory', {
      error: 0,
      message: '获取库存列表成功',
      body: {
        inventory: [
          {
            id: 1,
            material_name: 'CFM56发动机燃油喷嘴',
            part_number: 'CFM56-7B-001',
            location: '北京仓库A区',
            current_stock: 15,
            safety_stock: 5,
            status: 'normal',
            unit_price: 125000,
            supplier: '中航材供应商'
          }
        ],
        statistics: {
          total_items: 1850,
          warning_items: 23,
          shortage_items: 8,
          total_value: 15600000,
          turnover_rate: 0.78
        }
      },
      success: true
    }, { description: '获取库存列表接口' })

    // 通知管理相关Mock数据
    this.register('getNotifications', {
      error: 0,
      message: '获取通知列表成功',
      body: {
        notifications: [
          {
            id: 1,
            type: 'order',
            title: '新订单提醒',
            content: '您有一个新的采购订单需要处理',
            priority: 'high',
            is_read: false,
            created_at: '2024-07-17T10:00:00Z'
          },
          {
            id: 2,
            type: 'inventory',
            title: '库存预警',
            content: 'CFM56燃油喷嘴库存不足，请及时补充',
            priority: 'medium',
            is_read: false,
            created_at: '2024-07-17T09:30:00Z'
          }
        ],
        unread_count: 3
      },
      success: true
    }, { description: '获取通知列表接口' })

    // 需求管理相关Mock数据
    this.register('getDemands', {
      error: 0,
      message: '获取需求列表成功',
      body: {
        demands: [
          {
            id: 1,
            demand_number: 'DM2024071701',
            title: 'AOG紧急-A320起落架轮胎-紧急',
            type: 'aog',
            material_name: 'A320起落架轮胎',
            quantity: 2,
            priority: 'aog',
            status: 'published',
            requester: '中国国际航空',
            created_at: '2024-07-17T10:00:00Z',
            match_count: 3,
            response_count: 2
          },
          {
            id: 2,
            demand_number: 'DM2024071702',
            title: '周转件需求-CFM56发动机燃油喷嘴-A320',
            type: 'turnaround',
            material_name: 'CFM56发动机燃油喷嘴',
            quantity: 1,
            priority: 'high',
            status: 'matching',
            requester: '东方航空',
            created_at: '2024-07-16T14:30:00Z',
            match_count: 5,
            response_count: 1
          },
          {
            id: 3,
            demand_number: 'DM2024071703',
            title: '消耗件需求-B737机轮组件',
            type: 'consumable',
            material_name: 'B737机轮组件',
            quantity: 3,
            priority: 'normal',
            status: 'matched',
            requester: '南方航空',
            created_at: '2024-07-15T09:00:00Z',
            match_count: 2,
            response_count: 3
          }
        ],
        total: 3,
        page: 1,
        per_page: 20,
        pages: 1
      },
      success: true
    }, { description: '获取需求列表接口' })

    // 创建需求Mock数据
    this.register('createDemand', {
      error: 0,
      message: '需求创建成功',
      body: {
        demand_id: 'DMD' + Date.now(),
        message: '需求发布成功',
        matching_results: [
          {
            supplier_name: '中航材供应商',
            match_score: 95.5,
            price_estimate: '¥125,000',
            delivery_time: '7-10天',
            status: '有现货'
          },
          {
            supplier_name: '维修企业',
            match_score: 88.2,
            price_estimate: '¥118,000',
            delivery_time: '5-7天',
            status: '需调货'
          }
        ]
      },
      success: true
    }, { description: '创建需求接口' })

    // 需求统计Mock数据
    this.register('getDemandStatistics', {
      error: 0,
      message: '获取需求统计成功',
      body: {
        total_demands: 25,
        active_demands: 8,
        completed_demands: 15,
        matched_demands: 12,
        stats: {
          this_month: 8,
          last_month: 12,
          growth_rate: -33.3,
          avg_response_time: 2.5
        }
      },
      success: true
    }, { description: '获取需求统计接口' })

    // AOG管理相关Mock数据
    this.register('getAOGCases', {
      error: 0,
      message: '获取AOG案例列表成功',
      body: {
        cases: [
          {
            id: 1,
            case_number: 'AOG2024071701',
            aircraft_tail: 'B-1234',
            aircraft_type: 'A320',
            priority: 'critical',
            status: 'responding',
            fault_title: '左主起落架故障',
            location: '北京首都机场',
            created_at: '2024-07-17T10:00:00Z'
          }
        ]
      },
      success: true
    }, { description: '获取AOG案例列表接口' })

    // 数据分析相关Mock数据
    this.register('getAnalytics', {
      error: 0,
      message: '获取分析数据成功',
      body: {
        overview: {
          total_orders: 156,
          total_value: 12500000,
          avg_response_time: 2.5,
          customer_satisfaction: 98.5
        },
        charts: {
          monthly_orders: [
            { month: '1月', value: 45 },
            { month: '2月', value: 52 },
            { month: '3月', value: 48 },
            { month: '4月', value: 61 },
            { month: '5月', value: 55 },
            { month: '6月', value: 58 },
            { month: '7月', value: 62 }
          ]
        }
      },
      success: true
    }, { description: '获取分析数据接口' })

    // 维修管理相关Mock数据
    this.register('getWorkOrders', {
      error: 0,
      message: '获取维修工单列表成功',
      body: {
        work_orders: [
          {
            id: 1,
            work_order_number: 'WO2024071701',
            aircraft_tail: 'B-1234',
            aircraft_type: 'A320',
            priority: 'high',
            status: 'in_progress',
            fault_title: '左主起落架故障',
            fault_description: '左主起落架收放异常，需要检查液压系统',
            assigned_technician: {
              id: 1,
              name: '张师傅',
              level: '高级技师',
              specialty: '起落架系统'
            },
            estimated_hours: 8,
            actual_hours: 5.5,
            progress: 65,
            created_at: '2024-07-17T08:00:00Z',
            estimated_completion: '2024-07-17T18:00:00Z'
          },
          {
            id: 2,
            work_order_number: 'WO2024071702',
            aircraft_tail: 'B-5678',
            aircraft_type: 'B737',
            priority: 'aog',
            status: 'waiting_parts',
            fault_title: '发动机燃油泵故障',
            fault_description: '2号发动机燃油泵压力不足，需要更换',
            assigned_technician: {
              id: 2,
              name: '李师傅',
              level: '资深技师',
              specialty: '发动机系统'
            },
            estimated_hours: 12,
            actual_hours: 8,
            progress: 45,
            created_at: '2024-07-17T06:00:00Z',
            estimated_completion: '2024-07-18T10:00:00Z'
          }
        ],
        statistics: {
          total_work_orders: 156,
          in_progress: 23,
          waiting_parts: 8,
          completed_today: 12,
          avg_completion_time: 6.8,
          efficiency_rate: 92.5
        }
      },
      success: true
    }, { description: '获取维修工单列表接口' })

    this.register('getWorkOrderDetails', {
      error: 0,
      message: '获取工单详情成功',
      body: {
        work_order: {
          id: 1,
          work_order_number: 'WO2024071701',
          aircraft: {
            tail_number: 'B-1234',
            aircraft_type: 'A320',
            registration: 'B-1234',
            airline: '中国国际航空'
          },
          fault: {
            title: '左主起落架故障',
            description: '左主起落架收放异常，需要检查液压系统',
            category: '起落架系统',
            severity: 'high'
          },
          technician: {
            id: 1,
            name: '张师傅',
            level: '高级技师',
            specialty: '起落架系统',
            contact: '138****8888'
          },
          progress: {
            current: 65,
            estimated_hours: 8,
            actual_hours: 5.5,
            remaining_hours: 2.5
          },
          status: 'in_progress',
          created_at: '2024-07-17T08:00:00Z',
          estimated_completion: '2024-07-17T18:00:00Z',
          assigned_technician_id: 1
        },
        progress_history: [
          {
            id: 1,
            status: 'new',
            timestamp: '2024-07-17T08:00:00Z',
            operator: '调度员',
            notes: '工单创建，等待分配技师'
          },
          {
            id: 2,
            status: 'assigned',
            timestamp: '2024-07-17T08:30:00Z',
            operator: '张师傅',
            notes: '已分配技师，开始检查'
          },
          {
            id: 3,
            status: 'in_progress',
            timestamp: '2024-07-17T09:00:00Z',
            operator: '张师傅',
            notes: '开始拆解检查液压系统'
          }
        ],
        labor_records: [
          {
            id: 1,
            technician: '张师傅',
            start_time: '2024-07-17T09:00:00Z',
            end_time: '2024-07-17T12:00:00Z',
            hours: 3,
            description: '拆解检查液压系统，发现密封圈老化'
          },
          {
            id: 2,
            technician: '张师傅',
            start_time: '2024-07-17T13:00:00Z',
            end_time: '2024-07-17T15:30:00Z',
            hours: 2.5,
            description: '更换密封圈，进行系统测试'
          }
        ],
        parts_used: [
          {
            id: 1,
            part_name: '起落架密封圈',
            part_number: 'LG-SEAL-001',
            quantity: 2,
            unit_price: 580,
            total_price: 1160
          }
        ]
      },
      success: true
    }, { description: '获取工单详情接口' })

    this.register('getTechnicians', {
      error: 0,
      message: '获取技师列表成功',
      body: {
        technicians: [
          {
            id: 1,
            name: '张师傅',
            level: '高级技师',
            specialty: '起落架系统',
            certifications: ['A320起落架', 'B737起落架'],
            availability: 'busy',
            current_workload: 2,
            contact: '138****8888'
          },
          {
            id: 2,
            name: '李师傅',
            level: '资深技师',
            specialty: '发动机系统',
            certifications: ['CFM56发动机', 'V2500发动机'],
            availability: 'available',
            current_workload: 1,
            contact: '139****9999'
          },
          {
            id: 3,
            name: '王师傅',
            level: '中级技师',
            specialty: '航电系统',
            certifications: ['航电维修', '通信导航'],
            availability: 'available',
            current_workload: 0,
            contact: '136****6666'
          }
        ],
        skill_matrix: {
          '起落架系统': ['张师傅', '赵师傅'],
          '发动机系统': ['李师傅', '钱师傅'],
          '航电系统': ['王师傅', '孙师傅']
        }
      },
      success: true
    }, { description: '获取技师列表接口' })

    this.register('getMaintenanceSchedule', {
      error: 0,
      message: '获取维修计划成功',
      body: {
        scheduled_maintenance: [
          {
            id: 1,
            aircraft_tail: 'B-1234',
            aircraft_type: 'A320',
            maintenance_type: 'A检',
            scheduled_date: '2024-07-20T08:00:00Z',
            estimated_duration: 48,
            status: 'scheduled',
            assigned_team: '第一维修组',
            bay: '1号机库'
          },
          {
            id: 2,
            aircraft_tail: 'B-5678',
            aircraft_type: 'B737',
            maintenance_type: 'C检',
            scheduled_date: '2024-07-25T08:00:00Z',
            estimated_duration: 168,
            status: 'preparing',
            assigned_team: '第二维修组',
            bay: '2号机库'
          }
        ],
        resource_allocation: {
          'A检': { required_technicians: 4, available_technicians: 6 },
          'C检': { required_technicians: 12, available_technicians: 8 }
        },
        capacity_utilization: 0.75
      },
      success: true
    }, { description: '获取维修计划接口' })

    this.register('getMaintenanceStatistics', {
      error: 0,
      message: '获取维修统计数据成功',
      body: {
        overview: {
          completion_rate: 0.925,
          avg_repair_time: 6.8,
          on_time_delivery: 0.89,
          customer_satisfaction: 0.95
        },
        monthly_data: [
          { month: '1月', completed: 45, avg_time: 7.2, on_time: 0.87 },
          { month: '2月', completed: 52, avg_time: 6.9, on_time: 0.88 },
          { month: '3月', completed: 48, avg_time: 6.5, on_time: 0.91 },
          { month: '4月', completed: 61, avg_time: 6.8, on_time: 0.89 },
          { month: '5月', completed: 55, avg_time: 6.6, on_time: 0.90 },
          { month: '6月', completed: 58, avg_time: 6.4, on_time: 0.92 },
          { month: '7月', completed: 62, avg_time: 6.8, on_time: 0.89 }
        ],
        cost_analysis: {
          labor_cost: 1250000,
          parts_cost: 2890000,
          overhead_cost: 560000,
          total_cost: 4700000
        }
      },
      success: true
    }, { description: '获取维修统计数据接口' })

    // 门户首页相关Mock数据
    this.register('getPlatformStatistics', {
      error: 0,
      message: '获取平台统计数据成功',
      body: {
        total_users: 1500,
        total_materials: 50000,
        total_orders: 8000,
        total_transaction_amount: 500000000,
        monthly_growth: 15.5,
        active_suppliers: 200,
        completed_orders: 7500,
        avg_response_time: 2.5
      },
      success: true
    }, { description: '获取平台统计数据接口' })

    this.register('getHotMaterials', {
      error: 0,
      message: '获取热门航材成功',
      body: {
        materials: [
          {
            id: 1,
            part_number: 'CFM56-001',
            name: '发动机燃油喷嘴',
            category: '发动机',
            manufacturer: 'CFM International',
            search_count: 1250,
            order_count: 85,
            avg_price: 125000,
            availability: '现货',
            image_url: '/images/发动机燃油喷嘴.jpg',
            hot_score: 95.5
          },
          {
            id: 2,
            part_number: 'B737-002',
            name: '波音737机轮',
            category: '起落架',
            manufacturer: 'Boeing',
            search_count: 980,
            order_count: 62,
            avg_price: 85000,
            availability: '现货',
            image_url: '/images/机轮.jpg',
            hot_score: 88.2
          },
          {
            id: 3,
            part_number: 'A320-003',
            name: '空客A320航电设备',
            category: '航电',
            manufacturer: 'Airbus',
            search_count: 876,
            order_count: 45,
            avg_price: 215000,
            availability: '预订',
            image_url: '/images/航电1.jpg',
            hot_score: 82.3
          }
        ]
      },
      success: true
    }, { description: '获取热门航材推荐接口' })

    this.register('getIndustryNews', {
      error: 0,
      message: '获取行业动态成功',
      body: {
        news: [
          {
            id: 1,
            title: '中国商飞C919获得欧洲航空安全局认证',
            summary: '中国商飞C919客机正式获得欧洲航空安全局（EASA）的型号认证，标志着中国民用航空制造业的重要里程碑。',
            category: 'industry',
            source: '航空工业周刊',
            publish_date: '2025-07-18',
            image_url: '/images/飞机6.jpg',
            read_count: 1250,
            importance: 'high'
          },
          {
            id: 2,
            title: '波音737 MAX系列飞机全面复飞获批',
            summary: '经过全面安全审查和系统升级，波音737 MAX系列飞机获得全球航空监管机构批准，正式恢复商业运营。',
            category: 'industry',
            source: '国际航空报',
            publish_date: '2025-07-17',
            image_url: '/images/飞机3.jpg',
            read_count: 980,
            importance: 'high'
          },
          {
            id: 3,
            title: '空客A350-1000完成首次跨太平洋试飞',
            summary: '空客A350-1000宽体客机成功完成首次跨太平洋试飞，验证了其在长航程运营中的性能和可靠性。',
            category: 'industry',
            source: '航空技术杂志',
            publish_date: '2025-07-16',
            image_url: '/images/飞机4.jpg',
            read_count: 756,
            importance: 'medium'
          }
        ],
        total: 50,
        page: 1,
        per_page: 10,
        pages: 5
      },
      success: true
    }, { description: '获取行业动态新闻接口' })

    this.register('getSuccessCases', {
      error: 0,
      message: '获取成功案例成功',
      body: {
        cases: [
          {
            id: 1,
            title: '东方航空紧急AOG航材保障',
            description: '在48小时内为东方航空提供紧急航材，确保航班正常运行，避免了大规模航班延误。',
            client: '东方航空',
            material_type: '发动机部件',
            save_cost: 1200000,
            response_time: '48小时',
            success_rate: '100%',
            image_url: '/images/发动机2.jpg',
            case_type: 'aog'
          },
          {
            id: 2,
            title: '南方航空周转件优化方案',
            description: '通过智能周转件管理，帮助南方航空降低35%的库存成本，提高资金周转效率。',
            client: '南方航空',
            material_type: '起落架组件',
            save_cost: 2500000,
            response_time: '7天',
            success_rate: '98%',
            image_url: '/images/机轮2.jpg',
            case_type: 'cost_saving'
          },
          {
            id: 3,
            title: '海南航空批量采购项目',
            description: '为海南航空提供批量航材采购服务，实现年度采购成本节约20%。',
            client: '海南航空',
            material_type: '航电设备',
            save_cost: 1800000,
            response_time: '30天',
            success_rate: '95%',
            image_url: '/images/航电2.jpg',
            case_type: 'bulk_purchase'
          }
        ]
      },
      success: true
    }, { description: '获取成功案例接口' })

    this.register('getMarketAnalysis', {
      error: 0,
      message: '获取市场分析数据成功',
      body: {
        analysis: {
          market_size: 58500000000,
          growth_rate: 12.5,
          hot_categories: ['发动机', '航电', '起落架', '机体结构', '液压系统'],
          regional_data: [
            { region: '亚太', market_share: 35.2, growth: 15.8 },
            { region: '北美', market_share: 28.6, growth: 9.2 },
            { region: '欧洲', market_share: 22.1, growth: 11.5 },
            { region: '其他', market_share: 14.1, growth: 8.3 }
          ],
          price_trends: [
            { category: '发动机', trend: 'up', change: 8.5 },
            { category: '航电', trend: 'up', change: 12.3 },
            { category: '起落架', trend: 'stable', change: 2.1 },
            { category: '机体结构', trend: 'up', change: 6.8 },
            { category: '液压系统', trend: 'down', change: -3.2 }
          ],
          demand_forecast: [
            { month: '2025-07', demand: 850 },
            { month: '2025-08', demand: 920 },
            { month: '2025-09', demand: 1050 },
            { month: '2025-10', demand: 1180 },
            { month: '2025-11', demand: 1280 },
            { month: '2025-12', demand: 1350 }
          ]
        }
      },
      success: true
    }, { description: '获取市场分析数据接口' })

    // 共享件管理相关Mock数据
    this.register('publishSharedMaterial', {
      error: 0,
      message: '共享件发布成功，等待审核',
      body: {
        shared_material_id: 1,
        status: 'pending_review',
        workflow_instance_id: 1,
        message: '共享件发布成功，等待审核'
      },
      success: true
    }, { description: '发布共享件接口' })

    this.register('getSharedMaterials', {
      error: 0,
      message: '获取共享件列表成功',
      body: {
        shared_materials: [
          {
            id: 1,
            material: {
              id: 1,
              name: 'CFM56发动机燃油喷嘴',
              part_number: 'CFM56-7B-001',
              category: '发动机',
              manufacturer: 'CFM International',
              aircraft_type: 'A320',
              image_url: '/images/发动机燃油喷嘴.jpg'
            },
            owner: {
              id: 2,
              name: '张工程师',
              company: '中航材供应商',
              user_type: 'supplier'
            },
            share_type: 'sale',
            share_quantity: 10,
            available_quantity: 8,
            reserved_quantity: 2,
            price: 125000,
            min_order_quantity: 1,
            description: 'CFM56发动机燃油喷嘴，状态良好，可立即交付',
            sharing_policy: 'immediate',
            status: 'approved',
            expiry_date: '2025-12-31T00:00:00',
            created_at: '2024-07-15T10:00:00Z',
            updated_at: '2024-07-16T14:30:00Z'
          },
          {
            id: 2,
            material: {
              id: 2,
              name: 'B737主起落架机轮',
              part_number: 'WHL-B737-001',
              category: '起落架',
              manufacturer: '霍尼韦尔',
              aircraft_type: 'B737',
              image_url: '/images/机轮.jpg'
            },
            owner: {
              id: 3,
              name: '李主任',
              company: '维修企业',
              user_type: 'maintenance'
            },
            share_type: 'lease',
            share_quantity: 5,
            available_quantity: 3,
            reserved_quantity: 2,
            price: 89000,
            min_order_quantity: 1,
            description: '原厂机轮，经过严格质量检测，支持租赁',
            sharing_policy: 'approval',
            status: 'approved',
            expiry_date: '2025-10-31T00:00:00',
            created_at: '2024-07-14T08:00:00Z',
            updated_at: '2024-07-15T16:20:00Z'
          },
          {
            id: 3,
            material: {
              id: 3,
              name: 'A320航电设备模块',
              part_number: 'AV-A320-003',
              category: '航电',
              manufacturer: 'Airbus',
              aircraft_type: 'A320',
              image_url: '/images/航电1.jpg'
            },
            owner: {
              id: 4,
              name: '王经理',
              company: '东方航空',
              user_type: 'airline'
            },
            share_type: 'exchange',
            share_quantity: 3,
            available_quantity: 2,
            reserved_quantity: 1,
            price: 215000,
            min_order_quantity: 1,
            description: '高性能航电设备，可交换同类型产品',
            sharing_policy: 'inquiry',
            status: 'approved',
            expiry_date: null,
            created_at: '2024-07-13T12:00:00Z',
            updated_at: '2024-07-14T10:15:00Z'
          }
        ],
        total: 3,
        page: 1,
        per_page: 20,
        pages: 1
      },
      success: true
    }, { description: '获取共享件列表接口' })

    this.register('getSharedMaterialDetail', {
      error: 0,
      message: '获取共享件详情成功',
      body: {
        shared_material: {
          id: 1,
          share_type: 'sale',
          share_quantity: 10,
          available_quantity: 8,
          reserved_quantity: 2,
          price: 125000,
          min_order_quantity: 1,
          description: 'CFM56发动机燃油喷嘴，状态良好，可立即交付。经过全面检测，性能优异。',
          sharing_policy: 'immediate',
          status: 'approved',
          expiry_date: '2025-12-31T00:00:00',
          created_at: '2024-07-15T10:00:00Z',
          updated_at: '2024-07-16T14:30:00Z'
        },
        material: {
          id: 1,
          name: 'CFM56发动机燃油喷嘴',
          part_number: 'CFM56-7B-001',
          category: '发动机',
          manufacturer: 'CFM International',
          aircraft_type: 'A320',
          model: 'CFM56-7B',
          specifications: {
            '工作压力': '1500 PSI',
            '流量': '180 GPH',
            '重量': '2.5 kg',
            '材质': '钛合金'
          },
          image_url: '/images/发动机燃油喷嘴.jpg',
          description: '高性能燃油喷嘴，适用于A320系列飞机CFM56发动机'
        },
        owner: {
          id: 2,
          name: '张工程师',
          company: '中航材供应商',
          user_type: 'supplier',
          phone: '138****8888'
        },
        inquiry_count: 15,
        recent_inquiries: [
          {
            id: 1,
            user_name: '李采购',
            company: '南方航空',
            quantity: 2,
            message: '需要2个，请问价格是否可以优惠？',
            created_at: '2024-07-16T09:00:00Z'
          }
        ]
      },
      success: true
    }, { description: '获取共享件详情接口' })

    this.register('getMyPublishedMaterials', {
      error: 0,
      message: '获取我发布的共享件成功',
      body: {
        shared_materials: [
          {
            id: 1,
            material: {
              id: 1,
              name: 'CFM56发动机燃油喷嘴',
              part_number: 'CFM56-7B-001',
              category: '发动机',
              manufacturer: 'CFM International',
              image_url: '/images/发动机燃油喷嘴.jpg'
            },
            share_type: 'sale',
            share_quantity: 10,
            available_quantity: 8,
            price: 125000,
            status: 'approved',
            created_at: '2024-07-15T10:00:00Z',
            updated_at: '2024-07-16T14:30:00Z'
          },
          {
            id: 2,
            material: {
              id: 2,
              name: 'B737主起落架机轮',
              part_number: 'WHL-B737-001',
              category: '起落架',
              manufacturer: '霍尼韦尔',
              image_url: '/images/机轮.jpg'
            },
            share_type: 'lease',
            share_quantity: 5,
            available_quantity: 3,
            price: 89000,
            status: 'pending_review',
            created_at: '2024-07-17T08:00:00Z',
            updated_at: '2024-07-17T08:00:00Z'
          }
        ],
        statistics: {
          total: 15,
          approved: 10,
          pending_review: 3,
          rejected: 2
        },
        total: 2,
        page: 1,
        per_page: 20,
        pages: 1
      },
      success: true
    }, { description: '获取我发布的共享件接口' })

    this.register('approveSharedMaterial', {
      error: 0,
      message: '审核操作成功',
      body: {
        message: '审核操作成功',
        new_status: 'approved'
      },
      success: true
    }, { description: '审核共享件接口' })

    this.register('createInquiry', {
      error: 0,
      message: '询价创建成功',
      body: {
        inquiry_id: 1,
        message: '询价创建成功'
      },
      success: true
    }, { description: '创建询价接口' })

    this.register('getMaterialCategories', {
      error: 0,
      message: '获取航材分类成功',
      body: {
        categories: [
          {
            name: '发动机',
            count: 150,
            subcategories: ['燃油系统', '点火系统', '控制系统', '叶片组件', '齿轮传动']
          },
          {
            name: '起落架',
            count: 89,
            subcategories: ['机轮轮胎', '刹车系统', '液压系统', '减震器', '起落架结构']
          },
          {
            name: '航电',
            count: 234,
            subcategories: ['飞行控制', '通信导航', '显示系统', '计算机模块', '传感器']
          },
          {
            name: '机体结构',
            count: 178,
            subcategories: ['机身面板', '机翼组件', '尾翼结构', '舱门组件', '内饰件']
          },
          {
            name: '液压系统',
            count: 67,
            subcategories: ['液压泵', '过滤器', '管路接头', '蓄压器', '控制阀']
          },
          {
            name: '燃油系统',
            count: 45,
            subcategories: ['燃油泵', '燃油过滤器', '油箱组件', '燃油管路', '油量传感器']
          }
        ]
      },
      success: true
    }, { description: '获取航材分类接口' })

    this.register('getSearchSuggestions', {
      error: 0,
      message: '获取搜索建议成功',
      body: {
        suggestions: [
          {
            type: 'material',
            text: 'CFM56发动机燃油喷嘴',
            part_number: 'CFM56-7B-001',
            manufacturer: 'CFM International'
          },
          {
            type: 'material',
            text: 'CFM56风扇叶片',
            part_number: 'CFM56-FB-002',
            manufacturer: 'CFM International'
          },
          {
            type: 'material',
            text: 'CFM56控制模块',
            part_number: 'CFM56-CM-003',
            manufacturer: 'CFM International'
          }
        ]
      },
      success: true
    }, { description: '获取搜索建议接口' })

    // 角色管理相关Mock数据
    this.register('roles', {
      error: 0,
      message: '获取角色列表成功',
      body: {
        roles: [
          {
            id: 1,
            role_code: 'supplier',
            role_name: 'supplier',
            display_name: '供应商',
            description: '航材制造商，专注生产和销售',
            category: 'external',
            business_type: 'sell_only',
            is_active: true,
            sort_order: 1,
            icon_name: 'Factory',
            theme_color: '#10B981'
          },
          {
            id: 2,
            role_code: 'distributor',
            role_name: 'distributor',
            display_name: '分销商',
            description: '航材贸易商，连接供需两端',
            category: 'external',
            business_type: 'buy_and_sell',
            is_active: true,
            sort_order: 2,
            icon_name: 'TruckDelivery',
            theme_color: '#3B82F6'
          },
          {
            id: 3,
            role_code: 'airline_buyer',
            role_name: 'airline_buyer',
            display_name: '航空公司',
            description: '航材需求方，专注采购和使用',
            category: 'external',
            business_type: 'buy_only',
            is_active: true,
            sort_order: 3,
            icon_name: 'Airplane',
            theme_color: '#8B5CF6'
          },
          {
            id: 4,
            role_code: 'platform_staff',
            role_name: 'platform_staff',
            display_name: '平台员工',
            description: '平台运营管理人员',
            category: 'internal',
            business_type: 'service',
            is_active: true,
            sort_order: 4,
            icon_name: 'UserGroup',
            theme_color: '#F59E0B'
          },
          {
            id: 5,
            role_code: 'maintenance_engineer',
            role_name: 'maintenance_engineer',
            display_name: '维修工程师',
            description: '提供技术支持和维修服务',
            category: 'internal',
            business_type: 'service',
            is_active: true,
            sort_order: 5,
            icon_name: 'Wrench',
            theme_color: '#EF4444'
          },
          {
            id: 6,
            role_code: 'logistics_specialist',
            role_name: 'logistics_specialist',
            display_name: '物流专员',
            description: '负责配送跟踪和物流管理',
            category: 'internal',
            business_type: 'service',
            is_active: true,
            sort_order: 6,
            icon_name: 'Truck',
            theme_color: '#06B6D4'
          },
          {
            id: 7,
            role_code: 'admin',
            role_name: 'admin',
            display_name: '系统管理员',
            description: '系统配置和用户管理',
            category: 'internal',
            business_type: 'service',
            is_active: true,
            sort_order: 7,
            icon_name: 'Settings',
            theme_color: '#6366F1'
          }
        ]
      },
      success: true
    }, { description: '获取角色列表接口' })

    // 权限管理Mock数据
    this.register('permissions', {
      error: 0,
      message: '获取权限列表成功',
      body: {
        permissions: [
          {
            id: 1,
            permission_code: 'view_own_data',
            permission_name: '查看自己的数据',
            description: '用户可以查看自己的数据',
            category: '基础权限',
            module: 'user'
          },
          {
            id: 2,
            permission_code: 'manage_inventory',
            permission_name: '管理库存',
            description: '管理库存信息',
            category: '业务权限',
            module: 'inventory'
          },
          {
            id: 3,
            permission_code: 'publish_shared',
            permission_name: '发布共享件',
            description: '发布共享件到平台',
            category: '业务权限',
            module: 'shared'
          },
          {
            id: 4,
            permission_code: 'publish_demand',
            permission_name: '发布需求',
            description: '发布采购需求',
            category: '业务权限',
            module: 'demand'
          },
          {
            id: 5,
            permission_code: 'manage_orders',
            permission_name: '管理订单',
            description: '管理订单信息',
            category: '业务权限',
            module: 'order'
          },
          {
            id: 6,
            permission_code: 'user_management',
            permission_name: '用户管理',
            description: '管理系统用户',
            category: '管理权限',
            module: 'admin'
          },
          {
            id: 7,
            permission_code: 'role_management',
            permission_name: '角色管理',
            description: '管理系统角色',
            category: '管理权限',
            module: 'admin'
          }
        ]
      },
      success: true
    }, { description: '获取权限列表接口' })

    // 邀请码验证Mock数据
    this.register('validateInvitation', {
      error: 0,
      message: '邀请码验证成功',
      body: {
        valid: true,
        allowed_roles: ['platform_staff', 'maintenance_engineer', 'logistics_specialist'],
        expires_at: '2025-08-24T23:59:59Z'
      },
      success: true
    }, { description: '验证邀请码接口' })

    // 权限分类Mock数据
    this.register('permissionCategories', {
      error: 0,
      message: '获取权限分类成功',
      body: {
        categories: ['基础权限', '业务权限', '管理权限']
      },
      success: true
    }, { description: '获取权限分类接口' })

    // 用户权限Mock数据
    this.register('myPermissions', {
      error: 0,
      message: '获取用户权限成功',
      body: {
        permissions: [
          {
            id: 1,
            permission_code: 'view_own_data',
            permission_name: '查看自己的数据'
          },
          {
            id: 2,
            permission_code: 'manage_inventory',
            permission_name: '管理库存'
          }
        ]
      },
      success: true
    }, { description: '获取当前用户权限接口' })

    // 用户注册Mock数据
    this.register('register', {
      error: 0,
      message: '注册成功',
      body: {
        user_id: Date.now(),
        user: {
          id: Date.now(),
          username: 'test_user',
          email: '<EMAIL>',
          user_type: 'supplier',
          company_name: '测试公司'
        }
      },
      success: true
    }, { description: '用户注册接口' })

    // 邀请码注册Mock数据
    this.register('registerWithInvitation', {
      error: 0,
      message: '使用邀请码注册成功',
      body: {
        user_id: Date.now(),
        user: {
          id: Date.now(),
          username: 'staff_user',
          email: '<EMAIL>',
          user_type: 'platform_staff',
          company_name: '航材共享平台'
        }
      },
      success: true
    }, { description: '邀请码注册接口' })

    console.log('[MockManager] 初始化Mock数据完成，共注册', this.mockData.size, '个接口')
  }

  /**
   * 创建标准错误响应
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @returns {object} 错误响应对象
   */
  createErrorResponse(message, code = 500) {
    return {
      error: code,
      message: message,
      body: {},
      success: false
    }
  }

  /**
   * 创建标准成功响应
   * @param {any} data - 响应数据
   * @param {string} message - 成功消息
   * @returns {object} 成功响应对象
   */
  createSuccessResponse(data, message = '操作成功') {
    return {
      error: 0,
      message: message,
      body: data,
      success: true
    }
  }
}

// 创建单例实例
const mockManager = new MockManager()

// 导出单例实例
export default mockManager

// 也可以导出类，用于测试或其他用途
export { MockManager }