/**
 * 订单管理API - 优化版本
 * 使用新的数据提供器，支持智能数据源选择
 * 优先级：真实API > 缓存 > Mock备用
 * 
 * 功能描述：订单创建、查询、状态管理、审批等功能
 * 作者：Claude AI Assistant
 * 创建时间：2025-07-21
 */

import { dataProvider } from './dataProvider.js'
import request from '@/utils/request.js'

/**
 * 获取订单列表
 * 功能描述：获取用户的订单列表，支持筛选和分页
 * 入参：{ status: string, type: string, date_from: string, date_to: string, page: number, size: number }
 * 返回参数：{ total, page, size, pages, items: [{ id, order_number, material_info, supplier_info, status, total_amount, created_at }] }
 * url地址：/orders
 * 请求方式：GET
 */
export async function getOrders(params = {}) {
  try {
    return await dataProvider.getData('/orders/', params, {
      useCache: true,
      cacheTtl: 2 * 60 * 1000, // 2分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取订单列表失败:', error)
    throw error
  }
}

/**
 * 获取订单详情
 * 功能描述：获取指定订单的详细信息
 * 入参：{ id: number }
 * 返回参数：{ id, order_number, buyer_info, supplier_info, material_info, quantity, unit_price, total_amount, status, delivery_info, created_at, updated_at }
 * url地址：/orders/{id}
 * 请求方式：GET
 */
export async function getOrderDetail(id) {
  try {
    return await dataProvider.getData(`/orders/${id}/`, {}, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取订单详情失败:', error)
    throw error
  }
}

/**
 * 创建订单
 * 功能描述：创建新的采购订单
 * 入参：{ material_id: number, supplier_id: number, quantity: number, delivery_address: string, delivery_date: string, special_requirements: string }
 * 返回参数：{ order_id, order_number, message }
 * url地址：/orders
 * 请求方式：POST
 */
export async function createOrder(orderData) {
  try {
    // 直接调用真实API，不使用缓存
    const response = await request({
      url: '/orders/',
      method: 'post',
      data: orderData
    })
    
    // 成功后添加到动态数据
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      await mockManager.addDynamicData('orders', {
        ...orderData,
        id: response.body?.order_id || Date.now(),
        order_number: response.body?.order_number || `PO${Date.now()}`,
        status: 'pending',
        total_amount: (orderData.quantity || 0) * (orderData.unit_price || 0),
        created_at: new Date().toISOString()
      })
    }
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 创建订单失败:', error)
    throw error
  }
}

/**
 * 更新订单状态
 * 功能描述：更新订单的状态信息
 * 入参：{ id: number, status: string, reason: string, notes: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/status
 * 请求方式：PUT
 */
export async function updateOrderStatus(id, statusData) {
  try {
    const response = await request({
      url: `/orders/${id}/status/`,
      method: 'put',
      data: statusData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 更新订单状态失败:', error)
    throw error
  }
}

/**
 * 取消订单
 * 功能描述：取消指定的订单
 * 入参：{ id: number, reason: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/cancel
 * 请求方式：POST
 */
export async function cancelOrder(id, reason) {
  try {
    const response = await request({
      url: `/orders/${id}/cancel/`,
      method: 'post',
      data: { reason }
    })
    
    // 清除缓存并更新动态数据
    dataProvider.clearCache()
    
    const { mockController } = await import('@/config/environment.js')
    const mockManager = await mockController.getMockManager()
    if (mockManager) {
      // 更新动态数据中的订单状态
      const orders = mockManager.dynamicData.orders.map(order => {
        if (order.id === parseInt(id)) {
          return { ...order, status: 'cancelled', cancel_reason: reason }
        }
        return order
      })
      mockManager.dynamicData.orders = orders
      mockManager.saveDynamicData('orders', orders)
    }
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 取消订单失败:', error)
    throw error
  }
}

/**
 * 获取订单统计
 * 功能描述：获取用户的订单统计数据
 * 入参：无
 * 返回参数：{ pending_count, processing_count, shipping_count, completed_count, total_amount }
 * url地址：/orders/statistics
 * 请求方式：GET
 */
export async function getOrderStatistics() {
  try {
    return await dataProvider.getData('/orders/statistics/', {}, {
      useCache: true,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      cacheFirst: true, // 统计数据可以优先使用缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取订单统计失败:', error)
    throw error
  }
}

/**
 * 审批订单
 * 功能描述：审批订单
 * 入参：{ id: number, action: string, approval_level: number, comments: string }
 * 返回参数：{ message }
 * url地址：/orders/{id}/approve
 * 请求方式：POST
 */
export async function approveOrder(id, approvalData) {
  try {
    const response = await request({
      url: `/orders/${id}/approve/`,
      method: 'post',
      data: approvalData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 审批订单失败:', error)
    throw error
  }
}

/**
 * 获取订单状态历史
 * 功能描述：获取订单的状态变更历史
 * 入参：{ id: number }
 * 返回参数：状态历史列表
 * url地址：/orders/{id}/history
 * 请求方式：GET
 */
export async function getOrderHistory(id) {
  try {
    return await dataProvider.getData(`/orders/${id}/history/`, {}, {
      useCache: true,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取订单历史失败:', error)
    throw error
  }
}

/**
 * 批量更新订单
 * 功能描述：批量更新订单状态或信息
 * 入参：{ ids: [1, 2, 3], action: "approve" | "reject" | "ship", data: {} }
 * 返回参数：{ success_count, failed_count, results: [...] }
 * url地址：/orders/batch-update
 * 请求方式：POST
 */
export async function batchUpdateOrders(batchData) {
  try {
    const response = await request({
      url: '/orders/batch-update/',
      method: 'post',
      data: batchData
    })
    
    // 清除所有相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 批量更新订单失败:', error)
    throw error
  }
}

/**
 * 导出订单
 * 功能描述：导出订单数据为Excel或PDF
 * 入参：{ format: "excel" | "pdf", filters: {...} }
 * 返回参数：文件下载链接
 * url地址：/orders/export
 * 请求方式：POST
 */
export async function exportOrders(exportData) {
  try {
    const response = await request({
      url: '/orders/export/',
      method: 'post',
      data: exportData,
      responseType: 'blob'
    })
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 导出订单失败:', error)
    throw error
  }
}

/**
 * 获取待审批订单
 * 功能描述：获取需要当前用户审批的订单列表
 * 入参：{ page: number, size: number }
 * 返回参数：待审批订单列表
 * url地址：/orders/pending-approval
 * 请求方式：GET
 */
export async function getPendingApprovalOrders(params = {}) {
  try {
    return await dataProvider.getData('/orders/pending-approval/', params, {
      useCache: true,
      cacheTtl: 1 * 60 * 1000, // 1分钟缓存（审批数据更新频繁）
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取待审批订单失败:', error)
    throw error
  }
}

/**
 * 获取订单追踪信息
 * 功能描述：获取订单的物流追踪信息
 * 入参：{ id: number }
 * 返回参数：{ tracking_number, carrier, status, history: [...] }
 * url地址：/orders/{id}/tracking
 * 请求方式：GET
 */
export async function getOrderTracking(id) {
  try {
    return await dataProvider.getData(`/orders/${id}/tracking/`, {}, {
      useCache: true,
      cacheTtl: 3 * 60 * 1000, // 3分钟缓存
      enableFallback: true
    })
  } catch (error) {
    console.error('[OrdersAPI] 获取订单追踪信息失败:', error)
    throw error
  }
}

/**
 * 订单评价
 * 功能描述：对已完成的订单进行评价
 * 入参：{ id: number, rating: number, comment: string, tags: [...] }
 * 返回参数：{ success: true, message: "评价成功" }
 * url地址：/orders/{id}/review
 * 请求方式：POST
 */
export async function reviewOrder(id, reviewData) {
  try {
    const response = await request({
      url: `/orders/${id}/review/`,
      method: 'post',
      data: reviewData
    })
    
    // 清除相关缓存
    dataProvider.clearCache()
    
    return response
  } catch (error) {
    console.error('[OrdersAPI] 订单评价失败:', error)
    throw error
  }
}

// 导出API集合（保持向后兼容）
export const ordersApi = {
  getOrders,
  getOrderDetail,
  createOrder,
  updateOrderStatus,
  cancelOrder,
  getOrderStatistics,
  approveOrder,
  getOrderHistory,
  batchUpdateOrders,
  exportOrders,
  getPendingApprovalOrders,
  getOrderTracking,
  reviewOrder
}