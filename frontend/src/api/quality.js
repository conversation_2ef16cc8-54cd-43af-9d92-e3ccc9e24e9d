import request from '@/utils/request'

/**
 * 获取质量证书列表
 * 功能描述：获取航材质量证书和适航文件列表
 * 入参：{ part_number: string, certificate_type: string, status: string, expiry_date: string, page: number, size: number }
 * 返回参数：{ certificates: array, total: number, expiring_soon: number, statistics: object }
 * url地址：/quality/certificates
 * 请求方式：GET
 */
export function getCertificates(params = {}) {
  return request({
    url: '/quality/certificates',
    method: 'get',
    params
  })
}

/**
 * 创建质量证书
 * 功能描述：创建新的质量证书记录
 * 入参：{ part_number: string, certificate_type: string, issuing_authority: string, issue_date: string, expiry_date: string, documents: array }
 * 返回参数：{ certificate_id: string, message: string, verification_status: string }
 * url地址：/quality/certificates
 * 请求方式：POST
 */
export function createCertificate(certificateData) {
  return request({
    url: '/quality/certificates',
    method: 'post',
    data: certificateData
  })
}

/**
 * 获取证书详情
 * 功能描述：获取指定质量证书的详细信息
 * 入参：{ certificate_id: string }
 * 返回参数：{ certificate: object, documents: array, verification_history: array, related_parts: array }
 * url地址：/quality/certificates/{id}
 * 请求方式：GET
 */
export function getCertificateDetails(certificateId) {
  return request({
    url: `/quality/certificates/${certificateId}`,
    method: 'get'
  })
}

/**
 * 更新证书状态
 * 功能描述：更新质量证书的状态和有效性
 * 入参：{ certificate_id: string, status: string, notes: string, verified_by: string }
 * 返回参数：{ message: string, updated_certificate: object, next_review_date: string }
 * url地址：/quality/certificates/{id}/status
 * 请求方式：PATCH
 */
export function updateCertificateStatus(certificateId, statusData) {
  return request({
    url: `/quality/certificates/${certificateId}/status`,
    method: 'patch',
    data: statusData
  })
}

/**
 * 验证证书真实性
 * 功能描述：验证质量证书的真实性和有效性
 * 入参：{ certificate_id: string, verification_method: string, external_reference: string }
 * 返回参数：{ verification_result: boolean, verification_details: object, confidence_score: number }
 * url地址：/quality/certificates/{id}/verify
 * 请求方式：POST
 */
export function verifyCertificate(certificateId, verificationData) {
  return request({
    url: `/quality/certificates/${certificateId}/verify`,
    method: 'post',
    data: verificationData
  })
}

/**
 * 获取合规检查清单
 * 功能描述：获取质量合规检查项目和要求
 * 入参：{ part_category: string, regulation: string, checklist_type: string }
 * 返回参数：{ checklist_items: array, requirements: object, compliance_score: number }
 * url地址：/quality/compliance/checklist
 * 请求方式：GET
 */
export function getComplianceChecklist(params = {}) {
  return request({
    url: '/quality/compliance/checklist',
    method: 'get',
    params
  })
}

/**
 * 执行合规检查
 * 功能描述：对航材进行合规性检查和评估
 * 入参：{ part_id: string, checklist_id: string, check_results: array, inspector: string }
 * 返回参数：{ compliance_status: string, failed_items: array, recommendations: array, report_id: string }
 * url地址：/quality/compliance/check
 * 请求方式：POST
 */
export function performComplianceCheck(checkData) {
  return request({
    url: '/quality/compliance/check',
    method: 'post',
    data: checkData
  })
}

/**
 * 获取质量测试记录
 * 功能描述：获取航材质量测试和检验记录
 * 入参：{ part_number: string, test_type: string, date_range: object, status: string }
 * 返回参数：{ test_records: array, statistics: object, trends: array }
 * url地址：/quality/test-records
 * 请求方式：GET
 */
export function getTestRecords(params = {}) {
  return request({
    url: '/quality/test-records',
    method: 'get',
    params
  })
}

/**
 * 创建质量测试记录
 * 功能描述：创建新的质量测试记录
 * 入参：{ part_id: string, test_type: string, test_parameters: object, results: object, tester: string }
 * 返回参数：{ test_record_id: string, pass_status: boolean, quality_grade: string }
 * url地址：/quality/test-records
 * 请求方式：POST
 */
export function createTestRecord(testData) {
  return request({
    url: '/quality/test-records',
    method: 'post',
    data: testData
  })
}

/**
 * 获取供应商质量评估
 * 功能描述：获取供应商质量表现和评估报告
 * 入参：{ supplier_id: string, time_period: string, evaluation_criteria: array }
 * 返回参数：{ quality_score: number, performance_metrics: object, improvement_areas: array, certification_status: object }
 * url地址：/quality/supplier-assessment/{supplier_id}
 * 请求方式：GET
 */
export function getSupplierQualityAssessment(supplierId, params = {}) {
  return request({
    url: `/quality/supplier-assessment/${supplierId}`,
    method: 'get',
    params
  })
}

/**
 * 创建质量问题报告
 * 功能描述：创建质量问题和不符合项报告
 * 入参：{ part_id: string, issue_type: string, description: string, severity: string, reporter: string, evidence: array }
 * 返回参数：{ issue_id: string, tracking_number: string, assigned_team: object, estimated_resolution_time: string }
 * url地址：/quality/issue-report
 * 请求方式：POST
 */
export function createQualityIssueReport(issueData) {
  return request({
    url: '/quality/issue-report',
    method: 'post',
    data: issueData
  })
}

/**
 * 获取质量统计数据
 * 功能描述：获取质量管理相关的统计数据和KPI
 * 入参：{ time_range: string, metric_type: string, grouping: string }
 * 返回参数：{ quality_metrics: object, trend_analysis: array, benchmark_comparison: object }
 * url地址：/quality/statistics
 * 请求方式：GET
 */
export function getQualityStatistics(params = {}) {
  return request({
    url: '/quality/statistics',
    method: 'get',
    params
  })
}

/**
 * 生成质量报告
 * 功能描述：生成质量管理报告和合规报告
 * 入参：{ report_type: string, period: object, include_sections: array, format: string }
 * 返回参数：{ report_id: string, download_url: string, generation_status: string }
 * url地址：/quality/generate-report
 * 请求方式：POST
 */
export function generateQualityReport(reportConfig) {
  return request({
    url: '/quality/generate-report',
    method: 'post',
    data: reportConfig
  })
}

export const qualityApi = {
  getCertificates,
  createCertificate,
  getCertificateDetails,
  updateCertificateStatus,
  verifyCertificate,
  getComplianceChecklist,
  performComplianceCheck,
  getTestRecords,
  createTestRecord,
  getSupplierQualityAssessment,
  createQualityIssueReport,
  getQualityStatistics,
  generateQualityReport
}