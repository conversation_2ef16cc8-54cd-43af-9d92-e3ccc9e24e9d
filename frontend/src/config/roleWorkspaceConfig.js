/**
 * 用户角色工作台配置文件
 * 版本: 2.0
 * 创建时间: 2025-07-19
 * 
 * 为每个用户角色定义专属的工作台布局、菜单结构和功能模块
 */

import { 
  Grid, Document, ShoppingCart, Box, Folder, Tools, 
  DataAnalysis, Plus, Monitor, Setting, User, Phone,
  Van, Warning, Share, Message, DataLine, Search
} from '@element-plus/icons-vue'

// 用户角色工作台配置
export const ROLE_WORKSPACE_CONFIG = {
  // 航空公司采购员 - 专注采购功能（重构后）
  airline_buyer: {
    name: '航空公司采购员',
    description: '专注航材采购需求和供应商管理',
    theme: {
      primaryColor: '#1890ff',
      gradientFrom: '#1890ff',
      gradientTo: '#722ed1'
    },
    
    // 主导航菜单
    navigation: [
      { 
        name: 'AirlineBuyerWorkspace', 
        label: '我的工作台', 
        path: '/app/workspace/airline-buyer',
        icon: Grid,
        permission: 'view_own_data'
      },
      { 
        name: 'DemandManagement', 
        label: '需求管理', 
        path: '/app/demands',
        icon: Document,
        permission: 'publish_demand'
      },
      { 
        name: 'Marketplace', 
        label: '航材市场', 
        path: '/app/marketplace',
        icon: ShoppingCart,
        permission: 'view_inventory'
      },
      { 
        name: 'Orders', 
        label: '我的订单', 
        path: '/app/orders',
        icon: Box,
        permission: 'manage_orders'
      },
      { 
        name: 'Inventory', 
        label: '库存管理', 
        path: '/app/inventory',
        icon: Folder,
        permission: 'manage_own_inventory'
      },
      { 
        name: 'SharedMaterials', 
        label: '共享管理', 
        path: '/app/shared-materials',
        icon: Monitor,
        permission: 'publish_shared'
      }
    ],

    // 工作台快速操作卡片
    quickActions: [
      {
        id: 'publish_demand',
        title: '发布需求',
        description: '快速发布航材采购需求',
        icon: Plus,
        color: 'bg-primary-500',
        permission: 'publish_demand',
        action: 'openPublishDemandDialog'
      },
      {
        id: 'browse_market',
        title: '浏览市场',
        description: '查看可用的航材资源',
        icon: ShoppingCart,
        color: 'bg-success-500',
        permission: 'view_inventory',
        action: 'navigateToMarketplace'
      },
      {
        id: 'manage_inventory',
        title: '管理库存',
        description: '查看和管理我的库存',
        icon: Folder,
        color: 'bg-warning-500',
        permission: 'manage_own_inventory',
        action: 'navigateToInventory'
      },
      {
        id: 'publish_shared',
        title: '发布共享件',
        description: '将库存航材发布为共享件',
        icon: Monitor,
        color: 'bg-blue-500',
        permission: 'publish_shared',
        action: 'openPublishSharedDialog'
      },
      {
        id: 'my_orders',
        title: '我的订单',
        description: '管理采购和销售订单',
        icon: Box,
        color: 'bg-purple-500',
        permission: 'manage_orders',
        action: 'navigateToOrders'
      },
      {
        id: 'aog_response',
        title: 'AOG紧急响应',
        description: '启动紧急航材需求流程',
        icon: Warning,
        color: 'bg-red-500',
        permission: 'publish_demand',
        action: 'openAOGDialog'
      }
    ],

    // 数据看板配置
    dashboard: {
      widgets: [
        {
          id: 'pending_demands',
          title: '待处理需求',
          type: 'stat',
          size: 'small',
          dataSource: 'demandStats'
        },
        {
          id: 'active_orders',
          title: '进行中订单',
          type: 'stat', 
          size: 'small',
          dataSource: 'orderStats'
        },
        {
          id: 'inventory_alerts',
          title: '库存预警',
          type: 'stat',
          size: 'small',
          dataSource: 'inventoryAlerts'
        },
        {
          id: 'shared_materials',
          title: '共享件收益',
          type: 'stat',
          size: 'small',
          dataSource: 'sharedStats'
        },
        {
          id: 'recent_activities',
          title: '最近活动',
          type: 'timeline',
          size: 'large',
          dataSource: 'recentActivities'
        },
        {
          id: 'demand_chart',
          title: '需求趋势',
          type: 'chart',
          size: 'medium',
          dataSource: 'demandTrends'
        }
      ]
    }
  },

  // 供应商 - 航材制造商，只销售不采购
  supplier: {
    name: '供应商',
    description: '航材制造商专属销售工作台',
    theme: {
      primaryColor: '#10b981',
      gradientFrom: '#10b981',
      gradientTo: '#059669'
    },
    
    // 主导航菜单
    navigation: [
      { 
        name: 'SupplierWorkspace', 
        label: '销售工作台', 
        path: '/app/workspace/supplier',
        icon: Grid,
        permission: 'view_own_data'
      },
      { 
        name: 'ProductManagement', 
        label: '产品管理', 
        path: '/app/products',
        icon: Box,
        permission: 'manage_inventory'
      },
      { 
        name: 'SalesOrders', 
        label: '销售订单', 
        path: '/app/orders?type=sales',
        icon: ShoppingCart,
        permission: 'manage_sales_orders'
      },
      { 
        name: 'Inventory', 
        label: '库存管理', 
        path: '/app/inventory',
        icon: Folder,
        permission: 'manage_inventory'
      },
      { 
        name: 'CustomerInquiries', 
        label: '客户询价', 
        path: '/app/inquiries',
        icon: Message,
        permission: 'respond_to_inquiry'
      },
      { 
        name: 'SharedMaterials', 
        label: '共享件发布', 
        path: '/app/shared-materials',
        icon: Share,
        permission: 'publish_shared'
      },
      { 
        name: 'SalesAnalytics', 
        label: '销售统计', 
        path: '/app/analytics?focus=sales',
        icon: DataAnalysis,
        permission: 'view_own_data'
      }
    ],

    // 工作台快速操作卡片
    quickActions: [
      {
        id: 'manage_products',
        title: '产品管理',
        description: '管理产品目录和库存信息',
        icon: Box,
        color: 'bg-green-500',
        permission: 'manage_inventory',
        action: 'navigateToProductManagement'
      },
      {
        id: 'sales_orders',
        title: '销售订单',
        description: '查看和处理销售订单',
        icon: ShoppingCart,
        color: 'bg-green-600',
        permission: 'manage_sales_orders',
        action: 'navigateToSalesOrders'
      },
      {
        id: 'inventory_management',
        title: '库存管理',
        description: '监控库存状态和预警',
        icon: Folder,
        color: 'bg-blue-500',
        permission: 'manage_inventory',
        action: 'navigateToInventory'
      },
      {
        id: 'customer_inquiries',
        title: '客户询价',
        description: '响应客户的产品询价',
        icon: Message,
        color: 'bg-yellow-500',
        permission: 'respond_to_inquiry',
        action: 'navigateToInquiries'
      },
      {
        id: 'publish_shared',
        title: '共享件发布',
        description: '将富余库存发布为共享件',
        icon: Share,
        color: 'bg-purple-500',
        permission: 'publish_shared',
        action: 'openPublishSharedDialog'
      },
      {
        id: 'sales_analytics',
        title: '销售统计',
        description: '查看销售业绩和趋势分析',
        icon: DataAnalysis,
        color: 'bg-orange-500',
        permission: 'view_own_data',
        action: 'navigateToSalesAnalytics'
      }
    ],

    // 数据看板配置
    dashboard: {
      widgets: [
        {
          id: 'online_products',
          title: '在线产品',
          type: 'stat',
          size: 'small',
          dataSource: 'productStats'
        },
        {
          id: 'active_orders',
          title: '活跃订单',
          type: 'stat', 
          size: 'small',
          dataSource: 'orderStats'
        },
        {
          id: 'pending_inquiries',
          title: '待回复询价',
          type: 'stat',
          size: 'small',
          dataSource: 'inquiryStats'
        },
        {
          id: 'monthly_revenue',
          title: '月度收益',
          type: 'stat',
          size: 'small',
          dataSource: 'revenueStats'
        },
        {
          id: 'recent_activities',
          title: '最近活动',
          type: 'timeline',
          size: 'large',
          dataSource: 'recentActivities'
        },
        {
          id: 'sales_chart',
          title: '销售趋势',
          type: 'chart',
          size: 'medium',
          dataSource: 'salesTrends'
        },
        {
          id: 'hot_products',
          title: '热门产品',
          type: 'ranking',
          size: 'medium',
          dataSource: 'hotProducts'
        }
      ]
    }
  },

  // 分销商 - 航材贸易商，既买也卖
  distributor: {
    name: '分销商',
    description: '航材贸易商双向交易工作台',
    theme: {
      primaryColor: '#8B5CF6',
      gradientFrom: '#8B5CF6',
      gradientTo: '#7C3AED'
    },
    
    // 主导航菜单
    navigation: [
      { 
        name: 'DistributorWorkspace', 
        label: '贸易工作台', 
        path: '/app/workspace/distributor',
        icon: Grid,
        permission: 'view_own_data'
      },
      { 
        name: 'DemandManagement', 
        label: '采购需求', 
        path: '/app/demands',
        icon: Document,
        permission: 'publish_demand'
      },
      { 
        name: 'ProductManagement', 
        label: '销售产品', 
        path: '/app/products',
        icon: Box,
        permission: 'publish_shared'
      },
      { 
        name: 'SupplierManagement', 
        label: '供应商管理', 
        path: '/app/suppliers',
        icon: User,
        permission: 'manage_orders'
      },
      { 
        name: 'CustomerManagement', 
        label: '客户管理', 
        path: '/app/customers',
        icon: User,
        permission: 'manage_sales_orders'
      },
      { 
        name: 'Orders', 
        label: '双向订单', 
        path: '/app/orders',
        icon: ShoppingCart,
        permission: 'manage_orders'
      },
      { 
        name: 'Inventory', 
        label: '库存周转', 
        path: '/app/inventory',
        icon: Folder,
        permission: 'manage_inventory'
      },
      { 
        name: 'InquiryManagement', 
        label: '询价管理', 
        path: '/app/inquiries',
        icon: Message,
        permission: 'respond_to_inquiry'
      },
      { 
        name: 'TradeAnalytics', 
        label: '贸易分析', 
        path: '/app/analytics?focus=trade',
        icon: DataAnalysis,
        permission: 'trade_analysis'
      }
    ],

    // 工作台快速操作卡片
    quickActions: [
      {
        id: 'publish_demand',
        title: '发布采购需求',
        description: '寻找优质航材供应商',
        icon: Plus,
        color: 'bg-purple-500',
        permission: 'publish_demand',
        action: 'openPublishDemandDialog'
      },
      {
        id: 'product_publish',
        title: '产品发布',
        description: '发布航材产品到市场',
        icon: Box,
        color: 'bg-green-500',
        permission: 'publish_shared',
        action: 'navigateToProductPublish'
      },
      {
        id: 'supplier_management',
        title: '供应商管理',
        description: '管理和评价供应商',
        icon: User,
        color: 'bg-blue-500',
        permission: 'manage_orders',
        action: 'navigateToSuppliers'
      },
      {
        id: 'customer_management',
        title: '客户管理',
        description: '维护客户关系和服务',
        icon: User,
        color: 'bg-purple-600',
        permission: 'manage_sales_orders',
        action: 'navigateToCustomers'
      },
      {
        id: 'inventory_turnover',
        title: '库存周转',
        description: '监控库存流转效率',
        icon: DataLine,
        color: 'bg-blue-600',
        permission: 'manage_inventory',
        action: 'navigateToInventoryTurnover'
      },
      {
        id: 'inquiry_management',
        title: '询价管理',
        description: '处理双向询价业务',
        icon: Message,
        color: 'bg-purple-700',
        permission: 'respond_to_inquiry',
        action: 'navigateToInquiries'
      },
      {
        id: 'trade_analysis',
        title: '贸易分析',
        description: '分析贸易数据和利润',
        icon: DataAnalysis,
        color: 'bg-orange-500',
        permission: 'trade_analysis',
        action: 'navigateToTradeAnalytics'
      }
    ],

    // 数据看板配置
    dashboard: {
      widgets: [
        {
          id: 'active_purchases',
          title: '进行采购',
          type: 'stat',
          size: 'small',
          dataSource: 'purchaseStats'
        },
        {
          id: 'active_sales',
          title: '进行销售',
          type: 'stat', 
          size: 'small',
          dataSource: 'salesStats'
        },
        {
          id: 'total_inquiries',
          title: '待处理询价',
          type: 'stat',
          size: 'small',
          dataSource: 'inquiryStats'
        },
        {
          id: 'profit_margin',
          title: '利润率',
          type: 'stat',
          size: 'small',
          dataSource: 'profitStats'
        },
        {
          id: 'recent_activities',
          title: '最近活动',
          type: 'timeline',
          size: 'large',
          dataSource: 'recentActivities'
        },
        {
          id: 'profit_chart',
          title: '利润趋势',
          type: 'chart',
          size: 'medium',
          dataSource: 'profitTrends'
        },
        {
          id: 'hot_categories',
          title: '热门品类',
          type: 'category',
          size: 'medium',
          dataSource: 'hotCategories'
        },
        {
          id: 'price_monitor',
          title: '价格监控',
          type: 'price',
          size: 'medium',
          dataSource: 'priceMonitor'
        }
      ]
    }
  },

  // 平台员工 - 内部员工，跨公司权限
  platform_staff: {
    name: '平台员工',
    description: '管理平台运营和客户服务',
    theme: {
      primaryColor: '#52c41a',
      gradientFrom: '#52c41a',
      gradientTo: '#1890ff'
    },

    navigation: [
      { 
        name: 'PlatformStaffWorkspace', 
        label: '运营工作台', 
        path: '/app/workspace/platform-staff',
        icon: Grid,
        permission: 'view_all_data'
      },
      { 
        name: 'DemandManagement', 
        label: '需求管理', 
        path: '/app/demands',
        icon: Document,
        permission: 'view_demands'
      },
      { 
        name: 'Orders', 
        label: '订单管理', 
        path: '/app/orders',
        icon: Box,
        permission: 'process_all_orders'
      },
      { 
        name: 'Inventory', 
        label: '库存管理', 
        path: '/app/inventory',
        icon: Folder,
        permission: 'manage_inventory'
      },
      { 
        name: 'SharedMaterialReview', 
        label: '共享件审核', 
        path: '/app/shared-materials/review',
        icon: Monitor,
        permission: 'audit_shared_materials'
      },
      { 
        name: 'Analytics', 
        label: '数据分析', 
        path: '/app/analytics',
        icon: DataAnalysis,
        permission: 'data_analysis'
      },
      { 
        name: 'UserManagement', 
        label: '用户管理', 
        path: '/app/users',
        icon: User,
        permission: 'user_management'
      }
    ],

    quickActions: [
      {
        id: 'process_orders',
        title: '处理订单',
        description: '快速处理待审核订单',
        icon: Box,
        color: 'bg-primary-500',
        permission: 'process_all_orders',
        action: 'navigateToOrderReview'
      },
      {
        id: 'audit_shared',
        title: '审核共享件',
        description: '审核用户发布的共享件',
        icon: Monitor,
        color: 'bg-success-500',
        permission: 'audit_shared_materials',
        action: 'navigateToSharedReview'
      },
      {
        id: 'manage_inventory',
        title: '库存统筹',
        description: '跨公司库存统筹管理',
        icon: Folder,
        color: 'bg-warning-500',
        permission: 'manage_inventory',
        action: 'navigateToInventoryManagement'
      },
      {
        id: 'customer_service',
        title: '客户服务',
        description: '处理客户问题和投诉',
        icon: Phone,
        color: 'bg-blue-500',
        permission: 'view_all_data',
        action: 'openCustomerServicePanel'
      },
      {
        id: 'data_analysis',
        title: '数据分析',
        description: '查看平台运营数据',
        icon: DataAnalysis,
        color: 'bg-purple-500',
        permission: 'data_analysis',
        action: 'navigateToAnalytics'
      },
      {
        id: 'system_config',
        title: '系统配置',
        description: '管理平台参数配置',
        icon: Setting,
        color: 'bg-gray-500',
        permission: 'system_config',
        action: 'navigateToSystemConfig'
      }
    ],

    dashboard: {
      widgets: [
        {
          id: 'platform_orders',
          title: '平台订单',
          type: 'stat',
          size: 'small',
          dataSource: 'platformOrderStats'
        },
        {
          id: 'pending_reviews',
          title: '待审核项',
          type: 'stat',
          size: 'small',
          dataSource: 'pendingReviews'
        },
        {
          id: 'active_users',
          title: '活跃用户',
          type: 'stat',
          size: 'small',
          dataSource: 'activeUsers'
        },
        {
          id: 'revenue_today',
          title: '今日收益',
          type: 'stat',
          size: 'small',
          dataSource: 'revenueStats'
        },
        {
          id: 'platform_activities',
          title: '平台动态',
          type: 'timeline',
          size: 'large',
          dataSource: 'platformActivities'
        },
        {
          id: 'business_chart',
          title: '业务概览',
          type: 'chart',
          size: 'medium',
          dataSource: 'businessOverview'
        },
        {
          id: 'customer_feedback',
          title: '客户反馈',
          type: 'feedback',
          size: 'medium',
          dataSource: 'customerFeedback'
        }
      ]
    }
  },

  // 维修工程师
  maintenance_engineer: {
    name: '维修工程师',
    description: '管理维修任务和技术支持',
    theme: {
      primaryColor: '#722ed1',
      gradientFrom: '#722ed1',
      gradientTo: '#eb2f96'
    },

    navigation: [
      { 
        name: 'MaintenanceWorkspace', 
        label: '维修工作台', 
        path: '/app/workspace/maintenance',
        icon: Grid,
        permission: 'view_own_data'
      },
      { 
        name: 'MaintenanceManagement', 
        label: '维修管理', 
        path: '/app/maintenance',
        icon: Tools,
        permission: 'update_maintenance_status'
      },
      { 
        name: 'WorkOrders', 
        label: '工单管理', 
        path: '/app/work-orders',
        icon: Document,
        permission: 'manage_work_orders'
      },
      { 
        name: 'Inventory', 
        label: '备件管理', 
        path: '/app/inventory',
        icon: Folder,
        permission: 'view_inventory'
      },
      { 
        name: 'TechnicalDocs', 
        label: '技术文档', 
        path: '/app/technical-docs',
        icon: Document,
        permission: 'technical_support'
      }
    ],

    quickActions: [
      {
        id: 'create_work_order',
        title: '创建工单',
        description: '创建新的维修工单',
        icon: Plus,
        color: 'bg-primary-500',
        permission: 'manage_work_orders',
        action: 'openCreateWorkOrderDialog'
      },
      {
        id: 'update_progress',
        title: '更新进度',
        description: '更新维修工作进度',
        icon: Tools,
        color: 'bg-success-500',
        permission: 'update_maintenance_status',
        action: 'openProgressUpdateDialog'
      },
      {
        id: 'technical_support',
        title: '技术支持',
        description: '提供技术咨询服务',
        icon: Phone,
        color: 'bg-warning-500',
        permission: 'technical_support',
        action: 'openTechnicalSupportPanel'
      },
      {
        id: 'spare_parts',
        title: '申请备件',
        description: '申请维修所需备件',
        icon: Folder,
        color: 'bg-blue-500',
        permission: 'view_inventory',
        action: 'openSparePartsRequest'
      }
    ],

    dashboard: {
      widgets: [
        {
          id: 'active_work_orders',
          title: '进行中工单',
          type: 'stat',
          size: 'small',
          dataSource: 'activeWorkOrders'
        },
        {
          id: 'completed_today',
          title: '今日完成',
          type: 'stat',
          size: 'small',
          dataSource: 'completedToday'
        },
        {
          id: 'urgent_tasks',
          title: '紧急任务',
          type: 'stat',
          size: 'small',
          dataSource: 'urgentTasks'
        },
        {
          id: 'efficiency_rate',
          title: '完成效率',
          type: 'stat',
          size: 'small',
          dataSource: 'efficiencyRate'
        },
        {
          id: 'work_schedule',
          title: '工作计划',
          type: 'schedule',
          size: 'large',
          dataSource: 'workSchedule'
        },
        {
          id: 'maintenance_chart',
          title: '维修统计',
          type: 'chart',
          size: 'medium',
          dataSource: 'maintenanceStats'
        }
      ]
    }
  },

  // 物流专员
  logistics_specialist: {
    name: '物流专员',
    description: '管理物流配送和运输跟踪',
    theme: {
      primaryColor: '#fa8c16',
      gradientFrom: '#fa8c16',
      gradientTo: '#faad14'
    },

    navigation: [
      { 
        name: 'LogisticsWorkspace', 
        label: '物流工作台', 
        path: '/app/workspace/logistics',
        icon: Grid,
        permission: 'view_own_data'
      },
      { 
        name: 'LogisticsManagement', 
        label: '物流管理', 
        path: '/app/logistics',
        icon: Van,
        permission: 'track_shipment'
      },
      { 
        name: 'DeliveryTracking', 
        label: '配送跟踪', 
        path: '/app/delivery-tracking',
        icon: Monitor,
        permission: 'track_shipment'
      },
      { 
        name: 'Orders', 
        label: '配送订单', 
        path: '/app/orders',
        icon: Box,
        permission: 'manage_delivery'
      }
    ],

    quickActions: [
      {
        id: 'track_shipment',
        title: '跟踪货运',
        description: '实时跟踪货物运输状态',
        icon: Van,
        color: 'bg-primary-500',
        permission: 'track_shipment',
        action: 'openShipmentTracking'
      },
      {
        id: 'update_delivery',
        title: '更新配送',
        description: '更新配送状态信息',
        icon: Monitor,
        color: 'bg-success-500',
        permission: 'update_logistics',
        action: 'openDeliveryUpdate'
      },
      {
        id: 'schedule_pickup',
        title: '安排取件',
        description: '安排货物取件服务',
        icon: Plus,
        color: 'bg-warning-500',
        permission: 'manage_delivery',
        action: 'openPickupSchedule'
      },
      {
        id: 'delivery_report',
        title: '配送报告',
        description: '生成配送统计报告',
        icon: Document,
        color: 'bg-blue-500',
        permission: 'track_shipment',
        action: 'generateDeliveryReport'
      }
    ],

    dashboard: {
      widgets: [
        {
          id: 'active_shipments',
          title: '运输中',
          type: 'stat',
          size: 'small',
          dataSource: 'activeShipments'
        },
        {
          id: 'delivered_today',
          title: '今日送达',
          type: 'stat',
          size: 'small',
          dataSource: 'deliveredToday'
        },
        {
          id: 'delayed_shipments',
          title: '延误货物',
          type: 'stat',
          size: 'small',
          dataSource: 'delayedShipments'
        },
        {
          id: 'delivery_rate',
          title: '及时率',
          type: 'stat',
          size: 'small',
          dataSource: 'deliveryRate'
        },
        {
          id: 'shipment_map',
          title: '配送地图',
          type: 'map',
          size: 'large',
          dataSource: 'shipmentLocations'
        },
        {
          id: 'logistics_chart',
          title: '物流统计',
          type: 'chart',
          size: 'medium',
          dataSource: 'logisticsStats'
        }
      ]
    }
  },

  // 系统管理员
  admin: {
    name: '系统管理员',
    description: '管理系统配置和用户权限',
    theme: {
      primaryColor: '#13c2c2',
      gradientFrom: '#13c2c2',
      gradientTo: '#1890ff'
    },

    navigation: [
      { 
        name: 'AdminWorkspace', 
        label: '管理工作台', 
        path: '/app/workspace/admin',
        icon: Grid,
        permission: 'all_permissions'
      },
      { 
        name: 'UserManagement', 
        label: '用户管理', 
        path: '/app/admin/users',
        icon: User,
        permission: 'user_management'
      },
      { 
        name: 'PermissionManagement', 
        label: '权限管理', 
        path: '/app/admin/permissions',
        icon: Setting,
        permission: 'user_management'
      },
      { 
        name: 'SystemConfig', 
        label: '系统配置', 
        path: '/app/admin/config',
        icon: Setting,
        permission: 'system_config'
      },
      { 
        name: 'Analytics', 
        label: '数据分析', 
        path: '/app/analytics',
        icon: DataAnalysis,
        permission: 'data_analysis'
      },
      { 
        name: 'AuditLogs', 
        label: '审计日志', 
        path: '/app/admin/audit-logs',
        icon: Document,
        permission: 'all_permissions'
      }
    ],

    quickActions: [
      {
        id: 'user_management',
        title: '用户管理',
        description: '管理平台用户账户',
        icon: User,
        color: 'bg-primary-500',
        permission: 'user_management',
        action: 'navigateToUserManagement'
      },
      {
        id: 'permission_config',
        title: '权限配置',
        description: '配置用户权限设置',
        icon: Setting,
        color: 'bg-success-500',
        permission: 'user_management',
        action: 'navigateToPermissionConfig'
      },
      {
        id: 'system_monitor',
        title: '系统监控',
        description: '监控系统运行状态',
        icon: Monitor,
        color: 'bg-warning-500',
        permission: 'system_config',
        action: 'openSystemMonitor'
      },
      {
        id: 'data_backup',
        title: '数据备份',
        description: '执行系统数据备份',
        icon: Folder,
        color: 'bg-blue-500',
        permission: 'system_config',
        action: 'startDataBackup'
      },
      {
        id: 'platform_analytics',
        title: '平台分析',
        description: '查看平台运营数据',
        icon: DataAnalysis,
        color: 'bg-purple-500',
        permission: 'data_analysis',
        action: 'navigateToPlatformAnalytics'
      },
      {
        id: 'audit_review',
        title: '审计审查',
        description: '查看系统审计日志',
        icon: Document,
        color: 'bg-gray-500',
        permission: 'all_permissions',
        action: 'navigateToAuditLogs'
      }
    ],

    dashboard: {
      widgets: [
        {
          id: 'total_users',
          title: '总用户数',
          type: 'stat',
          size: 'small',
          dataSource: 'totalUsers'
        },
        {
          id: 'system_health',
          title: '系统健康度',
          type: 'stat',
          size: 'small',
          dataSource: 'systemHealth'
        },
        {
          id: 'daily_transactions',
          title: '日交易量',
          type: 'stat',
          size: 'small',
          dataSource: 'dailyTransactions'
        },
        {
          id: 'error_rate',
          title: '错误率',
          type: 'stat',
          size: 'small',
          dataSource: 'errorRate'
        },
        {
          id: 'system_activities',
          title: '系统活动',
          type: 'timeline',
          size: 'large',
          dataSource: 'systemActivities'
        },
        {
          id: 'performance_chart',
          title: '性能监控',
          type: 'chart',
          size: 'medium',
          dataSource: 'performanceStats'
        },
        {
          id: 'user_behavior',
          title: '用户行为',
          type: 'chart',
          size: 'medium',
          dataSource: 'userBehaviorStats'
        }
      ]
    }
  }
}

// 根据用户角色获取工作台配置
export function getRoleWorkspaceConfig(userType) {
  console.log('🎯 getRoleWorkspaceConfig - 请求的用户类型:', userType)
  console.log('🎯 可用的配置键:', Object.keys(ROLE_WORKSPACE_CONFIG))
  
  const config = ROLE_WORKSPACE_CONFIG[userType]
  if (config) {
    console.log('✅ 找到配置:', config.name)
    return config
  } else {
    console.log('⚠️ 未找到配置，使用默认 airline_buyer')
    return ROLE_WORKSPACE_CONFIG.airline_buyer
  }
}

// 根据用户权限过滤导航菜单
export function filterNavigationByPermissions(navigation, userPermissions) {
  return navigation.filter(nav => {
    if (!nav.permission) return true
    return userPermissions.includes(nav.permission) || userPermissions.includes('all_permissions')
  })
}

// 根据用户权限过滤快速操作
export function filterQuickActionsByPermissions(quickActions, userPermissions) {
  return quickActions.filter(action => {
    if (!action.permission) return true
    return userPermissions.includes(action.permission) || userPermissions.includes('all_permissions')
  })
}

// 获取角色主题配置
export function getRoleTheme(userType) {
  const config = getRoleWorkspaceConfig(userType)
  return config.theme
}

// 工作台布局模式配置
export const WORKSPACE_LAYOUTS = {
  // 双栏布局 - 适合航空公司采购员
  'two-column': {
    mainContent: 'lg:col-span-8',
    sidebar: 'lg:col-span-4'
  },
  
  // 三栏布局 - 适合平台员工
  'three-column': {
    sidebar: 'lg:col-span-2',
    mainContent: 'lg:col-span-7', 
    rightPanel: 'lg:col-span-3'
  },
  
  // 专注布局 - 适合维修工程师
  'focused': {
    mainContent: 'lg:col-span-10 lg:col-start-2'
  },
  
  // 全宽布局 - 适合管理员
  'full-width': {
    mainContent: 'lg:col-span-12'
  }
}

export default ROLE_WORKSPACE_CONFIG