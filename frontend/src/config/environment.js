/**
 * 环境配置管理器
 * 统一管理不同环境下的配置和Mock数据控制策略
 * 
 * 功能：
 * - 环境检测和配置获取
 * - Mock数据智能启用控制
 * - API基础URL管理
 * - 构建时优化配置
 */

// 环境检测
export const environment = {
  isDevelopment: import.meta.env.MODE === 'development',
  isStaging: import.meta.env.MODE === 'staging', 
  isProduction: import.meta.env.MODE === 'production',
  mode: import.meta.env.MODE,
  
  // Mock相关配置
  mockEnabled: import.meta.env.VITE_MOCK_ENABLED === 'true',
  mockFullData: import.meta.env.VITE_MOCK_FULL_DATA === 'true',
  mockApiDelay: import.meta.env.VITE_MOCK_API_DELAY === 'true',
  mockErrorSimulation: import.meta.env.VITE_MOCK_ERROR_SIMULATION === 'true',
  
  // API配置
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:5001',
  
  /**
   * 获取Mock配置
   * 根据当前环境返回相应的Mock配置策略
   */
  getMockConfig() {
    return {
      // 基础控制
      enabled: this.isDevelopment || this.mockEnabled,
      
      // 数据类型控制
      fullData: this.isDevelopment && this.mockFullData,
      fallbackOnly: !this.isDevelopment && this.mockEnabled,
      
      // 行为控制  
      apiDelay: this.isDevelopment && this.mockApiDelay,
      errorSimulation: this.isDevelopment && this.mockErrorSimulation,
      
      // 性能优化
      lazyLoad: this.isProduction, // 生产环境懒加载Mock代码
      treeShaking: this.isProduction, // 生产环境移除未使用代码
    }
  },
  
  /**
   * 获取API配置
   */
  getApiConfig() {
    return {
      baseURL: this.apiBaseUrl,
      timeout: this.isDevelopment ? 10000 : 5000,
      retries: this.isProduction ? 3 : 1,
      retryDelay: 1000,
      
      // 错误处理策略
      enableFallback: this.getMockConfig().enabled,
      strictMode: this.isProduction,
    }
  },
  
  /**
   * 日志配置
   */
  getLogConfig() {
    return {
      level: this.isDevelopment ? 'debug' : this.isProduction ? 'error' : 'info',
      mockOperations: this.isDevelopment,
      apiCalls: this.isDevelopment,
      performance: !this.isProduction,
    }
  },
  
  /**
   * 检查是否应该加载Mock管理器
   */
  shouldLoadMockManager() {
    const config = this.getMockConfig()
    return config.enabled || config.fallbackOnly
  }
}

/**
 * Mock配置预设
 * 为不同场景提供预定义的配置组合
 */
export const mockConfigPresets = {
  // 完整开发模式
  development: {
    enabled: true,
    fullData: true,
    fallbackOnly: false,
    apiDelay: true,
    errorSimulation: true,
  },
  
  // 演示模式  
  demo: {
    enabled: true,
    fullData: false,
    fallbackOnly: true,
    apiDelay: false,
    errorSimulation: false,
  },
  
  // 生产模式（禁用所有Mock）
  production: {
    enabled: false,
    fullData: false, 
    fallbackOnly: false,
    apiDelay: false,
    errorSimulation: false,
  },
  
  // 测试模式
  testing: {
    enabled: true,
    fullData: true,
    fallbackOnly: false,
    apiDelay: false,
    errorSimulation: true,
  }
}

/**
 * 根据环境获取预设配置
 */
export function getPresetConfig(preset = environment.mode) {
  return mockConfigPresets[preset] || mockConfigPresets.development
}

/**
 * 动态Mock管理器
 * 提供运行时的Mock控制能力
 */
export class MockController {
  constructor() {
    this.config = environment.getMockConfig()
    this.isInitialized = false
    this.mockManager = null
  }
  
  /**
   * 初始化Mock管理器
   * 使用动态导入避免在生产环境加载Mock代码
   */
  async initialize() {
    if (this.isInitialized || !environment.shouldLoadMockManager()) {
      return
    }
    
    try {
      // 动态导入Mock管理器（仅在需要时加载）
      if (__DEV__ || __MOCK_ENABLED__) {
        const { MockManager } = await import('@/api/mockManager.js')
        this.mockManager = new MockManager()
        this.isInitialized = true
        
        if (environment.getLogConfig().mockOperations) {
          console.log('[MockController] Mock管理器已初始化', this.config)
        }
      }
    } catch (error) {
      console.warn('[MockController] Mock管理器初始化失败:', error)
    }
  }
  
  /**
   * 获取Mock管理器实例
   */
  async getMockManager() {
    await this.initialize()
    return this.mockManager
  }
  
  /**
   * 检查Mock是否可用
   */
  isAvailable() {
    return this.isInitialized && this.mockManager
  }
  
  /**
   * 更新Mock配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    if (this.mockManager) {
      this.mockManager.updateConfig(this.config)
    }
  }
}

// 全局Mock控制器实例
export const mockController = new MockController()

// 开发模式下自动初始化
if (environment.isDevelopment) {
  mockController.initialize().catch(console.warn)
}

export default environment