/**
 * 角色工作台组合式函数
 * 版本: 2.0
 * 创建时间: 2025-07-19
 * 
 * 提供基于用户角色的工作台配置和权限控制功能
 */

import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { 
  getRoleWorkspaceConfig, 
  filterNavigationByPermissions,
  filterQuickActionsByPermissions,
  getRoleTheme
} from '@/config/roleWorkspaceConfig'

/**
 * 角色工作台Hook
 * @returns {Object} 工作台相关的响应式数据和方法
 */
export function useRoleWorkspace() {
  const authStore = useAuthStore()
  
  // 响应式数据
  const workspaceConfig = ref({})
  const navigation = ref([])
  const quickActions = ref([])
  const dashboardWidgets = ref([])
  const theme = ref({})
  const isLoading = ref(true)
  const error = ref(null)

  // 计算属性
  const currentUserRole = computed(() => {
    return authStore.user?.user_type || 'airline_buyer'
  })

  const userPermissions = computed(() => {
    // 暂时返回基于用户角色的基础权限
    // 在实际应用中，应该从用户数据或单独的权限API获取
    return authStore.user?.permissions || []
  })

  const roleConfig = computed(() => {
    return getRoleWorkspaceConfig(currentUserRole.value)
  })

  const roleName = computed(() => {
    return roleConfig.value.name || '用户'
  })

  const roleDescription = computed(() => {
    return roleConfig.value.description || ''
  })

  /**
   * 初始化工作台配置
   */
  const initializeWorkspace = async () => {
    try {
      isLoading.value = true
      error.value = null

      // 等待用户数据加载
      if (!authStore.user) {
        // 给一个短暂的延迟，等待用户数据从 localStorage 加载
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 获取角色配置
      workspaceConfig.value = roleConfig.value

      // 根据权限过滤导航菜单
      navigation.value = filterNavigationByPermissions(
        roleConfig.value.navigation || [],
        userPermissions.value
      )

      // 根据权限过滤快速操作
      quickActions.value = filterQuickActionsByPermissions(
        roleConfig.value.quickActions || [],
        userPermissions.value
      )

      // 设置看板组件
      dashboardWidgets.value = roleConfig.value.dashboard?.widgets || []

      // 设置主题
      theme.value = getRoleTheme(currentUserRole.value)

      // 工作台配置初始化成功

    } catch (err) {
      error.value = '工作台配置加载失败'
      console.error('工作台配置初始化失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 检查用户是否有指定权限
   * @param {string} permission 权限代码
   * @returns {boolean} 是否有权限
   */
  const hasPermission = (permission) => {
    // 如果没有指定权限要求，默认允许
    if (!permission) return true
    
    // 如果没有权限数据，暂时默认允许（开发阶段）
    if (!userPermissions.value || userPermissions.value.length === 0) {
      return true
    }
    
    // 检查是否有具体权限或全部权限
    return userPermissions.value.includes(permission) || 
           userPermissions.value.includes('all_permissions')
  }

  /**
   * 执行快速操作
   * @param {string} actionId 操作ID
   */
  const executeQuickAction = (actionId) => {
    const action = quickActions.value.find(a => a.id === actionId)
    if (!action) {
      console.warn('未找到快速操作:', actionId)
      return
    }

    // 检查权限
    if (!hasPermission(action.permission)) {
      console.warn('没有权限执行操作:', actionId)
      return
    }

    // 触发操作事件
    console.log('执行快速操作:', actionId, action.action)
    
    // 这里可以触发自定义事件或调用回调函数
    // 具体实现在各个工作台组件中处理
    return {
      actionId,
      actionType: action.action,
      actionData: action
    }
  }

  /**
   * 获取工作台统计数据
   * @param {string} dataSource 数据源标识
   * @returns {Promise} 统计数据
   */
  const getWorkspaceStats = async (dataSource) => {
    try {
      // 这里应该调用相应的API获取数据
      // 暂时返回模拟数据
      return await fetchWorkspaceData(dataSource, currentUserRole.value)
    } catch (err) {
      console.error('获取工作台数据失败:', dataSource, err)
      return null
    }
  }

  /**
   * 更新工作台配置
   * @param {Object} newConfig 新配置
   */
  const updateWorkspaceConfig = (newConfig) => {
    workspaceConfig.value = { ...workspaceConfig.value, ...newConfig }
  }

  /**
   * 重新加载工作台
   */
  const reloadWorkspace = () => {
    initializeWorkspace()
  }

  // 生命周期
  onMounted(() => {
    initializeWorkspace()
  })

  return {
    // 响应式数据
    workspaceConfig,
    navigation,
    quickActions,
    dashboardWidgets,
    theme,
    isLoading,
    error,
    
    // 计算属性
    currentUserRole,
    userPermissions,
    roleConfig,
    roleName,
    roleDescription,
    
    // 方法
    initializeWorkspace,
    hasPermission,
    executeQuickAction,
    getWorkspaceStats,
    updateWorkspaceConfig,
    reloadWorkspace
  }
}

/**
 * 获取工作台数据的模拟函数
 * 实际应用中应该调用真实的API
 */
async function fetchWorkspaceData(dataSource, userRole) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 根据角色和数据源返回不同的模拟数据
  const mockData = {
    // 航空公司采购员数据
    airline_buyer: {
      demandStats: { pending: 5, active: 12, completed: 45 },
      orderStats: { processing: 8, shipped: 15, delivered: 32 },
      inventoryAlerts: { lowStock: 3, outOfStock: 1, critical: 2 },
      sharedStats: { published: 12, sold: 8, revenue: 45600 },
      recentActivities: [
        { id: 1, type: 'demand', title: '发布新需求', description: '发动机燃油喷嘴需求', time: '2小时前' },
        { id: 2, type: 'order', title: '订单更新', description: '轮胎订单已发货', time: '4小时前' },
        { id: 3, type: 'shared', title: '共享件售出', description: '航电设备已售出', time: '1天前' }
      ]
    },
    
    // 平台员工数据
    platform_staff: {
      platformOrderStats: { total: 156, pending: 23, completed: 98 },
      pendingReviews: { shared: 8, users: 5, complaints: 3 },
      activeUsers: { online: 45, today: 123, thisWeek: 567 },
      revenueStats: { today: 125600, thisMonth: 2456000, growth: 15.6 }
    },
    
    // 维修工程师数据
    maintenance_engineer: {
      activeWorkOrders: { total: 18, urgent: 4, normal: 14 },
      completedToday: { count: 6, efficiency: 95.2 },
      urgentTasks: { aog: 2, critical: 3, normal: 13 },
      efficiencyRate: { today: 95.2, thisWeek: 92.8, thisMonth: 94.1 }
    },
    
    // 物流专员数据
    logistics_specialist: {
      activeShipments: { total: 34, domestic: 28, international: 6 },
      deliveredToday: { count: 12, onTime: 11, delayed: 1 },
      delayedShipments: { total: 3, weather: 1, traffic: 2 },
      deliveryRate: { today: 91.7, thisWeek: 94.2, thisMonth: 92.5 }
    },
    
    // 管理员数据  
    admin: {
      totalUsers: { count: 1247, active: 856, inactive: 391 },
      systemHealth: { cpu: 45.2, memory: 62.1, disk: 78.5, status: 'healthy' },
      dailyTransactions: { count: 2456, value: 5620000, growth: 12.3 },
      errorRate: { rate: 0.02, errors: 12, total: 60000 }
    }
  }
  
  return mockData[userRole]?.[dataSource] || {}
}

export default useRoleWorkspace