<template>
  <el-dialog
    v-model="dialogVisible"
    title="工时记录管理"
    width="900px"
    :before-close="handleClose"
    class="labor-time-dialog"
  >
    <div class="labor-time-container">
      <!-- 工单信息 -->
      <div class="work-order-info">
        <h3>工单信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">工单号：</span>
            <span class="value">{{ workOrder?.work_order_number }}</span>
          </div>
          <div class="info-item">
            <span class="label">飞机号：</span>
            <span class="value">{{ workOrder?.aircraft_tail }}</span>
          </div>
          <div class="info-item">
            <span class="label">分配技师：</span>
            <span class="value">{{ workOrder?.assigned_technician?.name || '未分配' }}</span>
          </div>
        </div>
      </div>

      <!-- 工时统计 -->
      <div class="labor-stats">
        <h3>工时统计</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalHours }}h</div>
              <div class="stat-label">总工时</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon estimated">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ workOrder?.estimated_hours || 0 }}h</div>
              <div class="stat-label">预计工时</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon efficiency">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ efficiencyRate }}%</div>
              <div class="stat-label">效率</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增工时记录 -->
      <div class="add-labor-section">
        <h3>
          新增工时记录
          <el-button type="primary" size="small" @click="showAddForm = !showAddForm">
            <el-icon><Plus /></el-icon>
            {{ showAddForm ? '取消' : '添加记录' }}
          </el-button>
        </h3>
        
        <div v-if="showAddForm" class="add-form">
          <el-form
            ref="addFormRef"
            :model="addForm"
            :rules="addRules"
            label-width="80px"
            class="labor-form"
          >
            <div class="form-row">
              <el-form-item label="开始时间" prop="start_time" class="form-item">
                <el-date-picker
                  v-model="addForm.start_time"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              
              <el-form-item label="结束时间" prop="end_time" class="form-item">
                <el-date-picker
                  v-model="addForm.end_time"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </div>

            <el-form-item label="工作描述" prop="work_description">
              <el-input
                v-model="addForm.work_description"
                type="textarea"
                :rows="3"
                placeholder="请详细描述工作内容"
              />
            </el-form-item>

            <div class="form-actions">
              <el-button @click="resetAddForm">重置</el-button>
              <el-button type="primary" @click="submitLaborRecord" :loading="submitting">
                保存记录
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 工时记录列表 -->
      <div class="labor-records">
        <h3>工时记录</h3>
        <div class="records-table">
          <el-table :data="laborRecords" stripe>
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="technician" label="技师" width="100" />
            <el-table-column prop="start_time" label="开始时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="end_time" label="结束时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.end_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="hours" label="工时" width="80">
              <template #default="scope">
                <el-tag type="success">{{ scope.row.hours }}h</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="工作内容" min-width="200" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  @click="editLaborRecord(scope.row)"
                  size="small"
                >
                  编辑
                </el-button>
                <el-button
                  link
                  type="danger"
                  @click="deleteLaborRecord(scope.row)"
                  size="small"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 工时分析图表 -->
      <div class="labor-analysis">
        <h3>工时分析</h3>
        <div class="analysis-content">
          <div class="chart-container">
            <div class="chart-placeholder">
              📊 工时分布图表
            </div>
          </div>
          <div class="analysis-summary">
            <div class="summary-item">
              <span class="label">平均每日工时：</span>
              <span class="value">{{ avgDailyHours }}h</span>
            </div>
            <div class="summary-item">
              <span class="label">最高单日工时：</span>
              <span class="value">{{ maxDailyHours }}h</span>
            </div>
            <div class="summary-item">
              <span class="label">工作天数：</span>
              <span class="value">{{ workingDays }}天</span>
            </div>
            <div class="summary-item">
              <span class="label">预计完成时间：</span>
              <span class="value">{{ estimatedCompletion }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportLaborReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Clock, Timer, DataLine, Plus, Download 
} from '@element-plus/icons-vue'
import { recordLaborTime } from '@/api/maintenance'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workOrder: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const addFormRef = ref(null)
const submitting = ref(false)
const showAddForm = ref(false)
const laborRecords = ref([])

// 添加工时表单
const addForm = reactive({
  start_time: '',
  end_time: '',
  work_description: ''
})

// 表单验证规则
const addRules = {
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  work_description: [
    { required: true, message: '请输入工作描述', trigger: 'blur' },
    { min: 5, max: 200, message: '工作描述长度在5到200个字符', trigger: 'blur' }
  ]
}

// 监听工单变化
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder && props.modelValue) {
    loadLaborRecords(newWorkOrder.id)
  }
}, { immediate: true })

// 计算属性
const totalHours = computed(() => {
  return laborRecords.value.reduce((total, record) => total + record.hours, 0)
})

const efficiencyRate = computed(() => {
  if (!props.workOrder?.estimated_hours) return 0
  const efficiency = (totalHours.value / props.workOrder.estimated_hours) * 100
  return Math.round(efficiency)
})

const avgDailyHours = computed(() => {
  if (workingDays.value === 0) return 0
  return Math.round((totalHours.value / workingDays.value) * 10) / 10
})

const maxDailyHours = computed(() => {
  // 模拟计算最高单日工时
  return Math.max(...laborRecords.value.map(r => r.hours), 0)
})

const workingDays = computed(() => {
  // 模拟计算工作天数
  const dates = new Set()
  laborRecords.value.forEach(record => {
    const date = new Date(record.start_time).toDateString()
    dates.add(date)
  })
  return dates.size
})

const estimatedCompletion = computed(() => {
  if (!props.workOrder?.estimated_hours || totalHours.value === 0) return '-'
  
  const remainingHours = Math.max(0, props.workOrder.estimated_hours - totalHours.value)
  if (remainingHours === 0) return '已完成'
  
  const avgHoursPerDay = avgDailyHours.value || 8
  const remainingDays = Math.ceil(remainingHours / avgHoursPerDay)
  
  const completionDate = new Date()
  completionDate.setDate(completionDate.getDate() + remainingDays)
  
  return completionDate.toLocaleDateString('zh-CN')
})

// 加载工时记录
const loadLaborRecords = async (workOrderId) => {
  // 模拟工时记录数据
  laborRecords.value = [
    {
      id: 1,
      technician: '张师傅',
      start_time: '2024-07-17T09:00:00Z',
      end_time: '2024-07-17T12:00:00Z',
      hours: 3,
      description: '拆解检查液压系统'
    },
    {
      id: 2,
      technician: '张师傅',
      start_time: '2024-07-17T14:00:00Z',
      end_time: '2024-07-17T17:30:00Z',
      hours: 3.5,
      description: '更换液压泵，系统测试'
    }
  ]
}

// 提交工时记录
const submitLaborRecord = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()
    
    // 验证时间逻辑
    if (new Date(addForm.start_time) >= new Date(addForm.end_time)) {
      ElMessage.error('结束时间必须晚于开始时间')
      return
    }

    submitting.value = true

    const laborData = {
      technician_id: props.workOrder.assigned_technician_id,
      start_time: addForm.start_time,
      end_time: addForm.end_time,
      work_description: addForm.work_description
    }

    const response = await recordLaborTime(props.workOrder.id, laborData)
    
    if (response.success) {
      ElMessage.success('工时记录添加成功')
      resetAddForm()
      showAddForm.value = false
      loadLaborRecords(props.workOrder.id)
      emit('refresh')
    } else {
      ElMessage.error(response.message || '添加失败')
    }
  } catch (error) {
    console.error('添加工时记录失败:', error)
    ElMessage.error('添加失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 重置添加表单
const resetAddForm = () => {
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
  Object.assign(addForm, {
    start_time: '',
    end_time: '',
    work_description: ''
  })
}

// 编辑工时记录
const editLaborRecord = (record) => {
  ElMessage.info('编辑功能开发中...')
}

// 删除工时记录
const deleteLaborRecord = async (record) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条工时记录吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    loadLaborRecords(props.workOrder.id)
    emit('refresh')
  } catch {
    // 用户取消删除
  }
}

// 导出工时报告
const exportLaborReport = () => {
  ElMessage.info('导出功能开发中...')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  showAddForm.value = false
  resetAddForm()
}

// 工具函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.labor-time-dialog {
  --el-dialog-content-font-size: 14px;
}

.labor-time-container {
  max-height: 80vh;
  overflow-y: auto;
}

.work-order-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.work-order-info h3 {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 70px;
}

.value {
  color: #333;
  font-weight: 400;
}

.labor-stats {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.labor-stats h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.stat-icon.estimated { background: #f59e0b; }
.stat-icon.efficiency { background: #10b981; }

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.add-labor-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.add-labor-section h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.add-form {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.labor-records {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.labor-records h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.records-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.labor-analysis {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.labor-analysis h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.analysis-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.chart-placeholder {
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  color: #9ca3af;
}

.analysis-summary {
  background: white;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  color: #666;
  font-size: 14px;
}

.summary-item .value {
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.labor-time-container::-webkit-scrollbar {
  width: 6px;
}

.labor-time-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.labor-time-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.labor-time-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .analysis-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>