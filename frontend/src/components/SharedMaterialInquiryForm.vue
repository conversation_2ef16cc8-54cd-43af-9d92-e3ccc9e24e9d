<template>
  <div class="shared-material-inquiry-form">
    <!-- 航材信息展示 -->
    <div v-if="material" class="material-summary">
      <div class="material-image">
        <img :src="material.material.image_url" :alt="material.material.name" />
      </div>
      <div class="material-info">
        <h3>{{ material.material.name }}</h3>
        <p class="part-number">零件号：{{ material.material.part_number }}</p>
        <p class="manufacturer">制造商：{{ material.material.manufacturer }}</p>
        <p class="price">当前价格：<span class="price-value">¥{{ formatPrice(material.price) }}</span> / 件</p>
        <p class="availability">可用数量：{{ material.available_quantity }} 件</p>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="询价数量" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="material?.min_order_quantity || 1"
          :max="material?.available_quantity || 999"
          style="width: 100%"
        />
        <div class="quantity-info">
          <el-text size="small" type="info">
            最小订购量：{{ material?.min_order_quantity || 1 }} 件，
            可用数量：{{ material?.available_quantity || 0 }} 件
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="期望价格">
        <el-input
          v-model.number="form.target_price"
          type="number"
          placeholder="可选，留空表示接受当前价格"
        >
          <template #append>元</template>
        </el-input>
        <div class="price-info">
          <el-text size="small" type="info">
            总价约：¥{{ formatPrice(estimatedTotal) }}
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="紧急程度" prop="urgency">
        <el-radio-group v-model="form.urgency">
          <el-radio value="normal">普通</el-radio>
          <el-radio value="urgent">紧急</el-radio>
          <el-radio value="aog">AOG</el-radio>
        </el-radio-group>
        <div class="urgency-help">
          <el-text size="small" type="info">
            <span v-if="form.urgency === 'normal'">
              普通需求，正常处理时间
            </span>
            <span v-else-if="form.urgency === 'urgent'">
              紧急需求，优先处理
            </span>
            <span v-else-if="form.urgency === 'aog'">
              飞机停场，最高优先级处理
            </span>
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="预期交付">
        <el-date-picker
          v-model="form.expected_delivery"
          type="date"
          placeholder="选择期望交付日期"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="询价信息" prop="message">
        <el-input
          v-model="form.message"
          type="textarea"
          :rows="4"
          placeholder="请描述您的具体需求、交付要求、技术规格等"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="联系方式">
        <el-input
          v-model="form.contact_info"
          placeholder="手机号码或邮箱（可选）"
        />
        <div class="contact-help">
          <el-text size="small" type="info">
            便于供应商快速联系您，如不填写将使用账户默认联系方式
          </el-text>
        </div>
      </el-form-item>

      <div class="inquiry-summary">
        <h4>询价汇总</h4>
        <div class="summary-item">
          <span>航材名称：</span>
          <span>{{ material?.material.name }}</span>
        </div>
        <div class="summary-item">
          <span>询价数量：</span>
          <span>{{ form.quantity }} 件</span>
        </div>
        <div class="summary-item">
          <span>期望价格：</span>
          <span>{{ form.target_price ? `¥${formatPrice(form.target_price)} / 件` : '按当前价格' }}</span>
        </div>
        <div class="summary-item">
          <span>预计总价：</span>
          <span class="total-price">¥{{ formatPrice(estimatedTotal) }}</span>
        </div>
        <div class="summary-item">
          <span>紧急程度：</span>
          <span :class="getUrgencyClass(form.urgency)">{{ getUrgencyText(form.urgency) }}</span>
        </div>
      </div>

      <div class="form-actions">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitForm">
          提交询价
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createInquiry } from '@/api/sharedMaterials'

// 定义属性
const props = defineProps({
  material: {
    type: Object,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)

// 表单数据
const form = reactive({
  quantity: 1,
  target_price: null,
  urgency: 'normal',
  expected_delivery: null,
  message: '',
  contact_info: ''
})

// 表单验证规则
const rules = {
  quantity: [
    { required: true, message: '请输入询价数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '询价数量至少为1', trigger: 'blur' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  message: [
    { required: true, message: '请输入询价信息', trigger: 'blur' },
    { min: 10, message: '询价信息至少10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const estimatedTotal = computed(() => {
  const price = form.target_price || props.material?.price || 0
  return price * form.quantity
})

// 监听器
watch(() => props.material, (newMaterial) => {
  if (newMaterial) {
    form.quantity = newMaterial.min_order_quantity || 1
  }
}, { immediate: true })

// 方法定义
const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN').format(price || 0)
}

const disabledDate = (date) => {
  return date < new Date()
}

const getUrgencyText = (urgency) => {
  const textMap = {
    normal: '普通',
    urgent: '紧急',
    aog: 'AOG'
  }
  return textMap[urgency] || '普通'
}

const getUrgencyClass = (urgency) => {
  const classMap = {
    normal: 'urgency-normal',
    urgent: 'urgency-urgent',
    aog: 'urgency-aog'
  }
  return classMap[urgency] || 'urgency-normal'
}

const submitForm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (!props.material?.id) {
      ElMessage.error('航材信息异常，请重新选择')
      return
    }

    submitting.value = true

    const submitData = {
      ...form,
      expected_delivery: form.expected_delivery ? form.expected_delivery.toISOString().split('T')[0] : null
    }

    const response = await createInquiry(props.material.id, submitData)
    
    if (response.success) {
      ElMessage.success('询价申请提交成功，供应商会尽快联系您')
      emit('success')
    } else {
      ElMessage.error(response.message || '提交询价失败')
    }
  } catch (error) {
    console.error('提交询价失败:', error)
    ElMessage.error('提交询价失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.shared-material-inquiry-form {
  max-height: 70vh;
  overflow-y: auto;
}

.material-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.material-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.material-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.material-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.price-value {
  color: #e6a23c;
  font-weight: 600;
}

.quantity-info,
.price-info,
.urgency-help,
.contact-help {
  margin-top: 8px;
}

.inquiry-summary {
  margin: 24px 0;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
}

.inquiry-summary h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.total-price {
  color: #e6a23c;
  font-weight: 600;
  font-size: 16px;
}

.urgency-normal {
  color: #67c23a;
}

.urgency-urgent {
  color: #e6a23c;
}

.urgency-aog {
  color: #f56c6c;
  font-weight: 600;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

@media (max-width: 768px) {
  .material-summary {
    flex-direction: column;
    text-align: center;
  }
  
  .material-image img {
    align-self: center;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>