<!--
角色数据看板组件
版本: 2.0
创建时间: 2025-07-19

通用的数据看板组件，根据用户角色显示不同的数据统计和图表
特点：
1. 支持多种图表类型
2. 角色自适应布局
3. 实时数据更新
4. 响应式设计
-->

<template>
  <div class="role-dashboard">
    <!-- 看板头部 -->
    <div class="dashboard-header">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="dashboard-title">{{ dashboardTitle }}</h2>
          <p class="dashboard-subtitle">{{ dashboardSubtitle }}</p>
        </div>
        <div class="dashboard-actions">
          <el-button-group>
            <el-button 
              v-for="period in timePeriods"
              :key="period.value"
              :type="selectedPeriod === period.value ? 'primary' : ''"
              size="small"
              @click="changePeriod(period.value)"
            >
              {{ period.label }}
            </el-button>
          </el-button-group>
          <el-button size="small" @click="refreshDashboard">
            <Refresh class="w-4 h-4 mr-1" />
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="key-metrics">
      <div class="metrics-grid" :class="getMetricsLayout">
        <div 
          v-for="metric in keyMetrics" 
          :key="metric.id"
          class="metric-card"
          :class="metric.color"
        >
          <div class="metric-icon">
            <component :is="metric.icon" class="w-6 h-6" />
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-change" :class="metric.changeClass">
              <component :is="metric.trendIcon" class="w-3 h-3 mr-1" />
              {{ metric.change }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid" :class="getChartsLayout">
        <!-- 主要趋势图表 -->
        <div class="chart-container main-chart">
          <div class="chart-header">
            <h3 class="chart-title">{{ mainChart.title }}</h3>
            <div class="chart-controls">
              <el-select 
                v-model="mainChart.selectedMetric" 
                size="small"
                @change="updateMainChart"
              >
                <el-option
                  v-for="option in mainChart.metricOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
          </div>
          <div class="chart-content">
            <LineChart 
              :data="mainChart.data" 
              :options="mainChart.options"
              :height="chartHeight"
            />
          </div>
        </div>

        <!-- 分布图表 -->
        <div class="chart-container distribution-chart">
          <div class="chart-header">
            <h3 class="chart-title">{{ distributionChart.title }}</h3>
          </div>
          <div class="chart-content">
            <PieChart 
              :data="distributionChart.data" 
              :options="distributionChart.options"
              :height="chartHeight"
            />
          </div>
        </div>

        <!-- 对比图表 -->
        <div class="chart-container comparison-chart" v-if="showComparisonChart">
          <div class="chart-header">
            <h3 class="chart-title">{{ comparisonChart.title }}</h3>
          </div>
          <div class="chart-content">
            <BarChart 
              :data="comparisonChart.data" 
              :options="comparisonChart.options"
              :height="chartHeight"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-section" v-if="showDataGrid">
      <div class="table-header">
        <h3 class="table-title">{{ dataGrid.title }}</h3>
        <div class="table-actions">
          <el-input
            v-model="tableSearch"
            placeholder="搜索..."
            size="small"
            class="table-search"
            clearable
          >
            <template #prefix>
              <Search class="w-4 h-4" />
            </template>
          </el-input>
          <el-button size="small" @click="exportGridData">
            <Download class="w-4 h-4 mr-1" />
            导出
          </el-button>
        </div>
      </div>
      
      <div class="table-content">
        <el-table
          :data="filteredGridData"
          :loading="tableLoading"
          style="width: 100%"
          stripe
          border
        >
          <el-table-column
            v-for="column in dataGrid.columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :formatter="column.formatter"
            :sortable="column.sortable"
          />
        </el-table>
      </div>
      
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="tablePagination.currentPage"
          v-model:page-size="tablePagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="tablePagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleGridSizeChange"
          @current-change="handleGridPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Search, Download, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import LineChart from './charts/LineChart.vue'
import PieChart from './charts/PieChart.vue'
import BarChart from './charts/BarChart.vue'

// Props
const props = defineProps({
  userRole: {
    type: String,
    required: true
  },
  dashboardConfig: {
    type: Object,
    required: true
  }
})

// Composables
const { currentUserRole, getWorkspaceStats } = useRoleWorkspace()

// 响应式数据
const selectedPeriod = ref('week')
const keyMetrics = ref([])
const mainChart = ref({})
const distributionChart = ref({})
const comparisonChart = ref({})
const dataGrid = ref({})
const tableSearch = ref('')
const tableLoading = ref(false)
const tablePagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 时间周期选项
const timePeriods = [
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本季', value: 'quarter' },
  { label: '本年', value: 'year' }
]

// 计算属性
const dashboardTitle = computed(() => {
  const titles = {
    airline_buyer: '航空公司采购员数据概览',
    platform_staff: '平台运营数据分析',
    maintenance_engineer: '维修工程数据统计',
    logistics_specialist: '物流配送数据监控',
    admin: '系统管理数据总览'
  }
  return titles[props.userRole] || '数据看板'
})

const dashboardSubtitle = computed(() => {
  const subtitles = {
    airline_buyer: '采购需求、库存共享、订单管理数据分析',
    platform_staff: '用户活动、交易数据、平台运营指标',
    maintenance_engineer: '维修进度、设备状态、工作效率统计',
    logistics_specialist: '配送进度、运输状态、物流效率分析',
    admin: '系统性能、用户管理、业务数据全览'
  }
  return subtitles[props.userRole] || '数据统计分析'
})

const getMetricsLayout = computed(() => {
  // 根据指标数量决定布局
  const count = keyMetrics.value.length
  if (count <= 2) return 'grid-cols-2'
  if (count <= 4) return 'grid-cols-2 lg:grid-cols-4'
  return 'grid-cols-2 lg:grid-cols-5'
})

const getChartsLayout = computed(() => {
  // 根据角色决定图表布局
  const layouts = {
    airline_buyer: 'grid-cols-1 lg:grid-cols-2',
    platform_staff: 'grid-cols-1 lg:grid-cols-2',
    maintenance_engineer: 'grid-cols-1 lg:grid-cols-2',
    logistics_specialist: 'grid-cols-1 lg:grid-cols-2',
    admin: 'grid-cols-1 lg:grid-cols-3'
  }
  return layouts[props.userRole] || 'grid-cols-1 lg:grid-cols-2'
})

const showComparisonChart = computed(() => {
  return ['platform_staff', 'admin'].includes(props.userRole)
})

const showDataGrid = computed(() => {
  return props.dashboardConfig.showGrid !== false
})

const chartHeight = computed(() => {
  return showComparisonChart.value ? 280 : 320
})

const filteredGridData = computed(() => {
  if (!tableSearch.value || !dataGrid.value.data) {
    return dataGrid.value.data || []
  }
  
  return dataGrid.value.data.filter(row => {
    return Object.values(row).some(value => 
      String(value).toLowerCase().includes(tableSearch.value.toLowerCase())
    )
  })
})

// 方法
const formatMetricValue = (value, type) => {
  switch (type) {
    case 'currency':
      return `¥${new Intl.NumberFormat('zh-CN').format(value)}`
    case 'percentage':
      return `${value}%`
    case 'number':
      return new Intl.NumberFormat('zh-CN').format(value)
    default:
      return value
  }
}

const changePeriod = async (period) => {
  selectedPeriod.value = period
  await loadDashboardData()
}

const refreshDashboard = async () => {
  ElMessage.info('刷新数据中...')
  await loadDashboardData()
  ElMessage.success('数据已更新')
}

const updateMainChart = async () => {
  // 更新主图表数据
  await loadChartData('main')
}

const exportGridData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleGridSizeChange = (newSize) => {
  tablePagination.value.pageSize = newSize
  loadGridData()
}

const handleGridPageChange = (newPage) => {
  tablePagination.value.currentPage = newPage
  loadGridData()
}

// 数据加载方法
const loadDashboardData = async () => {
  try {
    await Promise.all([
      loadKeyMetrics(),
      loadChartData('main'),
      loadChartData('distribution'),
      showComparisonChart.value ? loadChartData('comparison') : Promise.resolve(),
      showDataGrid.value ? loadGridData() : Promise.resolve()
    ])
  } catch (error) {
    console.error('加载看板数据失败:', error)
    ElMessage.error('数据加载失败')
  }
}

const loadKeyMetrics = async () => {
  // 根据用户角色加载关键指标
  const metricsConfig = {
    airline_buyer: [
      {
        id: 'active_demands',
        label: '活跃需求',
        value: 23,
        type: 'number',
        color: 'metric-primary',
        icon: 'Document',
        change: 12.5,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'shared_revenue',
        label: '共享收益',
        value: 156800,
        type: 'currency',
        color: 'metric-success',
        icon: 'Money',
        change: 8.3,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'order_count',
        label: '订单数量',
        value: 45,
        type: 'number',
        color: 'metric-warning',
        icon: 'Box',
        change: -2.1,
        changeClass: 'metric-down',
        trendIcon: ArrowDown
      },
      {
        id: 'inventory_alerts',
        label: '库存预警',
        value: 8,
        type: 'number',
        color: 'metric-danger',
        icon: 'Warning',
        change: -15.2,
        changeClass: 'metric-down',
        trendIcon: ArrowDown
      }
    ],
    platform_staff: [
      {
        id: 'active_users',
        label: '活跃用户',
        value: 1247,
        type: 'number',
        color: 'metric-primary',
        icon: 'User',
        change: 15.6,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'daily_revenue',
        label: '日收益',
        value: 456000,
        type: 'currency',
        color: 'metric-success',
        icon: 'Money',
        change: 22.3,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'transaction_count',
        label: '交易数量',
        value: 189,
        type: 'number',
        color: 'metric-warning',
        icon: 'TrendUp',
        change: 5.7,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'system_health',
        label: '系统健康度',
        value: 98.5,
        type: 'percentage',
        color: 'metric-info',
        icon: 'Monitor',
        change: 1.2,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      }
    ],
    maintenance_engineer: [
      {
        id: 'work_orders',
        label: '工单数量',
        value: 34,
        type: 'number',
        color: 'metric-primary',
        icon: 'Tools',
        change: 8.9,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'completion_rate',
        label: '完成率',
        value: 94.2,
        type: 'percentage',
        color: 'metric-success',
        icon: 'CheckCircle',
        change: 3.1,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'avg_time',
        label: '平均用时',
        value: 4.5,
        type: 'number',
        color: 'metric-warning',
        icon: 'Timer',
        change: -12.3,
        changeClass: 'metric-down',
        trendIcon: ArrowDown
      }
    ],
    logistics_specialist: [
      {
        id: 'active_shipments',
        label: '运输中',
        value: 67,
        type: 'number',
        color: 'metric-primary',
        icon: 'Truck',
        change: 11.2,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'on_time_rate',
        label: '准时率',
        value: 92.8,
        type: 'percentage',
        color: 'metric-success',
        icon: 'Timer',
        change: 2.5,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      },
      {
        id: 'delivery_count',
        label: '配送量',
        value: 234,
        type: 'number',
        color: 'metric-info',
        icon: 'Box',
        change: 18.6,
        changeClass: 'metric-up',
        trendIcon: ArrowUp
      }
    ]
  }
  
  keyMetrics.value = metricsConfig[props.userRole] || []
}

const loadChartData = async (chartType) => {
  // 模拟加载图表数据
  if (chartType === 'main') {
    mainChart.value = {
      title: '趋势分析',
      selectedMetric: 'revenue',
      metricOptions: [
        { label: '收益', value: 'revenue' },
        { label: '订单量', value: 'orders' },
        { label: '用户活跃度', value: 'activity' }
      ],
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '收益 (万元)',
          data: [65, 78, 82, 91, 103, 118],
          borderColor: '#1890ff',
          backgroundColor: 'rgba(24, 144, 255, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    }
  } else if (chartType === 'distribution') {
    distributionChart.value = {
      title: '分布统计',
      data: {
        labels: ['发动机', '起落架', '航电', '轮胎', '其他'],
        datasets: [{
          data: [35, 25, 20, 15, 5],
          backgroundColor: [
            '#1890ff',
            '#52c41a',
            '#faad14',
            '#f5222d',
            '#722ed1'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    }
  } else if (chartType === 'comparison') {
    comparisonChart.value = {
      title: '对比分析',
      data: {
        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
        datasets: [
          {
            label: '去年',
            data: [65, 78, 85, 92],
            backgroundColor: '#e6f7ff'
          },
          {
            label: '今年',
            data: [78, 89, 95, 105],
            backgroundColor: '#1890ff'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    }
  }
}

const loadGridData = async () => {
  tableLoading.value = true
  
  try {
    // 模拟表格数据
    const tableConfigs = {
      airline_buyer: {
        title: '最近订单记录',
        columns: [
          { prop: 'orderNo', label: '订单号', width: 150, sortable: true },
          { prop: 'materialName', label: '航材名称', sortable: true },
          { prop: 'quantity', label: '数量', width: 100, sortable: true },
          { prop: 'amount', label: '金额', width: 120, formatter: (row) => `¥${row.amount}`, sortable: true },
          { prop: 'status', label: '状态', width: 100 },
          { prop: 'createTime', label: '创建时间', width: 160, sortable: true }
        ],
        data: [
          {
            orderNo: 'PO-2024-001',
            materialName: 'CFM56发动机燃油喷嘴',
            quantity: 2,
            amount: 125000,
            status: '进行中',
            createTime: '2024-07-19 10:30'
          },
          {
            orderNo: 'PO-2024-002',
            materialName: '起落架轴承',
            quantity: 4,
            amount: 85000,
            status: '已完成',
            createTime: '2024-07-18 14:20'
          }
        ]
      },
      platform_staff: {
        title: '用户活动记录',
        columns: [
          { prop: 'username', label: '用户名', sortable: true },
          { prop: 'company', label: '公司', sortable: true },
          { prop: 'action', label: '操作', sortable: true },
          { prop: 'time', label: '时间', width: 160, sortable: true },
          { prop: 'ip', label: 'IP地址', width: 120 }
        ],
        data: [
          {
            username: '张三',
            company: '中国国航',
            action: '发布需求',
            time: '2024-07-19 15:30',
            ip: '*************'
          },
          {
            username: '李四',
            company: '东方航空',
            action: '更新库存',
            time: '2024-07-19 14:45',
            ip: '*************'
          }
        ]
      }
    }
    
    dataGrid.value = tableConfigs[props.userRole] || { title: '', columns: [], data: [] }
    tablePagination.value.total = dataGrid.value.data.length
    
  } finally {
    tableLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})

// 监听角色变化
watch(() => props.userRole, () => {
  loadDashboardData()
})
</script>

<style scoped>
.role-dashboard {
  @apply space-y-8;
}

.dashboard-header {
  @apply modern-card p-6;
}

.dashboard-title {
  @apply text-2xl font-bold text-gray-800;
}

.dashboard-subtitle {
  @apply text-gray-600 mt-1;
}

.dashboard-actions {
  @apply flex items-center space-x-4;
}

.key-metrics {
  @apply space-y-4;
}

.metrics-grid {
  @apply grid gap-6;
}

.metric-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.metric-card.metric-primary {
  @apply border-l-4 border-primary-500;
}

.metric-card.metric-success {
  @apply border-l-4 border-success-500;
}

.metric-card.metric-warning {
  @apply border-l-4 border-warning-500;
}

.metric-card.metric-danger {
  @apply border-l-4 border-red-500;
}

.metric-card.metric-info {
  @apply border-l-4 border-blue-500;
}

.metric-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.metric-card.metric-primary .metric-icon {
  @apply bg-primary-100 text-primary-600;
}

.metric-card.metric-success .metric-icon {
  @apply bg-success-100 text-success-600;
}

.metric-card.metric-warning .metric-icon {
  @apply bg-warning-100 text-warning-600;
}

.metric-card.metric-danger .metric-icon {
  @apply bg-red-100 text-red-600;
}

.metric-card.metric-info .metric-icon {
  @apply bg-blue-100 text-blue-600;
}

.metric-content {
  @apply flex-1;
}

.metric-value {
  @apply text-2xl font-bold text-gray-800;
}

.metric-label {
  @apply text-sm text-gray-600 mt-1;
}

.metric-change {
  @apply text-xs font-medium flex items-center mt-2;
}

.metric-change.metric-up {
  @apply text-success-600;
}

.metric-change.metric-down {
  @apply text-red-600;
}

.charts-section {
  @apply space-y-6;
}

.charts-grid {
  @apply grid gap-6;
}

.chart-container {
  @apply modern-card p-6;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800;
}

.chart-controls {
  @apply flex items-center space-x-2;
}

.chart-content {
  @apply w-full;
}

.data-table-section {
  @apply modern-card p-6;
}

.table-header {
  @apply flex items-center justify-between mb-4;
}

.table-title {
  @apply text-lg font-semibold text-gray-800;
}

.table-actions {
  @apply flex items-center space-x-4;
}

.table-search {
  @apply w-64;
}

.table-content {
  @apply mb-4;
}

.table-pagination {
  @apply flex justify-end;
}
</style>