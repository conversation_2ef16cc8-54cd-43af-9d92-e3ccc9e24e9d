<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑维修计划' : '新建维修计划'"
    width="1000px"
    :before-close="handleClose"
    class="maintenance-schedule-dialog"
  >
    <div class="schedule-form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="schedule-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <el-form-item label="飞机号" prop="aircraft_tail" class="form-item">
              <el-select
                v-model="form.aircraft_tail"
                placeholder="请选择飞机号"
                filterable
                @change="handleAircraftChange"
              >
                <el-option
                  v-for="aircraft in aircraftList"
                  :key="aircraft.tail_number"
                  :label="aircraft.tail_number"
                  :value="aircraft.tail_number"
                >
                  <span>{{ aircraft.tail_number }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px;">
                    {{ aircraft.aircraft_type }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="机型" prop="aircraft_type" class="form-item">
              <el-input v-model="form.aircraft_type" placeholder="自动填充" readonly />
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="维修类型" prop="maintenance_type" class="form-item">
              <el-select v-model="form.maintenance_type" placeholder="请选择维修类型">
                <el-option label="A检" value="A检" />
                <el-option label="B检" value="B检" />
                <el-option label="C检" value="C检" />
                <el-option label="D检" value="D检" />
                <el-option label="日常维护" value="日常维护" />
                <el-option label="预防性维护" value="预防性维护" />
                <el-option label="故障维修" value="故障维修" />
                <el-option label="改装维修" value="改装维修" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优先级" prop="priority" class="form-item">
              <el-select v-model="form.priority" placeholder="请选择优先级">
                <el-option label="AOG(停机)" value="aog" />
                <el-option label="高优先级" value="high" />
                <el-option label="普通" value="normal" />
                <el-option label="低优先级" value="low" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 时间安排 -->
        <div class="form-section">
          <h3 class="section-title">时间安排</h3>
          <div class="form-row">
            <el-form-item label="计划开始时间" prop="scheduled_start" class="form-item">
              <el-date-picker
                v-model="form.scheduled_start"
                type="datetime"
                placeholder="选择计划开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="calculateEstimatedEnd"
              />
            </el-form-item>
            
            <el-form-item label="预计工期" prop="estimated_duration" class="form-item">
              <el-input-number
                v-model="form.estimated_duration"
                :min="1"
                :max="720"
                :step="1"
                placeholder="小时"
                @change="calculateEstimatedEnd"
              />
              <span class="unit">小时</span>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="预计完成时间" prop="estimated_end" class="form-item">
              <el-date-picker
                v-model="form.estimated_end"
                type="datetime"
                placeholder="自动计算或手动选择"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            
            <el-form-item label="缓冲时间" prop="buffer_time" class="form-item">
              <el-input-number
                v-model="form.buffer_time"
                :min="0"
                :max="48"
                :step="0.5"
                placeholder="小时"
              />
              <span class="unit">小时</span>
            </el-form-item>
          </div>
        </div>

        <!-- 资源分配 -->
        <div class="form-section">
          <h3 class="section-title">资源分配</h3>
          <div class="form-row">
            <el-form-item label="分配机库" prop="hangar" class="form-item">
              <el-select v-model="form.hangar" placeholder="请选择机库">
                <el-option
                  v-for="hangar in hangarList"
                  :key="hangar.id"
                  :label="hangar.name"
                  :value="hangar.id"
                  :disabled="!hangar.available"
                >
                  <span>{{ hangar.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px;">
                    {{ hangar.available ? '可用' : '占用中' }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="机位" prop="bay" class="form-item">
              <el-input v-model="form.bay" placeholder="请输入机位号" />
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="分配团队" prop="assigned_team" class="form-item">
              <el-select v-model="form.assigned_team" placeholder="请选择维修团队">
                <el-option
                  v-for="team in teamList"
                  :key="team.id"
                  :label="team.name"
                  :value="team.id"
                >
                  <span>{{ team.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px;">
                    {{ team.available_members }}/{{ team.total_members }}人
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="技师需求" prop="technician_count" class="form-item">
              <el-input-number
                v-model="form.technician_count"
                :min="1"
                :max="20"
                :step="1"
                placeholder="人数"
              />
              <span class="unit">人</span>
            </el-form-item>
          </div>
        </div>

        <!-- 维修内容 -->
        <div class="form-section">
          <h3 class="section-title">维修内容</h3>
          <el-form-item label="维修项目" prop="maintenance_items">
            <div class="maintenance-items">
              <div
                v-for="(item, index) in form.maintenance_items"
                :key="index"
                class="maintenance-item"
              >
                <div class="item-row">
                  <el-input
                    v-model="item.name"
                    placeholder="维修项目名称"
                    class="item-input"
                  />
                  <el-select
                    v-model="item.category"
                    placeholder="类别"
                    class="item-select"
                  >
                    <el-option label="系统检查" value="system_check" />
                    <el-option label="零件更换" value="part_replacement" />
                    <el-option label="功能测试" value="function_test" />
                    <el-option label="清洁保养" value="cleaning" />
                    <el-option label="校准调整" value="calibration" />
                  </el-select>
                  <el-input-number
                    v-model="item.estimated_hours"
                    :min="0.1"
                    :max="100"
                    :step="0.1"
                    placeholder="工时"
                    class="item-hours"
                  />
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeMaintenanceItem(index)"
                  >
                    <template #icon><el-icon><Delete /></el-icon></template>
                  </el-button>
                </div>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="addMaintenanceItem"
              >
                <template #icon><el-icon><Plus /></el-icon></template>
                添加维修项目
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="维修说明" prop="maintenance_notes">
            <el-input
              v-model="form.maintenance_notes"
              type="textarea"
              :rows="4"
              placeholder="请输入维修说明、注意事项等"
            />
          </el-form-item>
        </div>

        <!-- 质量要求 -->
        <div class="form-section">
          <h3 class="section-title">质量要求</h3>
          <div class="form-row">
            <el-form-item label="质量标准" prop="quality_standard" class="form-item">
              <el-select v-model="form.quality_standard" placeholder="请选择质量标准">
                <el-option label="CAAC标准" value="caac" />
                <el-option label="FAA标准" value="faa" />
                <el-option label="EASA标准" value="easa" />
                <el-option label="公司标准" value="company" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="检查方式" prop="inspection_method" class="form-item">
              <el-select v-model="form.inspection_method" placeholder="请选择检查方式">
                <el-option label="目视检查" value="visual" />
                <el-option label="功能测试" value="functional" />
                <el-option label="无损检测" value="ndt" />
                <el-option label="仪器检测" value="instrument" />
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="质量检查点" prop="quality_checkpoints">
            <el-input
              v-model="form.quality_checkpoints"
              type="textarea"
              :rows="3"
              placeholder="请输入关键质量检查点"
            />
          </el-form-item>
        </div>

        <!-- 成本预算 -->
        <div class="form-section">
          <h3 class="section-title">成本预算</h3>
          <div class="cost-breakdown">
            <div class="cost-item">
              <el-form-item label="人工成本" prop="labor_cost" class="cost-form-item">
                <el-input-number
                  v-model="form.labor_cost"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                />
                <span class="unit">元</span>
              </el-form-item>
            </div>
            <div class="cost-item">
              <el-form-item label="材料成本" prop="material_cost" class="cost-form-item">
                <el-input-number
                  v-model="form.material_cost"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                />
                <span class="unit">元</span>
              </el-form-item>
            </div>
            <div class="cost-item">
              <el-form-item label="设备成本" prop="equipment_cost" class="cost-form-item">
                <el-input-number
                  v-model="form.equipment_cost"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                />
                <span class="unit">元</span>
              </el-form-item>
            </div>
            <div class="cost-item total-cost">
              <span class="label">总预算：</span>
              <span class="value">¥{{ totalCost }}</span>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handlePreview">预览</el-button>
        <el-button type="success" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新计划' : '创建计划' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import { createMaintenanceSchedule } from '@/api/maintenance'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  scheduleData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)
const submitting = ref(false)

// 基础数据
const aircraftList = ref([
  { tail_number: 'B-1234', aircraft_type: 'A320', status: 'available' },
  { tail_number: 'B-5678', aircraft_type: 'B737', status: 'available' },
  { tail_number: 'B-9012', aircraft_type: 'A321', status: 'maintenance' },
  { tail_number: 'B-3456', aircraft_type: 'B777', status: 'available' }
])

const hangarList = ref([
  { id: 1, name: '1号机库', available: true, capacity: 2 },
  { id: 2, name: '2号机库', available: false, capacity: 3 },
  { id: 3, name: '3号机库', available: true, capacity: 1 }
])

const teamList = ref([
  { id: 1, name: '第一维修组', total_members: 8, available_members: 6 },
  { id: 2, name: '第二维修组', total_members: 10, available_members: 8 },
  { id: 3, name: '第三维修组', total_members: 6, available_members: 4 }
])

// 表单数据
const form = reactive({
  aircraft_tail: '',
  aircraft_type: '',
  maintenance_type: '',
  priority: 'normal',
  scheduled_start: '',
  estimated_duration: 24,
  estimated_end: '',
  buffer_time: 2,
  hangar: '',
  bay: '',
  assigned_team: '',
  technician_count: 4,
  maintenance_items: [
    {
      name: '',
      category: '',
      estimated_hours: 1
    }
  ],
  maintenance_notes: '',
  quality_standard: 'caac',
  inspection_method: 'visual',
  quality_checkpoints: '',
  labor_cost: 0,
  material_cost: 0,
  equipment_cost: 0
})

// 表单验证规则
const rules = {
  aircraft_tail: [
    { required: true, message: '请选择飞机号', trigger: 'change' }
  ],
  aircraft_type: [
    { required: true, message: '请选择机型', trigger: 'change' }
  ],
  maintenance_type: [
    { required: true, message: '请选择维修类型', trigger: 'change' }
  ],
  scheduled_start: [
    { required: true, message: '请选择计划开始时间', trigger: 'change' }
  ],
  estimated_duration: [
    { required: true, message: '请输入预计工期', trigger: 'blur' },
    { type: 'number', min: 1, max: 720, message: '工期必须在1-720小时之间', trigger: 'blur' }
  ],
  hangar: [
    { required: true, message: '请选择机库', trigger: 'change' }
  ],
  assigned_team: [
    { required: true, message: '请选择维修团队', trigger: 'change' }
  ],
  technician_count: [
    { required: true, message: '请输入技师需求', trigger: 'blur' },
    { type: 'number', min: 1, max: 20, message: '技师需求必须在1-20人之间', trigger: 'blur' }
  ]
}

// 计算属性
const totalCost = computed(() => {
  return (form.labor_cost + form.material_cost + form.equipment_cost).toFixed(2)
})

// 监听计划数据变化
watch(() => props.scheduleData, (newData) => {
  if (newData && props.isEdit) {
    initFormData(newData)
  }
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
  if (!props.isEdit) {
    resetForm()
  }
})

// 初始化表单数据
const initFormData = (data) => {
  Object.assign(form, {
    aircraft_tail: data.aircraft_tail || '',
    aircraft_type: data.aircraft_type || '',
    maintenance_type: data.maintenance_type || '',
    priority: data.priority || 'normal',
    scheduled_start: data.scheduled_start || '',
    estimated_duration: data.estimated_duration || 24,
    estimated_end: data.estimated_end || '',
    buffer_time: data.buffer_time || 2,
    hangar: data.hangar || '',
    bay: data.bay || '',
    assigned_team: data.assigned_team || '',
    technician_count: data.technician_count || 4,
    maintenance_items: data.maintenance_items || [{ name: '', category: '', estimated_hours: 1 }],
    maintenance_notes: data.maintenance_notes || '',
    quality_standard: data.quality_standard || 'caac',
    inspection_method: data.inspection_method || 'visual',
    quality_checkpoints: data.quality_checkpoints || '',
    labor_cost: data.labor_cost || 0,
    material_cost: data.material_cost || 0,
    equipment_cost: data.equipment_cost || 0
  })
}

// 飞机选择变化
const handleAircraftChange = (tailNumber) => {
  const aircraft = aircraftList.value.find(a => a.tail_number === tailNumber)
  if (aircraft) {
    form.aircraft_type = aircraft.aircraft_type
  }
}

// 计算预计结束时间
const calculateEstimatedEnd = () => {
  if (form.scheduled_start && form.estimated_duration) {
    const start = new Date(form.scheduled_start)
    const end = new Date(start.getTime() + (form.estimated_duration + (form.buffer_time || 0)) * 60 * 60 * 1000)
    form.estimated_end = end.toISOString().slice(0, 19).replace('T', ' ')
  }
}

// 添加维修项目
const addMaintenanceItem = () => {
  form.maintenance_items.push({
    name: '',
    category: '',
    estimated_hours: 1
  })
}

// 删除维修项目
const removeMaintenanceItem = (index) => {
  if (form.maintenance_items.length > 1) {
    form.maintenance_items.splice(index, 1)
  }
}

// 预览计划
const handlePreview = () => {
  ElMessage.info('预览功能开发中...')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证维修项目
    const hasEmptyItems = form.maintenance_items.some(item => !item.name || !item.category)
    if (hasEmptyItems) {
      ElMessage.error('请完善维修项目信息')
      return
    }

    // 验证时间逻辑
    if (form.scheduled_start && form.estimated_end) {
      if (new Date(form.scheduled_start) >= new Date(form.estimated_end)) {
        ElMessage.error('预计完成时间必须晚于计划开始时间')
        return
      }
    }

    submitting.value = true

    const scheduleData = {
      ...form,
      total_cost: parseFloat(totalCost.value),
      status: 'scheduled'
    }

    const response = await createMaintenanceSchedule(scheduleData)
    
    if (response.success) {
      ElMessage.success(`维修计划${props.isEdit ? '更新' : '创建'}成功`)
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.message || `${props.isEdit ? '更新' : '创建'}失败`)
    }
  } catch (error) {
    console.error('提交维修计划失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    aircraft_tail: '',
    aircraft_type: '',
    maintenance_type: '',
    priority: 'normal',
    scheduled_start: '',
    estimated_duration: 24,
    estimated_end: '',
    buffer_time: 2,
    hangar: '',
    bay: '',
    assigned_team: '',
    technician_count: 4,
    maintenance_items: [
      {
        name: '',
        category: '',
        estimated_hours: 1
      }
    ],
    maintenance_notes: '',
    quality_standard: 'caac',
    inspection_method: 'visual',
    quality_checkpoints: '',
    labor_cost: 0,
    material_cost: 0,
    equipment_cost: 0
  })
}
</script>

<style scoped>
.maintenance-schedule-dialog {
  --el-dialog-content-font-size: 14px;
}

.schedule-form-container {
  max-height: 80vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-item {
  margin-bottom: 16px;
}

.unit {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.maintenance-items {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.maintenance-item {
  margin-bottom: 12px;
}

.item-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.item-input {
  flex: 2;
}

.item-select {
  flex: 1;
}

.item-hours {
  width: 100px;
}

.cost-breakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  align-items: end;
}

.cost-item {
  display: flex;
  flex-direction: column;
}

.cost-form-item {
  margin-bottom: 0;
}

.cost-item.total-cost {
  justify-content: center;
  align-items: center;
  padding: 16px;
  background: #f3f4f6;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.total-cost .label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.total-cost .value {
  font-size: 20px;
  font-weight: bold;
  color: #059669;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.schedule-form-container::-webkit-scrollbar {
  width: 6px;
}

.schedule-form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.schedule-form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.schedule-form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .cost-breakdown {
    grid-template-columns: 1fr;
  }
  
  .item-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .item-input,
  .item-select,
  .item-hours {
    width: 100%;
  }
}
</style>