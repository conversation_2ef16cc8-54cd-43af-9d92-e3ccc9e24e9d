<template>
  <div v-if="modelValue" class="notification-panel">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-25 z-40" @click="closePanel"></div>
    
    <!-- 通知面板 -->
    <div class="fixed top-20 right-6 w-96 bg-white rounded-2xl shadow-2xl z-50 max-h-[600px] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
            <Bell class="w-4 h-4 text-primary-600" />
          </div>
          <h3 class="text-lg font-semibold text-gray-800">通知中心</h3>
        </div>
        <div class="flex items-center space-x-2">
          <el-button link size="small" @click="markAllAsRead">
            全部已读
          </el-button>
          <el-button link size="small" @click="closePanel">
            <Close class="w-4 h-4" />
          </el-button>
        </div>
      </div>

      <!-- 通知筛选 -->
      <div class="p-4 border-b border-gray-100">
        <el-segmented v-model="activeTab" :options="tabOptions" style="width: 100%" />
      </div>

      <!-- 通知列表 -->
      <div class="overflow-y-auto max-h-96">
        <div v-if="filteredNotifications.length === 0" class="text-center py-12">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Bell class="w-8 h-8 text-gray-400" />
          </div>
          <p class="text-gray-500">暂无通知</p>
        </div>

        <div v-else class="space-y-1">
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="notification-item px-6 py-4 hover:bg-gray-50 transition-colors cursor-pointer border-b border-gray-50 last:border-b-0"
            :class="{ 'bg-blue-50': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <!-- 通知图标 -->
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0 mt-1">
                <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="getNotificationIconClass(notification.type)">
                  <component :is="getNotificationIcon(notification.type)" class="w-4 h-4" />
                </div>
                <div v-if="!notification.read" class="w-2 h-2 bg-primary-500 rounded-full mt-1 ml-3"></div>
              </div>

              <!-- 通知内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <h4 class="text-sm font-medium text-gray-800 truncate">
                    {{ notification.title }}
                  </h4>
                  <span class="text-xs text-gray-500 flex-shrink-0 ml-2">
                    {{ formatTime(notification.created_at) }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 line-clamp-2">
                  {{ notification.content }}
                </p>
                <div v-if="notification.metadata" class="mt-2 flex items-center space-x-2">
                  <el-tag v-if="notification.metadata.order_number" size="small" type="info">
                    {{ notification.metadata.order_number }}
                  </el-tag>
                  <el-tag v-if="notification.metadata.priority === 'high'" size="small" type="danger">
                    紧急
                  </el-tag>
                  <el-tag v-if="notification.metadata.priority === 'aog'" size="small" type="danger">
                    AOG
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="p-4 border-t border-gray-100 bg-gray-50">
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-500">
            {{ unreadCount }} 条未读通知
          </span>
          <el-button link type="primary" size="small" @click="viewAllNotifications">
            查看全部
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Bell, Close, Box, Warning, SuccessFilled, 
  Money, Van, Message, Setting 
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const router = useRouter()

// 状态
const activeTab = ref('all')

// 筛选选项
const tabOptions = [
  { label: '全部', value: 'all' },
  { label: '订单', value: 'order' },
  { label: '库存', value: 'inventory' },
  { label: '系统', value: 'system' }
]

// 模拟通知数据
const notifications = ref([
  {
    id: 1,
    type: 'order',
    title: '新订单待处理',
    content: '您有一个新的采购订单需要确认，订单号：PO-2025-001，请及时处理。',
    read: false,
    created_at: '2025-01-13T10:30:00Z',
    metadata: {
      order_number: 'PO-2025-001',
      priority: 'normal'
    }
  },
  {
    id: 2,
    type: 'inventory',
    title: '库存预警',
    content: '主起落架减震支柱库存不足，当前库存：3件，安全库存：5件，请及时补货。',
    read: false,
    created_at: '2025-01-13T09:15:00Z',
    metadata: {
      material_name: '主起落架减震支柱',
      priority: 'high'
    }
  },
  {
    id: 3,
    type: 'aog',
    title: 'AOG紧急响应',
    content: '南方航空维修紧急需求轮胎组件，已启动AOG响应流程，预计2小时内提供解决方案。',
    read: false,
    created_at: '2025-01-13T08:45:00Z',
    metadata: {
      customer: '南方航空维修',
      priority: 'aog'
    }
  },
  {
    id: 4,
    type: 'order',
    title: '订单已发货',
    content: '订单 SO-2025-002 已发货，预计明天到达，请注意查收。',
    read: true,
    created_at: '2025-01-12T16:20:00Z',
    metadata: {
      order_number: 'SO-2025-002',
      priority: 'normal'
    }
  },
  {
    id: 5,
    type: 'system',
    title: '系统维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用。',
    read: true,
    created_at: '2025-01-12T14:00:00Z',
    metadata: {
      priority: 'normal'
    }
  }
])

// 计算属性
const filteredNotifications = computed(() => {
  if (activeTab.value === 'all') {
    return notifications.value
  }
  return notifications.value.filter(notification => notification.type === activeTab.value)
})

const unreadCount = computed(() => {
  return notifications.value.filter(notification => !notification.read).length
})

// 方法
const closePanel = () => {
  emit('update:modelValue', false)
}

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
  ElMessage.success('所有通知已标记为已读')
}

const handleNotificationClick = (notification) => {
  // 标记为已读
  notification.read = true
  
  // 根据通知类型跳转到相应页面
  switch (notification.type) {
    case 'order':
      if (notification.metadata?.order_number) {
        router.push('/app/orders')
        closePanel()
      }
      break
    case 'inventory':
      router.push('/app/inventory')
      closePanel()
      break
    case 'aog':
      router.push('/app/orders')
      closePanel()
      break
    default:
      break
  }
}

const viewAllNotifications = () => {
  // 跳转到通知中心页面
  router.push('/app/notifications')
  closePanel()
}

const getNotificationIcon = (type) => {
  const icons = {
    order: Box,
    inventory: Warning,
    aog: Warning,
    system: Setting,
    payment: Money,
    shipping: Van,
    message: Message,
    success: SuccessFilled
  }
  return icons[type] || Bell
}

const getNotificationIconClass = (type) => {
  const classes = {
    order: 'bg-blue-100 text-blue-600',
    inventory: 'bg-orange-100 text-orange-600',
    aog: 'bg-red-100 text-red-600',
    system: 'bg-gray-100 text-gray-600',
    payment: 'bg-green-100 text-green-600',
    shipping: 'bg-purple-100 text-purple-600',
    message: 'bg-indigo-100 text-indigo-600',
    success: 'bg-emerald-100 text-emerald-600'
  }
  return classes[type] || 'bg-gray-100 text-gray-600'
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.notification-panel {
  z-index: 1000;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-item {
  position: relative;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-item.bg-blue-50 {
  background-color: #eff6ff;
}

.notification-item.bg-blue-50:hover {
  background-color: #dbeafe;
}
</style>