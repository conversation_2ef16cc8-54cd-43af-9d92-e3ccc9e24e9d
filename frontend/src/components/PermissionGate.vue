<!--
权限控制组件 - PermissionGate
版本: 1.0
创建时间: 2025-07-22

根据用户权限控制内容的显示与隐藏
支持多种权限验证模式：单一权限、多权限任一、多权限全部、角色验证等
-->

<template>
  <div v-if="hasAccess" :class="gateClass">
    <slot></slot>
  </div>
</template>

<script>
import { computed, inject } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'PermissionGate',
  props: {
    // 权限验证相关
    permission: {
      type: [String, Array],
      default: null,
      validator(value) {
        if (!value) return true
        if (typeof value === 'string') return true
        if (Array.isArray(value) && value.every(v => typeof v === 'string')) return true
        return false
      }
    },
    
    // 权限验证模式
    mode: {
      type: String,
      default: 'any', // 'any': 任一权限, 'all': 全部权限
      validator(value) {
        return ['any', 'all'].includes(value)
      }
    },
    
    // 角色验证
    role: {
      type: [String, Array],
      default: null
    },
    
    // 用户类型验证
    userType: {
      type: [String, Array], 
      default: null
    },
    
    // 公司类型验证
    companyType: {
      type: String,
      default: null,
      validator(value) {
        return !value || ['internal', 'external'].includes(value)
      }
    },
    
    // 权限等级验证
    permissionLevel: {
      type: Number,
      default: null,
      validator(value) {
        return !value || (Number.isInteger(value) && value >= 1 && value <= 4)
      }
    },
    
    // 资源所有权验证
    resourceOwnerId: {
      type: Number,
      default: null
    },
    
    // 允许跨公司访问
    allowCrossCompany: {
      type: Boolean,
      default: false
    },
    
    // 反转验证结果（当不具有权限时显示）
    reverse: {
      type: Boolean,
      default: false
    },
    
    // 自定义验证函数
    customValidator: {
      type: Function,
      default: null
    },
    
    // CSS类名
    gateClass: {
      type: String,
      default: ''
    },
    
    // 调试模式
    debug: {
      type: Boolean,
      default: false
    }
  },
  
  setup(props) {
    const authStore = useAuthStore()
    
    // 当前用户信息
    const currentUser = computed(() => authStore.user)
    const isAuthenticated = computed(() => authStore.isAuthenticated)
    const userPermissions = computed(() => authStore.userPermissions || [])
    
    // 权限验证函数
    const validatePermission = (permission, userPerms, mode = 'any') => {
      if (!permission) return true
      if (!userPerms || userPerms.length === 0) return false
      
      // 管理员拥有所有权限
      if (userPerms.includes('all_permissions')) return true
      
      if (typeof permission === 'string') {
        return userPerms.includes(permission)
      }
      
      if (Array.isArray(permission)) {
        if (mode === 'all') {
          return permission.every(p => userPerms.includes(p))
        } else {
          return permission.some(p => userPerms.includes(p))
        }
      }
      
      return false
    }
    
    // 角色验证函数
    const validateRole = (role, userRole) => {
      if (!role) return true
      if (!userRole) return false
      
      if (typeof role === 'string') {
        return userRole === role
      }
      
      if (Array.isArray(role)) {
        return role.includes(userRole)
      }
      
      return false
    }
    
    // 用户类型验证函数
    const validateUserType = (userType, currentUserType) => {
      if (!userType) return true
      if (!currentUserType) return false
      
      if (typeof userType === 'string') {
        return currentUserType === userType
      }
      
      if (Array.isArray(userType)) {
        return userType.includes(currentUserType)
      }
      
      return false
    }
    
    // 公司类型验证函数
    const validateCompanyType = (companyType, userCompanyType) => {
      if (!companyType) return true
      return userCompanyType === companyType
    }
    
    // 权限等级验证函数
    const validatePermissionLevel = (requiredLevel, userLevel) => {
      if (!requiredLevel) return true
      return userLevel >= requiredLevel
    }
    
    // 资源所有权验证函数
    const validateResourceOwnership = (resourceOwnerId, userId, user, allowCrossCompany) => {
      if (!resourceOwnerId) return true
      if (!userId) return false
      
      // 直接所有权
      if (resourceOwnerId === userId) return true
      
      // 管理员权限
      if (user && (user.user_type === 'admin' || user.permission_level >= 3)) {
        return true
      }
      
      // 跨公司访问权限
      if (allowCrossCompany && user) {
        return user.company_type === 'internal' || user.user_type === 'platform_staff'
      }
      
      return false
    }
    
    // 主验证函数
    const hasAccess = computed(() => {
      // 未认证用户无权限
      if (!isAuthenticated.value) {
        if (props.debug) {
          console.log('[PermissionGate] 用户未认证')
        }
        return props.reverse ? true : false
      }
      
      const user = currentUser.value
      const permissions = userPermissions.value
      
      if (!user) {
        if (props.debug) {
          console.log('[PermissionGate] 用户信息不存在')
        }
        return props.reverse ? true : false
      }
      
      let result = true
      
      // 权限验证
      if (props.permission) {
        const permissionResult = validatePermission(props.permission, permissions, props.mode)
        if (!permissionResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 权限验证:', {
            required: props.permission,
            userPermissions: permissions,
            mode: props.mode,
            result: permissionResult
          })
        }
      }
      
      // 角色验证
      if (result && props.role) {
        const roleResult = validateRole(props.role, user.user_type)
        if (!roleResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 角色验证:', {
            required: props.role,
            userRole: user.user_type,
            result: roleResult
          })
        }
      }
      
      // 用户类型验证
      if (result && props.userType) {
        const userTypeResult = validateUserType(props.userType, user.user_type)
        if (!userTypeResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 用户类型验证:', {
            required: props.userType,
            userType: user.user_type,
            result: userTypeResult
          })
        }
      }
      
      // 公司类型验证
      if (result && props.companyType) {
        const companyTypeResult = validateCompanyType(props.companyType, user.company_type)
        if (!companyTypeResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 公司类型验证:', {
            required: props.companyType,
            userCompanyType: user.company_type,
            result: companyTypeResult
          })
        }
      }
      
      // 权限等级验证
      if (result && props.permissionLevel) {
        const levelResult = validatePermissionLevel(props.permissionLevel, user.permission_level)
        if (!levelResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 权限等级验证:', {
            required: props.permissionLevel,
            userLevel: user.permission_level,
            result: levelResult
          })
        }
      }
      
      // 资源所有权验证
      if (result && props.resourceOwnerId) {
        const ownershipResult = validateResourceOwnership(
          props.resourceOwnerId, 
          user.id, 
          user, 
          props.allowCrossCompany
        )
        if (!ownershipResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 资源所有权验证:', {
            resourceOwnerId: props.resourceOwnerId,
            userId: user.id,
            allowCrossCompany: props.allowCrossCompany,
            result: ownershipResult
          })
        }
      }
      
      // 自定义验证
      if (result && props.customValidator) {
        const customResult = props.customValidator(user, permissions)
        if (!customResult) result = false
        
        if (props.debug) {
          console.log('[PermissionGate] 自定义验证:', {
            result: customResult
          })
        }
      }
      
      // 返回最终结果（考虑反转）
      const finalResult = props.reverse ? !result : result
      
      if (props.debug) {
        console.log('[PermissionGate] 最终结果:', {
          baseResult: result,
          reverse: props.reverse,
          finalResult: finalResult
        })
      }
      
      return finalResult
    })
    
    return {
      hasAccess,
      currentUser,
      userPermissions
    }
  }
}
</script>

<style scoped>
/* 权限控制组件本身不添加样式，由父组件控制 */
</style>