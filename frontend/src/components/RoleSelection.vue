<template>
  <div class="role-selection">
    <!-- 角色网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div 
        v-for="role in roles" 
        :key="role.code"
        :class="[
          'role-card border-2 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-lg',
          selectedRole?.code === role.code 
            ? 'border-primary-500 bg-primary-50 shadow-md' 
            : 'border-gray-200 hover:border-gray-300'
        ]"
        @click="selectRole(role)"
      >
        <!-- 角色图标 -->
        <div class="flex items-center justify-center mb-4">
          <div 
            :class="['w-16 h-16 rounded-xl flex items-center justify-center role-icon']"
            :style="{ 
              backgroundColor: role.theme?.primaryColor + '20' || '#f3f4f6',
              color: role.theme?.primaryColor || '#6b7280'
            }"
          >
            <!-- 动态图标组件 -->
            <component 
              :is="getIconComponent(role.icon)" 
              :size="24"
              class="role-icon-svg"
            />
          </div>
        </div>

        <!-- 角色信息 -->
        <div class="text-center">
          <h3 class="font-semibold text-gray-800 text-lg mb-2">{{ role.name }}</h3>
          <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ role.description }}</p>
          
          <!-- 角色标签 -->
          <div class="flex flex-wrap gap-2 justify-center mb-4">
            <!-- 分类标签 -->
            <el-tag 
              :type="getCategoryTagType(role.category)" 
              size="small"
              effect="light"
            >
              {{ getCategoryText(role.category) }}
            </el-tag>
            
            <!-- 业务类型标签 -->
            <el-tag 
              :type="getBusinessTypeTagType(role.businessType)" 
              size="small"
              effect="light"
            >
              {{ getBusinessTypeText(role.businessType) }}
            </el-tag>
          </div>

          <!-- 特色功能 -->
          <div v-if="role.quickActions && role.quickActions.length" class="text-xs text-gray-500">
            <p class="mb-1">主要功能：</p>
            <div class="flex flex-wrap gap-1 justify-center">
              <span 
                v-for="action in role.quickActions.slice(0, 3)" 
                :key="action.action"
                class="px-2 py-1 bg-gray-100 rounded-full"
              >
                {{ action.name }}
              </span>
            </div>
          </div>

          <!-- 选中指示器 -->
          <div v-if="selectedRole?.code === role.code" class="mt-4">
            <div class="flex items-center justify-center">
              <el-icon class="text-primary-500" :size="20">
                <CircleCheck />
              </el-icon>
              <span class="ml-2 text-sm text-primary-600 font-medium">已选择</span>
            </div>
          </div>
        </div>

        <!-- 悬停效果 -->
        <div 
          :class="[
            'absolute inset-0 rounded-xl opacity-0 transition-opacity duration-200 pointer-events-none',
            'bg-gradient-to-br from-transparent to-black/5'
          ]"
          :style="{ 
            background: selectedRole?.code === role.code 
              ? `linear-gradient(135deg, ${role.theme?.primaryColor}10, ${role.theme?.primaryColor}20)` 
              : ''
          }"
        ></div>
      </div>
    </div>

    <!-- 角色详情弹窗 -->
    <el-dialog
      v-model="showRoleDetail"
      :title="selectedRoleDetail?.name"
      width="600px"
      :before-close="closeRoleDetail"
    >
      <div v-if="selectedRoleDetail" class="space-y-4">
        <!-- 角色基本信息 -->
        <div class="flex items-start space-x-4">
          <div 
            class="w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0"
            :style="{ 
              backgroundColor: selectedRoleDetail.theme?.primaryColor + '20' || '#f3f4f6',
              color: selectedRoleDetail.theme?.primaryColor || '#6b7280'
            }"
          >
            <component 
              :is="getIconComponent(selectedRoleDetail.icon)" 
              :size="24"
            />
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ selectedRoleDetail.name }}</h3>
            <p class="text-gray-600 mb-3">{{ selectedRoleDetail.description }}</p>
            <div class="flex gap-2">
              <el-tag :type="getCategoryTagType(selectedRoleDetail.category)" size="small">
                {{ getCategoryText(selectedRoleDetail.category) }}
              </el-tag>
              <el-tag :type="getBusinessTypeTagType(selectedRoleDetail.businessType)" size="small">
                {{ getBusinessTypeText(selectedRoleDetail.businessType) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 主要功能 -->
        <div v-if="selectedRoleDetail.quickActions?.length">
          <h4 class="font-medium text-gray-800 mb-2">主要功能</h4>
          <div class="grid grid-cols-2 gap-2">
            <div 
              v-for="action in selectedRoleDetail.quickActions" 
              :key="action.action"
              class="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
            >
              <el-icon class="text-gray-500">
                <component :is="getIconComponent(action.icon)" />
              </el-icon>
              <span class="text-sm text-gray-700">{{ action.name }}</span>
            </div>
          </div>
        </div>

        <!-- 导航菜单预览 -->
        <div v-if="selectedRoleDetail.navigation?.length">
          <h4 class="font-medium text-gray-800 mb-2">工作台导航</h4>
          <div class="space-y-1">
            <div 
              v-for="nav in selectedRoleDetail.navigation" 
              :key="nav.path"
              class="flex items-center space-x-2 p-2 bg-gray-50 rounded text-sm"
            >
              <el-icon class="text-gray-500">
                <component :is="getIconComponent(nav.icon)" />
              </el-icon>
              <span class="text-gray-700">{{ nav.name }}</span>
            </div>
          </div>
        </div>

        <!-- 业务说明 -->
        <div class="bg-blue-50 p-4 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">适合人群</h4>
          <p class="text-sm text-blue-700">
            {{ getRoleTargetAudience(selectedRoleDetail) }}
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <el-button @click="closeRoleDetail">
            关闭
          </el-button>
          <el-button 
            type="primary"
            @click="selectRoleFromDetail"
          >
            选择此角色
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 帮助信息 -->
    <div v-if="showHelp" class="mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="flex items-start space-x-3">
        <el-icon class="text-blue-500 mt-0.5">
          <InfoFilled />
        </el-icon>
        <div class="flex-1">
          <h4 class="font-medium text-blue-800 mb-1">选择说明</h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• <strong>供应商</strong>：专注销售航材产品，不参与采购</li>
            <li>• <strong>分销商</strong>：既可采购又可销售，是供应链的重要中间环节</li>
            <li>• <strong>航空公司</strong>：主要采购航材用于维修和运营</li>
            <li>• <strong>内部员工</strong>：平台工作人员，需要邀请码注册</li>
          </ul>
        </div>
        <el-button 
          text 
          type="primary" 
          @click="showHelp = false"
          class="flex-shrink-0"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 显示帮助按钮 -->
    <div class="text-center mt-4" v-if="!showHelp">
      <el-button text type="primary" @click="showHelp = true">
        <el-icon><QuestionFilled /></el-icon>
        <span class="ml-1">角色选择说明</span>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  roles: {
    type: Array,
    default: () => []
  },
  selectedRole: {
    type: Object,
    default: null
  },
  showDetail: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['role-selected', 'role-detail'])

// 响应式数据
const showRoleDetail = ref(false)
const selectedRoleDetail = ref(null)
const showHelp = ref(false)

// 方法
const selectRole = (role) => {
  emit('role-selected', role)
  
  // 如果启用详情模式，显示详情弹窗
  if (props.showDetail) {
    selectedRoleDetail.value = role
    showRoleDetail.value = true
  }
}

const selectRoleFromDetail = () => {
  if (selectedRoleDetail.value) {
    emit('role-selected', selectedRoleDetail.value)
    closeRoleDetail()
  }
}

const closeRoleDetail = () => {
  showRoleDetail.value = false
  selectedRoleDetail.value = null
}

// 图标组件映射
const getIconComponent = (iconName) => {
  // Element Plus 图标映射
  const iconMap = {
    Factory: 'OfficeBuilding',
    TruckDelivery: 'Truck', 
    Airplane: 'WindPower',
    UserGroup: 'UserFilled',
    Wrench: 'Tools',
    Truck: 'Truck',
    Settings: 'Setting',
    Dashboard: 'Monitor',
    Package: 'Box', 
    Share: 'Share',
    Search: 'Search',
    Document: 'Document',
    Support: 'Service',
    Plus: 'Plus'
  }
  
  return iconMap[iconName] || 'User'
}

// 分类标签类型
const getCategoryTagType = (category) => {
  return category === 'internal' ? 'warning' : 'info'
}

const getCategoryText = (category) => {
  return category === 'internal' ? '内部员工' : '外部用户'
}

// 业务类型标签
const getBusinessTypeTagType = (businessType) => {
  const typeMap = {
    sell_only: 'success',
    buy_only: 'primary', 
    buy_and_sell: '',
    service: 'warning'
  }
  return typeMap[businessType] || ''
}

const getBusinessTypeText = (businessType) => {
  const textMap = {
    sell_only: '只销售',
    buy_only: '只采购',
    buy_and_sell: '买卖双向',
    service: '服务支持'
  }
  return textMap[businessType] || businessType
}

// 获取角色适合人群描述
const getRoleTargetAudience = (role) => {
  const audienceMap = {
    supplier: '适合航材制造商、OEM厂商等专注于生产和销售航材的企业',
    distributor: '适合航材贸易公司、代理商、经销商等需要采购和销售的中间商',
    airline_buyer: '适合各大航空公司采购部门，专注于航材采购和库存管理',
    platform_staff: '适合平台运营人员，负责客户服务、订单处理、业务支持等工作',
    maintenance_engineer: '适合维修工程师，提供技术支持、维修指导、质量控制等服务',
    logistics_specialist: '适合物流专员，负责货运跟踪、配送管理、物流优化等工作',
    admin: '适合系统管理员，负责用户管理、权限配置、系统维护等管理工作'
  }
  return audienceMap[role.code] || '请联系管理员了解详细信息'
}
</script>

<style scoped>
.role-selection {
  /* 容器样式 */
}

.role-card {
  position: relative;
  transition: all 0.3s ease;
  min-height: 280px;
}

.role-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.role-icon {
  transition: all 0.3s ease;
}

.role-card:hover .role-icon {
  transform: scale(1.1);
}

.role-icon-svg {
  transition: all 0.3s ease;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 选中状态动画 */
.role-card.selected {
  animation: selectedPulse 0.5s ease-out;
}

@keyframes selectedPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .role-card {
    min-height: 240px;
  }
}

/* 帮助信息样式 */
.help-info {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>