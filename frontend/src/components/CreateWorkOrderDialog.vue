<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建维修工单"
    width="800px"
    :before-close="handleClose"
    class="create-work-order-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="work-order-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <div class="form-row">
          <el-form-item label="飞机号" prop="aircraft_tail" class="form-item">
            <el-input v-model="form.aircraft_tail" placeholder="请输入飞机号" />
          </el-form-item>
          <el-form-item label="机型" prop="aircraft_type" class="form-item">
            <el-select v-model="form.aircraft_type" placeholder="请选择机型">
              <el-option label="A320" value="A320" />
              <el-option label="A321" value="A321" />
              <el-option label="B737" value="B737" />
              <el-option label="B777" value="B777" />
              <el-option label="B787" value="B787" />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="优先级" prop="priority" class="form-item">
            <el-select v-model="form.priority" placeholder="请选择优先级">
              <el-option label="AOG(停机)" value="aog" />
              <el-option label="高优先级" value="high" />
              <el-option label="普通" value="normal" />
              <el-option label="低优先级" value="low" />
            </el-select>
          </el-form-item>
          <el-form-item label="航空公司" prop="airline" class="form-item">
            <el-input v-model="form.airline" placeholder="请输入航空公司" />
          </el-form-item>
        </div>
      </div>

      <!-- 故障信息 -->
      <div class="form-section">
        <h3 class="section-title">故障信息</h3>
        <el-form-item label="故障标题" prop="fault_title">
          <el-input v-model="form.fault_title" placeholder="请输入故障标题" />
        </el-form-item>
        <div class="form-row">
          <el-form-item label="故障类别" prop="fault_category" class="form-item">
            <el-select v-model="form.fault_category" placeholder="请选择故障类别">
              <el-option label="起落架系统" value="landing_gear" />
              <el-option label="发动机系统" value="engine" />
              <el-option label="航电系统" value="avionics" />
              <el-option label="液压系统" value="hydraulic" />
              <el-option label="燃油系统" value="fuel" />
              <el-option label="客舱系统" value="cabin" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="严重程度" prop="severity" class="form-item">
            <el-select v-model="form.severity" placeholder="请选择严重程度">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="严重" value="critical" />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="故障描述" prop="fault_description">
          <el-input
            v-model="form.fault_description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述故障现象、发生时间、影响范围等"
          />
        </el-form-item>
      </div>

      <!-- 技师分配 -->
      <div class="form-section">
        <h3 class="section-title">技师分配</h3>
        <div class="form-row">
          <el-form-item label="分配技师" prop="assigned_technician" class="form-item">
            <el-select v-model="form.assigned_technician" placeholder="请选择技师" clearable>
              <el-option
                v-for="technician in availableTechnicians"
                :key="technician.id"
                :label="`${technician.name} (${technician.specialty})`"
                :value="technician.id"
                :disabled="technician.availability !== 'available'"
              >
                <div class="technician-option">
                  <span>{{ technician.name }}</span>
                  <span class="technician-info">{{ technician.specialty }} - {{ technician.level }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="预计工时" prop="estimated_hours" class="form-item">
            <el-input-number
              v-model="form.estimated_hours"
              :min="0.5"
              :max="200"
              :step="0.5"
              placeholder="预计工时"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 时间计划 -->
      <div class="form-section">
        <h3 class="section-title">时间计划</h3>
        <div class="form-row">
          <el-form-item label="计划开始" prop="planned_start" class="form-item">
            <el-date-picker
              v-model="form.planned_start"
              type="datetime"
              placeholder="请选择计划开始时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="预计完成" prop="estimated_completion" class="form-item">
            <el-date-picker
              v-model="form.estimated_completion"
              type="datetime"
              placeholder="请选择预计完成时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <h3 class="section-title">其他信息</h3>
        <el-form-item label="工作地点" prop="work_location">
          <el-input v-model="form.work_location" placeholder="请输入工作地点，如机库、停机坪等" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建工单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { createWorkOrder, getTechnicians } from '@/api/maintenance'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)
const submitting = ref(false)
const availableTechnicians = ref([])

// 表单数据
const form = reactive({
  aircraft_tail: '',
  aircraft_type: '',
  priority: 'normal',
  airline: '',
  fault_title: '',
  fault_category: '',
  severity: 'medium',
  fault_description: '',
  assigned_technician: null,
  estimated_hours: 8,
  planned_start: '',
  estimated_completion: '',
  work_location: '',
  notes: ''
})

// 表单验证规则
const rules = {
  aircraft_tail: [
    { required: true, message: '请输入飞机号', trigger: 'blur' },
    { min: 2, max: 20, message: '飞机号长度在2到20个字符', trigger: 'blur' }
  ],
  aircraft_type: [
    { required: true, message: '请选择机型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  airline: [
    { required: true, message: '请输入航空公司', trigger: 'blur' }
  ],
  fault_title: [
    { required: true, message: '请输入故障标题', trigger: 'blur' },
    { min: 5, max: 100, message: '故障标题长度在5到100个字符', trigger: 'blur' }
  ],
  fault_category: [
    { required: true, message: '请选择故障类别', trigger: 'change' }
  ],
  severity: [
    { required: true, message: '请选择严重程度', trigger: 'change' }
  ],
  fault_description: [
    { required: true, message: '请输入故障描述', trigger: 'blur' },
    { min: 10, max: 1000, message: '故障描述长度在10到1000个字符', trigger: 'blur' }
  ],
  estimated_hours: [
    { required: true, message: '请输入预计工时', trigger: 'blur' },
    { type: 'number', min: 0.5, max: 200, message: '预计工时必须在0.5到200小时之间', trigger: 'blur' }
  ],
  planned_start: [
    { required: true, message: '请选择计划开始时间', trigger: 'change' }
  ],
  estimated_completion: [
    { required: true, message: '请选择预计完成时间', trigger: 'change' }
  ],
  work_location: [
    { required: true, message: '请输入工作地点', trigger: 'blur' }
  ]
}

// 生命周期钩子
onMounted(() => {
  loadTechnicians()
})

// 加载技师列表
const loadTechnicians = async () => {
  try {
    const response = await getTechnicians()
    if (response.success) {
      availableTechnicians.value = response.body.technicians
    }
  } catch (error) {
    console.error('加载技师列表失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 验证时间逻辑
    if (new Date(form.planned_start) >= new Date(form.estimated_completion)) {
      ElMessage.error('预计完成时间必须晚于计划开始时间')
      return
    }

    const workOrderData = {
      aircraft_info: {
        tail_number: form.aircraft_tail,
        aircraft_type: form.aircraft_type,
        airline: form.airline
      },
      fault_info: {
        title: form.fault_title,
        category: form.fault_category,
        severity: form.severity,
        description: form.fault_description
      },
      priority: form.priority,
      assigned_technician_id: form.assigned_technician,
      estimated_hours: form.estimated_hours,
      planned_start: form.planned_start,
      estimated_completion: form.estimated_completion,
      work_location: form.work_location,
      notes: form.notes
    }

    const response = await createWorkOrder(workOrderData)
    
    if (response.success) {
      ElMessage.success('工单创建成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建工单失败:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    aircraft_tail: '',
    aircraft_type: '',
    priority: 'normal',
    airline: '',
    fault_title: '',
    fault_category: '',
    severity: 'medium',
    fault_description: '',
    assigned_technician: null,
    estimated_hours: 8,
    planned_start: '',
    estimated_completion: '',
    work_location: '',
    notes: ''
  })
}
</script>

<style scoped>
.create-work-order-dialog {
  --el-dialog-content-font-size: 14px;
}

.work-order-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.technician-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.technician-info {
  font-size: 12px;
  color: #666;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.work-order-form::-webkit-scrollbar {
  width: 6px;
}

.work-order-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.work-order-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.work-order-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>