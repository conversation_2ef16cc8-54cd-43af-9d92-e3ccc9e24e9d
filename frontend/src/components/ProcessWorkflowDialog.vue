<template>
  <el-dialog
    v-model="visible"
    title="处理工作流"
    width="50%"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 当前步骤信息 -->
      <div class="current-step-info mb-6">
        <h3 class="text-lg font-semibold mb-3">当前步骤信息</h3>
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="text-gray-600">步骤名称:</span>
              <span class="ml-2 font-medium">{{ stepInfoFilled.name }}</span>
            </div>
            <div>
              <span class="text-gray-600">审批人:</span>
              <span class="ml-2 font-medium">{{ stepInfoFilled.assignee }}</span>
            </div>
            <div>
              <span class="text-gray-600">到达时间:</span>
              <span class="ml-2">{{ formatDateTime(stepInfoFilled.arrived_at) }}</span>
            </div>
            <div>
              <span class="text-gray-600">超时时间:</span>
              <span class="ml-2">{{ stepInfoFilled.timeout || '无限制' }}</span>
            </div>
          </div>
          <div v-if="stepInfoFilled.description" class="mt-3">
            <span class="text-gray-600">步骤描述:</span>
            <div class="mt-1 text-gray-700">{{ stepInfoFilled.description }}</div>
          </div>
        </div>
      </div>

      <!-- 处理表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="处理动作" prop="action">
          <el-radio-group v-model="form.action">
            <el-radio value="approve">
              <span class="text-green-600">✓ 批准</span>
            </el-radio>
            <el-radio value="reject">
              <span class="text-red-600">✗ 拒绝</span>
            </el-radio>
            <el-radio value="transfer">
              <span class="text-blue-600">→ 转交</span>
            </el-radio>
            <el-radio value="return">
              <span class="text-orange-600">↩ 退回</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="form.action === 'transfer'" 
          label="转交给" 
          prop="assignee"
        >
          <el-select 
            v-model="form.assignee" 
            placeholder="选择转交对象" 
            style="width: 100%"
            filterable
          >
            <el-option 
              v-for="user in availableUsers" 
              :key="user.id"
              :label="user.name"
              :value="user.id"
            >
              <span>{{ user.name }}</span>
              <span class="text-gray-500 ml-2">{{ user.department }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item 
          v-if="form.action === 'return'" 
          label="退回到" 
          prop="return_step"
        >
          <el-select 
            v-model="form.return_step" 
            placeholder="选择退回步骤" 
            style="width: 100%"
          >
            <el-option 
              v-for="step in previousSteps" 
              :key="step.id"
              :label="step.name"
              :value="step.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="处理意见" prop="comment">
          <el-input 
            v-model="form.comment" 
            type="textarea" 
            :rows="4"
            :placeholder="getCommentPlaceholder()"
          />
        </el-form-item>

        <el-form-item label="优先级调整">
          <el-select v-model="form.priority" placeholder="可调整优先级" style="width: 200px">
            <el-option label="保持原优先级" value="" />
            <el-option label="低" value="low" />
            <el-option label="普通" value="normal" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="附件">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :auto-upload="false"
            multiple
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          >
            <el-button>上传附件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、图片格式，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <!-- 预估处理时间 -->
      <div class="estimated-time bg-gray-50 p-4 rounded-lg mt-4">
        <div class="flex items-center justify-between">
          <span class="text-gray-600">预估后续处理时间:</span>
          <span class="font-medium text-blue-600">{{ estimatedTime }}</span>
        </div>
        <div class="text-sm text-gray-500 mt-1">
          根据历史数据和当前流程预估
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveAsDraft" :loading="saving">保存草稿</el-button>
        <el-button 
          type="primary" 
          @click="submitForm" 
          :loading="submitting"
          :class="getSubmitButtonClass()"
        >
          {{ getSubmitButtonText() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { workflowApi } from '@/api/workflow'

const props = defineProps({
  modelValue: Boolean,
  workflowId: String,
  stepId: String
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)
const formRef = ref()
const uploadRef = ref()
const fileList = ref([])

// 步骤信息
const stepInfoFilled = ref({
  name: '',
  assignee: '',
  arrived_at: '',
  timeout: '',
  description: ''
})

// 表单数据
const form = reactive({
  action: 'approve',
  assignee: '',
  return_step: '',
  comment: '',
  priority: ''
})

// 可用用户列表
const availableUsers = ref([
  { id: 'user1', name: '王经理', department: '采购部' },
  { id: 'user2', name: '李总监', department: '财务部' },
  { id: 'user3', name: '张主管', department: '技术部' },
  { id: 'user4', name: '陈经理', department: '质量部' }
])

// 可退回的步骤
const previousSteps = ref([
  { id: 'step1', name: '发起申请' },
  { id: 'step2', name: '部门初审' }
])

// 验证规则
const rules = {
  action: [
    { required: true, message: '请选择处理动作', trigger: 'change' }
  ],
  assignee: [
    { required: true, message: '请选择转交对象', trigger: 'change' }
  ],
  return_step: [
    { required: true, message: '请选择退回步骤', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入处理意见', trigger: 'blur' },
    { min: 5, message: '处理意见至少5个字符', trigger: 'blur' }
  ]
}

// 计算属性
const estimatedTime = computed(() => {
  switch (form.action) {
    case 'approve':
      return '2-4小时'
    case 'reject':
      return '立即完成'
    case 'transfer':
      return '4-8小时'
    case 'return':
      return '1-2小时'
    default:
      return '未知'
  }
})

// 模拟数据
const initMockData = () => {
  stepInfoFilled.value = {
    name: '部门经理审批',
    assignee: '李经理',
    arrived_at: '2025-01-13 09:05:00',
    timeout: '24小时',
    description: '审核采购申请的合理性和预算符合性'
  }
}

// 加载步骤信息
const loadStepInfoFilled = async () => {
  if (!props.workflowId || !props.stepId) return
  
  try {
    loading.value = true
    // const response = await workflowApi.getWorkflowStatus(props.workflowId)
    // stepInfoFilled.value = response.body.current_step
    
    // 使用模拟数据
    initMockData()
  } catch (error) {
    ElMessage.error('加载步骤信息失败')
  } finally {
    loading.value = false
  }
}

// 方法
const getCommentPlaceholder = () => {
  switch (form.action) {
    case 'approve':
      return '请输入批准意见...'
    case 'reject':
      return '请输入拒绝原因...'
    case 'transfer':
      return '请输入转交原因...'
    case 'return':
      return '请输入退回原因...'
    default:
      return '请输入处理意见...'
  }
}

const getSubmitButtonText = () => {
  switch (form.action) {
    case 'approve':
      return '批准'
    case 'reject':
      return '拒绝'
    case 'transfer':
      return '转交'
    case 'return':
      return '退回'
    default:
      return '提交'
  }
}

const getSubmitButtonClass = () => {
  switch (form.action) {
    case 'approve':
      return 'submit-approve'
    case 'reject':
      return 'submit-reject'
    case 'transfer':
      return 'submit-transfer'
    case 'return':
      return 'submit-return'
    default:
      return ''
  }
}

const resetForm = () => {
  Object.keys(form).forEach(key => {
    form[key] = key === 'action' ? 'approve' : ''
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

const saveAsDraft = async () => {
  try {
    saving.value = true
    // 保存草稿到本地存储
    const draftKey = `workflow_process_draft_${props.workflowId}_${props.stepId}`
    localStorage.setItem(draftKey, JSON.stringify(form))
    ElMessage.success('草稿已保存')
  } catch (error) {
    ElMessage.error('保存草稿失败')
  } finally {
    saving.value = false
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 确认操作
    const confirmMessage = `确定要${getSubmitButtonText()}此工作流吗？`
    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      type: form.action === 'reject' ? 'warning' : 'info'
    })
    
    submitting.value = true
    
    // 准备提交数据
    const actionData = {
      action: form.action,
      comment: form.comment,
      assignee: form.assignee,
      return_step: form.return_step,
      priority: form.priority,
      attachments: fileList.value.map(file => ({
        name: file.name,
        size: file.size,
        type: file.raw?.type
      }))
    }
    
    // 调用API处理工作流
    // await workflowApi.processWorkflowStep(props.workflowId, props.stepId, actionData)
    
    ElMessage.success(`工作流${getSubmitButtonText()}成功`)
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel' && error !== 'validation failed') {
      ElMessage.error('处理失败')
    }
  } finally {
    submitting.value = false
  }
}

const formatDateTime = (datetime) => {
  return datetime ? new Date(datetime).toLocaleString('zh-CN') : ''
}

const handleClose = () => {
  resetForm()
  visible.value = false
}

// 监听dialog显示
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.workflowId && props.stepId) {
    loadStepInfoFilled()
    
    // 尝试加载草稿
    const draftKey = `workflow_process_draft_${props.workflowId}_${props.stepId}`
    const draft = localStorage.getItem(draftKey)
    if (draft) {
      try {
        const draftData = JSON.parse(draft)
        Object.assign(form, draftData)
      } catch (error) {
        // 忽略草稿加载错误
      }
    }
  }
})
</script>

<style scoped>
.current-step-info {
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  overflow: hidden;
}

.estimated-time {
  border-left: 4px solid #409eff;
}

.dialog-footer {
  text-align: right;
}

:deep(.submit-approve) {
  background-color: #67c23a;
  border-color: #67c23a;
}

:deep(.submit-reject) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.submit-transfer) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.submit-return) {
  background-color: #e6a23c;
  border-color: #e6a23c;
}
</style>