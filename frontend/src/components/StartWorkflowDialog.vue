<template>
  <el-dialog
    v-model="visible"
    title="启动工作流"
    width="50%"
    :before-close="handleClose"
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="工作流类型" prop="workflow_type">
        <el-select v-model="form.workflow_type" placeholder="选择工作流类型" style="width: 100%">
          <el-option label="采购订单审批" value="purchase_order" />
          <el-option label="销售合同审批" value="sales_contract" />
          <el-option label="AOG需求审批" value="aog_approval" />
          <el-option label="费用报销审批" value="expense_approval" />
          <el-option label="请假申请" value="leave_request" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联对象类型" prop="object_type">
        <el-select v-model="form.object_type" placeholder="选择对象类型" style="width: 100%">
          <el-option label="采购订单" value="purchase_order" />
          <el-option label="销售订单" value="sales_order" />
          <el-option label="需求申请" value="demand" />
          <el-option label="费用单据" value="expense" />
          <el-option label="请假单" value="leave" />
        </el-select>
      </el-form-item>

      <el-form-item label="对象ID" prop="object_id">
        <el-input v-model="form.object_id" placeholder="输入关联对象的ID" />
      </el-form-item>

      <el-form-item label="工作流标题" prop="title">
        <el-input v-model="form.title" placeholder="输入工作流标题" />
      </el-form-item>

      <el-form-item label="紧急程度" prop="priority">
        <el-radio-group v-model="form.priority">
          <el-radio value="low">低</el-radio>
          <el-radio value="normal">普通</el-radio>
          <el-radio value="high">高</el-radio>
          <el-radio value="urgent">紧急</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述说明" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="4"
          placeholder="描述启动此工作流的原因和相关信息"
        />
      </el-form-item>

      <el-form-item label="附加数据">
        <el-input 
          v-model="form.additional_data" 
          type="textarea" 
          :rows="3"
          placeholder="JSON格式的附加数据（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          启动工作流
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { workflowApi } from '@/api/workflow'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  workflow_type: '',
  object_type: '',
  object_id: '',
  title: '',
  priority: 'normal',
  description: '',
  additional_data: ''
})

// 验证规则
const rules = {
  workflow_type: [
    { required: true, message: '请选择工作流类型', trigger: 'change' }
  ],
  object_type: [
    { required: true, message: '请选择对象类型', trigger: 'change' }
  ],
  object_id: [
    { required: true, message: '请输入对象ID', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入工作流标题', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入描述说明', trigger: 'blur' }
  ]
}

// 方法
const resetForm = () => {
  Object.keys(form).forEach(key => {
    form[key] = key === 'priority' ? 'normal' : ''
  })
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 准备提交数据
    const submitData = {
      workflow_type: form.workflow_type,
      object_id: form.object_id,
      object_type: form.object_type,
      initiator_id: 'current_user_id', // 实际项目中从用户状态获取
      data: {
        title: form.title,
        priority: form.priority,
        description: form.description,
        additional_data: form.additional_data ? JSON.parse(form.additional_data) : {}
      }
    }
    
    // 调用API启动工作流
    // const response = await workflowApi.startWorkflow(submitData)
    
    ElMessage.success('工作流启动成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('启动工作流失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  resetForm()
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>