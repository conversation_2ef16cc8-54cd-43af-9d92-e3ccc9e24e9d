<template>
  <el-dialog
    v-model="dialogVisible"
    title="维修计划详情"
    width="1000px"
    :before-close="handleClose"
    class="schedule-detail-dialog"
  >
    <div v-if="scheduleDetail" class="schedule-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">计划编号：</span>
            <span class="value">{{ scheduleDetail.schedule_id }}</span>
          </div>
          <div class="info-item">
            <span class="label">飞机号：</span>
            <span class="value">{{ scheduleDetail.aircraft_tail }}</span>
          </div>
          <div class="info-item">
            <span class="label">机型：</span>
            <span class="value">{{ scheduleDetail.aircraft_type }}</span>
          </div>
          <div class="info-item">
            <span class="label">维修类型：</span>
            <span class="value">{{ scheduleDetail.maintenance_type }}</span>
          </div>
          <div class="info-item">
            <span class="label">优先级：</span>
            <el-tag :type="getPriorityTagType(scheduleDetail.priority)">
              {{ getPriorityLabel(scheduleDetail.priority) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">状态：</span>
            <el-tag :type="getStatusTagType(scheduleDetail.status)">
              {{ getStatusLabel(scheduleDetail.status) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 时间安排 -->
      <div class="detail-section">
        <h3 class="section-title">时间安排</h3>
        <div class="time-info">
          <div class="time-item">
            <span class="label">计划开始：</span>
            <span class="value">{{ formatDateTime(scheduleDetail.scheduled_start) }}</span>
          </div>
          <div class="time-item">
            <span class="label">预计完成：</span>
            <span class="value">{{ formatDateTime(scheduleDetail.estimated_end) }}</span>
          </div>
          <div class="time-item">
            <span class="label">预计工期：</span>
            <span class="value">{{ scheduleDetail.estimated_duration }}小时</span>
          </div>
          <div class="time-item">
            <span class="label">缓冲时间：</span>
            <span class="value">{{ scheduleDetail.buffer_time }}小时</span>
          </div>
        </div>
        
        <!-- 时间进度条 -->
        <div class="time-progress">
          <div class="progress-info">
            <span class="progress-label">计划进度</span>
            <span class="progress-text">{{ getProgressText() }}</span>
          </div>
          <el-progress
            :percentage="getTimeProgress()"
            :color="getProgressColor()"
            :stroke-width="8"
          />
        </div>
      </div>

      <!-- 资源分配 -->
      <div class="detail-section">
        <h3 class="section-title">资源分配</h3>
        <div class="resource-grid">
          <div class="resource-card">
            <div class="resource-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="resource-content">
              <div class="resource-title">机库</div>
              <div class="resource-value">{{ scheduleDetail.hangar_name }}</div>
            </div>
          </div>
          
          <div class="resource-card">
            <div class="resource-icon">
              <el-icon><Location /></el-icon>
            </div>
            <div class="resource-content">
              <div class="resource-title">机位</div>
              <div class="resource-value">{{ scheduleDetail.bay }}</div>
            </div>
          </div>
          
          <div class="resource-card">
            <div class="resource-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="resource-content">
              <div class="resource-title">维修团队</div>
              <div class="resource-value">{{ scheduleDetail.assigned_team_name }}</div>
            </div>
          </div>
          
          <div class="resource-card">
            <div class="resource-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="resource-content">
              <div class="resource-title">技师需求</div>
              <div class="resource-value">{{ scheduleDetail.technician_count }}人</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 维修项目 -->
      <div class="detail-section">
        <h3 class="section-title">维修项目</h3>
        <div class="maintenance-items">
          <el-table :data="scheduleDetail.maintenance_items || []" stripe>
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="name" label="项目名称" min-width="200" />
            <el-table-column prop="category" label="类别" width="120">
              <template #default="scope">
                <el-tag size="small" :type="getCategoryTagType(scope.row.category)">
                  {{ getCategoryLabel(scope.row.category) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="estimated_hours" label="预计工时" width="100">
              <template #default="scope">
                {{ scope.row.estimated_hours }}h
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag size="small" :type="getItemStatusTagType(scope.row.status)">
                  {{ getItemStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.progress || 0"
                  :color="getProgressColor()"
                  :stroke-width="6"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 质量要求 -->
      <div class="detail-section">
        <h3 class="section-title">质量要求</h3>
        <div class="quality-info">
          <div class="info-item">
            <span class="label">质量标准：</span>
            <span class="value">{{ getQualityStandardLabel(scheduleDetail.quality_standard) }}</span>
          </div>
          <div class="info-item">
            <span class="label">检查方式：</span>
            <span class="value">{{ getInspectionMethodLabel(scheduleDetail.inspection_method) }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">质量检查点：</span>
            <p class="value description">{{ scheduleDetail.quality_checkpoints }}</p>
          </div>
        </div>
      </div>

      <!-- 成本信息 -->
      <div class="detail-section">
        <h3 class="section-title">成本信息</h3>
        <div class="cost-analysis">
          <div class="cost-chart">
            <div class="chart-placeholder">
              📊 成本分析图表
            </div>
          </div>
          <div class="cost-breakdown">
            <div class="cost-item">
              <span class="cost-label">人工成本</span>
              <span class="cost-value">¥{{ formatCurrency(scheduleDetail.labor_cost) }}</span>
            </div>
            <div class="cost-item">
              <span class="cost-label">材料成本</span>
              <span class="cost-value">¥{{ formatCurrency(scheduleDetail.material_cost) }}</span>
            </div>
            <div class="cost-item">
              <span class="cost-label">设备成本</span>
              <span class="cost-value">¥{{ formatCurrency(scheduleDetail.equipment_cost) }}</span>
            </div>
            <div class="cost-item total">
              <span class="cost-label">总预算</span>
              <span class="cost-value">¥{{ formatCurrency(scheduleDetail.total_cost) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 维修说明 -->
      <div class="detail-section">
        <h3 class="section-title">维修说明</h3>
        <div class="maintenance-notes">
          <p>{{ scheduleDetail.maintenance_notes || '暂无维修说明' }}</p>
        </div>
      </div>

      <!-- 执行历史 -->
      <div class="detail-section">
        <h3 class="section-title">执行历史</h3>
        <div class="execution-history">
          <el-timeline>
            <el-timeline-item
              v-for="item in executionHistory"
              :key="item.id"
              :timestamp="formatDateTime(item.timestamp)"
              :type="getTimelineType(item.type)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="action">{{ item.action }}</span>
                  <span class="operator">{{ item.operator }}</span>
                </div>
                <div class="timeline-notes">{{ item.notes }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑计划</el-button>
        <el-button type="success" @click="handleStart" v-if="canStart">开始执行</el-button>
        <el-button type="warning" @click="handlePause" v-if="canPause">暂停</el-button>
        <el-button type="info" @click="handleComplete" v-if="canComplete">完成</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  House, Location, User 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  schedule: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh', 'edit'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const scheduleDetail = ref(null)
const executionHistory = ref([])

// 监听计划变化
watch(() => props.schedule, (newSchedule) => {
  if (newSchedule && props.modelValue) {
    loadScheduleDetail(newSchedule)
  }
}, { immediate: true })

// 加载计划详情
const loadScheduleDetail = (schedule) => {
  // 模拟详细数据
  scheduleDetail.value = {
    ...schedule,
    schedule_id: `MS${new Date().getFullYear()}${String(schedule.id).padStart(4, '0')}`,
    hangar_name: '1号机库',
    assigned_team_name: '第一维修组',
    maintenance_items: [
      {
        name: '发动机例行检查',
        category: 'system_check',
        estimated_hours: 8,
        status: 'completed',
        progress: 100
      },
      {
        name: '起落架系统检查',
        category: 'system_check',
        estimated_hours: 4,
        status: 'in_progress',
        progress: 65
      },
      {
        name: '航电系统测试',
        category: 'function_test',
        estimated_hours: 6,
        status: 'pending',
        progress: 0
      }
    ]
  }

  // 模拟执行历史
  executionHistory.value = [
    {
      id: 1,
      action: '创建计划',
      operator: '计划员',
      timestamp: '2024-07-15T10:00:00Z',
      type: 'create',
      notes: '维修计划创建完成，等待审批'
    },
    {
      id: 2,
      action: '审批通过',
      operator: '维修主管',
      timestamp: '2024-07-15T14:30:00Z',
      type: 'approve',
      notes: '计划审批通过，准备资源分配'
    },
    {
      id: 3,
      action: '资源分配',
      operator: '调度员',
      timestamp: '2024-07-16T08:00:00Z',
      type: 'assign',
      notes: '分配机库和维修团队，准备开始执行'
    }
  ]
}

// 计算属性
const canStart = computed(() => {
  return scheduleDetail.value?.status === 'scheduled'
})

const canPause = computed(() => {
  return scheduleDetail.value?.status === 'in_progress'
})

const canComplete = computed(() => {
  return scheduleDetail.value?.status === 'in_progress'
})

// 获取时间进度
const getTimeProgress = () => {
  if (!scheduleDetail.value) return 0
  
  const now = new Date()
  const start = new Date(scheduleDetail.value.scheduled_start)
  const end = new Date(scheduleDetail.value.estimated_end)
  
  if (now < start) return 0
  if (now > end) return 100
  
  const total = end - start
  const elapsed = now - start
  
  return Math.round((elapsed / total) * 100)
}

// 获取进度文本
const getProgressText = () => {
  const progress = getTimeProgress()
  if (progress === 0) return '未开始'
  if (progress === 100) return '已超期'
  return `${progress}%`
}

// 事件处理
const handleEdit = () => {
  emit('edit', scheduleDetail.value)
}

const handleStart = () => {
  ElMessage.success('计划开始执行')
  emit('refresh')
}

const handlePause = () => {
  ElMessage.warning('计划已暂停')
  emit('refresh')
}

const handleComplete = () => {
  ElMessage.success('计划执行完成')
  emit('refresh')
}

const handleClose = () => {
  dialogVisible.value = false
  scheduleDetail.value = null
  executionHistory.value = []
}

// 工具函数
const getPriorityTagType = (priority) => {
  const types = {
    aog: 'danger',
    high: 'warning',
    normal: 'info',
    low: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityLabel = (priority) => {
  const labels = {
    aog: 'AOG',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return labels[priority] || priority
}

const getStatusTagType = (status) => {
  const types = {
    scheduled: 'info',
    preparing: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    scheduled: '已计划',
    preparing: '准备中',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getCategoryTagType = (category) => {
  const types = {
    system_check: 'primary',
    part_replacement: 'warning',
    function_test: 'success',
    cleaning: 'info',
    calibration: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryLabel = (category) => {
  const labels = {
    system_check: '系统检查',
    part_replacement: '零件更换',
    function_test: '功能测试',
    cleaning: '清洁保养',
    calibration: '校准调整'
  }
  return labels[category] || category
}

const getItemStatusTagType = (status) => {
  const types = {
    pending: 'info',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getItemStatusLabel = (status) => {
  const labels = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getQualityStandardLabel = (standard) => {
  const labels = {
    caac: 'CAAC标准',
    faa: 'FAA标准',
    easa: 'EASA标准',
    company: '公司标准'
  }
  return labels[standard] || standard
}

const getInspectionMethodLabel = (method) => {
  const labels = {
    visual: '目视检查',
    functional: '功能测试',
    ndt: '无损检测',
    instrument: '仪器检测'
  }
  return labels[method] || method
}

const getTimelineType = (type) => {
  const types = {
    create: 'primary',
    approve: 'success',
    assign: 'warning',
    start: 'info',
    complete: 'success'
  }
  return types[type] || 'primary'
}

const getProgressColor = () => {
  const progress = getTimeProgress()
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN').format(amount || 0)
}
</script>

<style scoped>
.schedule-detail-dialog {
  --el-dialog-content-font-size: 14px;
}

.schedule-detail {
  max-height: 80vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  font-weight: 400;
}

.description {
  margin-top: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  line-height: 1.5;
}

.time-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-progress {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progress-label {
  font-weight: 500;
  color: #333;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.resource-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.resource-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.resource-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.resource-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.maintenance-items {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.quality-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.cost-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.cost-chart {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  color: #9ca3af;
}

.cost-breakdown {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.cost-item.total {
  border-bottom: none;
  border-top: 2px solid #e5e7eb;
  font-weight: bold;
  margin-top: 8px;
}

.cost-label {
  color: #666;
  font-size: 14px;
}

.cost-value {
  color: #333;
  font-weight: 500;
}

.maintenance-notes {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  line-height: 1.6;
}

.execution-history {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  max-height: 300px;
  overflow-y: auto;
}

.timeline-content {
  padding: 4px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.action {
  font-weight: 500;
  color: #333;
}

.operator {
  font-size: 12px;
  color: #666;
}

.timeline-notes {
  font-size: 13px;
  color: #555;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.schedule-detail::-webkit-scrollbar {
  width: 6px;
}

.schedule-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.schedule-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.schedule-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .cost-analysis {
    grid-template-columns: 1fr;
  }
  
  .resource-grid {
    grid-template-columns: 1fr;
  }
}
</style>