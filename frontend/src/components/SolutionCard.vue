<template>
  <div class="modern-card overflow-hidden group">
    <div class="h-64 relative overflow-hidden" :class="`bg-gradient-to-br ${bgColor}`">
      <div class="absolute inset-0 bg-black bg-opacity-20"></div>
      <img v-if="image" :src="image" :alt="title" class="absolute inset-0 w-full h-full object-cover opacity-20" />
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white">
          <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <slot name="icon">
              <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </slot>
          </div>
          <h3 class="subtitle-font text-2xl font-bold">{{ title }}</h3>
          <p class="text-blue-100 mt-2">{{ subtitle }}</p>
        </div>
      </div>
    </div>
    <div class="p-8">
      <h4 class="subtitle-font text-xl text-gray-800 mb-4">智能{{ title.slice(0, 2) }}管理</h4>
      <p class="text-gray-600 mb-6">
        通过智能匹配算法，为航空公司提供高效的{{ title }}，显著降低库存成本和资金占用
      </p>
      <ul class="space-y-3 text-sm text-gray-600 mb-8">
        <li v-for="feature in features" :key="feature" class="flex items-center">
          <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          {{ feature }}
        </li>
      </ul>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500">
          <span class="font-semibold text-primary-600">{{ savings }}</span>
        </div>
        <button class="btn-primary">立即体验</button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: String,
  subtitle: String,
  features: Array,
  savings: String,
  bgColor: String,
  image: String
})
</script>