<template>
  <div class="workflow-table">
    <el-table :data="workflows" v-loading="loading" style="width: 100%">
      <el-table-column prop="id" label="工作流ID" width="140" />
      
      <el-table-column prop="title" label="标题" min-width="200">
        <template #default="{ row }">
          <el-link type="primary" @click="$emit('view', row.id)">
            {{ row.title }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getTypeColor(row.type)" size="small">
            {{ getTypeText(row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="priority" label="优先级" width="80">
        <template #default="{ row }">
          <el-tag :type="getPriorityColor(row.priority)" size="small">
            {{ getPriorityText(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="current_step" label="当前步骤" width="140">
        <template #default="{ row }">
          <span class="text-blue-600">{{ row.current_step }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="initiator" label="发起人" width="100" />

      <el-table-column prop="created_at" label="创建时间" width="150">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ formatDateTime(row.created_at) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="flex space-x-2">
            <el-button link type="primary" size="small" @click="$emit('view', row.id)">
              查看
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending' && canProcess(row)"
              link 
              type="success" 
              size="small" 
              @click="$emit('process', row.id, row.current_step_id)"
            >
              处理
            </el-button>
            
            <el-dropdown v-if="hasMoreActions(row)">
              <el-button link size="small">
                更多
                <ArrowDown class="w-3 h-3 ml-1" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    v-if="canWithdraw(row)" 
                    @click="$emit('withdraw', row.id)"
                  >
                    撤回
                  </el-dropdown-item>
                  <el-dropdown-item @click="viewHistory(row.id)">
                    查看历史
                  </el-dropdown-item>
                  <el-dropdown-item @click="exportWorkflow(row.id)">
                    导出
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-center mt-6" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入

const props = defineProps({
  workflows: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'view', 'process', 'withdraw', 'size-change', 'current-change'
])

// 计算属性
const canProcess = (workflow) => {
  // 判断当前用户是否可以处理此工作流
  return workflow.status === 'pending' && workflow.assignee === 'current_user'
}

const canWithdraw = (workflow) => {
  // 判断是否可以撤回
  return ['pending', 'in_progress'].includes(workflow.status) && 
         workflow.initiator === 'current_user'
}

const hasMoreActions = (workflow) => {
  return canWithdraw(workflow) || true // 总是显示更多菜单（查看历史、导出等）
}

// 方法
const getTypeColor = (type) => {
  const colors = {
    purchase_order: 'primary',
    sales_contract: 'success',
    aog_approval: 'danger',
    expense_approval: 'warning',
    leave_request: 'info'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    purchase_order: '采购审批',
    sales_contract: '销售合同',
    aog_approval: 'AOG审批',
    expense_approval: '费用审批',
    leave_request: '请假申请'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    withdrawn: 'info',
    rejected: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    withdrawn: '已撤回',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString('zh-CN')
}

const viewHistory = (workflowId) => {
  ElMessage.info(`查看工作流历史: ${workflowId}`)
}

const exportWorkflow = (workflowId) => {
  ElMessage.info(`导出工作流: ${workflowId}`)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  emit('current-change', page)
}
</script>

<style scoped>
.workflow-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
}
</style>