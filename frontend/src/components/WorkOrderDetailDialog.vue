<template>
  <el-dialog
    v-model="dialogVisible"
    title="工单详情"
    width="1200px"
    :before-close="handleClose"
    class="work-order-detail-dialog"
  >
    <div v-if="workOrderDetail" class="work-order-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">工单号：</span>
            <span class="value">{{ workOrderDetail.work_order_number }}</span>
          </div>
          <div class="info-item">
            <span class="label">飞机号：</span>
            <span class="value">{{ workOrderDetail.aircraft?.tail_number }}</span>
          </div>
          <div class="info-item">
            <span class="label">机型：</span>
            <span class="value">{{ workOrderDetail.aircraft?.aircraft_type }}</span>
          </div>
          <div class="info-item">
            <span class="label">航空公司：</span>
            <span class="value">{{ workOrderDetail.aircraft?.airline }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态：</span>
            <el-tag :type="getStatusTagType(workOrderDetail.status)">
              {{ getStatusLabel(workOrderDetail.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(workOrderDetail.created_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">预计完成：</span>
            <span class="value">{{ formatDateTime(workOrderDetail.estimated_completion) }}</span>
          </div>
        </div>
      </div>

      <!-- 故障信息 -->
      <div class="detail-section">
        <h3 class="section-title">故障信息</h3>
        <div class="fault-info">
          <div class="info-item">
            <span class="label">故障标题：</span>
            <span class="value">{{ workOrderDetail.fault?.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">故障类别：</span>
            <span class="value">{{ workOrderDetail.fault?.category }}</span>
          </div>
          <div class="info-item">
            <span class="label">严重程度：</span>
            <el-tag :type="getSeverityTagType(workOrderDetail.fault?.severity)">
              {{ getSeverityLabel(workOrderDetail.fault?.severity) }}
            </el-tag>
          </div>
          <div class="info-item full-width">
            <span class="label">故障描述：</span>
            <p class="value description">{{ workOrderDetail.fault?.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技师信息 -->
      <div class="detail-section">
        <h3 class="section-title">技师信息</h3>
        <div class="technician-info">
          <div class="info-item">
            <span class="label">技师姓名：</span>
            <span class="value">{{ workOrderDetail.technician?.name || '未分配' }}</span>
          </div>
          <div class="info-item">
            <span class="label">技师级别：</span>
            <span class="value">{{ workOrderDetail.technician?.level || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">专业领域：</span>
            <span class="value">{{ workOrderDetail.technician?.specialty || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系方式：</span>
            <span class="value">{{ workOrderDetail.technician?.contact || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 进度信息 -->
      <div class="detail-section">
        <h3 class="section-title">进度信息</h3>
        <div class="progress-info">
          <div class="progress-stats">
            <div class="stat-item">
              <span class="label">当前进度：</span>
              <el-progress 
                :percentage="workOrderDetail.progress?.current || 0" 
                :color="getProgressColor(workOrderDetail.progress?.current || 0)"
                :stroke-width="8"
              />
            </div>
            <div class="stat-item">
              <span class="label">预计工时：</span>
              <span class="value">{{ workOrderDetail.progress?.estimated_hours || 0 }}小时</span>
            </div>
            <div class="stat-item">
              <span class="label">实际工时：</span>
              <span class="value">{{ workOrderDetail.progress?.actual_hours || 0 }}小时</span>
            </div>
            <div class="stat-item">
              <span class="label">剩余工时：</span>
              <span class="value">{{ workOrderDetail.progress?.remaining_hours || 0 }}小时</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度历史 -->
      <div class="detail-section">
        <h3 class="section-title">进度历史</h3>
        <div class="progress-history">
          <el-timeline>
            <el-timeline-item
              v-for="item in progressHistory"
              :key="item.id"
              :timestamp="formatDateTime(item.timestamp)"
              :type="getTimelineType(item.status)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="status">{{ getStatusLabel(item.status) }}</span>
                  <span class="operator">{{ item.operator }}</span>
                </div>
                <div class="timeline-notes">{{ item.notes }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 工时记录 -->
      <div class="detail-section">
        <h3 class="section-title">工时记录</h3>
        <div class="labor-records">
          <el-table :data="laborRecords" stripe>
            <el-table-column prop="technician" label="技师" width="120" />
            <el-table-column prop="start_time" label="开始时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="end_time" label="结束时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.end_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="hours" label="工时" width="80">
              <template #default="scope">
                {{ scope.row.hours }}h
              </template>
            </el-table-column>
            <el-table-column prop="description" label="工作内容" min-width="200" />
          </el-table>
        </div>
      </div>

      <!-- 使用零件 -->
      <div class="detail-section">
        <h3 class="section-title">使用零件</h3>
        <div class="parts-used">
          <el-table :data="partsUsed" stripe>
            <el-table-column prop="part_name" label="零件名称" width="200" />
            <el-table-column prop="part_number" label="零件号" width="150" />
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="unit_price" label="单价" width="100">
              <template #default="scope">
                ¥{{ scope.row.unit_price }}
              </template>
            </el-table-column>
            <el-table-column prop="total_price" label="总价" width="100">
              <template #default="scope">
                ¥{{ scope.row.total_price }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑工单</el-button>
        <el-button type="success" @click="handleUpdateProgress">更新进度</el-button>
        <el-button type="warning" @click="handleLaborManagement">
          <template #icon><el-icon><Timer /></el-icon></template>
          工时管理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
// 图标已全局注册，无需单独导入
import { getWorkOrderDetails } from '@/api/maintenance'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workOrder: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh', 'labor-management'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const workOrderDetail = ref(null)
const progressHistory = ref([])
const laborRecords = ref([])
const partsUsed = ref([])

// 监听工单变化
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder && props.modelValue) {
    loadWorkOrderDetails(newWorkOrder.id)
  }
}, { immediate: true })

// 加载工单详情
const loadWorkOrderDetails = async (workOrderId) => {
  try {
    const response = await getWorkOrderDetails(workOrderId)
    if (response.success) {
      workOrderDetail.value = response.body.work_order
      progressHistory.value = response.body.progress_history || []
      laborRecords.value = response.body.labor_records || []
      partsUsed.value = response.body.parts_used || []
    }
  } catch (error) {
    console.error('加载工单详情失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  workOrderDetail.value = null
  progressHistory.value = []
  laborRecords.value = []
  partsUsed.value = []
}

// 编辑工单
const handleEdit = () => {
  // 实现编辑逻辑
  console.log('编辑工单:', workOrderDetail.value)
}

// 更新进度
const handleUpdateProgress = () => {
  // 实现更新进度逻辑
  console.log('更新进度:', workOrderDetail.value)
}

// 工时管理
const handleLaborManagement = () => {
  emit('labor-management', workOrderDetail.value)
}

// 工具函数
const getStatusTagType = (status) => {
  const types = {
    new: 'info',
    in_progress: 'warning',
    waiting_parts: 'danger',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    new: '新建',
    in_progress: '进行中',
    waiting_parts: '待料中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getSeverityTagType = (severity) => {
  const types = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[severity] || 'info'
}

const getSeverityLabel = (severity) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[severity] || severity
}

const getTimelineType = (status) => {
  const types = {
    new: 'primary',
    in_progress: 'warning',
    waiting_parts: 'danger',
    completed: 'success'
  }
  return types[status] || 'primary'
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.work-order-detail-dialog {
  --el-dialog-content-font-size: 14px;
}

.work-order-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  font-weight: 400;
}

.description {
  margin-top: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  line-height: 1.5;
}

.fault-info,
.technician-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-history {
  max-height: 300px;
  overflow-y: auto;
}

.timeline-content {
  padding: 4px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.status {
  font-weight: 500;
  color: #333;
}

.operator {
  font-size: 12px;
  color: #666;
}

.timeline-notes {
  font-size: 13px;
  color: #555;
  line-height: 1.4;
}

.labor-records,
.parts-used {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.work-order-detail::-webkit-scrollbar {
  width: 6px;
}

.work-order-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.work-order-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.work-order-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>