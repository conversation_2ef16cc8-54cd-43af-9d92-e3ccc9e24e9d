<template>
  <el-dialog
    v-model="visible"
    title="工作流详情"
    width="70%"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 工作流基本信息 -->
      <div class="workflow-header mb-6">
        <div class="flex justify-between items-start">
          <div>
            <h2 class="text-xl font-bold text-gray-800 mb-2">{{ workflowDetail.title }}</h2>
            <div class="flex items-center space-x-4 text-sm text-gray-600">
              <span>工作流ID: {{ workflowDetail.id }}</span>
              <el-tag :type="getStatusColor(workflowDetail.status)" size="small">
                {{ getStatusText(workflowDetail.status) }}
              </el-tag>
              <el-tag :type="getPriorityColor(workflowDetail.priority)" size="small">
                {{ getPriorityText(workflowDetail.priority) }}
              </el-tag>
            </div>
          </div>
          <div class="text-right text-sm text-gray-600">
            <div>发起人: {{ workflowDetail.initiator }}</div>
            <div>创建时间: {{ formatDateTime(workflowDetail.created_at) }}</div>
            <div v-if="workflowDetail.completed_at">
              完成时间: {{ formatDateTime(workflowDetail.completed_at) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 工作流进度 -->
      <div class="workflow-progress mb-6">
        <h3 class="text-lg font-semibold mb-4">审批进度</h3>
        <el-steps :active="currentStepIndex" :finish-status="workflowDetail.status === 'completed' ? 'success' : 'process'">
          <el-step 
            v-for="(step, index) in workflowDetail.steps" 
            :key="index"
            :title="step.name"
            :description="step.assignee"
            :status="getStepStatus(step, index)"
          />
        </el-steps>
      </div>

      <!-- 当前步骤信息 -->
      <div v-if="currentStep" class="current-step mb-6">
        <h3 class="text-lg font-semibold mb-4">当前步骤</h3>
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="flex justify-between items-start">
            <div>
              <div class="font-medium text-blue-800">{{ currentStep.name }}</div>
              <div class="text-blue-600 mt-1">审批人: {{ currentStep.assignee }}</div>
              <div class="text-blue-600 mt-1">到达时间: {{ formatDateTime(currentStep.arrived_at) }}</div>
            </div>
            <div v-if="canProcessCurrentStep">
              <el-button type="primary" size="small" @click="showProcessDialog">
                立即处理
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 审批历史 -->
      <div class="workflow-history">
        <h3 class="text-lg font-semibold mb-4">审批历史</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in workflowDetail.history"
            :key="index"
            :timestamp="formatDateTime(record.timestamp)"
            :type="getHistoryType(record.action)"
          >
            <el-card>
              <div class="flex justify-between items-start">
                <div>
                  <div class="font-medium">{{ record.step_name }}</div>
                  <div class="text-gray-600 mt-1">
                    {{ getActionText(record.action) }} - {{ record.operator }}
                  </div>
                  <div v-if="record.comment" class="text-gray-700 mt-2 bg-gray-50 p-2 rounded">
                    {{ record.comment }}
                  </div>
                </div>
                <el-tag :type="getActionColor(record.action)" size="small">
                  {{ getActionText(record.action) }}
                </el-tag>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 关联对象信息 -->
      <div v-if="workflowDetail.related_object" class="related-object mt-6">
        <h3 class="text-lg font-semibold mb-4">关联对象</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <span class="text-gray-600">对象类型:</span>
              <span class="ml-2 font-medium">{{ workflowDetail.related_object.type }}</span>
            </div>
            <div>
              <span class="text-gray-600">对象ID:</span>
              <span class="ml-2 font-medium">{{ workflowDetail.related_object.id }}</span>
            </div>
            <div class="col-span-2">
              <span class="text-gray-600">描述:</span>
              <span class="ml-2">{{ workflowDetail.related_object.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="canProcessCurrentStep" type="primary" @click="showProcessDialog">
          处理当前步骤
        </el-button>
        <el-button v-if="canWithdrawWorkflow" type="danger" @click="withdrawWorkflow">
          撤回工作流
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { workflowApi } from '@/api/workflow'

const props = defineProps({
  modelValue: Boolean,
  workflowId: String
})

const emit = defineEmits(['update:modelValue', 'process', 'withdraw'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 工作流详情数据
const workflowDetail = ref({
  id: '',
  title: '',
  status: '',
  priority: '',
  initiator: '',
  created_at: '',
  completed_at: '',
  steps: [],
  history: [],
  related_object: null
})

// 模拟数据
const initMockData = () => {
  workflowDetail.value = {
    id: 'WF-2025-001',
    title: '采购订单审批 - PO240001',
    status: 'in_progress',
    priority: 'normal',
    initiator: '张三',
    created_at: '2025-01-13 09:00:00',
    completed_at: '',
    steps: [
      { name: '发起申请', assignee: '张三', status: 'completed', arrived_at: '2025-01-13 09:00:00' },
      { name: '部门经理审批', assignee: '李经理', status: 'current', arrived_at: '2025-01-13 09:05:00' },
      { name: '财务审批', assignee: '财务部', status: 'pending', arrived_at: '' },
      { name: '总经理审批', assignee: '王总', status: 'pending', arrived_at: '' }
    ],
    history: [
      {
        step_name: '发起申请',
        action: 'submit',
        operator: '张三',
        timestamp: '2025-01-13 09:00:00',
        comment: '紧急采购需求，请尽快审批'
      },
      {
        step_name: '系统自动分配',
        action: 'assign',
        operator: '系统',
        timestamp: '2025-01-13 09:05:00',
        comment: '自动分配给部门经理李经理'
      }
    ],
    related_object: {
      type: '采购订单',
      id: 'PO240001',
      description: '发动机零件采购订单，金额：50万元'
    }
  }
}

// 计算属性
const currentStepIndex = computed(() => {
  return workflowDetail.value.steps.findIndex(step => step.status === 'current')
})

const currentStep = computed(() => {
  return workflowDetail.value.steps.find(step => step.status === 'current')
})

const canProcessCurrentStep = computed(() => {
  // 判断当前用户是否可以处理当前步骤
  return currentStep.value && currentStep.value.assignee === '李经理' // 模拟当前用户
})

const canWithdrawWorkflow = computed(() => {
  // 判断是否可以撤回工作流
  return ['pending', 'in_progress'].includes(workflowDetail.value.status) && 
         workflowDetail.value.initiator === '张三' // 模拟当前用户
})

// 加载工作流详情
const loadWorkflowDetail = async () => {
  if (!props.workflowId) return
  
  try {
    loading.value = true
    // const response = await workflowApi.getWorkflowStatus(props.workflowId)
    // workflowDetail.value = response.body.workflow
    
    // 使用模拟数据
    initMockData()
  } catch (error) {
    ElMessage.error('加载工作流详情失败')
  } finally {
    loading.value = false
  }
}

// 方法
const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary', 
    completed: 'success',
    withdrawn: 'info',
    rejected: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成', 
    withdrawn: '已撤回',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getStepStatus = (step, index) => {
  if (step.status === 'completed') return 'finish'
  if (step.status === 'current') return 'process'
  return 'wait'
}

const getHistoryType = (action) => {
  const types = {
    submit: 'primary',
    approve: 'success',
    reject: 'danger',
    assign: 'info',
    withdraw: 'warning'
  }
  return types[action] || 'primary'
}

const getActionColor = (action) => {
  const colors = {
    submit: 'primary',
    approve: 'success',
    reject: 'danger',
    assign: 'info', 
    withdraw: 'warning'
  }
  return colors[action] || ''
}

const getActionText = (action) => {
  const texts = {
    submit: '提交',
    approve: '批准',
    reject: '拒绝',
    assign: '分配',
    withdraw: '撤回'
  }
  return texts[action] || action
}

const formatDateTime = (datetime) => {
  return datetime ? new Date(datetime).toLocaleString('zh-CN') : ''
}

const showProcessDialog = () => {
  emit('process', props.workflowId, currentStep.value.id)
}

const withdrawWorkflow = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要撤回此工作流吗？撤回后流程将终止。',
      '确认撤回',
      { type: 'warning' }
    )
    
    emit('withdraw', props.workflowId)
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败')
    }
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听workflowId变化
watch(() => props.workflowId, (newId) => {
  if (newId && props.modelValue) {
    loadWorkflowDetail()
  }
})

// 监听dialog显示
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.workflowId) {
    loadWorkflowDetail()
  }
})
</script>

<style scoped>
.workflow-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.workflow-progress .el-steps {
  margin-top: 20px;
}

.current-step {
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  overflow: hidden;
}

.dialog-footer {
  text-align: right;
}
</style>