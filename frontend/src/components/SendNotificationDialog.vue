<template>
  <el-dialog
    v-model="visible"
    title="发送通知"
    width="50%"
    :before-close="handleClose"
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="通知类型" prop="type">
        <el-select v-model="form.type" placeholder="选择通知类型" style="width: 100%">
          <el-option label="系统通知" value="system" />
          <el-option label="订单通知" value="order" />
          <el-option label="审批通知" value="approval" />
          <el-option label="库存预警" value="inventory" />
          <el-option label="AOG紧急" value="aog" />
        </el-select>
      </el-form-item>

      <el-form-item label="优先级" prop="priority">
        <el-radio-group v-model="form.priority">
          <el-radio value="low">低</el-radio>
          <el-radio value="normal">普通</el-radio>
          <el-radio value="high">高</el-radio>
          <el-radio value="urgent">紧急</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="接收对象" prop="recipient_type">
        <el-radio-group v-model="form.recipient_type" @change="handleRecipientTypeChange">
          <el-radio value="all">全体用户</el-radio>
          <el-radio value="role">按角色</el-radio>
          <el-radio value="department">按部门</el-radio>
          <el-radio value="specific">指定用户</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        v-if="form.recipient_type === 'role'" 
        label="选择角色" 
        prop="recipient_roles"
      >
        <el-select 
          v-model="form.recipient_roles" 
          placeholder="选择角色" 
          multiple 
          style="width: 100%"
        >
          <el-option label="系统管理员" value="admin" />
          <el-option label="采购员" value="purchaser" />
          <el-option label="仓库管理员" value="warehouse_manager" />
          <el-option label="财务人员" value="finance" />
          <el-option label="销售人员" value="sales" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="form.recipient_type === 'department'" 
        label="选择部门" 
        prop="recipient_departments"
      >
        <el-select 
          v-model="form.recipient_departments" 
          placeholder="选择部门" 
          multiple 
          style="width: 100%"
        >
          <el-option label="采购部" value="purchase" />
          <el-option label="销售部" value="sales" />
          <el-option label="仓储部" value="warehouse" />
          <el-option label="财务部" value="finance" />
          <el-option label="技术部" value="technical" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="form.recipient_type === 'specific'" 
        label="选择用户" 
        prop="recipient_users"
      >
        <el-select 
          v-model="form.recipient_users" 
          placeholder="选择用户" 
          multiple 
          filterable 
          style="width: 100%"
        >
          <el-option 
            v-for="user in availableUsers" 
            :key="user.id"
            :label="user.name"
            :value="user.id"
          >
            <span>{{ user.name }}</span>
            <span class="text-gray-500 ml-2">{{ user.department }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="通知标题" prop="title">
        <el-input 
          v-model="form.title" 
          placeholder="输入通知标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="通知内容" prop="content">
        <el-input 
          v-model="form.content" 
          type="textarea" 
          :rows="5"
          placeholder="输入通知内容"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="发送渠道">
        <el-checkbox-group v-model="form.channels">
          <el-checkbox value="system">站内通知</el-checkbox>
          <el-checkbox value="email">邮件通知</el-checkbox>
          <el-checkbox value="sms">短信通知</el-checkbox>
          <el-checkbox value="push">推送通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="定时发送">
        <el-switch 
          v-model="form.scheduled" 
          active-text="是" 
          inactive-text="否"
        />
      </el-form-item>

      <el-form-item 
        v-if="form.scheduled" 
        label="发送时间" 
        prop="send_time"
      >
        <el-date-picker
          v-model="form.send_time"
          type="datetime"
          placeholder="选择发送时间"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="关联数据">
        <el-input 
          v-model="form.metadata" 
          type="textarea" 
          :rows="3"
          placeholder="JSON格式的关联数据（可选）"
        />
      </el-form-item>
    </el-form>

    <!-- 预览区域 -->
    <div class="notification-preview mt-6">
      <h4 class="text-lg font-medium mb-3">通知预览</h4>
      <div class="preview-card p-4 border rounded-lg bg-gray-50">
        <div class="flex items-start space-x-3">
          <div class="notification-icon">
            <component 
              :is="getNotificationIcon(form.type)" 
              :class="getIconClass(form.priority)"
              class="w-6 h-6"
            />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h5 class="font-medium">{{ form.title || '通知标题' }}</h5>
              <el-tag :type="getPriorityColor(form.priority)" size="small">
                {{ getPriorityText(form.priority) }}
              </el-tag>
            </div>
            <p class="text-gray-600 mt-1">{{ form.content || '通知内容' }}</p>
            <div class="text-xs text-gray-500 mt-2">
              {{ new Date().toLocaleString('zh-CN') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          {{ form.scheduled ? '定时发送' : '立即发送' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Bell, ShoppingCart, Document, Warning, Notification, Setting
} from '@element-plus/icons-vue'
import { notificationsApi } from '@/api/notifications'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  type: 'system',
  priority: 'normal',
  recipient_type: 'all',
  recipient_roles: [],
  recipient_departments: [],
  recipient_users: [],
  title: '',
  content: '',
  channels: ['system'],
  scheduled: false,
  send_time: null,
  metadata: ''
})

// 可用用户列表
const availableUsers = ref([
  { id: 'user1', name: '张三', department: '采购部' },
  { id: 'user2', name: '李四', department: '销售部' },
  { id: 'user3', name: '王五', department: '仓储部' },
  { id: 'user4', name: '赵六', department: '财务部' }
])

// 验证规则
const rules = {
  type: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  recipient_type: [
    { required: true, message: '请选择接收对象', trigger: 'change' }
  ],
  recipient_roles: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  recipient_departments: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  recipient_users: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入通知标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入通知内容', trigger: 'blur' }
  ],
  send_time: [
    { required: true, message: '请选择发送时间', trigger: 'change' }
  ]
}

// 方法
const handleRecipientTypeChange = () => {
  // 清空相关选择
  form.recipient_roles = []
  form.recipient_departments = []
  form.recipient_users = []
}

const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (Array.isArray(form[key])) {
      form[key] = key === 'channels' ? ['system'] : []
    } else if (typeof form[key] === 'boolean') {
      form[key] = false
    } else {
      form[key] = key === 'type' ? 'system' : 
                  key === 'priority' ? 'normal' : 
                  key === 'recipient_type' ? 'all' : ''
    }
  })
  formRef.value?.clearValidate()
}

const saveDraft = () => {
  try {
    const draftKey = 'notification_draft'
    localStorage.setItem(draftKey, JSON.stringify(form))
    ElMessage.success('草稿已保存')
  } catch (error) {
    ElMessage.error('保存草稿失败')
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 准备接收者ID列表
    let recipient_ids = []
    switch (form.recipient_type) {
      case 'all':
        recipient_ids = ['all']
        break
      case 'role':
        recipient_ids = form.recipient_roles
        break
      case 'department':
        recipient_ids = form.recipient_departments
        break
      case 'specific':
        recipient_ids = form.recipient_users
        break
    }
    
    // 准备提交数据
    const submitData = {
      recipient_ids,
      type: form.type,
      title: form.title,
      content: form.content,
      priority: form.priority,
      metadata: form.metadata ? JSON.parse(form.metadata) : {},
      channels: form.channels,
      scheduled: form.scheduled,
      send_time: form.send_time
    }
    
    // 调用API发送通知
    // await notificationsApi.sendNotification(submitData)
    
    ElMessage.success(
      form.scheduled ? '定时通知设置成功' : '通知发送成功'
    )
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('发送失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  resetForm()
  visible.value = false
}

// 辅助函数
const getNotificationIcon = (type) => {
  const icons = {
    system: Setting,
    order: ShoppingCart,
    approval: Document,
    inventory: Warning,
    aog: Notification
  }
  return icons[type] || Bell
}

const getIconClass = (priority) => {
  const classes = {
    urgent: 'text-red-500',
    high: 'text-orange-500',
    normal: 'text-blue-500',
    low: 'text-gray-500'
  }
  return classes[priority] || classes.normal
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}
</script>

<style scoped>
.notification-preview {
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.preview-card {
  border: 1px solid #e3f2fd;
  background-color: #f8f9fa;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
}

.dialog-footer {
  text-align: right;
}
</style>