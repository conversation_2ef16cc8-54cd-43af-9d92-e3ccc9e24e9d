<template>
  <el-dialog
    v-model="visible"
    title="需求响应"
    width="70%"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 响应统计 -->
      <div class="response-stats mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="stats-card text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ responseStats.totalResponses }}</div>
            <div class="text-sm text-gray-600">总响应数</div>
          </div>
          <div class="stats-card text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ responseStats.validResponses }}</div>
            <div class="text-sm text-gray-600">有效响应</div>
          </div>
          <div class="stats-card text-center p-4 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">¥{{ formatPrice(responseStats.avgPrice) }}</div>
            <div class="text-sm text-gray-600">平均报价</div>
          </div>
          <div class="stats-card text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ responseStats.avgDelivery }}天</div>
            <div class="text-sm text-gray-600">平均交期</div>
          </div>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="filters mb-4">
        <div class="flex flex-wrap gap-4 items-center">
          <el-select v-model="filters.status" placeholder="响应状态" style="width: 120px" size="small">
            <el-option label="全部状态" value="" />
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
          
          <el-select v-model="filters.sortBy" placeholder="排序方式" style="width: 120px" size="small">
            <el-option label="价格升序" value="price_asc" />
            <el-option label="价格降序" value="price_desc" />
            <el-option label="交期升序" value="delivery_asc" />
            <el-option label="响应时间" value="response_time" />
          </el-select>
          
          <el-button @click="applyFilters" size="small">筛选</el-button>
          <el-button @click="resetFilters" size="small">重置</el-button>
        </div>
      </div>

      <!-- 响应列表 -->
      <el-table :data="responses" style="width: 100%">
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="供应商" width="180">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-avatar :src="row.supplier.avatar" size="small" class="mr-2" />
              <div>
                <div class="font-medium">{{ row.supplier.name }}</div>
                <div class="text-xs text-gray-500">{{ row.supplier.type }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="报价信息" min-width="200">
          <template #default="{ row }">
            <div>
              <div class="text-lg font-bold text-green-600">¥{{ formatPrice(row.quote.price) }}</div>
              <div class="text-sm text-gray-600">{{ row.quote.price_type }}</div>
              <div class="text-xs text-gray-500">
                有效期: {{ formatDate(row.quote.valid_until) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="交付信息" width="120">
          <template #default="{ row }">
            <div class="text-center">
              <div class="text-blue-600 font-medium">{{ row.delivery.lead_time }}天</div>
              <div class="text-xs text-gray-500">{{ row.delivery.method }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="响应状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="响应时间" width="120">
          <template #default="{ row }">
            <span class="text-sm text-gray-600">{{ formatDateTime(row.response_time) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex flex-col space-y-1">
              <div class="flex space-x-1">
                <el-button 
                  v-if="row.status === 'pending'"
                  type="success" 
                  size="small" 
                  @click="approveResponse(row)"
                >
                  接受
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'"
                  type="danger" 
                  size="small" 
                  @click="rejectResponse(row)"
                >
                  拒绝
                </el-button>
              </div>
              <div class="flex space-x-1">
                <el-button size="small" @click="viewDetails(row)">
                  详情
                </el-button>
                <el-button size="small" @click="contactSupplier(row)">
                  沟通
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions mt-4">
        <div class="flex justify-between items-center">
          <div>
            <el-button @click="batchApprove" :disabled="selectedResponses.length === 0">
              批量接受
            </el-button>
            <el-button @click="batchReject" :disabled="selectedResponses.length === 0">
              批量拒绝
            </el-button>
            <el-button @click="exportResponses">
              导出响应
            </el-button>
          </div>
          <div>
            <span class="text-sm text-gray-500">
              已选择 {{ selectedResponses.length }} 项
            </span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end mt-6">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>

    <!-- 响应详情弹窗 -->
    <ResponseDetailDialog 
      v-model="showDetailDialog" 
      :response="selectedResponse"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { demandsApi } from '@/api/demands-enhanced'

const props = defineProps({
  modelValue: Boolean,
  demandId: String
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const showDetailDialog = ref(false)
const selectedResponse = ref(null)
const selectedResponses = ref([])

// 响应统计
const responseStats = ref({
  totalResponses: 0,
  validResponses: 0,
  avgPrice: 0,
  avgDelivery: 0
})

// 筛选器
const filters = reactive({
  status: '',
  sortBy: 'response_time'
})

// 响应列表
const responses = ref([])

// 模拟数据
const initMockData = () => {
  responseStats.value = {
    totalResponses: 5,
    validResponses: 4,
    avgPrice: 720000,
    avgDelivery: 8
  }

  responses.value = [
    {
      id: 'R001',
      supplier: {
        name: '东航技术',
        avatar: '/avatars/supplier1.jpg',
        type: '维修企业'
      },
      quote: {
        price: 850000,
        price_type: '含税含运费',
        valid_until: '2025-02-13'
      },
      delivery: {
        lead_time: 3,
        method: '航空快递'
      },
      status: 'pending',
      response_time: '2025-01-13 14:30:00'
    },
    {
      id: 'R002',
      supplier: {
        name: '南航机务',
        avatar: '/avatars/supplier2.jpg',
        type: '航空公司'
      },
      quote: {
        price: 620000,
        price_type: '不含税',
        valid_until: '2025-02-15'
      },
      delivery: {
        lead_time: 5,
        method: '陆运'
      },
      status: 'approved',
      response_time: '2025-01-13 10:15:00'
    },
    {
      id: 'R003',
      supplier: {
        name: '海航技术',
        avatar: '/avatars/supplier3.jpg',
        type: '维修企业'
      },
      quote: {
        price: 780000,
        price_type: '含税价',
        valid_until: '2025-02-10'
      },
      delivery: {
        lead_time: 15,
        method: '海运+陆运'
      },
      status: 'pending',
      response_time: '2025-01-13 16:45:00'
    }
  ]
}

// 加载响应数据
const loadResponses = async () => {
  if (!props.demandId) return
  
  try {
    loading.value = true
    // const response = await demandsApi.getDemandResponses(props.demandId, filters)
    // responses.value = response.body.responses
    // responseStats.value = response.body.stats
    
    // 使用模拟数据
    initMockData()
    ElMessage.success('响应数据已加载')
  } catch (error) {
    ElMessage.error('加载响应数据失败')
  } finally {
    loading.value = false
  }
}

// 方法
const applyFilters = () => {
  loadResponses()
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'sortBy' ? 'response_time' : ''
  })
  loadResponses()
}

const approveResponse = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定接受供应商"${row.supplier.name}"的报价吗？`,
      '确认接受',
      { type: 'warning' }
    )
    
    // await demandsApi.acceptDemandResponse(props.demandId, row.id)
    row.status = 'approved'
    ElMessage.success('响应已接受')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const rejectResponse = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定拒绝供应商"${row.supplier.name}"的报价吗？`,
      '确认拒绝',
      { type: 'warning' }
    )
    
    row.status = 'rejected'
    ElMessage.success('响应已拒绝')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const batchApprove = async () => {
  if (selectedResponses.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定批量接受选中的 ${selectedResponses.value.length} 个响应吗？`,
      '批量接受',
      { type: 'warning' }
    )
    
    ElMessage.success('批量接受成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const batchReject = async () => {
  if (selectedResponses.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定批量拒绝选中的 ${selectedResponses.value.length} 个响应吗？`,
      '批量拒绝',
      { type: 'warning' }
    )
    
    ElMessage.success('批量拒绝成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const exportResponses = () => {
  ElMessage.info('正在导出响应数据...')
}

const viewDetails = (row) => {
  selectedResponse.value = row
  showDetailDialog.value = true
}

const contactSupplier = (row) => {
  ElMessage.info(`正在联系供应商: ${row.supplier.name}`)
}

const handleClose = () => {
  visible.value = false
}

// 辅助函数
const getStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已接受',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

const formatPrice = (price) => {
  return price.toLocaleString()
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString('zh-CN')
}

// 监听dialog显示
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.demandId) {
    loadResponses()
  }
})
</script>

<style scoped>
.stats-card {
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.batch-actions {
  border-top: 1px solid #eee;
  padding-top: 16px;
}
</style>