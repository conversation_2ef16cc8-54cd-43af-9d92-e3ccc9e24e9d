<template>
  <el-dialog
    v-model="visible"
    title="需求匹配"
    width="80%"
    :before-close="handleClose"
  >
    <!-- 调试信息：显示认证状态 -->
    <div v-if="isDevelopment" class="debug-info mb-4 p-2 bg-gray-100 rounded text-xs">
      <strong>🔧 调试信息:</strong>
      认证状态: {{ authDebugInfoFilled.isAuthenticated ? '✅已登录' : '❌未登录' }} |
      Token: {{ authDebugInfoFilled.hasToken ? '✅存在' : '❌不存在' }} |
      用户: {{ authDebugInfoFilled.username || '未知' }}
    </div>
    <div v-loading="loading">
      <!-- 匹配概览 -->
      <div class="match-overview mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="stats-card text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ matchStats.totalMatches }}</div>
            <div class="text-sm text-gray-600">找到匹配</div>
          </div>
          <div class="stats-card text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ matchStats.highScore }}</div>
            <div class="text-sm text-gray-600">高分匹配</div>
          </div>
          <div class="stats-card text-center p-4 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">{{ matchStats.availableStock }}</div>
            <div class="text-sm text-gray-600">有库存</div>
          </div>
          <div class="stats-card text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ matchStats.responseTime }}小时</div>
            <div class="text-sm text-gray-600">平均响应</div>
          </div>
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="filters mb-4">
        <div class="flex flex-wrap gap-4 items-center">
          <el-select v-model="filters.score" placeholder="匹配分数" style="width: 120px" size="small">
            <el-option label="全部分数" value="" />
            <el-option label="90+分" value="90" />
            <el-option label="80+分" value="80" />
            <el-option label="70+分" value="70" />
          </el-select>
          
          <el-select v-model="filters.stock" placeholder="库存状态" style="width: 120px" size="small">
            <el-option label="全部库存" value="" />
            <el-option label="有库存" value="available" />
            <el-option label="需采购" value="need_purchase" />
          </el-select>
          
          <el-select v-model="filters.location" placeholder="地理位置" style="width: 120px" size="small">
            <el-option label="全部位置" value="" />
            <el-option label="本地" value="local" />
            <el-option label="国内" value="domestic" />
            <el-option label="海外" value="overseas" />
          </el-select>
          
          <el-button @click="applyFilters" size="small">筛选</el-button>
          <el-button @click="resetFilters" size="small">重置</el-button>
        </div>
      </div>

      <!-- 匹配结果列表 -->
      <el-table :data="matchResults" style="width: 100%">
        <el-table-column label="供应商" width="200">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-avatar :src="row.supplier.avatar" size="small" class="mr-2" />
              <div>
                <div class="font-medium">{{ row.supplier.name }}</div>
                <div class="text-xs text-gray-500">{{ row.supplier.location }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="匹配分数" width="100" align="center">
          <template #default="{ row }">
            <div class="flex flex-col items-center">
              <el-progress 
                :percentage="row.match_score" 
                :stroke-width="6"
                :color="getScoreColor(row.match_score)"
                type="circle"
                :width="40"
              />
              <span class="text-xs mt-1">{{ row.match_score }}分</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="零件信息" min-width="180">
          <template #default="{ row }">
            <div>
              <div class="font-medium">{{ row.part.part_number }}</div>
              <div class="text-sm text-gray-600">{{ row.part.description }}</div>
              <div class="text-xs text-gray-500">序列号: {{ row.part.serial_number }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="库存状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.stock_status === 'available' ? 'success' : 'warning'" 
              size="small"
            >
              {{ row.stock_status === 'available' ? '有库存' : '需采购' }}
            </el-tag>
            <div class="text-xs text-gray-500 mt-1">数量: {{ row.available_quantity }}</div>
          </template>
        </el-table-column>

        <el-table-column label="参考价格" width="120" align="center">
          <template #default="{ row }">
            <div class="text-green-600 font-medium">¥{{ formatPrice(row.price) }}</div>
            <div class="text-xs text-gray-500">{{ row.price_type }}</div>
          </template>
        </el-table-column>

        <el-table-column label="交付时间" width="100" align="center">
          <template #default="{ row }">
            <div class="text-blue-600">{{ row.delivery_time }}天</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="flex flex-col space-y-1">
              <el-button type="primary" size="small" @click="contactSupplier(row)">
                联系供应商
              </el-button>
              <el-button size="small" @click="viewDetails(row)">
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 操作按钮 -->
      <div class="flex justify-between mt-6">
        <div>
          <el-button @click="refreshMatches">
            <Refresh class="w-4 h-4 mr-1" />
            重新匹配
          </el-button>
          <el-button @click="exportResults">
            <Download class="w-4 h-4 mr-1" />
            导出结果
          </el-button>
        </div>
        <div>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import { demandsApi } from '@/api/demands-enhanced'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  modelValue: Boolean,
  demandId: String
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 开发环境标识
const isDevelopment = import.meta.env.MODE === 'development'

// 认证状态调试信息
const authStore = useAuthStore()
const authDebugInfoFilled = computed(() => {
  return {
    isAuthenticated: authStore.isAuthenticated,
    hasToken: !!authStore.token,
    username: authStore.user?.username || authStore.user?.real_name
  }
})

// 匹配统计
const matchStats = ref({
  totalMatches: 0,
  highScore: 0,
  availableStock: 0,
  responseTime: 24
})

// 筛选器
const filters = reactive({
  score: '',
  stock: '',
  location: ''
})

// 匹配结果
const matchResults = ref([])

// 模拟数据
const initMockData = () => {
  matchStats.value = {
    totalMatches: 8,
    highScore: 5,
    availableStock: 6,
    responseTime: 24
  }

  matchResults.value = [
    {
      id: 'M001',
      supplier: {
        name: '东航技术',
        avatar: '/avatars/supplier1.jpg',
        location: '上海'
      },
      match_score: 95,
      part: {
        part_number: 'CF6-80C2-B6F-001',
        description: '发动机高压压气机叶片',
        serial_number: 'SN123456789'
      },
      stock_status: 'available',
      available_quantity: 2,
      price: 850000,
      price_type: '含税价',
      delivery_time: 3
    },
    {
      id: 'M002',
      supplier: {
        name: '南航机务',
        avatar: '/avatars/supplier2.jpg',
        location: '广州'
      },
      match_score: 88,
      part: {
        part_number: 'CF6-80C2-B6F-002',
        description: '发动机高压压气机叶片（翻新）',
        serial_number: 'SN987654321'
      },
      stock_status: 'available',
      available_quantity: 1,
      price: 620000,
      price_type: '不含税价',
      delivery_time: 5
    },
    {
      id: 'M003',
      supplier: {
        name: '海航技术',
        avatar: '/avatars/supplier3.jpg',
        location: '海口'
      },
      match_score: 82,
      part: {
        part_number: 'CF6-80C2-B6F-003',
        description: '发动机高压压气机叶片',
        serial_number: 'SN456789123'
      },
      stock_status: 'need_purchase',
      available_quantity: 0,
      price: 780000,
      price_type: '含税价',
      delivery_time: 15
    }
  ]
}

// 检查需求数据来源
const checkDemandDataSource = async () => {
  try {
    // 获取混合数据列表来判断数据来源
    const mockManager = await import('@/api/mockManager')
    const hybridData = mockManager.default.getDemandsList()
    
    if (hybridData && hybridData.body.demands.length > 0) {
      const demand = hybridData.body.demands.find(d => d.id.toString() === props.demandId.toString())
      if (demand) {
        // 检查是否为用户创建的真实数据
        const isUserCreated = demand.user_created === true || demand.from_real_form === true
        console.log('📊 需求数据来源分析:', {
          demandId: props.demandId,
          isUserCreated: isUserCreated,
          source: isUserCreated ? '用户真实数据' : 'Mock示例数据'
        })
        return {
          exists: true,
          isUserCreated: isUserCreated,
          demand: demand
        }
      }
    }
    
    console.log('📊 需求数据来源分析: 未在本地数据中找到，可能是服务器数据')
    return {
      exists: false,
      isUserCreated: false,
      demand: null
    }
  } catch (error) {
    console.warn('❌ 检查数据来源失败:', error)
    return {
      exists: false,
      isUserCreated: false,
      demand: null
    }
  }
}

// 检查认证状态的完整性（简化版，避免不必要的API调用）
const checkAuthenticationStatus = async () => {
  try {
    const { useAuthStore } = await import('@/stores/auth')
    const authStore = useAuthStore()

    // 详细的认证状态调试
    console.log('🔍 简化认证状态检查:')
    console.log('  authStore.token:', authStore.token ? '存在' : '不存在')
    console.log('  authStore.isAuthenticated:', authStore.isAuthenticated)
    console.log('  authStore.user:', authStore.user?.username || '未知')

    // 基础检查：token存在且用户已登录
    if (!authStore.token || !authStore.isAuthenticated) {
      console.log('❌ 基础认证检查失败: 无token或未登录')
      return false
    }

    // 简化检查：只检查基本的认证状态，不发起额外的API请求
    // 让实际的业务API调用来处理token验证和刷新
    console.log('✅ 基础认证状态检查通过')
    return true

  } catch (error) {
    console.warn('❌ 认证检查异常:', error)
    return false
  }
}

// 加载匹配结果
const loadMatchResults = async () => {
  if (!props.demandId) return
  
  try {
    loading.value = true
    console.log('开始加载需求匹配结果:', props.demandId)
    
    // 首先检查数据来源
    const dataSource = await checkDemandDataSource()
    console.log('数据来源检查结果:', dataSource)
    
    // 如果是用户创建的真实数据，需要尝试真实API
    if (dataSource.exists && dataSource.isUserCreated) {
      console.log('🚀 检测到用户真实数据，尝试真实API匹配')
      
      // 执行认证状态检查
      const isAuthenticated = await checkAuthenticationStatus()
      console.log('认证状态检查结果:', isAuthenticated)
      
      if (isAuthenticated) {
        try {
          console.log('🔍 调用真实API进行匹配...')
          const response = await demandsApi.matchDemand(props.demandId, filters)
          
          if (response.success && response.body.matches) {
            // 转换API数据格式为组件需要的格式
            matchResults.value = response.body.matches.map(match => ({
              id: match.id,
              supplier: {
                name: match.supplier?.company || match.supplier?.name || 'Unknown',
                avatar: '/avatars/default.jpg',
                location: match.inventory?.location || 'Unknown'
              },
              match_score: Math.round(match.match_score || 0),
              part: {
                part_number: match.material?.part_number || '',
                description: match.material?.name || '',
                serial_number: match.inventory?.batch_number || 'N/A'
              },
              stock_status: match.availability === 'available' ? 'available' : 'need_purchase',
              available_quantity: match.inventory?.current_stock || 0,
              price: match.inventory?.unit_price || 0,
              price_type: '含税价',
              delivery_time: parseInt(match.estimated_delivery?.split('-')[0]) || 7
            }))
            
            // 更新统计信息
            matchStats.value = {
              totalMatches: response.body.total_matches || matchResults.value.length,
              highScore: matchResults.value.filter(m => m.match_score >= 90).length,
              availableStock: matchResults.value.filter(m => m.stock_status === 'available').length,
              responseTime: 24
            }
            
            if (matchResults.value.length > 0) {
              ElMessage.success(`找到 ${matchResults.value.length} 个真实匹配结果`)
              console.log('✅ 真实API调用成功，返回匹配结果')
            } else {
              ElMessage.info('未找到匹配结果，当前数据库中暂无合适的供应商')
              console.log('ℹ️ 真实API调用成功，但无匹配结果')
            }
            return
          } else {
            console.warn('真实API未返回有效数据')
            throw new Error('真实API返回数据无效')
          }
        } catch (apiError) {
          console.warn('真实API调用失败:', apiError.message || apiError)
          ElMessage.warning('匹配API调用失败，真实数据库中暂无匹配结果')
          
          // 对于真实数据，如果API失败，显示空结果而不是Mock数据
          matchResults.value = []
          matchStats.value = {
            totalMatches: 0,
            highScore: 0,
            availableStock: 0,
            responseTime: 24
          }
          return
        }
      } else {
        console.log('🔒 用户未登录，无法匹配真实数据')
        ElMessage.warning('请先登录后再进行需求匹配')
        
        // 对于真实数据，未登录时显示空结果
        matchResults.value = []
        matchStats.value = {
          totalMatches: 0,
          highScore: 0,
          availableStock: 0,
          responseTime: 24
        }
        return
      }
    } else {
      // Mock数据或未找到数据，使用Mock匹配结果
      console.log('🎭 检测到Mock示例数据，使用Mock匹配结果')
      initMockData()
      ElMessage.info('匹配结果已加载（示例数据）')
      return
    }
    
  } catch (error) {
    console.error('加载匹配结果失败:', error)
    // 最终错误时显示空结果
    matchResults.value = []
    matchStats.value = {
      totalMatches: 0,
      highScore: 0,
      availableStock: 0,
      responseTime: 24
    }
    ElMessage.error('加载匹配结果时发生错误')
  } finally {
    loading.value = false
  }
}

// 方法
const applyFilters = () => {
  loadMatchResults()
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  loadMatchResults()
}

const refreshMatches = async () => {
  if (!props.demandId) return
  
  try {
    loading.value = true
    console.log('开始重新匹配需求:', props.demandId)
    
    // 首先检查数据来源
    const dataSource = await checkDemandDataSource()
    console.log('重新匹配 - 数据来源检查结果:', dataSource)
    
    // 如果是用户创建的真实数据，需要尝试真实API
    if (dataSource.exists && dataSource.isUserCreated) {
      console.log('🚀 检测到用户真实数据，尝试真实API重新匹配')
      
      // 执行认证状态检查
      const isAuthenticated = await checkAuthenticationStatus()
      console.log('重新匹配认证状态检查结果:', isAuthenticated)
      
      if (isAuthenticated) {
        try {
          console.log('🔄 调用真实API进行重新匹配...')
          const response = await demandsApi.rematchDemand(props.demandId)
        
          if (response.success && response.body.matches) {
            // 转换API数据格式
            matchResults.value = response.body.matches.map(match => ({
              id: match.id,
              supplier: {
                name: match.supplier?.company || match.supplier?.name || 'Unknown',
                avatar: '/avatars/default.jpg',
                location: match.inventory?.location || 'Unknown'
              },
              match_score: Math.round(match.match_score || 0),
              part: {
                part_number: match.material?.part_number || '',
                description: match.material?.name || '',
                serial_number: match.inventory?.batch_number || 'N/A'
              },
              stock_status: match.availability === 'available' ? 'available' : 'need_purchase',
              available_quantity: match.inventory?.current_stock || 0,
              price: match.inventory?.unit_price || 0,
              price_type: '含税价',
              delivery_time: parseInt(match.estimated_delivery?.split('-')[0]) || 7
            }))
            
            // 更新统计信息
            matchStats.value = {
              totalMatches: response.body.total_matches || matchResults.value.length,
              highScore: matchResults.value.filter(m => m.match_score >= 90).length,
              availableStock: matchResults.value.filter(m => m.stock_status === 'available').length,
              responseTime: 24
            }
            
            if (matchResults.value.length > 0) {
              ElMessage.success(`重新匹配完成，找到 ${matchResults.value.length} 个匹配结果`)
              console.log('✅ 重新匹配API调用成功')
            } else {
              ElMessage.info('重新匹配完成，数据库中暂无合适的供应商')
              console.log('ℹ️ 重新匹配API调用成功，但无匹配结果')
            }
            return
          } else {
            console.warn('重新匹配API未返回有效数据')
            throw new Error('重新匹配返回数据无效')
          }
        } catch (apiError) {
          console.warn('重新匹配API调用失败:', apiError.message || apiError)
          ElMessage.warning('重新匹配API调用失败，真实数据库中暂无匹配结果')
          
          // 对于真实数据，如果API失败，显示空结果
          matchResults.value = []
          matchStats.value = {
            totalMatches: 0,
            highScore: 0,
            availableStock: 0,
            responseTime: 24
          }
          return
        }
      } else {
        console.log('🔒 用户未登录，无法重新匹配真实数据')
        ElMessage.warning('请先登录后再进行重新匹配')
        
        // 对于真实数据，未登录时显示空结果
        matchResults.value = []
        matchStats.value = {
          totalMatches: 0,
          highScore: 0,
          availableStock: 0,
          responseTime: 24
        }
        return
      }
    } else {
      // Mock数据，模拟重新匹配
      console.log('🎭 检测到Mock示例数据，模拟重新匹配操作')
      
      // 模拟更新的匹配结果（分数略有变化）
      initMockData()
      // 稍微调整分数来模拟重新匹配的效果
      matchResults.value.forEach(item => {
        item.match_score = Math.min(100, item.match_score + Math.floor(Math.random() * 5))
      })
      
      matchStats.value = {
        totalMatches: matchResults.value.length,
        highScore: matchResults.value.filter(m => m.match_score >= 90).length,
        availableStock: matchResults.value.filter(m => m.stock_status === 'available').length,
        responseTime: 22 // 稍微改善响应时间
      }
      
      ElMessage.success('重新匹配完成（示例数据）')
      return
    }
    
  } catch (error) {
    console.error('重新匹配失败:', error)
    ElMessage.error('重新匹配失败')
  } finally {
    loading.value = false
  }
}

const exportResults = () => {
  ElMessage.info('正在导出匹配结果...')
}

const contactSupplier = (row) => {
  ElMessage.info(`正在联系供应商: ${row.supplier.name}`)
}

const viewDetails = (row) => {
  ElMessage.info(`查看详情: ${row.part.part_number}`)
}

const handleClose = () => {
  visible.value = false
}

// 辅助函数
const getScoreColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#e6a23c'
  if (score >= 70) return '#f56c6c'
  return '#909399'
}

const formatPrice = (price) => {
  return price.toLocaleString()
}

// 监听dialog显示
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.demandId) {
    loadMatchResults()
  }
})
</script>

<style scoped>
.stats-card {
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}
</style>