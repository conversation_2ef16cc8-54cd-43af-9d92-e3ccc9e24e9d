<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Logo区域 -->
        <div class="flex items-center space-x-6">
          <router-link to="/" class="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
              </svg>
            </div>
            <div>
              <h1 class="title-font text-xl text-gray-800">航材共享保障平台</h1>
              <p class="text-xs text-gray-500">Aviation Material Sharing Platform</p>
            </div>
          </router-link>

          <!-- 智能面包屑导航 -->
          <nav v-if="showBreadcrumb" class="hidden md:flex items-center space-x-2 text-sm">
            <template v-for="(item, index) in breadcrumbItems" :key="index">
              <div v-if="index > 0" class="text-gray-400">
                <Right class="w-4 h-4" />
              </div>
              <router-link
                v-if="item.to && index < breadcrumbItems.length - 1"
                :to="item.to"
                class="text-gray-600 hover:text-blue-600 transition-colors font-medium"
              >
                {{ item.name }}
              </router-link>
              <span
                v-else
                class="text-gray-900 font-semibold"
              >
                {{ item.name }}
              </span>
            </template>
          </nav>
        </div>

        <!-- 导航菜单和用户操作区 -->
        <div class="flex items-center space-x-4">
          <!-- 门户导航菜单 (仅在门户页面显示) -->
          <nav v-if="isPortalPage" class="hidden lg:flex items-center space-x-6">
            <router-link to="/" class="nav-link">首页</router-link>
            <router-link to="/shared-materials" class="nav-link">共享件市场</router-link>
            <a href="/#features" class="nav-link">平台功能</a>
            <a href="/#hot-materials" class="nav-link">热门航材</a>
            <a href="/#industry-news" class="nav-link">行业资讯</a>
            <a href="/#success-cases" class="nav-link">成功案例</a>
          </nav>

          <!-- 工作台导航按钮 (在门户页面显示) -->
          <template v-if="isAuthenticated && isPortalPage">
            <router-link 
              to="/app/workspace" 
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2"
            >
              <House class="w-4 h-4" />
              <span>进入工作台</span>
            </router-link>
          </template>

          <!-- 返回门户按钮 (在工作台页面显示) -->
          <template v-if="isWorkspacePage">
            <router-link 
              to="/" 
              class="text-gray-700 hover:text-blue-600 font-medium flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-all duration-200"
            >
              <ArrowLeft class="w-4 h-4" />
              <span>返回门户</span>
            </router-link>

            <!-- 工作台快速操作 -->
            <el-button type="primary" size="default" @click="$emit('quick-action', 'publish-demand')">
              <Plus class="w-4 h-4 mr-2" />
              发布需求
            </el-button>

            <!-- 通知 -->
            <el-badge :value="unreadCount" class="item">
              <el-button circle size="default" @click="$emit('toggle-notifications')">
                <Bell class="w-5 h-5" />
              </el-button>
            </el-badge>
          </template>

          <!-- 用户菜单 -->
          <template v-if="isAuthenticated">
            <el-dropdown>
              <div class="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer">
                <el-avatar :size="32" :src="userAvatar">
                  {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}
                </el-avatar>
                <div class="text-left hidden sm:block">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-semibold text-gray-800">{{ userStore.user?.username }}</span>
                    <div class="w-1 h-3 bg-gray-400 rounded-full"></div>
                    <span class="text-xs text-gray-500">{{ getUserTypeLabel(userStore.user?.user_type) }}</span>
                  </div>
                </div>
                <ArrowDown class="w-4 h-4 text-gray-500" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <User class="w-4 h-4 mr-3" />
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <Setting class="w-4 h-4 mr-3" />
                    账户设置
                  </el-dropdown-item>
                  <template v-if="isWorkspacePage">
                    <el-dropdown-item divided @click="$router.push('/')">
                      <House class="w-4 h-4 mr-3" />
                      返回门户
                    </el-dropdown-item>
                  </template>
                  <template v-if="isPortalPage">
                    <el-dropdown-item divided @click="$router.push('/app/workspace')">
                      <Briefcase class="w-4 h-4 mr-3" />
                      进入工作台
                    </el-dropdown-item>
                  </template>
                  <el-dropdown-item @click="handleLogout">
                    <SwitchButton class="w-4 h-4 mr-3" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          
          <!-- 未登录状态 -->
          <template v-else>
            <router-link to="/login" class="text-gray-700 hover:text-blue-600 font-medium">
              登录
            </router-link>
            <router-link 
              to="/login" 
              class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200"
            >
              免费试用
            </router-link>
          </template>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { 
  House, Right, ArrowLeft, ArrowDown, Plus, Bell, User, Setting, 
  SwitchButton, Briefcase
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props 和 Emits
defineProps({
  userAvatar: {
    type: String,
    default: ''
  },
  unreadCount: {
    type: Number,
    default: 0
  }
})

defineEmits(['quick-action', 'toggle-notifications'])

// 组件状态
const route = useRoute()
const router = useRouter()
const userStore = useAuthStore()

// 计算属性
const isAuthenticated = computed(() => userStore.isAuthenticated)

// 判断当前页面类型
const isPortalPage = computed(() => {
  const path = route.path
  return path === '/' || path.startsWith('/shared-materials') || path.startsWith('/login')
})

const isWorkspacePage = computed(() => {
  return route.path.startsWith('/app/')
})

// 面包屑导航计算
const breadcrumbItems = computed(() => {
  const path = route.path
  const items = []

  if (isPortalPage.value) {
    // 门户页面的面包屑
    items.push({ name: '首页', to: '/' })
    
    if (path.startsWith('/shared-materials')) {
      items.push({ name: '共享件市场', to: '/shared-materials' })
      
      if (route.params.id) {
        items.push({ name: `航材详情 #${route.params.id}` })
      }
    }
  } else if (isWorkspacePage.value) {
    // 工作台页面的面包屑
    const pathSegments = path.split('/').filter(Boolean)
    
    if (pathSegments.length > 1) {
      // 工作台根页面
      items.push({ name: '工作台', to: '/app/workspace' })
      
      // 根据路由添加子页面
      const routeMapping = {
        'workspace': '工作台',
        'marketplace': '航材市场',
        'orders': '订单管理',
        'inventory': '库存管理',
        'maintenance': '维修管理',
        'quality': '质量管理',
        'analytics': '分析报告',
        'notifications': '通知管理',
        'demands': '需求管理'
      }
      
      if (pathSegments.length > 2) {
        const mainSection = pathSegments[2]
        if (routeMapping[mainSection]) {
          items.push({ name: routeMapping[mainSection] })
        }
      }
    }
  }

  return items
})

const showBreadcrumb = computed(() => {
  return breadcrumbItems.value.length > 1
})

// 方法
const getUserTypeLabel = (userType) => {
  const typeMap = {
    'airline_buyer': '航空公司采购员',
    'material_staff': '航材业务员',
    'maintenance_engineer': '维修工程师',
    'logistics_staff': '物流专员',
    'admin': '系统管理员'
  }
  return typeMap[userType] || '用户'
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}
</script>

<style scoped>
.nav-link {
  @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200;
}

.nav-link.router-link-active {
  @apply text-blue-600 font-semibold;
}

.title-font {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
</style>