<!--
动态导航组件
版本: 2.0
创建时间: 2025-07-19

根据用户角色和权限动态显示导航菜单
-->

<template>
  <nav class="dynamic-navigation">
    <!-- 桌面端导航 -->
    <div class="hidden lg:flex items-center space-x-4 ml-8 flex-nowrap">
      <router-link 
        v-for="nav in filteredNavigation" 
        :key="nav.name"
        :to="nav.path"
        class="main-nav-item whitespace-nowrap"
        :class="{ 'active': isActiveRoute(nav) }"
        :style="{ '--theme-color': themeColor }"
      >
        <component 
          v-if="nav.icon" 
          :is="nav.icon" 
          class="w-4 h-4 mr-2" 
        />
        <span>{{ nav.label }}</span>
      </router-link>
    </div>

    <!-- 移动端导航抽屉 -->
    <div class="lg:hidden">
      <el-button 
        circle 
        size="default" 
        @click="toggleMobileMenu"
        class="mobile-menu-btn"
      >
        <Menu class="w-5 h-5" />
      </el-button>

      <!-- 移动端菜单抽屉 -->
      <el-drawer
        v-model="showMobileMenu"
        title="导航菜单"
        direction="ltr"
        size="280px"
        :with-header="true"
      >
        <div class="mobile-nav-content">
          <!-- 角色信息 -->
          <div class="role-info mb-6 p-4 rounded-lg" :style="roleInfoFilledStyle">
            <div class="flex items-center space-x-3">
              <el-avatar :size="40" :src="userAvatar">
                {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}
              </el-avatar>
              <div>
                <div class="font-semibold text-white">{{ roleName }}</div>
                <div class="text-sm text-white/80">{{ userStore.user?.username }}</div>
              </div>
            </div>
          </div>

          <!-- 导航菜单 -->
          <div class="space-y-2">
            <router-link
              v-for="nav in filteredNavigation"
              :key="nav.name"
              :to="nav.path"
              class="mobile-nav-item"
              :class="{ 'active': isActiveRoute(nav) }"
              @click="closeMobileMenu"
            >
              <component 
                v-if="nav.icon" 
                :is="nav.icon" 
                class="w-5 h-5 mr-3" 
              />
              <span>{{ nav.label }}</span>
            </router-link>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions mt-8">
            <div class="text-sm font-medium text-gray-500 mb-3">快速操作</div>
            <div class="space-y-2">
              <button
                v-for="action in topQuickActions"
                :key="action.id"
                class="quick-action-btn w-full"
                @click="handleQuickAction(action)"
              >
                <component 
                  v-if="action.icon" 
                  :is="action.icon" 
                  class="w-4 h-4 mr-2" 
                />
                <span>{{ action.title }}</span>
              </button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleWorkspace } from '@/composables/useRoleWorkspace'
// 图标已全局注册，无需单独导入

// Props
const props = defineProps({
  userAvatar: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['quickAction'])

// Stores & Composables
const route = useRoute()
const userStore = useAuthStore()
const { 
  navigation, 
  quickActions, 
  theme, 
  roleName, 
  hasPermission,
  executeQuickAction 
} = useRoleWorkspace()

// 响应式数据
const showMobileMenu = ref(false)

// 计算属性
const filteredNavigation = computed(() => {
  // 确保 navigation 已经加载
  if (!navigation.value || !Array.isArray(navigation.value)) {
    return []
  }
  
  return navigation.value.filter(nav => {
    // 如果没有设置权限要求，则显示
    if (!nav.permission) return true
    // 检查用户是否有相应权限
    return hasPermission(nav.permission)
  })
})

const topQuickActions = computed(() => {
  // 确保 quickActions 已经加载
  if (!quickActions.value || !Array.isArray(quickActions.value)) {
    return []
  }
  // 只显示前4个快速操作
  return quickActions.value.slice(0, 4)
})

const themeColor = computed(() => {
  return theme.value?.primaryColor || '#1890ff'
})

const roleInfoFilledStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${theme.value?.gradientFrom || '#1890ff'}, ${theme.value?.gradientTo || '#722ed1'})`
  }
})

// 方法
const isActiveRoute = (nav) => {
  // 检查当前路由是否匹配导航项
  return route.name === nav.name || route.path.startsWith(nav.path)
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleQuickAction = (action) => {
  const result = executeQuickAction(action.id)
  if (result) {
    emit('quickAction', result)
  }
  closeMobileMenu()
}

// 监听路由变化，自动关闭移动菜单
watch(() => route.path, () => {
  showMobileMenu.value = false
})
</script>

<style scoped>
.dynamic-navigation {
  @apply flex items-center;
}

.main-nav-item {
  @apply flex items-center px-4 py-2 text-gray-600 font-semibold text-sm rounded-lg transition-all duration-200 hover:text-primary-600 hover:bg-primary-50;
}

.main-nav-item.active {
  @apply text-primary-600 bg-primary-50;
  color: var(--theme-color);
  background-color: color-mix(in srgb, var(--theme-color) 10%, transparent);
}

.main-nav-item:hover {
  color: var(--theme-color);
  background-color: color-mix(in srgb, var(--theme-color) 5%, transparent);
}

.mobile-menu-btn {
  @apply border-gray-200 hover:border-gray-300;
}

.mobile-nav-content {
  @apply h-full flex flex-col;
}

.mobile-nav-item {
  @apply flex items-center w-full px-4 py-3 text-gray-700 font-medium rounded-lg transition-all duration-200 hover:bg-gray-50 hover:text-primary-600;
}

.mobile-nav-item.active {
  @apply bg-primary-50 text-primary-600;
}

.quick-action-btn {
  @apply flex items-center px-3 py-2 text-sm text-gray-600 font-medium rounded-lg transition-all duration-200 hover:bg-gray-50 hover:text-primary-600;
}

.role-info {
  @apply shadow-sm;
}

/* 主题色变量 */
:root {
  --theme-color: #1890ff;
}
</style>