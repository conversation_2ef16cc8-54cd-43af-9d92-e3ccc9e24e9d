<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑需求"
    width="600px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" v-loading="loading">
      <!-- 需求标题 -->
      <el-form-item label="需求标题" prop="title">
        <el-input 
          v-model="form.title" 
          placeholder="请输入需求标题（可选，留空将自动生成）" 
          maxlength="300"
          show-word-limit
          @input="onTitleInput"
        />
        <div v-if="autoGeneratedTitle" class="text-sm text-gray-500 mt-1">
          预览标题：{{ autoGeneratedTitle }}
        </div>
      </el-form-item>

      <!-- 需求类型 -->
      <el-form-item label="需求类型" prop="type">
        <el-radio-group v-model="form.type" @change="updateAutoTitle">
          <el-radio value="turnaround">周转件需求</el-radio>
          <el-radio value="consumable">消耗件需求</el-radio>
          <el-radio value="maintenance">维修需求</el-radio>
          <el-radio value="aog">AOG紧急</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 航材信息 -->
      <el-form-item label="航材名称" prop="material_name">
        <el-input 
          v-model="form.material_name" 
          placeholder="请输入航材名称"
          @input="updateAutoTitle"
        />
      </el-form-item>

      <el-form-item label="零件号" prop="part_number">
        <el-input v-model="form.part_number" placeholder="请输入零件号" />
      </el-form-item>

      <el-form-item label="航材类别" prop="category">
        <el-select v-model="form.category" placeholder="请选择航材类别" style="width: 100%">
          <el-option label="发动机" value="engine" />
          <el-option label="起落架" value="landing_gear" />
          <el-option label="航电设备" value="avionics" />
          <el-option label="液压系统" value="hydraulic" />
          <el-option label="轮胎刹车" value="wheels_brakes" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <div class="grid grid-cols-2 gap-4">
        <el-form-item label="适用机型" prop="aircraft_type">
          <el-select 
            v-model="form.aircraft_type" 
            placeholder="请选择机型" 
            style="width: 100%"
            @change="updateAutoTitle"
          >
            <el-option label="A320" value="A320" />
            <el-option label="A330" value="A330" />
            <el-option label="A350" value="A350" />
            <el-option label="B737" value="B737" />
            <el-option label="B777" value="B777" />
            <el-option label="B787" value="B787" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="需求数量" prop="quantity">
          <el-input-number v-model="form.quantity" :min="1" style="width: 100%" />
        </el-form-item>
      </div>

      <!-- 需求详情 -->
      <el-form-item label="航材状态">
        <el-checkbox-group v-model="form.acceptable_conditions">
          <el-checkbox value="new">全新</el-checkbox>
          <el-checkbox value="serviceable">服务件</el-checkbox>
          <el-checkbox value="repaired">修理件</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div class="grid grid-cols-2 gap-4">
        <el-form-item label="期望价格" prop="expected_price">
          <el-input v-model="form.expected_price" placeholder="预算范围">
            <template #append>元</template>
          </el-input>
        </el-form-item>

        <el-form-item label="交付地点" prop="delivery_location">
          <el-select v-model="form.delivery_location" placeholder="请选择" style="width: 100%">
            <el-option label="北京" value="beijing" />
            <el-option label="上海" value="shanghai" />
            <el-option label="广州" value="guangzhou" />
            <el-option label="深圳" value="shenzhen" />
            <el-option label="成都" value="chengdu" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <el-form-item label="期望交付" prop="expected_delivery_date">
          <el-date-picker
            v-model="form.expected_delivery_date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="紧急程度" prop="urgency">
          <el-select 
            v-model="form.urgency" 
            placeholder="请选择" 
            style="width: 100%"
            @change="updateAutoTitle"
          >
            <el-option label="一般" value="normal" />
            <el-option label="紧急" value="high" />
            <el-option label="AOG" value="aog" />
          </el-select>
        </el-form-item>
      </div>

      <!-- 需求描述 -->
      <el-form-item label="需求描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请详细描述您的需求，包括技术要求、质量标准等"
        />
      </el-form-item>

      <!-- 联系信息 -->
      <el-form-item label="联系人" prop="contact_person">
        <el-input v-model="form.contact_person" placeholder="请输入联系人姓名" />
      </el-form-item>

      <div class="grid grid-cols-2 gap-4">
        <el-form-item label="联系电话" prop="contact_phone">
          <el-input v-model="form.contact_phone" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="联系邮箱" prop="contact_email">
          <el-input v-model="form.contact_email" placeholder="请输入联系邮箱" />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitEdit" :loading="submitting">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  demandData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])
const router = useRouter()

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单引用和状态
const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive({
  title: '',
  type: 'turnaround',
  material_name: '',
  part_number: '',
  category: '',
  aircraft_type: '',
  quantity: 1,
  acceptable_conditions: ['new', 'serviceable'],
  expected_price: '',
  delivery_location: '',
  expected_delivery_date: '',
  urgency: 'normal',
  description: '',
  contact_person: '',
  contact_phone: '',
  contact_email: ''
})

// 自动生成的标题
const autoGeneratedTitle = ref('')
const isManualTitle = ref(false)

// 验证规则
const rules = {
  type: [
    { required: true, message: '请选择需求类型', trigger: 'change' }
  ],
  material_name: [
    { required: true, message: '请输入航材名称', trigger: 'blur' }
  ],
  part_number: [
    { required: true, message: '请输入零件号', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择航材类别', trigger: 'change' }
  ],
  aircraft_type: [
    { required: true, message: '请选择适用机型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入需求数量', trigger: 'blur' }
  ],
  delivery_location: [
    { required: true, message: '请选择交付地点', trigger: 'change' }
  ],
  expected_delivery_date: [
    { required: true, message: '请选择期望交付日期', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入需求描述', trigger: 'blur' },
    { min: 10, message: '需求描述至少10个字符', trigger: 'blur' }
  ],
  contact_person: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contact_email: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 标题生成逻辑
const generateAutoTitle = () => {
  const typeMap = {
    'turnaround': '周转件需求',
    'consumable': '消耗件需求',
    'maintenance': '维修需求',
    'aog': 'AOG紧急'
  }
  
  const priorityMap = {
    'aog': '紧急',
    'high': '高优先级',
    'normal': '',
    'low': '低优先级'
  }
  
  const parts = []
  
  // 添加需求类型
  if (form.type && typeMap[form.type]) {
    parts.push(typeMap[form.type])
  }
  
  // 添加航材名称
  if (form.material_name && form.material_name.trim()) {
    parts.push(form.material_name.trim())
  }
  
  // 添加机型信息
  if (form.aircraft_type && form.aircraft_type !== 'other') {
    parts.push(form.aircraft_type)
  }
  
  // 添加优先级（仅当非普通优先级时）
  if (form.urgency && priorityMap[form.urgency]) {
    parts.push(priorityMap[form.urgency])
  }
  
  return parts.length > 0 ? parts.join('-') : '航材需求'
}

const updateAutoTitle = () => {
  if (!isManualTitle.value) {
    autoGeneratedTitle.value = generateAutoTitle()
  }
}

const onTitleInput = () => {
  isManualTitle.value = form.title && form.title.trim().length > 0
  if (!isManualTitle.value) {
    updateAutoTitle()
  }
}

// 监听需求类型变化，调整紧急程度
watch(() => form.type, (newType) => {
  if (newType === 'aog') {
    form.urgency = 'aog'
  } else if (form.urgency === 'aog') {
    form.urgency = 'normal'
  }
  updateAutoTitle()
})

// 监听props变化，填充表单数据
watch(() => props.demandData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    loadDemandData(newData)
  }
}, { immediate: true, deep: true })

// 加载需求数据到表单
const loadDemandData = (demandData) => {
  console.log('加载需求数据:', demandData)
  
  // 填充基本信息
  form.title = demandData.title || ''
  form.type = demandData.type || 'turnaround'
  form.urgency = demandData.priority || 'normal'
  
  // 填充航材信息
  if (demandData.material_info) {
    form.material_name = demandData.material_info.name || ''
    form.part_number = demandData.material_info.part_number || ''
    form.aircraft_type = demandData.material_info.aircraft_type || ''
  }
  
  // 填充需求信息
  if (demandData.requirements) {
    form.quantity = demandData.requirements.quantity || 1
    form.description = demandData.requirements.description || ''
    form.delivery_location = demandData.requirements.delivery_location || ''
    form.expected_price = demandData.requirements.expected_price || ''
    form.acceptable_conditions = demandData.requirements.acceptable_conditions || ['new', 'serviceable']
    
    // 处理日期
    if (demandData.requirements.expected_delivery_date) {
      try {
        form.expected_delivery_date = new Date(demandData.requirements.expected_delivery_date)
      } catch (e) {
        console.warn('日期解析失败:', e)
      }
    }
  }
  
  // 填充联系信息
  if (demandData.contact_info) {
    form.contact_person = demandData.contact_info.contact_person || ''
    form.contact_phone = demandData.contact_info.contact_phone || ''
    form.contact_email = demandData.contact_info.contact_email || ''
  }
  
  // 设置标题状态
  isManualTitle.value = !!(form.title && form.title.trim())
  updateAutoTitle()
}

// 方法
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    title: '',
    type: 'turnaround',
    material_name: '',
    part_number: '',
    category: '',
    aircraft_type: '',
    quantity: 1,
    acceptable_conditions: ['new', 'serviceable'],
    expected_price: '',
    delivery_location: '',
    expected_delivery_date: '',
    urgency: 'normal',
    description: '',
    contact_person: '',
    contact_phone: '',
    contact_email: ''
  })
  
  // 重置标题状态
  isManualTitle.value = false
  autoGeneratedTitle.value = ''
  updateAutoTitle()
}

const submitEdit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 准备提交数据
    const submitData = {
      title: form.title,
      type: form.type,
      priority: form.urgency === 'aog' ? 'urgent' : form.urgency === 'urgent' ? 'high' : 'normal',
      urgency: form.urgency,
      material_info: {
        part_number: form.part_number,
        name: form.material_name,
        category: form.category,
        aircraft_type: form.aircraft_type,
        manufacturer: ''
      },
      requirements: {
        quantity: form.quantity,
        acceptable_conditions: form.acceptable_conditions,
        expected_price: form.expected_price,
        delivery_location: form.delivery_location,
        expected_delivery_date: form.expected_delivery_date,
        description: form.description
      },
      contact_info: {
        contact_person: form.contact_person,
        contact_phone: form.contact_phone,
        contact_email: form.contact_email
      }
    }

    console.log('提交编辑数据:', submitData)
    
    // 调用实际的编辑API
    const { updateDemand } = await import('@/api/demands-enhanced')
    const response = await updateDemand(props.demandData.id, submitData)
    
    if (response.success) {
      ElMessage.success('需求编辑成功！')
      
      emit('success', {
        id: props.demandData.id,
        ...submitData
      })
      
      dialogVisible.value = false
    } else {
      ElMessage.error(response.message || '编辑需求失败')
    }
    
  } catch (error) {
    console.error('编辑需求失败:', error)
    ElMessage.error('编辑需求失败')
  } finally {
    submitting.value = false
  }
}

// 初始化时生成标题
updateAutoTitle()
</script>

<style scoped>
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-4 {
  gap: 1rem;
}
</style>