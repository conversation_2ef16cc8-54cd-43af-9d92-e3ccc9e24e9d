<template>
  <nav aria-label="Breadcrumb" class="breadcrumb-nav">
    <ol class="flex items-center space-x-2 text-sm">
      <!-- 首页链接 -->
      <li>
        <router-link
          to="/app/workspace"
          class="breadcrumb-item flex items-center text-gray-500 hover:text-primary-600 transition-colors"
        >
          <House class="w-4 h-4 mr-1" />
          工作台
        </router-link>
      </li>

      <!-- 面包屑分隔符和路径 -->
      <template v-for="(item, index) in breadcrumbItems" :key="index">
        <li class="flex items-center">
          <Right class="w-4 h-4 text-gray-400" />
        </li>
        <li>
          <router-link
            v-if="item.to && index < breadcrumbItems.length - 1"
            :to="item.to"
            class="breadcrumb-item text-gray-500 hover:text-primary-600 transition-colors"
          >
            {{ item.name }}
          </router-link>
          <span
            v-else
            class="text-gray-900 font-medium"
          >
            {{ item.name }}
          </span>
        </li>
      </template>
    </ol>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
// 图标已全局注册，无需单独导入

const route = useRoute()

// 定义路由映射关系
const routeMapping = {
  'workspace': { name: '工作台', to: '/app/workspace' },
  'marketplace': { name: '航材市场', to: '/app/marketplace' },
  'orders': { name: '订单管理', to: '/app/orders' },
  'inventory': { name: '库存管理', to: '/app/inventory' },
  'maintenance': { name: '维修管理', to: '/app/maintenance' },
  'quality': { name: '质量管理', to: '/app/quality' },
  'analytics': { name: '分析报告', to: '/app/analytics' },
  'notifications': { name: '通知管理', to: '/app/notifications' },
  'demands': { name: '需求管理', to: '/app/demands' },
  'shared-materials': { name: '共享航材', to: '/shared-materials' }
}

// 计算面包屑路径
const breadcrumbItems = computed(() => {
  const path = route.path
  const pathSegments = path.split('/').filter(Boolean)
  const items = []

  // 处理主应用内的路由
  if (pathSegments[0] === 'app' && pathSegments.length > 1) {
    const mainSection = pathSegments[1]
    
    // 添加主要页面
    if (routeMapping[mainSection]) {
      items.push(routeMapping[mainSection])
    }

    // 处理子路由
    if (pathSegments.length > 2) {
      // 这里可以根据具体的子路由添加更多面包屑项
      const subSection = pathSegments[2]
      if (subSection === 'detail' && route.params.id) {
        items.push({ name: `详情 #${route.params.id}` })
      } else if (subSection === 'edit' && route.params.id) {
        items.push({ name: `编辑 #${route.params.id}` })
      } else if (subSection === 'create') {
        items.push({ name: '创建' })
      }
    }
  }
  // 处理共享航材页面
  else if (pathSegments[0] === 'shared-materials') {
    items.push(routeMapping['shared-materials'])
    
    if (pathSegments.length > 1 && route.params.id) {
      items.push({ name: `航材详情 #${route.params.id}` })
    }
  }
  // 处理其他特殊页面
  else if (path === '/') {
    return [] // 首页不显示面包屑
  }

  return items
})
</script>

<style scoped>
.breadcrumb-nav {
  @apply bg-white rounded-lg shadow-sm border border-gray-100 px-4 py-3;
}

.breadcrumb-item {
  @apply font-medium;
}

.breadcrumb-item:hover {
  @apply underline;
}
</style>