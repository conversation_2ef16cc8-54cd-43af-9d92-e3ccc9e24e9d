<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="500px"
    :before-close="handleClose"
  >
    <div class="batch-action-content">
      <div class="action-info">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <div class="info-text">
          <p>您即将对以下 <strong>{{ users.length }}</strong> 个用户执行 <strong>{{ getActionLabel() }}</strong> 操作：</p>
        </div>
      </div>

      <div class="users-list">
        <el-scrollbar max-height="200px">
          <div
            v-for="user in users"
            :key="user.id"
            class="user-item"
          >
            <el-avatar :size="32" class="user-avatar">
              {{ user.real_name ? user.real_name.charAt(0) : user.username.charAt(0) }}
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ user.real_name || user.username }}</div>
              <div class="user-meta">{{ user.email }} | {{ user.company_name }}</div>
            </div>
            <el-tag :type="getStatusTagType(user.status)" size="small">
              {{ getStatusLabel(user.status) }}
            </el-tag>
          </div>
        </el-scrollbar>
      </div>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="操作原因" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入操作原因（必填）"
          />
        </el-form-item>
      </el-form>

      <div class="warning-text">
        <el-icon><InfoFilled /></el-icon>
        <span>{{ getWarningText() }}</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          :type="getButtonType()" 
          :loading="loading" 
          @click="handleConfirm"
        >
          确认{{ getActionLabel() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  action: {
    type: String,
    default: ''
  },
  users: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirmed'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  reason: ''
})

// 表单验证规则
const rules = {
  reason: [
    { required: true, message: '请输入操作原因', trigger: 'blur' },
    { min: 5, message: '操作原因至少5个字符', trigger: 'blur' }
  ]
}

// 获取对话框标题
const getDialogTitle = () => {
  const actionMap = {
    activate: '批量激活用户',
    deactivate: '批量停用用户',
    suspend: '批量暂停用户',
    delete: '批量删除用户'
  }
  return actionMap[props.action] || '批量操作'
}

// 获取操作标签
const getActionLabel = () => {
  const actionMap = {
    activate: '激活',
    deactivate: '停用',
    suspend: '暂停',
    delete: '删除'
  }
  return actionMap[props.action] || '操作'
}

// 获取按钮类型
const getButtonType = () => {
  const typeMap = {
    activate: 'success',
    deactivate: 'warning',
    suspend: 'warning',
    delete: 'danger'
  }
  return typeMap[props.action] || 'primary'
}

// 获取警告文本
const getWarningText = () => {
  const warningMap = {
    activate: '激活后用户将能够正常登录和使用系统功能。',
    deactivate: '停用后用户将无法登录系统，但数据会保留。',
    suspend: '暂停后用户将无法登录系统，需要管理员手动恢复。',
    delete: '删除后用户数据将被永久删除，此操作不可恢复！'
  }
  return warningMap[props.action] || '请确认您的操作。'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '暂停',
    deleted: '已删除'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    suspended: 'warning',
    deleted: 'danger'
  }
  return statusMap[status] || ''
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  form.reason = ''
  visible.value = false
}

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    emit('confirmed', form.reason)
    handleClose()
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.batch-action-content {
  padding: 0 4px;
}

.action-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px;
  background: #fdf6ec;
  border: 1px solid #fcdcb8;
  border-radius: 4px;
}

.warning-icon {
  color: #e6a23c;
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.info-text p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.users-list {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  margin-right: 12px;
  background: #409eff;
  color: white;
  font-weight: 500;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.user-meta {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.warning-text {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.warning-text .el-icon {
  color: #409eff;
  margin-right: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-form-item {
  margin-bottom: 16px;
}
</style>
