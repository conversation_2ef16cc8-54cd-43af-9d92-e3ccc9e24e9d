<template>
  <el-dialog
    v-model="visible"
    title="通知详情"
    width="60%"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 通知头部信息 -->
      <div class="notification-header mb-6">
        <div class="flex items-start space-x-4">
          <div class="notification-icon">
            <component 
              :is="getNotificationIcon(notification.type)" 
              :class="getIconClass(notification.priority)"
              class="w-8 h-8"
            />
          </div>
          <div class="flex-1">
            <div class="flex justify-between items-start mb-2">
              <h2 class="text-xl font-bold text-gray-800">{{ notification.title }}</h2>
              <div class="flex space-x-2">
                <el-tag :type="getTypeColor(notification.type)" size="small">
                  {{ getTypeText(notification.type) }}
                </el-tag>
                <el-tag :type="getPriorityColor(notification.priority)" size="small">
                  {{ getPriorityText(notification.priority) }}
                </el-tag>
                <el-tag :type="notification.read ? 'success' : 'warning'" size="small">
                  {{ notification.read ? '已读' : '未读' }}
                </el-tag>
              </div>
            </div>
            
            <div class="text-sm text-gray-600 space-y-1">
              <div>发送时间: {{ formatDateTime(notification.created_at) }}</div>
              <div v-if="notification.read_at">
                阅读时间: {{ formatDateTime(notification.read_at) }}
              </div>
              <div v-if="notification.sender">
                发送者: {{ notification.sender }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知内容 -->
      <div class="notification-content mb-6">
        <h3 class="text-lg font-semibold mb-3">通知内容</h3>
        <div class="content-text p-4 bg-gray-50 rounded-lg">
          <p class="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {{ notification.content }}
          </p>
        </div>
      </div>

      <!-- 关联信息 -->
      <div v-if="notification.metadata" class="related-info mb-6">
        <h3 class="text-lg font-semibold mb-3">关联信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="notification.metadata.order_id" class="info-item">
            <span class="label">关联订单:</span>
            <el-link 
              type="primary" 
              @click="viewOrder(notification.metadata.order_id)"
              class="ml-2"
            >
              {{ notification.metadata.order_id }}
            </el-link>
          </div>
          
          <div v-if="notification.metadata.workflow_id" class="info-item">
            <span class="label">关联工作流:</span>
            <el-link 
              type="primary" 
              @click="viewWorkflow(notification.metadata.workflow_id)"
              class="ml-2"
            >
              {{ notification.metadata.workflow_id }}
            </el-link>
          </div>
          
          <div v-if="notification.metadata.part_number" class="info-item">
            <span class="label">零件号:</span>
            <span class="value">{{ notification.metadata.part_number }}</span>
          </div>
          
          <div v-if="notification.metadata.tracking_number" class="info-item">
            <span class="label">物流单号:</span>
            <el-link 
              type="primary" 
              @click="viewTracking(notification.metadata.tracking_number)"
              class="ml-2"
            >
              {{ notification.metadata.tracking_number }}
            </el-link>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div v-if="notification.actions && notification.actions.length > 0" class="action-history mb-6">
        <h3 class="text-lg font-semibold mb-3">操作记录</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(action, index) in notification.actions"
            :key="index"
            :timestamp="formatDateTime(action.timestamp)"
            :type="getActionType(action.type)"
          >
            <div class="action-item">
              <div class="font-medium">{{ action.title }}</div>
              <div class="text-gray-600 text-sm mt-1">{{ action.description }}</div>
              <div v-if="action.operator" class="text-gray-500 text-xs mt-1">
                操作人: {{ action.operator }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 快速操作 -->
      <div v-if="hasQuickActions" class="quick-actions">
        <h3 class="text-lg font-semibold mb-3">快速操作</h3>
        <div class="flex space-x-3">
          <el-button 
            v-if="notification.metadata.workflow_id && notification.type === 'approval'"
            type="primary" 
            @click="handleWorkflow"
          >
            立即审批
          </el-button>
          
          <el-button 
            v-if="notification.metadata.order_id"
            @click="viewOrderDetail"
          >
            查看订单
          </el-button>
          
          <el-button 
            v-if="notification.type === 'inventory'"
            type="warning"
            @click="handleInventoryWarning"
          >
            处理库存预警
          </el-button>
          
          <el-button 
            v-if="notification.type === 'aog'"
            type="danger"
            @click="handleAOG"
          >
            处理AOG
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="!notification.read"
          type="primary" 
          @click="markAsRead"
        >
          标记已读
        </el-button>
        <el-button 
          type="danger" 
          @click="deleteNotification"
        >
          删除通知
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Bell, ShoppingCart, Document, Warning, Notification, Setting
} from '@element-plus/icons-vue'
import { notificationsApi } from '@/api/notifications'

const props = defineProps({
  modelValue: Boolean,
  notification: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'read', 'delete'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 计算属性
const hasQuickActions = computed(() => {
  const { type, metadata } = props.notification
  return (
    (type === 'approval' && metadata?.workflow_id) ||
    (type === 'inventory') ||
    (type === 'aog') ||
    metadata?.order_id
  )
})

// 方法
const markAsRead = async () => {
  try {
    // await notificationsApi.markNotificationAsRead(props.notification.id)
    emit('read', props.notification.id)
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNotification = async () => {
  try {
    await ElMessageBox.confirm(
      '确定删除此通知吗？',
      '确认删除',
      { type: 'warning' }
    )
    
    // await notificationsApi.deleteNotification(props.notification.id)
    emit('delete', props.notification.id)
    ElMessage.success('通知已删除')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleWorkflow = () => {
  const workflowId = props.notification.metadata.workflow_id
  ElMessage.info(`跳转到工作流处理: ${workflowId}`)
  // 实际项目中跳转到工作流处理页面
}

const viewOrderDetail = () => {
  const orderId = props.notification.metadata.order_id
  ElMessage.info(`查看订单详情: ${orderId}`)
  // 实际项目中跳转到订单详情页面
}

const handleInventoryWarning = () => {
  ElMessage.info('跳转到库存管理页面')
  // 实际项目中跳转到库存管理页面
}

const handleAOG = () => {
  ElMessage.info('跳转到AOG处理页面')
  // 实际项目中跳转到AOG紧急处理页面
}

const viewOrder = (orderId) => {
  ElMessage.info(`查看订单: ${orderId}`)
}

const viewWorkflow = (workflowId) => {
  ElMessage.info(`查看工作流: ${workflowId}`)
}

const viewTracking = (trackingNumber) => {
  ElMessage.info(`查看物流跟踪: ${trackingNumber}`)
}

const handleClose = () => {
  visible.value = false
}

// 辅助函数
const getNotificationIcon = (type) => {
  const icons = {
    system: Setting,
    order: ShoppingCart,
    approval: Document,
    inventory: Warning,
    aog: Notification
  }
  return icons[type] || Bell
}

const getIconClass = (priority) => {
  const classes = {
    urgent: 'text-red-500',
    high: 'text-orange-500',
    normal: 'text-blue-500',
    low: 'text-gray-500'
  }
  return classes[priority] || classes.normal
}

const getTypeColor = (type) => {
  const colors = {
    system: 'info',
    order: 'primary',
    approval: 'warning',
    inventory: 'danger',
    aog: 'danger'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    system: '系统',
    order: '订单',
    approval: '审批',
    inventory: '库存',
    aog: 'AOG'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return texts[priority] || priority
}

const getActionType = (actionType) => {
  const types = {
    create: 'primary',
    update: 'info',
    approve: 'success',
    reject: 'danger',
    complete: 'success'
  }
  return types[actionType] || 'primary'
}

const formatDateTime = (datetime) => {
  return datetime ? new Date(datetime).toLocaleString('zh-CN') : ''
}
</script>

<style scoped>
.notification-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
}

.content-text {
  border: 1px solid #e3f2fd;
  font-size: 14px;
  line-height: 1.6;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.value {
  color: #333;
  margin-left: 8px;
}

.action-item {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.quick-actions {
  background-color: #f0f9ff;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e3f2fd;
}

.dialog-footer {
  text-align: right;
}
</style>