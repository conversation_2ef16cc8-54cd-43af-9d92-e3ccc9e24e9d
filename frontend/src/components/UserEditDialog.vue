<template>
  <el-dialog
    v-model="visible"
    title="编辑用户"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="real_name">
            <el-input v-model="form.real_name" placeholder="请输入真实姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入电话号码" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="公司名称" prop="company_name">
            <el-input v-model="form.company_name" placeholder="请输入公司名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户类型" prop="user_type">
            <el-select v-model="form.user_type" placeholder="请选择用户类型" style="width: 100%">
              <el-option
                v-for="type in userTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="活跃" value="active" />
              <el-option label="非活跃" value="inactive" />
              <el-option label="暂停" value="suspended" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司类型" prop="company_type">
            <el-select v-model="form.company_type" placeholder="请选择公司类型" style="width: 100%">
              <el-option label="外部公司" value="external" />
              <el-option label="内部员工" value="internal" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权限等级" prop="permission_level">
            <el-select v-model="form.permission_level" placeholder="请选择权限等级" style="width: 100%">
              <el-option label="普通用户 (1级)" :value="1" />
              <el-option label="平台员工 (2级)" :value="2" />
              <el-option label="管理员 (3级)" :value="3" />
              <el-option label="超级管理员 (4级)" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 角色权限配置 -->
      <el-divider content-position="left">角色权限配置</el-divider>
      <el-form-item label="用户角色" prop="role_id">
        <el-select v-model="form.role_id" placeholder="请选择用户角色" style="width: 100%">
          <el-option
            v-for="role in roles"
            :key="role.id"
            :label="role.display_name"
            :value="role.id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>{{ role.display_name }}</span>
              <el-tag size="small" :type="role.category === 'internal' ? 'warning' : 'info'">
                {{ role.category === 'internal' ? '内部' : '外部' }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 备注信息 -->
      <el-divider content-position="left">备注信息</el-divider>
      <el-form-item label="修改原因">
        <el-input
          v-model="form.update_reason"
          type="textarea"
          :rows="3"
          placeholder="请输入修改原因（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { usersApi } from '@/api/users'
import { rolesApi } from '@/api/roles'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'updated'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const loading = ref(false)
const userTypes = ref([])
const roles = ref([])

// 表单数据
const form = reactive({
  username: '',
  real_name: '',
  email: '',
  phone: '',
  company_name: '',
  user_type: '',
  status: '',
  company_type: '',
  permission_level: 1,
  role_id: null,
  update_reason: ''
})

// 表单验证规则
const rules = {
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  user_type: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  company_type: [
    { required: true, message: '请选择公司类型', trigger: 'change' }
  ],
  permission_level: [
    { required: true, message: '请选择权限等级', trigger: 'change' }
  ]
}

// 监听用户变化，初始化表单
watch(() => props.user, (newUser) => {
  if (newUser && props.modelValue) {
    initForm(newUser)
  }
}, { immediate: true })

// 初始化
onMounted(() => {
  loadUserTypes()
  loadRoles()
})

// 初始化表单
const initForm = (user) => {
  Object.assign(form, {
    username: user.username || '',
    real_name: user.real_name || '',
    email: user.email || '',
    phone: user.phone || '',
    company_name: user.company_name || '',
    user_type: user.user_type || '',
    status: user.status || '',
    company_type: user.company_type || '',
    permission_level: user.permission_level || 1,
    role_id: user.role_id || null,
    update_reason: ''
  })
}

// 加载用户类型
const loadUserTypes = async () => {
  try {
    const response = await usersApi.getUserTypes()
    if (response.success) {
      userTypes.value = response.body.user_types
    }
  } catch (error) {
    console.error('加载用户类型失败:', error)
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    const response = await rolesApi.getRoles()
    if (response.success) {
      roles.value = response.body.roles
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 准备更新数据
    const updateData = {
      real_name: form.real_name,
      email: form.email,
      phone: form.phone,
      company_name: form.company_name,
      user_type: form.user_type,
      status: form.status,
      company_type: form.company_type,
      permission_level: form.permission_level,
      role_id: form.role_id
    }
    
    const response = await usersApi.updateUser(props.user.id, updateData)
    
    if (response.success) {
      ElMessage.success('用户信息更新成功')
      emit('updated')
      handleClose()
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新用户信息失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-divider {
  margin: 20px 0;
}

.el-form-item {
  margin-bottom: 18px;
}
</style>
