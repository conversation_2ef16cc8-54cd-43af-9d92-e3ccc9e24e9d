<!--
工作流状态同步组件
版本: 1.0
创建时间: 2025-07-19

用于实时同步不同角色之间的工作流状态
特点：
1. 实时状态同步
2. 工作流进度跟踪
3. 状态变更通知
4. 异常状态处理
-->

<template>
  <div class="workflow-sync">
    <!-- 状态同步头部 -->
    <div class="sync-header">
      <div class="flex items-center justify-between">
        <div class="header-info">
          <h2 class="text-xl font-semibold text-gray-800">工作流同步</h2>
          <p class="text-gray-600 mt-1">实时同步各角色工作状态</p>
        </div>
        <div class="sync-status" :class="getSyncStatusClass">
          <div class="status-indicator"></div>
          <span class="status-text">{{ syncStatusText }}</span>
          <el-button size="small" @click="forceSyncAll">
            <Refresh class="w-4 h-4 mr-1" />
            强制同步
          </el-button>
        </div>
      </div>
    </div>

    <!-- 工作流概览 -->
    <div class="workflow-overview">
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        <div 
          v-for="workflow in workflows" 
          :key="workflow.id"
          class="workflow-card"
          :class="getWorkflowCardClass(workflow.status)"
        >
          <div class="workflow-header">
            <div class="workflow-info">
              <h3 class="workflow-title">{{ workflow.title }}</h3>
              <p class="workflow-type">{{ workflow.type }}</p>
            </div>
            <div class="workflow-status" :class="getStatusBadgeClass(workflow.status)">
              {{ getStatusLabel(workflow.status) }}
            </div>
          </div>
          
          <div class="workflow-progress">
            <div class="progress-info">
              <span class="progress-label">进度</span>
              <span class="progress-percentage">{{ workflow.progress }}%</span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${workflow.progress}%` }"
                :class="getProgressBarClass(workflow.status)"
              ></div>
            </div>
          </div>

          <div class="workflow-participants">
            <div class="participant-list">
              <div 
                v-for="participant in workflow.participants" 
                :key="participant.role"
                class="participant-item"
                :class="getParticipantClass(participant.status)"
                :title="`${participant.roleLabel} - ${participant.status}`"
              >
                <component :is="getRoleIcon(participant.role)" class="w-4 h-4" />
                <span class="participant-name">{{ participant.name }}</span>
                <div class="participant-status-icon" :class="getParticipantStatusClass(participant.status)">
                  <component :is="getParticipantStatusIcon(participant.status)" class="w-3 h-3" />
                </div>
              </div>
            </div>
          </div>

          <div class="workflow-actions">
            <el-button 
              size="small" 
              @click="viewWorkflowDetails(workflow)"
            >
              详情
            </el-button>
            <el-button 
              v-if="canSync(workflow)" 
              size="small" 
              type="primary"
              @click="syncWorkflow(workflow)"
            >
              同步
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步日志 -->
    <div class="sync-logs">
      <div class="logs-header">
        <h3 class="text-lg font-semibold text-gray-800">同步日志</h3>
        <div class="logs-actions">
          <el-select v-model="logFilter" size="small" @change="filterLogs">
            <el-option label="全部" value="all" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="error" />
            <el-option label="警告" value="warning" />
          </el-select>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </div>
      
      <div class="logs-content">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id"
          class="log-item"
          :class="getLogItemClass(log.type)"
        >
          <div class="log-icon">
            <component :is="getLogIcon(log.type)" class="w-4 h-4" />
          </div>
          <div class="log-content">
            <div class="log-header">
              <span class="log-title">{{ log.title }}</span>
              <span class="log-time">{{ formatTime(log.time) }}</span>
            </div>
            <p class="log-message">{{ log.message }}</p>
            <div v-if="log.details" class="log-details">
              <el-collapse>
                <el-collapse-item title="详细信息">
                  <pre class="log-details-content">{{ log.details }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流详情对话框 -->
    <el-dialog 
      v-model="workflowDialogVisible" 
      :title="`工作流详情 - ${selectedWorkflow?.title}`"
      width="70%"
      :before-close="handleDialogClose"
    >
      <div v-if="selectedWorkflow" class="workflow-details">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>工作流ID:</label>
              <span>{{ selectedWorkflow.id }}</span>
            </div>
            <div class="info-item">
              <label>类型:</label>
              <span>{{ selectedWorkflow.type }}</span>
            </div>
            <div class="info-item">
              <label>状态:</label>
              <span class="status-badge" :class="getStatusBadgeClass(selectedWorkflow.status)">
                {{ getStatusLabel(selectedWorkflow.status) }}
              </span>
            </div>
            <div class="info-item">
              <label>进度:</label>
              <span>{{ selectedWorkflow.progress }}%</span>
            </div>
            <div class="info-item">
              <label>创建时间:</label>
              <span>{{ formatTime(selectedWorkflow.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>最后同步:</label>
              <span>{{ formatTime(selectedWorkflow.lastSyncAt) }}</span>
            </div>
          </div>
        </div>

        <!-- 参与者状态 -->
        <div class="detail-section">
          <h4 class="section-title">参与者状态</h4>
          <div class="participants-timeline">
            <div 
              v-for="participant in selectedWorkflow.participants" 
              :key="participant.role"
              class="timeline-item"
              :class="getTimelineItemClass(participant.status)"
            >
              <div class="timeline-marker" :class="getTimelineMarkerClass(participant.status)">
                <component :is="getRoleIcon(participant.role)" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="participant-header">
                  <h5 class="participant-role">{{ participant.roleLabel }}</h5>
                  <span class="participant-status" :class="getParticipantStatusClass(participant.status)">
                    {{ getParticipantStatusLabel(participant.status) }}
                  </span>
                </div>
                <p class="participant-name">{{ participant.name }}</p>
                <div class="participant-meta">
                  <span v-if="participant.completedAt" class="completion-time">
                    完成时间: {{ formatTime(participant.completedAt) }}
                  </span>
                  <span v-if="participant.estimatedTime" class="estimated-time">
                    预计用时: {{ participant.estimatedTime }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 同步历史 -->
        <div class="detail-section">
          <h4 class="section-title">同步历史</h4>
          <div class="sync-history">
            <div 
              v-for="sync in selectedWorkflow.syncHistory" 
              :key="sync.id"
              class="sync-record"
              :class="getSyncRecordClass(sync.status)"
            >
              <div class="sync-record-header">
                <div class="sync-info">
                  <span class="sync-action">{{ sync.action }}</span>
                  <span class="sync-status" :class="getSyncStatusClass(sync.status)">
                    {{ getSyncStatusLabel(sync.status) }}
                  </span>
                </div>
                <span class="sync-time">{{ formatTime(sync.time) }}</span>
              </div>
              <p v-if="sync.message" class="sync-message">{{ sync.message }}</p>
              <div v-if="sync.changes" class="sync-changes">
                <el-tag 
                  v-for="change in sync.changes" 
                  :key="change"
                  size="small"
                  class="mr-2"
                >
                  {{ change }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="workflowDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="canSync(selectedWorkflow)" 
            type="primary"
            @click="syncWorkflowFromDialog"
          >
            立即同步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { 
  Refresh, CheckCircle, Clock, CircleClose, Warning, InfoFilled,
  Avatar, Tools, Truck, Monitor, CheckCircle
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  userRole: {
    type: String,
    required: true
  }
})

// Stores
const userStore = useUserStore()

// 响应式数据
const workflows = ref([])
const syncLogs = ref([])
const filteredLogs = ref([])
const logFilter = ref('all')
const selectedWorkflow = ref(null)
const workflowDialogVisible = ref(false)
const syncInterval = ref(null)
const lastSyncTime = ref(null)
const syncStatus = ref('connected') // connected, disconnected, syncing

// 计算属性
const syncStatusText = computed(() => {
  const statusTexts = {
    connected: '已连接',
    disconnected: '连接断开',
    syncing: '同步中...'
  }
  return statusTexts[syncStatus.value] || '未知状态'
})

const getSyncStatusClass = computed(() => {
  return {
    'sync-connected': syncStatus.value === 'connected',
    'sync-disconnected': syncStatus.value === 'disconnected',
    'sync-syncing': syncStatus.value === 'syncing'
  }
})

// 方法
const getRoleIcon = (role) => {
  const icons = {
    airline_buyer: Avatar,
    platform_staff: Monitor,
    maintenance_engineer: Tools,
    logistics_specialist: Truck
  }
  return icons[role] || Avatar
}

const getWorkflowCardClass = (status) => {
  const classes = {
    active: 'workflow-active',
    pending: 'workflow-pending',
    completed: 'workflow-completed',
    error: 'workflow-error'
  }
  return classes[status] || ''
}

const getStatusBadgeClass = (status) => {
  const classes = {
    active: 'status-active',
    pending: 'status-pending',
    completed: 'status-completed',
    error: 'status-error'
  }
  return classes[status] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    active: '进行中',
    pending: '等待中',
    completed: '已完成',
    error: '异常'
  }
  return labels[status] || status
}

const getProgressBarClass = (status) => {
  const classes = {
    active: 'progress-active',
    pending: 'progress-pending',
    completed: 'progress-completed',
    error: 'progress-error'
  }
  return classes[status] || ''
}

const getParticipantClass = (status) => {
  return {
    'participant-completed': status === 'completed',
    'participant-active': status === 'active',
    'participant-pending': status === 'pending',
    'participant-error': status === 'error'
  }
}

const getParticipantStatusClass = (status) => {
  const classes = {
    completed: 'status-icon-completed',
    active: 'status-icon-active',
    pending: 'status-icon-pending',
    error: 'status-icon-error'
  }
  return classes[status] || ''
}

const getParticipantStatusIcon = (status) => {
  const icons = {
    completed: CheckCircle,
    active: Clock,
    pending: Clock,
    error: CircleClose
  }
  return icons[status] || Clock
}

const getParticipantStatusLabel = (status) => {
  const labels = {
    completed: '已完成',
    active: '进行中',
    pending: '等待中',
    error: '异常'
  }
  return labels[status] || status
}

const getLogItemClass = (type) => {
  const classes = {
    success: 'log-success',
    error: 'log-error',
    warning: 'log-warning',
    info: 'log-info'
  }
  return classes[type] || ''
}

const getLogIcon = (type) => {
  const icons = {
    success: CheckCircle,
    error: CircleClose,
    warning: Warning,
    info: InfoFilled
  }
  return icons[type] || InfoFilled
}

const getTimelineItemClass = (status) => {
  return {
    'timeline-completed': status === 'completed',
    'timeline-active': status === 'active',
    'timeline-pending': status === 'pending',
    'timeline-error': status === 'error'
  }
}

const getTimelineMarkerClass = (status) => {
  return {
    'marker-completed': status === 'completed',
    'marker-active': status === 'active',
    'marker-pending': status === 'pending',
    'marker-error': status === 'error'
  }
}

const getSyncRecordClass = (status) => {
  const classes = {
    success: 'sync-success',
    error: 'sync-error',
    warning: 'sync-warning'
  }
  return classes[status] || ''
}

const getSyncStatusClass = (status) => {
  const classes = {
    success: 'sync-status-success',
    error: 'sync-status-error',
    warning: 'sync-status-warning'
  }
  return classes[status] || ''
}

const getSyncStatusLabel = (status) => {
  const labels = {
    success: '成功',
    error: '失败',
    warning: '警告'
  }
  return labels[status] || status
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const canSync = (workflow) => {
  return workflow.status === 'active' || workflow.status === 'error'
}

const viewWorkflowDetails = (workflow) => {
  selectedWorkflow.value = workflow
  workflowDialogVisible.value = true
}

const syncWorkflow = async (workflow) => {
  try {
    syncStatus.value = 'syncing'
    
    // 模拟同步操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新工作流状态
    const randomProgress = Math.min(workflow.progress + Math.floor(Math.random() * 20) + 10, 100)
    workflow.progress = randomProgress
    workflow.lastSyncAt = new Date().toISOString()
    
    if (randomProgress >= 100) {
      workflow.status = 'completed'
    }
    
    // 添加同步日志
    addSyncLog({
      type: 'success',
      title: '工作流同步成功',
      message: `工作流 "${workflow.title}" 同步完成，进度更新为 ${randomProgress}%`,
      workflowId: workflow.id
    })
    
    ElMessage.success('工作流同步成功')
    
  } catch (error) {
    addSyncLog({
      type: 'error',
      title: '工作流同步失败',
      message: `工作流 "${workflow.title}" 同步失败: ${error.message}`,
      workflowId: workflow.id
    })
    
    ElMessage.error('工作流同步失败')
  } finally {
    syncStatus.value = 'connected'
  }
}

const syncWorkflowFromDialog = () => {
  if (selectedWorkflow.value) {
    syncWorkflow(selectedWorkflow.value)
  }
}

const forceSyncAll = async () => {
  try {
    syncStatus.value = 'syncing'
    
    const activeWorkflows = workflows.value.filter(w => canSync(w))
    
    for (const workflow of activeWorkflows) {
      await syncWorkflow(workflow)
    }
    
    ElMessage.success(`已同步 ${activeWorkflows.length} 个工作流`)
    
  } catch (error) {
    ElMessage.error('批量同步失败')
  } finally {
    syncStatus.value = 'connected'
  }
}

const addSyncLog = (logData) => {
  const log = {
    id: Date.now(),
    time: new Date().toISOString(),
    ...logData
  }
  
  syncLogs.value.unshift(log)
  
  // 限制日志数量
  if (syncLogs.value.length > 100) {
    syncLogs.value = syncLogs.value.slice(0, 100)
  }
  
  filterLogs()
}

const filterLogs = () => {
  if (logFilter.value === 'all') {
    filteredLogs.value = syncLogs.value
  } else {
    filteredLogs.value = syncLogs.value.filter(log => log.type === logFilter.value)
  }
}

const clearLogs = () => {
  syncLogs.value = []
  filteredLogs.value = []
  ElMessage.success('日志已清空')
}

const handleDialogClose = () => {
  workflowDialogVisible.value = false
  selectedWorkflow.value = null
}

const startAutoSync = () => {
  syncInterval.value = setInterval(async () => {
    // 模拟自动同步
    const activeWorkflows = workflows.value.filter(w => w.status === 'active')
    
    for (const workflow of activeWorkflows) {
      // 随机更新进度
      if (Math.random() > 0.7) {
        const progressIncrease = Math.floor(Math.random() * 5) + 1
        workflow.progress = Math.min(workflow.progress + progressIncrease, 100)
        workflow.lastSyncAt = new Date().toISOString()
        
        if (workflow.progress >= 100) {
          workflow.status = 'completed'
          
          addSyncLog({
            type: 'success',
            title: '工作流自动完成',
            message: `工作流 "${workflow.title}" 已自动完成`,
            workflowId: workflow.id
          })
        }
      }
    }
    
    lastSyncTime.value = new Date().toISOString()
  }, 10000) // 每10秒检查一次
}

const stopAutoSync = () => {
  if (syncInterval.value) {
    clearInterval(syncInterval.value)
    syncInterval.value = null
  }
}

// 加载工作流数据
const loadWorkflows = () => {
  workflows.value = [
    {
      id: 'WF-001',
      title: '订单 PO-2024-001 处理流程',
      type: '订单处理',
      status: 'active',
      progress: 60,
      createdAt: '2024-07-19T08:00:00',
      lastSyncAt: '2024-07-19T10:30:00',
      participants: [
        {
          role: 'airline_buyer',
          roleLabel: '航空公司采购员',
          name: '张三',
          status: 'completed',
          completedAt: '2024-07-19T08:30:00'
        },
        {
          role: 'platform_staff',
          roleLabel: '平台员工',
          name: '李四',
          status: 'completed',
          completedAt: '2024-07-19T09:15:00'
        },
        {
          role: 'maintenance_engineer',
          roleLabel: '维修工程师',
          name: '王五',
          status: 'active'
        },
        {
          role: 'logistics_specialist',
          roleLabel: '物流专员',
          name: '赵六',
          status: 'pending'
        }
      ],
      syncHistory: [
        {
          id: '1',
          action: '状态同步',
          status: 'success',
          time: '2024-07-19T10:30:00',
          message: '工作流状态已同步',
          changes: ['进度更新: 60%', '当前处理人: 王五']
        },
        {
          id: '2',
          action: '参与者状态更新',
          status: 'success',
          time: '2024-07-19T09:15:00',
          message: '平台员工审核完成',
          changes: ['李四状态: 已完成']
        }
      ]
    },
    {
      id: 'WF-002',
      title: '紧急航材需求 AOG-001',
      type: 'AOG处理',
      status: 'active',
      progress: 85,
      createdAt: '2024-07-19T07:00:00',
      lastSyncAt: '2024-07-19T10:25:00',
      participants: [
        {
          role: 'airline_buyer',
          roleLabel: '航空公司采购员',
          name: '孙七',
          status: 'completed',
          completedAt: '2024-07-19T07:15:00'
        },
        {
          role: 'platform_staff',
          roleLabel: '平台员工',
          name: '周八',
          status: 'completed',
          completedAt: '2024-07-19T07:30:00'
        },
        {
          role: 'maintenance_engineer',
          roleLabel: '维修工程师',
          name: '吴九',
          status: 'completed',
          completedAt: '2024-07-19T08:45:00'
        },
        {
          role: 'logistics_specialist',
          roleLabel: '物流专员',
          name: '郑十',
          status: 'active'
        }
      ],
      syncHistory: [
        {
          id: '3',
          action: '紧急同步',
          status: 'success',
          time: '2024-07-19T10:25:00',
          message: 'AOG工作流紧急同步完成',
          changes: ['进度更新: 85%', '当前阶段: 紧急配送']
        }
      ]
    },
    {
      id: 'WF-003',
      title: '库存共享申请 INV-002',
      type: '库存共享',
      status: 'completed',
      progress: 100,
      createdAt: '2024-07-18T14:00:00',
      lastSyncAt: '2024-07-19T09:00:00',
      participants: [
        {
          role: 'airline_buyer',
          roleLabel: '航空公司采购员',
          name: '钱一',
          status: 'completed',
          completedAt: '2024-07-18T14:30:00'
        },
        {
          role: 'platform_staff',
          roleLabel: '平台员工',
          name: '赵二',
          status: 'completed',
          completedAt: '2024-07-18T16:00:00'
        }
      ],
      syncHistory: [
        {
          id: '4',
          action: '工作流完成',
          status: 'success',
          time: '2024-07-19T09:00:00',
          message: '库存共享申请已完成',
          changes: ['状态: 已完成', '进度: 100%']
        }
      ]
    }
  ]
}

// 生命周期
onMounted(() => {
  loadWorkflows()
  startAutoSync()
  
  // 添加初始日志
  addSyncLog({
    type: 'info',
    title: '同步服务启动',
    message: '工作流同步服务已启动，开始监控状态变更'
  })
})

onUnmounted(() => {
  stopAutoSync()
})
</script>

<style scoped>
.workflow-sync {
  @apply space-y-6;
}

.sync-header {
  @apply modern-card p-6;
}

.sync-status {
  @apply flex items-center space-x-3;
}

.status-indicator {
  @apply w-3 h-3 rounded-full;
}

.sync-connected .status-indicator {
  @apply bg-success-500;
}

.sync-disconnected .status-indicator {
  @apply bg-red-500;
}

.sync-syncing .status-indicator {
  @apply bg-warning-500 animate-pulse;
}

.status-text {
  @apply text-sm font-medium;
}

.sync-connected .status-text {
  @apply text-success-600;
}

.sync-disconnected .status-text {
  @apply text-red-600;
}

.sync-syncing .status-text {
  @apply text-warning-600;
}

/* 工作流卡片样式 */
.workflow-overview {
  @apply space-y-6;
}

.workflow-card {
  @apply modern-card p-6;
}

.workflow-card.workflow-active {
  @apply border-l-4 border-primary-500;
}

.workflow-card.workflow-pending {
  @apply border-l-4 border-warning-500;
}

.workflow-card.workflow-completed {
  @apply border-l-4 border-success-500;
}

.workflow-card.workflow-error {
  @apply border-l-4 border-red-500;
}

.workflow-header {
  @apply flex items-start justify-between mb-4;
}

.workflow-title {
  @apply text-lg font-semibold text-gray-800;
}

.workflow-type {
  @apply text-sm text-gray-600 mt-1;
}

.workflow-status {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-primary-100 text-primary-800;
}

.status-pending {
  @apply bg-warning-100 text-warning-800;
}

.status-completed {
  @apply bg-success-100 text-success-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.workflow-progress {
  @apply space-y-2 mb-4;
}

.progress-info {
  @apply flex justify-between text-sm;
}

.progress-label {
  @apply text-gray-600;
}

.progress-percentage {
  @apply font-medium text-gray-800;
}

.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full transition-all duration-300;
}

.progress-active {
  @apply bg-primary-500;
}

.progress-pending {
  @apply bg-warning-500;
}

.progress-completed {
  @apply bg-success-500;
}

.progress-error {
  @apply bg-red-500;
}

.workflow-participants {
  @apply mb-4;
}

.participant-list {
  @apply space-y-2;
}

.participant-item {
  @apply flex items-center space-x-2 text-sm p-2 rounded-lg transition-colors;
}

.participant-item.participant-completed {
  @apply bg-success-50 text-success-800;
}

.participant-item.participant-active {
  @apply bg-primary-50 text-primary-800;
}

.participant-item.participant-pending {
  @apply bg-gray-50 text-gray-600;
}

.participant-item.participant-error {
  @apply bg-red-50 text-red-800;
}

.participant-name {
  @apply flex-1 truncate;
}

.participant-status-icon {
  @apply flex-shrink-0;
}

.status-icon-completed {
  @apply text-success-600;
}

.status-icon-active {
  @apply text-primary-600;
}

.status-icon-pending {
  @apply text-gray-400;
}

.status-icon-error {
  @apply text-red-600;
}

.workflow-actions {
  @apply flex space-x-2;
}

/* 同步日志样式 */
.sync-logs {
  @apply modern-card p-6;
}

.logs-header {
  @apply flex items-center justify-between mb-4;
}

.logs-actions {
  @apply flex items-center space-x-3;
}

.logs-content {
  @apply space-y-3 max-h-64 overflow-y-auto;
}

.log-item {
  @apply flex items-start space-x-3 p-3 rounded-lg;
}

.log-item.log-success {
  @apply bg-success-50;
}

.log-item.log-error {
  @apply bg-red-50;
}

.log-item.log-warning {
  @apply bg-warning-50;
}

.log-item.log-info {
  @apply bg-blue-50;
}

.log-icon {
  @apply flex-shrink-0 mt-0.5;
}

.log-item.log-success .log-icon {
  @apply text-success-600;
}

.log-item.log-error .log-icon {
  @apply text-red-600;
}

.log-item.log-warning .log-icon {
  @apply text-warning-600;
}

.log-item.log-info .log-icon {
  @apply text-blue-600;
}

.log-content {
  @apply flex-1 min-w-0;
}

.log-header {
  @apply flex items-center justify-between mb-1;
}

.log-title {
  @apply font-medium text-gray-800;
}

.log-time {
  @apply text-xs text-gray-500 flex-shrink-0;
}

.log-message {
  @apply text-sm text-gray-700;
}

.log-details {
  @apply mt-2;
}

.log-details-content {
  @apply text-xs text-gray-600 bg-gray-100 p-2 rounded;
}

/* 对话框样式 */
.workflow-details {
  @apply space-y-6;
}

.detail-section {
  @apply border-b border-gray-200 pb-6 last:border-b-0;
}

.section-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.info-item {
  @apply flex flex-col space-y-1;
}

.info-item label {
  @apply text-sm font-medium text-gray-600;
}

.info-item span {
  @apply text-gray-800;
}

.status-badge {
  @apply px-2 py-1 rounded text-xs font-medium;
}

/* 时间线样式 */
.participants-timeline {
  @apply space-y-4;
}

.timeline-item {
  @apply flex items-start space-x-4;
}

.timeline-marker {
  @apply w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1;
}

.marker-completed {
  @apply bg-success-500 text-white;
}

.marker-active {
  @apply bg-primary-500 text-white;
}

.marker-pending {
  @apply bg-gray-300 text-gray-600;
}

.marker-error {
  @apply bg-red-500 text-white;
}

.timeline-content {
  @apply flex-1 min-w-0;
}

.participant-header {
  @apply flex items-center justify-between mb-2;
}

.participant-role {
  @apply font-semibold text-gray-800;
}

.participant-status {
  @apply text-sm px-2 py-1 rounded;
}

.participant-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500 mt-2;
}

/* 同步历史样式 */
.sync-history {
  @apply space-y-3;
}

.sync-record {
  @apply p-3 rounded-lg;
}

.sync-record.sync-success {
  @apply bg-success-50;
}

.sync-record.sync-error {
  @apply bg-red-50;
}

.sync-record.sync-warning {
  @apply bg-warning-50;
}

.sync-record-header {
  @apply flex items-center justify-between mb-2;
}

.sync-info {
  @apply flex items-center space-x-2;
}

.sync-action {
  @apply font-medium text-gray-800;
}

.sync-status {
  @apply text-xs px-2 py-1 rounded;
}

.sync-status-success {
  @apply bg-success-100 text-success-800;
}

.sync-status-error {
  @apply bg-red-100 text-red-800;
}

.sync-status-warning {
  @apply bg-warning-100 text-warning-800;
}

.sync-time {
  @apply text-xs text-gray-500;
}

.sync-message {
  @apply text-sm text-gray-700 mb-2;
}

.sync-changes {
  @apply flex flex-wrap gap-1;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>