<!--
消息通信中心组件
版本: 1.0
创建时间: 2025-07-19

用于不同角色之间的即时通信和消息管理
特点：
1. 实时消息通信
2. 角色间快速联系
3. 消息分类管理
4. 文件共享功能
-->

<template>
  <div class="message-center">
    <!-- 消息中心头部 -->
    <div class="message-header">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-800">消息中心</h2>
        <div class="header-actions">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-button size="small" @click="markAllAsRead">
              <CheckDouble class="w-4 h-4 mr-1" />
              全部已读
            </el-button>
          </el-badge>
          <el-button size="small" type="primary" @click="openNewMessage">
            <Plus class="w-4 h-4 mr-1" />
            新消息
          </el-button>
        </div>
      </div>
    </div>

    <!-- 消息列表和聊天区域 -->
    <div class="message-content">
      <div class="flex h-96">
        <!-- 左侧消息列表 -->
        <div class="message-list">
          <div class="list-header">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索消息..." 
              size="small"
              clearable
            >
              <template #prefix>
                <Search class="w-4 h-4" />
              </template>
            </el-input>
          </div>
          
          <div class="message-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <el-tab-pane label="全部" name="all" />
              <el-tab-pane label="未读" name="unread" />
              <el-tab-pane label="系统" name="system" />
              <el-tab-pane label="工作" name="work" />
            </el-tabs>
          </div>

          <div class="conversations-list">
            <div 
              v-for="conversation in filteredConversations" 
              :key="conversation.id"
              class="conversation-item"
              :class="{ 
                'active': selectedConversation?.id === conversation.id,
                'unread': conversation.unread > 0
              }"
              @click="selectConversation(conversation)"
            >
              <div class="conversation-avatar">
                <div class="avatar-icon" :class="getRoleAvatarClass(conversation.participantRole)">
                  <component :is="getRoleIcon(conversation.participantRole)" class="w-5 h-5" />
                </div>
                <div v-if="conversation.unread > 0" class="unread-badge">
                  {{ conversation.unread > 99 ? '99+' : conversation.unread }}
                </div>
              </div>
              
              <div class="conversation-content">
                <div class="conversation-header">
                  <h4 class="participant-name">{{ conversation.participantName }}</h4>
                  <span class="message-time">{{ formatTime(conversation.lastMessageTime) }}</span>
                </div>
                <div class="conversation-preview">
                  <span class="participant-role">{{ conversation.participantRoleLabel }}</span>
                  <p class="last-message">{{ conversation.lastMessage }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-area">
          <div v-if="selectedConversation" class="chat-container">
            <!-- 聊天头部 -->
            <div class="chat-header">
              <div class="flex items-center">
                <div class="chat-avatar" :class="getRoleAvatarClass(selectedConversation.participantRole)">
                  <component :is="getRoleIcon(selectedConversation.participantRole)" class="w-5 h-5" />
                </div>
                <div class="chat-info">
                  <h3 class="chat-participant">{{ selectedConversation.participantName }}</h3>
                  <p class="chat-role">{{ selectedConversation.participantRoleLabel }}</p>
                </div>
              </div>
              <div class="chat-actions">
                <el-button size="small" @click="callParticipant">
                  <Phone class="w-4 h-4 mr-1" />
                  呼叫
                </el-button>
                <el-button size="small" @click="viewParticipantInfoFilled">
                  <User class="w-4 h-4 mr-1" />
                  详情
                </el-button>
              </div>
            </div>

            <!-- 消息记录 -->
            <div class="messages-container" ref="messagesContainer">
              <div 
                v-for="message in selectedConversation.messages" 
                :key="message.id"
                class="message-item"
                :class="{ 'own-message': message.sender === currentUser.username }"
              >
                <div class="message-wrapper">
                  <div class="message-avatar" v-if="message.sender !== currentUser.username">
                    <div class="avatar-small" :class="getRoleAvatarClass(selectedConversation.participantRole)">
                      <component :is="getRoleIcon(selectedConversation.participantRole)" class="w-4 h-4" />
                    </div>
                  </div>
                  
                  <div class="message-content">
                    <div class="message-bubble" :class="getMessageBubbleClass(message)">
                      <div v-if="message.type === 'text'" class="message-text">
                        {{ message.content }}
                      </div>
                      <div v-else-if="message.type === 'file'" class="message-file">
                        <div class="file-info">
                          <Paperclip class="w-4 h-4" />
                          <span class="file-name">{{ message.fileName }}</span>
                          <el-button size="small" text @click="downloadFile(message)">
                            下载
                          </el-button>
                        </div>
                      </div>
                      <div v-else-if="message.type === 'system'" class="message-system">
                        <InfoFilled class="w-4 h-4 mr-1" />
                        {{ message.content }}
                      </div>
                    </div>
                    <div class="message-meta">
                      <span class="message-time">{{ formatTime(message.time) }}</span>
                      <span v-if="message.sender === currentUser.username && message.status" class="message-status">
                        <component :is="getStatusIcon(message.status)" class="w-3 h-3" />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 消息输入 -->
            <div class="message-input">
              <div class="input-container">
                <el-input
                  v-model="newMessage"
                  type="textarea"
                  :rows="3"
                  placeholder="输入消息... (Ctrl+Enter发送)"
                  @keydown.ctrl.enter="sendMessage"
                  @keydown="handleTyping"
                />
                <div class="input-actions">
                  <el-upload
                    :show-file-list="false"
                    :before-upload="beforeFileUpload"
                    action="#"
                  >
                    <el-button size="small" text>
                      <Paperclip class="w-4 h-4" />
                    </el-button>
                  </el-upload>
                  <el-button size="small" text @click="insertTemplate">
                    <Document class="w-4 h-4" />
                  </el-button>
                  <el-button 
                    type="primary" 
                    size="small"
                    :disabled="!newMessage.trim()"
                    @click="sendMessage"
                  >
                    发送
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="chat-empty">
            <div class="empty-content">
              <ChatDotRound class="w-16 h-16 text-gray-300 mb-4" />
              <h3 class="text-lg font-medium text-gray-500 mb-2">选择对话开始聊天</h3>
              <p class="text-gray-400">选择左侧的对话或创建新的消息</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新消息对话框 -->
    <el-dialog v-model="newMessageDialog" title="发送新消息" width="50%">
      <el-form :model="newMessageForm" label-width="100px">
        <el-form-item label="收件人角色">
          <el-select v-model="newMessageForm.targetRole" placeholder="选择角色" @change="loadRoleUsers">
            <el-option 
              v-for="role in availableRoles" 
              :key="role.value"
              :label="role.label" 
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收件人">
          <el-select v-model="newMessageForm.targetUser" placeholder="选择用户">
            <el-option 
              v-for="user in roleUsers" 
              :key="user.id"
              :label="user.name" 
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input
            v-model="newMessageForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入消息内容..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="newMessageDialog = false">取消</el-button>
          <el-button type="primary" @click="sendNewMessage">发送</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { 
  Search, Plus, CheckDouble, Phone, User, Paperclip, Document,
  ChatDotRound, InfoFilled, Check, CheckCircle, Avatar,
  Tools, Truck, Briefcase, Monitor
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  userRole: {
    type: String,
    required: true
  }
})

// Stores
const userStore = useUserStore()

// 响应式数据
const conversations = ref([])
const selectedConversation = ref(null)
const searchKeyword = ref('')
const activeTab = ref('all')
const newMessage = ref('')
const newMessageDialog = ref(false)
const newMessageForm = ref({
  targetRole: '',
  targetUser: '',
  content: ''
})
const roleUsers = ref([])
const messagesContainer = ref(null)

// 当前用户信息
const currentUser = computed(() => userStore.user || { username: '当前用户' })

// 可用角色列表
const availableRoles = [
  { label: '航空公司采购员', value: 'airline_buyer' },
  { label: '平台员工', value: 'platform_staff' },
  { label: '维修工程师', value: 'maintenance_engineer' },
  { label: '物流专员', value: 'logistics_specialist' }
]

// 计算属性
const unreadCount = computed(() => {
  return conversations.value.reduce((total, conv) => total + conv.unread, 0)
})

const filteredConversations = computed(() => {
  let filtered = conversations.value

  // 按标签筛选
  if (activeTab.value === 'unread') {
    filtered = filtered.filter(conv => conv.unread > 0)
  } else if (activeTab.value === 'system') {
    filtered = filtered.filter(conv => conv.type === 'system')
  } else if (activeTab.value === 'work') {
    filtered = filtered.filter(conv => conv.type === 'work')
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(conv => 
      conv.participantName.toLowerCase().includes(keyword) ||
      conv.lastMessage.toLowerCase().includes(keyword)
    )
  }

  return filtered.sort((a, b) => new Date(b.lastMessageTime) - new Date(a.lastMessageTime))
})

// 方法
const getRoleIcon = (role) => {
  const icons = {
    airline_buyer: Avatar,
    platform_staff: Monitor,
    maintenance_engineer: Tools,
    logistics_specialist: Truck
  }
  return icons[role] || Avatar
}

const getRoleAvatarClass = (role) => {
  const classes = {
    airline_buyer: 'avatar-primary',
    platform_staff: 'avatar-success',
    maintenance_engineer: 'avatar-warning',
    logistics_specialist: 'avatar-info'
  }
  return classes[role] || 'avatar-primary'
}

const getMessageBubbleClass = (message) => {
  if (message.type === 'system') return 'bubble-system'
  return message.sender === currentUser.value.username ? 'bubble-own' : 'bubble-other'
}

const getStatusIcon = (status) => {
  const icons = {
    sent: Check,
    delivered: CheckCircle,
    read: CheckCircle
  }
  return icons[status] || Check
}

const formatTime = (time) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  return date.toLocaleDateString('zh-CN')
}

const selectConversation = (conversation) => {
  selectedConversation.value = conversation
  
  // 标记为已读
  if (conversation.unread > 0) {
    conversation.unread = 0
    conversation.messages.forEach(msg => {
      if (msg.sender !== currentUser.value.username && !msg.read) {
        msg.read = true
      }
    })
  }
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const markAllAsRead = () => {
  conversations.value.forEach(conv => {
    conv.unread = 0
    conv.messages.forEach(msg => {
      if (msg.sender !== currentUser.value.username) {
        msg.read = true
      }
    })
  })
  ElMessage.success('所有消息已标记为已读')
}

const openNewMessage = () => {
  newMessageForm.value = {
    targetRole: '',
    targetUser: '',
    content: ''
  }
  newMessageDialog.value = true
}

const loadRoleUsers = () => {
  // 模拟加载角色用户列表
  const usersByRole = {
    airline_buyer: [
      { id: '1', name: '张三 - 中国国航' },
      { id: '2', name: '李四 - 东方航空' }
    ],
    platform_staff: [
      { id: '3', name: '王五 - 平台运营' },
      { id: '4', name: '赵六 - 客户服务' }
    ],
    maintenance_engineer: [
      { id: '5', name: '钱七 - 发动机维修' },
      { id: '6', name: '孙八 - 航电维修' }
    ],
    logistics_specialist: [
      { id: '7', name: '周九 - 配送中心' },
      { id: '8', name: '吴十 - 仓储管理' }
    ]
  }
  
  roleUsers.value = usersByRole[newMessageForm.value.targetRole] || []
}

const sendNewMessage = () => {
  if (!newMessageForm.value.targetUser || !newMessageForm.value.content.trim()) {
    ElMessage.warning('请完整填写收件人和消息内容')
    return
  }
  
  // 创建新对话或发送到现有对话
  const targetUser = roleUsers.value.find(user => user.id === newMessageForm.value.targetUser)
  const roleLabel = availableRoles.find(role => role.value === newMessageForm.value.targetRole)?.label
  
  let conversation = conversations.value.find(conv => 
    conv.participantRole === newMessageForm.value.targetRole &&
    conv.participantName === targetUser.name
  )
  
  if (!conversation) {
    conversation = {
      id: Date.now(),
      participantName: targetUser.name,
      participantRole: newMessageForm.value.targetRole,
      participantRoleLabel: roleLabel,
      type: 'work',
      unread: 0,
      lastMessage: newMessageForm.value.content,
      lastMessageTime: new Date().toISOString(),
      messages: []
    }
    conversations.value.unshift(conversation)
  }
  
  // 添加消息
  const message = {
    id: Date.now(),
    sender: currentUser.value.username,
    content: newMessageForm.value.content,
    time: new Date().toISOString(),
    type: 'text',
    status: 'sent'
  }
  
  conversation.messages.push(message)
  conversation.lastMessage = newMessageForm.value.content
  conversation.lastMessageTime = message.time
  
  newMessageDialog.value = false
  ElMessage.success('消息发送成功')
  
  // 选择新对话
  selectConversation(conversation)
}

const sendMessage = () => {
  if (!newMessage.value.trim() || !selectedConversation.value) return
  
  const message = {
    id: Date.now(),
    sender: currentUser.value.username,
    content: newMessage.value,
    time: new Date().toISOString(),
    type: 'text',
    status: 'sent'
  }
  
  selectedConversation.value.messages.push(message)
  selectedConversation.value.lastMessage = message.content
  selectedConversation.value.lastMessageTime = message.time
  
  newMessage.value = ''
  
  // 模拟消息状态变化
  setTimeout(() => {
    message.status = 'delivered'
  }, 1000)
  
  setTimeout(() => {
    message.status = 'read'
  }, 3000)
  
  nextTick(() => {
    scrollToBottom()
  })
}

const handleTyping = () => {
  // 处理正在输入状态
}

const beforeFileUpload = (file) => {
  if (!selectedConversation.value) {
    ElMessage.warning('请先选择对话')
    return false
  }
  
  const message = {
    id: Date.now(),
    sender: currentUser.value.username,
    fileName: file.name,
    fileSize: file.size,
    time: new Date().toISOString(),
    type: 'file',
    status: 'sent'
  }
  
  selectedConversation.value.messages.push(message)
  selectedConversation.value.lastMessage = `[文件] ${file.name}`
  selectedConversation.value.lastMessageTime = message.time
  
  ElMessage.success('文件发送成功')
  
  nextTick(() => {
    scrollToBottom()
  })
  
  return false // 阻止实际上传
}

const downloadFile = (message) => {
  ElMessage.info('文件下载功能开发中...')
}

const insertTemplate = () => {
  const templates = [
    '您好，请问这个订单的进度如何？',
    '紧急需求，请优先处理',
    '请确认技术规格是否正确',
    '物流信息已更新，请查收'
  ]
  
  ElMessage.info('消息模板功能开发中...')
}

const callParticipant = () => {
  ElMessage.info('语音通话功能开发中...')
}

const viewParticipantInfoFilled = () => {
  ElMessage.info('用户详情功能开发中...')
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 加载对话数据
const loadConversations = () => {
  // 模拟对话数据
  conversations.value = [
    {
      id: '1',
      participantName: '张三 - 中国国航',
      participantRole: 'airline_buyer',
      participantRoleLabel: '航空公司采购员',
      type: 'work',
      unread: 2,
      lastMessage: '请确认这个订单的交付时间',
      lastMessageTime: '2024-07-19T10:30:00',
      messages: [
        {
          id: '1',
          sender: '张三',
          content: '您好，关于订单PO-2024-001的进度如何？',
          time: '2024-07-19T10:25:00',
          type: 'text',
          read: true
        },
        {
          id: '2',
          sender: currentUser.value.username,
          content: '订单正在处理中，预计明天完成',
          time: '2024-07-19T10:28:00',
          type: 'text',
          status: 'read'
        },
        {
          id: '3',
          sender: '张三',
          content: '请确认这个订单的交付时间',
          time: '2024-07-19T10:30:00',
          type: 'text',
          read: false
        }
      ]
    },
    {
      id: '2',
      participantName: '王五 - 发动机维修',
      participantRole: 'maintenance_engineer',
      participantRoleLabel: '维修工程师',
      type: 'work',
      unread: 0,
      lastMessage: '技术规格已确认',
      lastMessageTime: '2024-07-19T09:15:00',
      messages: [
        {
          id: '4',
          sender: '王五',
          content: '需要确认CFM56发动机喷嘴的规格',
          time: '2024-07-19T09:10:00',
          type: 'text',
          read: true
        },
        {
          id: '5',
          sender: currentUser.value.username,
          content: '规格文件已发送，请查收',
          time: '2024-07-19T09:12:00',
          type: 'text',
          status: 'read'
        },
        {
          id: '6',
          sender: '王五',
          content: '技术规格已确认',
          time: '2024-07-19T09:15:00',
          type: 'text',
          read: true
        }
      ]
    },
    {
      id: '3',
      participantName: '系统通知',
      participantRole: 'system',
      participantRoleLabel: '系统',
      type: 'system',
      unread: 1,
      lastMessage: '您有新的工单分配',
      lastMessageTime: '2024-07-19T08:00:00',
      messages: [
        {
          id: '7',
          sender: 'system',
          content: '您有新的工单分配，请及时处理',
          time: '2024-07-19T08:00:00',
          type: 'system',
          read: false
        }
      ]
    }
  ]
}

// 生命周期
onMounted(() => {
  loadConversations()
})
</script>

<style scoped>
.message-center {
  @apply space-y-6;
}

.message-header {
  @apply modern-card p-4;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.message-content {
  @apply modern-card p-0 overflow-hidden;
}

/* 消息列表样式 */
.message-list {
  @apply w-80 border-r border-gray-200 flex flex-col;
}

.list-header {
  @apply p-4 border-b border-gray-200;
}

.message-tabs {
  @apply border-b border-gray-200;
}

.conversations-list {
  @apply flex-1 overflow-y-auto;
}

.conversation-item {
  @apply flex items-center p-4 border-b border-gray-100 cursor-pointer transition-colors;
}

.conversation-item:hover {
  @apply bg-gray-50;
}

.conversation-item.active {
  @apply bg-primary-50 border-primary-200;
}

.conversation-item.unread {
  @apply bg-blue-50;
}

.conversation-avatar {
  @apply relative mr-3 flex-shrink-0;
}

.avatar-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white;
}

.avatar-primary {
  @apply bg-primary-500;
}

.avatar-success {
  @apply bg-success-500;
}

.avatar-warning {
  @apply bg-warning-500;
}

.avatar-info {
  @apply bg-info-500;
}

.unread-badge {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center;
}

.conversation-content {
  @apply flex-1 min-w-0;
}

.conversation-header {
  @apply flex items-center justify-between mb-1;
}

.participant-name {
  @apply font-medium text-gray-800 truncate;
}

.message-time {
  @apply text-xs text-gray-500 flex-shrink-0;
}

.conversation-preview {
  @apply space-y-1;
}

.participant-role {
  @apply text-xs text-primary-600;
}

.last-message {
  @apply text-sm text-gray-600 truncate;
}

/* 聊天区域样式 */
.chat-area {
  @apply flex-1 flex flex-col;
}

.chat-container {
  @apply flex flex-col h-full;
}

.chat-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.chat-avatar {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white mr-3;
}

.chat-info {
  @apply flex-1;
}

.chat-participant {
  @apply font-medium text-gray-800;
}

.chat-role {
  @apply text-sm text-primary-600;
}

.chat-actions {
  @apply flex space-x-2;
}

.messages-container {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
}

.message-item {
  @apply flex;
}

.message-item.own-message {
  @apply justify-end;
}

.message-wrapper {
  @apply flex items-end space-x-2 max-w-xs lg:max-w-md;
}

.message-item.own-message .message-wrapper {
  @apply flex-row-reverse space-x-reverse;
}

.message-avatar {
  @apply flex-shrink-0;
}

.avatar-small {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white;
}

.message-content {
  @apply flex flex-col;
}

.message-bubble {
  @apply rounded-lg px-3 py-2 max-w-full break-words;
}

.bubble-own {
  @apply bg-primary-500 text-white;
}

.bubble-other {
  @apply bg-gray-200 text-gray-800;
}

.bubble-system {
  @apply bg-blue-100 text-blue-800 flex items-center;
}

.message-text {
  @apply text-sm leading-relaxed;
}

.message-file {
  @apply text-sm;
}

.file-info {
  @apply flex items-center space-x-2;
}

.file-name {
  @apply truncate;
}

.message-system {
  @apply text-sm flex items-center;
}

.message-meta {
  @apply flex items-center justify-between mt-1 text-xs text-gray-500;
}

.message-item.own-message .message-meta {
  @apply justify-end;
}

.message-status {
  @apply ml-2 text-primary-600;
}

/* 消息输入样式 */
.message-input {
  @apply p-4 border-t border-gray-200;
}

.input-container {
  @apply space-y-3;
}

.input-actions {
  @apply flex items-center justify-between;
}

/* 空状态样式 */
.chat-empty {
  @apply flex-1 flex items-center justify-center;
}

.empty-content {
  @apply text-center;
}

/* 对话框样式 */
.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>