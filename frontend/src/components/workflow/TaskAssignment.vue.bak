<!--
跨角色任务分配系统组件
版本: 1.0
创建时间: 2025-07-19

用于在不同角色之间分配和管理任务
特点：
1. 智能任务分配
2. 负载均衡
3. 技能匹配
4. 任务跟踪
-->

<template>
  <div class="task-assignment">
    <!-- 任务分配头部 -->
    <div class="assignment-header">
      <div class="flex items-center justify-between">
        <div class="header-info">
          <h2 class="text-xl font-semibold text-gray-800">任务分配中心</h2>
          <p class="text-gray-600 mt-1">智能分配任务给最适合的角色</p>
        </div>
        <div class="header-actions">
          <el-button size="small" @click="openCreateTask">
            <Plus class="w-4 h-4 mr-1" />
            创建任务
          </el-button>
          <el-button size="small" type="primary" @click="autoAssignTasks">
            <Star class="w-4 h-4 mr-1" />
            智能分配
          </el-button>
        </div>
      </div>
    </div>

    <!-- 任务概览统计 -->
    <div class="task-overview">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="overview-card overview-pending">
          <div class="overview-icon">
            <Clock class="w-6 h-6" />
          </div>
          <div class="overview-content">
            <div class="overview-value">{{ taskStats.pending }}</div>
            <div class="overview-label">待分配任务</div>
          </div>
        </div>
        
        <div class="overview-card overview-assigned">
          <div class="overview-icon">
            <User class="w-6 h-6" />
          </div>
          <div class="overview-content">
            <div class="overview-value">{{ taskStats.assigned }}</div>
            <div class="overview-label">已分配任务</div>
          </div>
        </div>
        
        <div class="overview-card overview-progress">
          <div class="overview-icon">
            <Loading class="w-6 h-6" />
          </div>
          <div class="overview-content">
            <div class="overview-value">{{ taskStats.inProgress }}</div>
            <div class="overview-label">进行中任务</div>
          </div>
        </div>
        
        <div class="overview-card overview-completed">
          <div class="overview-icon">
            <CheckCircle class="w-6 h-6" />
          </div>
          <div class="overview-content">
            <div class="overview-value">{{ taskStats.completed }}</div>
            <div class="overview-label">已完成任务</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务筛选和视图切换 -->
    <div class="task-filters">
      <div class="flex items-center justify-between">
        <div class="filter-controls">
          <el-select v-model="statusFilter" placeholder="状态筛选" size="small" @change="filterTasks">
            <el-option label="全部" value="" />
            <el-option label="待分配" value="pending" />
            <el-option label="已分配" value="assigned" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
          
          <el-select v-model="priorityFilter" placeholder="优先级筛选" size="small" @change="filterTasks">
            <el-option label="全部优先级" value="" />
            <el-option label="紧急" value="urgent" />
            <el-option label="高" value="high" />
            <el-option label="普通" value="normal" />
            <el-option label="低" value="low" />
          </el-select>
          
          <el-select v-model="roleFilter" placeholder="角色筛选" size="small" @change="filterTasks">
            <el-option label="全部角色" value="" />
            <el-option 
              v-for="role in availableRoles" 
              :key="role.value"
              :label="role.label" 
              :value="role.value"
            />
          </el-select>
        </div>
        
        <div class="view-controls">
          <el-button-group>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              size="small"
              @click="viewMode = 'grid'"
            >
              <Grid class="w-4 h-4" />
            </el-button>
            <el-button 
              :type="viewMode === 'list' ? 'primary' : ''" 
              size="small"
              @click="viewMode = 'list'"
            >
              <List class="w-4 h-4" />
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 任务列表/网格视图 -->
    <div class="tasks-section">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="tasks-grid">
        <div 
          v-for="task in filteredTasks" 
          :key="task.id"
          class="task-card"
          :class="getTaskCardClass(task)"
          @click="selectTask(task)"
        >
          <div class="task-header">
            <div class="task-priority" :class="getPriorityClass(task.priority)">
              <component :is="getPriorityIcon(task.priority)" class="w-4 h-4" />
            </div>
            <div class="task-status" :class="getStatusClass(task.status)">
              {{ getStatusLabel(task.status) }}
            </div>
          </div>
          
          <div class="task-content">
            <h3 class="task-title">{{ task.title }}</h3>
            <p class="task-description">{{ task.description }}</p>
            
            <div class="task-meta">
              <div class="task-type">
                <Tag class="w-4 h-4 mr-1" />
                {{ task.type }}
              </div>
              <div class="task-deadline">
                <Calendar class="w-4 h-4 mr-1" />
                {{ formatDate(task.deadline) }}
              </div>
            </div>
          </div>
          
          <div class="task-assignment">
            <div v-if="task.assignee" class="assigned-user">
              <div class="assignee-avatar" :class="getRoleAvatarClass(task.assignee.role)">
                <component :is="getRoleIcon(task.assignee.role)" class="w-4 h-4" />
              </div>
              <div class="assignee-info">
                <div class="assignee-name">{{ task.assignee.name }}</div>
                <div class="assignee-role">{{ task.assignee.roleLabel }}</div>
              </div>
            </div>
            <div v-else class="unassigned">
              <el-button size="small" type="primary" @click.stop="assignTask(task)">
                分配任务
              </el-button>
            </div>
          </div>
          
          <div class="task-actions">
            <el-button size="small" @click.stop="viewTaskDetails(task)">
              详情
            </el-button>
            <el-button 
              v-if="!task.assignee" 
              size="small" 
              type="primary"
              @click.stop="assignTask(task)"
            >
              分配
            </el-button>
            <el-button 
              v-else-if="task.status === 'assigned'" 
              size="small" 
              type="warning"
              @click.stop="reassignTask(task)"
            >
              重新分配
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="tasks-list">
        <el-table :data="filteredTasks" style="width: 100%" @row-click="selectTask">
          <el-table-column prop="title" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-2">
                <div class="task-priority-icon" :class="getPriorityClass(row.priority)">
                  <component :is="getPriorityIcon(row.priority)" class="w-4 h-4" />
                </div>
                <span class="font-medium">{{ row.title }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="120" />
          
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityTagType(row.priority)" size="small">
                {{ getPriorityLabel(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="assignee" label="负责人" width="180">
            <template #default="{ row }">
              <div v-if="row.assignee" class="flex items-center space-x-2">
                <div class="assignee-avatar-small" :class="getRoleAvatarClass(row.assignee.role)">
                  <component :is="getRoleIcon(row.assignee.role)" class="w-3 h-3" />
                </div>
                <div>
                  <div class="text-sm font-medium">{{ row.assignee.name }}</div>
                  <div class="text-xs text-gray-500">{{ row.assignee.roleLabel }}</div>
                </div>
              </div>
              <span v-else class="text-gray-400">未分配</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="deadline" label="截止时间" width="150">
            <template #default="{ row }">
              <span :class="{ 'text-red-600': isOverdue(row.deadline) }">
                {{ formatDate(row.deadline) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click.stop="viewTaskDetails(row)">
                详情
              </el-button>
              <el-button 
                v-if="!row.assignee" 
                size="small" 
                type="primary"
                @click.stop="assignTask(row)"
              >
                分配
              </el-button>
              <el-button 
                v-else-if="row.status === 'assigned'" 
                size="small" 
                type="warning"
                @click.stop="reassignTask(row)"
              >
                重分配
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="createTaskDialog" title="创建新任务" width="60%">
      <el-form :model="newTaskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务标题" prop="title">
              <el-input v-model="newTaskForm.title" placeholder="请输入任务标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="type">
              <el-select v-model="newTaskForm.type" placeholder="选择任务类型">
                <el-option label="订单处理" value="order_processing" />
                <el-option label="技术审核" value="technical_review" />
                <el-option label="物流配送" value="logistics_delivery" />
                <el-option label="客户服务" value="customer_service" />
                <el-option label="系统维护" value="system_maintenance" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="newTaskForm.priority" placeholder="选择优先级">
                <el-option label="紧急" value="urgent" />
                <el-option label="高" value="high" />
                <el-option label="普通" value="normal" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="newTaskForm.deadline"
                type="datetime"
                placeholder="选择截止时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="newTaskForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述任务内容和要求"
          />
        </el-form-item>
        
        <el-form-item label="技能要求">
          <el-select
            v-model="newTaskForm.requiredSkills"
            multiple
            placeholder="选择所需技能"
            style="width: 100%"
          >
            <el-option 
              v-for="skill in availableSkills" 
              :key="skill.value"
              :label="skill.label" 
              :value="skill.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="指定角色">
          <el-select v-model="newTaskForm.targetRole" placeholder="可选择特定角色" clearable>
            <el-option 
              v-for="role in availableRoles" 
              :key="role.value"
              :label="role.label" 
              :value="role.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createTaskDialog = false">取消</el-button>
          <el-button type="primary" @click="createTask">创建任务</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务分配对话框 -->
    <el-dialog v-model="assignTaskDialog" title="分配任务" width="50%">
      <div v-if="selectedTask" class="assignment-content">
        <div class="task-info">
          <h3 class="text-lg font-semibold mb-2">{{ selectedTask.title }}</h3>
          <p class="text-gray-600 mb-4">{{ selectedTask.description }}</p>
          
          <div class="task-requirements">
            <div class="requirement-item">
              <label>所需技能:</label>
              <div class="skills-list">
                <el-tag 
                  v-for="skill in selectedTask.requiredSkills" 
                  :key="skill"
                  size="small"
                  class="mr-1"
                >
                  {{ getSkillLabel(skill) }}
                </el-tag>
              </div>
            </div>
            <div class="requirement-item">
              <label>截止时间:</label>
              <span :class="{ 'text-red-600': isOverdue(selectedTask.deadline) }">
                {{ formatDate(selectedTask.deadline) }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="candidate-selection">
          <h4 class="text-md font-semibold mb-3">推荐候选人</h4>
          <div class="candidates-list">
            <div 
              v-for="candidate in recommendedCandidates" 
              :key="candidate.id"
              class="candidate-item"
              :class="{ 'selected': assignmentForm.assigneeId === candidate.id }"
              @click="selectCandidate(candidate)"
            >
              <div class="candidate-avatar" :class="getRoleAvatarClass(candidate.role)">
                <component :is="getRoleIcon(candidate.role)" class="w-5 h-5" />
              </div>
              <div class="candidate-info">
                <div class="candidate-name">{{ candidate.name }}</div>
                <div class="candidate-role">{{ candidate.roleLabel }}</div>
                <div class="candidate-workload">
                  当前任务: {{ candidate.currentTasks }} / {{ candidate.maxTasks }}
                </div>
              </div>
              <div class="candidate-match">
                <div class="match-score">
                  <span class="score-label">匹配度</span>
                  <div class="score-bar">
                    <div 
                      class="score-fill" 
                      :style="{ width: `${candidate.matchScore}%` }"
                    ></div>
                  </div>
                  <span class="score-value">{{ candidate.matchScore }}%</span>
                </div>
                <div class="candidate-skills">
                  <el-tag 
                    v-for="skill in candidate.skills" 
                    :key="skill"
                    size="small"
                    :type="selectedTask.requiredSkills.includes(skill) ? 'success' : 'info'"
                    class="mr-1"
                  >
                    {{ getSkillLabel(skill) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignTaskDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :disabled="!assignmentForm.assigneeId"
            @click="confirmAssignment"
          >
            确认分配
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Star, Clock, User, Loading, CheckCircle, Grid, List,
  Tag, Calendar, Avatar, Tools, Truck, Monitor,
  Flag, Warning, Minus
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  userRole: {
    type: String,
    required: true
  }
})

// Stores
const userStore = useUserStore()

// 响应式数据
const tasks = ref([])
const filteredTasks = ref([])
const statusFilter = ref('')
const priorityFilter = ref('')
const roleFilter = ref('')
const viewMode = ref('grid')
const selectedTask = ref(null)
const createTaskDialog = ref(false)
const assignTaskDialog = ref(false)
const recommendedCandidates = ref([])
const taskFormRef = ref(null)

// 表单数据
const newTaskForm = ref({
  title: '',
  description: '',
  type: '',
  priority: 'normal',
  deadline: null,
  requiredSkills: [],
  targetRole: ''
})

const assignmentForm = ref({
  assigneeId: ''
})

// 验证规则
const taskRules = {
  title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  deadline: [{ required: true, message: '请选择截止时间', trigger: 'change' }]
}

// 可用角色和技能
const availableRoles = [
  { label: '航空公司采购员', value: 'airline_buyer' },
  { label: '平台员工', value: 'platform_staff' },
  { label: '维修工程师', value: 'maintenance_engineer' },
  { label: '物流专员', value: 'logistics_specialist' }
]

const availableSkills = [
  { label: '订单处理', value: 'order_processing' },
  { label: '技术审核', value: 'technical_review' },
  { label: '航材识别', value: 'material_identification' },
  { label: '质量检验', value: 'quality_inspection' },
  { label: '物流配送', value: 'logistics_delivery' },
  { label: '客户沟通', value: 'customer_communication' },
  { label: '系统操作', value: 'system_operation' },
  { label: '数据分析', value: 'data_analysis' }
]

// 计算属性
const taskStats = computed(() => {
  return {
    pending: tasks.value.filter(t => t.status === 'pending').length,
    assigned: tasks.value.filter(t => t.status === 'assigned').length,
    inProgress: tasks.value.filter(t => t.status === 'in_progress').length,
    completed: tasks.value.filter(t => t.status === 'completed').length
  }
})

// 方法
const getRoleIcon = (role) => {
  const icons = {
    airline_buyer: Avatar,
    platform_staff: Monitor,
    maintenance_engineer: Tools,
    logistics_specialist: Truck
  }
  return icons[role] || Avatar
}

const getRoleAvatarClass = (role) => {
  const classes = {
    airline_buyer: 'avatar-primary',
    platform_staff: 'avatar-success',
    maintenance_engineer: 'avatar-warning',
    logistics_specialist: 'avatar-info'
  }
  return classes[role] || 'avatar-primary'
}

const getPriorityIcon = (priority) => {
  const icons = {
    urgent: Flag,
    high: Warning,
    normal: Minus,
    low: Minus
  }
  return icons[priority] || Minus
}

const getPriorityClass = (priority) => {
  const classes = {
    urgent: 'priority-urgent',
    high: 'priority-high',
    normal: 'priority-normal',
    low: 'priority-low'
  }
  return classes[priority] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return labels[priority] || priority
}

const getPriorityTagType = (priority) => {
  const types = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info'
  }
  return types[priority] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '待分配',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getStatusClass = (status) => {
  const classes = {
    pending: 'status-pending',
    assigned: 'status-assigned',
    in_progress: 'status-progress',
    completed: 'status-completed'
  }
  return classes[status] || ''
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    assigned: 'primary',
    in_progress: 'success',
    completed: 'info'
  }
  return types[status] || ''
}

const getTaskCardClass = (task) => {
  return {
    'task-urgent': task.priority === 'urgent',
    'task-overdue': isOverdue(task.deadline)
  }
}

const getSkillLabel = (skill) => {
  const skillMap = availableSkills.find(s => s.value === skill)
  return skillMap ? skillMap.label : skill
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const isOverdue = (deadline) => {
  return new Date(deadline) < new Date()
}

const filterTasks = () => {
  let filtered = tasks.value

  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  if (priorityFilter.value) {
    filtered = filtered.filter(task => task.priority === priorityFilter.value)
  }

  if (roleFilter.value) {
    filtered = filtered.filter(task => 
      task.assignee && task.assignee.role === roleFilter.value
    )
  }

  filteredTasks.value = filtered
}

const selectTask = (task) => {
  selectedTask.value = task
}

const openCreateTask = () => {
  newTaskForm.value = {
    title: '',
    description: '',
    type: '',
    priority: 'normal',
    deadline: null,
    requiredSkills: [],
    targetRole: ''
  }
  createTaskDialog.value = true
}

const createTask = async () => {
  try {
    await taskFormRef.value.validate()
    
    const newTask = {
      id: `TASK-${Date.now()}`,
      ...newTaskForm.value,
      status: 'pending',
      assignee: null,
      createdAt: new Date().toISOString(),
      createdBy: userStore.user?.username || '当前用户'
    }
    
    tasks.value.unshift(newTask)
    filterTasks()
    
    createTaskDialog.value = false
    ElMessage.success('任务创建成功')
    
  } catch (error) {
    console.error('创建任务失败:', error)
  }
}

const assignTask = (task) => {
  selectedTask.value = task
  loadRecommendedCandidates(task)
  assignmentForm.value.assigneeId = ''
  assignTaskDialog.value = true
}

const reassignTask = (task) => {
  assignTask(task)
}

const loadRecommendedCandidates = (task) => {
  // 模拟加载推荐候选人
  const candidates = [
    {
      id: '1',
      name: '张三',
      role: 'airline_buyer',
      roleLabel: '航空公司采购员',
      currentTasks: 3,
      maxTasks: 8,
      skills: ['order_processing', 'customer_communication', 'material_identification'],
      matchScore: 95
    },
    {
      id: '2',
      name: '李四',
      role: 'platform_staff',
      roleLabel: '平台员工',
      currentTasks: 2,
      maxTasks: 6,
      skills: ['system_operation', 'data_analysis', 'customer_communication'],
      matchScore: 88
    },
    {
      id: '3',
      name: '王五',
      role: 'maintenance_engineer',
      roleLabel: '维修工程师',
      currentTasks: 4,
      maxTasks: 5,
      skills: ['technical_review', 'quality_inspection', 'material_identification'],
      matchScore: 92
    },
    {
      id: '4',
      name: '赵六',
      role: 'logistics_specialist',
      roleLabel: '物流专员',
      currentTasks: 1,
      maxTasks: 7,
      skills: ['logistics_delivery', 'system_operation', 'customer_communication'],
      matchScore: 85
    }
  ]
  
  // 根据任务要求筛选和排序候选人
  let filtered = candidates
  
  if (task.targetRole) {
    filtered = filtered.filter(c => c.role === task.targetRole)
  }
  
  // 计算匹配分数
  filtered.forEach(candidate => {
    let score = 60 // 基础分数
    
    // 技能匹配加分
    const matchedSkills = candidate.skills.filter(skill => 
      task.requiredSkills.includes(skill)
    )
    score += (matchedSkills.length / task.requiredSkills.length) * 30
    
    // 负载情况加分
    const loadRatio = candidate.currentTasks / candidate.maxTasks
    if (loadRatio < 0.5) score += 10
    else if (loadRatio < 0.8) score += 5
    
    candidate.matchScore = Math.min(Math.round(score), 100)
  })
  
  recommendedCandidates.value = filtered.sort((a, b) => b.matchScore - a.matchScore)
}

const selectCandidate = (candidate) => {
  assignmentForm.value.assigneeId = candidate.id
}

const confirmAssignment = () => {
  const candidate = recommendedCandidates.value.find(c => c.id === assignmentForm.value.assigneeId)
  
  if (candidate) {
    selectedTask.value.assignee = {
      id: candidate.id,
      name: candidate.name,
      role: candidate.role,
      roleLabel: candidate.roleLabel
    }
    selectedTask.value.status = 'assigned'
    selectedTask.value.assignedAt = new Date().toISOString()
    
    // 更新候选人的任务数量
    candidate.currentTasks += 1
    
    assignTaskDialog.value = false
    ElMessage.success(`任务已分配给 ${candidate.name}`)
    
    filterTasks()
  }
}

const autoAssignTasks = async () => {
  try {
    const pendingTasks = tasks.value.filter(t => t.status === 'pending')
    
    if (pendingTasks.length === 0) {
      ElMessage.info('没有待分配的任务')
      return
    }
    
    await ElMessageBox.confirm(
      `将自动分配 ${pendingTasks.length} 个待分配任务，是否继续？`,
      '智能分配确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟智能分配逻辑
    for (const task of pendingTasks) {
      loadRecommendedCandidates(task)
      
      if (recommendedCandidates.value.length > 0) {
        const bestCandidate = recommendedCandidates.value[0]
        
        task.assignee = {
          id: bestCandidate.id,
          name: bestCandidate.name,
          role: bestCandidate.role,
          roleLabel: bestCandidate.roleLabel
        }
        task.status = 'assigned'
        task.assignedAt = new Date().toISOString()
        
        bestCandidate.currentTasks += 1
      }
    }
    
    ElMessage.success(`成功自动分配 ${pendingTasks.length} 个任务`)
    filterTasks()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('自动分配失败')
    }
  }
}

const viewTaskDetails = (task) => {
  ElMessage.info('任务详情功能开发中...')
}

// 加载任务数据
const loadTasks = () => {
  tasks.value = [
    {
      id: 'TASK-001',
      title: '处理紧急航材需求',
      description: '处理来自中国国航的紧急发动机部件需求，需要在24小时内完成审核和配送安排',
      type: 'order_processing',
      priority: 'urgent',
      status: 'pending',
      deadline: '2024-07-20T18:00:00',
      requiredSkills: ['order_processing', 'material_identification', 'customer_communication'],
      targetRole: '',
      createdAt: '2024-07-19T08:00:00',
      createdBy: '系统管理员',
      assignee: null
    },
    {
      id: 'TASK-002',
      title: '技术规格审核',
      description: '审核起落架轴承的技术规格文档，确保符合B737-800机型要求',
      type: 'technical_review',
      priority: 'high',
      status: 'assigned',
      deadline: '2024-07-22T16:00:00',
      requiredSkills: ['technical_review', 'quality_inspection'],
      targetRole: 'maintenance_engineer',
      createdAt: '2024-07-19T09:30:00',
      createdBy: '张三',
      assignee: {
        id: '3',
        name: '王五',
        role: 'maintenance_engineer',
        roleLabel: '维修工程师'
      },
      assignedAt: '2024-07-19T10:15:00'
    },
    {
      id: 'TASK-003',
      title: '配送路线优化',
      description: '优化华东地区的航材配送路线，提高配送效率',
      type: 'logistics_delivery',
      priority: 'normal',
      status: 'in_progress',
      deadline: '2024-07-25T12:00:00',
      requiredSkills: ['logistics_delivery', 'data_analysis'],
      targetRole: 'logistics_specialist',
      createdAt: '2024-07-18T14:00:00',
      createdBy: '李四',
      assignee: {
        id: '4',
        name: '赵六',
        role: 'logistics_specialist',
        roleLabel: '物流专员'
      },
      assignedAt: '2024-07-18T15:30:00'
    },
    {
      id: 'TASK-004',
      title: '客户满意度调研',
      description: '收集和分析客户对平台服务的反馈意见',
      type: 'customer_service',
      priority: 'low',
      status: 'completed',
      deadline: '2024-07-21T17:00:00',
      requiredSkills: ['customer_communication', 'data_analysis'],
      targetRole: 'platform_staff',
      createdAt: '2024-07-17T10:00:00',
      createdBy: '周八',
      assignee: {
        id: '2',
        name: '李四',
        role: 'platform_staff',
        roleLabel: '平台员工'
      },
      assignedAt: '2024-07-17T11:00:00',
      completedAt: '2024-07-19T09:00:00'
    }
  ]
  
  filteredTasks.value = tasks.value
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.task-assignment {
  @apply space-y-6;
}

.assignment-header {
  @apply modern-card p-6;
}

.header-actions {
  @apply flex items-center space-x-3;
}

/* 概览统计样式 */
.task-overview {
  @apply space-y-4;
}

.overview-card {
  @apply modern-card p-6 flex items-center space-x-4;
}

.overview-card.overview-pending {
  @apply border-l-4 border-warning-500;
}

.overview-card.overview-assigned {
  @apply border-l-4 border-primary-500;
}

.overview-card.overview-progress {
  @apply border-l-4 border-info-500;
}

.overview-card.overview-completed {
  @apply border-l-4 border-success-500;
}

.overview-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.overview-pending .overview-icon {
  @apply bg-warning-100 text-warning-600;
}

.overview-assigned .overview-icon {
  @apply bg-primary-100 text-primary-600;
}

.overview-progress .overview-icon {
  @apply bg-info-100 text-info-600;
}

.overview-completed .overview-icon {
  @apply bg-success-100 text-success-600;
}

.overview-value {
  @apply text-2xl font-bold text-gray-800;
}

.overview-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 筛选控件样式 */
.task-filters {
  @apply modern-card p-4;
}

.filter-controls {
  @apply flex items-center space-x-3;
}

/* 任务卡片样式 */
.tasks-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6;
}

.task-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200;
}

.task-card:hover {
  @apply transform -translate-y-1 shadow-lg;
}

.task-card.task-urgent {
  @apply ring-2 ring-red-200;
}

.task-card.task-overdue {
  @apply border-l-4 border-red-500;
}

.task-header {
  @apply flex items-center justify-between mb-4;
}

.task-priority {
  @apply p-2 rounded-full;
}

.priority-urgent {
  @apply bg-red-100 text-red-600;
}

.priority-high {
  @apply bg-warning-100 text-warning-600;
}

.priority-normal {
  @apply bg-gray-100 text-gray-600;
}

.priority-low {
  @apply bg-blue-100 text-blue-600;
}

.task-status {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-warning-100 text-warning-800;
}

.status-assigned {
  @apply bg-primary-100 text-primary-800;
}

.status-progress {
  @apply bg-info-100 text-info-800;
}

.status-completed {
  @apply bg-success-100 text-success-800;
}

.task-content {
  @apply space-y-3 mb-4;
}

.task-title {
  @apply text-lg font-semibold text-gray-800;
}

.task-description {
  @apply text-sm text-gray-600 line-clamp-2;
}

.task-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.task-type,
.task-deadline {
  @apply flex items-center;
}

.task-assignment {
  @apply mb-4;
}

.assigned-user {
  @apply flex items-center space-x-3;
}

.assignee-avatar {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white;
}

.avatar-primary {
  @apply bg-primary-500;
}

.avatar-success {
  @apply bg-success-500;
}

.avatar-warning {
  @apply bg-warning-500;
}

.avatar-info {
  @apply bg-info-500;
}

.assignee-name {
  @apply font-medium text-gray-800;
}

.assignee-role {
  @apply text-xs text-gray-500;
}

.task-actions {
  @apply flex space-x-2;
}

/* 列表视图样式 */
.tasks-list {
  @apply modern-card p-0;
}

.task-priority-icon {
  @apply p-1 rounded;
}

.assignee-avatar-small {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-white;
}

/* 对话框样式 */
.dialog-footer {
  @apply flex justify-end space-x-3;
}

.assignment-content {
  @apply space-y-6;
}

.task-info {
  @apply border-b border-gray-200 pb-4;
}

.task-requirements {
  @apply space-y-2;
}

.requirement-item {
  @apply flex items-start space-x-2;
}

.requirement-item label {
  @apply text-sm font-medium text-gray-600 min-w-max;
}

.skills-list {
  @apply flex flex-wrap gap-1;
}

.candidate-selection {
  @apply space-y-4;
}

.candidates-list {
  @apply space-y-3 max-h-64 overflow-y-auto;
}

.candidate-item {
  @apply flex items-center space-x-4 p-4 border border-gray-200 rounded-lg cursor-pointer transition-colors;
}

.candidate-item:hover {
  @apply bg-gray-50;
}

.candidate-item.selected {
  @apply border-primary-500 bg-primary-50;
}

.candidate-avatar {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white flex-shrink-0;
}

.candidate-info {
  @apply flex-1 min-w-0;
}

.candidate-name {
  @apply font-medium text-gray-800;
}

.candidate-role {
  @apply text-sm text-gray-600;
}

.candidate-workload {
  @apply text-xs text-gray-500;
}

.candidate-match {
  @apply flex-shrink-0 w-48;
}

.match-score {
  @apply flex items-center space-x-2 mb-2;
}

.score-label {
  @apply text-xs text-gray-600;
}

.score-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2 overflow-hidden;
}

.score-fill {
  @apply h-full bg-primary-500 transition-all duration-300;
}

.score-value {
  @apply text-xs font-medium text-gray-800;
}

.candidate-skills {
  @apply flex flex-wrap gap-1;
}
</style>