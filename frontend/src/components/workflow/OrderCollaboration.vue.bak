<!--
订单协作系统组件
版本: 1.0
创建时间: 2025-07-19

用于不同角色之间的订单协作和状态跟踪
特点：
1. 订单全生命周期跟踪
2. 角色间任务分配
3. 状态实时同步
4. 协作通信记录
-->

<template>
  <div class="order-collaboration">
    <!-- 协作订单列表 ---->
    <div class="collaboration-header">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-800">订单协作</h2>
        <div class="header-actions">
          <el-select 
            v-model="filterStatus" 
            placeholder="筛选状态" 
            size="small" 
            clearable
            @change="filterOrders"
          >
            <el-option 
              v-for="status in statusOptions" 
              :key="status.value"
              :label="status.label" 
              :value="status.value"
            />
          </el-select>
          <el-button size="small" @click="refreshOrders">
            <Refresh class="w-4 h-4 mr-1" />
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 订单列表 ---->
    <div class="orders-section">
      <div class="orders-grid">
        <div 
          v-for="order in filteredOrders" 
          :key="order.id"
          class="order-card"
          :class="getOrderStatusClass(order.status)"
          @click="selectOrder(order)"
        >
          <div class="order-header">
            <div class="order-info">
              <h3 class="order-number">{{ order.orderNumber }}</h3>
              <p class="order-material">{{ order.materialName }}</p>
            </div>
            <div class="order-status" :class="getStatusBadgeClass(order.status)">
              {{ getStatusLabel(order.status) }}
            </div>
          </div>
          
          <div class="order-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${order.progress}%` }"
                :class="getProgressClass(order.status)"
              ></div>
            </div>
            <span class="progress-text">{{ order.progress }}%</span>
          </div>

          <div class="order-participants">
            <div class="participant-roles">
              <span 
                v-for="participant in order.participants" 
                :key="participant.role"
                class="role-badge"
                :class="participant.status"
              >
                {{ participant.roleLabel }}
              </span>
            </div>
            <div class="order-actions">
              <el-button 
                v-if="canTakeAction(order)" 
                size="small" 
                type="primary"
                @click.stop="takeAction(order)"
              >
                {{ getActionLabel(order) }}
              </el-button>
              <el-button 
                size="small" 
                @click.stop="viewOrderDetails(order)"
              >
                详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情对话框 ---->
    <el-dialog 
      v-model="dialogVisible" 
      :title="`订单详情 - ${selectedOrder?.orderNumber}`"
      width="80%"
      :before-close="handleDialogClose"
    >
      <div v-if="selectedOrder" class="order-details">
        <!-- 基本信息 ---->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>订单号:</label>
              <span>{{ selectedOrder.orderNumber }}</span>
            </div>
            <div class="info-item">
              <label>航材名称:</label>
              <span>{{ selectedOrder.materialName }}</span>
            </div>
            <div class="info-item">
              <label>规格型号:</label>
              <span>{{ selectedOrder.specification }}</span>
            </div>
            <div class="info-item">
              <label>数量:</label>
              <span>{{ selectedOrder.quantity }}</span>
            </div>
            <div class="info-item">
              <label>总金额:</label>
              <span>¥{{ new Intl.NumberFormat('zh-CN').format(selectedOrder.amount) }}</span>
            </div>
            <div class="info-item">
              <label>紧急等级:</label>
              <span class="priority-badge" :class="selectedOrder.priority">
                {{ getPriorityLabel(selectedOrder.priority) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 流程跟踪 ---->
        <div class="detail-section">
          <h3 class="section-title">流程跟踪</h3>
          <div class="workflow-timeline">
            <div 
              v-for="(step, index) in selectedOrder.workflowSteps" 
              :key="step.id"
              class="timeline-item"
              :class="getTimelineItemClass(step)"
            >
              <div class="timeline-marker" :class="getTimelineMarkerClass(step)">
                <component :is="step.icon" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="step-header">
                  <h4 class="step-title">{{ step.title }}</h4>
                  <span class="step-role">{{ step.roleLabel }}</span>
                </div>
                <p class="step-description">{{ step.description }}</p>
                <div class="step-meta">
                  <span v-if="step.assignee" class="assignee">负责人: {{ step.assignee }}</span>
                  <span v-if="step.completedAt" class="time">{{ step.completedAt }}</span>
                  <span v-else-if="step.status === 'active'" class="time active">进行中</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 协作记录 ---->
        <div class="detail-section">
          <h3 class="section-title">协作记录</h3>
          <div class="collaboration-records">
            <div 
              v-for="record in selectedOrder.collaborationRecords" 
              :key="record.id"
              class="record-item"
            >
              <div class="record-header">
                <div class="record-user">
                  <span class="user-name">{{ record.userName }}</span>
                  <span class="user-role">{{ record.userRole }}</span>
                </div>
                <span class="record-time">{{ record.time }}</span>
              </div>
              <div class="record-content">{{ record.content }}</div>
              <div v-if="record.attachments" class="record-attachments">
                <el-tag 
                  v-for="attachment in record.attachments" 
                  :key="attachment.id"
                  size="small"
                  @click="downloadAttachment(attachment)"
                >
                  {{ attachment.name }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <!-- 添加记录 ---->
          <div class="add-record">
            <el-input
              v-model="newRecordContent"
              type="textarea"
              :rows="3"
              placeholder="添加协作记录..."
              @keydown.ctrl.enter="addCollaborationRecord"
            />
            <div class="record-actions">
              <el-upload
                :show-file-list="false"
                :before-upload="beforeUpload"
                action="#"
              >
                <el-button size="small">
                  <Paperclip class="w-4 h-4 mr-1" />
                  附件
                </el-button>
              </el-upload>
              <el-button 
                type="primary" 
                size="small"
                @click="addCollaborationRecord"
              >
                添加记录
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            v-if="canTakeAction(selectedOrder)" 
            type="primary"
            @click="takeActionInDialog"
          >
            {{ getActionLabel(selectedOrder) }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Paperclip, Clock, CheckCircle, CircleClose, 
  Document, Tools, Truck, Money
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  userRole: {
    type: String,
    required: true
  }
})

// Stores
const userStore = useUserStore()

// 响应式数据
const orders = ref([])
const filteredOrders = ref([])
const filterStatus = ref('')
const selectedOrder = ref(null)
const dialogVisible = ref(false)
const newRecordContent = ref('')

// 状态选项
const statusOptions = [
  { label: '待确认', value: 'pending' },
  { label: '生产中', value: 'production' },
  { label: '配送中', value: 'shipping' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// 计算属性
const canTakeAction = (order) => {
  const currentStep = order.workflowSteps.find(step => step.status === 'active')
  return currentStep && currentStep.role === props.userRole
}

const getActionLabel = (order) => {
  const currentStep = order.workflowSteps.find(step => step.status === 'active')
  if (!currentStep) return ''
  
  const actionLabels = {
    airline_buyer: '确认订单',
    platform_staff: '审核通过',
    maintenance_engineer: '确认规格',
    logistics_specialist: '安排配送'
  }
  
  return actionLabels[currentStep.role] || '处理'
}

// 方法
const getOrderStatusClass = (status) => {
  const classes = {
    pending: 'order-pending',
    production: 'order-production',
    shipping: 'order-shipping',
    completed: 'order-completed',
    cancelled: 'order-cancelled'
  }
  return classes[status] || ''
}

const getStatusBadgeClass = (status) => {
  const classes = {
    pending: 'status-pending',
    production: 'status-production',
    shipping: 'status-shipping',
    completed: 'status-completed',
    cancelled: 'status-cancelled'
  }
  return classes[status] || ''
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '待确认',
    production: '生产中',
    shipping: '配送中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getProgressClass = (status) => {
  const classes = {
    pending: 'progress-pending',
    production: 'progress-production',
    shipping: 'progress-shipping',
    completed: 'progress-completed',
    cancelled: 'progress-cancelled'
  }
  return classes[status] || ''
}

const getPriorityLabel = (priority) => {
  const labels = {
    urgent: '紧急',
    high: '高',
    normal: '普通',
    low: '低'
  }
  return labels[priority] || priority
}

const getTimelineItemClass = (step) => {
  return {
    'timeline-completed': step.status === 'completed',
    'timeline-active': step.status === 'active',
    'timeline-pending': step.status === 'pending'
  }
}

const getTimelineMarkerClass = (step) => {
  return {
    'marker-completed': step.status === 'completed',
    'marker-active': step.status === 'active',
    'marker-pending': step.status === 'pending'
  }
}

const selectOrder = (order) => {
  selectedOrder.value = order
  dialogVisible.value = true
}

const viewOrderDetails = (order) => {
  selectOrder(order)
}

const takeAction = async (order) => {
  try {
    const action = getActionLabel(order)
    await ElMessageBox.confirm(
      `确定要${action}吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟处理操作
    ElMessage.success(`${action}成功`)
    
    // 更新订单状态
    updateOrderStatus(order)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const takeActionInDialog = () => {
  if (selectedOrder.value) {
    takeAction(selectedOrder.value)
  }
}

const updateOrderStatus = (order) => {
  // 模拟状态更新逻辑
  const currentStepIndex = order.workflowSteps.findIndex(step => step.status === 'active')
  if (currentStepIndex !== -1) {
    // 完成当前步骤
    order.workflowSteps[currentStepIndex].status = 'completed'
    order.workflowSteps[currentStepIndex].completedAt = new Date().toLocaleString('zh-CN')
    
    // 激活下一步骤
    if (currentStepIndex + 1 < order.workflowSteps.length) {
      order.workflowSteps[currentStepIndex + 1].status = 'active'
      order.progress += 25
    } else {
      order.status = 'completed'
      order.progress = 100
    }
  }
}

const addCollaborationRecord = () => {
  if (!newRecordContent.value.trim()) return
  
  const newRecord = {
    id: Date.now(),
    userName: userStore.user?.username || '当前用户',
    userRole: props.userRole,
    content: newRecordContent.value,
    time: new Date().toLocaleString('zh-CN')
  }
  
  selectedOrder.value.collaborationRecords.unshift(newRecord)
  newRecordContent.value = ''
  
  ElMessage.success('记录添加成功')
}

const beforeUpload = (file) => {
  ElMessage.info('文件上传功能开发中...')
  return false
}

const downloadAttachment = (attachment) => {
  ElMessage.info('文件下载功能开发中...')
}

const filterOrders = () => {
  if (!filterStatus.value) {
    filteredOrders.value = orders.value
  } else {
    filteredOrders.value = orders.value.filter(order => order.status === filterStatus.value)
  }
}

const refreshOrders = () => {
  loadOrders()
  ElMessage.success('订单列表已刷新')
}

const handleDialogClose = () => {
  dialogVisible.value = false
  selectedOrder.value = null
  newRecordContent.value = ''
}

// 加载订单数据
const loadOrders = () => {
  // 模拟订单数据
  orders.value = [
    {
      id: '001',
      orderNumber: 'PO-2024-001',
      materialName: 'CFM56发动机燃油喷嘴',
      specification: 'CFM56-7B26/3',
      quantity: 2,
      amount: 125000,
      status: 'production',
      progress: 50,
      priority: 'urgent',
      participants: [
        { role: 'airline_buyer', roleLabel: '采购员', status: 'completed' },
        { role: 'platform_staff', roleLabel: '平台', status: 'completed' },
        { role: 'maintenance_engineer', roleLabel: '维修', status: 'active' },
        { role: 'logistics_specialist', roleLabel: '物流', status: 'pending' }
      ],
      workflowSteps: [
        {
          id: 1,
          title: '订单确认',
          description: '采购员确认订单详情和需求',
          role: 'airline_buyer',
          roleLabel: '采购员',
          status: 'completed',
          icon: Document,
          assignee: '张三',
          completedAt: '2024-07-18 10:30'
        },
        {
          id: 2,
          title: '平台审核',
          description: '平台员工审核订单合规性',
          role: 'platform_staff',
          roleLabel: '平台员工',
          status: 'completed',
          icon: CheckCircle,
          assignee: '李四',
          completedAt: '2024-07-18 14:20'
        },
        {
          id: 3,
          title: '技术确认',
          description: '维修工程师确认技术规格',
          role: 'maintenance_engineer',
          roleLabel: '维修工程师',
          status: 'active',
          icon: Tools,
          assignee: '王五'
        },
        {
          id: 4,
          title: '物流安排',
          description: '物流专员安排配送计划',
          role: 'logistics_specialist',
          roleLabel: '物流专员',
          status: 'pending',
          icon: Truck
        }
      ],
      collaborationRecords: [
        {
          id: 1,
          userName: '张三',
          userRole: '采购员',
          content: '紧急需求，请优先处理',
          time: '2024-07-18 10:35'
        },
        {
          id: 2,
          userName: '李四',
          userRole: '平台员工',
          content: '订单已审核通过，转交技术部门',
          time: '2024-07-18 14:25'
        }
      ]
    },
    {
      id: '002',
      orderNumber: 'PO-2024-002',
      materialName: '起落架轴承',
      specification: 'B737-800',
      quantity: 4,
      amount: 85000,
      status: 'shipping',
      progress: 75,
      priority: 'high',
      participants: [
        { role: 'airline_buyer', roleLabel: '采购员', status: 'completed' },
        { role: 'platform_staff', roleLabel: '平台', status: 'completed' },
        { role: 'maintenance_engineer', roleLabel: '维修', status: 'completed' },
        { role: 'logistics_specialist', roleLabel: '物流', status: 'active' }
      ],
      workflowSteps: [
        {
          id: 1,
          title: '订单确认',
          description: '采购员确认订单详情和需求',
          role: 'airline_buyer',
          roleLabel: '采购员',
          status: 'completed',
          icon: Document,
          assignee: '赵六',
          completedAt: '2024-07-17 09:15'
        },
        {
          id: 2,
          title: '平台审核',
          description: '平台员工审核订单合规性',
          role: 'platform_staff',
          roleLabel: '平台员工',
          status: 'completed',
          icon: CheckCircle,
          assignee: '李四',
          completedAt: '2024-07-17 11:30'
        },
        {
          id: 3,
          title: '技术确认',
          description: '维修工程师确认技术规格',
          role: 'maintenance_engineer',
          roleLabel: '维修工程师',
          status: 'completed',
          icon: Tools,
          assignee: '王五',
          completedAt: '2024-07-18 16:45'
        },
        {
          id: 4,
          title: '物流安排',
          description: '物流专员安排配送计划',
          role: 'logistics_specialist',
          roleLabel: '物流专员',
          status: 'active',
          icon: Truck,
          assignee: '孙七'
        }
      ],
      collaborationRecords: [
        {
          id: 1,
          userName: '赵六',
          userRole: '采购员',
          content: '请确认轴承规格是否正确',
          time: '2024-07-17 09:20'
        },
        {
          id: 2,
          userName: '王五',
          userRole: '维修工程师',
          content: '规格已确认，可以发货',
          time: '2024-07-18 16:50'
        }
      ]
    }
  ]
  
  filteredOrders.value = orders.value
}

// 生命周期
onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.order-collaboration {
  @apply space-y-6;
}

.collaboration-header {
  @apply modern-card p-4;
}

.header-actions {
  @apply flex items-center space-x-3;
}

.orders-section {
  @apply space-y-4;
}

.orders-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6;
}

.order-card {
  @apply modern-card p-6 cursor-pointer transition-all duration-200;
}

.order-card:hover {
  @apply transform -translate-y-1 shadow-lg;
}

.order-card.order-pending {
  @apply border-l-4 border-warning-500;
}

.order-card.order-production {
  @apply border-l-4 border-primary-500;
}

.order-card.order-shipping {
  @apply border-l-4 border-info-500;
}

.order-card.order-completed {
  @apply border-l-4 border-success-500;
}

.order-card.order-cancelled {
  @apply border-l-4 border-red-500;
}

.order-header {
  @apply flex items-start justify-between mb-4;
}

.order-number {
  @apply text-lg font-semibold text-gray-800;
}

.order-material {
  @apply text-sm text-gray-600 mt-1;
}

.order-status {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-warning-100 text-warning-800;
}

.status-production {
  @apply bg-primary-100 text-primary-800;
}

.status-shipping {
  @apply bg-info-100 text-info-800;
}

.status-completed {
  @apply bg-success-100 text-success-800;
}

.status-cancelled {
  @apply bg-red-100 text-red-800;
}

.order-progress {
  @apply flex items-center space-x-3 mb-4;
}

.progress-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full transition-all duration-300;
}

.progress-pending {
  @apply bg-warning-500;
}

.progress-production {
  @apply bg-primary-500;
}

.progress-shipping {
  @apply bg-info-500;
}

.progress-completed {
  @apply bg-success-500;
}

.progress-cancelled {
  @apply bg-red-500;
}

.progress-text {
  @apply text-sm font-medium text-gray-600;
}

.order-participants {
  @apply space-y-3;
}

.participant-roles {
  @apply flex flex-wrap gap-2;
}

.role-badge {
  @apply px-2 py-1 rounded text-xs font-medium;
}

.role-badge.completed {
  @apply bg-success-100 text-success-800;
}

.role-badge.active {
  @apply bg-primary-100 text-primary-800;
}

.role-badge.pending {
  @apply bg-gray-100 text-gray-600;
}

.order-actions {
  @apply flex space-x-2;
}

/* 对话框样式 */
.order-details {
  @apply space-y-6;
}

.detail-section {
  @apply border-b border-gray-200 pb-6 last:border-b-0;
}

.section-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.info-item {
  @apply flex flex-col space-y-1;
}

.info-item label {
  @apply text-sm font-medium text-gray-600;
}

.info-item span {
  @apply text-gray-800;
}

.priority-badge {
  @apply px-2 py-1 rounded text-xs font-medium;
}

.priority-badge.urgent {
  @apply bg-red-100 text-red-800;
}

.priority-badge.high {
  @apply bg-warning-100 text-warning-800;
}

.priority-badge.normal {
  @apply bg-gray-100 text-gray-800;
}

.priority-badge.low {
  @apply bg-blue-100 text-blue-800;
}

/* 时间线样式 */
.workflow-timeline {
  @apply space-y-4;
}

.timeline-item {
  @apply flex items-start space-x-4;
}

.timeline-marker {
  @apply w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1;
}

.marker-completed {
  @apply bg-success-500 text-white;
}

.marker-active {
  @apply bg-primary-500 text-white;
}

.marker-pending {
  @apply bg-gray-300 text-gray-600;
}

.timeline-content {
  @apply flex-1 min-w-0;
}

.step-header {
  @apply flex items-center justify-between mb-2;
}

.step-title {
  @apply font-semibold text-gray-800;
}

.step-role {
  @apply text-sm text-primary-600;
}

.step-description {
  @apply text-gray-600 text-sm mb-2;
}

.step-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.time.active {
  @apply text-primary-600 font-medium;
}

/* 协作记录样式 */
.collaboration-records {
  @apply space-y-4 max-h-64 overflow-y-auto;
}

.record-item {
  @apply bg-gray-50 rounded-lg p-4;
}

.record-header {
  @apply flex items-center justify-between mb-2;
}

.record-user {
  @apply flex items-center space-x-2;
}

.user-name {
  @apply font-medium text-gray-800;
}

.user-role {
  @apply text-sm text-primary-600;
}

.record-time {
  @apply text-xs text-gray-500;
}

.record-content {
  @apply text-gray-700 mb-2;
}

.record-attachments {
  @apply flex flex-wrap gap-2;
}

.add-record {
  @apply space-y-3 mt-4 pt-4 border-t border-gray-200;
}

.record-actions {
  @apply flex items-center justify-end space-x-2;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}
</style>