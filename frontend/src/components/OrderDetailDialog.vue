<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单详情"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div v-loading="loading">
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-4">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="font-semibold">订单信息</span>
              <div class="flex items-center space-x-2">
                <el-tag :type="getStatusType(orderDetail.status)" size="large">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
                <el-tag :type="getPriorityType(orderDetail.priority)">
                  {{ getPriorityText(orderDetail.priority) }}
                </el-tag>
              </div>
            </div>
          </template>

          <el-descriptions :column="3" border>
            <el-descriptions-item label="订单号">
              <span class="font-mono">{{ orderDetail.order_number }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusType(orderDetail.status)">
                {{ getStatusText(orderDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(orderDetail.priority)">
                {{ getPriorityText(orderDetail.priority) }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="订单总金额">
              <span class="text-lg font-semibold text-green-600">
                {{ formatCurrency(orderDetail.total_amount) }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="货币类型">
              {{ orderDetail.currency || 'CNY' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(orderDetail.created_at) }}
            </el-descriptions-item>

            <el-descriptions-item label="交货地址" :span="2">
              {{ orderDetail.delivery_address }}
            </el-descriptions-item>
            <el-descriptions-item label="交货日期">
              {{ formatDateTime(orderDetail.delivery_date) }}
            </el-descriptions-item>

            <el-descriptions-item label="订单备注" :span="3" v-if="orderDetail.notes">
              {{ orderDetail.notes }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 审批信息 -->
        <el-card class="mb-4" v-if="orderDetail.requires_approval">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="font-semibold">审批信息</span>
              <el-tag :type="getApprovalStatusType(orderDetail.approval_status)">
                {{ getApprovalStatusText(orderDetail.approval_status) }}
              </el-tag>
            </div>
          </template>

          <div v-if="orderDetail.approvals && orderDetail.approvals.length > 0">
            <div
              v-for="approval in orderDetail.approvals"
              :key="approval.id"
              class="border rounded-lg p-4 mb-3 last:mb-0"
            >
              <div class="flex justify-between items-start">
                <div>
                  <div class="font-medium">审批级别 {{ approval.approval_level }}</div>
                  <div class="text-sm text-gray-600 mt-1">
                    审批人: {{ approval.approver?.username || '未指定' }}
                  </div>
                </div>
                <el-tag :type="getApprovalStatusType(approval.status)">
                  {{ getApprovalStatusText(approval.status) }}
                </el-tag>
              </div>

              <div v-if="approval.comments" class="mt-3 p-3 bg-gray-50 rounded">
                <div class="text-sm text-gray-600 mb-1">审批意见:</div>
                <div>{{ approval.comments }}</div>
              </div>

              <div v-if="approval.approved_at" class="text-sm text-gray-500 mt-2">
                审批时间: {{ formatDateTime(approval.approved_at) }}
              </div>
            </div>
          </div>

          <div v-else class="text-center text-gray-500 py-4">
            <Clock class="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>等待审批中...</p>
          </div>
        </el-card>

        <!-- 订单项目 -->
        <el-card class="mb-4">
          <template #header>
            <span class="font-semibold">订单项目</span>
          </template>

          <el-table :data="orderDetail.items" border>
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column label="航材信息" min-width="200">
              <template #default="{ row }">
                <div>
                  <div class="font-medium">{{ row.material?.part_name }}</div>
                  <div class="text-sm text-gray-600">{{ row.material?.part_number }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="数量" prop="quantity" width="80" />
            <el-table-column label="单价" width="120">
              <template #default="{ row }">
                {{ formatCurrency(row.unit_price) }}
              </template>
            </el-table-column>
            <el-table-column label="小计" width="120">
              <template #default="{ row }">
                {{ formatCurrency(row.subtotal) }}
              </template>
            </el-table-column>
            <el-table-column label="状况代码" prop="condition_code" width="100" />
            <el-table-column label="交货要求" prop="delivery_requirement" min-width="150" />
          </el-table>
        </el-card>

        <!-- 状态历史 -->
        <el-card v-if="orderDetail.status_history && orderDetail.status_history.length > 0">
          <template #header>
            <span class="font-semibold">状态历史</span>
          </template>

          <el-timeline>
            <el-timeline-item
              v-for="history in orderDetail.status_history"
              :key="history.id"
              :timestamp="formatDateTime(history.created_at)"
              placement="top"
            >
              <div class="flex items-center space-x-2 mb-2">
                <el-tag :type="getStatusType(history.to_status)" size="small">
                  {{ getStatusText(history.to_status) }}
                </el-tag>
                <span class="text-sm text-gray-600">
                  操作人: {{ history.operator?.username || '系统' }}
                </span>
              </div>

              <div v-if="history.reason" class="text-sm text-gray-700 mb-1">
                原因: {{ history.reason }}
              </div>

              <div v-if="history.notes" class="text-sm text-gray-600">
                备注: {{ history.notes }}
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <div v-else class="text-center py-8">
        <Box class="w-12 h-12 mx-auto mb-2 text-gray-400" />
        <p class="text-gray-500">订单信息加载中...</p>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div>
          <!-- 操作按钮 -->
          <el-button
            v-if="canUpdateStatus"
            type="primary"
            @click="showStatusUpdateDialog = true"
          >
            更新状态
          </el-button>

          <el-button
            v-if="canApprove"
            type="success"
            @click="handleApprove"
          >
            审批通过
          </el-button>

          <el-button
            v-if="canApprove"
            type="danger"
            @click="handleReject"
          >
            审批拒绝
          </el-button>
        </div>

        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

    <!-- 状态更新对话框 -->
    <el-dialog
      v-model="showStatusUpdateDialog"
      title="更新订单状态"
      width="500px"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="新状态">
          <el-select v-model="statusForm.status" placeholder="请选择新状态">
            <el-option
              v-for="status in availableStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="变更原因">
          <el-input
            v-model="statusForm.reason"
            placeholder="请输入状态变更原因"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="statusForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showStatusUpdateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleStatusUpdate" :loading="updating">
          确认更新
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入
import { ordersApi } from '@/api/orders'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  modelValue: Boolean,
  orderId: [String, Number]
})

const emit = defineEmits(['update:modelValue', 'updated'])

const authStore = useAuthStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 响应式数据
const loading = ref(false)
const updating = ref(false)
const orderDetail = ref(null)
const showStatusUpdateDialog = ref(false)

// 状态更新表单
const statusForm = reactive({
  status: '',
  reason: '',
  notes: ''
})

// 监听对话框显示状态和订单ID变化
watch([dialogVisible, () => props.orderId], ([visible, orderId]) => {
  if (visible && orderId) {
    loadOrderDetail()
  }
})

// 计算属性
const canUpdateStatus = computed(() => {
  if (!orderDetail.value) return false

  const currentUserId = authStore.user?.id
  const order = orderDetail.value

  // 买方和供应商都可以更新状态，但有不同的权限
  return order.buyer_id === currentUserId || order.supplier_id === currentUserId
})

const canApprove = computed(() => {
  if (!orderDetail.value) return false

  // 简化处理：管理员可以审批
  return authStore.user?.user_type === 'admin' &&
         orderDetail.value.requires_approval &&
         orderDetail.value.approval_status === 'pending'
})

const availableStatuses = computed(() => {
  if (!orderDetail.value) return []

  const currentStatus = orderDetail.value.status
  const statusMap = {
    'pending': [
      { value: 'confirmed', label: '已确认' },
      { value: 'cancelled', label: '已取消' }
    ],
    'confirmed': [
      { value: 'processing', label: '处理中' },
      { value: 'cancelled', label: '已取消' }
    ],
    'processing': [
      { value: 'shipping', label: '已发货' },
      { value: 'cancelled', label: '已取消' }
    ],
    'shipping': [
      { value: 'completed', label: '已完成' }
    ],
    'completed': [],
    'cancelled': []
  }

  return statusMap[currentStatus] || []
})

// 方法
const loadOrderDetail = async () => {
  try {
    loading.value = true

    const response = await ordersApi.getOrderDetail(props.orderId)
    if (response.success) {
      orderDetail.value = response.body
    } else {
      ElMessage.error(response.message || '加载订单详情失败')
    }

  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleStatusUpdate = async () => {
  try {
    if (!statusForm.status) {
      ElMessage.error('请选择新状态')
      return
    }

    updating.value = true

    const response = await ordersApi.updateOrderStatus(props.orderId, {
      status: statusForm.status,
      reason: statusForm.reason,
      notes: statusForm.notes
    })

    if (response.success) {
      ElMessage.success('订单状态更新成功')
      showStatusUpdateDialog.value = false

      // 重新加载订单详情
      await loadOrderDetail()

      // 通知父组件更新
      emit('updated')
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }

  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败，请重试')
  } finally {
    updating.value = false
  }
}

const handleApprove = async () => {
  try {
    const result = await ElMessageBox.prompt('请输入审批意见', '审批通过', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入审批意见（可选）'
    })

    // 调用审批API
    const response = await ordersApi.approveOrder(props.orderId, {
      action: 'approve',
      approval_level: 1, // 简化处理
      comments: result.value
    })

    if (response.success) {
      ElMessage.success('审批通过')
      await loadOrderDetail()
      emit('updated')
    } else {
      ElMessage.error(response.message || '审批失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
      ElMessage.error('审批失败，请重试')
    }
  }
}

const handleReject = async () => {
  try {
    const result = await ElMessageBox.prompt('请输入拒绝原因', '审批拒绝', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入拒绝原因',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '请输入拒绝原因'
        }
        return true
      }
    })

    // 调用审批API
    const response = await ordersApi.approveOrder(props.orderId, {
      action: 'reject',
      approval_level: 1, // 简化处理
      comments: result.value
    })

    if (response.success) {
      ElMessage.success('审批已拒绝')
      await loadOrderDetail()
      emit('updated')
    } else {
      ElMessage.error(response.message || '审批失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
      ElMessage.error('审批失败，请重试')
    }
  }
}

// 工具函数
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'confirmed': 'info',
    'processing': 'primary',
    'shipping': 'success',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'processing': '处理中',
    'shipping': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

const getPriorityType = (priority) => {
  const typeMap = {
    'aog': 'danger',
    'high': 'warning',
    'normal': 'info',
    'low': 'info'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const textMap = {
    'aog': 'AOG紧急',
    'high': '高优先级',
    'normal': '普通',
    'low': '低优先级'
  }
  return textMap[priority] || priority
}

const getApprovalStatusType = (status) => {
  const typeMap = {
    'not_required': 'info',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getApprovalStatusText = (status) => {
  const textMap = {
    'not_required': '无需审批',
    'pending': '待审批',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return textMap[status] || status
}

const formatCurrency = (amount) => {
  if (!amount) return '¥0.00'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>