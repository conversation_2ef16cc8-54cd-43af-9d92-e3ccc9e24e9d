<!--
权限控制组件使用示例
版本: 1.0
创建时间: 2025-07-22

展示 PermissionGate 组件的各种使用方法和场景
-->

<template>
  <div class="permission-examples">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">权限控制组件使用示例</h2>
    
    <!-- 基础权限控制 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">1. 基础权限控制</h3>
      
      <!-- 单一权限验证 -->
      <PermissionGate permission="publish_demand">
        <el-button type="primary" icon="Plus">
          发布需求
        </el-button>
      </PermissionGate>
      
      <!-- 多权限验证（任一） -->
      <PermissionGate :permission="['manage_orders', 'view_all_data']" mode="any">
        <el-button type="success" icon="View">
          查看订单
        </el-button>
      </PermissionGate>
      
      <!-- 多权限验证（全部） -->
      <PermissionGate :permission="['manage_inventory', 'publish_shared']" mode="all">
        <el-button type="warning" icon="Share">
          发布共享航材
        </el-button>
      </PermissionGate>
    </div>
    
    <!-- 角色和用户类型控制 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">2. 角色和用户类型控制</h3>
      
      <!-- 单一角色验证 -->
      <PermissionGate role="admin">
        <el-button type="danger" icon="Setting">
          系统配置
        </el-button>
      </PermissionGate>
      
      <!-- 多角色验证 -->
      <PermissionGate :role="['admin', 'platform_staff']">
        <el-button type="info" icon="Avatar">
          用户管理
        </el-button>
      </PermissionGate>
      
      <!-- 用户类型验证 -->
      <PermissionGate :userType="['airline_buyer', 'platform_staff']">
        <el-button icon="Search">
          高级搜索
        </el-button>
      </PermissionGate>
    </div>
    
    <!-- 公司类型和权限等级 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">3. 公司类型和权限等级</h3>
      
      <!-- 内部员工功能 -->
      <PermissionGate companyType="internal">
        <el-button type="primary" icon="View">
          查看所有数据
        </el-button>
      </PermissionGate>
      
      <!-- 权限等级验证 -->
      <PermissionGate :permissionLevel="3">
        <el-button type="warning" icon="Key">
          高级功能
        </el-button>
      </PermissionGate>
      
      <!-- 组合验证：权限+角色 -->
      <PermissionGate permission="audit_shared_materials" :userType="['platform_staff', 'admin']">
        <el-button type="success" icon="CircleCheck">
          审核共享航材
        </el-button>
      </PermissionGate>
    </div>
    
    <!-- 资源所有权控制 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">4. 资源所有权控制</h3>
      
      <!-- 示例订单操作按钮 -->
      <div class="space-y-2" v-for="order in exampleOrders" :key="order.id">
        <div class="border p-3 rounded">
          <h4 class="font-medium">订单 #{{ order.id }} (所有者: {{ order.owner_name }})</h4>
          
          <!-- 只有资源所有者或管理员可以编辑 -->
          <PermissionGate :resourceOwnerId="order.owner_id">
            <el-button size="small" type="primary" icon="Edit">
              编辑订单
            </el-button>
          </PermissionGate>
          
          <!-- 允许跨公司访问的删除操作 -->
          <PermissionGate :resourceOwnerId="order.owner_id" :allowCrossCompany="true">
            <el-button size="small" type="danger" icon="Delete">
              删除订单
            </el-button>
          </PermissionGate>
          
          <!-- 平台员工可以查看所有订单详情 -->
          <PermissionGate permission="view_all_data">
            <el-button size="small" type="info" icon="View">
              查看详情
            </el-button>
          </PermissionGate>
        </div>
      </div>
    </div>
    
    <!-- 反向控制和自定义验证 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">5. 高级控制</h3>
      
      <!-- 反向控制：没有权限时显示 -->
      <PermissionGate permission="admin_features" :reverse="true">
        <el-alert
          title="权限不足"
          description="您需要管理员权限才能访问此功能"
          type="warning"
          show-icon
          :closable="false"
          class="mb-4"
        />
      </PermissionGate>
      
      <!-- 自定义验证函数 -->
      <PermissionGate :customValidator="customPermissionCheck">
        <el-button type="success" icon="Star">
          VIP功能
        </el-button>
      </PermissionGate>
      
      <!-- 调试模式 -->
      <PermissionGate permission="publish_demand" :debug="true">
        <el-button type="primary">
          调试模式按钮
        </el-button>
      </PermissionGate>
    </div>
    
    <!-- 菜单导航权限控制 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">6. 菜单导航权限控制</h3>
      
      <el-menu mode="horizontal" class="border-none">
        <PermissionGate permission="view_own_data">
          <el-menu-item index="demands">需求管理</el-menu-item>
        </PermissionGate>
        
        <PermissionGate permission="manage_own_inventory">
          <el-menu-item index="inventory">库存管理</el-menu-item>
        </PermissionGate>
        
        <PermissionGate permission="manage_orders">
          <el-menu-item index="orders">订单管理</el-menu-item>
        </PermissionGate>
        
        <PermissionGate :permission="['audit_shared_materials', 'user_management']" mode="any">
          <el-sub-menu index="admin" title="管理功能">
            <PermissionGate permission="audit_shared_materials">
              <el-menu-item index="shared-review">共享件审核</el-menu-item>
            </PermissionGate>
            <PermissionGate permission="user_management">
              <el-menu-item index="user-management">用户管理</el-menu-item>
            </PermissionGate>
          </el-sub-menu>
        </PermissionGate>
      </el-menu>
    </div>
    
    <!-- 表格操作列权限控制 -->
    <div class="example-section">
      <h3 class="text-lg font-semibold text-gray-700 mb-4">7. 表格操作列权限控制</h3>
      
      <el-table :data="exampleGridData" style="width: 100%">
        <el-table-column prop="name" label="航材名称" width="180" />
        <el-table-column prop="category" label="类别" width="120" />
        <el-table-column prop="status" label="状态" width="120" />
        
        <el-table-column label="操作" width="300">
          <template #default="scope">
            <PermissionGate permission="manage_own_inventory">
              <el-button size="small" type="primary">编辑</el-button>
            </PermissionGate>
            
            <PermissionGate :resourceOwnerId="scope.row.owner_id">
              <el-button size="small" type="warning">转移</el-button>
            </PermissionGate>
            
            <PermissionGate permission="publish_shared">
              <el-button size="small" type="success">共享</el-button>
            </PermissionGate>
            
            <PermissionGate :permissionLevel="3">
              <el-button size="small" type="danger">删除</el-button>
            </PermissionGate>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import PermissionGate from './PermissionGate.vue'
import { useAuthStore } from '@/stores/auth'
import { 
  Plus, View, Share, Setting, Avatar, Search, Key, CircleCheck, 
  Edit, Delete, Star
} from '@element-plus/icons-vue'

export default {
  name: 'PermissionGateExamples',
  components: {
    PermissionGate
  },
  setup() {
    const authStore = useAuthStore()
    
    // 示例数据
    const exampleOrders = [
      { id: 1001, owner_id: 1, owner_name: 'Alice' },
      { id: 1002, owner_id: 2, owner_name: 'Bob' },
      { id: 1003, owner_id: 1, owner_name: 'Alice' }
    ]
    
    const exampleGridData = [
      { 
        name: 'A380发动机叶片', 
        category: '发动机', 
        status: '正常',
        owner_id: 1
      },
      { 
        name: 'B737机轮', 
        category: '起落架', 
        status: '维修中',
        owner_id: 2
      },
      { 
        name: '航电控制单元', 
        category: '航电', 
        status: '正常',
        owner_id: 1
      }
    ]
    
    // 自定义权限验证函数
    const customPermissionCheck = (user, permissions) => {
      // 示例：VIP用户或高等级用户才能访问
      return user && (
        user.permission_level >= 2 || 
        permissions.includes('advanced_features')
      )
    }
    
    return {
      exampleOrders,
      exampleGridData,
      customPermissionCheck,
      // Element Plus 图标
      Plus, View, Share, Setting, Avatar, Search, Key, CircleCheck, 
      Edit, Delete, Star
    }
  }
}
</script>

<style scoped>
.permission-examples {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.example-section .el-button {
  margin: 0 8px 8px 0;
}

.space-y-2 > * + * {
  margin-top: 8px;
}
</style>