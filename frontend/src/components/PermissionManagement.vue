<template>
  <el-dialog
    :model-value="modelValue"
    :title="`配置角色权限 - ${role?.display_name}`"
    width="900px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <div v-loading="loading" class="permission-management">
      <!-- 角色信息 -->
      <div class="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
        <div 
          class="w-12 h-12 rounded-lg flex items-center justify-center"
          :style="{ 
            backgroundColor: role?.theme_color + '20' || '#f3f4f6',
            color: role?.theme_color || '#6b7280'
          }"
        >
          <el-icon :size="24">
            <component :is="getIconComponent(role?.icon_name)" />
          </el-icon>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">{{ role?.display_name }}</h3>
          <p class="text-sm text-gray-600">{{ role?.description }}</p>
          <div class="flex gap-2 mt-1">
            <el-tag 
              :type="role?.category === 'internal' ? 'warning' : 'info'"
              size="small"
            >
              {{ role?.category === 'internal' ? '内部员工' : '外部用户' }}
            </el-tag>
            <el-tag size="small">{{ getBusinessTypeText(role?.business_type) }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 权限搜索和筛选 -->
      <div class="flex space-x-4 mb-4">
        <el-input
          v-model="searchQuery"
          placeholder="搜索权限..."
          clearable
          class="flex-1"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterCategory"
          placeholder="权限分类"
          clearable
          style="width: 200px"
        >
          <el-option 
            v-for="category in permissionCategories" 
            :key="category" 
            :label="category" 
            :value="category" 
          />
        </el-select>
      </div>

      <!-- 权限统计 -->
      <div class="grid grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-blue-600">{{ totalPermissions }}</div>
          <div class="text-sm text-blue-600">总权限数</div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-green-600">{{ selectedPermissions.length }}</div>
          <div class="text-sm text-green-600">已分配</div>
        </div>
        <div class="bg-orange-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-orange-600">{{ totalPermissions - selectedPermissions.length }}</div>
          <div class="text-sm text-orange-600">未分配</div>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-purple-600">{{ Math.round((selectedPermissions.length / totalPermissions) * 100) }}%</div>
          <div class="text-sm text-purple-600">分配率</div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex space-x-2">
          <el-button size="small" @click="selectAll">全选</el-button>
          <el-button size="small" @click="clearAll">清空</el-button>
          <el-button size="small" @click="selectByCategory('基础权限')">选择基础权限</el-button>
          <el-button size="small" @click="selectByCategory('业务权限')">选择业务权限</el-button>
        </div>
        
        <div class="text-sm text-gray-500">
          已选择 {{ selectedPermissions.length }} / {{ filteredPermissions.length }} 项
        </div>
      </div>

      <!-- 权限列表 -->
      <div class="max-h-96 overflow-y-auto border rounded-lg">
        <div v-if="groupedPermissions.length === 0" class="text-center py-8 text-gray-500">
          没有找到相关权限
        </div>
        
        <div v-else>
          <div 
            v-for="group in groupedPermissions" 
            :key="group.category"
            class="border-b last:border-b-0"
          >
            <!-- 分类标题 -->
            <div class="bg-gray-50 px-4 py-3 font-medium text-gray-700 sticky top-0">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <el-icon><FolderOpened /></el-icon>
                  <span>{{ group.category }}</span>
                  <el-tag size="small" type="info">{{ group.permissions.length }}</el-tag>
                </div>
                <div class="flex items-center space-x-2">
                  <el-checkbox
                    :model-value="isCategorySelected(group.category)"
                    :indeterminate="isCategoryIndeterminate(group.category)"
                    @change="toggleCategory(group.category, $event)"
                  >
                    {{ isCategorySelected(group.category) ? '全部取消' : '全部选择' }}
                  </el-checkbox>
                </div>
              </div>
            </div>

            <!-- 权限项 -->
            <div class="divide-y">
              <div 
                v-for="permission in group.permissions" 
                :key="permission.id"
                class="px-4 py-3 hover:bg-gray-50 transition-colors"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3 flex-1">
                    <el-checkbox
                      :model-value="selectedPermissions.includes(permission.id)"
                      @change="togglePermission(permission.id, $event)"
                    />
                    
                    <div class="flex-1">
                      <div class="font-medium text-gray-900">{{ permission.permission_name }}</div>
                      <div class="text-sm text-gray-500">{{ permission.description }}</div>
                      <div class="flex items-center space-x-2 mt-1">
                        <el-tag size="small" type="warning">{{ permission.module }}</el-tag>
                        <span class="text-xs text-gray-400">{{ permission.permission_code }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 权限风险等级 -->
                  <div class="ml-4">
                    <el-tag 
                      :type="getPermissionRiskType(permission)"
                      size="small"
                      effect="light"
                    >
                      {{ getPermissionRiskText(permission) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限变更摘要 -->
      <div v-if="hasChanges" class="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div class="flex items-start space-x-3">
          <el-icon class="text-yellow-600 mt-0.5"><WarningFilled /></el-icon>
          <div class="flex-1">
            <h4 class="font-medium text-yellow-800">权限变更摘要</h4>
            <div class="text-sm text-yellow-700 mt-1 space-y-1">
              <div v-if="addedPermissions.length">
                新增权限：{{ addedPermissions.map(p => p.permission_name).join(', ') }}
              </div>
              <div v-if="removedPermissions.length">
                移除权限：{{ removedPermissions.map(p => p.permission_name).join(', ') }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="text-sm text-gray-500">
          注意：权限变更将立即生效，请谨慎操作
        </div>
        <div class="flex space-x-3">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            :loading="saving"
            :disabled="!hasChanges"
            @click="savePermissions"
          >
            保存权限配置
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { permissionsApi } from '@/api/permissions'
import { rolesApi } from '@/api/roles'

// Props & Emits
const props = defineProps({
  modelValue: Boolean,
  role: Object
})

const emit = defineEmits(['update:modelValue', 'updated'])

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const filterCategory = ref('')
const allPermissions = ref([])
const permissionCategories = ref([])
const selectedPermissions = ref([])
const originalPermissions = ref([])

// 计算属性
const totalPermissions = computed(() => allPermissions.value.length)

const filteredPermissions = computed(() => {
  let filtered = allPermissions.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(permission => 
      permission.permission_name.toLowerCase().includes(query) ||
      permission.description?.toLowerCase().includes(query) ||
      permission.permission_code.toLowerCase().includes(query)
    )
  }

  // 分类过滤
  if (filterCategory.value) {
    filtered = filtered.filter(permission => permission.category === filterCategory.value)
  }

  return filtered
})

const groupedPermissions = computed(() => {
  const groups = {}
  filteredPermissions.value.forEach(permission => {
    const category = permission.category || '其他'
    if (!groups[category]) {
      groups[category] = {
        category,
        permissions: []
      }
    }
    groups[category].permissions.push(permission)
  })
  
  return Object.values(groups).sort((a, b) => {
    const order = ['基础权限', '业务权限', '管理权限', '其他']
    return order.indexOf(a.category) - order.indexOf(b.category)
  })
})

const hasChanges = computed(() => {
  const original = new Set(originalPermissions.value)
  const current = new Set(selectedPermissions.value)
  
  if (original.size !== current.size) return true
  
  for (const id of current) {
    if (!original.has(id)) return true
  }
  
  return false
})

const addedPermissions = computed(() => {
  const original = new Set(originalPermissions.value)
  return allPermissions.value.filter(p => 
    selectedPermissions.value.includes(p.id) && !original.has(p.id)
  )
})

const removedPermissions = computed(() => {
  const current = new Set(selectedPermissions.value)
  return allPermissions.value.filter(p => 
    originalPermissions.value.includes(p.id) && !current.has(p.id)
  )
})

// 方法
const loadPermissions = async () => {
  loading.value = true
  try {
    // 加载所有权限
    const permissionsResponse = await permissionsApi.getAllPermissions()
    if (permissionsResponse.success) {
      allPermissions.value = permissionsResponse.body.permissions || []
    }

    // 加载权限分类
    const categoriesResponse = await permissionsApi.getPermissionCategories()
    if (categoriesResponse.success) {
      permissionCategories.value = categoriesResponse.body.categories || []
    }

    // 加载角色当前权限
    if (props.role?.id) {
      const rolePermissionsResponse = await rolesApi.getRolePermissions(props.role.id)
      if (rolePermissionsResponse.success) {
        const rolePermissions = rolePermissionsResponse.body.permissions || []
        selectedPermissions.value = rolePermissions.map(p => p.id)
        originalPermissions.value = [...selectedPermissions.value]
      }
    }
  } catch (error) {
    console.error('加载权限数据失败:', error)
    ElMessage.error('加载权限数据失败')
  } finally {
    loading.value = false
  }
}

const togglePermission = (permissionId, checked) => {
  if (checked) {
    if (!selectedPermissions.value.includes(permissionId)) {
      selectedPermissions.value.push(permissionId)
    }
  } else {
    const index = selectedPermissions.value.indexOf(permissionId)
    if (index > -1) {
      selectedPermissions.value.splice(index, 1)
    }
  }
}

const toggleCategory = (category, checked) => {
  const categoryPermissions = allPermissions.value
    .filter(p => p.category === category)
    .map(p => p.id)

  if (checked) {
    // 添加该分类的所有权限
    categoryPermissions.forEach(id => {
      if (!selectedPermissions.value.includes(id)) {
        selectedPermissions.value.push(id)
      }
    })
  } else {
    // 移除该分类的所有权限
    selectedPermissions.value = selectedPermissions.value.filter(
      id => !categoryPermissions.includes(id)
    )
  }
}

const isCategorySelected = (category) => {
  const categoryPermissions = allPermissions.value
    .filter(p => p.category === category)
    .map(p => p.id)
  
  return categoryPermissions.length > 0 && 
         categoryPermissions.every(id => selectedPermissions.value.includes(id))
}

const isCategoryIndeterminate = (category) => {
  const categoryPermissions = allPermissions.value
    .filter(p => p.category === category)
    .map(p => p.id)
  
  const selectedCount = categoryPermissions.filter(id => 
    selectedPermissions.value.includes(id)
  ).length
  
  return selectedCount > 0 && selectedCount < categoryPermissions.length
}

const selectAll = () => {
  selectedPermissions.value = filteredPermissions.value.map(p => p.id)
}

const clearAll = () => {
  const filteredIds = new Set(filteredPermissions.value.map(p => p.id))
  selectedPermissions.value = selectedPermissions.value.filter(id => !filteredIds.has(id))
}

const selectByCategory = (category) => {
  const categoryPermissions = allPermissions.value
    .filter(p => p.category === category)
    .map(p => p.id)

  categoryPermissions.forEach(id => {
    if (!selectedPermissions.value.includes(id)) {
      selectedPermissions.value.push(id)
    }
  })
}

const savePermissions = async () => {
  if (!props.role?.id) return

  saving.value = true
  try {
    await rolesApi.updateRolePermissions(props.role.id, selectedPermissions.value)
    originalPermissions.value = [...selectedPermissions.value]
    ElMessage.success('权限配置保存成功')
    emit('updated')
    handleClose()
  } catch (error) {
    console.error('保存权限配置失败:', error)
    ElMessage.error('保存权限配置失败')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}

// 工具方法
const getIconComponent = (iconName) => {
  const iconMap = {
    Factory: 'OfficeBuilding',
    TruckDelivery: 'Truck',
    Airplane: 'WindPower',
    UserGroup: 'UserFilled',
    Wrench: 'Tool',
    Truck: 'Truck',
    Settings: 'Setting',
    User: 'User'
  }
  return iconMap[iconName] || 'User'
}

const getBusinessTypeText = (businessType) => {
  const textMap = {
    sell_only: '只销售',
    buy_only: '只采购',
    buy_and_sell: '买卖双向',
    service: '服务支持'
  }
  return textMap[businessType] || businessType
}

const getPermissionRiskType = (permission) => {
  // 根据权限代码判断风险等级
  const highRiskKeywords = ['delete', 'admin', 'system', 'manage_users']
  const mediumRiskKeywords = ['create', 'update', 'manage']
  
  const code = permission.permission_code.toLowerCase()
  
  if (highRiskKeywords.some(keyword => code.includes(keyword))) {
    return 'danger'
  } else if (mediumRiskKeywords.some(keyword => code.includes(keyword))) {
    return 'warning'
  } else {
    return 'success'
  }
}

const getPermissionRiskText = (permission) => {
  const type = getPermissionRiskType(permission)
  const textMap = {
    danger: '高风险',
    warning: '中风险',
    success: '低风险'
  }
  return textMap[type] || '未知'
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.role) {
    loadPermissions()
  }
})

// 生命周期
onMounted(() => {
  if (props.modelValue && props.role) {
    loadPermissions()
  }
})
</script>

<style scoped>
.permission-management {
  /* 权限管理容器样式 */
}

/* 自定义滚动条 */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 权限项悬停效果 */
.permission-item:hover {
  background-color: #f9fafb;
}

/* 分类标题粘性定位 */
.sticky {
  z-index: 10;
  backdrop-filter: blur(10px);
}
</style>