<template>
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Logo区域 -->
        <div class="flex items-center space-x-3">
          <router-link to="/" class="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
              </svg>
            </div>
            <div>
              <h1 class="title-font text-xl text-gray-800">航材共享保障平台</h1>
              <p class="text-xs text-gray-500">Aviation Material Sharing Platform</p>
            </div>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <nav class="hidden lg:flex items-center space-x-8">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/shared-materials" class="nav-link">共享件市场</router-link>
          <a href="/#features" class="nav-link">平台功能</a>
          <a href="/#hot-materials" class="nav-link">热门航材</a>
          <a href="/#industry-news" class="nav-link">行业资讯</a>
          <a href="/#success-cases" class="nav-link">成功案例</a>
        </nav>

        <!-- 用户操作区 -->
        <div class="flex items-center space-x-4">
          <template v-if="isAuthenticated">
            <!-- 已登录状态 -->
            <router-link to="/app/workspace" class="text-gray-700 hover:text-blue-600 font-medium">
              进入工作台
            </router-link>
            <el-dropdown>
              <div class="flex items-center space-x-2 text-gray-700 hover:text-blue-600 cursor-pointer">
                <el-avatar :size="32">
                  {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}
                </el-avatar>
                <span class="text-sm font-medium">{{ userStore.user?.username }}</span>
                <ArrowDown class="w-4 h-4" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="$router.push('/app/workspace')">
                    <House class="w-4 h-4 mr-2" />
                    工作台
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <SwitchButton class="w-4 h-4 mr-2" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <!-- 未登录状态 -->
            <router-link to="/login" class="text-gray-700 hover:text-blue-600 font-medium">
              登录
            </router-link>
            <router-link 
              to="/login" 
              class="btn-primary text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-6 py-2 rounded-lg font-medium transition-all duration-200"
            >
              免费试用
            </router-link>
          </template>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 (仅在非首页显示) -->
    <div v-if="showBreadcrumb" class="bg-gray-50 border-t border-gray-100">
      <div class="max-w-7xl mx-auto px-6 py-3">
        <nav aria-label="Breadcrumb">
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <router-link to="/" class="text-gray-500 hover:text-blue-600 transition-colors flex items-center">
                <House class="w-4 h-4 mr-1" />
                首页
              </router-link>
            </li>
            <template v-for="(item, index) in breadcrumbItems" :key="index">
              <li class="flex items-center">
                <Right class="w-4 h-4 text-gray-400" />
              </li>
              <li>
                <router-link
                  v-if="item.to && index < breadcrumbItems.length - 1"
                  :to="item.to"
                  class="text-gray-500 hover:text-blue-600 transition-colors"
                >
                  {{ item.name }}
                </router-link>
                <span
                  v-else
                  class="text-gray-900 font-medium"
                >
                  {{ item.name }}
                </span>
              </li>
            </template>
          </ol>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入

const route = useRoute()
const router = useRouter()
const userStore = useAuthStore()

// 计算属性
const isAuthenticated = computed(() => userStore.isAuthenticated)

// 是否显示面包屑导航
const showBreadcrumb = computed(() => {
  return route.path !== '/'
})

// 面包屑路径映射
const routeMapping = {
  'shared-materials': { name: '共享件市场', to: '/shared-materials' }
}

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  const path = route.path
  const pathSegments = path.split('/').filter(Boolean)
  const items = []

  if (pathSegments[0] === 'shared-materials') {
    items.push(routeMapping['shared-materials'])
    
    if (pathSegments.length > 1 && route.params.id) {
      items.push({ name: `航材详情 #${route.params.id}` })
    }
  }

  return items
})

// 退出登录
const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}
</script>

<style scoped>
.nav-link {
  @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200;
}

.nav-link.router-link-active {
  @apply text-blue-600;
}

.btn-primary {
  @apply inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-all duration-200;
}
</style>