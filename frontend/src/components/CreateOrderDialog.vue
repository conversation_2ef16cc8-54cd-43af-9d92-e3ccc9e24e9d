<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建订单"
    width="800px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span class="font-semibold">基本信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select
                v-model="form.supplier_id"
                placeholder="请选择供应商"
                filterable
                style="width: 100%"
                @change="handleSupplierChange"
              >
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.company_name || supplier.username"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级">
                <el-option label="AOG紧急" value="aog" />
                <el-option label="高优先级" value="high" />
                <el-option label="普通" value="normal" />
                <el-option label="低优先级" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交货日期" prop="delivery_date">
              <el-date-picker
                v-model="form.delivery_date"
                type="datetime"
                placeholder="请选择交货日期"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="货币类型" prop="currency">
              <el-select v-model="form.currency" placeholder="请选择货币">
                <el-option label="人民币 (CNY)" value="CNY" />
                <el-option label="美元 (USD)" value="USD" />
                <el-option label="欧元 (EUR)" value="EUR" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="交货地址" prop="delivery_address">
          <el-input
            v-model="form.delivery_address"
            type="textarea"
            :rows="2"
            placeholder="请输入详细的交货地址"
          />
        </el-form-item>
      </el-card>

      <!-- 订单项目 -->
      <el-card class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="font-semibold">订单项目</span>
            <el-button type="primary" size="small" @click="addOrderItem">
              <Plus class="w-4 h-4 mr-1" />
              添加项目
            </el-button>
          </div>
        </template>

        <div v-if="form.items.length === 0" class="text-center text-gray-500 py-8">
          <Box class="w-12 h-12 mx-auto mb-2 text-gray-400" />
          <p>暂无订单项目，请点击"添加项目"按钮添加</p>
        </div>

        <div v-else>
          <div
            v-for="(item, index) in form.items"
            :key="index"
            class="border rounded-lg p-4 mb-4 bg-gray-50"
          >
            <div class="flex justify-between items-start mb-3">
              <span class="font-medium text-gray-700">项目 {{ index + 1 }}</span>
              <el-button
                type="danger"
                size="small"
                text
                @click="removeOrderItem(index)"
              >
                <Delete class="w-4 h-4" />
              </el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  :prop="`items.${index}.material_id`"
                  :rules="[{ required: true, message: '请选择航材', trigger: 'change' }]"
                  label="航材"
                >
                  <el-select
                    v-model="item.material_id"
                    placeholder="请选择航材"
                    filterable
                    style="width: 100%"
                    @change="handleMaterialChange(index)"
                  >
                    <el-option
                      v-for="material in availableMaterials"
                      :key="material.id"
                      :label="`${material.part_number} - ${material.part_name}`"
                      :value="material.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="4">
                <el-form-item
                  :prop="`items.${index}.quantity`"
                  :rules="[{ required: true, message: '请输入数量', trigger: 'blur' }]"
                  label="数量"
                >
                  <el-input-number
                    v-model="item.quantity"
                    :min="1"
                    :max="9999"
                    style="width: 100%"
                    @change="calculateSubtotal(index)"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item
                  :prop="`items.${index}.unit_price`"
                  :rules="[{ required: true, message: '请输入单价', trigger: 'blur' }]"
                  label="单价"
                >
                  <el-input-number
                    v-model="item.unit_price"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    @change="calculateSubtotal(index)"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="小计">
                  <el-input
                    :value="formatCurrency(item.subtotal || 0)"
                    readonly
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="状况代码">
                  <el-select v-model="item.condition_code" placeholder="请选择状况代码">
                    <el-option label="NE - 新件" value="NE" />
                    <el-option label="NS - 新剩余" value="NS" />
                    <el-option label="OH - 大修件" value="OH" />
                    <el-option label="SV - 可用件" value="SV" />
                    <el-option label="AR - 待修件" value="AR" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="16">
                <el-form-item label="交货要求">
                  <el-input
                    v-model="item.delivery_requirement"
                    placeholder="请输入特殊交货要求（可选）"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 订单总计 -->
          <div class="border-t pt-4 mt-4">
            <div class="flex justify-end">
              <div class="text-right">
                <div class="text-lg font-semibold text-gray-700">
                  订单总计: {{ formatCurrency(totalAmount) }}
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  共 {{ form.items.length }} 个项目
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 备注信息 -->
      <el-card>
        <template #header>
          <span class="font-semibold">备注信息</span>
        </template>

        <el-form-item label="订单备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入订单备注信息（可选）"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          <span v-if="form.priority === 'aog'" class="text-red-500">
            <Warning class="w-4 h-4 inline mr-1" />
            AOG订单需要审批
          </span>
          <span v-else-if="totalAmount > 100000" class="text-orange-500">
            <Warning class="w-4 h-4 inline mr-1" />
            高金额订单需要审批
          </span>
        </div>

        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleCreate"
            :loading="loading"
            :disabled="form.items.length === 0"
          >
            创建订单
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标已全局注册，无需单独导入
import { ordersApi } from '@/api/orders'
import { materialsApi } from '@/api/materials'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'created'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 响应式数据
const loading = ref(false)
const formRef = ref(null)
const suppliers = ref([])
const availableMaterials = ref([])

// 表单数据
const form = reactive({
  supplier_id: null,
  priority: 'normal',
  delivery_date: null,
  delivery_address: '',
  currency: 'CNY',
  notes: '',
  items: []
})

// 表单验证规则
const rules = {
  supplier_id: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  delivery_date: [
    { required: true, message: '请选择交货日期', trigger: 'change' }
  ],
  delivery_address: [
    { required: true, message: '请输入交货地址', trigger: 'blur' }
  ]
}

// 计算属性
const totalAmount = computed(() => {
  return form.items.reduce((total, item) => {
    return total + (item.subtotal || 0)
  }, 0)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetForm()
    loadInitialData()
  }
})

// 方法
const resetForm = () => {
  form.supplier_id = null
  form.priority = 'normal'
  form.delivery_date = null
  form.delivery_address = ''
  form.currency = 'CNY'
  form.notes = ''
  form.items = []
}

const loadInitialData = async () => {
  try {
    loading.value = true

    // 加载供应商列表（模拟数据）
    suppliers.value = [
      { id: 2, username: 'supplier_user', company_name: '中航材集团' },
      { id: 3, username: 'maintenance_user', company_name: '海航技术' }
    ]

    // 加载航材列表
    const materialsResponse = await materialsApi.search({ page: 1, size: 100 })
    if (materialsResponse.success) {
      availableMaterials.value = materialsResponse.body.items || []
    }

  } catch (error) {
    console.error('加载初始数据失败:', error)
    ElMessage.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

const disabledDate = (time) => {
  // 禁用过去的日期
  return time.getTime() < Date.now() - 8.64e7
}

const addOrderItem = () => {
  form.items.push({
    material_id: null,
    quantity: 1,
    unit_price: 0,
    subtotal: 0,
    condition_code: 'NE',
    delivery_requirement: ''
  })
}

const removeOrderItem = (index) => {
  form.items.splice(index, 1)
}

const handleSupplierChange = () => {
  // 供应商变更时可以加载该供应商的航材库存
  console.log('供应商变更:', form.supplier_id)
}

const handleMaterialChange = (index) => {
  const item = form.items[index]
  const material = availableMaterials.value.find(m => m.id === item.material_id)

  if (material && material.unit_price) {
    item.unit_price = material.unit_price
    calculateSubtotal(index)
  }
}

const calculateSubtotal = (index) => {
  const item = form.items[index]
  if (item.quantity && item.unit_price) {
    item.subtotal = item.quantity * item.unit_price
  } else {
    item.subtotal = 0
  }
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: form.currency || 'CNY'
  }).format(amount)
}

const handleCreate = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    if (form.items.length === 0) {
      ElMessage.error('请至少添加一个订单项目')
      return
    }

    // 验证所有订单项目
    for (let i = 0; i < form.items.length; i++) {
      const item = form.items[i]
      if (!item.material_id || !item.quantity || !item.unit_price) {
        ElMessage.error(`请完善第 ${i + 1} 个订单项目的信息`)
        return
      }
    }

    loading.value = true

    // 准备提交数据
    const submitData = {
      supplier_id: form.supplier_id,
      priority: form.priority,
      delivery_address: form.delivery_address,
      delivery_date: form.delivery_date ? form.delivery_date.toISOString() : null,
      currency: form.currency,
      notes: form.notes,
      items: form.items.map(item => ({
        material_id: item.material_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        condition_code: item.condition_code,
        delivery_requirement: item.delivery_requirement
      }))
    }

    // 调用API创建订单
    const response = await ordersApi.createOrder(submitData)

    if (response.success) {
      ElMessage.success('订单创建成功')
      emit('created', response.body)
      dialogVisible.value = false
    } else {
      ElMessage.error(response.message || '订单创建失败')
    }

  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error('创建订单失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>