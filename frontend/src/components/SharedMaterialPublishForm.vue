<template>
  <div class="shared-material-publish-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="航材选择" prop="material_id">
        <el-select
          v-model="form.material_id"
          placeholder="请选择要发布的航材"
          filterable
          remote
          :remote-method="searchMaterials"
          :loading="materialsLoading"
          style="width: 100%"
          @change="handleMaterialChange"
        >
          <el-option
            v-for="material in materialOptions"
            :key="material.id"
            :label="`${material.name} (${material.part_number})`"
            :value="material.id"
          />
        </el-select>
        <div v-if="selectedMaterial" class="material-preview">
          <img :src="selectedMaterial.image_url" :alt="selectedMaterial.name" />
          <div class="material-info">
            <h4>{{ selectedMaterial.name }}</h4>
            <p>零件号：{{ selectedMaterial.part_number }}</p>
            <p>制造商：{{ selectedMaterial.manufacturer }}</p>
            <p>适用机型：{{ selectedMaterial.aircraft_type }}</p>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="共享类型" prop="share_type">
        <el-radio-group v-model="form.share_type">
          <el-radio value="sale">出售</el-radio>
          <el-radio value="lease">租赁</el-radio>
          <el-radio value="exchange">交换</el-radio>
          <el-radio value="loan">借用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="共享数量" prop="share_quantity">
            <el-input-number
              v-model="form.share_quantity"
              :min="1"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预留数量" prop="reserved_quantity">
            <el-input-number
              v-model="form.reserved_quantity"
              :min="0"
              :max="form.share_quantity || 0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="单价" prop="price">
            <el-input
              v-model.number="form.price"
              type="number"
              placeholder="请输入单价"
            >
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最小订购量" prop="min_order_quantity">
            <el-input-number
              v-model="form.min_order_quantity"
              :min="1"
              :max="form.share_quantity || 1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="共享策略" prop="sharing_policy">
        <el-radio-group v-model="form.sharing_policy">
          <el-radio value="immediate">立即生效</el-radio>
          <el-radio value="approval">需要审批</el-radio>
          <el-radio value="inquiry">仅接受询价</el-radio>
        </el-radio-group>
        <div class="policy-help">
          <el-text size="small" type="info">
            <span v-if="form.sharing_policy === 'immediate'">
              发布后立即对外展示，买家可直接下单
            </span>
            <span v-else-if="form.sharing_policy === 'approval'">
              买家下单后需要您确认才能成交
            </span>
            <span v-else-if="form.sharing_policy === 'inquiry'">
              买家只能询价，不能直接下单
            </span>
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="过期日期">
        <el-date-picker
          v-model="form.expiry_date"
          type="date"
          placeholder="选择过期日期"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="存储位置">
        <el-input
          v-model="form.location"
          placeholder="请输入航材存储位置"
        />
      </el-form-item>

      <el-form-item label="状况代码" prop="condition_code">
        <el-select v-model="form.condition_code" placeholder="请选择状况代码">
          <el-option label="NE - 新件" value="NE" />
          <el-option label="NS - 新的剩余" value="NS" />
          <el-option label="OH - 大修件" value="OH" />
          <el-option label="SV - 可维修件" value="SV" />
          <el-option label="AR - 按要求修理" value="AR" />
        </el-select>
      </el-form-item>

      <el-form-item label="描述信息">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请描述航材的详细信息、状态、质量等"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitForm">
          发布共享件
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { publishSharedMaterial } from '@/api/sharedMaterials'
import { getMaterials } from '@/api/materials'

// 定义事件
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)
const materialsLoading = ref(false)
const materialOptions = ref([])
const selectedMaterial = ref(null)

// 表单数据
const form = reactive({
  material_id: null,
  share_type: 'sale',
  share_quantity: 1,
  reserved_quantity: 0,
  price: null,
  min_order_quantity: 1,
  sharing_policy: 'immediate',
  expiry_date: null,
  location: '',
  condition_code: 'NE',
  description: ''
})

// 表单验证规则
const rules = {
  material_id: [
    { required: true, message: '请选择要发布的航材', trigger: 'change' }
  ],
  share_type: [
    { required: true, message: '请选择共享类型', trigger: 'change' }
  ],
  share_quantity: [
    { required: true, message: '请输入共享数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '共享数量至少为1', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能为负数', trigger: 'blur' }
  ],
  min_order_quantity: [
    { required: true, message: '请输入最小订购量', trigger: 'blur' },
    { type: 'number', min: 1, message: '最小订购量至少为1', trigger: 'blur' }
  ],
  condition_code: [
    { required: true, message: '请选择状况代码', trigger: 'change' }
  ]
}

// 监听器
watch(() => form.share_quantity, (newValue) => {
  if (form.reserved_quantity > newValue) {
    form.reserved_quantity = 0
  }
  if (form.min_order_quantity > newValue) {
    form.min_order_quantity = Math.min(1, newValue)
  }
})

// 方法定义
const searchMaterials = async (query) => {
  if (!query) {
    materialOptions.value = []
    return
  }

  try {
    materialsLoading.value = true
    // 这里应该调用真实的航材搜索API
    // 暂时使用模拟数据
    materialOptions.value = [
      {
        id: 1,
        name: 'CFM56发动机燃油喷嘴',
        part_number: 'CFM56-7B-001',
        manufacturer: 'CFM International',
        aircraft_type: 'A320',
        image_url: '/images/发动机燃油喷嘴.jpg'
      },
      {
        id: 2,
        name: 'B737主起落架机轮',
        part_number: 'WHL-B737-001',
        manufacturer: '霍尼韦尔',
        aircraft_type: 'B737',
        image_url: '/images/机轮.jpg'
      },
      {
        id: 3,
        name: 'A320航电设备模块',
        part_number: 'AV-A320-003',
        manufacturer: 'Airbus',
        aircraft_type: 'A320',
        image_url: '/images/航电1.jpg'
      }
    ].filter(item => 
      item.name.toLowerCase().includes(query.toLowerCase()) ||
      item.part_number.toLowerCase().includes(query.toLowerCase())
    )
  } catch (error) {
    console.error('搜索航材失败:', error)
    ElMessage.error('搜索航材失败')
  } finally {
    materialsLoading.value = false
  }
}

const handleMaterialChange = (materialId) => {
  selectedMaterial.value = materialOptions.value.find(m => m.id === materialId)
}

const disabledDate = (date) => {
  return date < new Date()
}

const submitForm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    const submitData = {
      ...form,
      expiry_date: form.expiry_date ? form.expiry_date.toISOString().split('T')[0] : null
    }

    const response = await publishSharedMaterial(submitData)
    
    if (response.success) {
      ElMessage.success('共享件发布成功，等待审核')
      emit('success')
    } else {
      ElMessage.error(response.message || '发布共享件失败')
    }
  } catch (error) {
    console.error('发布共享件失败:', error)
    ElMessage.error('发布共享件失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.shared-material-publish-form {
  max-height: 70vh;
  overflow-y: auto;
}

.material-preview {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.material-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.material-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.material-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.policy-help {
  margin-top: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

@media (max-width: 768px) {
  .material-preview {
    flex-direction: column;
    text-align: center;
  }
  
  .material-preview img {
    align-self: center;
  }
}
</style>