<template>
  <el-dialog
    v-model="dialogVisible"
    title="维修资源调度"
    width="1200px"
    :before-close="handleClose"
    class="resource-scheduling-dialog"
  >
    <div class="resource-scheduling-container">
      <!-- 资源概览 -->
      <div class="resource-overview">
        <h3>资源概览</h3>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon hangar">
              <el-icon><House /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">机库</div>
              <div class="card-stats">
                <span class="available">{{ resourceStats.hangars.available }}</span>
                <span class="divider">/</span>
                <span class="total">{{ resourceStats.hangars.total }}</span>
              </div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon technician">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">技师</div>
              <div class="card-stats">
                <span class="available">{{ resourceStats.technicians.available }}</span>
                <span class="divider">/</span>
                <span class="total">{{ resourceStats.technicians.total }}</span>
              </div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon equipment">
              <el-icon><Tools /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">设备</div>
              <div class="card-stats">
                <span class="available">{{ resourceStats.equipment.available }}</span>
                <span class="divider">/</span>
                <span class="total">{{ resourceStats.equipment.total }}</span>
              </div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon aircraft">
              <el-icon><Van /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">飞机</div>
              <div class="card-stats">
                <span class="available">{{ resourceStats.aircraft.in_maintenance }}</span>
                <span class="divider">/</span>
                <span class="total">{{ resourceStats.aircraft.total }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 调度视图切换 -->
      <div class="schedule-view">
        <el-tabs v-model="activeView" class="schedule-tabs">
          <el-tab-pane label="时间线视图" name="timeline">
            <div class="timeline-view">
              <div class="timeline-header">
                <div class="time-range">
                  <el-date-picker
                    v-model="timeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    @change="handleTimeRangeChange"
                  />
                </div>
                <div class="view-controls">
                  <el-button-group>
                    <el-button :type="timeUnit === 'day' ? 'primary' : ''" @click="setTimeUnit('day')">
                      日视图
                    </el-button>
                    <el-button :type="timeUnit === 'week' ? 'primary' : ''" @click="setTimeUnit('week')">
                      周视图
                    </el-button>
                    <el-button :type="timeUnit === 'month' ? 'primary' : ''" @click="setTimeUnit('month')">
                      月视图
                    </el-button>
                  </el-button-group>
                </div>
              </div>
              
              <div class="timeline-content">
                <div class="resource-gantt">
                  <div class="gantt-header">
                    <div class="resource-column">资源</div>
                    <div class="timeline-column">
                      <div class="time-grid">
                        <div
                          v-for="day in timelineData.days"
                          :key="day.date"
                          class="time-cell"
                          :class="{ today: day.isToday }"
                        >
                          {{ day.label }}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="gantt-body">
                    <div
                      v-for="resource in resourceList"
                      :key="resource.id"
                      class="resource-row"
                    >
                      <div class="resource-info">
                        <div class="resource-name">{{ resource.name }}</div>
                        <div class="resource-type">{{ resource.type }}</div>
                      </div>
                      <div class="resource-timeline">
                        <div class="timeline-grid">
                          <div
                            v-for="slot in resource.timeSlots"
                            :key="slot.date"
                            class="time-slot"
                            :class="{ occupied: slot.occupied, conflict: slot.conflict }"
                            @click="handleSlotClick(resource, slot)"
                          >
                            <div v-if="slot.occupied" class="slot-content">
                              <div class="task-info">
                                <div class="task-title">{{ slot.task?.title }}</div>
                                <div class="task-aircraft">{{ slot.task?.aircraft }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="资源列表" name="list">
            <div class="resource-list-view">
              <div class="resource-filters">
                <el-input
                  v-model="filterForm.keyword"
                  placeholder="搜索资源"
                  clearable
                  class="filter-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-select v-model="filterForm.type" placeholder="资源类型" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="机库" value="hangar" />
                  <el-option label="技师" value="technician" />
                  <el-option label="设备" value="equipment" />
                </el-select>
                <el-select v-model="filterForm.status" placeholder="状态" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="空闲" value="available" />
                  <el-option label="占用" value="occupied" />
                  <el-option label="维护中" value="maintenance" />
                </el-select>
              </div>

              <div class="resource-table">
                <el-table :data="filteredResources" stripe>
                  <el-table-column prop="name" label="资源名称" width="150" />
                  <el-table-column prop="type" label="类型" width="100">
                    <template #default="scope">
                      <el-tag :type="getResourceTypeTag(scope.row.type)" size="small">
                        {{ getResourceTypeLabel(scope.row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="getResourceStatusTag(scope.row.status)" size="small">
                        {{ getResourceStatusLabel(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="current_task" label="当前任务" min-width="200">
                    <template #default="scope">
                      <div v-if="scope.row.current_task">
                        <div class="task-title">{{ scope.row.current_task.title }}</div>
                        <div class="task-aircraft">{{ scope.row.current_task.aircraft }}</div>
                      </div>
                      <span v-else class="no-task">空闲</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="utilization" label="利用率" width="120">
                    <template #default="scope">
                      <el-progress
                        :percentage="scope.row.utilization"
                        :color="getUtilizationColor(scope.row.utilization)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="next_available" label="下次可用" width="150">
                    <template #default="scope">
                      {{ formatDateTime(scope.row.next_available) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        @click="scheduleResource(scope.row)"
                        size="small"
                      >
                        调度
                      </el-button>
                      <el-button
                        link
                        type="info"
                        @click="viewResourceHistory(scope.row)"
                        size="small"
                      >
                        历史
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="冲突管理" name="conflicts">
            <div class="conflict-management">
              <div class="conflict-header">
                <h4>资源冲突</h4>
                <el-button type="primary" @click="resolveAllConflicts">
                  <el-icon><Check /></el-icon>
                  批量解决
                </el-button>
              </div>

              <div class="conflict-list">
                <div
                  v-for="conflict in conflicts"
                  :key="conflict.id"
                  class="conflict-item"
                >
                  <div class="conflict-info">
                    <div class="conflict-title">
                      <el-icon><Warning /></el-icon>
                      {{ conflict.title }}
                    </div>
                    <div class="conflict-details">
                      <span class="resource">资源：{{ conflict.resource }}</span>
                      <span class="time">时间：{{ formatDateTime(conflict.time) }}</span>
                      <span class="tasks">冲突任务：{{ conflict.tasks.join(', ') }}</span>
                    </div>
                  </div>
                  <div class="conflict-actions">
                    <el-button type="primary" size="small" @click="resolveConflict(conflict)">
                      解决
                    </el-button>
                    <el-button type="warning" size="small" @click="rescheduleConflict(conflict)">
                      重新调度
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportSchedule">
          <el-icon><Download /></el-icon>
          导出调度
        </el-button>
        <el-button type="success" @click="saveSchedule">
          <el-icon><Check /></el-icon>
          保存调度
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  House, User, Tools, Van, Search, Warning, Check, Download
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeView = ref('timeline')
const timeUnit = ref('day')
const timeRange = ref([])

// 资源统计
const resourceStats = reactive({
  hangars: { available: 2, total: 3 },
  technicians: { available: 15, total: 24 },
  equipment: { available: 8, total: 12 },
  aircraft: { in_maintenance: 6, total: 18 }
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  type: '',
  status: ''
})

// 时间线数据
const timelineData = reactive({
  days: []
})

// 资源列表
const resourceList = ref([])

// 冲突列表
const conflicts = ref([
  {
    id: 1,
    title: '机库资源冲突',
    resource: '1号机库',
    time: '2024-07-18T10:00:00Z',
    tasks: ['A320维修', 'B737检查'],
    severity: 'high'
  },
  {
    id: 2,
    title: '技师资源冲突',
    resource: '张师傅',
    time: '2024-07-18T14:00:00Z',
    tasks: ['发动机检查', '起落架维修'],
    severity: 'medium'
  }
])

// 计算属性
const filteredResources = computed(() => {
  return resourceList.value.filter(resource => {
    const matchKeyword = !filterForm.keyword || 
      resource.name.toLowerCase().includes(filterForm.keyword.toLowerCase())
    const matchType = !filterForm.type || resource.type === filterForm.type
    const matchStatus = !filterForm.status || resource.status === filterForm.status
    
    return matchKeyword && matchType && matchStatus
  })
})

// 生命周期钩子
onMounted(() => {
  initTimeRange()
  loadResourceData()
  generateTimelineData()
})

// 初始化时间范围
const initTimeRange = () => {
  const today = new Date()
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
  timeRange.value = [
    today.toISOString().split('T')[0],
    nextWeek.toISOString().split('T')[0]
  ]
}

// 加载资源数据
const loadResourceData = () => {
  resourceList.value = [
    {
      id: 1,
      name: '1号机库',
      type: 'hangar',
      status: 'occupied',
      current_task: {
        title: 'A320定检',
        aircraft: 'B-1234'
      },
      utilization: 85,
      next_available: '2024-07-19T08:00:00Z',
      timeSlots: []
    },
    {
      id: 2,
      name: '张师傅',
      type: 'technician',
      status: 'available',
      current_task: null,
      utilization: 65,
      next_available: new Date().toISOString(),
      timeSlots: []
    },
    {
      id: 3,
      name: '液压测试台',
      type: 'equipment',
      status: 'maintenance',
      current_task: null,
      utilization: 0,
      next_available: '2024-07-20T10:00:00Z',
      timeSlots: []
    }
  ]
}

// 生成时间线数据
const generateTimelineData = () => {
  if (!timeRange.value || timeRange.value.length !== 2) return
  
  const startDate = new Date(timeRange.value[0])
  const endDate = new Date(timeRange.value[1])
  const days = []
  
  for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
    const today = new Date()
    days.push({
      date: date.toISOString().split('T')[0],
      label: `${date.getMonth() + 1}/${date.getDate()}`,
      isToday: date.toDateString() === today.toDateString()
    })
  }
  
  timelineData.days = days
  
  // 为每个资源生成时间槽
  resourceList.value.forEach(resource => {
    resource.timeSlots = days.map(day => ({
      date: day.date,
      occupied: Math.random() > 0.6,
      conflict: Math.random() > 0.9,
      task: Math.random() > 0.7 ? {
        title: 'A320维修',
        aircraft: 'B-1234'
      } : null
    }))
  })
}

// 事件处理
const handleTimeRangeChange = () => {
  generateTimelineData()
}

const setTimeUnit = (unit) => {
  timeUnit.value = unit
  generateTimelineData()
}

const handleSlotClick = (resource, slot) => {
  ElMessage.info(`点击了${resource.name}的${slot.date}时间槽`)
}

const scheduleResource = (resource) => {
  ElMessage.info(`调度${resource.name}`)
}

const viewResourceHistory = (resource) => {
  ElMessage.info(`查看${resource.name}的历史记录`)
}

const resolveConflict = (conflict) => {
  ElMessage.success(`解决冲突：${conflict.title}`)
  const index = conflicts.value.findIndex(c => c.id === conflict.id)
  if (index > -1) {
    conflicts.value.splice(index, 1)
  }
}

const rescheduleConflict = (conflict) => {
  ElMessage.info(`重新调度：${conflict.title}`)
}

const resolveAllConflicts = () => {
  ElMessage.success('批量解决所有冲突')
  conflicts.value = []
}

const exportSchedule = () => {
  ElMessage.info('导出调度表')
}

const saveSchedule = () => {
  ElMessage.success('保存调度成功')
}

const handleClose = () => {
  dialogVisible.value = false
}

// 工具函数
const getResourceTypeTag = (type) => {
  const tags = {
    hangar: 'primary',
    technician: 'success',
    equipment: 'warning'
  }
  return tags[type] || 'info'
}

const getResourceTypeLabel = (type) => {
  const labels = {
    hangar: '机库',
    technician: '技师',
    equipment: '设备'
  }
  return labels[type] || type
}

const getResourceStatusTag = (status) => {
  const tags = {
    available: 'success',
    occupied: 'warning',
    maintenance: 'danger'
  }
  return tags[status] || 'info'
}

const getResourceStatusLabel = (status) => {
  const labels = {
    available: '空闲',
    occupied: '占用',
    maintenance: '维护中'
  }
  return labels[status] || status
}

const getUtilizationColor = (utilization) => {
  if (utilization < 50) return '#67c23a'
  if (utilization < 80) return '#e6a23c'
  return '#f56c6c'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.resource-scheduling-dialog {
  --el-dialog-content-font-size: 14px;
}

.resource-scheduling-container {
  max-height: 80vh;
  overflow-y: auto;
}

.resource-overview {
  margin-bottom: 20px;
}

.resource-overview h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.overview-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-icon.hangar { background: #3b82f6; }
.card-icon.technician { background: #10b981; }
.card-icon.equipment { background: #f59e0b; }
.card-icon.aircraft { background: #8b5cf6; }

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.card-stats {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.available {
  color: #10b981;
}

.divider {
  margin: 0 4px;
  color: #666;
}

.total {
  color: #666;
}

.schedule-view {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.schedule-tabs {
  padding: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.resource-gantt {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.gantt-header {
  background: #f3f4f6;
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.resource-column {
  width: 200px;
  padding: 12px;
  font-weight: 500;
  border-right: 1px solid #e5e7eb;
}

.timeline-column {
  flex: 1;
}

.time-grid {
  display: flex;
}

.time-cell {
  flex: 1;
  padding: 8px;
  border-right: 1px solid #e5e7eb;
  text-align: center;
  font-size: 12px;
}

.time-cell.today {
  background: #eff6ff;
  color: #3b82f6;
  font-weight: bold;
}

.resource-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.resource-info {
  width: 200px;
  padding: 12px;
  border-right: 1px solid #e5e7eb;
}

.resource-name {
  font-weight: 500;
  color: #333;
}

.resource-type {
  font-size: 12px;
  color: #666;
}

.resource-timeline {
  flex: 1;
}

.timeline-grid {
  display: flex;
  height: 60px;
}

.time-slot {
  flex: 1;
  border-right: 1px solid #e5e7eb;
  position: relative;
  cursor: pointer;
}

.time-slot.occupied {
  background: #dbeafe;
}

.time-slot.conflict {
  background: #fee2e2;
}

.slot-content {
  padding: 4px;
  height: 100%;
  display: flex;
  align-items: center;
}

.task-info {
  font-size: 11px;
}

.task-title {
  font-weight: 500;
  color: #333;
}

.task-aircraft {
  color: #666;
}

.resource-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-input {
  flex: 1;
  max-width: 300px;
}

.conflict-management {
  padding: 16px;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.conflict-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
}

.conflict-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #dc2626;
  margin-bottom: 4px;
}

.conflict-details {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 16px;
}

.conflict-actions {
  display: flex;
  gap: 8px;
}

.no-task {
  color: #9ca3af;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.resource-scheduling-container::-webkit-scrollbar {
  width: 6px;
}

.resource-scheduling-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.resource-scheduling-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.resource-scheduling-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .resource-filters {
    flex-direction: column;
  }
  
  .conflict-item {
    flex-direction: column;
    gap: 12px;
  }
}
</style>