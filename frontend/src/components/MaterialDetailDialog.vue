<template>
  <el-dialog
    v-model="dialogVisible"
    title="航材详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <div v-if="material">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 左侧图片 -->
          <div>
            <img 
              v-if="material.image" 
              :src="material.image" 
              :alt="material.name"
              class="w-full h-64 object-cover rounded-lg"
            />
          </div>
          
          <!-- 右侧信息 -->
          <div>
            <h3 class="text-2xl font-bold text-gray-800 mb-4">{{ material.name }}</h3>
            <div class="space-y-3">
              <div>
                <span class="text-gray-600">零件号:</span>
                <span class="ml-2 font-medium">{{ material.part_number }}</span>
              </div>
              <div>
                <span class="text-gray-600">价格:</span>
                <span class="ml-2 font-bold text-primary-600 text-xl">¥{{ material.price }}</span>
              </div>
              <div>
                <span class="text-gray-600">机型:</span>
                <span class="ml-2 font-medium">{{ material.aircraft_type }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary">加入采购</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  materialId: [String, Number]
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const material = ref(null)

const handleClose = () => {
  dialogVisible.value = false
}

watch(() => props.materialId, (newId) => {
  if (newId && dialogVisible.value) {
    // 加载航材详情
    material.value = {
      id: newId,
      name: '发动机高压压气机叶片',
      part_number: 'CFM56-7B-001',
      price: 35600,
      aircraft_type: 'B737',
      image: '/images/发动机1.jpg'
    }
  }
})
</script>