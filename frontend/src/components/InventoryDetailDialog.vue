<template>
  <el-dialog v-model="dialogVisible" title="库存详情" width="800px">
    <div>库存详情组件 - ID: {{ inventoryId }}</div>
    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  inventoryId: [String, Number]
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>