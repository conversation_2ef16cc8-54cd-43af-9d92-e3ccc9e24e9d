<!--
饼图组件
版本: 2.0
创建时间: 2025-07-19

基于 Canvas 的饼图组件，用于显示分布数据
-->

<template>
  <div class="pie-chart">
    <canvas 
      ref="chartCanvas" 
      :width="width" 
      :height="height"
    ></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 300
  }
})

// 响应式数据
const chartCanvas = ref(null)
let chartInstance = null

// 默认配置
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'bottom'
    },
    tooltip: {
      enabled: true
    }
  }
}

// 方法
const createChart = async () => {
  if (!chartCanvas.value) return
  
  try {
    const ctx = chartCanvas.value.getContext('2d')
    
    // 销毁现有图表
    if (chartInstance) {
      destroyChart()
    }
    
    // 合并配置
    const chartOptions = {
      ...defaultOptions,
      ...props.options
    }
    
    // 数据验证
    const chartData = props.data || { datasets: [], labels: [] }
    
    // 创建饼图
    chartInstance = createMockPieChart(ctx, chartData, chartOptions)
    
  } catch (error) {
    console.error('创建饼图失败:', error)
  }
}

const createMockPieChart = (ctx, data, options) => {
  const canvas = ctx.canvas
  const width = canvas.width
  const height = canvas.height
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, height)
  
  // 数据验证和默认值
  const chartData = data || {}
  const datasets = chartData.datasets || []
  const labels = chartData.labels || []
  
  // 绘制饼图
  if (datasets.length > 0 && labels.length > 0) {
    drawPieChart(ctx, { ...chartData, datasets, labels }, width, height, options)
  } else {
    // 绘制默认提示
    drawNoDataMessage(ctx, width, height)
  }
  
  return {
    destroy: () => {
      ctx.clearRect(0, 0, width, height)
    },
    update: () => {
      createMockPieChart(ctx, data, options)
    }
  }
}

const drawPieChart = (ctx, data, width, height, options) => {
  const labels = data.labels || []
  const dataset = data.datasets[0] || {}
  const values = dataset.data || []
  const colors = dataset.backgroundColor || []
  
  if (values.length === 0) return
  
  // 计算中心和半径
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(width, height) / 2 - 40
  
  // 计算总值
  const total = values.reduce((sum, value) => sum + value, 0)
  
  // 绘制饼图扇形
  let currentAngle = -Math.PI / 2 // 从顶部开始
  
  values.forEach((value, index) => {
    const sliceAngle = (value / total) * 2 * Math.PI
    const endAngle = currentAngle + sliceAngle
    
    // 绘制扇形
    ctx.fillStyle = colors[index] || `hsl(${(index * 360) / values.length}, 70%, 60%)`
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, endAngle)
    ctx.closePath()
    ctx.fill()
    
    // 绘制边框
    ctx.strokeStyle = '#ffffff'
    ctx.lineWidth = 2
    ctx.stroke()
    
    // 绘制标签（如果值大于5%）
    const percentage = (value / total) * 100
    if (percentage > 5) {
      const labelAngle = currentAngle + sliceAngle / 2
      const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7)
      const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7)
      
      ctx.fillStyle = '#ffffff'
      ctx.font = 'bold 12px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(`${percentage.toFixed(1)}%`, labelX, labelY)
    }
    
    currentAngle = endAngle
  })
  
  // 绘制图例
  if (options.plugins?.legend?.display !== false) {
    drawLegend(ctx, labels, colors, width, height, options.plugins?.legend?.position || 'bottom')
  }
}

const drawNoDataMessage = (ctx, width, height) => {
  ctx.fillStyle = '#999'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('暂无数据', width / 2, height / 2)
}

const drawLegend = (ctx, labels, colors, width, height, position) => {
  if (!labels || labels.length === 0) return
  
  const legendItemHeight = 20
  const legendItemWidth = 100
  const legendPadding = 10
  
  ctx.font = '12px Arial'
  ctx.textAlign = 'left'
  ctx.textBaseline = 'middle'
  
  if (position === 'bottom') {
    const startY = height - (labels.length * legendItemHeight) - legendPadding
    const startX = (width - (Math.ceil(labels.length / 2) * legendItemWidth)) / 2
    
    labels.forEach((label, index) => {
      const row = Math.floor(index / 2)
      const col = index % 2
      const x = startX + col * legendItemWidth
      const y = startY + row * legendItemHeight
      
      // 绘制颜色方块
      ctx.fillStyle = colors[index] || `hsl(${(index * 360) / labels.length}, 70%, 60%)`
      ctx.fillRect(x, y - 6, 12, 12)
      
      // 绘制标签文本
      ctx.fillStyle = '#333'
      ctx.fillText(label, x + 18, y)
    })
  } else if (position === 'right') {
    const startX = width - 120
    const startY = (height - (labels.length * legendItemHeight)) / 2
    
    labels.forEach((label, index) => {
      const y = startY + index * legendItemHeight
      
      // 绘制颜色方块
      ctx.fillStyle = colors[index] || `hsl(${(index * 360) / labels.length}, 70%, 60%)`
      ctx.fillRect(startX, y - 6, 12, 12)
      
      // 绘制标签文本
      ctx.fillStyle = '#333'
      ctx.fillText(label, startX + 18, y)
    })
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

const updateChart = () => {
  nextTick(() => {
    createChart()
  })
}

// 生命周期
onMounted(() => {
  createChart()
})

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.options, updateChart, { deep: true })

// 清理
onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.pie-chart {
  @apply w-full h-full;
}

canvas {
  @apply w-full h-full;
}
</style>