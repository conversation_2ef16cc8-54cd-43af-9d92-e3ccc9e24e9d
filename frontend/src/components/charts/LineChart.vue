<!--
折线图组件
版本: 2.0
创建时间: 2025-07-19

基于 Chart.js 的折线图组件，用于显示趋势数据
-->

<template>
  <div class="line-chart">
    <canvas 
      ref="chartCanvas" 
      :width="width" 
      :height="height"
    ></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 300
  }
})

// 响应式数据
const chartCanvas = ref(null)
let chartInstance = null

// 默认配置
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top'
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#ddd',
      borderWidth: 1
    }
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)'
      }
    },
    y: {
      display: true,
      beginAtZero: true,
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)'
      }
    }
  },
  elements: {
    line: {
      tension: 0.4
    },
    point: {
      radius: 4,
      hoverRadius: 6
    }
  },
  interaction: {
    intersect: false,
    mode: 'index'
  }
}

// 方法
const createChart = async () => {
  if (!chartCanvas.value) return
  
  try {
    // 动态导入 Chart.js
    const { Chart, registerables } = await import('chart.js')
    Chart.register(...registerables)
    
    const ctx = chartCanvas.value.getContext('2d')
    
    // 销毁现有图表
    if (chartInstance) {
      destroyChart()
    }
    
    // 合并配置
    const chartOptions = {
      ...defaultOptions,
      ...props.options
    }
    
    // 数据验证
    const chartData = props.data || { datasets: [], labels: [] }
    
    // 模拟图表创建
    chartInstance = createMockChart(ctx, chartData, chartOptions)
    
  } catch (error) {
    console.error('创建图表失败:', error)
  }
}

const createMockChart = (ctx, data, options) => {
  // 简化版图表绘制
  const canvas = ctx.canvas
  const width = canvas.width
  const height = canvas.height
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, height)
  
  // 数据验证和默认值
  const chartData = data || {}
  const datasets = chartData.datasets || []
  const labels = chartData.labels || []
  
  // 绘制网格
  drawGrid(ctx, width, height)
  
  // 绘制数据
  if (datasets.length > 0 && labels.length > 0) {
    drawLineChart(ctx, { ...chartData, datasets, labels }, width, height)
  } else {
    // 绘制默认提示
    drawNoDataMessage(ctx, width, height)
  }
  
  return {
    destroy: () => {
      ctx.clearRect(0, 0, width, height)
    },
    update: () => {
      createMockChart(ctx, data, options)
    }
  }
}

const drawGrid = (ctx, width, height) => {
  const padding = 40
  const gridColor = 'rgba(0, 0, 0, 0.1)'
  
  ctx.strokeStyle = gridColor
  ctx.lineWidth = 1
  
  // 垂直网格线
  for (let i = 0; i <= 6; i++) {
    const x = padding + (width - padding * 2) * (i / 6)
    ctx.beginPath()
    ctx.moveTo(x, padding)
    ctx.lineTo(x, height - padding)
    ctx.stroke()
  }
  
  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding + (height - padding * 2) * (i / 5)
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(width - padding, y)
    ctx.stroke()
  }
}

const drawLineChart = (ctx, data, width, height) => {
  const padding = 40
  const chartWidth = width - padding * 2
  const chartHeight = height - padding * 2
  
  const labels = data.labels || []
  const datasets = data.datasets || []
  
  if (labels.length === 0 || datasets.length === 0) return
  
  // 计算数据范围
  const allValues = datasets.flatMap(dataset => dataset.data || [])
  const minValue = Math.min(...allValues, 0)
  const maxValue = Math.max(...allValues)
  const valueRange = maxValue - minValue || 1
  
  datasets.forEach((dataset, datasetIndex) => {
    const points = []
    const dataPoints = dataset.data || []
    
    // 计算点位置
    dataPoints.forEach((value, index) => {
      const x = padding + (chartWidth * index) / (labels.length - 1)
      const y = height - padding - ((value - minValue) / valueRange) * chartHeight
      points.push({ x, y, value })
    })
    
    // 绘制线条
    if (points.length > 1) {
      ctx.strokeStyle = dataset.borderColor || '#1890ff'
      ctx.lineWidth = 2
      ctx.beginPath()
      
      points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      })
      
      ctx.stroke()
      
      // 填充区域
      if (dataset.backgroundColor) {
        ctx.fillStyle = dataset.backgroundColor
        ctx.lineTo(points[points.length - 1].x, height - padding)
        ctx.lineTo(points[0].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
    }
    
    // 绘制数据点
    points.forEach(point => {
      ctx.fillStyle = dataset.borderColor || '#1890ff'
      ctx.beginPath()
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
      ctx.fill()
    })
  })
  
  // 绘制标签
  ctx.fillStyle = '#666'
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  
  labels.forEach((label, index) => {
    const x = padding + (chartWidth * index) / (labels.length - 1)
    const y = height - padding + 20
    ctx.fillText(label, x, y)
  })
}

const drawNoDataMessage = (ctx, width, height) => {
  ctx.fillStyle = '#999'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('暂无数据', width / 2, height / 2)
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

const updateChart = () => {
  nextTick(() => {
    createChart()
  })
}

// 生命周期
onMounted(() => {
  createChart()
})

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.options, updateChart, { deep: true })

// 清理
onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.line-chart {
  @apply w-full h-full;
}

canvas {
  @apply w-full h-full;
}
</style>