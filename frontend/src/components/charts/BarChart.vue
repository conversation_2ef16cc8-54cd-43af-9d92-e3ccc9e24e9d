<!--
柱状图组件
版本: 2.0
创建时间: 2025-07-19

基于 Canvas 的柱状图组件，用于显示对比数据
-->

<template>
  <div class="bar-chart">
    <canvas 
      ref="chartCanvas" 
      :width="width" 
      :height="height"
    ></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 300
  }
})

// 响应式数据
const chartCanvas = ref(null)
let chartInstance = null

// 默认配置
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top'
    }
  },
  scales: {
    x: {
      display: true
    },
    y: {
      display: true,
      beginAtZero: true
    }
  }
}

// 方法
const createChart = async () => {
  if (!chartCanvas.value) return
  
  try {
    const ctx = chartCanvas.value.getContext('2d')
    
    // 销毁现有图表
    if (chartInstance) {
      destroyChart()
    }
    
    // 合并配置
    const chartOptions = {
      ...defaultOptions,
      ...props.options
    }
    
    // 数据验证
    const chartData = props.data || { datasets: [], labels: [] }
    
    // 创建柱状图
    chartInstance = createMockBarChart(ctx, chartData, chartOptions)
    
  } catch (error) {
    console.error('创建柱状图失败:', error)
  }
}

const createMockBarChart = (ctx, data, options) => {
  const canvas = ctx.canvas
  const width = canvas.width
  const height = canvas.height
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, height)
  
  // 数据验证和默认值
  const chartData = data || {}
  const datasets = chartData.datasets || []
  const labels = chartData.labels || []
  
  // 绘制网格
  drawGrid(ctx, width, height)
  
  // 绘制柱状图
  if (datasets.length > 0 && labels.length > 0) {
    drawBarChart(ctx, { ...chartData, datasets, labels }, width, height, options)
  } else {
    // 绘制默认提示
    drawNoDataMessage(ctx, width, height)
  }
  
  return {
    destroy: () => {
      ctx.clearRect(0, 0, width, height)
    },
    update: () => {
      createMockBarChart(ctx, data, options)
    }
  }
}

const drawGrid = (ctx, width, height) => {
  const padding = 60
  const gridColor = 'rgba(0, 0, 0, 0.1)'
  
  ctx.strokeStyle = gridColor
  ctx.lineWidth = 1
  
  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding + (height - padding * 2) * (i / 5)
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(width - padding / 2, y)
    ctx.stroke()
  }
}

const drawBarChart = (ctx, data, width, height, options) => {
  const padding = 60
  const chartWidth = width - padding - padding / 2
  const chartHeight = height - padding * 2
  
  const labels = data.labels || []
  const datasets = data.datasets || []
  
  if (labels.length === 0 || datasets.length === 0) return
  
  // 计算数据范围
  const allValues = datasets.flatMap(dataset => dataset.data || [])
  const minValue = Math.min(...allValues, 0)
  const maxValue = Math.max(...allValues)
  const valueRange = maxValue - minValue || 1
  
  // 计算柱子宽度
  const categoryWidth = chartWidth / labels.length
  const barWidth = categoryWidth / (datasets.length + 1)
  const barSpacing = barWidth * 0.1
  
  // 绘制柱子
  datasets.forEach((dataset, datasetIndex) => {
    const dataPoints = dataset.data || []
    const color = dataset.backgroundColor || `hsl(${datasetIndex * 60}, 70%, 60%)`
    
    dataPoints.forEach((value, index) => {
      if (value == null) return
      
      const barHeight = ((value - minValue) / valueRange) * chartHeight
      const x = padding + index * categoryWidth + datasetIndex * (barWidth + barSpacing) + barSpacing
      const y = height - padding - barHeight
      
      // 绘制柱子
      ctx.fillStyle = color
      ctx.fillRect(x, y, barWidth, barHeight)
      
      // 绘制柱子边框
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
      ctx.lineWidth = 1
      ctx.strokeRect(x, y, barWidth, barHeight)
      
      // 绘制数值标签
      if (barHeight > 20) {
        ctx.fillStyle = '#ffffff'
        ctx.font = '11px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(value.toString(), x + barWidth / 2, y + barHeight / 2)
      }
    })
  })
  
  // 绘制X轴标签
  ctx.fillStyle = '#666'
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'top'
  
  labels.forEach((label, index) => {
    const x = padding + index * categoryWidth + categoryWidth / 2
    const y = height - padding + 10
    ctx.fillText(label, x, y)
  })
  
  // 绘制Y轴标签
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'
  
  for (let i = 0; i <= 5; i++) {
    const value = minValue + (valueRange * i) / 5
    const y = height - padding - (chartHeight * i) / 5
    ctx.fillText(Math.round(value).toString(), padding - 10, y)
  }
  
  // 绘制图例
  if (options.plugins?.legend?.display !== false && datasets.length > 1) {
    drawLegend(ctx, datasets, width, height, options.plugins?.legend?.position || 'top')
  }
}

const drawLegend = (ctx, datasets, width, height, position) => {
  const legendItemWidth = 80
  const legendHeight = 30
  
  ctx.font = '12px Arial'
  ctx.textAlign = 'left'
  ctx.textBaseline = 'middle'
  
  if (position === 'top') {
    const startX = (width - (datasets.length * legendItemWidth)) / 2
    const y = 20
    
    datasets.forEach((dataset, index) => {
      const x = startX + index * legendItemWidth
      
      // 绘制颜色方块
      ctx.fillStyle = dataset.backgroundColor || `hsl(${index * 60}, 70%, 60%)`
      ctx.fillRect(x, y - 6, 12, 12)
      
      // 绘制标签文本
      ctx.fillStyle = '#333'
      ctx.fillText(dataset.label || `数据${index + 1}`, x + 18, y)
    })
  }
}

const drawNoDataMessage = (ctx, width, height) => {
  ctx.fillStyle = '#999'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText('暂无数据', width / 2, height / 2)
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

const updateChart = () => {
  nextTick(() => {
    createChart()
  })
}

// 生命周期
onMounted(() => {
  createChart()
})

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.options, updateChart, { deep: true })

// 清理
onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.bar-chart {
  @apply w-full h-full;
}

canvas {
  @apply w-full h-full;
}
</style>