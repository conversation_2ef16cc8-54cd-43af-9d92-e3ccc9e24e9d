<template>
  <el-dialog
    v-model="dialogVisible"
    title="更新维修进度"
    width="800px"
    :before-close="handleClose"
    class="progress-update-dialog"
  >
    <div v-if="workOrder" class="progress-form">
      <!-- 工单基本信息 -->
      <div class="work-order-info">
        <h3>工单信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">工单号：</span>
            <span class="value">{{ workOrder.work_order_number }}</span>
          </div>
          <div class="info-item">
            <span class="label">飞机号：</span>
            <span class="value">{{ workOrder.aircraft_tail }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前状态：</span>
            <el-tag :type="getStatusTagType(workOrder.status)">
              {{ getStatusLabel(workOrder.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">当前进度：</span>
            <el-progress 
              :percentage="workOrder.progress || 0" 
              :color="getProgressColor(workOrder.progress || 0)"
              :stroke-width="6"
            />
          </div>
        </div>
      </div>

      <!-- 进度更新表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="update-form"
      >
        <div class="form-section">
          <h3>进度更新</h3>
          
          <div class="form-row">
            <el-form-item label="状态更新" prop="status" class="form-item">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="新建" value="new" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="待料中" value="waiting_parts" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="进度百分比" prop="progress" class="form-item">
              <el-slider
                v-model="form.progress"
                :min="0"
                :max="100"
                :step="5"
                show-input
                :show-input-controls="false"
                class="progress-slider"
              />
            </el-form-item>
          </div>

          <el-form-item label="工作描述" prop="work_description">
            <el-input
              v-model="form.work_description"
              type="textarea"
              :rows="3"
              placeholder="请描述本次完成的工作内容"
            />
          </el-form-item>

          <el-form-item label="更新说明" prop="notes">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="2"
              placeholder="请输入进度更新的说明"
            />
          </el-form-item>
        </div>

        <!-- 工时记录 -->
        <div class="form-section">
          <h3>工时记录</h3>
          
          <div class="form-row">
            <el-form-item label="开始时间" prop="start_time" class="form-item">
              <el-date-picker
                v-model="form.start_time"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            
            <el-form-item label="结束时间" prop="end_time" class="form-item">
              <el-date-picker
                v-model="form.end_time"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="工时统计" class="form-item">
              <div class="labor-stats">
                <div class="stat-item">
                  <span class="label">本次工时：</span>
                  <span class="value">{{ calculateWorkHours() }}小时</span>
                </div>
                <div class="stat-item">
                  <span class="label">累计工时：</span>
                  <span class="value">{{ workOrder.actual_hours || 0 }}小时</span>
                </div>
                <div class="stat-item">
                  <span class="label">预计工时：</span>
                  <span class="value">{{ workOrder.estimated_hours || 0 }}小时</span>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 使用零件 -->
        <div class="form-section">
          <h3>使用零件</h3>
          
          <div class="parts-section">
            <div class="parts-header">
              <span>零件使用记录</span>
              <el-button type="primary" size="small" @click="addPart">
                <el-icon><Plus /></el-icon>
                添加零件
              </el-button>
            </div>

            <div class="parts-list">
              <div
                v-for="(part, index) in form.parts_used"
                :key="index"
                class="part-item"
              >
                <div class="part-row">
                  <el-input
                    v-model="part.part_name"
                    placeholder="零件名称"
                    class="part-input"
                  />
                  <el-input
                    v-model="part.part_number"
                    placeholder="零件号"
                    class="part-input"
                  />
                  <el-input-number
                    v-model="part.quantity"
                    :min="1"
                    placeholder="数量"
                    class="part-input"
                  />
                  <el-button
                    type="danger"
                    size="small"
                    @click="removePart(index)"
                  >
                    <template #icon><el-icon><Delete /></el-icon></template>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 质量检查 -->
        <div class="form-section">
          <h3>质量检查</h3>
          
          <div class="quality-check">
            <el-form-item label="质量状态" prop="quality_status">
              <el-radio-group v-model="form.quality_status">
                <el-radio-button label="pass">合格</el-radio-button>
                <el-radio-button label="fail">不合格</el-radio-button>
                <el-radio-button label="pending">待检</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="检查结果" prop="quality_notes">
              <el-input
                v-model="form.quality_notes"
                type="textarea"
                :rows="2"
                placeholder="请输入质量检查结果"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>

      <!-- 进度历史 -->
      <div class="progress-history">
        <h3>进度历史</h3>
        <div class="history-list">
          <el-timeline>
            <el-timeline-item
              v-for="item in progressHistory"
              :key="item.id"
              :timestamp="formatDateTime(item.timestamp)"
              :type="getTimelineType(item.status)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="status">{{ getStatusLabel(item.status) }}</span>
                  <span class="operator">{{ item.operator }}</span>
                </div>
                <div class="timeline-notes">{{ item.notes }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          更新进度
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 图标已全局注册，无需单独导入
import { updateWorkOrderStatus, recordLaborTime } from '@/api/maintenance'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workOrder: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)
const submitting = ref(false)
const progressHistory = ref([])

// 表单数据
const form = reactive({
  status: '',
  progress: 0,
  work_description: '',
  notes: '',
  start_time: '',
  end_time: '',
  parts_used: [],
  quality_status: 'pending',
  quality_notes: ''
})

// 表单验证规则
const rules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  progress: [
    { required: true, message: '请设置进度百分比', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '进度必须在0-100之间', trigger: 'change' }
  ],
  work_description: [
    { required: true, message: '请描述工作内容', trigger: 'blur' },
    { min: 10, max: 500, message: '工作描述长度在10到500个字符', trigger: 'blur' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 监听工单变化
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder && props.modelValue) {
    initForm(newWorkOrder)
    loadProgressHistory(newWorkOrder.id)
  }
}, { immediate: true })

// 初始化表单
const initForm = (workOrder) => {
  form.status = workOrder.status || 'new'
  form.progress = workOrder.progress || 0
  form.work_description = ''
  form.notes = ''
  form.start_time = ''
  form.end_time = ''
  form.parts_used = []
  form.quality_status = 'pending'
  form.quality_notes = ''
}

// 加载进度历史
const loadProgressHistory = async (workOrderId) => {
  // 模拟进度历史数据
  progressHistory.value = [
    {
      id: 1,
      status: 'new',
      timestamp: new Date().toISOString(),
      operator: '调度员',
      notes: '工单创建，等待分配技师'
    },
    {
      id: 2,
      status: 'in_progress',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      operator: '张师傅',
      notes: '开始维修工作，进行故障诊断'
    }
  ]
}

// 计算工作时间
const calculateWorkHours = () => {
  if (!form.start_time || !form.end_time) return 0
  
  const start = new Date(form.start_time)
  const end = new Date(form.end_time)
  const diff = end - start
  
  if (diff <= 0) return 0
  
  return Math.round(diff / (1000 * 60 * 60) * 10) / 10
}

// 添加零件
const addPart = () => {
  form.parts_used.push({
    part_name: '',
    part_number: '',
    quantity: 1
  })
}

// 删除零件
const removePart = (index) => {
  form.parts_used.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 验证时间逻辑
    if (form.start_time && form.end_time) {
      if (new Date(form.start_time) >= new Date(form.end_time)) {
        ElMessage.error('结束时间必须晚于开始时间')
        return
      }
    }

    submitting.value = true

    // 更新工单状态
    const statusData = {
      status: form.status,
      progress: form.progress,
      notes: form.notes,
      quality_status: form.quality_status,
      quality_notes: form.quality_notes
    }

    const statusResponse = await updateWorkOrderStatus(props.workOrder.id, statusData)
    
    if (!statusResponse.success) {
      ElMessage.error(statusResponse.message || '更新状态失败')
      return
    }

    // 记录工时
    if (form.start_time && form.end_time) {
      const laborData = {
        technician_id: props.workOrder.assigned_technician_id,
        start_time: form.start_time,
        end_time: form.end_time,
        work_description: form.work_description
      }

      const laborResponse = await recordLaborTime(props.workOrder.id, laborData)
      
      if (!laborResponse.success) {
        ElMessage.warning('工时记录失败，但状态更新成功')
      }
    }

    ElMessage.success('进度更新成功')
    emit('refresh')
    handleClose()
    
  } catch (error) {
    console.error('更新进度失败:', error)
    ElMessage.error('更新失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.parts_used = []
  progressHistory.value = []
}

// 工具函数
const getStatusTagType = (status) => {
  const types = {
    new: 'info',
    in_progress: 'warning',
    waiting_parts: 'danger',
    completed: 'success'
  }
  return types[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    new: '新建',
    in_progress: '进行中',
    waiting_parts: '待料中',
    completed: '已完成'
  }
  return labels[status] || status
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getTimelineType = (status) => {
  const types = {
    new: 'primary',
    in_progress: 'warning',
    waiting_parts: 'danger',
    completed: 'success'
  }
  return types[status] || 'primary'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.progress-update-dialog {
  --el-dialog-content-font-size: 14px;
}

.progress-form {
  max-height: 80vh;
  overflow-y: auto;
}

.work-order-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.work-order-info h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  font-weight: 400;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.form-section h3 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.progress-slider {
  width: 100%;
}

.labor-stats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parts-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.parts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
}

.parts-list {
  padding: 16px;
}

.part-item {
  margin-bottom: 12px;
}

.part-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.part-input {
  flex: 1;
}

.quality-check {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-history {
  margin-top: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.progress-history h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.timeline-content {
  padding: 4px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.status {
  font-weight: 500;
  color: #333;
}

.operator {
  font-size: 12px;
  color: #666;
}

.timeline-notes {
  font-size: 13px;
  color: #555;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.progress-form::-webkit-scrollbar {
  width: 6px;
}

.progress-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.progress-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.progress-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .labor-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .part-row {
    flex-direction: column;
  }
  
  .part-input {
    width: 100%;
  }
}
</style>