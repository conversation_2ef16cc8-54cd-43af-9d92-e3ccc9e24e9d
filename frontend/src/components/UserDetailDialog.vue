<template>
  <el-dialog
    v-model="visible"
    title="用户详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="user" class="user-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>用户名：</label>
              <span>{{ user.username }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>真实姓名：</label>
              <span>{{ user.real_name || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>邮箱：</label>
              <span>{{ user.email }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>电话：</label>
              <span>{{ user.phone || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>公司名称：</label>
              <span>{{ user.company_name || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>用户类型：</label>
              <el-tag :type="getUserTypeTagType(user.user_type)">
                {{ getUserTypeLabel(user.user_type) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="getStatusTagType(user.status)">
                {{ getStatusLabel(user.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>公司类型：</label>
              <el-tag :type="user.company_type === 'internal' ? 'warning' : 'info'">
                {{ user.company_type === 'internal' ? '内部员工' : '外部公司' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>权限等级：</label>
              <el-tag :type="getPermissionLevelTagType(user.permission_level)">
                {{ getPermissionLevelLabel(user.permission_level) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>在线状态：</label>
              <el-tag :type="user.is_online ? 'success' : 'info'" size="small">
                {{ user.is_online ? '在线' : '离线' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 登录信息 -->
      <div class="detail-section">
        <h3 class="section-title">登录信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>登录次数：</label>
              <span>{{ user.login_count || 0 }} 次</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>最后登录：</label>
              <span>{{ formatDateTime(user.last_login) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>注册时间：</label>
              <span>{{ formatDateTime(user.created_at) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(user.updated_at) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 权限信息 -->
      <div class="detail-section">
        <h3 class="section-title">权限信息</h3>
        <div class="permissions-list">
          <el-tag
            v-for="permission in user.permissions"
            :key="permission"
            class="permission-tag"
            size="small"
          >
            {{ permission }}
          </el-tag>
          <span v-if="!user.permissions || user.permissions.length === 0" class="no-permissions">
            暂无权限信息
          </span>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="detail-section">
        <h3 class="section-title">
          最近活动
          <el-button size="small" text @click="viewAllLogs">查看全部</el-button>
        </h3>
        <div v-loading="logsLoading" class="activity-logs">
          <div
            v-for="log in activityLogs"
            :key="log.id"
            class="log-item"
          >
            <div class="log-icon">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="log-content">
              <div class="log-description">{{ log.description }}</div>
              <div class="log-meta">
                <span class="log-time">{{ formatDateTime(log.created_at) }}</span>
                <span class="log-ip">IP: {{ log.ip_address }}</span>
              </div>
            </div>
          </div>
          <div v-if="activityLogs.length === 0" class="no-logs">
            暂无活动记录
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editUser">编辑用户</el-button>
        <el-button type="warning" @click="resetPassword">重置密码</el-button>
        <el-dropdown @command="handleAction">
          <el-button>
            更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="changeStatus">修改状态</el-dropdown-item>
              <el-dropdown-item command="viewLogs">查看完整日志</el-dropdown-item>
              <el-dropdown-item command="exportData">导出用户数据</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <!-- 用户活动日志对话框 -->
    <UserActivityLogsDialog
      v-model="showLogsDialog"
      :user="user"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usersApi } from '@/api/users'
import UserActivityLogsDialog from './UserActivityLogsDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'updated', 'edit'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activityLogs = ref([])
const logsLoading = ref(false)
const showLogsDialog = ref(false)

// 监听用户变化，加载活动日志
watch(() => props.user, (newUser) => {
  if (newUser && props.modelValue) {
    loadActivityLogs()
  }
}, { immediate: true })

// 加载用户活动日志
const loadActivityLogs = async () => {
  if (!props.user) return
  
  logsLoading.value = true
  try {
    const response = await usersApi.getUserActivityLogs(props.user.id, { per_page: 5 })
    if (response.success) {
      activityLogs.value = response.body.activity_logs
    }
  } catch (error) {
    console.error('加载活动日志失败:', error)
  } finally {
    logsLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 编辑用户
const editUser = () => {
  emit('edit', props.user)
  handleClose()
}

// 重置密码
const resetPassword = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${props.user.username} 的密码吗？`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await usersApi.resetUserPassword(props.user.id)
    if (response.success) {
      ElMessage.success(`密码重置成功，新密码：${response.body.new_password}`)
      emit('updated')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 查看全部日志
const viewAllLogs = () => {
  showLogsDialog.value = true
}

// 处理更多操作
const handleAction = (command) => {
  switch (command) {
    case 'changeStatus':
      changeUserStatus()
      break
    case 'viewLogs':
      viewAllLogs()
      break
    case 'exportData':
      exportUserData()
      break
  }
}

// 修改用户状态
const changeUserStatus = async () => {
  // 这里可以打开状态修改对话框
  ElMessage.info('状态修改功能开发中')
}

// 导出用户数据
const exportUserData = async () => {
  try {
    const response = await usersApi.exportUsers('csv', { user_ids: [props.user.id] })
    if (response.success) {
      ElMessage.success('用户数据导出成功')
      window.open(response.body.download_url)
    }
  } catch (error) {
    console.error('导出用户数据失败:', error)
    ElMessage.error('导出用户数据失败')
  }
}

// 工具方法
const getUserTypeLabel = (type) => {
  const typeMap = {
    admin: '系统管理员',
    platform_staff: '平台员工',
    supplier: '供应商',
    distributor: '分销商',
    airline_buyer: '航空公司',
    maintenance_engineer: '维修工程师',
    logistics_specialist: '物流专员'
  }
  return typeMap[type] || type
}

const getUserTypeTagType = (type) => {
  const typeMap = {
    admin: 'danger',
    platform_staff: 'warning',
    supplier: 'success',
    distributor: 'primary',
    airline_buyer: 'info',
    maintenance_engineer: '',
    logistics_specialist: ''
  }
  return typeMap[type] || ''
}

const getStatusLabel = (status) => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '暂停',
    deleted: '已删除'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    suspended: 'warning',
    deleted: 'danger'
  }
  return statusMap[status] || ''
}

const getPermissionLevelLabel = (level) => {
  const levelMap = {
    1: '普通用户',
    2: '平台员工',
    3: '管理员',
    4: '超级管理员'
  }
  return levelMap[level] || `等级${level}`
}

const getPermissionLevelTagType = (level) => {
  const levelMap = {
    1: 'info',
    2: 'warning',
    3: 'danger',
    4: 'danger'
  }
  return levelMap[level] || 'info'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.user-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

.no-permissions {
  color: #909399;
  font-style: italic;
}

.activity-logs {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.log-icon {
  margin-right: 12px;
  color: #409eff;
}

.log-content {
  flex: 1;
}

.log-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.log-meta {
  font-size: 12px;
  color: #909399;
}

.log-meta span {
  margin-right: 16px;
}

.no-logs {
  text-align: center;
  color: #909399;
  font-style: italic;
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
