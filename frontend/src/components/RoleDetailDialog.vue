<template>
  <el-dialog
    :model-value="modelValue"
    :title="`角色详情 - ${role?.display_name}`"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div v-if="role" class="role-detail">
      <!-- 角色基本信息 -->
      <div class="mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
        <div class="flex items-start space-x-4">
          <div 
            class="w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0"
            :style="{ 
              backgroundColor: role.theme_color + '20' || '#f3f4f6',
              color: role.theme_color || '#6b7280'
            }"
          >
            <el-icon :size="32">
              <component :is="getIconComponent(role.icon_name)" />
            </el-icon>
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-bold text-gray-900 mb-2">{{ role.display_name }}</h2>
            <p class="text-gray-600 mb-3">{{ role.description }}</p>
            
            <div class="flex flex-wrap gap-2">
              <el-tag 
                :type="role.category === 'internal' ? 'warning' : 'info'"
                size="large"
              >
                {{ role.category === 'internal' ? '内部员工' : '外部用户' }}
              </el-tag>
              <el-tag 
                :type="getBusinessTypeTagType(role.business_type)"
                size="large"
              >
                {{ getBusinessTypeText(role.business_type) }}
              </el-tag>
              <el-tag 
                :type="role.is_active ? 'success' : 'danger'"
                size="large"
              >
                {{ role.is_active ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- 基本属性 -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
            <el-icon class="mr-2 text-blue-500"><InfoFilled /></el-icon>
            基本属性
          </h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">角色编码:</span>
              <code class="px-2 py-1 bg-gray-100 rounded text-sm">{{ role.role_code }}</code>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">排序顺序:</span>
              <span class="font-medium">{{ role.sort_order || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">主题色:</span>
              <div class="flex items-center space-x-2">
                <div 
                  class="w-4 h-4 rounded border"
                  :style="{ backgroundColor: role.theme_color }"
                ></div>
                <code class="text-sm">{{ role.theme_color }}</code>
              </div>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">创建时间:</span>
              <span>{{ formatDate(role.created_at) }}</span>
            </div>
          </div>
        </div>

        <!-- 权限统计 -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
            <el-icon class="mr-2 text-green-500"><Key /></el-icon>
            权限统计
          </h3>
          <div v-loading="loadingPermissions" class="space-y-3">
            <div v-if="rolePermissions.length === 0" class="text-gray-500 text-center py-4">
              暂无权限配置
            </div>
            <template v-else>
              <div class="flex justify-between">
                <span class="text-gray-600">权限总数:</span>
                <span class="font-medium text-blue-600">{{ rolePermissions.length }}</span>
              </div>
              <div v-for="category in permissionStats" :key="category.name" class="flex justify-between">
                <span class="text-gray-600">{{ category.name }}:</span>
                <span class="font-medium">{{ category.count }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 权限详情 -->
      <div v-if="rolePermissions.length > 0" class="mb-6">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <el-icon class="mr-2 text-purple-500"><List /></el-icon>
          权限详情
        </h3>
        
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <div class="max-h-64 overflow-y-auto">
            <div 
              v-for="permission in rolePermissions" 
              :key="permission.id"
              class="flex items-center justify-between px-4 py-3 border-b last:border-b-0 hover:bg-gray-50"
            >
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ permission.permission_name }}</div>
                <div class="text-sm text-gray-500">{{ permission.description }}</div>
              </div>
              <div class="flex items-center space-x-2">
                <el-tag size="small" type="warning">{{ permission.category }}</el-tag>
                <el-tag size="small">{{ permission.module }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用统计 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-blue-600">{{ userCount }}</div>
          <div class="text-sm text-blue-600">使用用户数</div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-green-600">{{ activeUserCount }}</div>
          <div class="text-sm text-green-600">活跃用户数</div>
        </div>
        <div class="bg-orange-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-orange-600">{{ Math.round((activeUserCount / Math.max(userCount, 1)) * 100) }}%</div>
          <div class="text-sm text-orange-600">活跃率</div>
        </div>
      </div>

      <!-- 操作历史 -->
      <div v-if="operationHistory.length > 0" class="mt-6">
        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
          <el-icon class="mr-2 text-gray-500"><Clock /></el-icon>
          最近操作
        </h3>
        
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="space-y-2">
            <div 
              v-for="(operation, index) in operationHistory.slice(0, 5)" 
              :key="index"
              class="flex items-center justify-between text-sm"
            >
              <div class="flex items-center space-x-2">
                <div 
                  :class="[
                    'w-2 h-2 rounded-full',
                    getOperationColor(operation.type)
                  ]"
                ></div>
                <span>{{ operation.description }}</span>
              </div>
              <span class="text-gray-500">{{ formatDate(operation.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <el-button 
          type="primary" 
          text
          @click="openPermissionConfig"
        >
          <el-icon><Key /></el-icon>
          配置权限
        </el-button>
        <el-button @click="$emit('update:modelValue', false)">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { rolesApi } from '@/api/roles'

// Props & Emits
const props = defineProps({
  modelValue: Boolean,
  role: Object
})

const emit = defineEmits(['update:modelValue', 'configure-permissions'])

// 响应式数据
const loadingPermissions = ref(false)
const rolePermissions = ref([])
const userCount = ref(0)
const activeUserCount = ref(0)
const operationHistory = ref([])

// 计算属性
const permissionStats = computed(() => {
  const stats = {}
  rolePermissions.value.forEach(permission => {
    const category = permission.category || '其他'
    if (!stats[category]) {
      stats[category] = 0
    }
    stats[category]++
  })
  
  return Object.entries(stats).map(([name, count]) => ({ name, count }))
})

// 方法
const loadRolePermissions = async () => {
  if (!props.role?.id) return

  loadingPermissions.value = true
  try {
    const response = await rolesApi.getRolePermissions(props.role.id)
    if (response.success) {
      rolePermissions.value = response.body.permissions || []
    }
  } catch (error) {
    console.error('加载角色权限失败:', error)
    ElMessage.error('加载角色权限失败')
  } finally {
    loadingPermissions.value = false
  }
}

const loadRoleStats = async () => {
  // 模拟加载用户统计数据
  userCount.value = Math.floor(Math.random() * 100) + 10
  activeUserCount.value = Math.floor(userCount.value * (0.6 + Math.random() * 0.3))
  
  // 模拟操作历史
  operationHistory.value = [
    {
      type: 'update',
      description: '更新角色权限配置',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      type: 'create',
      description: '创建角色',
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    }
  ]
}

const openPermissionConfig = () => {
  emit('configure-permissions', props.role)
  emit('update:modelValue', false)
}

// 工具方法
const getIconComponent = (iconName) => {
  const iconMap = {
    Factory: 'OfficeBuilding',
    TruckDelivery: 'Truck',
    Airplane: 'WindPower',
    UserGroup: 'UserFilled',
    Wrench: 'Tool',
    Truck: 'Truck',
    Settings: 'Setting',
    User: 'User'
  }
  return iconMap[iconName] || 'User'
}

const getBusinessTypeTagType = (businessType) => {
  const typeMap = {
    sell_only: 'success',
    buy_only: 'primary',
    buy_and_sell: '',
    service: 'warning'
  }
  return typeMap[businessType] || ''
}

const getBusinessTypeText = (businessType) => {
  const textMap = {
    sell_only: '只销售',
    buy_only: '只采购',
    buy_and_sell: '买卖双向',
    service: '服务支持'
  }
  return textMap[businessType] || businessType
}

const getOperationColor = (type) => {
  const colorMap = {
    create: 'bg-green-500',
    update: 'bg-blue-500',
    delete: 'bg-red-500'
  }
  return colorMap[type] || 'bg-gray-500'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch (error) {
    return dateString
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.role) {
    loadRolePermissions()
    loadRoleStats()
  }
})
</script>

<style scoped>
.role-detail {
  /* 角色详情容器样式 */
}

/* 自定义滚动条 */
.max-h-64::-webkit-scrollbar {
  width: 6px;
}

.max-h-64::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 渐变背景 */
.bg-gradient-to-r {
  background: linear-gradient(to right, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 代码样式 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}
</style>