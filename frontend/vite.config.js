import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig(({ mode }) => {
  const isProd = mode === 'production'
  const isStaging = mode === 'staging'
  
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    define: {
      __DEV__: !isProd && !isStaging,
      __MOCK_ENABLED__: process.env.VITE_MOCK_ENABLED === 'true',
      __PROD__: isProd,
      __STAGING__: isStaging
    },
    server: {
      host: 'localhost',
      port: 3000,
      strictPort: false,
      open: false,
      cors: true,
      proxy: {
        '/api': {
          target: process.env.VITE_API_BASE_URL || 'http://127.0.0.1:5001',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      rollupOptions: {
        // 生产环境优化：排除Mock相关代码
        external: isProd ? [] : undefined,
        output: {
          manualChunks: {
            // 将Mock相关代码分离到独立chunk，便于条件加载
            'mock-manager': ['./src/api/mockManager.js'],
          },
        },
      },
      // 生产环境和staging环境启用代码分割和树摇
      minify: isProd || isStaging,
      sourcemap: !isProd,
      // 确保staging模式使用正确的输出目录
      outDir: isStaging ? 'dist-staging' : 'dist',
    },
  }
})