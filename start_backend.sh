#!/bin/bash

# 启动航材共享保障平台后端服务
# 创建时间: 2025-07-19

echo "🚀 启动航材共享保障平台后端服务..."

# 进入后端目录
cd backend

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查依赖
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: 未找到requirements.txt文件"
    exit 1
fi

# 安装依赖（如果需要）
echo "📦 检查Python依赖..."
pip3 install -r requirements.txt

# 检查数据库文件
if [ ! -f "dev_aviation.db" ]; then
    echo "📊 初始化数据库..."
    python3 -c "
from models import db
from app import create_app
app = create_app()
with app.app_context():
    db.create_all()
    print('数据库初始化完成')
"
fi

# 启动Flask应用
echo "🌟 启动Flask应用..."
echo "📍 服务地址: http://localhost:5000"
echo "📍 API地址: http://localhost:5000/api/v1"
echo "🔧 开发模式已启用"
echo ""
echo "按 Ctrl+C 停止服务"
echo "==============================================="

# 设置环境变量并启动
export FLASK_APP=app.py
export FLASK_ENV=development
export FLASK_DEBUG=1

python3 app.py